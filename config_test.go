package mnms

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"testing"
	"time"

	"mnms/simulator"
	"mnms/simulator/net"
	"mnms/simulator/yaml"

	"github.com/qeof/q"
)

func TestConfigDevData(t *testing.T) {
	err := ReadTestData()
	if err != nil {
		t.Fatal(err)
	}

	dev, err := FindDev("02-42-C0-A8-64-A7")
	if err != nil || dev == nil {
		t.Fatal(err)
	}
	q.Q(dev)

	configData := `{ "ipaddress": "***********","hostname":"fancycat"}`

	conf := make(map[string]string)
	err = json.Unmarshal([]byte(configData), &conf)
	if err != nil {
		t.Fatalf("can't unmarshal %v", err)
	}
}

// TestConfigSnmpBasic tests the basic snmp configuration
/*
  simulator yaml file:

	Environments:
  simulator_group: #create once device using macAddress:"00-60-e9-18-01-99"
      type: "EH7520"
      startPrefixip: "**************/24"
      startMacAddress: "00-60-e9-18-01-99"

  run simulator:
	simulator.exe run -y config.yaml

	objID .*******.4.1.3755.0.0.21.
	.*******.4.1.3755.*********.*******

	acceptable settings:
	syslogFields := map[string]string{
		"status":       ".10.*******:Integer",
		"server-ip":    ".********.0:OctetString",
		"server-port":  ".********.0:OctetString",
		"log-level": ".********.0:Integer",
		"LogToFlash":   ".********.0:Integer",
	}

	trapServerFields := map[string]string{
		"status":      ".*******.0:Integer",
		"server-ip":   ".*******.0:OctetString",
		"server-port": ".*******.0:Integer",
		"community":   ".*******.0:OctetString",
	}

*/

var testSnmpSimulators []*simulator.AtopGwdClient

func runTestSnmpSimulators(ctx context.Context) error {
	er := make(chan error)
	go func() {
		name, err := net.GetDefaultInterfaceName()
		if err != nil {
			q.Q(err)
		}

		// type: "EH7520"
		//   startPrefixip: "**************/24"
		//   startMacAddress: "00-60-e9-18-01-99"
		env := map[string]yaml.Simulator{
			"simulator_group": {
				DeviceType:    "EH7520",
				Number:        1,
				StartPreFixIp: "**************/24",
				MacAddress:    targetMac,
			},
		}

		newSims, err := yaml.NewSimulator(env, name)

		if err != nil {
			er <- err
		}
		for _, v := range newSims {
			_ = v.StartUp()
			testSnmpSimulators = append(testSnmpSimulators, v)
		}
		q.Q(len(testSnmpSimulators))
		er <- nil
		<-ctx.Done()
	}()
	return <-er
}

var targetMac = "00-60-E9-18-01-01"

// TestConfigSnmpBasic tests the basic snmp configuration such as syslog, trap server
func TestConfigSnmpBasic(t *testing.T) {
	t.Log("config syslog has bug, can't pass testing, close this testing for now. After fix bug should modify this test case")

	//  ./simulator.exe run -y config.yaml
	ctx, cancel := context.WithCancel(context.Background())
	// run simulator and wait it start
	err := runTestSnmpSimulators(ctx)
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		for _, v := range testSnmpSimulators {
			_ = v.Shutdown()
		}
	}()
	time.Sleep(time.Millisecond * 1000)
	// testing snmp basic
	ret, err := SnmpGet("**************", []string{SystemObjectID})
	if err != nil {
		t.Fatal(err)
	}
	if len(ret.Variables) < 1 {
		t.Error("expect 1 result but got", len(ret.Variables))
	}
	value := PDUToString(ret.Variables[0])
	t.Log("SystemObjectID:", value)
	if value != ".*******.4.1.3755.0.0.21" {
		t.Error("expect .*******.4.1.3755.0.0.21 but got", value)
	}
	// read status
	ret, err = SnmpGet("**************", []string{".*******.4.1.3755.*********.*******"})
	if err != nil {
		t.Fatal(err)
	}
	if len(ret.Variables) < 1 {
		t.Error("expect 1 result but got", len(ret.Variables))
	}
	value = PDUToString(ret.Variables[0])
	t.Log(".*******.4.1.3755.*********.*******:", value)
	if value != "2" {
		t.Error("expect 2 but got", value)
	}
	// write status 1
	pkt, err := SnmpSet("**************", ".*******.4.1.3755.*********.*******", "1", "Integer")
	if err != nil {
		t.Fatal(err)
	}

	if uint8(pkt.Error) > 0 {
		t.Error("expect 0 but got", pkt.Error)
	}

	// read status
	ret, err = SnmpGet("**************", []string{".*******.4.1.3755.*********.*******"})
	if err != nil {
		t.Fatal(err)
	}
	if len(ret.Variables) < 1 {
		t.Error("expect 1 result but got", len(ret.Variables))
	}
	value = PDUToString(ret.Variables[0])
	t.Log(".*******.4.1.3755.*********.*******:", value)
	if value != "1" {
		t.Error("expect 1 but got", value)
	}

	t.Skip()
	QC.IsRoot = true
	// run services
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		GwdMain()
	}()
	wg.Add(1)
	go func() {
		// root and non-root run http api service.
		// if both root and non-root runs on same machine (should not happen)
		// then whoever runs first will run http service (port conflict)
		defer wg.Done()
		HTTPMain()
		// TODO root to dump snapshots of devices, logs, commands
	}()
	defer cancel()

	// t.Log("mnmsctl is running, waitting 5 secound for scan")
	time.Sleep(time.Second * 5)
	// check mnmsctl is running

	// http request to localhost:27182/api
	resp, err := http.Get("http://localhost:27182/api")
	if err != nil {
		t.Fatal("mnmsctl is not running, should run mnmsctl first")
	}
	if resp == nil {
		t.Fatal("nil response")
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal("mnms haven't /api endpoint: ", err)
	}
	if string(respBody) != "mnms says hello" {
		t.Fatal("expected mnms says hello, got ", respBody)
	}
	// save close, already check resp is not nil
	resp.Body.Close()

	// Run simulator check simulator device exist
	queryUrl := fmt.Sprintf("http://localhost:27182/api/v1/devices?dev=%s", targetMac)

	adminToken, err := GetToken("admin")
	if err != nil {
		t.Fatal("can't get admin token: ", err)
	}
	resp1, err := GetWithToken(queryUrl, adminToken)
	if resp1.StatusCode != 200 || err != nil {
		t.Log("resp1.StatusCode: ", resp1.StatusCode)
		t.Log("err: ", err)
		t.Fatal("can't get device info: ", err)
	}
	if resp1 == nil {
		t.Fatal("nil response")
	}

	var devinfo DevInfo
	err = json.NewDecoder(resp1.Body).Decode(&devinfo)
	if err != nil {
		t.Fatal("marshal devinfo fail ", err)
	}
	// save close, already check resp is not nil
	resp1.Body.Close()

	// Testing syslog
	//Usage : config syslog set [mac address] [status] [server ip] [server port] [log level] [log to flash]
	// config syslog set [mac address] 2 *************** 123 2 2
	syslogSettings := map[string]string{
		"status":       "1",
		"server-ip":    "***************",
		"server-port":  "123",
		"log-level": "2",
		"LogToFlash":   "2",
	}

	syslogFields := map[string]string{
		"status":       ".10.*******:Integer",
		"server-ip":    ".********.0:OctetString",
		"server-port":  ".********.0:Integer",
		"log-level": ".********.0:Integer",
		"LogToFlash":   ".********.0:Integer",
	}

	cmdJson := `[{"command":"config syslog set 00-60-E9-18-01-99 1 *************** 123 2 2"}]`
	resp, err = PostWithToken("http://localhost:27182/api/v1/commands", adminToken, bytes.NewBuffer([]byte(cmdJson)))

	if err != nil {
		t.Fatal("config syslog post error", err)
	}
	if resp == nil {
		t.Fatal("nil response")
	}
	if resp != nil && resp.StatusCode != 200 {
		t.Fatal("config syslog post status code", resp.StatusCode)
	}
	// save close, already check resp == nil
	resp.Body.Close()

	err = CheckCmds()
	if err != nil {
		t.Fatal("check command error", err)
	}

	// read back
	// TODO: implement read config API
	for f, s := range syslogSettings {
		objID := ".*******.4.1.3755.0.0.21."
		oidNType := strings.Split(syslogFields[f], ":")
		oid := objID + oidNType[0]

		res, err := SnmpGet(devinfo.IPAddress, []string{oid})
		// res, err := params.Get(oids)
		if err != nil {
			t.Error("snmp get fail", err)
		}
		if len(res.Variables) < 1 {
			t.Error("expect 1 result but got", len(res.Variables))
		}
		value := PDUToString(res.Variables[0])

		if value != s {
			t.Errorf("expect %s but got %s", s, value)
		}
	}

	/*
		TODO: fix the commented out trap test
		trapServerFields := map[string]string{
			"status":      ".*******.0:Integer",
			"server-ip":   ".*******.0:OctetString",
			"server-port": ".*******.0:Integer",
			"community":   ".*******.0:OctetString",
		}

		trapSettings := map[string]string{
			"status":      "2",
			"server-ip":   "***************",
			"server-port": "123",
			"community":   "test-community",
		}

		for f, s := range trapSettings {
			cmdinfo := make(map[string]CmdInfo)
			cmd := fmt.Sprintf("snmp trap add  00-60-E9-18-01-99 %s %s", f, s)
			insertcmd(cmd, &cmdinfo)
			jsonBytes, err := json.Marshal(cmdinfo)
			if err != nil {
				t.Fatal("json marshal", err)
			}
			resp, err := PostWithToken("http://localhost:27182/api/v1/commands", adminToken, bytes.NewBuffer([]byte(jsonBytes)))

			if err != nil {
				t.Error("snmp trap add post fail", err)
			}
			if resp == nil {
				t.Fatal("nil response")
			}
			if resp.StatusCode != 200 {
				q.Q("snmp trap add post fail", f, resp.StatusCode)
			}
			// save close
			resp.Body.Close()
			// read back
			// TODO: implement read config API
			objID := ".*******.4.1.3755.0.0.21."
			oidNType := strings.Split(trapServerFields[f], ":")
			oid := objID + oidNType[0]

			res, err := SnmpGet(devinfo.IPAddress, []string{oid})
			// res, err := params.Get(oids)
			if err != nil {
				q.Q("snmp get fail", err)
				return
			}
			if len(res.Variables) < 1 {
				q.Q("expect 1 result but got", len(res.Variables))
				return
			}
			value := PDUToString(res.Variables[0])
			t.Logf("expect %s  got %s", s, value)
			if value != s {
				q.Q("expect but got", s, value)
			}

			return

		}
	*/
}
