package mnms

import (
	"strconv"
	"testing"
	"time"

	"github.com/qeof/q"
	"github.com/stretchr/testify/assert"
)

func TestVerifyDeviceDeleteCmds(t *testing.T) {
	QC.DevMutex.Lock()
	clear(QC.DevData)
	QC.DevMutex.Unlock()
	QC.TopologyMutex.Lock()
	clear(QC.TopologyData)
	QC.TopologyMutex.Unlock()

	tests := []struct {
		name     string
		cmdinfo  *CmdInfo
		expected string
	}{
		{
			name: "Valid command - delete specific device",
			cmdinfo: &CmdInfo{
				Command: "device delete AA-BB-CC-DD-EE-FF",
			},
			expected: "ok",
		},
		{
			name: "Valid command - delete all devices",
			cmdinfo: &CmdInfo{
				Command: "device delete all",
			},
			expected: "ok",
		},
		/*
			{
				name: "Invalid command - incorrect format",
				cmdinfo: &CmdInfo{
					Command: "delete",
				},
				expected: "error: Verification of command length wrong failed",
			},
			{
				name: "Invalid command - target device not found",
				cmdinfo: &CmdInfo{
					Command: "device delete AA-BB-CC-DD-EE-FF",
				},
				expected: "error: Verification of delete AA-BB-CC-DD-EE-FF failed",
			}, */
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := verifyDeviceDeleteCmds(tt.cmdinfo)
			if err != nil {
				t.Errorf("verifyDeviceDeleteCmds() error = %v", err)
			}
			if tt.cmdinfo.Verify != tt.expected {
				t.Errorf("verifyDeviceDeleteCmds() verify = %v, want %v", tt.cmdinfo.Verify, tt.expected)
			}
		})
	}
}

func TestVerifySSHSetting(t *testing.T) {
	QC.SshConnections = make(map[int]SshConnection)

	tests := []struct {
		name        string
		cmdinfo     *CmdInfo
		setup       func()
		expected    string
		expectError bool
	}{
		{
			name: "Valid command - ssh tunnel close",
			cmdinfo: &CmdInfo{
				Command: "ssh tunnel close 62345",
			},
			setup: func() {
				QC.SshConnectionsMutex.Lock()
				QC.SshConnections[62345] = SshConnection{
					RemoteAddr: "127.0.0.1",
					ListenPort: 62345,
					DevMac:     "AA-BB-CC-DD-EE-FF",
				}
				QC.SshConnectionsMutex.Unlock()
			},
			expected:    "error: Verification of ssh tunnel close command failed",
			expectError: false,
		},
		{
			name: "Valid command - ssh tunnel close with no existing connection",
			cmdinfo: &CmdInfo{
				Command: "ssh tunnel close 62345",
			},
			setup: func() {
				QC.SshConnectionsMutex.Lock()
				delete(QC.SshConnections, 62345)
				QC.SshConnectionsMutex.Unlock()
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Valid command - ssh tunnel fetch with existing connection",
			cmdinfo: &CmdInfo{
				Command: "ssh tunnel fetch 62345",
			},
			setup: func() {
				QC.SshConnectionsMutex.Lock()
				QC.SshConnections[62345] = SshConnection{
					RemoteAddr: "1.2.3.4",
					ListenPort: 62345,
					DevMac:     "AA-BB-CC-DD-EE-FF",
				}
				QC.SshConnectionsMutex.Unlock()
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Valid command - ssh tunnels list",
			cmdinfo: &CmdInfo{
				Command: "ssh tunnels list",
			},
			setup: func() {
				QC.SshConnectionsMutex.Lock()
				QC.SshConnections[62345] = SshConnection{
					RemoteAddr: "1.2.3.4",
					ListenPort: 62345,
					DevMac:     "AA-BB-CC-DD-EE-FF",
				}
				QC.SshConnectionsMutex.Unlock()
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - missing port",
			cmdinfo: &CmdInfo{
				Command: "ssh tunnel close",
			},
			setup:       func() {},
			expected:    "",
			expectError: true,
		},
		{
			name: "Invalid command - non-integer port",
			cmdinfo: &CmdInfo{
				Command: "ssh tunnel close abc",
			},
			setup:       func() {},
			expected:    "",
			expectError: true,
		},
		{
			name: "Valid command - no tunnel to close",
			cmdinfo: &CmdInfo{
				Command: "ssh tunnel close 62345",
			},
			setup: func() {
				QC.SshConnectionsMutex.Lock()
				delete(QC.SshConnections, 62345)
				QC.SshConnectionsMutex.Unlock()
			},
			expected:    "ok",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Run setup if provided
			tt.setup()

			// Call the function
			err := verifySSHSetting(tt.cmdinfo)

			// Check for expected error
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error, got nil")
				}
				return
			}
			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			// Check expected Verify value
			if tt.cmdinfo.Verify != tt.expected {
				t.Errorf("Verify value = %v, want %v", tt.cmdinfo.Verify, tt.expected)
			}
		})
	}
}

func TestVerifyAgentSSHSetting(t *testing.T) {
	QC.DevData = make(map[string]DevInfo)

	tests := []struct {
		name        string
		cmdinfo     *CmdInfo
		setup       func()
		expected    string
		expectError bool
	}{
		{
			name: "Valid command - agent ssh reverse websrv with valid TunneledUrl",
			cmdinfo: &CmdInfo{
				DevId:     "device1",
				Command:   "agent ssh reverse websrv device1",
				Timestamp: strconv.FormatInt(time.Now().Unix(), 10),
			},
			setup: func() {
				QC.DevMutex.Lock()
				QC.DevData["device1"] = DevInfo{
					Timestamp:   strconv.FormatInt(time.Now().Add(-time.Minute).Unix(), 10),
					TunneledUrl: "https://example.com",
				}
				QC.DevMutex.Unlock()
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Valid command - agent ssh reverse websrv with missing TunneledUrl",
			cmdinfo: &CmdInfo{
				DevId:     "device1",
				Command:   "agent ssh reverse websrv device1",
				Timestamp: strconv.FormatInt(time.Now().Unix(), 10),
			},
			setup: func() {
				QC.DevMutex.Lock()
				QC.DevData["device1"] = DevInfo{
					Timestamp:   strconv.FormatInt(time.Now().Add(-time.Minute).Unix(), 10),
					TunneledUrl: "",
				}
				QC.DevMutex.Unlock()
			},
			expected:    "error: Verification of ssh auto command failed",
			expectError: false,
		},
		{
			name: "Valid command - agent ssh reverse start",
			cmdinfo: &CmdInfo{
				DevId:     "device1",
				Command:   "agent ssh reverse start device1 1.2.3.4 12345 443 22",
				Timestamp: strconv.FormatInt(time.Now().Unix(), 10),
			},
			setup: func() {
				QC.DevMutex.Lock()
				QC.DevData["device1"] = DevInfo{
					Timestamp:   strconv.FormatInt(time.Now().Add(-time.Minute).Unix(), 10),
					TunneledUrl: "https://example.com",
				}
				QC.DevMutex.Unlock()
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Valid command - agent ssh reverse stop",
			cmdinfo: &CmdInfo{
				DevId:     "device1",
				Command:   "agent ssh reverse stop device1 1.2.3.4 12345",
				Timestamp: strconv.FormatInt(time.Now().Unix(), 10),
			},
			setup: func() {
				QC.DevMutex.Lock()
				QC.DevData["device1"] = DevInfo{
					Timestamp:   strconv.FormatInt(time.Now().Add(-time.Minute).Unix(), 10),
					TunneledUrl: "https://example.com",
				}
				QC.DevMutex.Unlock()
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Valid command - agent ssh reverse status",
			cmdinfo: &CmdInfo{
				DevId:     "device1",
				Command:   "agent ssh reverse status device1",
				Timestamp: strconv.FormatInt(time.Now().Unix(), 10),
			},
			setup: func() {
				QC.DevMutex.Lock()
				QC.DevData["device1"] = DevInfo{
					Timestamp:   strconv.FormatInt(time.Now().Add(-time.Minute).Unix(), 10),
					TunneledUrl: "https://example.com",
				}
				QC.DevMutex.Unlock()
			},
			expected:    "ok",
			expectError: false,
		},
		/*
		   {
		       name: "Outdated device timestamp",
		       cmdinfo: &CmdInfo{
		           DevId:     "device1",
		           Command:   "agent ssh reverse websrv device1",
		           Timestamp: strconv.FormatInt(time.Now().Unix(), 10),
		       },
		       setup: func() {
		           QC.DevMutex.Lock()
		           QC.DevData["device1"] = DevInfo{
		               Timestamp:   strconv.FormatInt(time.Now().Add(time.Minute).Unix(), 10),
		               TunneledUrl: "https://example.com",
		           }
		           QC.DevMutex.Unlock()
		       },
		       expected:    "", // No Verify field is set because the function returns early
		       expectError: false,
		   },
		   {
		       name: "Malformed command",
		       cmdinfo: &CmdInfo{
		           DevId:     "device1",
		           Command:   "malformed command",
		           Timestamp: strconv.FormatInt(time.Now().Unix(), 10),
		       },
		       setup:       func() {},
		       expected:    "",
		       expectError: true,
		   },
		   {
		       name: "Device not found in QC.DevData",
		       cmdinfo: &CmdInfo{
		           DevId:     "nonexistent_device",
		           Command:   "agent ssh reverse websrv nonexistent_device",
		           Timestamp: strconv.FormatInt(time.Now().Unix(), 10),
		       },
		       setup:       func() {},
		       expected:    "",
		       expectError: true,
		   }, */
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Run setup if provided
			tt.setup()

			// Call the function
			err := verifyAgentSSHSetting(tt.cmdinfo)

			// Check for expected error
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error, got nil")
				}
				return
			}
			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			// Check expected Verify value
			if tt.cmdinfo.Verify != tt.expected {
				t.Errorf("Verify value = %v, want %v", tt.cmdinfo.Verify, tt.expected)
			}
		})
	}
}

func TestVerifyDebugLogSetting(t *testing.T) {
	tests := []struct {
		name        string
		cmdinfo     *CmdInfo
		setup       func()
		expected    string
		expectError bool
	}{
		{
			name: "Valid command - debug log on with correct pattern",
			cmdinfo: &CmdInfo{
				Command: "debug log on",
			},
			setup: func() {
				q.P = ".*" // Set the pattern to match
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - debug log on with incorrect pattern",
			cmdinfo: &CmdInfo{
				Command: "debug log on",
			},
			setup: func() {
				q.P = "invalid_pattern"
			},
			expected:    "error: Verification of debug log on setting failed",
			expectError: false,
		},
		{
			name: "Valid command - debug log off with no pattern",
			cmdinfo: &CmdInfo{
				Command: "debug log off",
			},
			setup: func() {
				q.P = "" // No pattern should match
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - debug log off with existing pattern",
			cmdinfo: &CmdInfo{
				Command: "debug log off",
			},
			setup: func() {
				q.P = ".*" // Existing pattern causes failure
			},
			expected:    "error: Verification of debug log off setting failed",
			expectError: false,
		},
		{
			name: "Valid command - debug log pattern matches",
			cmdinfo: &CmdInfo{
				Command: "debug log pattern .*",
			},
			setup: func() {
				q.P = ".*" // Pattern to validate against
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - debug log pattern mismatch",
			cmdinfo: &CmdInfo{
				Command: "debug log pattern different_pattern",
			},
			setup: func() {
				q.P = ".*" // Mismatch pattern
			},
			expected:    "error: Verification of debug log pattern setting failed",
			expectError: false,
		},
		{
			name: "Valid command - debug log output matches",
			cmdinfo: &CmdInfo{
				Command: "debug log output stderr",
			},
			setup: func() {
				q.O = "stderr" // Output to validate against
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - debug log output mismatch",
			cmdinfo: &CmdInfo{
				Command: "debug log output stdout",
			},
			setup: func() {
				q.O = "stderr" // Mismatched output
			},
			expected:    "error: Verification of debug log output setting failed",
			expectError: false,
		},
		{
			name: "Invalid command - debug log pattern with insufficient arguments",
			cmdinfo: &CmdInfo{
				Command: "debug log pattern",
			},
			setup:       func() {},
			expected:    "",
			expectError: true,
		},
		{
			name: "Invalid command - debug log output with insufficient arguments",
			cmdinfo: &CmdInfo{
				Command: "debug log output",
			},
			setup:       func() {},
			expected:    "",
			expectError: true,
		},
	}
	defer func() {
		q.P = ""
	}()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Run setup if provided
			tt.setup()

			// Call the function
			err := verifyDebugLogSetting(tt.cmdinfo)

			// Check for expected error
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error, got nil")
				}
				return
			}
			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			// Check expected Verify value
			if tt.cmdinfo.Verify != tt.expected {
				t.Errorf("Verify value = %v, want %v", tt.cmdinfo.Verify, tt.expected)
			}
		})
	}
}

func TestVerifyLocalSyslogSetting(t *testing.T) {
	tests := []struct {
		name        string
		cmdinfo     *CmdInfo
		setup       func()
		expected    string
		expectError bool
	}{
		{
			name: "Valid command - config local syslog path",
			cmdinfo: &CmdInfo{
				Command: "config local syslog path tmp/log",
			},
			setup: func() {
				QC.SyslogLocalPath = "tmp/log"
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - config local syslog path mismatch",
			cmdinfo: &CmdInfo{
				Command: "config local syslog path tmp/invalid",
			},
			setup: func() {
				QC.SyslogLocalPath = "tmp/log"
			},
			expected:    "error: Verification of local syslog path failed",
			expectError: false,
		},
		{
			name: "Valid command - config local syslog maxsize",
			cmdinfo: &CmdInfo{
				Command: "config local syslog maxsize 100",
			},
			setup: func() {
				QC.SyslogFileSize = 100
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - config local syslog maxsize mismatch",
			cmdinfo: &CmdInfo{
				Command: "config local syslog maxsize 200",
			},
			setup: func() {
				QC.SyslogFileSize = 100
			},
			expected:    "error: Verification of local syslog maxsize failed",
			expectError: false,
		},
		{
			name: "Valid command - config local syslog compress true",
			cmdinfo: &CmdInfo{
				Command: "config local syslog compress true",
			},
			setup: func() {
				QC.SyslogCompress = true
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - config local syslog compress mismatch",
			cmdinfo: &CmdInfo{
				Command: "config local syslog compress false",
			},
			setup: func() {
				QC.SyslogCompress = true
			},
			expected:    "error: Verification of local syslog compress failed",
			expectError: false,
		},
		{
			name: "Valid command - config local syslog remote",
			cmdinfo: &CmdInfo{
				Command: "config local syslog remote 122.147.151.234:5514",
			},
			setup: func() {
				QC.RemoteSyslogServerAddr = "122.147.151.234:5514"
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - config local syslog remote mismatch",
			cmdinfo: &CmdInfo{
				Command: "config local syslog remote 127.0.0.1:5514",
			},
			setup: func() {
				QC.RemoteSyslogServerAddr = "122.147.151.234:5514"
			},
			expected:    "error: Verification of local syslog remote server addr failed",
			expectError: false,
		},
		{
			name: "Valid command - config local syslog backup-after-forward true",
			cmdinfo: &CmdInfo{
				Command: "config local syslog backup-after-forward true",
			},
			setup: func() {
				QC.SyslogBakAfterFwd = true
			},
			expected:    "ok",
			expectError: false,
		},
		{
			name: "Invalid command - config local syslog backup-after-forward mismatch",
			cmdinfo: &CmdInfo{
				Command: "config local syslog backup-after-forward false",
			},
			setup: func() {
				QC.SyslogBakAfterFwd = true
			},
			expected:    "error: Verification of local syslog back after forward command failed",
			expectError: false,
		},
		{
			name: "Invalid command - insufficient arguments",
			cmdinfo: &CmdInfo{
				Command: "config local syslog",
			},
			setup:       func() {},
			expected:    "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Run setup if provided
			tt.setup()

			// Call the function
			err := verifyLocalSyslogSetting(tt.cmdinfo)

			// Check for expected error
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error, got nil")
				}
				return
			}
			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			// Check expected Verify value
			if tt.cmdinfo.Verify != tt.expected {
				t.Errorf("Verify value = %v, want %v", tt.cmdinfo.Verify, tt.expected)
			}
		})
	}
}

/*
func TestMqttPubCmd(t *testing.T) {
	cmdinfo := &CmdInfo{
		DevId:     "dev123",
		Command:   "mqtt pub 192.168.12.1:1883 topictest \"this is messages.\"",
		Timestamp: "1625079174",
	}

	err := verifyMqttCmds(cmdinfo)
	assert.NoError(t, err)
	assert.Equal(t, "ok", cmdinfo.Verify)
}

func TestMqttSubCmd(t *testing.T) {
	cmdinfo := &CmdInfo{
		DevId:     "dev123",
		Command:   "mqtt sub 192.168.12.1:1883 topictest",
		Timestamp: "1625079174",
	}

	// Mock the mqttclient subscribe list with a sample subscribed topic
	mqttMutex.Lock()
	mqttclient.client["dev123"] = MqttClient{
		tcp: "192.168.12.1:1883",
		subscribeList: map[string]SubscribeStatus{
			"topictest": {inSubscribe: true},
		},
	}
	mqttMutex.Unlock()

	err := verifyMqttCmds(cmdinfo)
	assert.NoError(t, err)
	assert.Equal(t, "ok", cmdinfo.Verify)
}

func TestMqttUnsubCmd(t *testing.T) {
	cmdinfo := &CmdInfo{
		DevId:     "dev123",
		Command:   "mqtt unsub 192.168.12.1:1883 topictest",
		Timestamp: "1625079174",
	}

	// Mock the mqttclient subscribe list with a subscribed topic
	mqttMutex.Lock()
	mqttclient.client["dev123"] = MqttClient{
		tcp: "192.168.12.1:1883",
		subscribeList: map[string]SubscribeStatus{
			"topictest": {inSubscribe: true},
		},
	}
	mqttMutex.Unlock()

	err := verifyMqttCmds(cmdinfo)
	assert.NoError(t, err)
	assert.Equal(t, "error: Verification of mqtt subscribe command failed", cmdinfo.Verify)
}
*/

func TestVerifyDeviceUser(t *testing.T) {
	QC.DevData = make(map[string]DevInfo)
	// Test Case 1: Correct User and Password Configuration
	t.Run("Correct User and Password", func(t *testing.T) {
		cmdinfo := &CmdInfo{
			DevId:     "AA-BB-CC-DD-EE-FF",
			Command:   "config user AA-BB-CC-DD-EE-FF admin default",
			Timestamp: "1625079174",
		}

		// Mock device data
		QC.DevMutex.Lock()
		QC.DevData["AA-BB-CC-DD-EE-FF"] = DevInfo{
			UserName:  "admin",
			PassWord:  "default",
			Timestamp: "1625079170",
		}
		QC.DevMutex.Unlock()

		err := verifyDeviceUser(cmdinfo)
		assert.NoError(t, err)
		assert.Equal(t, "ok", cmdinfo.Verify)
	})

	// Test Case 2: Incorrect Username
	t.Run("Incorrect Username", func(t *testing.T) {
		cmdinfo := &CmdInfo{
			DevId:     "AA-BB-CC-DD-EE-FF",
			Command:   "config user AA-BB-CC-DD-EE-FF guest default",
			Timestamp: "1625079174",
		}

		// Mock device data
		QC.DevMutex.Lock()
		QC.DevData["AA-BB-CC-DD-EE-FF"] = DevInfo{
			UserName:  "admin",
			PassWord:  "default",
			Timestamp: "1625079170",
		}
		QC.DevMutex.Unlock()

		err := verifyDeviceUser(cmdinfo)
		assert.NoError(t, err)
		assert.Equal(t, "error: Verification of user name failed", cmdinfo.Verify)
	})

	// Test Case 3: Incorrect Password
	t.Run("Incorrect Password", func(t *testing.T) {
		cmdinfo := &CmdInfo{
			DevId:     "AA-BB-CC-DD-EE-FF",
			Command:   "config user AA-BB-CC-DD-EE-FF admin wrongpassword",
			Timestamp: "1625079174",
		}

		// Mock device data
		QC.DevMutex.Lock()
		QC.DevData["AA-BB-CC-DD-EE-FF"] = DevInfo{
			UserName:  "admin",
			PassWord:  "default",
			Timestamp: "1625079170",
		}
		QC.DevMutex.Unlock()

		err := verifyDeviceUser(cmdinfo)
		assert.NoError(t, err)
		assert.Equal(t, "error: Verification of password failed", cmdinfo.Verify)
	})
	// Test Case 4: Command Length Validation (Invalid Command)
	t.Run("Invalid Command Length", func(t *testing.T) {
		cmdinfo := &CmdInfo{
			DevId:     "AA-BB-CC-DD-EE-FF",
			Command:   "config user AA-BB-CC-DD-EE-FF admin", // Missing password
			Timestamp: "1625079174",
		}
		err := verifyDeviceUser(cmdinfo)
		assert.Error(t, err)
		assert.Equal(t, "error: command length wrong", err.Error())
	})
}

func TestVerifyAgentPortEnable(t *testing.T) {
	QC.PortAndPowerInfo = make(map[string]PortAndPowerInfo)
	QC.PortAndPowerInfo["device-1"] = PortAndPowerInfo{
		Timestamp: "1672531200", // Mock timestamp
		PortStatus: []PortInfo{
			{
				PortName:   "port1",
				PortStatus: true, // Port is enabled
			},
			{
				PortName:   "port2",
				PortStatus: false, // Port is disabled
			},
		},
	}

	tests := []struct {
		name           string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		// Valid command test cases
		{
			name: "Valid port enable command (enabled)",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config port enable AA-BB-CC-DD-EE-FF port1 1",
				Timestamp: "1672531195", // Earlier than device timestamp
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Valid port disable command (disabled)",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config port enable AA-BB-CC-DD-EE-FF port2 0",
				Timestamp: "1672531195",
			},
			expectedVerify: "ok",
			expectError:    false,
		},

		// Invalid command test cases
		{
			name: "Port enable command on already enabled port",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config port enable AA-BB-CC-DD-EE-FF port1 1",
				Timestamp: "1672531195", // Valid timestamp
			},
			expectedVerify: "ok", // No issue with re-enabling an already enabled port
			expectError:    false,
		},
		{
			name: "Port disable command on already disabled port",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config port enable AA-BB-CC-DD-EE-FF port2 0",
				Timestamp: "1672531195",
			},
			expectedVerify: "ok", // No issue with re-disabling an already disabled port
			expectError:    false,
		},
		{
			name: "Invalid command length",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config port enable AA-BB-CC-DD-EE-FF port1",
				Timestamp: "1672531195", // Invalid command length
			},
			expectedVerify: "",
			expectError:    true, // Expect error due to invalid command length
		},
		{
			name: "Port not found in PortStatus",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config port enable AA-BB-CC-DD-EE-FF port3 1",
				Timestamp: "1672531195",
			},
			expectedVerify: "error: Verification of port enable command failed",
			expectError:    false, // Port3 does not exist in PortStatus
		},
		{
			name: "Port status mismatch (enable command on disabled port)",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config port enable AA-BB-CC-DD-EE-FF port2 1",
				Timestamp: "1672531195",
			},
			expectedVerify: "error: Verification of port enable command failed",
			expectError:    false, // Trying to enable a port that is already disabled
		},
		{
			name: "Port status mismatch (disable command on enabled port)",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config port enable AA-BB-CC-DD-EE-FF port1 0",
				Timestamp: "1672531195",
			},
			expectedVerify: "error: Verification of port enable command failed",
			expectError:    false, // Trying to disable a port that is already enabled
		},
		{
			name: "Port and Power Info missing in QC",
			cmdInfo: &CmdInfo{
				DevId:     "device-unknown",
				Command:   "agent config port enable AA-BB-CC-DD-EE-FF port1 1",
				Timestamp: "1672531195",
			},
			expectedVerify: "",
			expectError:    true, // Expect error because PortAndPowerInfo for this DevId does not exist
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyAgentPortEnable(test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: '%v'", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}

func TestVerifyAgentGPSEnable(t *testing.T) {
	QC.DevData = make(map[string]DevInfo)

	// Mock device data with expanded GPS info
	QC.DevData["device-1"] = DevInfo{
		Timestamp: "1672531200", // Mock timestamp
		GpsInfo: GpsInfo{
			Supported: "1",          // GPS is supported
			Enabled:   "1",          // GPS is enabled
			Lasttime:  "1672531150", // Last update time
			Latitude:  "12.9716",    // Mock latitude
			Longitude: "77.5946",    // Mock longitude
		},
	}
	tests := []struct {
		name           string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		// Valid command test cases
		{
			name: "Valid GPS enable command (enabled)",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config gps enable AA-BB-CC-DD-EE-FF 1",
				Timestamp: "1672531195", // Earlier than device timestamp
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Valid GPS enable command (disabled)",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config gps enable AA-BB-CC-DD-EE-FF 0",
				Timestamp: "1672531195",
			},
			expectedVerify: "error: Verification of GPS enable command failed",
			expectError:    false,
		},
		{
			name: "Invalid command length",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config gps enable",
				Timestamp: "1672531195",
			},
			expectedVerify: "",
			expectError:    true, // Expect error due to invalid command length
		},
		{
			name: "Missing device in DevData",
			cmdInfo: &CmdInfo{
				DevId:     "device-unknown",
				Command:   "agent config gps enable AA-BB-CC-DD-EE-FF 1",
				Timestamp: "1672531195",
			},
			expectedVerify: "",
			expectError:    true, // Expect error because DevId does not exist in DevData
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyAgentGPSEnable(test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: '%v'", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}

func TestVerifyScanCmds(t *testing.T) {
	// Initialize mock data
	QC.DevData = make(map[string]DevInfo)

	// Mock device data with timestamps
	QC.DevData["device-1"] = DevInfo{
		Timestamp: "1672531200", // Mock device timestamp
	}

	tests := []struct {
		name           string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		// Valid command test cases
		{
			name: "Valid scan gwd command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "scan gwd",
				Timestamp: "1672531195", // Earlier than device timestamp + 10
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Valid scan snmp command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "scan snmp",
				Timestamp: "1672531195", // Earlier than device timestamp + 10
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Missing device in DevData",
			cmdInfo: &CmdInfo{
				DevId:     "device-unknown",
				Command:   "scan gwd",
				Timestamp: "1672531195",
			},
			expectedVerify: "",
			expectError:    true, // Expect error because DevId does not exist in DevData
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyScanCmds(test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: '%v'", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}

func TestVerifyAgentUpdateInfoCmds(t *testing.T) {
	// Initialize mock data
	QC.DevData = make(map[string]DevInfo)
	QC.TopologyData = make(map[string]Topology)
	QC.PortAndPowerInfo = make(map[string]PortAndPowerInfo)

	QC.DevData["device-1"] = DevInfo{
		Timestamp: "1672531200", // Mock device timestamp
	}

	QC.TopologyData["device-1"] = Topology{
		LastUpdated: 1672531200, // Mock topology timestamp
	}

	QC.PortAndPowerInfo["device-1"] = PortAndPowerInfo{
		Timestamp: "1672531200", // Mock port & power timestamp
	}

	tests := []struct {
		name           string
		updatedCmd     string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		// Tests for "devinfo"
		{
			name:       "Valid devinfo command",
			updatedCmd: "devinfo",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent devinfo send AA-BB-CC-DD-EE-FF",
				Timestamp: "1672531195", // Earlier than devdata timestamp + 10
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		// Tests for "topologyinfo"
		{
			name:       "Valid topologyinfo command",
			updatedCmd: "topologyinfo",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent topologyinfo send AA-BB-CC-DD-EE-FF",
				Timestamp: "1672531195", // Earlier than topologydata timestamp + 10
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		// Tests for "portpwinfo"
		{
			name:       "Valid portpwinfo command",
			updatedCmd: "portpwinfo",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent portpwinfo send AA-BB-CC-DD-EE-FF",
				Timestamp: "1672531195", // Earlier than ptpwinfodata timestamp + 10
			},
			expectedVerify: "ok",
			expectError:    false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyAgentUpdateInfoCmds(test.updatedCmd, test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: '%v'", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}

func TestVerifyGwdNetworkSetting(t *testing.T) {
	// Initialize mock device data
	QC.DevData = make(map[string]DevInfo)

	QC.DevData["device-1"] = DevInfo{
		Timestamp: "1672531200",
		IsDHCP:    false,
		IPAddress: "*********",
		Netmask:   "*************",
		Gateway:   "0.0.0.0",
		Hostname:  "switch",
	}

	tests := []struct {
		name           string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		{
			name: "Valid static network setting command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "gwd config network set AA-BB-CC-DD-EE-FF ********* ********* ************* 0.0.0.0 switch",
				Timestamp: "1672531100",
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Valid DHCP-enabled network setting command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "gwd config network set AA-BB-CC-DD-EE-FF ********* 0.0.0.0 ************* 0.0.0.0 switch",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of dhcp failed",
			expectError:    false,
		},
		{
			name: "Invalid static network setting - incorrect IP address",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "gwd config network set AA-BB-CC-DD-EE-FF ********* ********* ************* 0.0.0.0 switch",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of ip address failed",
			expectError:    false,
		},
		{
			name: "Invalid static network setting - incorrect netmask",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "gwd config network set AA-BB-CC-DD-EE-FF ********* ********* *********** 0.0.0.0 switch",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of Netmask failed",
			expectError:    false,
		},
		{
			name: "Invalid static network setting - incorrect gateway",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "gwd config network set AA-BB-CC-DD-EE-FF ********* ********* ************* ******** switch",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of gateway failed",
			expectError:    false,
		},
		{
			name: "Invalid static network setting - incorrect hostname",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "gwd config network set AA-BB-CC-DD-EE-FF ********* ********* ************* 0.0.0.0 router",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of hostanme failed",
			expectError:    false,
		},
		{
			name: "Invalid network setting command - insufficient parameters",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "gwd config network set AA-BB-CC-DD-EE-FF ********* *********",
				Timestamp: "1672531100",
			},
			expectedVerify: "",
			expectError:    true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyGwdNetworkSetting(test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: %v", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}

func TestVerifyAgentSNMPEnable(t *testing.T) {
	// Initialize mock device data
	QC.DevData = make(map[string]DevInfo)

	QC.DevData["device-1"] = DevInfo{
		Timestamp:   "1672531200",
		SnmpEnabled: "1", // SNMP enabled
	}

	tests := []struct {
		name           string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		{
			name: "Valid SNMP enable command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent snmp enable AA-BB-CC-DD-EE-FF 1",
				Timestamp: "1672531100",
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Invalid SNMP enable command - Disabled instead of enabled",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent snmp enable AA-BB-CC-DD-EE-FF 0",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of SNMP enable failed",
			expectError:    false,
		},
		{
			name: "Invalid SNMP enable command - Insufficient parameters",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent snmp enable AA-BB-CC-DD-EE-FF",
				Timestamp: "1672531100",
			},
			expectedVerify: "",
			expectError:    true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyAgentSNMPEnable(test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: %v", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}

func TestVerifyAgentNetworkSetting_DHCP(t *testing.T) {
	// Initialize mock device data
	QC.DevData = make(map[string]DevInfo)

	QC.DevData["device-1"] = DevInfo{
		Timestamp: "1672531200",
		IsDHCP:    true,
		IPAddress: "***********",
		Netmask:   "*************",
		Gateway:   "***********",
		Hostname:  "switch",
	}

	tests := []struct {
		name           string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		{
			name: "Valid DHCP network setting command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 1",
				Timestamp: "1672531100",
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Invalid DHCP network setting command - Static IP provided",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 0",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of dhcp failed",
			expectError:    false,
		},
		{
			name: "Invalid DHCP network setting command - Wrong hostname",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** router 1",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of hostanme failed",
			expectError:    false,
		},
		{
			name: "Invalid DHCP network setting command - Insufficient parameters",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config network set AA-BB-CC-DD-EE-FF ***********",
				Timestamp: "1672531100",
			},
			expectedVerify: "",
			expectError:    true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyAgentNetworkSetting(test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: %v", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}

func TestVerifyAgentNetworkSetting(t *testing.T) {
	// Initialize mock device data
	QC.DevData = make(map[string]DevInfo)

	QC.DevData["device-1"] = DevInfo{
		Timestamp: "1672531200",
		IsDHCP:    false,
		IPAddress: "***********",
		Netmask:   "*************",
		Gateway:   "***********",
		Hostname:  "switch",
	}

	tests := []struct {
		name           string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		{
			name: "Valid static network setting command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 0",
				Timestamp: "1672531100",
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Invalid static network setting command - wrong IP address",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 0",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of ip address failed",
			expectError:    false,
		},
		{
			name: "Invalid static network setting command - wrong gateway",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config network set AA-BB-CC-DD-EE-FF *********** ************* ***********54 switch 0",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of gateway failed",
			expectError:    false,
		},
		{
			name: "Valid DHCP network setting command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 1",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of dhcp failed",
			expectError:    false,
		},
		{
			name: "Invalid network setting command - insufficient parameters",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config network set AA-BB-CC-DD-EE-FF",
				Timestamp: "1672531100",
			},
			expectedVerify: "",
			expectError:    true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyAgentNetworkSetting(test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: %v", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}

func TestVerifyAgentTrapSetting(t *testing.T) {
	// Initialize mock device data
	QC.DevData = make(map[string]DevInfo)

	QC.DevData["device-1"] = DevInfo{
		Timestamp: "1672531200",
		TrapSetting: []TrapSetting{
			{
				ServerIp:   "*************",
				ServerPort: "5162",
				Community:  "public",
			},
			{
				ServerIp:   "***********43",
				ServerPort: "5163",
				Community:  "public",
			},
		},
	}
	QC.DevData["device-2"] = DevInfo{
		Timestamp: "1672531200",
		TrapSetting: []TrapSetting{
			{
				ServerIp:   "*************",
				ServerPort: "5162",
				Community:  "public",
			},
		},
	}

	tests := []struct {
		name           string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		{
			name: "Valid trap add command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent snmp trap add AA-BB-CC-DD-EE-FF ************* 5162 public",
				Timestamp: "1672531100",
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Invalid trap add command - wrong ServerIp",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent snmp trap add AA-BB-CC-DD-EE-FF ***********50 5162 public",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of snmp trap failed",
			expectError:    false,
		},
		{
			name: "Valid trap delete command",
			cmdInfo: &CmdInfo{
				DevId:     "device-2",
				Command:   "agent snmp trap del AA-BB-CC-DD-EE-FF ***********43 5163 public",
				Timestamp: "1672531100",
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Invalid trap delete command - trap still exists",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent snmp trap del AA-BB-CC-DD-EE-FF ************* 5162 public",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of snmp trap failed",
			expectError:    false,
		},
		{
			name: "Invalid trap command - insufficient parameters",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent snmp trap add AA-BB-CC",
				Timestamp: "1672531100",
			},
			expectedVerify: "",
			expectError:    true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyAgentTrapSetting(test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: %v", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}

func TestVerifyAgentSyslogSetting(t *testing.T) {
	// Initialize mock device data
	QC.DevData = make(map[string]DevInfo)

	QC.DevData["device-1"] = DevInfo{
		Timestamp: "1672531200",
		SyslogSetting: SyslogSetting{
			LogToServer: "1",
			ServerIp:    "*************",
			ServerPort:  "5514",
			LogLevel:    "8",
			LogToFlash:  "1",
		},
	}

	tests := []struct {
		name           string
		cmdInfo        *CmdInfo
		expectedVerify string
		expectError    bool
	}{
		{
			name: "Valid syslog setting command",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config syslog set AA-BB-CC-DD-EE-FF 1 ************* 5514 8 1",
				Timestamp: "1672531100",
			},
			expectedVerify: "ok",
			expectError:    false,
		},
		{
			name: "Invalid syslog setting command - wrong ServerIp",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config syslog set AA-BB-CC-DD-EE-FF 1 ***********500 5514 8 1",
				Timestamp: "1672531100",
			},
			expectedVerify: "error: Verification of ServerIp failed",
			expectError:    false,
		},
		{
			name: "Invalid syslog setting command - insufficient parameters",
			cmdInfo: &CmdInfo{
				DevId:     "device-1",
				Command:   "agent config syslog set AA-BB-CC-DD-EE-FF 1",
				Timestamp: "1672531100",
			},
			expectedVerify: "",
			expectError:    true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := verifyAgentSyslogSetting(test.cmdInfo)
			if (err != nil) != test.expectError {
				t.Fatalf("unexpected error: %v", err)
			}
			if test.cmdInfo.Verify != test.expectedVerify {
				t.Fatalf("expected verification '%v', got: %v", test.expectedVerify, test.cmdInfo.Verify)
			}
		})
	}
}
