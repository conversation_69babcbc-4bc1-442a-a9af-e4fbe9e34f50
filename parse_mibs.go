/*
This file is used to get snmp list files.
Copy from NMS project, author: <PERSON> is the same format as snmp list file, it is used to test snmp get/set function where the oids are fixed for most of the cases.

Running Parse_MIBs function will parse all MIB files in MIB-files-json folder and create snmp list files in snmplist folder.

File format:
	first line: private mib oid
	other lines: description=rest oid=value=syntax
*/

package mnms

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"path/filepath"
	"regexp"
	"strings"
)

var Basic_Oids string = `
nil
systemModelName=.1.10.0=test_string=OctetString
backupServerIP=.*******=***********=OctetString
backupAgentBoardFwFileName=.*******=test_string.dld=OctetString
restoreServerIP=.*******=***********=OctetString
restoreAgentBoardFwFileName=.*******=test_string.dld=OctetString
syslogStatus=.********.0=1=Integer
eventServerPort=.********.0=1=Integer
eventServerLevel=.********.0=5=Integer
eventLogToFlash=.********.0=1=Integer
sntpClientStatus=.*******=1=Integer
sntpUTCTimezone=.*******=1=Integer
sntpServer1=.*******=********=OctetString
sntpServer2=.********=********=OctetString
sntpServerQueryPeriod=.********=1=Integer
agingTimeSetting=.********=1=Integer
ptpState=.********.0=1=Integer
ptpVersion=.********.0=1=Integer
ptpSyncInterval=.********.0=1=Integer
ptpClockStratum=.********.0=1=Integer
ptpPriority1=.********.0=1=Integer
ptpPriority2=.********.0=1=Integer
rstpStatus=.*******=1=Integer
qosCOSPriorityQueue=.*******.1=1=Integer
qosTOSPriorityQueue=.*******.1=1=Integer
eventPortEventEmail=.********.1.3.1=1=Integer
eventPortEventRelay=.********.1.4.1=1=Integer
eventPowerEventSMTP=.********.1.3.1=1=Integer
syslogEventsSMTP=.********.1.0=1=Integer
eventEmailAlertAddr=.10.1.3.2.0=<EMAIL>=OctetString
eventEmailAlertAuthentication=.10.1.3.3.0=1=Integer
eventEmailAlertAccount=.10.1.3.4.0=test_account=OctetString
lldpStatus=.12.1.0=1=Integer
`

func add_snmptestlist(filename string, s string, cur_dir string) error {
	filename = path.Join(cur_dir, filename+".txt")
	// fmt.Println("filename: ", filename)
	f, err := os.OpenFile(filename, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		return err
	}
	defer f.Close()

	_, err = f.Write([]byte(s + "\n"))
	if err != nil {
		return err
	}
	f.Close()
	return nil
}

func check_object_indentifier_table_element(input string) bool {
	if m, _ := regexp.MatchString("Entry|Table", input); m {
		return true
	} else {
		return false
	}
}

func syntax_type_convert(input string) string {
	output := ""
	if m, _ := regexp.MatchString("Integer|INTEGER", input); m {
		output = "Integer"
	} else if m, _ := regexp.MatchString("IpAddress|IPAddress", input); m {
		output = "IpAddress"
	} else if m, _ := regexp.MatchString("MacAddress|MACAddress", input); m {
		output = "OctetString"
	} else if m, _ := regexp.MatchString("String|STRING|string", input); m {
		output = "OctetString"
	} else {
		//other
		output = "OctetString"
	}
	return output
}

func syntax_to_fixed_val(input string) string {
	output := ""
	if m, _ := regexp.MatchString("Integer|INTEGER", input); m {
		output = "1"
	} else if m, _ := regexp.MatchString("IpAddress|IPAddress", input); m {
		output = "*********"
	} else if m, _ := regexp.MatchString("MacAddress|MACAddress", input); m {
		output = "AA:BB:CC:DD:EE:FF"
	} else if m, _ := regexp.MatchString("String|STRING|string", input); m {
		output = "test_string"
	} else {
		//other
		output = "1"
	}
	return output
}

type Mib struct {
	ObjectName       string `json:"ObjectName"`
	ModuleName       string `json:"ModuleName"`
	MACRO            string `json:"MACRO"`
	OID              string `json:"OID"`
	NameSpace        string `json:"NameSpace"`
	Syntax           string `json:"SYNTAX"`
	ObjectIdentifier string `json:"OBJECT_IDENTIFIER"`
	// Add other fields if necessary
}

func (m *Mib) UnmarshalJSON(data []byte) error {
	type Alias Mib
	aux := &struct {
		*Alias
	}{
		Alias: (*Alias)(m),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		// Ignore the error if it's a type mismatch error
		if _, ok := err.(*json.UnmarshalTypeError); !ok {
			return err
		}
	}
	return nil
}

type MibMap map[string]Mib

func findAtopMibAndCreateTestList(mibFullPath string, snmplist_dir string) error {
	//json to map
	var mib map[string]MibMap
	fmt.Println("mibFullPath: ", mibFullPath)
	jsonFile, err := os.Open(mibFullPath)
	if err != nil {
		return err
	}
	defer jsonFile.Close()
	byteValue, _ := io.ReadAll(jsonFile)

	err = json.Unmarshal(byteValue, &mib)
	if err != nil {
		return err
	}

	// found private mib name
	model_name := ""
	regexp1 := regexp.MustCompile("1.3.6.1.4.1.3755")
	regexp2 := regexp.MustCompile("MGMT-SMI")

	for model, mibs := range mib {
		// foreach mibs check if any OID equal 1.3.6.1.4.1.3755
		if m := regexp2.MatchString(model); m {
			// MGMT-SMI mean mib oid location
			continue
		}
		for _, mib_val := range mibs {
			// check
			if m := regexp1.MatchString(mib_val.OID); m {
				// OID equal
				model_name = model
				break
			}
		}
	}
	if model_name == "" {
		return fmt.Errorf("%s doesn't have private mib", mibFullPath)
	}

	// remove old snmp list file
	if _, e_out := os.Stat(snmplist_dir + model_name + ".txt"); e_out == nil {
		e := os.Remove(snmplist_dir + model_name + ".txt")
		if e != nil {
			fmt.Printf("Remove %s fail", model_name)
		}
	}
	//add list add private mib
	private_mib_oid := ""
	regexp3 := regexp.MustCompile("MODULE-IDENTITY")
	for _, mib_val := range mib[model_name] {
		if m := regexp3.MatchString(mib_val.MACRO); m {
			// MODULE-IDENTITY mean mib oid location
			private_mib_oid = mib_val.OID
			break
		}
	}
	if private_mib_oid == "" {
		return fmt.Errorf("%s doesn't have oid %s", mibFullPath, private_mib_oid)
	}
	err = add_snmptestlist(model_name, private_mib_oid, snmplist_dir)
	if err != nil {
		return err
	}

	for ele, ele_val := range mib[model_name] {
		//value type
		if len(ele_val.Syntax) > 0 {
			//exist SYNTAX
			list_log := ""
			// OID empty no use
			if ele_val.OID != "" {
				if check_object_indentifier_table_element(ele_val.ObjectIdentifier) {
					// mib table elements
					list_log = ele + "=" + ele_val.OID + ".1"
				} else {
					// normal mib elements
					list_log = ele + "=" + ele_val.OID + ".0"
				}
				if ele_val.Syntax == "" {
					ele_val.Syntax = "INTEGER"
				}
				// Remove Table entry: SYNTAX : SEQUENCE OF or Entry
				if !strings.Contains(ele_val.Syntax, "SEQUENCE OF") {
					if !strings.Contains(ele_val.Syntax, "Entry") {
						// Remove Table evtry: ObjectName : Index
						if !strings.Contains(ele_val.ObjectName, "Index") {
							fixed_val := syntax_to_fixed_val(ele_val.Syntax)
							ele_syntax_type := syntax_type_convert(ele_val.Syntax)
							list_log = list_log + "=" + fixed_val + "=" + ele_syntax_type
							//add list
							err = add_snmptestlist(model_name, list_log, snmplist_dir)
							if err != nil {
								return err
							}

						}
					}
				}
			}
		}
	}
	return nil
}

/*
Parse_MIBs function is used to parse MIB files and create snmp list files in snmplist folder
*/
func Parse_MIBs() {
	current_dir, err := os.Getwd()
	if err != nil {
		panic(err)
	}

	root_dir := filepath.Dir(current_dir)

	mib_dir := ""
	// snmp list folder
	snmplist := ""

	//mib_dir = root_dir + "\\regression_test\\MIB\\Atop\\MIB-files-json\\"
	mib_dir = path.Join(root_dir, "regression_test", "MIB", "Atop", "MIB-files-json")

	// testsnmp folder
	// snmp list folder
	//snmplist = root_dir + "\\regression_test\\testsnmp\\snmplist\\"
	snmplist = path.Join(root_dir, "regression_test", "testsnmp", "snmplist")

	if err := os.MkdirAll(snmplist, os.ModePerm); err != nil {
		fmt.Println("Create snmplist folder fail: ", err)
		panic(err)
	}

	// remove old snmp list file in snmplist folder
	files, _ := os.ReadDir(snmplist)
	for _, file := range files {
		if file.IsDir() {
			continue
		} else {
			e := os.Remove(path.Join(snmplist, file.Name()))
			if e != nil {
				fmt.Printf("Remove %s fail", file.Name())
			}
		}
	}

	// for each mib file
	files, _ = os.ReadDir(mib_dir)
	fmt.Println("Process mib_dir: ", mib_dir)
	match, _ := regexp.Compile("EH")
	for _, file := range files {
		if file.IsDir() {
			continue
		} else {
			if match.MatchString(file.Name()) {
				fmt.Println("Process file name: ", file.Name())
				fullPath := path.Join(mib_dir, file.Name())
				err := findAtopMibAndCreateTestList(fullPath, snmplist)
				//output := json_call_mib2(file.Name(), mib_dir, testsnmp_dir, snmplist)
				if err != nil {
					fmt.Println("json_call_mib fail: ", err)
				}
				fmt.Println("Done")
			}
		}
	}
}
