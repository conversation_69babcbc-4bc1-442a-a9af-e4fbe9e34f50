# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands

### Main Build Process
- `make` - Builds all core components (frontend + Go services)
- `make dev` - Smart development build (builds frontend only if missing, otherwise just Go services)
- `make devcmds` - Builds Go services only (excludes frontend)

### Individual Service Builds
- `make bbrootsvc` - Root service (main NMS coordinator)
- `make bbnmssvc` - Network management service (device discovery, SNMP)
- `make bbctl` - Command-line interface tool
- `make bblogsvc` - Log aggregation and syslog forwarding service
- `make bbidpsvc` - Intrusion Detection and Prevention System
- `make bbfwdsvc` - Forwarding service
- `make bbpollsvc` - Polling service
- `make frontend` - React-based web UI

### Release Build
- `make release` - Complete release process (prompts for version, builds installers)

## Test Commands

### Go Tests
- `sudo go test -v -p 1` - Run all unit tests (requires sudo on Linux)
- `go test -v -p 1` - Windows version (run as administrator)
- `sudo go test -v -p 1 -timeout 20m` - Extended timeout for regression tests

### Frontend Tests
- `cd frontend && npm run test` - Vitest unit tests
- `cd frontend && npm run test:cy` - Cypress end-to-end tests
- `cd frontend && npm run coverage` - Test coverage report

### Integration Tests
- Regression tests are included in the main Go test suite
- All tests must pass before merging PRs

## Lint and Code Quality

### Go Linting
- `make lint` - Runs comprehensive Go linting:
  - `go vet` - Static analysis
  - `revive` - Go linter
  - `golangci-lint run` - Advanced linting
  - `staticcheck` - Static analysis tool

### Frontend Linting
- `cd frontend && npm run lint` - ESLint for React code

## Architecture Overview

### Service Architecture
The system follows a distributed microservices architecture:

**bbrootsvc** - Central coordinator service that:
- Manages cluster state and node registration
- Provides main API endpoints
- Coordinates between other services
- Handles authentication and licensing

**bbnmssvc** - Network management service that:
- Performs device discovery via SNMP
- Manages network topology
- Handles device configuration and monitoring

**bblogsvc** - Log management service that:
- Aggregates syslog from network devices
- Provides log search and filtering
- Supports log forwarding and correlation

**bbidpsvc** - Security service that:
- Intrusion Detection System (IDS) monitoring
- Intrusion Prevention System (IPS) with packet filtering
- Uses Hyperscan for high-performance pattern matching
- Integrates with netfilter on Linux, WinDivert on Windows

**Frontend** - React-based web UI that:
- Provides network visualization and management interface
- Real-time dashboards and monitoring
- Device configuration and control
- Anomaly detection with OpenAI integration

### Key Technologies
- **Go 1.24+** with extensive networking libraries
- **React 18** with Ant Design components
- **SNMP** for device management (gosnmp)
- **Hyperscan/PCRE2** for pattern matching in IDPS
- **WebSockets** for real-time communication
- **JWT** authentication
- **Caddy** web server for TLS
- **Cross-platform** support (Linux, Windows, macOS)

### Directory Structure
- `/bbrootsvc/`, `/bbnmssvc/`, `/bblogsvc/`, etc. - Individual service directories with their own Makefiles
- `/frontend/` - React web application
- `/idpsystem/` - IDPS engine and detection logic
- `/doc/` - Comprehensive documentation
- `/examples/` - Usage examples and tools

## Development Workflow

### Testing Requirements
- Testing is the highest priority - all code changes require tests
- Run tests before AND after any code modifications
- Create unit tests for all new functionality
- Never commit code without passing tests

### Branch Workflow
- Never commit directly to main branch
- Create feature branches and submit PRs
- All PRs require review and approval
- Use GitHub issues to track bugs and features

### Code Style
- Follow existing Go and React conventions
- Use double quotes and tabs in JavaScript
- Avoid large binary files in repository
- Document all features and changes

### Dependencies
- Minimal dependency principle - avoid unnecessary libraries
- Go modules managed via `go.mod`
- Frontend dependencies via `package.json`
- External libraries for cross-compilation stored in separate repo

## Common Tasks

### Development Setup
1. `sudo make install_system_packages` (Linux) - Install system dependencies
2. `make install_build_environment` - Setup build tools
3. `make dev` - Build for development

### Running Services
- Services can run with monitor mode: `-M` flag
- Check individual service documentation in `/doc/` directory
- Use `bbctl` for command-line management

### Release Process
- Follow documented release procedure in `doc/release_procedure.md`
- Version must be specified for releases
- Comprehensive testing required before release