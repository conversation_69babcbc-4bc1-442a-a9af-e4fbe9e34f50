package main

import (
	"flag"
	"fmt"
	"mnms"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime/debug"
	"sync"
	"time"

	"github.com/qeof/q"
)

var Version string

func printVersion() {
	if Version == "" {
		Version = "Development version"
	}
	// print out version
	fmt.Fprintf(os.<PERSON>derr, "BlackBear NIMBL Version: %s\n", Version)
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintln(os.Stderr, "Build information:")
	fmt.Fprintln(os.Stderr, info.GoVersion, info.Main, info.Settings)
}

func main() {
	var wg sync.WaitGroup
	q.O = "stderr"
	q.P = ""

	stop := func() {
		mnms.ClientExit()
		mnms.SyslogExit()
	}

	flag.IntVar(&mnms.QC.Port, "p", 27196, "port")
	flag.StringVar(&mnms.QC.RootURL, "r", "", "root URL")
	flag.StringVar(&mnms.QC.Name, "n", "", "name")
	flag.StringVar(&q.O, "O", "stderr", "debug log output")
	dp := flag.String("P", "", "debug log pattern string")
	debuglog := flag.Bool("debuglog", false, "enable debug log, this will override -P to .*")
	flag.BoolVar(&mnms.QC.DumpStackTrace, "ds", false, "dump stack trace when exiting with non zero code")
	nohttp := flag.Bool("nohttp", true, "no http service")
	flag.IntVar(&mnms.QC.CmdInterval, "ic", mnms.QC.CmdInterval, "command processing interval")
	flag.IntVar(&mnms.QC.RegisterInterval, "ir", mnms.QC.RegisterInterval, "service registration interval")
	flag.StringVar(&mnms.QC.SyslogServerAddr, "ss",
		":5544", "syslog server address")
	flag.StringVar(&mnms.QC.RemoteSyslogServerAddr, "rs",
		mnms.QC.RemoteSyslogServerAddr, "remote syslog server address")
	flag.BoolVar(&mnms.QC.SyslogBakAfterFwd, "sbk", false, "backup syslog after forwarding")
	flag.StringVar(&mnms.QC.SyslogLocalPath, "so", mnms.QC.SyslogLocalPath, "local path of syslog")
	flag.UintVar(&mnms.QC.SyslogFileSize, "sf", mnms.QC.SyslogFileSize, "file size(megabytes) of syslog")
	flag.BoolVar(&mnms.QC.SyslogCompress, "sc", false, "enable compress file of backup syslog")
	flagversion := flag.Bool("version", false, "print version")
	_ = flag.Bool("M", false, "monitor mode")
	var daemon string
	flag.StringVar(&daemon, mnms.DaemonFlag, "", mnms.Usage)

	// forward config
	var configFile string
	flag.StringVar(&configFile, "fc", "", "forward config file")

	flag.Parse()
	if *flagversion {
		printVersion()
		mnms.DoExit(0)
	}

	// version
	if Version != "" {
		mnms.QC.Version = Version
	}

	service := func() {
		mnms.QC.Kind = "forward"

		if *debuglog {
			*dp = ".*"
		}

		if *dp == "." {
			fmt.Fprintln(os.Stderr, "error: invalid debug pattern")
			mnms.DoExit(1)
		}
		_, err := regexp.Compile(*dp)
		if err != nil {
			fmt.Fprintf(os.Stderr, "error: invalid regular expression, %v\n", err)
			mnms.DoExit(1)
		}
		q.P = *dp
		q.Q(q.O, q.P)

		ex, err := os.Executable()
		if err != nil {
			panic(err)
		}
		exPath := filepath.Dir(ex)
		q.Q(exPath)
		// enforce -M must be the first argument if present
		for i := 1; i < len(os.Args); i++ {
			if os.Args[i] == "-M" && i != 1 {
				fmt.Fprintln(os.Stderr, "error: -M must be the first argument")
				mnms.DoExit(1)
			}
		}
		if len(os.Args) > 2 && os.Args[1] == "-M" {
			q.P = ".*"
			q.Q("monitor run mode")
			t0 := time.Now().Unix()
			ix := 0
			for {
				ix++
				runarg := fmt.Sprintf("monitor: run #%d %v", ix, os.Args)
				q.Q("monitor: run", ix, os.Args)
				err = mnms.SendSyslog(mnms.LOG_NOTICE, "monitor", runarg)
				if err != nil {
					q.Q("error: syslog", err)
				}
				ec := exec.Command(ex, os.Args[2:]...)
				ec.Dir = exPath
				output, err := ec.CombinedOutput()
				t1 := time.Now().Unix()
				diff := t1 - t0
				q.Q("monitor:", string(output))
				if diff < 3 { // XXX
					q.Q("monitor: spinning, exit")
					mnms.DoExit(1)
				}
				t0 = t1
				if err != nil {
					q.Q("monitor:", err)
					errmsg := fmt.Sprintf("monitor: #%d %v",
						ix, err.Error())
					err = mnms.SendSyslog(mnms.LOG_ERR, "monitor", errmsg)
					if err != nil {
						q.Q("error: syslog", err)
					}
					continue
				}
			}
		}

		if mnms.QC.Name == "" {
			fmt.Fprintln(os.Stderr, "error: -n name is required")
			mnms.DoExit(1)
		}
		if mnms.QC.RootURL == "" {
			fmt.Fprintln(os.Stderr, "error: -r root URL is required")
			mnms.DoExit(1)
		}

		mnms.QC.AdminToken, err = mnms.GetToken("admin")
		if err != nil {
			q.Q(err)
			fmt.Fprintln(os.Stderr, "error: can't get admin token")
			mnms.DoExit(1)
		}

		// runHttpServer
		if !*nohttp {
			wg.Add(1)
			go func() {
				defer wg.Done()
				fmt.Printf("start http server at %d\n", mnms.QC.Port)
				mnms.HTTPMain()
			}()
		}

		//CheckCmds
		wg.Add(1)
		go func() {
			defer wg.Done()
			q.Q(mnms.QC.CmdInterval)
			for {
				time.Sleep(time.Duration(mnms.QC.CmdInterval) * time.Second) // XXX
				err := mnms.CheckCmds()
				if err != nil {
					q.Q(err)
				}
			}
		}()
		//CheckCmds done

		//RegisterMain
		wg.Add(1)
		q.Q(mnms.QC.RegisterInterval)
		go func() {
			defer wg.Done()
			mnms.RegisterMain()
		}()

		//
		mnms.ForwardInit(configFile)
		wg.Add(1)
		go func() {
			defer wg.Done()
			mnms.PostForwardConfigToRoot(10)
		}()

		// startSyslogServer
		wg.Add(1)
		go func() {
			defer wg.Done()
			fmt.Println("start syslog server at", mnms.QC.SyslogServerAddr)
			mnms.StartSyslogServer()
			mnms.DoExit(1)
		}()

		// automatically check service version
		wg.Add(1)
		go func() {
			defer wg.Done()
			err = mnms.RunAutomaticallyCheckServiceVersion()
			if err != nil {
				q.Q(err)
			}
		}()

		wg.Wait()
		q.Q("exit normally")
		mnms.DoExit(0)
	}
	// enable Daemon
	s, err := mnms.NewDaemon(mnms.QC.Name, os.Args)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
	s.RegisterRunEvent(service)
	s.RegisterStopEvent(stop)
	err = s.RunMode(daemon)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
}
