package mnms

import (
	"fmt"
	"net"
	"net/http"
	"testing"
	"time"
)

func TestTcpProxy(t *testing.T) {
	// create a fake http server listen to a random port
	destPort, err := randomOpenPort()
	if err != nil {
		t.Error(err)
		return
	}
	server := HttpServer(destPort)
	defer server.Close()

	time.Sleep(1 * time.Second)

	fromPort, err := randomOpenPort()
	if err != nil {
		t.Error(err)
		return
	}
	cmd := "tcpproxy start :" + fmt.Sprintf("%d", fromPort) + " :" + fmt.Sprintf("%d", destPort)
	cmdinfo := CmdInfo{
		Timestamp: time.Now().Format(time.RFC3339),
		Command:   cmd,
	}
	RunCmd(&cmdinfo)

	time.Sleep(1 * time.Second)

	// test the proxy
	conn, err := net.Dial("tcp", "localhost:"+fmt.Sprintf("%d", fromPort))
	if err != nil {
		t.Fatalf("connection should be established")
	}
	conn.Close()

	// stop the proxy
	cmd = "tcpproxy stop :" + fmt.Sprintf("%d", fromPort)
	cmdinfo = CmdInfo{
		Timestamp: time.Now().Format(time.RFC3339),
		Command:   cmd,
	}
	RunCmd(&cmdinfo)
	time.Sleep(1 * time.Second)

	conn, err = net.Dial("tcp", "localhost:"+fmt.Sprintf("%d", fromPort))
	if err == nil {
		conn.Close()
		t.Fatalf("connection should be refused")
	}
}

func HttpServer(port int) *http.Server {
	mux := http.NewServeMux()
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("Hello, world!"))
	})

	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: mux,
	}

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Println("Failed to start HTTP server:", err)
		}
	}()

	return server
}
