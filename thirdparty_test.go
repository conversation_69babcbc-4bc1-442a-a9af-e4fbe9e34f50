package mnms

import (
	"net"
	"testing"
)

func TestIsIPInRange(t *testing.T) {
	dev := NewThirdParty()
	start := net.ParseIP("************")
	end := net.ParseIP("************")

	tests := []struct {
		ip   string
		want bool
		msg  string
	}{
		{"************", true, "equal to start"},
		{"************", true, "in range"},
		{"************", true, "equal to end"},
		{"***********", false, "below start"},
		{"************", false, "above end"},
		{"0.0.0.0", false, "far below"},
		{"***************", false, "far above"},
		{"::1", false, "IPv6 not supported"},
		{"invalid", false, "invalid format"},
	}

	for _, tc := range tests {
		ip := net.ParseIP(tc.ip)
		got := dev.isIPInRange(ip, start, end)
		if got != tc.want {
			t.Errorf("isIPInRange(%q, %q, %q) = %v, want %v (%s)", tc.ip, start, end, got, tc.want, tc.msg)
		}
	}
}
