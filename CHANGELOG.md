# v1.0.12

August 9, 2025 to August 28, 2025

## 🐞 Bug Fixes
- **Forward Command Fixes** (#1976) - Fixed configuration not saved issue, enhanced forward upload status, improved UI/test command integration with facility and severity input validation, enhanced forward command functionality with improved configuration management, channel/group guide integration, and comprehensive validation for severity, rate limit and max alert command flags
- **Agent Version Comparison Logging** (#1974) - Refactored agent version comparison system with enhanced logging, dynamic version support detection from agents instead of maintaining static tables, and added previous version mapping for backward compatibility (#1970)
- **Hierarchical Topology UI Issues** (#1971) - Fixed UI display problems where long device names were overflowing outside their containers in the hierarchical topology page (#1969)
- **Group Management Improvements** (#1968, #1964) - Enhanced group management functionality to display devices on group selection, fixed various group-related issues, and removed unnecessary image fields and nmssvc from zone configuration (#1962)
- **Syslog Message False Warnings** (#1965) - Resolved false warning messages in syslog for GWD firmware updates and agent version checks to reduce unnecessary log noise
- **Polling Service Status Display** (#1959) - Fixed polling service status to show empty when bbpollsvc is not started, and properly display online/offline status when service is running
- **Service Argument Validation** (#1958) - Enhanced argument validation for -M flag in bbnmssvc and bbrootsvc main.go files, ensuring proper argument order and improved error handling
- **Help Documentation and Syslog Structure** (#1967) - Minor fixes in help.go and improvements to syslog status structure

## 📄 Documentation & Build System
- **Installation Documentation Enhancement** (#1960) - Updated installation process to include comprehensive documentation in multiple formats (txt, pdf, md) for better user guidance (#1479)
- **Build System Updates** (#1951) - Updated MANIFEST files to include bbfwdsvc and bbpollsvc services in the build process
- **Version Management** (#1953) - Updated version information across the system
- **BBPollSvc Build Process** (#1952) - Fixed Makefile configuration for bbpollsvc service compilation
- **Makefile Improvements** (#1950) - Enhanced Makefile with better build targets and process optimization

## 🔧 System Improvements
- **Service Integration** - Enhanced integration of bbfwdsvc (Syslog Alert Forwarder Service) and bbpollsvc (Polling Service) into the main build and deployment process
- **Code Refactoring** - Improved code organization and structure across multiple services
- **Enhanced Error Handling** - Better error management and validation across various system components
- **Service Configuration** - Improved configuration management for distributed services

## 🎯 Key Technical Achievements
- **Forward Command Enhancement** - Comprehensive improvements to the syslog alert forwarding system with better validation and configuration management
- **Agent Version Management** - Dynamic agent version support detection eliminating the need for static version tables
- **UI/UX Improvements** - Fixed topology display issues and enhanced group management interface
- **Service Architecture** - Better integration of polling and forwarding services into the main system architecture
- **Documentation Integration** - Enhanced installation process with comprehensive documentation support

### 📈 Development Summary
- **Total Pull Requests**: 15 merged PRs covering August 9-28, 2025
- **Key Focus Areas**: Service integration, UI fixes, agent management, build system improvements, documentation enhancement, forward command functionality
- **Recent Developments**: Improved agent version management, better UI responsiveness, comprehensive build system updates

# v1.0.11

March 7, 2025 to August 8, 2025

## 🆕 New Features
- **Polling Services Implementation** (#1941) - Complete implementation of distributed polling services for device monitoring with enhanced UI components
- **Syslog Alert Forwarder Service** (#1926) - Added distributed syslog alert forwarding system with WhatsApp, Telegram, and MQTT support
- **Hierarchical Topology Implementation** (#1929) - Enhanced network topology visualization with hierarchical structure for complex network infrastructure
- **Model Context Protocol (MCP) Integration** - Implemented basic demo MCP server for NIMBL API integration (#1894, #1884)
- **Group Management System** - Comprehensive group management implementation with UI enhancements (#1925)
- **NIMBL Version Info Icon** - Added informational icon for NIMBL version display in sidebar footer (#1927)
- **Advanced ARP Scanning** - Enhanced ARP scanning with subnet filtering and crash protection (#1932)
- **LLM Monitor Implementation** - Added Large Language Model monitoring capabilities
- **HTTP API Validation** - Implemented comprehensive HTTP API validation system (#1924)
- **Command Validation Program** - Added program to validate manual's commands (#1911)
- **Network Analysis Examples** - Enhanced network analysis capabilities with practical examples
- **Enhanced Network Service Validation** (#1949) - Added comprehensive validation checks for network service name (-nsn flags), ensuring proper service existence, type validation, and activity status

## 🐞 Bug Fixes
- **Firmware Unit Test Fixes** (#1943) - Resolved firmware unit test failures in Unix systems with cross-platform compatibility
- **Enhanced Firmware File Handling** (#1937) - Improved firmware file retrieval methods, better error messaging, and cross-platform testing
- **ARP Issue Resolution** (#1932) - Fixed ARP crashes and added subnet filtering for improved network discovery
- **Groups Test Fixes** (#1938) - Corrected issues in group management testing functionality
- **GWD Firmware Syslog Issues** - Fixed syslog handling for GWD firmware updates (#1928)
- **Device Summary Error Handling** - Fixed "no device" error in device summary display (#1881)
- **Syslog Command Fixes** - Multiple syslog command processing improvements (#1893)
- **Tool Call Fixes** - Various tool call handling improvements
- **Frontend Build Issues** - Resolved malformed import path issues during release builds
- **UI Responsiveness** - Multiple UI improvements and bug fixes
- **Topology Label Theme Issues** (#1948) - Fixed topology labels not updating properly when switching between light and dark themes
- **UI Theme-Related Fixes** (#1946) - Resolved various UI theme inconsistencies including button hover visibility issues and layout problems
- **Topology MAC Address Display** (#1945) - Fixed topology display for devices with same IP for chassis ID and MAC address, improved device identification in network visualization

## 📄 Documentation & Build System
- **BBCTL Syslog Command Restructure** (#1905) - Streamlined syslog commands (`syslog list`, `syslog export`, `syslog exported rm`), added waiting functionality with polling, enhanced command management
- **Release Script Consolidation** - Merged release.sh functionality into Makefile for streamlined build process (#1879)
- **Makefile Enhancements** - Added individual build targets and improved build system organization (#1885)
- **Comprehensive Build Documentation** - Enhanced build system documentation with detailed target descriptions
- **API Documentation Updates** - Improved API documentation and authentication guides
- **User Manual Updates** - Enhanced user manual with latest features and procedures
- **BBPollSvc Documentation** (#1945) - Added comprehensive documentation for the polling service, including service registration and monitoring capabilities

## 🔧 System Improvements
- **Build Environment Optimization** - Streamlined build environment setup and dependency management
- **Code Cleanup** - Removed unused wgclient code and performed various code cleanup tasks (#1845)
- **Frontend Architecture** - Multiple frontend improvements and architectural enhancements
- **Service Integration** - Enhanced integration between various bb services (bbrootsvc, bblogsvc, bbnmssvc, bbidpsvc)
- **Testing Infrastructure** - Improved testing capabilities and test coverage

## 🚀 Infrastructure & DevOps
- **GitHub CI Flow Updates** (#1942) - Enhanced continuous integration with unit tests, improved workflow automation, added process for ignoring regression tests and IDPS tests in CI environment
- **Cluster Testing Framework** (#1899) - Advanced cluster testing capabilities for distributed deployments *[In Progress]*
- **Automated Release Process** - Comprehensive release automation with installer generation
- **Cross-platform Support** - Enhanced Windows and Linux build support
- **Dependency Management** - Improved Go module and npm dependency handling
- **Development Workflow** - Enhanced development workflow with smart build targets

## 🎯 Key Technical Achievements
- **Polling Services Architecture** - Complete distributed polling service implementation with real-time device monitoring
- **Cross-Platform Firmware Support** - Validated firmware upgrade processes on both Windows and Linux systems (EHG7508-4SFP tested)
- **Enhanced Command-Line Tools** - Improved BBCTL with wait functionality, command status tracking, and better user experience
- **Network Discovery Improvements** - More robust ARP handling with subnet awareness and crash prevention
- **Advanced Topology Management** - Multi-level hierarchical topology for complex network infrastructure visualization
- **MCP Server Integration** - Successfully implemented Model Context Protocol server for AI integration
- **Advanced Network Monitoring** - Enhanced network device monitoring and topology management
- **Distributed Service Architecture** - Improved distributed service communication and management
- **Real-time Alert System** - Implemented comprehensive real-time alerting with multiple platform support
- **Enhanced UI/UX** - Significant improvements to user interface and user experience

### 📈 Development Summary
- **Total Commits**: 160+ commits covering March 7 - August 8, 2025
- **Key Focus Areas**: Polling services, firmware handling, system reliability, user interface enhancements, distributed architecture, network service validation, topology visualization improvements
- **Recent Developments** (August 7-8): Enhanced network service validation, improved topology visualization with theme support and MAC address handling, comprehensive GitHub CI workflow implementation

# v1.0.10

## Feature Changes
- 🆕 **Nested NMS Support**  
  - Implemented nested NMS service support, enabling hierarchical cluster network topologies. (#1744, #1666)

- 🆕 **Manual Device Edit**  
  - Added functionality for manual device addition/editing. (#1725)

- 🆕 **Manual Topology Create/Save/Restore**  
  - Introduced manual topology creation, saving, and restoration features. (#1706, #1721)

## Fixes
- 🐞 **#1753**: Fixed SSH server startup failure.

- 🐞 **#1752**: Resolved various UI issues via refactoring.

- 🐞 **#1751**: Added missing commands in `cmd_test.go` (in `cmdinfo` test function).

- 🐞 **#1750**: Fixed verification test issues.

- 🐞 **#1749**: Added initial alert data and performed code refactoring.

- 🐞 **#1748**: Fixed issue where only one rule was allowed in IDPS.

- 🐞 **#1743**: Updated IDPS TCP header implementation.

- 🐞 **#1742**: Added alarm feature description.

- 🐞 **#1741**: Provided SSH reverse connections help.

- 🐞 **#1740**: Added method to avoid replacing edited data.

- 🐞 **#1734**: Improved IDPS IP-keyword parsing.

- 🐞 **#1731**: Fixed display of WebSocket history messages.

- 🐞 **#1730**: Corrected MAC address format validation error (`AA:BB:CC:DD:EE:FF`).

- 🐞 **#1726**: Updated service documentation.

- 🐞 **#1724**: Modified agent token renewal syslog.

- 🐞 **#1723**: Fixed failure in GWD command when user command is encrypted.

- 🐞 **#1715**: Fixed Unix test issues.

- 🐞 **#1714**: Added test cases for command validation functions.

- 🐞 **#1713**: Fixed device delete panic and corrected SNMP trap switch handling for `EHG2408`.

- 🐞 **#1712**: Resolved get commands query parameter issue.

- 🐞 **#1709**: Fixed IDPS build error (`Invalid hs_expr_ext flag`).

- 🐞 **#1708**: Corrected SNMP trap get command bug.

- 🐞 **#1701**: Fixed IDPS Hyperscan parsing rule bug.

- 🐞 **#1700**: Resolved anomaly report double count issue.

- 🐞 **#1697**: Refactored IDPS to improve performance.

- 🐞 **#1696**: Mapped `mtderase` command to the agent and GWD option.

- 🐞 **#1694**: Fixed issue with agent command execution.

- 🐞 **#1693**: Added scan command by ARP for third-party devices.

- 🐞 **#1689**: Fixed command status issue on agent.

- 🐞 **#1687**: Expanded IP and MAC fields in device information.

- 🐞 **#1686**: Removed status running flag and redundant retries.

- 🐞 **#1684**: Refactored command validation functions.

- 🐞 **#1665**: Enabled automatic service updates.

- 🐞 **#1634**: Added agent token renewal functionality.

- 🐞 **#1631**: Implemented auto verification.


# v1.0.9

## Features Changes

- 🆕 **Binary Combination**
  - Enhanced feature to combine bbnimbl into bbroot for improved efficiency and deployment. (#1590)
  
- 🆕 **Topology Device Image**
  - Bound topology device image from Nimbl server for better visualization. (#1675)

## Fixes

- 🐞 **#1678**: Fix the naming and add comments.

- 🐞 **#1677**: Fix unit test.

- 🐞 **#1672**: Fixed issue causing report upload failures.
  
- 🐞 **#1664**: Fixed report upload fail issue.
  
- 🐞 **#1662**: Updated test cases.
  
- 🐞 **#1660**: [Nimbl][Topology] Topology's refresh button doesn't seem to work.
  
- 🐞 **#1659**: Fixed topology image undefined issue.
  
- 🐞 **#1654**: Fixed issue where one command was creating two commands in `QC.CmdData`.
  
- 🐞 **#1653**: Fixed undefined title issue in all MDR dialog boxes upon closing.
  
- 🐞 **#1646**: Restricted firmware update URLs to only allow HTTP or HTTPS protocols.
  
- 🐞 **#1645**: Refactored protocol struct to extend support for new protocol parsing.
  
- 🐞 **#1644**: Added/fixed topology images, merged MDR-device page to device, and enabled running UI with `rootsvs`.
  
- 🐞 **#1641**: Fixed second occurrence of the CmdData insert command bug.
  
- 🐞 **#1639**: Fixed initial occurrence of the CmdData insert command bug.
  
- 🐞 **#1637**: Fixed issue where `npcap.exe` could not install in v1.0.8.
  
- 🐞 **#1636**: Turned off LLM debug output to reduce unnecessary logging.
  
- 🐞 **#1633**: Implemented periodic cleanup of realtime messages.
  


# v1.0.8

## Features Changes

- 🆕 **Validation Enhancements**
  - Added validation for empty fields to ensure data integrity. (#1613)
  
- 🆕 **Configuration Updates**
  - Updated GWD config command description for DHCP enable case to improve clarity. (#1614)
  
- 🆕 **GitHub Copilot Integration**
  - Enhanced development workflow by updating GitHub Copilot settings. (#1607)
  
- 🆕 **Documentation Improvements**
  - Updated old documentation and test documents for better usability and understanding. (#1609, #1606)

## Fixes

- 🐞 **#1621**: Updated tcpproxy tests to ensure reliable proxy functionality.
  
- 🐞 **#1620**: Fixed Linux error in IDS to enhance system stability.
  
- 🐞 **#1617**: Resolved pagination issue to improve data navigation and user experience.
  
- 🐞 **#1616**: Fixed IDPS Hyperscan to ensure accurate threat detection.
  
- 🐞 **#1612**: Fixed test process of IPDS to streamline testing procedures.
  
- 🐞 **#1611**: Corrected network setting bugs related to hostname and DHCP values.
  
- 🐞 **#1605**: Fixed SSH tunnel handler to ensure secure and reliable connections.
  
- 🐞 **#1583**: Fixed bug in event handling within IDPS for improved event management.
  
- 🐞 **#1581**: Resolved issue of sending alerts directly to WebSocket bypassing the syslog system.

## Documentation

- 📄 **#1609**: Updated old documentation to reflect recent changes and enhancements.
  
- 📄 **#1606**: Updated test documentation to assist in streamlined testing processes.

---
*For any questions or further details, please contact the development team.*


# v1.0.7

## Features Changes

- 🆕 Added support for Key-Value Memory Database (KVstore) in Root Service with CLI.

- 🆕 Introduced ollama as the LLM server for anomaly detection service, reducing operational costs.

- 🆕 SSH tunnel auto setup added for each device.

## Fixes

- 🐞 #1557: Root should monitor services that are running and issue alerts when a service goes down.

- 🐞 #1556: Alert message panel fixed to display correct messages.

- 🐞 #1555: Syslog command error resolved for too many arguments.

- 🐞 #1549: Fixed inconsistent behavior of the "Save and Send" button in IDPS frontend.

- 🐞 #1546: Agent client devinfo now reflects correct DHCP status.

- 🐞 #1545: Added MAC address support for SNMP get/set/walk/bulk commands.

- 🐞 #1542: Error messages for release.sh script resolved.

- 🐞 #1541: SSH tunnel close command now correctly handles ports not listed in tunnel.

- 🐞 #1537: Fixed SNMP commands API timeout and response issues.

- 🐞 #1535: SNMP Get, Bulk, Walk now handle empty "readcommunity" and "writecommunity" correctly.

- 🐞 #1534: Updated SNMP community is now set correctly in devinfo.

- 🐞 #1530: Implemented KVstore with CLI support in Root Service.

- 🐞 #1529: KVstore implementation in Root Service is complete.

- 🐞 #1528: Fixed issue with inconsistent display of agentclient version info and capabilities.

- 🐞 #1522: Resolved enable/disable port issue in devices.

- 🐞 #1521: Device ID binding in agent command fixed.

- 🐞 #1515: Restful API responses now properly include data for certain services.

- 🐞 #1509: Corrected syslog setting behavior for negative test result in script commands.

- 🐞 #1508: Username and password empty case is now handled in script command execution.

- 🐞 #1507: Script command execution now correctly handles network commands.

- 🐞 #1506: Authentication command running results via negative test case handled.

- 🐞 #1505: Enhanced script command execution for various negative test cases.

- 🐞 #1494: Test section 16 log service testing issue resolved.

- 🐞 #1493: Test section 15 agent client testing issue fixed.

- 🐞 #1492: Fixed issues in OpenVPN testing.

- 🐞 #1491: IDP testing bugs resolved.

- 🐞 #1488: Fixed issues in WireGuard testing.

- 🐞 #1487: Resolved TLS testing inconsistencies.

- 🐞 #1486: API testing issues fixed.

- 🐞 #1485: Addressed CLI testing inconsistencies.

- 🐞 #1484: Scripting testing bugs resolved.

- 🐞 #1483: Cluster mode testing issues fixed.

- 🐞 #1481: Resolved missing client name issue in Nimbl testing.

- 🐞 #1463: Resolved anomalies in real-time detection UI.

- 🐞 #1446: Corrected frontend error in bulk operation changes.

- 🐞 #1436: Nimbl UI changes for bulk operations completed.

- 🐞 #1435: Separated testbed from demo setup.

- 🐞 #1434: Log server alert issues fixed.

- 🐞 #1433: Corrected device info details in agent commands.

- 🐞 #1428: Batch command bug resolved.

- 🐞 #1427: Testbed setup and maintenance issues fixed.

- 🐞 #1416: UI command delete now works properly in backend.

- 🐞 #1415: Agent command issues related to model name resolved.

- 🐞 #1414: Corrected issues with TCP connection reset in fake client.

- 🐞 #1413: Comments for Nimbl UI commands updated.

- 🐞 #1412: Launcher bug resolved.

- 🐞 #1410: Cosmetic changes on launcher page.

- 🐞 #1409: Copyright year updated.

- 🐞 #1397: Fixed syslog display order.

- 🐞 #1395: Features missing in NIMBL vs NMU corrected.

- 🐞 #1394: Device icon issue fixed.

- 🐞 #1393: Mass-select and mass-action added to UI.

- 🐞 #1392: Search field in UI now case-insensitive.

- 🐞 #1389: Enhancement in anomaly detection runtime analysis.

- 🐞 #1388: IDPS suggestions implemented.

- 🐞 #1387: IDPS alert UI issues fixed.

- 🐞 #1385: Fixed anomaly detection UI pagination.

- 🐞 #1384: IDPS event log pagination corrected.

- 🐞 #1375: Anomaly detection fixes implemented.

- 🐞 #1373: Fixed issues related to SSH tunnel handling.

- 🐞 #1360: Corrected root command client settings.

- 🐞 #1354: Corrected garbage can icon issue in SSH tunnels.

- 🐞 #1351: SSH tunnel and web bug issues fixed.

- 🐞 #1344: Timeout mechanism added for commands.

- 🐞 #1340: Fixed bblauncher issue with client status not reflecting properly.

- 🐞 #1338: Waiting for rootsvc startup in bblauncher handled correctly.

- 🐞 #1334: Anomaly feature testing observation fixes.

- 🐞 #1332: bblauncher suggestions and fixes implemented.

- 🐞 #1323: View result button issue corrected.

- 🐞 #1313: Nimbl script command execution now properly handles the "-cc" flag.

- 🐞 #1312: Fixed internal server error on script upload.

- 🐞 #1308: Trap info retrieval issue fixed for agent command.

- 🐞 #1307: Trap add/del command corrected.

- 🐞 #1306: Fixed error during knowledge backup/restore.

- 🐞 #1303: Offline devices are now deleted in real-time from the device list page.

- 🐞 #1302: Corrected firmware update error for devices.

- 🐞 #1301: IP address configuration error fixed in agent command for devices.

- 🐞 #1299: Fixed issue with enabling the syslog server through agent command.

- 🐞 #1298: Addressed the problem with unsupported "agent snmp enable" command on some devices.

- 🐞 #1297: Corrected incorrect NIMBL version display.

- 🐞 #1291: UI enhancements for command list.

- 🐞 #1282: Crash in checkLicense.go resolved.

- 🐞 #1278: Corrected blackbear logo display in dark mode.

- 🐞 #1273: Release ZIP files placed under NIMBL directory.

- 🐞 #1272: Easy one-click starter app fixes.

- 🐞 #1271: Installer enhancements.

- 🐞 #1269: Fixed Nmssvc name display issue after agent commands.

- 🐞 #1266: Resolved UI issues with IDPS and Anomaly services visibility.

- 🐞 #1262: Addressed long-term UI running issues.

- 🐞 #1261: Added SSH tunnel auto setup per device.

- 🐞 #1260: Corrected anomaly UI message context.

- 🐞 #1259: IDP UI changes implemented.

- 🐞 #1258: Deletion of anomaly reports enabled.

- 🐞 #1257: Corrected anomaly/error count display in reports.

- 🐞 #1256: Implemented requirements for bbanomsvc report and training.

- 🐞 #1247: Resolved issue in retrieveRootCmd function.

- 🐞 #1244: Updated UI anomaly details.

- 🐞 #1243: Detect log file UI now properly shows the client name.

- 🐞 #1240: Made IDPS rules and anomaly files available on bbtechhive.

- 🐞 #1234: IDPS ported to run inside switch devices.

- 🐞 #1233: IDPS now uses eBPF for packet processing.

- 🐞 #1219: Corrected device error messages when left idle.

- 🐞 #1218: Fixed device reboot error messages.

- 🐞 #1216: LLM backend using ollama does not incur additional cost.

- 🐞 #1206: Corrected status setting when running commands with all flags when IDPS, Anomaly, and Log services are active.

- 🐞 #1201: Fixed network settings issue for EHG2408.

- 🐞 #1195: Ollama integration to allow different LLM backends.

- 🐞 #1151: Enhanced command space parsing logic for better consistency.


# v1.0.6

## Features Changes

- 🆕 Anomaly detection service supports ollama

## Fixes

- 🐞#1218: Display error message when restarting nimble and rebooting device.

- 🐞#1237: Anomaly page URL validation.

- 🐞#1217: Anomaly stats display incorrect success message.

- 🐞#1228: Rule client-wise should display properly after import rule again.

- 🐞#1222: System name should display properly.

- 🐞#1224: Log list should not only display 100 items.

- 🐞#1223: SNMP walk page 1 result should display properly.

- 🐞#1220: Anomaly detect-related message are displayed incompletely in Google Chrome.

- 🐞#1219: Device displays error message when running NIMBL and leaving devices idle for a period of time.

- 🐞#1221: "Firmware upgrade" command is wrong in the user manual.

- 🐞#1196: bbanomsvc does not have -rs flag.

- 🐞#1198: bbanomsvc doc in user manual talks about -p but bbanomsvc does not support -p.

- 🐞#1197: bbanomsvc doc is not up to date.

- 🐞#1207: Not getting auto detect report history in anomaly report even after 5 min.

- 🐞#1139: Use syslog for all logs.


# v1.0.5

## Features Changes

- 🆕 Add anomaly detection service, bbanomsvc.

- 🆕 Add idps service, bbidpsvc.

- 🆕 Add log service, bblogsvc.

- 🆕 Add bbrootsvc, bbnmssvc and bbctl.

- 🆕 Remove bbnim.

- 🆕 Introduce high level commands.

- 🆕 Organize commands.

- 🆕 Organize flags.

## Fixes

- 🐞#1180: Highlevel save running config OK button not working.

- 🐞#1175: Fix issue of testing opcua.

- 🐞#1171: IDPS with basic rule when startup.

- 🐞#1168: Command status not setting and not getting command response on GET handle command.

- 🐞#1166: Anomaly backup database, import anomalies, import normal logs features.

- 🐞#1160: Select specific command by different service.

- 🐞#1159: Agent command not updating status, keeps running the command.

- 🐞#1146: Refactor and separate function forwarding syslog to remote.

- 🐞#1141: Remove ID from IDPS file name.

- 🐞#1140: Updation of test commands in cmd_test.go.

- 🐞#1132: High-level command not updating tag to low-level agent command.

- 🐞#1127: Command run at root.

- 🐞#1126: Get rid of PostgreSQL dependency.

- 🐞#1125: Encrypt OpenAI API key.

- 🐞#1119: IDPS detect loopback unnecessarily.

- 🐞#1108: Remove mnmsctl in documentation.

- 🐞#1098: Agent get syslog result and config get syslog (using SNMP) result not consistent.

- 🐞#1093: IDPS report UI implementation.

- 🐞#1079: Improve syslog and anomaly features to enhance user experience.

- 🐞#1068: Frontend should convert special characters when cmd with special characters in script page.

- 🐞#1068: Parameters id of HandleCommands have conflict with command if it with id.

- 🐞#1060: More anomaly functions.

- 🐞#1040: Anomaly session command.

- 🐞#1038: Anomaly service document.

- 🐞#1021: How to get network service commands?

- 🐞#1019: High-level Firmware Upgrade Command Implementation.

- 🐞#1018: High-level Command for Trap Setting Implementation.

- 🐞#1017: High-level Command for Syslog Setting Implementation.

- 🐞#1016: High-level Save Running Config Command Implementation.

- 🐞#1015: High-level Enable SNMP Command Implementation.

- 🐞#1014: High-level Beep Command Implementation.

- 🐞#1013: High-level Network Command Implementation.

- 🐞#1012: High-level Reset/Reboot Command.

- 🐞#1006: Syslog Goes into Infinite Loop When NMS-svc Starts Pointing to localhost:5514.

- 🐞#995: Nimbl Network Service Crash When Running at Testbed.

- 🐞#993: [Nimbl][Update-bbrootconfig.enc] Can’t Update bbrootconfig.enc.

- 🐞#991: [Nimbl][License] License File Should Work Without Error Message.

- 🐞#990: [Nimbl][Wireguard] Client Service Not Running After Running “wg root start” and “wg start” Commands.

- 🐞#989: Separate NMS Root Service from NMS Service and NMSctl.

- 🐞#986: Scanproto and a List of Different Scan Methods.

- 🐞#984: [Nimbl][UserManagement] Block Modification of Role to Superuser for Default Admin

- 🐞#983: [Nimbl][Script-SNMP] The Result of Script Should Always Display “Error” After Updating Device’s SNMP Communities

- 🐞#981: [Nimbl][Suggestion] Nimbl Does Not Support “Schedule Backup” Function Compared to NMU

- 🐞#980: [Nimbl][Agent] Can’t Get IP by DHCP Server Successfully on Agent-Model

- 🐞#979: [Nimbl][Agent-fw Update] Unable to Upgrade EH75xx Firmware by Agent Scripts

- 🐞#978: [Nimbl][Agent] Can’t Set Gateway (GW) to 0.0.0.0 Successfully on Agent-Model

- 🐞#977: [Nimbl][Agent] “Agent Network Command” Is Abnormal on Agent-Model (EH7520/EHG7504/EHG7604/EHG6508)

- 🐞#975: [Nimbl][Agent] Agent Device Will Change to GWD When Restarting Root/Client Service

- 🐞#974: [Nimbl][Device] Device Info Display Inconsistent on GWD/Agent Model

- 🐞#973: [Nimbl][Web UI] Devices Function Failures (Saving Running Config Not Working on RHG7628)

- 🐞#972: [Nimbl][Device] The Offline Check -disable Can’t Work Normally

- 🐞#971: [Nimbl][Agent][Device] The Offline of Device List Can’t Display Normally on Agent Device

- 🐞#970: [Nimbl][SNMP] It’s Failed to Enable SNMP/Syslog/Trap on NSG330X

- 🐞#967: Basic Syslogsvc Service Command

- 🐞#959: GO Test Fail About TestLicenseTimerExit

- 🐞#952: If License File Does Not Exist, We Should Quit Root Service After One Hour

- 🐞#951: Code and Build Process Clean Up

- 🐞#949: Go Test Fail

- 🐞#941: Do the Syslog/IDPS Service Need HTTP Function?

- 🐞#935: Separate Debug Log Cmds from Syslog Commands and Cleanup Documentation

- 🐞#932: Add Agent -ns Flag Warning Message

- 🐞#929: SNMP Walk Result UI Pagination Not Working

- 🐞#925: Device Offline Mechanism Makes UI Hard to Use

- 🐞#921: License Verification Fails When We Start Daemon Service

- 🐞#918: Log Document Is Unclear, Do Not Understand Its Meaning

- 🐞#915: Blackbear Annual Event Present

- 🐞#905: Separate Services and Different Service Types

- 🐞#885: Commands Array Editing

- 🐞#880: IDPS Work on Client

- 🐞#773: For New Features, Flags and Enable/Disable Feature API Are Different

- 🐞#725: Integrate with Syslog Anomaly Detection System

- 🐞#712: Topology Support for Agent

- 🐞#510: Anomaly Detection

- 🐞#387: EHG2408 Firmware Upgrade Error Bug


# v1.0.4

## Features Changes

- 🆕 Add agent support.

- 🆕 Add anomaly detection.

- 🆕 Add idps (Linux only).

- 🆕 Show more hints when running frontend service(bbnimbl) .

- 🆕 NIMBL software name changed from `mnmsctl` to `bbnim`; frontend name changed from `frontend` to `bbnimbl`

- 🆕 Update user manual including agent, anomaly detection and idps features.

## Fixes

- 🐞#421: Topology should update automatically when unplug and plug cable.

- 🐞#524: Trap messages (port link up/link down/cold start/warm start/authentication Failure) should display clearly in Alert Message and Logs.

- 🐞#539: Unable to configure syslog setting successfully with Script.

- 🐞#644: Fix web browser logo is still atop.

- 🐞#665: Dashboard command details page option doesn't work.

- 🐞#667: Date of Start/Now display incorrectly for Export file (PDF/CSV)

- 🐞#669: Mark dhcp on the list of devices

- 🐞#671: Can't save running-config (Syslog/Trap) for EMG8510/RHG7628/EHG2408/RHG7528/RHG9628

- 🐞#672: Script accepts wrong command and displays "ok" but it does't actually work

- 🐞#673: Script command "switch show ip command" does not work on RHG7628/ EHG9608

- 🐞#675: Can't change Network setting when wireguard start

- 🐞#678: Can't set EHG6510 trap server via NMS

- 🐞#679: Can't display Trap message in Alert Message and Logs for L2 EH model/L3 EHG76xx model

- 🐞#702: Topology page display wrong client's data

- 🐞#714: Added automated regression test cases for NMS.

- 🐞#725: Integrate with Syslog Anomaly Detection System.

- 🐞#741: Detect Device Syslog Anomalies in NMS.

- 🐞#772: Flags and Build Tags for New License-able Feature (Anomaly and IDPS).

- 🐞#773: Different APIs for Flags and Enable/Disable Feature for New Features.

- 🐞#776: Added regression tests for All Agent Commands.

- 🐞#779: Depending on the Scanproto in devices struct we need to send agent commands as needed.

- 🐞#780: Validate Enable Features with License File.

- 🐞#782: Add api which enable flag on idps.

- 🐞#785: Fix cross compile windows binary due to cgo.

- 🐞#787: Each action /connection we make to the device should be done with timeout.

- 🐞#794: Anomaly detection license check.

- 🐞#795: Add api which export file on idps.

- 🐞#800: Add restful api which import file and add some summary.

- 🐞#805: Fix-sometime-mqtt-test-fail.

- 🐞#806: Added Set open ai key.

- 🐞#807: Fix bug on sending alert message to websocket.

- 🐞#814: Add api to save agent openvpn key file.

- 🐞#816: Add Version Information, License Information, and GET /license-info API.

- 🐞#821: Added offlineCheck flag for device online and offline.

- 🐞#836: Fix issue idps flags in building.

- 🐞#841: Change the format of an IDPS (Intrusion Detection and Prevention System) log entry.

- 🐞#907: Resolved the bug that allowed registration of services with the same name.

- 🐞#911: Fix password validator failure in the 'Add New User' user interface.

# v1.0.3

## Features Changes

- 🆕Feature License key support.
- 🆕Feature disabled sign in button for 10sec,if login failed.
- 🆕Feature of Wireguard VPN for NMS.
- 🆕Feature Implemented RSTP/ERPS blocked ports.

- 🆕Created user manual documentation for mnms usage, how to use nms, install, set up cluster, etc.

- 🆕Client Active and Inactive status.

- 🆕Root server status component.

- 🆕Added Changelog.md file.

- 🆕In topology added new components like Physics,Fit view point ,Edit node position etc..
- 🆕Added device model field in topology.

- 🆕Added "log clear" Command to Clear the QC.Logs.

## Fixes

- 🐞#533:Fix issue of enabling SNMP via Script.
- 🐞#567:Fix Client Active and Inactive status via Timestamp.

- 🐞#546:Modified Firmware status messages.
- 🐞#545:Fix MTDErase for EHG6XXX Series by providing error message.
- 🐞#522:Fix Hostname and Appname in Logs.
- 🐞#547:Fix complete message display in Response Result.

- 🐞#539:Fix SNMP error message.
- 🐞#515:Fix NMS terminologies rectification.

- 🐞#546:Modified firmware status messages.

- 🐞#534:Not allowed to delete admin role in user management.

- 🐞#543:Fix V1.0.2 can't display devices.

- 🐞#540:Fix log file size more than 1024 KB.

- 🐞#520:Fix syslog message display in logs for particular dates.

- 🐞#524:Fix Trap messages display in alert message and logs.

- 🐞#521:Fix topology shouldn't change when root with 2 or more clients.

- 📦 Remove all pop up notification.

- 🐞#640:Fix snmp trap cause mnms crash #641.

# v1.0.2

## Features Changes

-🆕Implementation of 2FA.

-🆕Implementation of daemon service flags.

## Fixes

- 🐞#507:Added blackbear site link.

- 🐞#505:Login page logo changed.

- 🐞#503:Logo changed.

- 🐞#501:Fix import bbrootconfig.enc file bug.

- 🐞#497:Save running config.

- 🐞#495:Get and set syslog setting to UI.

- 🐞#493:Add command config switch save into help.

- 🐞#492:Change commnad which is about save config.

- 🐞#490:Add command which get syslog setting.

- 🐞#468:Update password rule.

- 🐞#467:Fix 2fa #499,#372

- 🐞#465:When Ip address is incorrect it does not beep/reboot/reset.

- 🐞#460:Switch cli support list update.

- 🐞#447:Fix support EHG2408 reset,snmp,syslog and network setting.

- 🐞#481:Added -nohttp flag,#478

- 🐞#485:Updated go doc #477

- 🐞#475:Fix topology ui show error when get a lldp mac address which is not exist in device list.

- 🐞#442:Fix cluster web timestamp #474

- 🐞#464:Fix reset device #444

- 🐞#413:Fix Root can't display syslog message and SNMP trap send from devices #458

- 🐞#461:Fix which can't enable snmp in EHG2408 series device #438

- 🐞#463:Fix do not append status when cancelled #430

- 🐞#469:Fix gwd dhcp fail #440

- 🐞#408:Fix issue root check command #405

- 🐞#404:Fix which can't keep running status, #393

- 🐞#401:Fix enable snmp EHG model devices, #401

- 🐞#376:Added MIB browser missing community, version, and port settings,#388

- 🐞#457:Fix Topology's web page should keep the last modification when the page jump out and back.

- 🐞#414:Fix Timestamp display wrong time in Logs and command detail.

- 🐞#377:Fix setting ip fail.

- 🐞#373:Fix snmp return error.

- 🐞#640:Fix script doesn't give feedback with invalid role permission.
