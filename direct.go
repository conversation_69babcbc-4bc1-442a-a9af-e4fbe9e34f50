package mnms

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"

	"github.com/qeof/q"
)

// direct commands
type directCommands struct {
	GenerateRSAKeyPair bool   // generate rsa key pair
	Decrypt            bool   // decrypt something
	Encrypt            bool   // encrypt something
	Export             bool   // export nimbl config
	Import             bool   // import nimbl config
	PublickeyPath      string // public key path
	PrivatekeyPath     string // private key path
	In                 string // input file
	Out                string // output file
	Name               string // name
	RootURL            string // root url
}

// runUtilImport run util import command
// util import config -in {in_file} -root {root_url}
func runUtilImport(cmds []string, dc *directCommands) error {
	if len(cmds) < 2 {
		fmt.Fprintf(os.Stderr, "Missing resource type\n")
		fmt.Fprintf(os.Stderr, "%s", utilImportHelpText)
		return nil
	}

	inputFile, err := getFileWithDefault(dc.In, os.Stdin)
	if err != nil {
		return err
	}
	if inputFile != os.Stdin {
		defer inputFile.Close()
	}

	resourceKind := cmds[1]

	switch resourceKind {
	case "config":
		// validate config file with  POST dc.Root/api/v1/validateconfig endpoint form key is configfile
		file := dc.In
		url := fmt.Sprintf("%s/api/v1/validateconfig", dc.RootURL)
		requestBody := new(bytes.Buffer)
		writer := multipart.NewWriter(requestBody)
		// Add the file to the form
		fileHandle, err := os.Open(file)
		if err != nil {
			return fmt.Errorf("error opening file: %s", err.Error())
		}
		defer fileHandle.Close()
		part, err := writer.CreateFormFile("configfile", filepath.Base(file))
		if err != nil {
			return fmt.Errorf("error creating form file: %s", err.Error())
		}
		_, err = io.Copy(part, fileHandle)
		if err != nil {
			return fmt.Errorf("error copying file contents: %s", err.Error())
		}
		// Close the multipart form
		err = writer.Close()
		if err != nil {
			return fmt.Errorf("error closing form: %s", err.Error())
		}
		request, err := http.NewRequest("POST", url, requestBody)
		if err != nil {
			return fmt.Errorf("error creating request: %s", err.Error())
		}
		request.Header.Set("Content-Type", writer.FormDataContentType())

		// Send the request and print the response
		client := &http.Client{}
		resp, err := client.Do(request)
		if err != nil {
			return fmt.Errorf("error sending request: %s", err.Error())
		}
		defer resp.Body.Close()
		// check resp status code is 200
		if resp.StatusCode != 200 {
			// get error message from response body
			bodyBytes, err := io.ReadAll(resp.Body)
			if err != nil {
				return err
			}
			return fmt.Errorf("config file is not valid: %s", string(bodyBytes))
		}

		return nil

	default:
		fmt.Fprintf(os.Stderr, "Resource %s is not supported", resourceKind)
		fmt.Fprintf(os.Stderr, "%s", utilImportHelpText)
	}
	return nil
}

// runUtilExport run util export command
// util export config
// util export default-public-key
func runUtilExport(cmds []string, dc *directCommands) error {
	// check resource
	if len(cmds) < 2 {
		fmt.Fprintf(os.Stderr, "%s", utilExportHelpText)
		return nil
	}
	// bbnim util export [what] -out {output_file} -pubkey {public_key_file}

	resourceKind := cmds[1]
	outputFile, err := getFileWithDefault(dc.Out, os.Stdout)
	if err != nil {
		return err
	}
	if outputFile != os.Stdout {
		defer outputFile.Close()
	}

	switch resourceKind {
	case "config":
		if dc.PublickeyPath == "" {
			return fmt.Errorf("public key is required, specify public key with -pubkey flag")
		}

		// read public key
		pubkeyBytes, err := os.ReadFile(dc.PublickeyPath)
		if err != nil {
			return err
		}
		c, err := GetMNMSConfig()
		if err != nil {
			return err
		}
		// export nimbl config
		configJSON, err := json.Marshal(c)
		if err != nil {
			return err
		}

		// encrypt with public key
		encryptedConfig, err := EncryptWithPublicKey(configJSON, pubkeyBytes)
		if err != nil {
			return err
		}
		// write encrypted config to output file
		_, err = outputFile.Write(encryptedConfig)
		if err != nil {
			return err
		}

		return nil
	case "default-public-key":
		// export default public key
		outputFile, err := getFileWithDefault(dc.Out, os.Stdout)
		if err != nil {
			return err
		}
		if outputFile != os.Stdout {
			defer outputFile.Close()
		}
		pubkeyBytes, err := GenerateOwnPublickey()
		if err != nil {
			return err
		}
		_, err = outputFile.Write(pubkeyBytes)
		if err != nil {
			return err
		}
		return nil
	default:
		fmt.Fprintf(os.Stderr, "%s", utilExportHelpText)
	}

	return nil
}

// getFileWithDefault get io.Writer from filename, if filename is empty, return os.Stdout
func getFileWithDefault(filename string, def *os.File) (*os.File, error) {
	if filename == "" {
		return def, nil
	}

	f, err := os.OpenFile(filename, os.O_CREATE|os.O_RDWR, 0o600)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	return f, nil
}

// parseSubCommands parse sub commands,bbnim util [subcommands ...] [flags]
// return sub commands and flags
func parseSubCommands() ([]string, []string, error) {
	if len(os.Args) < 3 {
		return nil, nil, fmt.Errorf("%s", utilExportHelpText)
	}
	// collect sub commands
	var subCommands []string
	var flags []string
	for idx, arg := range os.Args[2:] {
		if !strings.HasPrefix(arg, "-") {
			subCommands = append(subCommands, arg)
			continue
		}
		// add remaining args to flags
		if idx > 0 {
			flags = append(flags, os.Args[2+idx:]...)
			break
		}
	}
	return subCommands, flags, nil
}

// ProcessDirectCommands checks if there is any direct commands
/*
Utility command.

Usage: util genpair
	To Generate rsa key pair. default output is
	$HOME/.mnms/id_rsa and $HOME/.mnms/id_rsa.pub

Example :
	util genpair

Usage: util genpair -name [file_prefix]
	Generate rsa key pair to [file_prefix].pub and [file_prefix]
	[file_prefix]  : output file prefix (optional)

Example :
	util genpair -name ~/nimblkey

Usage: util encrypt -pubkey [pubkey_file] -in [in_file] -out [out_file]
	Encrypt [in_file] with [pubkey_file] and output to [out_file].
	If [out_file] is empty, output to stdout. if [in_file] is empty,
	input from stdin.

	[pubkey_file]  : public key file
	[in_file]      : input file (optional)
	[out_file]     : output file (optional)

Example :
	util encrypt -pubkey C:/Users/<USER>/nimbl.pub -in nimbl.conf.enc -out nimbl.conf

Usage: util decrypt -privkey [privkey_file] -in [in_file] -out [out_file]
	Decrypt [in_file] with [privkey_file] and output to [out_file].
	If [out_file] is empty, output to stdout. if [in_file] is empty,
	input from stdin.

	[privkey_file] : private key file
	[in_file]      : input file (optional)
	[out_file]     : output file (optional)

Example :
	util decrypt -privkey C:/Users/<USER>/nimbl -in nimbl.conf.enc -out nimbl.conf

Usage : util export [resource] -pubkey [pubkey_file] -out [out_file]
	Export specified resource, for secure reason NIMBL ask user to input the
	public key that use to encrypt the resource. If [out_file] is empty,
	output to stdout.
Resource:
	- config: export nimbl config file, flag -pubkey is required
	- default-public-key: Export the NIMBL default public key.
		Use with caution; intended for testing purposes only.
		It's used for encryption when importing data without
		a specified private key at NIMBL startup
Arguments:
	[resource]    : resource name, such as 'config'.
	[pubkey_file]: public key file path
Example :
	util export config -pubkey C:/Users/<USER>/nimbl.pub -out nimbl.conf.enc
	util export config -pubkey C:/Users/<USER>/nimbl.pub > nimbl.conf.enc
	util export default-public-key -out nimbl.pub


Usage: util import [resource] -in [in_file] -root [root_url]
	Import resource from [in_file]. If [in_file] is empty, input from stdin.
	input file must be encrypted by public key that corresponding to the NIMBL root's
	private key.
Resource:
	- config: import nimbl config file
Arguments:
	[in_file]  : input file
	[root_url] : nimbl root url
Example :
	util import config -in nimbl.conf -root http://localhost:27182`


Usage: util logdetect -in [in_file] -out [out_file]
	logdetect [in_file] and output to [out_file].
	If [in_file] is empty, input from stdin. If [out_file] is empty, output to stdout.

	[in_file]  : input file (optional)
	[out_file] : output file (optional)

Example :
	util logdetect -in syslog.log -out syslog.log.analyze
*/
func ProcessDirectCommands() error {

	dc := directCommands{
		// default values
		GenerateRSAKeyPair: false,
		Decrypt:            false,
		Export:             false,
		Import:             false,
		Encrypt:            false,
	}

	subCmds, flags, err := parseSubCommands()
	if err != nil {
		fmt.Fprintf(os.Stderr, "%s", utilExportHelpText)
	}

	// bbnim util [command] xxx xxx xxx
	subsetFlags := flag.NewFlagSet("subset", flag.ExitOnError)
	subsetFlags.StringVar(&dc.PublickeyPath, "pubkey", "", "public key path")
	subsetFlags.StringVar(&dc.PrivatekeyPath, "privkey", "", "private key path")
	subsetFlags.StringVar(&dc.In, "in", "", "input file")
	subsetFlags.StringVar(&dc.Out, "out", "", "output file")
	subsetFlags.StringVar(&dc.Name, "name", "", "name")
	subsetFlags.StringVar(&dc.RootURL, "root", "", "root url")
	help := subsetFlags.Bool("help", false, "help")
	if err := subsetFlags.Parse(flags); err != nil {
		fmt.Printf("err %v", err)
		return err
	}

	switch subCmds[0] {
	case "genpair":
		dc.GenerateRSAKeyPair = true
	case "decrypt":
		dc.Decrypt = true
	case "encrypt":
		dc.Encrypt = true
	case "import":
		return runUtilImport(subCmds, &dc)
	case "export":
		return runUtilExport(subCmds, &dc)

	default:
		return fmt.Errorf("unsupported command: %s", os.Args[2])
	}

	// dump usage
	if *help {
		subsetFlags.Usage()
		return nil
	}

	mnmsFolder, err := CheckMNMSFolder()
	if err != nil {
		return err
	}

	// bbnim util genpair -pubkey {public_key_file} -privkey {private_key_file}
	if dc.GenerateRSAKeyPair {
		fmt.Fprintln(os.Stderr, "generating rsa key pair...")
		prikeyPath := path.Join(mnmsFolder, "id_rsa")
		pubkeyPath := path.Join(mnmsFolder, "id_rsa.pub")

		if len(dc.Name) > 0 {

			prikeyPath = dc.Name
			pubkeyPath = dc.Name + ".pub"
		}

		fmt.Fprintln(os.Stderr, "output private key to", prikeyPath)
		fmt.Fprintln(os.Stderr, "output public key to", pubkeyPath)

		// generate rsa key pair
		prikey, err := GenerateRSAKeyPair(4096)
		if err != nil {
			return err
		}
		// generate private and public key bytes
		prikeyBytes, err := EndcodePrivateKeyToPEM(prikey)
		if err != nil {
			return err
		}
		// write private key to prikeyPath
		err = os.WriteFile(prikeyPath, prikeyBytes, 0o600)
		if err != nil {
			return err
		}

		pubkeyBytes, err := GenerateRSAPublickey(prikey)
		if err != nil {
			return err
		}

		// write public key to pubkeyPath
		err = os.WriteFile(pubkeyPath, pubkeyBytes, 0o644)
		if err != nil {
			return err
		}

		return nil
	}

	// bbnim util encrypt -in {plain_file} -out {cipher_file} -pubkey {public_key_file}
	if dc.Encrypt {
		inputFile, err := getFileWithDefault(dc.In, os.Stdin)
		if err != nil {
			return err
		}
		if inputFile != os.Stdin {
			defer inputFile.Close()
		}

		outputFile, err := getFileWithDefault(dc.Out, os.Stdout)
		if err != nil {
			return err
		}
		if outputFile != os.Stdout {
			defer outputFile.Close()
		}

		if dc.PublickeyPath == "" {
			return fmt.Errorf("public key is required, specify public key with -pubkey flag")
		}
		// read public key
		pubkeyBytes, err := os.ReadFile(dc.PublickeyPath)
		if err != nil {
			return err
		}
		// read plain text
		plainBytes, err := io.ReadAll(inputFile)
		if err != nil {
			return err
		}
		// encrypt plain text
		cipherBytes, err := EncryptWithPublicKey(plainBytes, pubkeyBytes)
		if err != nil {
			return err
		}
		// write cipher text to output file
		_, err = outputFile.Write(cipherBytes)
		if err != nil {
			return err
		}
		return nil
	}

	// bbnim util decrypt -in {cipher_file} -out {plain_file} -privkey {private_key_file}
	if dc.Decrypt {
		inputFile, err := getFileWithDefault(dc.In, os.Stdin)
		if err != nil {
			return err
		}
		if inputFile != os.Stdin {
			defer inputFile.Close()
		}

		output, err := getFileWithDefault(dc.Out, os.Stdout)
		if err != nil {
			return err
		}
		if output != os.Stdout {
			defer output.Close()
		}

		if dc.PrivatekeyPath == "" {
			return fmt.Errorf("private key is required")
		}
		// read private key
		prikeyBytes, err := os.ReadFile(dc.PrivatekeyPath)
		if err != nil {
			return err
		}
		// read encrypted pass
		cipherBytes, err := os.ReadFile(dc.In)
		if err != nil {
			return err
		}
		// decrypt pass
		decryptedPass, err := DecryptWithPrivateKeyPEM(cipherBytes, prikeyBytes)
		if err != nil {
			return err
		}
		// write decrypted data to output file
		_, err = output.Write(decryptedPass)
		if err != nil {
			return err
		}

		return nil
	}

	// bbnim util import -in {config_file} -root {root_url}

	return nil

	// }
	// return nil
}
