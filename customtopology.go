package mnms

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/qeof/q"
)

// CustomTopologyLLDPConnection represents an LLDP-discovered connection between devices
type CustomTopologyLLDPConnection struct {
	ID             string `json:"id"`             // Unique connection ID
	SourceMac      string `json:"sourceMac"`      // Source device MAC
	TargetMac      string `json:"targetMac"`      // Target device MAC
	SourcePort     string `json:"sourcePort"`     // Source port name
	TargetPort     string `json:"targetPort"`     // Target port name
	BlockedPort    string `json:"blockedPort"`    // "true" or "false" - STP/RSTP blocked port
	ConnectionType string `json:"connectionType"` // "lldp" or "manual"
	Status         string `json:"status"`         // "active", "inactive", "unknown"
	LastDiscovered string `json:"lastDiscovered"` // Timestamp of last LLDP discovery
	TreeName       string `json:"treeName"`       // Custom topology tree name
}

// CustomTopologyNode represents a node in the custom topology tree
type CustomTopologyNode struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Type       string                 `json:"type"` // "node" or "device"
	IPAddress  string                 `json:"ipAddress,omitempty"`
	MacAddress string                 `json:"macAddress,omitempty"`
	ModelName  string                 `json:"modelName,omitempty"`
	Children   []*CustomTopologyNode  `json:"children,omitempty"`
	Properties map[string]interface{} `json:"properties,omitempty"`
	CreatedAt  string                 `json:"createdAt"`
	UpdatedAt  string                 `json:"updatedAt"`
}

// CustomTopologyTree represents the entire custom topology structure
// NOTE: Connections are not stored here - they are dynamically computed from QC.TopologyData
// to avoid data duplication and ensure consistency with device online/offline status
type CustomTopologyTree struct {
	Name      string                `json:"name"`
	Comment   string                `json:"comment,omitempty"`
	Nodes     []*CustomTopologyNode `json:"nodes"`
	CreatedAt string                `json:"createdAt"`
	UpdatedAt string                `json:"updatedAt"`
}

// CustomTopologyManager handles all custom topology operations
// NOTE: Access via QC.CustomTopologyMgr (initialized in qc.go)
type CustomTopologyManager struct {
	mutex sync.RWMutex
	trees map[string]*CustomTopologyTree // treeName -> tree data
}

// generateNodeID generates a unique ID for a node
func (ctm *CustomTopologyManager) generateNodeID() string {
	return fmt.Sprintf("node_%d_%s", time.Now().UnixNano(), randomString(8))
}

// randomString generates a random string of given length
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

// CreateTree creates a new custom topology tree
func (ctm *CustomTopologyManager) CreateTree(name, comment string) error {
	ctm.mutex.Lock()
	defer ctm.mutex.Unlock()

	if _, exists := ctm.trees[name]; exists {
		return fmt.Errorf("custom topology tree '%s' already exists", name)
	}

	now := time.Now().Format(time.RFC3339)
	ctm.trees[name] = &CustomTopologyTree{
		Name:      name,
		Comment:   comment,
		Nodes:     []*CustomTopologyNode{},
		CreatedAt: now,
		UpdatedAt: now,
	}

	q.Q("Created custom topology tree:", name)
	return ctm.persistTrees()
}

// GetTree retrieves a custom topology tree by name
func (ctm *CustomTopologyManager) GetTree(name string) (*CustomTopologyTree, error) {
	ctm.mutex.RLock()
	defer ctm.mutex.RUnlock()

	tree, exists := ctm.trees[name]
	if !exists {
		return nil, fmt.Errorf("custom topology tree '%s' not found", name)
	}

	// Clean up any null nodes before returning
	if tree != nil && tree.Nodes != nil {
		// Create a copy to avoid modifying the original during read lock
		treeCopy := *tree
		treeCopy.Nodes = ctm.cleanupNullNodes(tree.Nodes)
		return &treeCopy, nil
	}

	return tree, nil
}

// ListTrees returns all custom topology trees
func (ctm *CustomTopologyManager) ListTrees() map[string]*CustomTopologyTree {
	ctm.mutex.RLock()
	defer ctm.mutex.RUnlock()

	result := make(map[string]*CustomTopologyTree)
	for k, v := range ctm.trees {
		result[k] = v
	}
	return result
}

// DeleteTree deletes a custom topology tree
func (ctm *CustomTopologyManager) DeleteTree(name string) error {
	ctm.mutex.Lock()
	defer ctm.mutex.Unlock()

	if _, exists := ctm.trees[name]; !exists {
		return fmt.Errorf("custom topology tree '%s' not found", name)
	}

	delete(ctm.trees, name)
	q.Q("Deleted custom topology tree:", name)
	return ctm.persistTrees()
}

// findNodeByID recursively finds a node by ID in the tree
func (ctm *CustomTopologyManager) findNodeByID(nodes []*CustomTopologyNode, nodeID string) (*CustomTopologyNode, []*CustomTopologyNode, int) {
	for i, node := range nodes {
		// Skip null/nil nodes
		if node == nil {
			continue
		}
		if node.ID == nodeID {
			return node, nodes, i
		}
		if node.Children != nil {
			if found, parent, index := ctm.findNodeByID(node.Children, nodeID); found != nil {
				return found, parent, index
			}
		}
	}
	return nil, nil, -1
}

// AddNode adds a new node to the custom topology tree
func (ctm *CustomTopologyManager) AddNode(treeName, parentID, nodeType, nodeName string) (*CustomTopologyNode, error) {
	ctm.mutex.Lock()
	defer ctm.mutex.Unlock()

	tree, exists := ctm.trees[treeName]
	if !exists {
		return nil, fmt.Errorf("custom topology tree '%s' not found", treeName)
	}

	now := time.Now().Format(time.RFC3339)
	newNode := &CustomTopologyNode{
		ID:        ctm.generateNodeID(),
		Name:      nodeName,
		Type:      nodeType,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// Initialize children for nodes (but not devices)
	if nodeType == "node" {
		newNode.Children = []*CustomTopologyNode{}
	}

	// Add to parent or root
	if parentID == "" {
		// Add to root level
		tree.Nodes = append(tree.Nodes, newNode)
	} else {
		// Find parent node and add as child
		parentNode, _, _ := ctm.findNodeByID(tree.Nodes, parentID)
		if parentNode == nil {
			return nil, fmt.Errorf("parent node '%s' not found", parentID)
		}
		if parentNode.Type == "device" {
			return nil, fmt.Errorf("cannot add child nodes to device nodes")
		}
		if parentNode.Children == nil {
			parentNode.Children = []*CustomTopologyNode{}
		}
		parentNode.Children = append(parentNode.Children, newNode)
		parentNode.UpdatedAt = now
	}

	tree.UpdatedAt = now
	q.Q("Added node", nodeName, "to tree", treeName)

	err := ctm.persistTrees()
	if err != nil {
		return nil, err
	}

	return newNode, nil
}

// RemoveNode removes a node from the custom topology tree
func (ctm *CustomTopologyManager) RemoveNode(treeName, nodeID string) error {
	ctm.mutex.Lock()
	defer ctm.mutex.Unlock()

	tree, exists := ctm.trees[treeName]
	if !exists {
		return fmt.Errorf("custom topology tree '%s' not found", treeName)
	}

	// Find and remove the node
	if removed := ctm.removeNodeByID(tree.Nodes, nodeID); !removed {
		return fmt.Errorf("node '%s' not found", nodeID)
	}

	tree.UpdatedAt = time.Now().Format(time.RFC3339)
	q.Q("Removed node", nodeID, "from tree", treeName)
	return ctm.persistTrees()
}

// removeNodeByID recursively removes a node by ID
func (ctm *CustomTopologyManager) removeNodeByID(nodes []*CustomTopologyNode, nodeID string) bool {
	for i, node := range nodes {
		// Skip null/nil nodes
		if node == nil {
			continue
		}
		if node.ID == nodeID {
			// Remove from slice
			copy(nodes[i:], nodes[i+1:])
			nodes[len(nodes)-1] = nil
			nodes = nodes[:len(nodes)-1]
			return true
		}
		if node.Children != nil && ctm.removeNodeByID(node.Children, nodeID) {
			return true
		}
	}
	return false
}

// UpdateNode updates a node's properties
func (ctm *CustomTopologyManager) UpdateNode(treeName, nodeID string, updates map[string]interface{}) error {
	ctm.mutex.Lock()
	defer ctm.mutex.Unlock()

	tree, exists := ctm.trees[treeName]
	if !exists {
		return fmt.Errorf("custom topology tree '%s' not found", treeName)
	}

	node, _, _ := ctm.findNodeByID(tree.Nodes, nodeID)
	if node == nil {
		return fmt.Errorf("node '%s' not found", nodeID)
	}

	// Update allowed fields
	if name, ok := updates["name"].(string); ok {
		node.Name = name
	}
	if ipAddress, ok := updates["ipAddress"].(string); ok {
		node.IPAddress = ipAddress
	}
	if macAddress, ok := updates["macAddress"].(string); ok {
		node.MacAddress = macAddress
	}
	if modelName, ok := updates["modelName"].(string); ok {
		node.ModelName = modelName
	}
	if properties, ok := updates["properties"].(map[string]interface{}); ok {
		if node.Properties == nil {
			node.Properties = make(map[string]interface{})
		}
		// Update properties - merge with existing properties
		for key, value := range properties {
			node.Properties[key] = value
		}
	}

	node.UpdatedAt = time.Now().Format(time.RFC3339)
	tree.UpdatedAt = node.UpdatedAt

	q.Q("Updated node", nodeID, "in tree", treeName)
	return ctm.persistTrees()
}

// AssignDeviceToNode assigns a real device to a topology node
func (ctm *CustomTopologyManager) AssignDeviceToNode(treeName, nodeID, deviceMac string) error {
	ctm.mutex.Lock()
	defer ctm.mutex.Unlock()

	tree, exists := ctm.trees[treeName]
	if !exists {
		return fmt.Errorf("custom topology tree '%s' not found", treeName)
	}

	node, _, _ := ctm.findNodeByID(tree.Nodes, nodeID)
	if node == nil {
		return fmt.Errorf("node '%s' not found", nodeID)
	}

	if node.Type != "node" {
		return fmt.Errorf("can only assign devices to nodes, not to other devices")
	}

	// Get device info from DevData
	QC.DevMutex.Lock()
	deviceInfo, exists := QC.DevData[deviceMac]
	QC.DevMutex.Unlock()

	if !exists {
		return fmt.Errorf("device '%s' not found in system", deviceMac)
	}

	// Create device node
	now := time.Now().Format(time.RFC3339)

	// Use hostname if available, otherwise use a friendly device name with model, otherwise fall back to MAC
	deviceName := deviceInfo.Mac
	if deviceInfo.Hostname != "" {
		deviceName = deviceInfo.Hostname
	} else if deviceInfo.ModelName != "" {
		deviceName = fmt.Sprintf("%s-%s", deviceInfo.ModelName, deviceInfo.Mac[len(deviceInfo.Mac)-8:])
	}

	deviceNode := &CustomTopologyNode{
		ID:         ctm.generateNodeID(),
		Name:       deviceName,
		Type:       "device",
		IPAddress:  deviceInfo.IPAddress,
		MacAddress: deviceInfo.Mac,
		ModelName:  deviceInfo.ModelName,
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	// Add to parent node
	if node.Children == nil {
		node.Children = []*CustomTopologyNode{}
	}
	node.Children = append(node.Children, deviceNode)
	node.UpdatedAt = now
	tree.UpdatedAt = now

	// Update device's custom topology assignment
	QC.DevMutex.Lock()
	deviceInfo.Group = fmt.Sprintf("ct_%s_%s", treeName, node.Name)
	QC.DevData[deviceMac] = deviceInfo
	QC.DevMutex.Unlock()

	q.Q("Assigned device", deviceMac, "to node", nodeID, "in tree", treeName)
	return ctm.persistTrees()
}

// RemoveDeviceFromNode removes a device assignment from a topology node
func (ctm *CustomTopologyManager) RemoveDeviceFromNode(treeName, nodeID, deviceMac string) error {
	ctm.mutex.Lock()
	defer ctm.mutex.Unlock()

	tree, exists := ctm.trees[treeName]
	if !exists {
		return fmt.Errorf("custom topology tree '%s' not found", treeName)
	}

	node, _, _ := ctm.findNodeByID(tree.Nodes, nodeID)
	if node == nil {
		return fmt.Errorf("node '%s' not found", nodeID)
	}

	// Find and remove the device node
	if node.Children == nil {
		return fmt.Errorf("device '%s' not found in node '%s'", deviceMac, nodeID)
	}

	for i, child := range node.Children {
		if child.Type == "device" && child.MacAddress == deviceMac {
			// Remove from slice
			node.Children = append(node.Children[:i], node.Children[i+1:]...)
			node.UpdatedAt = time.Now().Format(time.RFC3339)
			tree.UpdatedAt = node.UpdatedAt

			// Clear device's custom topology assignment
			QC.DevMutex.Lock()
			if deviceInfo, exists := QC.DevData[deviceMac]; exists {
				deviceInfo.Group = ""
				QC.DevData[deviceMac] = deviceInfo
			}
			QC.DevMutex.Unlock()

			q.Q("Removed device", deviceMac, "from node", nodeID, "in tree", treeName)
			return ctm.persistTrees()
		}
	}

	return fmt.Errorf("device '%s' not found in node '%s'", deviceMac, nodeID)
}

// isDeviceAssignedInTree checks if a device is already assigned somewhere in the tree
func (ctm *CustomTopologyManager) isDeviceAssignedInTree(nodes []*CustomTopologyNode, deviceMac string) bool {
	for _, node := range nodes {
		// Skip null/nil nodes
		if node == nil {
			continue
		}
		if node.Type == "device" && node.MacAddress == deviceMac {
			return true
		}
		if node.Children != nil && ctm.isDeviceAssignedInTree(node.Children, deviceMac) {
			return true
		}
	}
	return false
}

// isDeviceAssignedAnywhere checks if a device is assigned anywhere in the system
func (ctm *CustomTopologyManager) isDeviceAssignedAnywhere(deviceMac string) (bool, string) {
	ctm.mutex.RLock()
	defer ctm.mutex.RUnlock()

	return ctm.isDeviceAssignedAnywhereInternal(deviceMac)
}

// isDeviceAssignedAnywhereInternal checks if a device is assigned anywhere in the system
// NOTE: This method assumes the caller already holds the appropriate mutex lock
func (ctm *CustomTopologyManager) isDeviceAssignedAnywhereInternal(deviceMac string) (bool, string) {
	// Check all custom topology trees
	for treeName, tree := range ctm.trees {
		if ctm.isDeviceAssignedInTree(tree.Nodes, deviceMac) {
			return true, fmt.Sprintf("custom topology tree '%s'", treeName)
		}
	}

	// Check if device is assigned to traditional groups (via device.Group field)
	QC.DevMutex.Lock()
	deviceInfo, exists := QC.DevData[deviceMac]
	QC.DevMutex.Unlock()

	if exists && deviceInfo.Group != "" {
		// If the group starts with "ct_", it's a custom topology assignment
		if strings.HasPrefix(deviceInfo.Group, "ct_") {
			// Extract tree name from group format: "ct_treeName_nodeName"
			parts := strings.Split(deviceInfo.Group, "_")
			if len(parts) >= 3 {
				treeName := parts[1]
				return true, fmt.Sprintf("custom topology tree '%s'", treeName)
			}
			return true, "custom topology (unknown tree)"
		} else {
			// It's assigned to traditional groups system
			return true, fmt.Sprintf("traditional group '%s'", deviceInfo.Group)
		}
	}

	return false, ""
}

// GetAvailableDevices returns devices that can be assigned to custom topology
// Reuses the proven GM.GetUnassignedDevices() function to avoid code duplication
func (ctm *CustomTopologyManager) GetAvailableDevices() []map[string]interface{} {
	// Get all devices from the working GM.GetUnassignedDevices() function
	alldevices := QC.DevData

	// Transform the devices to the format expected by the frontend
	var devices []map[string]interface{}
	for _, device := range alldevices {
		// Create a friendly device title
		deviceTitle := device.Mac
		if device.Hostname != "" {
			deviceTitle = device.Hostname
		} else if device.ModelName != "" {
			deviceTitle = fmt.Sprintf("%s-%s", device.ModelName, device.Mac[len(device.Mac)-8:])
		}
		// skip 11-22-33-44-55-66
		if device.Mac == "11-22-33-44-55-66" || device.Mac == "11:22:33:44:55:66" {
			continue
		}
		deviceData := map[string]interface{}{
			"key":         device.Mac,
			"title":       deviceTitle,
			"description": fmt.Sprintf("IP: %s | Model: %s", device.IPAddress, device.ModelName),
			"macAddress":  device.Mac,
			"ipAddress":   device.IPAddress,
			"model":       device.ModelName,
			"modelname":   device.ModelName, // Add modelname field for frontend compatibility
			"hostname":    device.Hostname,
		}
		devices = append(devices, deviceData)
		q.Q("Available device:", deviceData)
	}

	return devices
}

// collectAssignedDevices recursively collects all device MAC addresses assigned to custom topology
func (ctm *CustomTopologyManager) collectAssignedDevices(nodes []*CustomTopologyNode, assigned map[string]bool) {
	for _, node := range nodes {
		// Skip null/nil nodes
		if node == nil {
			continue
		}
		if node.Type == "device" && node.MacAddress != "" {
			assigned[node.MacAddress] = true
		}
		if node.Children != nil {
			ctm.collectAssignedDevices(node.Children, assigned)
		}
	}
}

// ExportTree exports a custom topology tree to JSON
func (ctm *CustomTopologyManager) ExportTree(treeName string) (string, error) {
	tree, err := ctm.GetTree(treeName)
	if err != nil {
		return "", err
	}

	// Convert to JSON string (simplified)
	return fmt.Sprintf("Custom Topology Export: %s", tree.Name), nil
}

// ImportTree imports a custom topology tree from JSON
func (ctm *CustomTopologyManager) ImportTree(treeName, jsonData string) error {
	// Simplified implementation - in real use case, parse JSON and recreate tree
	return ctm.CreateTree(treeName, "Imported custom topology")
}

// persistTrees saves all custom topology trees to MNMS config
// NOTE: This method assumes the caller already holds the appropriate mutex lock
func (ctm *CustomTopologyManager) persistTrees() error {
	// Get current MNMS config
	config, err := GetMNMSConfig()
	if err != nil {
		q.Q("Failed to get MNMS config:", err)
		return err
	}

	// Update custom topology trees in config
	if config.CustomTopology == nil {
		config.CustomTopology = make(map[string]*CustomTopologyTree)
	}

	// Create a copy to avoid holding the lock during config save
	treesCopy := make(map[string]*CustomTopologyTree)
	for name, tree := range ctm.trees {
		treesCopy[name] = tree
	}

	for name, tree := range treesCopy {
		config.CustomTopology[name] = tree
	}

	// Write updated config
	err = WriteMNMSConfig(config)
	if err != nil {
		q.Q("Failed to write MNMS config:", err)
		return err
	}

	return nil
}

// LoadTreesFromConfig loads all custom topology trees from MNMS config on startup
func (ctm *CustomTopologyManager) LoadTreesFromConfig() error {
	config, err := GetMNMSConfig()
	if err != nil {
		q.Q("Failed to get MNMS config:", err)
		return err
	}

	ctm.mutex.Lock()
	defer ctm.mutex.Unlock()

	// Initialize trees if not present in config
	if config.CustomTopology == nil {
		ctm.trees = make(map[string]*CustomTopologyTree)
		return nil
	}

	// Load trees from config
	ctm.trees = make(map[string]*CustomTopologyTree)
	for name, tree := range config.CustomTopology {
		// Clean up any null entries in the tree nodes
		if tree != nil && tree.Nodes != nil {
			tree.Nodes = ctm.cleanupNullNodes(tree.Nodes)
		}
		ctm.trees[name] = tree
	}

	q.Q("Loaded", len(ctm.trees), "custom topology trees from config")
	return nil
}

// cleanupNullNodes removes null/nil entries from a node slice and recursively cleans children
func (ctm *CustomTopologyManager) cleanupNullNodes(nodes []*CustomTopologyNode) []*CustomTopologyNode {
	var cleanedNodes []*CustomTopologyNode
	for _, node := range nodes {
		if node != nil {
			// Recursively clean children if they exist
			if node.Children != nil {
				node.Children = ctm.cleanupNullNodes(node.Children)
			}
			cleanedNodes = append(cleanedNodes, node)
		}
	}
	return cleanedNodes
}

// generateConnectionID generates a unique ID for an LLDP connection
func (ctm *CustomTopologyManager) generateConnectionID() string {
	return fmt.Sprintf("conn_%d_%s", time.Now().UnixNano(), randomString(8))
}

// DiscoverCustomTopologyLLDP validates that topology data exists for devices in the custom topology tree
// NOTE: This no longer stores connections - connections are dynamically computed from QC.TopologyData
// This function now serves to validate devices and update the tree timestamp
func (ctm *CustomTopologyManager) DiscoverCustomTopologyLLDP(treeName string) error {
	// Add nil check for the manager itself
	if ctm == nil {
		return fmt.Errorf("custom topology manager is not initialized")
	}

	// Ensure trees map is initialized
	if ctm.trees == nil {
		ctm.trees = make(map[string]*CustomTopologyTree)
	}

	ctm.mutex.Lock()
	defer ctm.mutex.Unlock()

	tree, exists := ctm.trees[treeName]
	if !exists {
		return fmt.Errorf("custom topology tree '%s' not found", treeName)
	}

	// Add nil check for the tree
	if tree == nil {
		return fmt.Errorf("custom topology tree '%s' is nil", treeName)
	}

	q.Q("Validating LLDP topology data for custom topology tree:", treeName)

	// Collect all devices in the tree (handle nil nodes)
	var devices []string
	if tree.Nodes != nil {
		devices = ctm.collectDevicesInTree(tree.Nodes)
	}
	if len(devices) == 0 {
		q.Q("No devices found in custom topology tree:", treeName)
		return nil
	}

	// Validate that topology data exists
	q.Q("Checking topology data availability for", len(devices), "devices")

	QC.TopologyMutex.Lock()
	devicesWithTopology := 0
	totalLinks := 0
	for _, deviceMac := range devices {
		if topologyData, exists := QC.TopologyData[deviceMac]; exists {
			if topologyData.LinkData != nil && len(topologyData.LinkData) > 0 {
				devicesWithTopology++
				totalLinks += len(topologyData.LinkData)
			}
		}
	}
	QC.TopologyMutex.Unlock()

	q.Q("Found topology data for", devicesWithTopology, "out of", len(devices), "devices with", totalLinks, "total links")

	// Update tree timestamp to indicate discovery was performed
	tree.UpdatedAt = time.Now().Format(time.RFC3339)

	// Save updated tree (only timestamp changed)
	err := ctm.persistTrees()
	if err != nil {
		return fmt.Errorf("failed to persist custom topology trees: %w", err)
	}

	q.Q("Updated custom topology tree", treeName, "- connections will be dynamically computed from QC.TopologyData")

	// NOTE: No WebSocket message sent here
	// topology_links_updated from syslog.go already handles topology changes
	// when device states change

	return nil
}

// collectDevicesInTree recursively collects all device MAC addresses in the tree
func (ctm *CustomTopologyManager) collectDevicesInTree(nodes []*CustomTopologyNode) []string {
	var devices []string
	for _, node := range nodes {
		if node != nil && node.Type == "device" && node.MacAddress != "" {
			devices = append(devices, node.MacAddress)
		}
		if node != nil && node.Children != nil {
			childDevices := ctm.collectDevicesInTree(node.Children)
			devices = append(devices, childDevices...)
		}
	}
	return devices
}

// isDeviceInTree checks if a device MAC address exists in the tree
func (ctm *CustomTopologyManager) isDeviceInTree(nodes []*CustomTopologyNode, macAddress string) bool {
	for _, node := range nodes {
		if node == nil {
			continue
		}
		if node.Type == "device" && node.MacAddress == macAddress {
			return true
		}
		if node.Children != nil && ctm.isDeviceInTree(node.Children, macAddress) {
			return true
		}
	}
	return false
}

// GetTreeConnections dynamically computes and returns LLDP connections for a specific tree
// by reading from QC.TopologyData to ensure real-time accuracy with device online/offline status
func (ctm *CustomTopologyManager) GetTreeConnections(treeName string) ([]*CustomTopologyLLDPConnection, error) {
	ctm.mutex.RLock()
	defer ctm.mutex.RUnlock()

	tree, exists := ctm.trees[treeName]
	if !exists {
		return nil, fmt.Errorf("custom topology tree '%s' not found", treeName)
	}

	// Collect all devices in the tree
	var devices []string
	if tree.Nodes != nil {
		devices = ctm.collectDevicesInTree(tree.Nodes)
	}

	if len(devices) == 0 {
		return []*CustomTopologyLLDPConnection{}, nil
	}

	// Dynamically compute connections from QC.TopologyData
	var connections []*CustomTopologyLLDPConnection
	now := time.Now().Format(time.RFC3339)

	QC.TopologyMutex.Lock()
	defer QC.TopologyMutex.Unlock()

	// Create a set for quick device lookup
	deviceSet := make(map[string]bool)
	for _, mac := range devices {
		deviceSet[mac] = true
	}

	// Process each device's topology data
	for _, deviceMac := range devices {
		topologyData, exists := QC.TopologyData[deviceMac]
		if !exists || topologyData.LinkData == nil {
			continue
		}

		// Extract links where both source and target are in this tree
		for _, link := range topologyData.LinkData {
			// Only include connections where both devices are in the same custom topology tree
			if deviceSet[link.Target] {
				// Determine connection status based on link type
				status := "active"
				if link.LinkType == "dashed" {
					status = "inactive"
				}

				connection := &CustomTopologyLLDPConnection{
					ID:             ctm.generateConnectionID(),
					SourceMac:      link.Source,
					TargetMac:      link.Target,
					SourcePort:     link.SourcePort,
					TargetPort:     link.TargetPort,
					BlockedPort:    link.BlockedPort,
					ConnectionType: "lldp",
					Status:         status,
					LastDiscovered: now,
					TreeName:       treeName,
				}
				connections = append(connections, connection)
			}
		}
	}

	return connections, nil
}

// collectDeviceMacAddresses recursively collects all device MAC addresses from a node's children
func (ctm *CustomTopologyManager) collectDeviceMacAddresses(node *CustomTopologyNode) []string {
	var devices []string

	if node.Children == nil {
		return devices
	}

	for _, child := range node.Children {
		if child.Type == "device" && child.MacAddress != "" {
			devices = append(devices, child.MacAddress)
		}
		// Recursively check children's children
		childDevices := ctm.collectDeviceMacAddresses(child)
		devices = append(devices, childDevices...)
	}

	return devices
}
