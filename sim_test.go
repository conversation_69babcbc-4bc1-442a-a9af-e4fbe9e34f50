package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"testing"
	"time"

	"mnms/simulator"
	"mnms/simulator/net"
	atopyaml "mnms/simulator/yaml"

	"github.com/qeof/q"
)

var simulatorPath = "simulator.yaml"

var totalsimulator []*simulator.AtopGwdClient

func init() {
	QC.IsRoot = true
}

// pelase run as administrator or root
//
// createSimualtor simulator base on config.yaml
func createSimulatorFile() error {
	startup := make(chan bool)
	name, err := net.GetDefaultInterfaceName()
	if err != nil {
		return err
	}
	simulators, err := atopyaml.NewSimulatorFile(simulatorPath, name)
	if err != nil {
		q.Q(err)
		return err
	}
	go func() {

		for _, v := range simulators {
			_ = v.StartUp()
			totalsimulator = append(totalsimulator, v)
		}
		startup <- true
	}()

	q.Q("simulator number:", len(simulators))
	time.Sleep(time.Millisecond * 300)
	<-startup
	return nil
}

func createSimulator() error {
	startup := make(chan bool)
	name, err := net.GetDefaultInterfaceName()
	if err != nil {
		return err
	}
	simmap := map[string]atopyaml.Simulator{}
	simmap["group1"] = atopyaml.Simulator{Number: 5, DeviceType: "EH7506", StartPreFixIp: "************/24", MacAddress: "00-60-E9-18-01-01"}
	simmap["group2"] = atopyaml.Simulator{Number: 5, DeviceType: "EH7508", StartPreFixIp: "************/24", MacAddress: "00-60-E9-18-0A-01"}
	simulators, err := atopyaml.NewSimulator(simmap, name)
	if err != nil {
		q.Q((err))
		return err
	}
	go func() {
		for _, v := range simulators {
			_ = v.StartUp()
			totalsimulator = append(totalsimulator, v)
		}
		startup <- true
	}()

	time.Sleep(time.Millisecond * 300)
	<-startup
	q.Q("simulator number:", len(simulators))
	return nil
}

// ShutdownSimulator Shutdown simualtor
func ShutdownSimulator() {
	for _, v := range totalsimulator {
		_ = v.Shutdown()
	}
}

// TestExample  get simulator value of oid *******.*******.0
//
// simulator detail follow simulator.yaml
func TestSimFileExample(t *testing.T) {
	t.Skip()
	err := createSimulatorFile()
	if err != nil {
		t.Fatal(err)
	}
	defer ShutdownSimulator()

	cmdJson := `[{"command":"snmp get ************ *******.*******.0"}]`
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()

		HTTPMain()
	}()

	myName := "test123"
	time.Sleep(1 * time.Second)
	url := fmt.Sprintf("http://localhost:%d/api/v1/register", QC.Port)
	ci := ClientInfo{Name: myName}
	jsonBytes, err := json.Marshal(ci)
	if err != nil {
		t.Fatalf("json marshal %v", err)
	}
	resp, err := PostWithToken(url, adminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		//save close, resp should not be nil here
		resp.Body.Close()
		if resp.StatusCode != 200 {
			t.Fatalf("post StatusCode %d", resp.StatusCode)
		}
		q.Q(resp.Header)
	}

	url = fmt.Sprintf("http://localhost:%d/api/v1/commands", QC.Port)
	resp, err = http.Post(url,
		"application/json",
		bytes.NewBuffer([]byte(cmdJson)))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		q.Q(resp.Header)
		// save close resp should not be nil here
		resp.Body.Close()
	}

	url = fmt.Sprintf("http://localhost:%d/api/v1/commands?id=%s", QC.Port, myName)
	resp, err = http.Get(url)

	if err != nil {
		t.Fatalf("get %v", err)
	}
	if resp != nil {
		cmdinfo := make(map[string]CmdInfo)
		_ = json.NewDecoder(resp.Body).Decode(&cmdinfo)
		//save close
		resp.Body.Close()
		q.Q(cmdinfo)
	}

	q.Q(QC.CmdData)
	q.Q(QC.Clients)
	_ = CheckCmds()
}

// TestSimExample  get simulator value of oid *******.*******.0
func TestSimExample(t *testing.T) {
	t.Skip()
	err := createSimulator()
	if err != nil {
		t.Fatal(err)
	}
	defer ShutdownSimulator()
	cmdJson := `[{"command":"snmp get ************ *******.*******.0"}]`
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()

		HTTPMain()
	}()

	myName := "test123"

	time.Sleep(1 * time.Second)
	url := fmt.Sprintf("http://localhost:%d/api/v1/register", QC.Port)
	ci := ClientInfo{Name: myName}
	jsonBytes, err := json.Marshal(ci)
	if err != nil {
		t.Fatalf("json marshal %v", err)
	}
	resp, err := PostWithToken(url, adminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		if resp.StatusCode != 200 {
			t.Fatalf("post StatusCode %d", resp.StatusCode)
		}
		q.Q(resp.Header)
	}
	resp.Body.Close()

	url = fmt.Sprintf("http://localhost:%d/api/v1/commands", QC.Port)
	resp, err = http.Post(url,
		"application/json",
		bytes.NewBuffer([]byte(cmdJson)))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		q.Q(resp.Header)
		// save close resp should not be nil here
		resp.Body.Close()
	}

	url = fmt.Sprintf("http://localhost:%d/api/v1/commands?id=%s", QC.Port, myName)
	resp, err = http.Get(url)

	if err != nil {
		t.Fatalf("get %v", err)
	}
	if resp != nil {
		cmdinfo := make(map[string]CmdInfo)
		_ = json.NewDecoder(resp.Body).Decode(&cmdinfo)
		q.Q(cmdinfo)
		// save close resp should not be nil here
		resp.Body.Close()
	}

	q.Q(QC.CmdData)
	q.Q(QC.Clients)
	_ = CheckCmds()
}
