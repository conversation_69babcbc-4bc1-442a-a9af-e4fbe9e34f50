package main

import (
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime/debug"
	"sync"
	"time"

	"mnms"
	_ "net/http/pprof"

	"github.com/qeof/q"
)

var Version string

func printVersion() {
	if Version == "" {
		Version = "Development version"
	}
	// print out version
	fmt.Fprintf(os.Stderr, "BlackBear NIMBL Version: %s\n", Version)
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintln(os.Stderr, "Build information:")
	fmt.Fprintln(os.Stderr, info.GoVersion, info.Main, info.Settings)
}

func main() {
	var wg sync.WaitGroup
	q.O = "stderr"
	q.P = ""

	stop := func() {
		mnms.ClientExit()
		mnms.SyslogExit()
	}
	flagversion := flag.Bool("version", false, "print version")
	flag.IntVar(&mnms.QC.Port, "p", 27183, "port")
	flag.StringVar(&mnms.QC.RootURL, "r", "", "root URL")
	flag.StringVar(&mnms.QC.Name, "n", "", "name")
	flag.StringVar(&q.O, "O", "stderr", "debug log output")
	dp := flag.String("P", "", "debug log pattern string")
	debuglog := flag.Bool("debuglog", false, "enable debug log, this will override -P to .*")
	flag.BoolVar(&mnms.QC.DumpStackTrace, "ds", false, "dump stack trace when exiting with non zero code")
	flag.IntVar(&mnms.QC.CmdInterval, "ic", mnms.QC.CmdInterval, "command processing interval")
	flag.IntVar(&mnms.QC.RegisterInterval, "ir", mnms.QC.RegisterInterval, "Network service registration interval")
	flag.IntVar(&mnms.QC.GwdInterval, "ig", mnms.QC.GwdInterval, "device scan interval")
	flag.StringVar(&mnms.QC.Domain, "d", "", "domain")
	notrap := flag.Bool("notrap", false, "no snmp trap service")
	nohttp := flag.Bool("nohttp", false, "no http service")
	nosyslog := flag.Bool("nosyslog", false, "no syslog service")
	fake := flag.Bool("fake", false, "fake mode for testing")
	_ = flag.Bool("M", false, "monitor mode")
	nomqttbroker := flag.Bool("nomqbr", false, "no mqtt broker")
	flag.StringVar(&mnms.QC.MqttBrokerAddr, "mb",
		mnms.QC.MqttBrokerAddr, "mqtt broker address")
	flag.StringVar(&mnms.QC.TrapServerAddr, "ts",
		mnms.QC.TrapServerAddr, "trap server address")
	flag.StringVar(&mnms.QC.SyslogServerAddr, "ss",
		":5534", "syslog server address")
	flag.BoolVar(&mnms.QC.SyslogBakAfterFwd, "sbk", false, "backup syslog after forwarding")
	flag.StringVar(&mnms.QC.SyslogLocalPath, "so", mnms.QC.SyslogLocalPath, "local path of syslog")
	flag.UintVar(&mnms.QC.SyslogFileSize, "sf", mnms.QC.SyslogFileSize, "file size(megabytes) of syslog")
	flag.BoolVar(&mnms.QC.SyslogCompress, "sc", false, "enable compress file of backup syslog")
	flag.StringVar(&mnms.QC.RemoteSyslogServerAddr, "rs",
		mnms.QC.RemoteSyslogServerAddr, "remote syslog server address")
	// prikeyfile := flag.String("privkey", "", "private key file")
	pp := flag.Bool("pprof", false, "enable pprof analysis")
	// flag.BoolVar(&mnms.QC.AnomalySvcEnabled, "detect", false, "run anomaly detection system")
	// wiregiard interface name
	nowg := flag.Bool("nowg", false, "no nms wireguard service")
	wgname := flag.String("wgname", "nms-wg0", "wireguard vpn interface name")
	flag.StringVar(&mnms.QC.NmsServiceURL, "ns", "", "Specify the NIMBL network service URL for agent client")

	var daemon string
	flag.StringVar(&daemon, mnms.DaemonFlag, "", mnms.Usage)
	flag.Parse()

	// version
	if Version != "" {
		mnms.QC.Version = Version
	}

	service := func() {
		mnms.QC.IsRoot = false
		if *flagversion {
			printVersion()
			mnms.DoExit(0)
		}
		if *pp {
			go func() {
				q.Q(http.ListenAndServe("localhost:6060", nil))
			}()
		}

		if *debuglog {
			*dp = ".*"
		}

		if *dp == "." {
			fmt.Fprintln(os.Stderr, "error: invalid debug pattern")
			mnms.DoExit(1)
		}
		_, err := regexp.Compile(*dp)
		if err != nil {
			fmt.Fprintf(os.Stderr, "error: invalid regular expression, %v\n", err)
			mnms.DoExit(1)
		}
		q.P = *dp
		q.Q(q.O, q.P)
		ex, err := os.Executable()
		if err != nil {
			panic(err)
		}
		exPath := filepath.Dir(ex)
		q.Q(exPath)
		// enforce -M must be the first argument if present
		for i := 1; i < len(os.Args); i++ {
			if os.Args[i] == "-M" && i != 1 {
				fmt.Fprintln(os.Stderr, "error: -M must be the first argument")
				mnms.DoExit(1)
			}
		}
		if len(os.Args) > 2 && os.Args[1] == "-M" {
			q.P = ".*"
			q.Q("monitor run mode")
			t0 := time.Now().Unix()
			ix := 0
			for {
				ix++
				runarg := fmt.Sprintf("monitor: run #%d %v", ix, os.Args)
				q.Q("monitor: run", ix, os.Args)
				err = mnms.SendSyslog(mnms.LOG_NOTICE, "monitor", runarg)
				if err != nil {
					q.Q("error: syslog", err)
				}
				ec := exec.Command(ex, os.Args[2:]...)
				ec.Dir = exPath
				output, err := ec.CombinedOutput()
				t1 := time.Now().Unix()
				diff := t1 - t0
				q.Q("monitor:", string(output))
				if diff < 3 { // XXX
					q.Q("monitor: spinning, exit")
					mnms.DoExit(1)
				}
				t0 = t1
				if err != nil {
					q.Q("monitor:", err)
					errmsg := fmt.Sprintf("monitor: #%d %v",
						ix, err.Error())
					err = mnms.SendSyslog(mnms.LOG_ERR, "monitor", errmsg)
					if err != nil {
						q.Q("error: syslog", err)
					}
					continue
				}
			}
		}

		mnms.QC.AdminToken, err = mnms.GetToken("admin")
		if err != nil {
			q.Q(err)
			fmt.Fprintln(os.Stderr, "error: can't get admin token")
			mnms.DoExit(1)
		}
		if mnms.QC.RemoteSyslogServerAddr == "" {
			q.Q("warning: missing remote syslog server address")
		}

		// validate wgname is valid
		if !mnms.WgTunnelNameIsValid(*wgname) && !*nowg {
			fmt.Fprintln(os.Stderr, "error: invalid wireguard vpn interface name")
			mnms.DoExit(1)
		}
		mnms.WgInit(*wgname, (!*nowg))

		if mnms.QC.Name == "" {
			fmt.Fprintln(os.Stderr, "error: -n name is required")
			mnms.DoExit(1)
		}
		q.Q(mnms.QC.Name)
		q.Q(mnms.QC.Domain, len(mnms.QC.Domain))

		// runHttpServer
		runHttpServer := func() {
			if !*nohttp {
				wg.Add(1)
				go func() {
					defer wg.Done()
					mnms.HTTPMain()
				}()
			}
		}
		// runHttpServer done

		// CheckCmds
		runCheckCmds := func() {
			wg.Add(1)
			go func() {
				defer wg.Done()
				q.Q(mnms.QC.CmdInterval)
				for {
					time.Sleep(time.Duration(mnms.QC.CmdInterval) * time.Second) // XXX
					err := mnms.CheckCmds()
					if err != nil {
						q.Q(err)
					}
				}
			}()
		}
		// CheckCmds done

		// RegisterMain
		registerMain := func() {
			if mnms.QC.RootURL != "" {
				wg.Add(1)
				q.Q(mnms.QC.RegisterInterval)
				go func() {
					defer wg.Done()
					mnms.RegisterMain()
				}()
			}
		}

		// Nms
		runNms := func() {
			runHttpServer()

			q.Q("service running", mnms.QC.RootURL)
			if mnms.QC.RootURL == "" {
				q.Q("warning: service running with no root URL")
			}
			registerMain()
			if !*fake {
				wg.Add(1)
				go func() {
					defer wg.Done()
					mnms.GwdMain()
					q.Q("GwdMain returned")
				}()

				wg.Add(1)
				go func() {
					defer wg.Done()
					mnms.CheckNetworkServicesAlive(60)
				}()
			} else {
				err := mnms.SetupFakeClient()
				if err != nil {
					q.Q("failed to setup fake Network service", err)
					mnms.DoExit(1)
				}
			}

			if !*nosyslog {
				wg.Add(1)
				go func() {
					defer wg.Done()
					mnms.StartSyslogServer()
					q.Q("syslog server returned")
				}()
			} else {
				q.Q("skip running syslog server")
			}

			if !*notrap {
				wg.Add(1)
				go func() {
					defer wg.Done()
					mnms.StartTrapServer()
				}()
			} else {
				q.Q("skip running trap server")
			}
			if !*nomqttbroker {
				wg.Add(1)
				go func() {
					defer wg.Done()
					err := mnms.RunMqttBroker(mnms.QC.Name)
					if err != nil {
						q.Q(err)
					}
				}()
			} else {
				q.Q("skip running mqtt broker")
			}
			if mnms.QC.RootURL != "" {
				/*
					wg.Add(1)
					go func() {
						defer wg.Done()
						mnms.CheckOfflinePollingWithTimer(20)
					}()
				*/
				if (*wgname != "") && !*nowg {
					wg.Add(1)
					go func() {
						defer wg.Done()
						mnms.WgPub(20)
					}()
				}

				wg.Add(1)
				go func() {
					defer wg.Done()
					mnms.TcpProxyPub(10)
				}()
			}

			// automatically check service version
			wg.Add(1)
			go func() {
				defer wg.Done()
				err = mnms.RunAutomaticallyCheckServiceVersion()
				if err != nil {
					q.Q(err)
				}
			}()

			wg.Add(1)
			go func() {
				defer wg.Done()
				mnms.StartAgentServer()
			}()
			runCheckCmds()
		}
		// Nms done

		time.Sleep(1 * time.Second)

		// run nms service
		mnms.QC.Kind = "nms"
		runNms()

		wg.Wait()
		q.Q("exit normally")
		mnms.DoExit(0)
	}

	// enable Daemon
	s, err := mnms.NewDaemon(mnms.QC.Name, os.Args)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
	s.RegisterRunEvent(service)
	s.RegisterStopEvent(stop)
	err = s.RunMode(daemon)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
}
