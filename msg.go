package mnms

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/qeof/q"
)

func MsgCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	if strings.HasPrefix(cmd, "msg syslog send") {
		return MsgSyslogSendCmd(cmdinfo)
	}
	q.Q("unrecognized", cmd)
	cmdinfo.Status = "error: invalid command"
	return cmdinfo
}

// Usage : msg syslog send [facility] [severity] [tag] [message]
//
//	[facility]    : syslog facility
//	[severity]    : syslog severity
//	[tag]         : syslog was sent from tag what feature name
//	[message]     : would send messages

// Example :

//  msg syslog send 0 1 InsertDev "new device"

func MsgSyslogSendCmd(cmdinfo *CmdInfo) *CmdInfo {
	q.Q("sending syslog", cmdinfo)
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	if len(ws) < 7 {
		cmdinfo.Status = fmt.Sprintf("error: too few arguments, expected minimum 7 but got %d", len(ws))
		return cmdinfo
	}
	val, err := strconv.Atoi(ws[3])
	if err != nil {
		cmdinfo.Status = "error: invalid facility value"
		return cmdinfo
	}
	facility := val
	if facility < LOG_KERN ||
		facility > LOG_LOCAL7 {
		cmdinfo.Status = "error: invalid facility"
		return cmdinfo
	}
	val, err = strconv.Atoi(ws[4])
	if err != nil {
		cmdinfo.Status = "error: invalid severity value"
		return cmdinfo
	}
	severity := val
	if severity < LOG_EMERG ||
		severity > LOG_DEBUG {
		cmdinfo.Status = "error: invalid severity"
		return cmdinfo
	}
	priority := facility*8 | severity
	tag := ws[5]
	msg := strings.Join(ws[6:], " ")
	err = SendSyslog(priority, tag, msg)
	if err != nil {
		q.Q(err)
		cmdinfo.Status = "error: sending syslog"
		return cmdinfo
	}
	cmdinfo.Status = "ok"
	q.Q("ok, sent syslog", cmdinfo)
	return cmdinfo
}
