package main

import (
	"flag"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime/debug"
	"sync"
	"time"

	"mnms"

	"github.com/qeof/q"
)

var Version string

func printVersion() {
	if Version == "" {
		Version = "Development version"
	}
	// print out version
	fmt.Fprintf(os.<PERSON>derr, "BlackBear NIMBL Version: %s\n", Version)
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintln(os.Stderr, "Build information:")
	fmt.Fprintln(os.Stderr, info.GoVersion, info.Main, info.Settings)
}

func main() {
	var wg sync.WaitGroup
	q.O = "stderr"
	q.P = ""

	stop := func() {
		mnms.ClientExit()
		mnms.SyslogExit()
	}

	flagversion := flag.Bool("version", false, "print version")
	flag.IntVar(&mnms.QC.Port, "p", 27189, "port")
	flag.StringVar(&mnms.QC.RootURL, "r", "", "root URL")
	flag.StringVar(&mnms.QC.Name, "n", "", "name")
	flag.StringVar(&q.O, "O", "stderr", "debug log output")
	dp := flag.String("P", "", "debug log pattern string")
	debuglog := flag.Bool("debuglog", false, "enable debug log, this will override -P to .*")
	flag.BoolVar(&mnms.QC.DumpStackTrace, "ds", false, "dump stack trace when exiting with non zero code")
	flag.IntVar(&mnms.QC.RegisterInterval, "ir", mnms.QC.RegisterInterval, "Network service registration interval")
	flag.StringVar(&mnms.QC.SyslogServerAddr, "ss",
		":5554", "syslog server address")
	flag.StringVar(&mnms.QC.RemoteSyslogServerAddr, "rs",
		mnms.QC.RemoteSyslogServerAddr, "remote syslog server address")
	_ = flag.Bool("M", false, "monitor mode")
	nmsName := flag.String("nsn", "", "Specify the NIMBL network service name for polling")
	pollingInterval := flag.Int("pi", 10, "Specify the polling interval in seconds")

	var daemon string
	flag.StringVar(&daemon, mnms.DaemonFlag, "", mnms.Usage)
	flag.Parse()
	// version
	if Version != "" {
		mnms.QC.Version = Version
	}
	service := func() {
		if *flagversion {
			printVersion()
			mnms.DoExit(0)
		}

		if *debuglog {
			*dp = ".*"
		}

		if *dp == "." {
			fmt.Fprintln(os.Stderr, "error: invalid debug pattern")
			mnms.DoExit(1)
		}
		q.P = *dp
		q.Q(q.O, q.P)

		ex, err := os.Executable()
		if err != nil {
			panic(err)
		}
		exPath := filepath.Dir(ex)
		q.Q(exPath)
		// enforce -M must be the first argument if present
		for i := 1; i < len(os.Args); i++ {
			if os.Args[i] == "-M" && i != 1 {
				fmt.Fprintln(os.Stderr, "error: -M must be the first argument")
				mnms.DoExit(1)
			}
		}
		if len(os.Args) > 2 && os.Args[1] == "-M" {
			q.P = ".*"
			q.Q("monitor run mode")
			t0 := time.Now().Unix()
			ix := 0
			for {
				ix++
				runarg := fmt.Sprintf("monitor: run #%d %v", ix, os.Args)
				q.Q("monitor: run", ix, os.Args)
				err = mnms.SendSyslog(mnms.LOG_NOTICE, "monitor", runarg)
				if err != nil {
					q.Q("error: syslog", err)
				}
				ec := exec.Command(ex, os.Args[2:]...)
				ec.Dir = exPath
				output, err := ec.CombinedOutput()
				t1 := time.Now().Unix()
				diff := t1 - t0
				q.Q("monitor:", string(output))
				if diff < 3 { // XXX
					q.Q("monitor: spinning, exit")
					mnms.DoExit(1)
				}
				t0 = t1
				if err != nil {
					q.Q("monitor:", err)
					errmsg := fmt.Sprintf("monitor: #%d %v",
						ix, err.Error())
					err = mnms.SendSyslog(mnms.LOG_ERR, "monitor", errmsg)
					if err != nil {
						q.Q("error: syslog", err)
					}
					continue
				}
			}
		}

		if mnms.QC.Name == "" {
			fmt.Fprintln(os.Stderr, "error: -n name is required")
			mnms.DoExit(1)
		}
		if mnms.QC.RootURL == "" {
			fmt.Fprintln(os.Stderr, "error: -r root URL is required")
			mnms.DoExit(1)
		}
		if *nmsName == "" {
			fmt.Fprintln(os.Stderr, "error: -nsn nms name is required")
			mnms.DoExit(1)
		}
		if mnms.QC.RemoteSyslogServerAddr == "" {
			fmt.Fprintln(os.Stderr, "error: -rs remote syslog server address is required")
			mnms.DoExit(1)
		}
		q.Q(mnms.QC.Name)
		q.Q(mnms.QC.RootURL)
		q.Q(*nmsName)
		mnms.QC.Kind = mnms.Polling

		mnms.QC.AdminToken, err = mnms.GetToken("admin")
		if err != nil {
			q.Q(err)
			fmt.Fprintln(os.Stderr, "error: can't get admin token")
			mnms.DoExit(1)
		}

		// RegisterMain
		registerMain := func() {
			if mnms.QC.RootURL != "" {
				wg.Add(1)
				q.Q(mnms.QC.RegisterInterval)
				go func() {
					defer wg.Done()
					mnms.RegisterMain()
				}()
			}
		}
		runPolling := func() {
			wg.Add(1)
			go func() {
				defer wg.Done()
				mnms.StartSyslogServer()
				q.Q("syslog server returned")
			}()
			wg.Add(1)
			go func() {
				defer wg.Done()
				mnms.StartPolling(*nmsName, *pollingInterval)
			}()
		}
		// Check NMS name validation
		checkNsnValidation := func() {
			wg.Add(1)
			go func() {
				defer wg.Done()
				mnms.CheckNsnFromRoot(*nmsName, *pollingInterval)
			}()
		}
		err = mnms.CheckNsnFromRootOnce(*nmsName)
		if err != nil {
			q.Q(err)
			mnms.DoExit(1)
		}
		checkNsnValidation()
		registerMain()
		runPolling()
		wg.Wait()
		q.Q("exit normally")
		mnms.DoExit(0)
	}
	// enable Daemon
	s, err := mnms.NewDaemon(mnms.QC.Name, os.Args)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
	s.RegisterRunEvent(service)
	s.RegisterStopEvent(stop)
	err = s.RunMode(daemon)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
}
