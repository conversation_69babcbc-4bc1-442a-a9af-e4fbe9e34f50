import { defineConfig } from "cypress";
import { config as dotenvConfig } from "dotenv";
import { OpenAI } from "openai";
import fs from "fs";

// ✅ Load env vars (.env)
dotenvConfig();

// ✅ OpenAI client
const openai = new OpenAI({
  apiKey: process.env.CYPRESS_OPENAI_API_KEY,
});

export default defineConfig({
  e2e: {
    baseUrl: "http://localhost:3000",
    reporter: "mochawesome",
    reporterOptions: {
      reportDir: "cypress/reports",
      overwrite: false,
      html: false,
      json: false,
    },

    setupNodeEvents(on, config) {
      on("task", {
        // ✅ LLM task
        async callOpenAI({ prompt }) {
          const completion = await openai.chat.completions.create({
            model: process.env.CYPRESS_OPENAI_MODEL,
            messages: [{ role: "user", content: prompt }],
          });
          return completion.choices[0].message.content;
        },

        // ✅ Dynamic file finder task
        findFile({ folder, pattern }) {
          const files = fs.readdirSync(folder);
          const regex = new RegExp(pattern);
          const match = files.find((file) => regex.test(file));
          return match || null;
        },
      });

      return config;
    },
  },
});
