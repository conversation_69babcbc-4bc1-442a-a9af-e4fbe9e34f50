let macAddress = ""; // Shared across tests

describe("ScriptPage Component", () => {
  beforeEach(() => {
    cy.visit("/login");
    cy.get('[data-testid="username"]').type("admin");
    cy.get('[data-testid="password"]').type("default");
    cy.get('[data-testid="submit"]').click();
    cy.url().should("not.include", "/login");

    cy.window().then((win) => {
      const token = win.sessionStorage.getItem("nmstoken");
      expect(token).to.exist;

      cy.request({
        method: "GET",
        url: "http://localhost:27182/api/v1/devices",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }).then((res) => {
        expect(res.status).to.eq(200);
        const devices = Object.entries(res.body);
        const validDevice = devices.find(([_, d]) => d?.ipaddress && d?.mac);
        expect(validDevice, "Expected a device with both IP and MAC address").to.exist;

        macAddress = validDevice[0]; // the MAC key
      });
    });

    cy.visit("/scripts");
  });

  it("should allow typing in the textarea", () => {
    cy.get("textarea").first().type(`beep ${macAddress}`).should("have.value", `beep ${macAddress}`);
  });

  it("should validate command syntax", () => {
    cy.get("textarea").first().type(`keep ${macAddress}`);
    cy.contains("run command").click();
    cy.contains("View Result").click();
    cy.contains("error: unknown command").should("be.visible");
  });

  it("should handle MAC address normalization", () => {
    const colonMac = macAddress.replace(/-/g, ":");
    cy.get("textarea").first().type(`beep ${colonMac}`);
    cy.contains("run command").click();
    cy.contains("View Result").click();
    cy.contains(`gwd beep ${macAddress}`).should("be.visible");
  });

  it("should allow setting command flags", () => {
    cy.get('input[placeholder="-cc"]').type("client1").should("have.value", "client1");
    cy.get('input[placeholder="-ct"]').type("tag1").should("have.value", "tag1");
    cy.get('input[placeholder="-ck"]').type("root").should("have.value", "root");

    cy.contains("clear command flags").click();
    cy.get('input[placeholder="-cc"]').should("have.value", "");
    cy.get('input[placeholder="-ct"]').should("have.value", "");
    cy.get('input[placeholder="-ck"]').should("have.value", "");
  });

  it("should apply flags to commands", () => {
    cy.get('input[placeholder="-cc"]').type("ishwarclient");
    cy.get("textarea").first().type(`beep ${macAddress}`);
    cy.contains("run command").click();
    cy.contains("View Result").click();
    cy.contains("Service Name:ishwarclient").should("be.visible");
  });

  it("should send a command and display the result", () => {
    const command = `gwd beep ${macAddress}`;
    cy.get("textarea").first().clear().type(command);
    cy.contains("run command").click();

    cy.contains(command).should("be.visible");
    cy.contains("View Result", { timeout: 10000 }).click();

    // ✅ Wait for the modal or table to be visible before checking result
    cy.contains("View Result", { timeout: 10000 }).click({ force: true });
    cy.wait(500); // let modal render
    cy.get("div.ant-modal-root:visible, div.ant-modal-wrap:visible", {
      timeout: 10000,
    }).should("exist");

    // Then continue
    cy.contains("Command:").should("be.visible");
    cy.contains("Service Name:").should("be.visible");
    cy.contains("Status:").should("be.visible");
    cy.contains("Result:").should("be.visible");
    cy.contains("ok").should("be.visible");
  });

  it("should download all results", () => {
    cy.intercept("GET", "/api/v1/commands?cmd=all").as("downloadAll");
    cy.contains("Download all results").click();
    cy.wait("@downloadAll").then((interception) => {
      expect(interception.response.statusCode).to.eq(200);
      const headers = interception.response.headers;
      if (headers["content-disposition"]) {
        expect(headers["content-disposition"]).to.contain("attachment");
      }
    });
  });

  it("should show error for device not found", () => {
    cy.get("textarea").first().type("beep 00-00-00-00-00-00");
    cy.contains("run command").click();
    cy.contains("View Result").click();

    cy.intercept("POST", "/api/v1/commands", {
      statusCode: 200,
      body: {
        command: "beep 00-00-00-00-00-00",
        status: "error: device not found",
        result: "",
      },
    }).as("beepCommand");
    cy.contains("error: device not found").should("be.visible");
  });

  it("should handle firmware update command", () => {
    const firmwareUrl = "https://www.atoponline.com/wp-content/uploads/2017/11/EHG7504_EHG7508_EHG7512_EHG7516_EHG7520_EHG9508_EHG9512_EMG8508_EMG8510_RHG7528_RHG9528-K800A800.zip";
    cy.get("textarea").first().type(`gwd firmware update ${macAddress} ${firmwareUrl}`);
    cy.contains("run command").click();
    cy.contains("View Result").click();
  });

  it("should handle device management commands", () => {
    cy.get("textarea").first().type("device delete all");
    cy.contains("run command").click();
    cy.contains("View Result").click();
  });
});
