/// <reference types="cypress" />

const extractJSON = (text) => {
  const match = text.match(/{[\s\S]*}/);
  if (match) {
    return JSON.parse(match[0]);
  }
  throw new Error(`Could not extract JSON from OpenAI response: ${text}`);
};

describe('KeyStorePage', () => {
    beforeEach(() => {
        cy.visit("/login");
        cy.get('[data-testid="username"]').type("admin");
        cy.get('[data-testid="password"]').type("default");
        cy.get('[data-testid="submit"]').click();
        cy.url().should("not.include", "/login");
        cy.visit('/key-store')
      });
 
    it('renders the KeyStorePage correctly', () => {
      cy.contains("Add Key").should('be.visible');
      cy.contains("List of Keys").should('be.visible');
    });
  
    it('shows validation errors when submitting empty form', () => {
      cy.get('#key-store-form').within(() => {
        cy.get('button[type="submit"]').click();
      });
      cy.contains('add at least 1 key value!').should('exist');
    });
  
    it('adds a key-value field and submits the form successfully', () => {
      cy.contains('Add fields').click();
  
      cy.get('input').eq(0).type('testKey');
      cy.get('input').eq(1).type('testValue');
  
      cy.get('button[type="submit"]').click();
  
      cy.contains("successfully added key value !").should('exist');
    });
 
    it('allows removing a key-value field', () => {
      cy.contains('Add fields').click();
      cy.get('input').eq(0).type('toRemove');
      cy.get('input').eq(1).type('toRemoveVal');
      cy.get('.anticon-minus-circle').click();
      cy.contains("add at least 1 key value!").should('exist');
    });
    it("adds AI-generated key-value pairs", () => {
    cy.task("callOpenAI", {
      prompt: `Give me valid JSON of a random key-value pair to test a key store form. Format: { "key": "exampleKey", "value": "exampleValue" }`,
    }).then((aiData) => {
      const { key, value } = extractJSON(aiData);

      cy.contains("Add fields").click();
      cy.get('input').eq(0).type(key);
      cy.get('input').eq(1).type(value);
      cy.get('button[type="submit"]').click();
      cy.contains("successfully added key value !").should("exist");
    });
  });


  });
  