describe("CommandModal via MainLayout", () => {
  beforeEach(() => {
    cy.visit("/login");
    cy.get('[data-testid="username"]').type("admin");
    cy.get('[data-testid="password"]').type("default");
    cy.get('[data-testid="submit"]').click();
    cy.url().should("not.include", "/login");
    cy.visit("/dashboard");
  });

  it("opens the CommandModal from FloatButton", () => {
    cy.get(".ant-float-btn")
      .should("be.visible")
      .click();

    cy.get(".ant-modal").should("be.visible");
    cy.contains("Command List").should("exist");
  });
});
