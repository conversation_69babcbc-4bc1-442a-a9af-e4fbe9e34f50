describe("ClusterInfoPage", () => {
  beforeEach(() => {
    cy.visit("/login");
    cy.get('[data-testid="username"]').type("admin");
    cy.get('[data-testid="password"]').type("default");
    cy.get('[data-testid="submit"]').click();
    cy.url().should("include", "/dashboard");
    cy.visit("/clusterinfo");
  });

  it("should load and display the cluster info table", () => {
    cy.contains("Cluster Info").should("exist");
    cy.get(".ant-pro-table")
      .first()
      .within(() => {
        cy.get("table").should("exist");
        cy.get("tbody > tr").should("have.length.greaterThan", 0);
      });
  });

  it("should display correct table headers", () => {
    const expectedHeaders = [
      "Service Name",
      "Devices",
      "Cmds",
      "Logs Received",
      "Logs Sent",
      "Start",
      "Now",
      "Go Routines",
      "IP Addresses",
    ];

    cy.get("thead tr th").then(($ths) => {
      const actualHeaders = [...$ths].map((th) => th.innerText.trim());
      const headersToCompare = actualHeaders.slice(0, expectedHeaders.length);
      expect(headersToCompare).to.deep.equal(expectedHeaders);
    });
  });

  it("should paginate table data", () => {
    cy.wait(500);
    cy.get(".ant-pagination").should("exist");
    cy.get(".ant-pagination-item").last().click({ force: true });
    cy.get("tbody tr").should("exist");
  });

  it("should export data when export button is clicked", () => {
    cy.contains("button", "Export").click();
    cy.window().then((win) => {
      const link = win.document.querySelector("a[download]");
      expect(link).to.exist;
      expect(link.download).to.include("Cluster_Info");
    });
  });
});
