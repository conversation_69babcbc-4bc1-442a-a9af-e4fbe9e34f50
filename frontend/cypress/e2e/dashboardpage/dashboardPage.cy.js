describe('Dashboard Page', () => {
    beforeEach(() => {
        cy.visit("/login");
        cy.get('[data-testid="username"]').type("admin");
        cy.get('[data-testid="password"]').type("default");
        cy.get('[data-testid="submit"]').click();
        cy.url().should("not.include", "/login");
        cy.visit('/dashboard/device');
    });
    
    it('renders SyslogTrapCount and PieChart', () => {
      cy.get('.ant-pro-statistic-card-chart').should('have.length.at.least', 2);
    });
  
    it('loads and displays alert messages', () => {
      cy.get('.ant-card-head-title').contains('Alert Message');
    });
  
    it('clears alert messages when "clear all" button is clicked', () => {
      cy.get('button').contains('clear all').click();
      cy.get('.ant-list-item').should('not.exist'); 
    });
  
    it('renders layout columns and cards correctly', () => {
      cy.get('.ant-row').should('exist');
      cy.get('.ant-col').should('have.length.at.least', 3);
      cy.get('.ant-card').should('have.length.at.least', 1);
    });
  });
  