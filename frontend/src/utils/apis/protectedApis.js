import axios from "axios";

// Create Axios instance
// const instance = axios.create({
//   baseURL: baseURL,
// });

const baseURL =
  process.env.NODE_ENV === "development"
    ? localStorage.getItem("nms-setting") === null
      ? "http://localhost:27182"
      : `${JSON.parse(localStorage.getItem("nms-setting")).state.baseURL}`
    : window.location.origin;

const instance = axios.create({
  baseURL: baseURL,
});

// Add a request interceptor to set the JWT token dynamically for each request
instance.interceptors.request.use(
  (config) => {
    const token = sessionStorage.getItem("nmstoken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default instance;
