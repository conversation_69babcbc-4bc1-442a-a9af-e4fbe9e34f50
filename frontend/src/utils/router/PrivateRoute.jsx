import { Navigate } from "react-router-dom";
import { isExpired } from "react-jwt";

export default function PrivateRoute({ children }) {
  let isValidToken = IsValidToken();
  return isValidToken ? children : <Navigate to="/login" replace={true} />;
}

export function IsValidToken(params) {
  const token = sessionStorage.getItem("nmstoken");
  let istokenExpired = isExpired(token);
  return token && !istokenExpired;
}
