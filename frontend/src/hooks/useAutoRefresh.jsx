import { useEffect, useRef, useState, useCallback } from "react";

export default function useAutoRefresh(onRefresh, defaultEnabled = false, defaultIntervalMs = 60000, storageKey = null) {
  const getSavedSettings = () => {
    if (!storageKey) return { enabled: defaultEnabled, intervalMs: defaultIntervalMs };
    
    try {
      const saved = localStorage.getItem(`autoRefresh_${storageKey}`);
      if (saved) {
        const { enabled, intervalMs } = JSON.parse(saved);
        return { 
          enabled: enabled ?? defaultEnabled, 
          intervalMs: intervalMs ?? defaultIntervalMs 
        };
      }
    } catch (error) {
      console.error("Error loading auto-refresh settings:", error);
    }
    return { enabled: defaultEnabled, intervalMs: defaultIntervalMs };
  };

  const { enabled: initialEnabled, intervalMs: initialIntervalMs } = getSavedSettings();
  const [enabled, setEnabled] = useState(initialEnabled);
  const [intervalMs, setIntervalMs] = useState(initialIntervalMs);
  const cbRef = useRef(onRefresh);

  useEffect(() => {
    cbRef.current = onRefresh;
  }, [onRefresh]);

  useEffect(() => {
    if (!enabled) return undefined;
    const id = setInterval(() => {
      if (cbRef.current) cbRef.current();
    }, intervalMs);
    return () => clearInterval(id);
  }, [enabled, intervalMs]);

  useEffect(() => {
    if (storageKey) {
      try {
        localStorage.setItem(
          `autoRefresh_${storageKey}`, 
          JSON.stringify({ enabled, intervalMs })
        );
      } catch (error) {
        console.error("Error saving auto-refresh settings:", error);
      }
    }
  }, [enabled, intervalMs, storageKey]);

  const toggle = useCallback(() => setEnabled((v) => !v), []);

  // Wrap setIntervalMs to enforce minimum value
  const setIntervalMsSafe = useCallback((ms) => {
    setIntervalMs(Math.max(5000, ms)); // Minimum 5 seconds
  }, []);

  return {
    enabled,
    toggle,
    setEnabled,
    intervalMs,
    setIntervalMs: setIntervalMsSafe,
  };
}
