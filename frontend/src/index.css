.container-login {
  height: 100vh;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  background: transparent;
}

.confirm-class .ant-modal-confirm-content {
  width: 100%;
}
#topology-graph-wrapper {
  width: 100%;
  height: 100%;
}
.cutomBadge .ant-badge-status-dot {
  position: relative;
  top: -2px !important;
  display: inline-block;
  width: 15px !important;
  height: 15px !important;
  vertical-align: middle;
  border-radius: 50%;
}

.custom-ant-pro-global-header {
  position: relative;
  background: transparent;
  display: flex;
  align-items: center;
  margin-block: 0;
  margin-inline: 16px;
  height: 56px;
  box-sizing: border-box;
}

.cutomBadge .ant-badge-color-green {
  color: #49aa19 !important;
  background-color: #49aa19 !important;
}

.error-details {
  white-space: pre-wrap;
}
.custom-colapse .ant-collapse-content-box {
  padding: 0 !important;
}
.custom-colapse .ant-collapse-header {
  align-items: center !important;
}
.custom-colapse .ant-collapse-header-text {
  flex-grow: 1 !important;
}
.row_select {
  background-color: var(--ant-table-row-selected-bg);
}

.terminal {
  height: 500px;
  overflow: scroll;
  background-color: #3c3c3c;
  color: #c4c4c4;
  padding: 24px;
  font-size: 14px;
  line-height: 1.42;
  font-family: "IBM Plex Mono", Consolas, Menlo, Monaco, "Courier New", Courier,
    monospace;
  text-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}

.terminal__line {
  line-height: 2;
  text-wrap: nowrap;
}

.content {
  display: grid;
}
.title {
  background-color: #333333;
  color: white;
}

.power-item {
  display: inline-flex;
  margin-left: calc(50% - 30px);
}
