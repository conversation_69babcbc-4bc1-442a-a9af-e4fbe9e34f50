# Hub-and-Spoke Topology Visualization

The Custom Topology page now displays a hub-and-spoke topology where the selected node appears as a central hub with all its direct children connected to it.

## 🎯 New Topology Layout

### Hub-and-Spoke Structure
- **Central Hub**: The selected node appears in the center (larger, red)
- **Spokes**: All direct children connect to the central node
- **No Inter-Child Connections**: Children only connect to the hub, not each other
- **Clear Hierarchy**: Visual representation of parent-child relationships

### Visual Distinctions

#### Central Node (Selected)
- **Color**: Red (`#ff4d4f`)
- **Size**: Larger (80x80 pixels)
- **Label**: Includes "(Selected Node)" indicator
- **Border**: Thick red border (3px)
- **Position**: Always at the center of the layout

#### Device Nodes
- **Color**: Green (`#52c41a`)
- **Size**: Standard (56x56 pixels)
- **Label**: Shows name, IP address, and MAC address
- **Border**: Green border (2px)
- **Type Indicator**: Device icon

#### Sub-Node Children
- **Color**: Blue (`#1890ff`)
- **Size**: Standard (56x56 pixels)
- **Label**: Shows name with "(Sub-node)" indicator
- **Border**: Blue border (2px)
- **Type Indicator**: Node icon

## 🔄 Layout Algorithm

### Radial Layout
- **Type**: Radial positioning around central hub
- **Algorithm**: G6's radial layout with focus node
- **Center**: Dynamic center based on container size
- **Radius**: 120px from center to children
- **Unit Radius**: 80px spacing between children
- **Overlap Prevention**: Enabled to prevent node collisions

### Connection Pattern
```
    Device A
       |
   Sub-node B ---- [SELECTED NODE] ---- Device C
       |                    |
   Device D            Sub-node E
```

## 📊 Data Structure Changes

### New Functions Added

#### `getDirectChildren(node)`
```javascript
// Returns all direct children (both devices and nodes)
const children = getDirectChildren(selectedNode);
```

#### `generateHubSpokeConnections(centralNode, children)`
```javascript
// Creates connections from central node to all children
const connections = generateHubSpokeConnections(hub, spokes);
```

### Topology Data Format
```javascript
{
  nodes: [
    {
      id: "central_node_id",
      label: "Node Name\n(Selected Node)",
      type: "image",
      style: { stroke: "#ff4d4f", lineWidth: 3 },
      size: [80, 80]
    },
    {
      id: "child_device_id",
      label: "Device Name\nIP: ***********\nMAC: 00:11:22:33:44:55",
      type: "image",
      style: { stroke: "#52c41a", lineWidth: 2 },
      size: [56, 56]
    },
    {
      id: "child_node_id",
      label: "Sub-node Name\n(Sub-node)",
      type: "image",
      style: { stroke: "#1890ff", lineWidth: 2 },
      size: [56, 56]
    }
  ],
  edges: [
    {
      id: "central_node_id-child_device_id",
      source: "central_node_id",
      target: "child_device_id",
      type: "circle-running"
    },
    {
      id: "central_node_id-child_node_id",
      source: "central_node_id",
      target: "child_node_id",
      type: "circle-running"
    }
  ]
}
```

## 🎨 User Experience

### Visual Clarity
- **Clear Hierarchy**: Easy to understand parent-child relationships
- **Central Focus**: Selected node is obviously the focal point
- **Type Distinction**: Different colors for devices vs. sub-nodes
- **Clean Layout**: No cluttered inter-connections

### Interactive Features
- **Node Selection**: Click any node in the tree to see its topology
- **Tooltips**: Hover over nodes for detailed information
- **Zoom Controls**: Zoom in/out and fit view
- **Drag and Drop**: Manually adjust node positions

### Empty States
- **No Selection**: Shows "Select a node from the tree" message
- **No Children**: Shows "No devices found in the selected node" message
- **Device Selection**: If a device is selected, only shows that device

## 🔧 Technical Implementation

### Layout Updates
- **Radial Layout**: Uses G6's radial layout algorithm
- **Focus Node**: Dynamically sets the central node as focus
- **Auto-Centering**: Automatically centers around the hub
- **Responsive**: Adapts to container size changes

### Connection Logic
```javascript
// Generate connections from hub to all spokes
children.forEach((child) => {
  connections.push({
    source: centralNode.id,
    target: child.id,
    // ... connection properties
  });
});
```

### Redux State Management
- **selectNode**: Updated to generate hub-and-spoke topology
- **getDirectChildren**: New function for getting immediate children
- **generateHubSpokeConnections**: New connection generation logic

## 📋 Usage Examples

### Example 1: Node with Mixed Children
**Selected**: "Office Network"
**Children**: 2 devices + 1 sub-node
**Result**: Hub-and-spoke with Office Network at center, 3 spokes

### Example 2: Node with Only Devices
**Selected**: "Server Room"
**Children**: 5 devices
**Result**: Hub-and-spoke with Server Room at center, 5 device spokes

### Example 3: Node with Only Sub-nodes
**Selected**: "Building A"
**Children**: 3 sub-nodes
**Result**: Hub-and-spoke with Building A at center, 3 sub-node spokes

### Example 4: Device Selected
**Selected**: "Router-01" (device)
**Children**: None (devices have no children)
**Result**: Single node showing just the device

## 🚀 Benefits

### Clear Structure Visualization
- **Immediate Understanding**: Parent-child relationships are obvious
- **Hierarchy Display**: Clear visual hierarchy representation
- **Focus Management**: Always clear what the focal node is

### Better Navigation
- **Logical Flow**: Navigate through hierarchy level by level
- **Context Awareness**: Always know which node you're viewing
- **Breadcrumb-like**: Visual breadcrumb through node selection

### Improved User Experience
- **Less Clutter**: No confusing inter-device connections
- **Consistent Layout**: Predictable hub-and-spoke pattern
- **Visual Hierarchy**: Clear distinction between hub and spokes

This hub-and-spoke topology provides a much clearer and more intuitive way to visualize the hierarchical relationships in your network topology, making it easier to understand the structure and navigate through different levels of the hierarchy.