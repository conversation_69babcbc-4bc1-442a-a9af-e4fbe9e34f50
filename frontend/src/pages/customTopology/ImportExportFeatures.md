# Import/Export Features for Custom Topology

The Custom Topology page now includes comprehensive import/export functionality to save, share, and restore tree structures.

## Features Added

### 🔄 Import/Export Modal
- **Access**: Click the Import/Export button (📥) in the tree header
- **Two-tab interface**: Export and Import tabs
- **Modal-based UI**: Clean, user-friendly interface

### 📤 Export Functionality

#### 1. Download as JSO<PERSON> File
- **Action**: Click "Download as JSON File"
- **Result**: Downloads a `.json` file with timestamp in filename
- **Format**: `topology-tree-YYYY-MM-DD.json`
- **Contents**: Tree structure with metadata (timestamp, version)

#### 2. Copy to Clipboard
- **Action**: Click "Copy to Clipboard"
- **Result**: JSON data copied to system clipboard
- **Use case**: Quick sharing or backup

#### Export Data Format
```json
{
  "treeData": [...], // Complete tree structure
  "timestamp": "2023-09-23T10:00:00.000Z",
  "version": "1.0"
}
```

### 📥 Import Functionality

#### 1. Upload JSON File
- **Action**: Click "Upload JSON File"
- **Accepts**: `.json` files only
- **Validation**: Checks data structure and format
- **Result**: Loads tree structure from file

#### 2. Paste from Clipboard
- **Action**: Click "Paste from Clipboard"
- **Source**: System clipboard content
- **Validation**: Automatic JSON parsing and validation
- **Result**: Populates text area for review before import

#### 3. Manual JSON Input
- **Action**: Paste JSON directly into text area
- **Features**: 8-row text area for editing
- **Validation**: Real-time validation on import
- **Result**: Manual control over import data

### 🔐 Data Validation

#### Import Validation
- **JSON Format**: Must be valid JSON
- **Structure**: Must contain `treeData` array
- **Content**: Validates node structure and relationships
- **Error Handling**: Clear error messages for invalid data

#### Safety Features
- **Warning**: Import replaces current tree structure
- **Recommendation**: Export current data before importing
- **Validation**: Prevents invalid or corrupted data import

### 💾 Persistence

#### localStorage Integration
- **Auto-save**: Imported data automatically saved to localStorage
- **Persistence**: Data survives browser refreshes and sessions
- **Consistency**: Same storage mechanism as manual tree operations

#### State Management
- **Redux Integration**: Uses existing customTopology slice
- **State Reset**: Clears selection and topology view on import
- **Clean State**: Ensures consistent application state

## Technical Implementation

### Redux Actions
```javascript
// Import tree data
dispatch(restoreTreeData(jsonString))

// Helper function for export
const jsonData = exportTreeData(treeData)
```

### File Structure
```
components/customTopology/
├── ImportExportModal.jsx     // Main modal component
├── NodeTreeView.jsx          // Updated with import/export button
└── ...

features/customTopology/
├── customTopologySlice.js    // Updated with import/export actions
└── ...
```

### Key Functions

#### Export Helper
```javascript
export const exportTreeData = (treeData) => {
  const dataToStore = {
    treeData: treeData,
    timestamp: new Date().toISOString(),
    version: "1.0"
  };
  return JSON.stringify(dataToStore, null, 2);
};
```

#### Redux Actions
- `restoreTreeData(jsonString)` - Import and validate tree data
- `loadTreeData()` - Load from localStorage
- Helper functions for data transformation

## Usage Scenarios

### 🔄 Backup & Restore
1. **Regular Backups**: Export tree structures periodically
2. **Disaster Recovery**: Restore from backup files
3. **Version Control**: Maintain different versions of topologies

### 🤝 Sharing & Collaboration
1. **Team Sharing**: Export and share topology configurations
2. **Templates**: Create and distribute standard topologies
3. **Documentation**: Include topology files in project documentation

### 🔀 Migration & Testing
1. **Environment Migration**: Move topologies between environments
2. **Testing**: Create test topologies from production data
3. **Experimentation**: Save current state before making changes

## User Interface

### Export Tab
- **File Download**: Direct download with automatic naming
- **Clipboard Copy**: One-click clipboard operation
- **Summary Info**: Shows current tree statistics

### Import Tab
- **Multiple Sources**: File upload, clipboard, manual input
- **Visual Feedback**: Clear success/error messages
- **Safety Warning**: Prominent warning about data replacement

### Integration
- **Seamless**: Integrated into existing tree management interface
- **Consistent**: Follows application UI patterns
- **Accessible**: Clear icons and tooltips

The import/export functionality provides a complete data management solution for the custom topology feature, enabling users to backup, share, and restore their network topology configurations with ease.