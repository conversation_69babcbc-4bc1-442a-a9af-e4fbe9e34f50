import React from "react";
import { Row, Col, Card } from "antd";
import { ApartmentOutlined } from "@ant-design/icons";
import NodeTreeView from "../../components/customTopology/NodeTreeView";
import TopologyView from "../../components/customTopology/TopologyView";

const CustomTopologyPage = () => {
  return (
    <div style={{ padding: 0 }}>
      <Row gutter={16} style={{ height: "100%" }}>
        {/* Left Panel - Tree View */}
        <Col span={6}>
          <Card
            title={
              <span>
                <ApartmentOutlined style={{ marginRight: 8 }} />
                Node Management
              </span>
            }
            styles={{
              body: {
                height: "calc(100vh - 145px)",
                overflow: "auto",
                padding: 10,
              },
            }}
          >
            <NodeTreeView />
          </Card>
        </Col>

        {/* Right Panel - Topology View */}
        <Col span={18}>
          <div style={{ height: "100%" }}>
            <TopologyView />
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default CustomTopologyPage;
