# 節點圖標縮放問題修復

解決了當topology中元件數量較少時，節點圖標被自動放大導致點陣圖變得不美觀的問題。

## 🎯 問題描述

### 原始問題：
- **自動縮放**：G6在元件數量少時會自動放大節點
- **點陣圖失真**：使用Canvas生成的點陣圖在放大時變得模糊和像素化
- **視覺效果差**：圖標在不同縮放級別下顯示不一致

### 影響範圍：
- 小型拓撲（1-3個節點）顯示效果不佳
- 節點圖標在放大時失去清晰度
- 整體用戶體驗受到影響

## ✅ 解決方案

### 1. 🎨 SVG圖標替換Canvas
**之前（Canvas點陣圖）：**
```javascript
const createDeviceIcon = (color) => {
  const canvas = document.createElement("canvas");
  const context = canvas.getContext("2d");
  // ... Canvas繪製代碼
  return canvas.toDataURL(); // 點陣圖輸出
};
```

**現在（SVG向量圖）：**
```javascript
const createSVGDeviceIcon = (color, type) => {
  const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
  // ... SVG元素創建
  const svgData = new XMLSerializer().serializeToString(svg);
  return `data:image/svg+xml;base64,${btoa(svgData)}`;
};
```

### 2. 📏 固定節點大小
**問題修復：**
- **固定尺寸**：設定節點為固定大小 `[60, 60]` 像素
- **防止自動縮放**：在佈局配置中設定 `nodeSize: 60`
- **一致性**：所有節點保持相同基礎大小

**配置更新：**
```javascript
const GRAPH_CONFIG = {
  DEFAULT_NODE_SIZE: [60, 60], // 固定預設大小
  RADIAL_LAYOUT: {
    radius: 150,           // 增大半徑避免擁擠
    unitRadius: 100,       // 增大單位半徑
    nodeSize: 60,          // 固定節點大小
    preventOverlap: true,  // 防止重疊
  }
};
```

### 3. 🎯 改進的視覺區分
**節點類型區分：**
- **選中節點**：紅色 (`#ff4d4f`)，稍大 `[70, 70]`，標記"N"
- **設備節點**：綠色 (`#52c41a`)，標準大小，標記"D"
- **子節點**：藍色 (`#1890ff`)，標準大小，標記"N"

## 🔧 技術實現

### SVG圖標生成
```javascript
const createSVGDeviceIcon = (color = "#1890ff", type = "device") => {
  const size = 40;
  const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");

  // 設定SVG屬性
  svg.setAttribute("viewBox", `0 0 ${size} ${size}`);

  // 創建圓形背景
  const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle");
  circle.setAttribute("fill", color);

  // 創建文字圖標
  const icon = document.createElementNS("http://www.w3.org/2000/svg", "text");
  icon.textContent = type === "device" ? "D" : "N";

  return svg轉為base64 data URL;
};
```

### 固定大小配置
```javascript
const nodes = data.nodes.map(node => {
  let nodeSize = [60, 60]; // 固定大小

  // 選中節點稍大
  if (node.label.includes("(Selected Node)")) {
    nodeSize = [70, 70];
  }

  return {
    ...node,
    img: createSVGDeviceIcon(iconColor, iconType),
    size: nodeSize, // 固定大小，不受G6自動縮放影響
  };
});
```

### 佈局優化
```javascript
// 初始化佈局
layout: {
  type: "radial",
  radius: 150,              // 更大半徑
  unitRadius: 100,          // 更大單位半徑
  nodeSize: 60,             // 固定節點大小
  preventOverlap: true,     // 防止重疊
}

// 動態更新佈局
graph.updateLayout({
  radius: 150,
  unitRadius: 100,
  nodeSize: 60,            // 關鍵：固定大小防止自動縮放
  preventOverlap: true,
});
```

## 🎨 視覺改善

### 縮放一致性
- **所有元件數量**：1個或10個節點都保持相同大小
- **清晰度**：SVG在任何縮放級別都保持清晰
- **專業外觀**：一致的視覺效果

### 佈局優化
- **更大間距**：增大半徑避免節點擁擠
- **防止重疊**：確保節點不會重疊
- **中心對齊**：始終以選中節點為中心

### 類型識別
- **顏色編碼**：不同類型使用不同顏色
- **圖標標記**：D代表設備，N代表節點
- **大小區分**：選中節點稍大以突出顯示

## 📊 對比效果

### 修復前：
- ❌ 少量節點時圖標被過度放大
- ❌ 點陣圖在放大時模糊
- ❌ 不同元件數量下顯示不一致
- ❌ 視覺效果不專業

### 修復後：
- ✅ 所有情況下節點大小一致
- ✅ SVG圖標在任何大小都清晰
- ✅ 固定大小配置防止自動縮放
- ✅ 專業和美觀的視覺效果

## 🚀 使用體驗

### 改善項目：
1. **視覺一致性**：無論元件數量多少都保持相同外觀
2. **清晰度**：SVG確保在所有縮放級別都清晰
3. **專業外觀**：乾淨、一致的圖標設計
4. **響應式**：適應不同容器大小而不失真

### 測試場景：
- **單節點**：選中一個只有1個子元件的節點
- **多節點**：選中有5-10個子元件的節點
- **混合類型**：包含設備和子節點的組合
- **縮放操作**：使用放大/縮小功能測試

這個修復確保了topology視圖在所有情況下都能提供清晰、專業和一致的視覺體驗，無論元件數量多少都不會出現圖標失真的問題。