# Custom Topology Page

A new topology management page that provides an interactive tree view for managing nodes and devices, with real-time topology visualization.

## Features

### Left Panel - Node Management Tree
- **Interactive Tree View**: Hierarchical display of nodes and devices
- **CRUD Operations**:
  - Add nodes and devices at any level
  - Delete nodes and devices with confirmation
  - Rename nodes and devices inline
  - Edit device properties (IP address, MAC address)
- **Context Menus**: Right-click operations for all actions
- **No Root Node Restriction**: Multiple top-level nodes allowed
- **Persistent Storage**: Tree structure saved to localStorage

### Right Panel - Topology Visualization
- **G6 Graph Visualization**: Interactive network topology display
- **Real-time Updates**: Topology updates when tree selection changes
- **Device Nodes**: Visual representation of devices with tooltips
- **Animated Connections**: Flowing animations on connections
- **Graph Controls**: Zoom in/out, fit view, download
- **Responsive Layout**: Adapts to container size changes

## Component Architecture

```
CustomTopologyPage (Main container)
├── NodeTreeView (Left panel)
│   ├── Tree component with context menus
│   ├── Add/Edit/Delete modals
│   └── Device property editor
└── TopologyView (Right panel)
    ├── G6 graph visualization
    ├── Control toolbar
    └── Empty state handling
```

## Redux State Management

**Slice**: `customTopologySlice.js`
- Tree data management
- Node selection state
- Topology data generation
- CRUD operations
- LocalStorage persistence

## Data Structure

```javascript
{
  id: "unique_id",
  name: "Node Name",
  type: "node" | "device",
  children: [...], // For nodes only
  ipAddress: "***********", // For devices only
  macAddress: "00:11:22:33:44:55" // For devices only
}
```

## Navigation

- **Route**: `/customtopology`
- **Menu Item**: "Custom Topology" with NodeIndexOutlined icon
- **Access**: Protected route requiring authentication

## Usage

1. **Navigate** to Custom Topology from the main menu
2. **Add Nodes**: Click "Add Node" or use context menu
3. **Add Devices**: Use context menu on nodes to add devices
4. **Edit Items**: Right-click for rename/edit options
5. **View Topology**: Select any node to see its device topology
6. **Manage Structure**: Drag, drop, and organize as needed

## Technical Details

- **React 18** with functional components and hooks
- **Ant Design** components for UI consistency
- **G6 Graph** library for topology visualization
- **Redux Toolkit** for state management
- **LocalStorage** for data persistence
- **Responsive** design with grid layout

## Files Created

1. `features/customTopology/customTopologySlice.js` - Redux state management
2. `components/customTopology/NodeTreeView.jsx` - Tree management component
3. `components/customTopology/TopologyView.jsx` - Graph visualization component
4. `pages/customTopology/CustomTopologyPage.jsx` - Main page component
5. `pages/customTopology/README.md` - This documentation

## Integration Points

- Added to Redux store in `app/store.js`
- Route added to `utils/router/AppRoutes.jsx`
- Navigation menu updated in `layout/_DefaultProps.jsx`

The implementation follows existing codebase patterns and conventions while providing a robust, user-friendly topology management interface.