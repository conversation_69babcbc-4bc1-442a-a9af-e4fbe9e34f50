import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  Button,
  Image,
  App,
  Card,
  Modal,
  Space,
  Flex,
} from "antd";
import { UserOutlined, LockOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  clearAuthData,
  clearState,
  loginUser,
  userAuthSelector,
} from "../../features/auth/userAuthSlice";
import ProtectedApis from "../../utils/apis/protectedApis";
import logo from "../../assets/images/NIMBL_Logo.svg";
import atopDarklogo from "../../assets/images/darkmode-logo.svg";
import TwoFAValidator from "../two_factor_auth/2FAValidator";
import SettingsComp from "../../components/comman/SettingsComp";
import ServerStatus from "../../components/comman/ServerStatus";
import { useThemeMode } from "antd-style";

const is2FAEnabled = false;

const LoginPage = () => {
  const { notification } = App.useApp();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [is2FAModalOpen, set2FAModalOpen] = useState(false);
  const [buttonDisable, setButtonDisable] = useState(false);
  const { isFetching, isSuccess, isError, errorMessage } =
    useSelector(userAuthSelector);
  const [form] = Form.useForm();
  const onFinish = (values) => {
    //console.log(values);
    dispatch(loginUser(values));
  };

  useEffect(() => {
    sessionStorage.removeItem("nmstoken");
    sessionStorage.removeItem("nmsuser");
    sessionStorage.removeItem("nmsuserrole");
    sessionStorage.removeItem("sessionid");
    sessionStorage.removeItem("is2faenabled");
    delete ProtectedApis.defaults.headers.common["Authorization"];
    dispatch(clearAuthData());

    return () => {
      dispatch(clearState());
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (isError) {
      notification.error({ message: errorMessage });
      form.resetFields();
      dispatch(clearState());
      setButtonDisable(true);
      setTimeout(() => {
        setButtonDisable(false);
      }, 10000);
    }

    if (isSuccess) {
      dispatch(clearState());
      form.resetFields();
      if (sessionStorage.getItem("sessionid") !== null) {
        sessionStorage.setItem("is2faenabled", true);
        set2FAModalOpen(true);
      } else {
        sessionStorage.setItem("is2faenabled", false);
        navigate("/dashboard");
      }
    }
  }, [isError, isSuccess]); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <div className="container-login">
      <div
        style={{
          position: "fixed",
          right: "20px",
          top: "20px",
        }}
      >
        <Space>
          {process.env.NODE_ENV === "development" && <SettingsComp />}
        </Space>
      </div>
      <Card varient="borderless">
        <LoginForm
          onFinish={onFinish}
          form={form}
          loading={isFetching}
          is2FAEnabled={is2FAEnabled}
          is2FAModalOpen={is2FAModalOpen}
          set2FAModalOpen={set2FAModalOpen}
          buttonDisable={buttonDisable}
        />
      </Card>
    </div>
  );
};

export default LoginPage;

const LoginForm = (props) => {
  const { appearance } = useThemeMode();
  return (
    <Flex vertical align="center" gap={15}>
      <Image
        height={56}
        alt="NIMBL"
        src={appearance === "dark" ? atopDarklogo : logo}
        preview={false}
      />
      <ServerStatus isDashboardPage={false} />
      <Form
        name="normal_login"
        size="large"
        autoComplete="off"
        onFinish={props.onFinish}
        form={props.form}
      >
        <Form.Item
          name="user"
          data-testid="username"
          rules={[
            {
              required: true,
              message: "Please input your username !",
            },
          ]}
        >
          <Input prefix={<UserOutlined />} placeholder="Username" />
        </Form.Item>
        <Form.Item
          name="password"
          data-testid="password"
          rules={[
            {
              required: true,
              message: "Please input your Password!",
            },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            type="password"
            placeholder="Password"
          />
        </Form.Item>
        <Form.Item data-testid="submit">
          <Button
            type="primary"
            htmlType="submit"
            block
            loading={props.loading}
            disabled={props.buttonDisable}
          >
            Sign in
          </Button>
        </Form.Item>
      </Form>

      {/**Start: 2FA Validator Modal */}
      <Modal
        width={400}
        title=""
        open={props.is2FAModalOpen}
        centered
        onOk={() => {
          props.set2FAModalOpen(false);
        }}
        onCancel={() => {
          props.set2FAModalOpen(false);
        }}
        footer={[]}
      >
        <TwoFAValidator />
      </Modal>
      {/**End: QR Code Modal */}
    </Flex>
  );
};
