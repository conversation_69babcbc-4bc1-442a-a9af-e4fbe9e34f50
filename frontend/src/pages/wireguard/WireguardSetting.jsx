import React, { useEffect, useState } from "react";
import { But<PERSON>, Card, Spin, Space } from "antd";
import {
  clusterInfoSelector,
  RequestClusterInfo,
  RootClusterInfo,
} from "../../features/clusterInfo/clusterInfoSlice";

import { useSelector, useDispatch } from "react-redux";

const ListIP = (props) => {
  const { ip_addresses } = props;
  return (
    <>
      {ip_addresses.map((ip, index) => (
        <p key={`ip_${index}`}>{ip}</p>
      ))}
    </>
  );
};

const WireguardSetting = () => {
  const { clusterInfoData, fetching, rootInfoData, rootinfofetching } =
    useSelector(clusterInfoSelector);
  const [showStep, setShowStep] = useState(false);
  const [wgConfguration, setWgConfguration] = useState({});
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(RootClusterInfo());
    dispatch(RequestClusterInfo());
  }, []);

  const wgConfgurationEle = () => {
    return (
      <div>
        <p> Create a configuration file with name wgclient.json </p>
        {wgConfguration.error ? (
          <p>{wgConfguration.error}</p>
        ) : (
          <pre>{wgConfguration}</pre>
        )}
        <p>
          In the same folder run command following:
          <pre>
            <code>wgclient -config wgclient.json start</code>
          </pre>
        </p>
      </div>
    );
  };

  const handleCreateTunnel = (root_url, root_name, client_name) => {
    const updatedRootUrl = root_url ? root_url : "localhost:27182";
    const name = Math.random().toString(36).substring(7);
    // generate rand between 10-100
    var randNum = Math.floor(Math.random() * 100) + 1;
    const interfaceName = `wg-${randNum}`;
    const updatedInfo = {
      root_url: updatedRootUrl,
      interface: interfaceName,
      root_name,
      client_name,
      name,
    };

    setShowStep(true);

    if (!root_name || !client_name) {
      setWgConfguration({ error: "Missing root_name or client_name" });
      return;
    }
    setWgConfguration(JSON.stringify(updatedInfo, null, 2));
    return;
  };

  console.log("root : ", rootInfoData);
  console.log("clients : ", clusterInfoData);
  return (
    <div>
      <h1>Wireguard Setting</h1>
      {fetching ? (
        <Space>
          <Spin /> <p> Fetching clients' information</p>
        </Space>
      ) : (
        <Space>
          {clusterInfoData.map((cluster) => (
            <Card
              title={cluster.name}
              style={{ width: 300 }}
              key={cluster.name}
            >
              <ListIP ip_addresses={cluster.ip_addresses} />
              <Button
                type="primary"
                onClick={() =>
                  handleCreateTunnel(
                    rootInfoData.root_url,
                    rootInfoData.name,
                    cluster.name
                  )
                }
              >
                Create tunnel to Client
              </Button>
            </Card>
          ))}
        </Space>
      )}
      {showStep ? wgConfgurationEle() : null}
    </div>
  );
};

export default WireguardSetting;
