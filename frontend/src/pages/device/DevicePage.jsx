import React, { useState, useCallback, useMemo, useEffect } from "react";
import { useThemeStore } from "../../utils/themes/useStore";
import {
  useGetInventriesQuery,
  useSendCommandMutation,
} from "../../app/services/commandApi";
import { useGetFlattenedCustomTopologyNodesQuery } from "../../app/services/customTopologyApi";
import { useDispatch, useSelector } from "react-redux";
import {
  inventorySliceSelector,
  setSelectedRowKeys,
  setInventoryType,
  setScanTimerInterval,
  setScanTimeoutId,
  setSearchFilter,
  clearSelectedRowKeys,
  resetScanningStates,
  startScan,
  updateScanTimer,
} from "../../features/inventory/inventorySlice";
import { ProTable } from "@ant-design/pro-components";
import { deviceColumns } from "../../components/table-columns/device-table";
import {
  App,
  Button,
  Input,
  List,
  Segmented,
  Space,
  Table,
  Tooltip,
  Typography,
  Badge,
  Tag,
  Card,
  Row,
  Col,
  Divider,
  Flex,
} from "antd";
import DeviceContextMenu from "../../components/context-menu/device-menu";
import { useContextMenu } from "react-contexify";
import { mapDeviceDataForExportOnOff } from "../../utils/comman/dataMapping";
import ExportData from "../../components/exportData/ExportData";
import Icon, {
  CloseCircleFilled,
  CloseOutlined,
  ExclamationCircleFilled,
  PlusOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { RequestLocateDevice } from "../../features/singleDeviceConfigurations/locateDeviceSlice";
import { RequestRebootDevice } from "../../features/singleDeviceConfigurations/rebootDeviceSlice";
import { RequestEnableSNMP } from "../../features/singleDeviceConfigurations/enableSNMPDeciceSlice";
import FwUpdateModel from "../../components/device/FwUpdateModel";
import TrapSettingModel from "../../components/device/TrapSettingModel";
import SyslogSettimgModel from "../../components/device/SyslogSettimgModel";
import PortInfoModel from "../../components/device/PortInfoModel";
import { RequestSaveRunningConfig } from "../../features/singleDeviceConfigurations/saveRunningConfigSlice";
import { openNetworkSettingDrawer } from "../../features/singleDeviceConfigurations/singleNetworkSetting";
import { openSyslogSettingDrawer } from "../../features/singleDeviceConfigurations/singleSyslogSetting";
import { openTrapSettingDrawer } from "../../features/singleDeviceConfigurations/singleTrapSetting";
import { openFwUpdateDrawer } from "../../features/singleDeviceConfigurations/updateFirmwareDeviceSlice";
import NetworkSettingModel from "../../components/mdr/NetworkSettingModel";
import MdrSetLedModel from "../../components/mdr/MdrSetLedModel";
import MdrProfinetModel from "../../components/mdr/MdrProfinetModel";
import MdrConfigModel from "../../components/mdr/MdrConfigModel";
import ScanByIPRangeModel from "../../components/device/ScanByIPRangeModel";
import DeviceEditModel from "../../components/device/DeviceEditModel";
import { useTheme } from "antd-style";
import { cleanupServiceWorkers } from "../../utils/serviceWorkerUtils";

const { Title, Text } = Typography;

const DevicePage = () => {
  // All hooks must be called at the top level, in the same order every time
  const token = useTheme();
  const dispatch = useDispatch();
  const {
    inventoryType,
    changeInventoryType,
    setSelectedSubnetNode,
    selectedSubnetNode,
  } = useThemeStore();
  const { modal, notification } = App.useApp();
  const { show, hideAll } = useContextMenu();
  const {
    selectedRowKeys,
    isScanning,
    scanTimeRemaining,
    scanTimerInterval,
    scanTimeoutId,
    pollingInventory,
    scanStartTime,
    scanTotalDuration,
  } = useSelector(inventorySliceSelector);

  // API hooks - always called in the same order
  const {
    data = [],
    isFetching,
    refetch,
  } = useGetInventriesQuery(
    {
      inventoryType,
      // Remove groupid parameter as we'll filter devices client-side based on topology nodes
    },
    {
      refetchOnMountOrArgChange: true,
      pollingInterval: pollingInventory,
    }
  );

  const { data: flattenedTopologyNodes = [], refetch: refetchTopologyNodes } =
    useGetFlattenedCustomTopologyNodesQuery(
      {},
      {
        refetchOnMountOrArgChange: true,
        pollingInterval: pollingInventory,
      }
    );

  const [sendCommand] = useSendCommandMutation();
  const [sendFwCommand, { isLoading: massUpdateLoading }] =
    useSendCommandMutation();
  const [sendCommandConfig, { isLoading: configSetLoading }] =
    useSendCommandMutation();

  // State hooks - always called in the same order
  const [contextRecord, setContextRecord] = useState({});
  const [inputSearch, setInputSearch] = useState("");
  const [fwModelOpen, setFwModelOpen] = useState(false);
  const [trapModelOpen, setTrapModelOpen] = useState(false);
  const [syslogModelOpen, setSyslogModelOpen] = useState(false);
  const [openPortInfo, setOpenPortInfo] = useState(false);
  const [openNetSetModel, setOpenNetSetModel] = useState(false);
  const [openLedSetModel, setOpenLedSetModel] = useState(false);
  const [openProfSetModel, setOpenProfSetModel] = useState(false);
  const [openMdrSetModel, setOpenMdrSetModel] = useState(false);
  const [openScanbyIP, setOpenScanbyIP] = useState(false);
  const [nodeFilter, setNodeFilter] = useState(""); // Filter for device nodes
  const [hoveredCard, setHoveredCard] = useState(null); // Track hovered card

  // Process flattened topology nodes from the backend
  const topologyNodes = useMemo(() => {
    const nodes = [];

    // Add "All Devices" node - device count will be calculated from actual data
    nodes.push({
      id: "all",
      name: "All Devices",
      displayName: "All Devices",
      deviceCount: data.length, // Set from actual data
      treeName: null,
      nodeId: null,
      level: 0,
    });

    // Add flattened nodes from backend (they already include all nested nodes)
    if (Array.isArray(flattenedTopologyNodes)) {
      nodes.push(
        ...flattenedTopologyNodes.map((node) => ({
          ...node,
          // Ensure compatibility with existing UI structure
          comment: node.comment || node.description,
          nodeType: node.type || "node",
        }))
      );
    }

    return nodes;
  }, [flattenedTopologyNodes, data]);

  // Memoized filtered nodes based on search input and device assignment
  const filteredNodes = useMemo(() => {
    // First filter: only show nodes that have devices assigned (except "All Devices")
    const nodesWithDevices = topologyNodes.filter((node) => {
      // Always show "All Devices" node
      if (node.id === "all") return true;

      // Only show topology nodes that have devices assigned
      return node.devices && node.devices.length > 0;
    });

    // Second filter: apply search term if provided
    if (!nodeFilter) return nodesWithDevices;

    return nodesWithDevices.filter((node) => {
      const searchTerm = nodeFilter.toLowerCase();
      return (
        node.name.toLowerCase().includes(searchTerm) ||
        node.displayName?.toLowerCase().includes(searchTerm) ||
        node.comment?.toLowerCase().includes(searchTerm) ||
        node.treeName?.toLowerCase().includes(searchTerm) ||
        node.nodeType?.toLowerCase().includes(searchTerm) ||
        node.ipAddress?.toLowerCase().includes(searchTerm) ||
        node.macAddress?.toLowerCase().includes(searchTerm) ||
        node.modelName?.toLowerCase().includes(searchTerm)
      );
    });
  }, [topologyNodes, nodeFilter]);

  // Memoized configuration objects
  const confirmConfig = useMemo(
    () => ({
      icon: null,
      className: "confirm-class",
      width: 360,
    }),
    []
  );

  // Memoized components
  const ConfirmContent = useCallback(
    ({ icon, title, text }) => (
      <Space align="center" direction="vertical" style={{ width: "100%" }}>
        <Icon
          component={icon}
          style={{
            color: token.colorWarning,
            fontSize: 64,
          }}
        />
        <Title level={4}>{title}</Title>
        <Text strong>{text}</Text>
      </Space>
    ),
    [token.colorWarning]
  );

  // Memoized filtered data - handles both node filtering and search filtering
  const filteredData = useMemo(() => {
    let result = data;

    // Filter by selected topology node
    if (selectedSubnetNode && selectedSubnetNode !== "all") {
      const selectedNode = topologyNodes.find(
        (node) => node.id === selectedSubnetNode
      );
      if (
        selectedNode &&
        selectedNode.devices &&
        selectedNode.devices.length > 0
      ) {
        // Filter devices based on topology node's assigned devices
        result = result.filter((device) =>
          selectedNode.devices.includes(device.mac)
        );
      } else if (selectedNode && selectedNode.treeName && selectedNode.nodeId) {
        // If no devices array, filter based on any other criteria if needed
        // For now, show no devices if node has no assigned devices
        result = [];
      }
    }

    // Filter by search input
    if (inputSearch) {
      result = result.filter((row) => {
        const searchableFields = deviceColumns().map((element) => {
          return row[element.dataIndex]
            ?.toString()
            ?.toLowerCase()
            ?.includes(inputSearch?.toLowerCase());
        });
        return searchableFields.includes(true);
      });
    }

    return result;
  }, [data, inputSearch, selectedSubnetNode, topologyNodes]);

  // Event handlers with useCallback
  const handleDeleteAllDevice = useCallback(async () => {
    try {
      const command = [
        {
          command: "device delete all",
          all: true,
        },
      ];
      await sendCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
      refetch();
      refetchTopologyNodes();
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    }
  }, [sendCommand, notification, refetch]);

  const handleAddSSHTunnel = useCallback(
    async (mac) => {
      try {
        const command = [
          {
            command: `agent ssh reverse websrv ${mac}`,
          },
        ];
        await sendCommand(command).unwrap();
        notification.success({
          message: "no tunnel url, sent command to add tunnel!",
        });
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      }
    },
    [sendCommand, notification]
  );

  const handleDevicesReboot = useCallback(
    async (selectedKeys) => {
      if (selectedKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }

      try {
        const confirmed = await modal.confirm({
          title: "Devices Reboot",
          content: "Do you want to reboot for selected devices ?",
        });
        if (!confirmed) {
          return;
        }
        const command = selectedKeys.map((mac) => ({
          command: `reset ${mac}`,
        }));
        await sendCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
      }
    },
    [notification, modal, sendCommand, refetch, dispatch]
  );

  const handleDevicesEnableSnmp = useCallback(
    async (selectedKeys) => {
      if (selectedKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }

      try {
        const confirmed = await modal.confirm({
          title: "Enable SNMP",
          content: "Do you want to enable SNMP for selected devices ?",
        });
        if (!confirmed) {
          return;
        }
        const command = selectedKeys.map((mac) => ({
          command: `snmp enable ${mac}`,
        }));
        await sendCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
      }
    },
    [notification, modal, sendCommand, refetch, dispatch]
  );

  const handleDevicesDelete = useCallback(
    async (selectedKeys) => {
      if (selectedKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }

      try {
        const confirmed = await modal.confirm({
          title: "Delete Device",
          content: "Do you want to delete selected devices ?",
        });
        if (!confirmed) {
          return;
        }
        const command = selectedKeys.map((mac) => ({
          command: `device delete ${mac}`,
        }));
        await sendCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
      }
    },
    [notification, modal, sendCommand, refetch, dispatch]
  );

  const handleContextMenuClick = useCallback(
    (key, data, selectedKey) => {
      console.log(key, data, selectedKey);
      const {
        ipaddress,
        mac,
        netmask,
        gateway,
        hostname,
        isdhcp,
        modelname,
        tunneled_url,
        syslogSetting,
        trapSetting,
      } = data;

      switch (key) {
        case "openweb":
          window.open(`http://${ipaddress}`, "_blank");
          break;
        case "openwebtunnel":
          if (tunneled_url !== "") {
            window.open(`${tunneled_url}`, "_blank");
          } else handleAddSSHTunnel(mac);
          break;
        case "beep":
          modal.confirm({
            ...confirmConfig,
            content: (
              <ConfirmContent
                icon={() => <ExclamationCircleFilled />}
                title="Confirm Beep Device"
                text="This will let device beep."
              />
            ),
            onOk() {
              dispatch(RequestLocateDevice({ mac, ipaddress }));
            },
            onCancel() {
              console.log("Cancel");
            },
          });
          break;
        case "reboot":
          modal.confirm({
            ...confirmConfig,
            content: (
              <ConfirmContent
                icon={() => <CloseCircleFilled />}
                title="Confirm Reboot Device"
                text="This will let device reboot."
              />
            ),
            onOk() {
              dispatch(RequestRebootDevice({ mac, ipaddress }));
            },
            onCancel() {
              console.log("Cancel");
            },
          });
          break;
        case "enablesnmp":
          modal.confirm({
            ...confirmConfig,
            content: (
              <ConfirmContent
                icon={() => <ExclamationCircleFilled />}
                title="Device SNMP enable"
                text="This will enable device SNMP."
              />
            ),
            onOk() {
              dispatch(RequestEnableSNMP({ mac, ipaddress }));
            },
            onCancel() {
              console.log("Cancel");
            },
          });
          break;
        case "networkSetting":
          dispatch(
            openNetworkSettingDrawer({
              ipaddress,
              mac,
              netmask,
              gateway,
              hostname,
              new_ip_address: ipaddress,
              modelname,
              isdhcp,
            })
          );
          break;
        case "syslogSetting":
          dispatch(
            openSyslogSettingDrawer({
              mac,
              modelname,
              syslogSetting,
            })
          );
          break;
        case "trapSetting":
          dispatch(
            openTrapSettingDrawer({
              mac,
              modelname,
              trapSetting,
            })
          );
          break;
        case "uploadFirmware":
          dispatch(
            openFwUpdateDrawer({
              mac,
              modelname,
            })
          );
          break;
        case "saveConfig":
          modal.confirm({
            ...confirmConfig,
            content: (
              <ConfirmContent
                icon={() => <ExclamationCircleFilled />}
                title="Device save config"
                text="This will save device."
              />
            ),
            onOk() {
              dispatch(RequestSaveRunningConfig({ mac_address: mac }));
            },
            onCancel() {
              console.log("Cancel");
            },
          });
          break;
        case "massReboot":
          handleDevicesReboot(selectedKey);
          break;
        case "massEnablesnmp":
          handleDevicesEnableSnmp(selectedKey);
          break;
        case "deleteDevice":
          handleDevicesDelete(selectedKey);
          break;
        case "massUploadFirmware":
          setFwModelOpen(true);
          break;
        case "massTrapSetting":
          setTrapModelOpen(true);
          break;
        case "massSyslogSetting":
          setSyslogModelOpen(true);
          break;
        case "portInfo":
          setContextRecord(data);
          setOpenPortInfo(true);
          break;
        case "setNetwork":
          setContextRecord(data);
          setOpenNetSetModel(true);
          break;
        case "setLed":
          setContextRecord(data);
          setOpenLedSetModel(true);
          break;
        case "setMdr":
          setContextRecord(data);
          setOpenMdrSetModel(true);
          break;
        case "setProfinet":
          setContextRecord(data);
          setOpenProfSetModel(true);
          break;
        default:
          break;
      }
    },
    [
      modal,
      confirmConfig,
      ConfirmContent,
      dispatch,
      handleAddSSHTunnel,
      handleDevicesReboot,
      handleDevicesEnableSnmp,
      handleDevicesDelete,
      setFwModelOpen,
      setTrapModelOpen,
      setSyslogModelOpen,
      setContextRecord,
      setOpenPortInfo,
      setOpenNetSetModel,
      setOpenLedSetModel,
      setOpenMdrSetModel,
      setOpenProfSetModel,
    ]
  );

  const handleDevicesFwUpgrade = useCallback(
    async (value) => {
      if (selectedRowKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }
      try {
        const command = selectedRowKeys.map((mac) => ({
          command: `firmware update ${mac} ${value.fwUrl}`,
        }));
        await sendFwCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
        setFwModelOpen(false);
      }
    },
    [
      selectedRowKeys,
      notification,
      sendFwCommand,
      refetch,
      dispatch,
      setFwModelOpen,
    ]
  );

  const handleDevicesTrapSetting = useCallback(
    async (value) => {
      if (selectedRowKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }
      try {
        const command = selectedRowKeys.map((mac) => ({
          command: `snmp trap add ${mac} ${value.serverIp} ${value.serverPort} ${value.comString}`,
        }));
        await sendFwCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
        setTrapModelOpen(false);
      }
    },
    [
      selectedRowKeys,
      notification,
      sendFwCommand,
      refetch,
      dispatch,
      setTrapModelOpen,
    ]
  );

  const handleDevicesSyslogSetting = useCallback(
    async (value) => {
      if (selectedRowKeys.length <= 0) {
        notification.error({ message: "please select device!" });
        return;
      }
      try {
        const command = selectedRowKeys.map((mac) => ({
          command: `config syslog set ${mac} ${value.logToServer ? 1 : 0} ${
            value.serverIP
          } ${value.serverPort} ${value.logLevel} ${value.logToFlash ? 1 : 0}`,
        }));
        await sendFwCommand(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        refetch();
      } catch (error) {
        notification.error({ message: error.data.error || error.data });
      } finally {
        dispatch(setSelectedRowKeys([]));
        setSyslogModelOpen(false);
      }
    },
    [
      selectedRowKeys,
      notification,
      sendFwCommand,
      refetch,
      dispatch,
      setSyslogModelOpen,
    ]
  );

  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Handler for subnet node card selection (radio-style single selection)
  const handleSubnetNodeSelect = useCallback((nodeId) => {
    setSelectedSubnetNode(nodeId);
  }, []);

  // Development helper to clean up service workers if needed
  const handleCleanupServiceWorkers = useCallback(async () => {
    if (process.env.NODE_ENV === "development") {
      try {
        await cleanupServiceWorkers();
        notification.success({
          message: "Service Workers Cleaned",
          description:
            "All service workers and caches have been cleared. Consider refreshing the page.",
        });
      } catch (error) {
        notification.error({
          message: "Cleanup Failed",
          description: "Failed to clean up service workers: " + error.message,
        });
      }
    }
  }, [notification]);

  const handleSetMdrNetwork = useCallback(
    async (values) => {
      const newIp = values.isdhcp ? "0.0.0.0" : values.newipaddress;
      const dhcp = values.isdhcp ? 1 : 0;
      try {
        const command = [
          {
            command: `config network set ${values.mac} ${values.ipaddress} ${newIp} ${values.netmask} ${values.gateway} ${values.hostname} ${dhcp}`,
          },
        ];
        await sendCommandConfig(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        handleRefresh();
      } catch (error) {
        notification.error({
          message:
            error.data.error || error.data || error || "somthing went wrong",
        });
      } finally {
        setOpenNetSetModel(false);
        setContextRecord({});
      }
    },
    [
      sendCommandConfig,
      notification,
      handleRefresh,
      setOpenNetSetModel,
      setContextRecord,
    ]
  );

  const handleSetMdrLed = useCallback(
    async (values) => {
      try {
        const command = [
          {
            command: `agent motor led set ${values.mac} ${values.led}`,
          },
        ];
        await sendCommandConfig(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        handleRefresh();
      } catch (error) {
        notification.error({
          message:
            error.data.error || error.data || error || "somthing went wrong",
        });
      } finally {
        setOpenLedSetModel(false);
        setContextRecord({});
      }
    },
    [
      sendCommandConfig,
      notification,
      handleRefresh,
      setOpenLedSetModel,
      setContextRecord,
    ]
  );

  const handleSetMdrProfinet = useCallback(
    async (values) => {
      try {
        const command = [
          {
            command: `agent motor profinet set ${values.mac} ${values.profinet}`,
          },
        ];
        await sendCommandConfig(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        handleRefresh();
      } catch (error) {
        notification.error({
          message:
            error.data.error || error.data || error || "somthing went wrong",
        });
      } finally {
        setOpenProfSetModel(false);
        setContextRecord({});
      }
    },
    [
      sendCommandConfig,
      notification,
      handleRefresh,
      setOpenProfSetModel,
      setContextRecord,
    ]
  );

  const handleSetMdrConfig = useCallback(
    async (values) => {
      const { mac, zone, mode, holding, speed, direction, level, sensor } =
        values;
      try {
        const command = [
          {
            command: `agent motor mdr set ${mac} ${zone} ${mode} ${holding} ${speed} ${direction} ${level} ${sensor}`,
          },
        ];
        await sendCommandConfig(command).unwrap();
        notification.success({
          message: "successfully command sent!",
        });
        handleRefresh();
      } catch (error) {
        notification.error({
          message:
            error.data.error || error.data || error || "somthing went wrong",
        });
      } finally {
        setOpenMdrSetModel(false);
        setContextRecord({});
      }
    },
    [
      sendCommandConfig,
      notification,
      handleRefresh,
      setOpenMdrSetModel,
      setContextRecord,
    ]
  );

  const handleScanIPrange = useCallback(
    async (value) => {
      try {
        const command = [
          {
            command: `scan ${value.cidr} -save`,
          },
        ];
        await sendCommand(command).unwrap();

        // If there's already an active scan, extend/restart the timer with the new timeout
        const totalTimeMs = 60000 * value.refreshTimeout;

        // Clear existing timers if any (to restart with new duration)
        if (scanTimerInterval) {
          clearInterval(scanTimerInterval);
        }
        if (scanTimeoutId) {
          clearTimeout(scanTimeoutId);
        }

        // Start/restart the scan with new timing
        dispatch(startScan({ totalDurationMs: totalTimeMs }));

        // Set up countdown timer that will persist across page navigation
        const intervalId = setInterval(() => {
          dispatch(updateScanTimer());
        }, 1000);

        dispatch(setScanTimerInterval(intervalId));

        // Set up timeout to end the scan
        const timeoutId = setTimeout(() => {
          dispatch(resetScanningStates());
          if (intervalId) {
            clearInterval(intervalId);
          }
        }, totalTimeMs);

        // Store the timeout ID in Redux for cleanup
        dispatch(setScanTimeoutId(timeoutId));

        const scanMessage = isScanning
          ? `Additional scan request sent! Extended scanning for ${value.refreshTimeout} minutes...`
          : `Scan by IP Range (CIDR) command sent! Scanning for ${value.refreshTimeout} minutes...`;

        notification.success({
          message: scanMessage,
        });

        // Store timeout ID for cleanup
        return () => {
          clearTimeout(timeoutId);
          if (intervalId) {
            clearInterval(intervalId);
          }
        };
      } catch (error) {
        dispatch(resetScanningStates());
        notification.error({
          message: error.data.error || error.data || error,
        });
      } finally {
        setOpenScanbyIP(false);
        refetch();
      }
    },
    [sendCommand, notification, setOpenScanbyIP, refetch, isScanning]
  );

  // Initialize timer on component mount if scan is active
  useEffect(() => {
    if (isScanning && !scanTimerInterval && scanStartTime) {
      // Restore timer if scan is active but timer is not running
      const intervalId = setInterval(() => {
        dispatch(updateScanTimer());
      }, 1000);

      dispatch(setScanTimerInterval(intervalId));

      // Calculate remaining time and set timeout
      const elapsed = Date.now() - scanStartTime;
      const remaining = Math.max(0, scanTotalDuration - elapsed);

      if (remaining > 0) {
        const timeoutId = setTimeout(() => {
          dispatch(resetScanningStates());
          clearInterval(intervalId);
        }, remaining);
        dispatch(setScanTimeoutId(timeoutId));
      } else {
        // Scan should have ended, reset states
        dispatch(resetScanningStates());
        clearInterval(intervalId);
      }
    }
  }, [
    isScanning,
    scanTimerInterval,
    scanStartTime,
    scanTotalDuration,
    dispatch,
  ]);

  // Cleanup timer on component unmount (but don't reset scan states)
  useEffect(() => {
    return () => {
      if (scanTimerInterval) {
        clearInterval(scanTimerInterval);
        // Don't reset scanning states here - let them persist
        dispatch(setScanTimerInterval(null));
      }
      if (scanTimeoutId) {
        clearTimeout(scanTimeoutId);
        dispatch(setScanTimeoutId(null));
      }
    };
  }, [scanTimerInterval, scanTimeoutId, dispatch]);

  // Helper function to format time remaining
  const formatTimeRemaining = useCallback((seconds) => {
    if (seconds <= 0) return "00:00";

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  }, []);

  return (
    <>
      {/* Device Group Cards Section - Always Visible */}

      <div data-testid="device-table-div">
        <ProTable
          columns={deviceColumns(token)}
          dataSource={filteredData || []}
          rowKey="mac"
          defaultSize="small"
          loading={isFetching}
          options={{
            reload: handleRefresh,
            fullScreen: false,
            density: false,
            setting: false,
          }}
          search={false}
          toolbar={{
            search: {
              allowClear: true,
              onSearch: setInputSearch,
              onClear: () => setInputSearch(""),
            },
            actions: [
              // Timer display when scanning is active
              <>
                {isScanning && scanTimeRemaining > 0 ? (
                  <Tag
                    key="scan-timer"
                    color="processing"
                    style={{
                      fontSize: "14px",
                      padding: "4px 12px",
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                    }}
                  >
                    <span>🔄 Active Scan</span>
                    <Badge
                      count={formatTimeRemaining(scanTimeRemaining)}
                      style={{
                        backgroundColor: "#52c41a",
                        fontSize: "12px",
                        fontWeight: "bold",
                      }}
                    />
                  </Tag>
                ) : null}
              </>,
              <Tooltip key="clear-all-tooltip" title="clear all devices">
                <Button
                  icon={<CloseOutlined />}
                  onClick={handleDeleteAllDevice}
                />
              </Tooltip>,
              <Tooltip
                key="scan-ip-tooltip"
                title={
                  isScanning
                    ? "add device by ip range (extend current scan)"
                    : "add device by ip range"
                }
              >
                <Button
                  data-testid="scan-ip-btn"
                  icon={<PlusOutlined />}
                  onClick={() => setOpenScanbyIP(true)}
                />
              </Tooltip>,

              <Segmented
                key="inventory-type-segmented"
                options={["device", "mdr"]}
                value={inventoryType}
                onChange={(v) => {
                  changeInventoryType(v);
                  dispatch(setInventoryType(v));
                }}
              />,

              <ExportData
                key="export-data"
                Columns={deviceColumns(token)}
                DataSource={mapDeviceDataForExportOnOff(data)}
                title="Inventory_Device_List"
              />,
            ].filter(Boolean),
          }}
          pagination={{
            position: ["bottomCenter"],
            showQuickJumper: true,
            size: "default",
            total: filteredData.length,
            defaultPageSize: 10,
            pageSizeOptions: [10, 15, 20, 25],
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} items`,
          }}
          expandable={{
            expandedRowRender: (record) => (
              <List
                size="small"
                header={<div>Device Errors</div>}
                bordered
                dataSource={record.device_errors?.slice(0, 5) || []}
                renderItem={(item) => <List.Item>{item}</List.Item>}
              />
            ),
            rowExpandable: (record) => !!record.device_errors,
          }}
          scroll={{
            x: 1100,
          }}
          rowSelection={
            inventoryType === "mdr"
              ? undefined
              : {
                  selectedRowKeys,
                  onChange: (newSelectedRowKeys) => {
                    hideAll();
                    dispatch(setSelectedRowKeys(newSelectedRowKeys));
                  },
                  selections: [Table.SELECTION_ALL, Table.SELECTION_NONE],
                }
          }
          cardProps={{
            style: { boxShadow: token?.Card?.boxShadow },
          }}
          dateFormatter="string"
          columnsState={{
            persistenceKey: "nms-device-table",
            persistenceType: "localStorage",
          }}
          onRow={(record) => ({
            onContextMenu: async (event) => {
              if (record) {
                show({
                  id:
                    inventoryType === "device"
                      ? selectedRowKeys.length === 0
                        ? "device-menu"
                        : "mass-menu"
                      : "mdr-menu",
                  event,
                  props: {
                    record,
                    selectedRowKeys,
                  },
                });
              }
            },
          })}
        />
        <DeviceContextMenu onMenuItemClicked={handleContextMenuClick} />
        <ScanByIPRangeModel
          open={openScanbyIP}
          onOk={handleScanIPrange}
          onCancel={() => setOpenScanbyIP(false)}
        />
        <DeviceEditModel />
        <FwUpdateModel
          open={fwModelOpen}
          onCancel={() => {
            setFwModelOpen(false);
            dispatch(setSelectedRowKeys([]));
          }}
          onOk={handleDevicesFwUpgrade}
          loading={massUpdateLoading}
        />
        <TrapSettingModel
          open={trapModelOpen}
          onCancel={() => {
            setTrapModelOpen(false);
            dispatch(setSelectedRowKeys([]));
          }}
          onOk={handleDevicesTrapSetting}
          loading={massUpdateLoading}
        />
        <SyslogSettimgModel
          open={syslogModelOpen}
          onCancel={() => {
            setSyslogModelOpen(false);
            dispatch(setSelectedRowKeys([]));
          }}
          onOk={handleDevicesSyslogSetting}
          loading={massUpdateLoading}
        />
        <PortInfoModel
          open={openPortInfo}
          onCancel={() => {
            setOpenPortInfo(false);
            setContextRecord({});
          }}
          recordData={contextRecord}
        />
        <NetworkSettingModel
          record={contextRecord}
          open={openNetSetModel}
          onCancel={() => {
            setOpenNetSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrNetwork}
          loading={configSetLoading}
        />
        <MdrSetLedModel
          record={contextRecord}
          open={openLedSetModel}
          onCancel={() => {
            setOpenLedSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrLed}
          loading={configSetLoading}
        />
        <MdrProfinetModel
          record={contextRecord}
          open={openProfSetModel}
          onCancel={() => {
            setOpenProfSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrProfinet}
          loading={configSetLoading}
        />
        <MdrConfigModel
          record={contextRecord}
          open={openMdrSetModel}
          onCancel={() => {
            setOpenMdrSetModel(false);
            setContextRecord({});
          }}
          onOk={handleSetMdrConfig}
          loading={configSetLoading}
        />
      </div>
    </>
  );
};

export default DevicePage;
