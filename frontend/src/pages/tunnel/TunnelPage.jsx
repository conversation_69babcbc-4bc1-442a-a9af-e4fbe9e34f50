import { DeleteOutlined } from "@ant-design/icons";
import React from "react";
import {
  useGetTunnelInfoQuery,
  useSendCommandMutation,
} from "../../app/services/commandApi";
import { ProTable } from "@ant-design/pro-components";
import { App, theme } from "antd";

const TunnelPage = () => {
  const token = theme.useToken().token;
  const { notification } = App.useApp();
  const { data = [], isLoading, refetch } = useGetTunnelInfoQuery();
  const [sendCommand, {}] = useSendCommandMutation();

  const handleDeleteSSHTunnel = async (port) => {
    try {
      const command = [
        {
          command: `ssh tunnel close ${port}`,
          kind: "root",
        },
      ];
      await sendCommand(command).unwrap();
      notification.success({
        message: "successfully command sent!",
      });
    } catch (error) {
      const errorMessage =
        error && error.data
          ? error.data.error || error.data
          : "An unknown error occurred";
      notification.error({ message: errorMessage });
    } finally {
      refetch();
    }
  };

  const columns = [
    {
      title: "Dev Mac",
      dataIndex: "dev_mac",
      key: "dev_mac",
    },
    {
      title: "Listen Port",
      dataIndex: "listen_port",
      key: "listen_port",
    },
    {
      title: "Remote Addr",
      dataIndex: "remote_addr",
      key: "remote_addr",
    },
    {
      title: "Action",
      key: "action",
      align: "center",
      render: (_, { listen_port }) => {
        return (
          <DeleteOutlined
            style={{ color: token.colorError }}
            onClick={() => handleDeleteSSHTunnel(listen_port)}
          />
        );
      },
    },
  ];

  return (
    <>
      <ProTable
        cardProps={{ bodyStyle: { paddingInline: 5, paddingBlock: 0 } }}
        cardBordered={false}
        loading={isLoading}
        columns={columns}
        bordered
        rowKey="dev_mac"
        headerTitle="SSH Tunnel List"
        size="small"
        dataSource={data}
        pagination={{
          position: ["bottomCenter"],
          showQuickJumper: true,
          size: "default",
          total: data?.length,
          defaultPageSize: 10,
          pageSizeOptions: [5, 10, 15, 20, 25],
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
        }}
        options={{
          search: false,
          fullScreen: false,
          density: false,
          reload: () => {
            refetch();
          },
        }}
        search={false}
        dateFormatter="string"
        columnsState={{
          persistenceKey: "ssh-tunnel-table",
          persistenceType: "localStorage",
        }}
      />
    </>
  );
};

export default TunnelPage;
