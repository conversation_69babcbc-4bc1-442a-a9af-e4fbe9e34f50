import React, { useEffect, useState } from "react";
import {
  Button,
  Form,
  Select,
  Input,
  Row,
  Col,
  Space,
  InputNumber,
  App,
  Card,
  List,
  Tag,
  Pagination,
  Spin,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import {
  clearErrMibMsg,
  clearValueAndType,
  GetMibBrowserData,
  GetMibCommandResult,
  mibmgmtSelector,
  setMibIp,
  setMibMaxRepeators,
  setMibOID,
  setMibOperation,
} from "../../features/mibbrowser/MibBrowserSlice";
import AdvancedSetting from "../../components/mibbrowser/AdvancedSetting";
import SetOptionPoup from "../../components/mibbrowser/SetOptionPoup";
import ModalResult from "../../components/debug/ModalResult";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import {
  getInventoryData,
  inventorySliceSelector,
} from "../../features/inventory/inventorySlice";

const operations = [
  { value: "get", label: "Get" },
  { value: "walk", label: "Walk" },
  { value: "bulk", label: "Bulk" },
  { value: "set", label: "Set" },
];

const MibbrowserPage = () => {
  const {
    ip_address,
    oid,
    operation,
    maxRepetors,
    mibBrowserStatus,
    message,
    value,
    valueType,
    cmdResponse,
  } = useSelector(mibmgmtSelector);
  const { deviceData, scanning } = useSelector(inventorySliceSelector);
  const dispatch = useDispatch();
  const { notification, modal } = App.useApp();
  const [openPopupSettings, setOpenPopupSettings] = useState(false);
  const [openPopupSetOpt, setOpenPopupSetOpt] = useState(false);
  const [cmdResult, setCmdResult] = useState(null);
  const [isSNMPCommand, setSNMPCommand] = useState(false);
  const [totalCountResult, setTotalCount] = useState(0);
  const [resultData, setResultData] = useState("");
  const [pageSize, setPageSize] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [cValue, setCvalue] = useState("");
  const [totalPage, setTotalPage] = useState(5);
  const [totalPageError, setTotalPageError] = useState("");
  const [totalPageSelected, setTotalPageSelected] = useState(5);
  const [originaTotalCount, setOriginaTotalCount] = useState(0);

  const handleGoClick = () => {
    if (operation === "set") {
      setOpenPopupSetOpt(true);
    } else {
      const client = deviceData.filter(
        (item) => item.ipaddress === ip_address
      )[0].scannedby;
      if (client !== "") {
        dispatch(
          GetMibBrowserData([
            { command: `snmp ${operation} ${ip_address} ${oid}`, client },
          ])
        );
      } else {
        notification.error({ message: "network services not found!" });
      }
    }
  };

  const handleViewResult = (cValue) => {
    setCvalue(cValue);
    const paramObj = {
      cValue: cValue,
      pageSize: pageSize,
      pageNum: pageNum,
      totalPage: totalPageSelected,
    };
    dispatch(GetMibCommandResult(paramObj))
      .unwrap()
      .then((result) => {
        const cResult = Object.values(result);
        if (cResult.length > 0) {
          if (cResult[0].status === "" || cResult[0].status === "running") {
            setTimeout(() => {
              handleViewResult(cValue);
            }, 5000);
          }
          if (
            cResult[0].command.includes("snmp walk") ||
            cResult[0].command.includes("snmp bulk")
          ) {
            setSNMPCommand(true);

            //Extract totalCount and data from result
            const resultString = cResult[0].result;
            if (resultString) {
              const resultObj = JSON.parse(resultString);
              setOriginaTotalCount(resultObj.TotalCount);
              let paginationTotalCount = totalPage * pageSize;
              if (paginationTotalCount > resultObj.TotalCount) {
                setTotalCount(resultObj.TotalCount);
              } else {
                setTotalCount(paginationTotalCount);
              }
              setResultData(resultObj?.Data);
            }
          } else {
            setSNMPCommand(false);
          }
        }
        setCmdResult(cResult);
      })
      .catch((error) => {
        modal.error({
          title: "Mib command Result",
          content: error,
        });
      });
  };

  useEffect(() => {
    if (totalPageError === "") {
      if (cValue) {
        handleViewResult(cValue);
      }
    }
  }, [pageNum, pageSize, totalPageSelected]);

  const handleSnmpSetCancelClick = () => {
    dispatch(clearValueAndType());
    setOpenPopupSetOpt(false);
  };
  const handleSnmpSetOkClick = () => {
    const client = deviceData.filter((item) => item.ipaddress === ip_address)[0]
      .scannedby;
    if (client !== "") {
      dispatch(
        GetMibBrowserData([
          {
            command: `snmp set ${ip_address} ${oid} ${value} ${valueType}`,
            client,
          },
        ])
      );
    } else {
      notification.error({ message: "network services not found!" });
    }

    setOpenPopupSetOpt(false);
  };

  useEffect(() => {
    if (mibBrowserStatus && mibBrowserStatus === "failed") {
      notification.error({ message: message });
      dispatch(clearErrMibMsg());
    }
  }, [mibBrowserStatus]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    dispatch(getInventoryData());
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const handlePagination = (pageNum, pageSize) => {
    setPageSize(pageSize);
    setPageNum(pageNum);
  };

  const handleTotalPage = (e) => {
    setTotalPage(e.target.value);
    if (e.target.value <= 0) {
      setTotalPageError("Minimum page size 1");
    } else {
      setTotalPageError("");
    }
  };

  const handleViewBtnClick = () => {
    let totalPageNo = parseInt(totalPage);
    let calTotalCount = totalPage * pageSize;
    if (calTotalCount > originaTotalCount) {
      let calPgNo = Math.ceil(originaTotalCount / pageSize);
      setPageNum(calPgNo);
      setTotalPageSelected(calPgNo);
    } else {
      setPageNum(totalPageNo);
      setTotalPageSelected(totalPageNo);
    }
  };

  if (scanning) {
    return <Spin fullscreen tip="Loading..." size="large" />;
  }

  return (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <Row gutter={[16, 16]}>
          <Col xs={24} md={24} lg={12}>
            <Card variant="borderless" title="Mib Browser">
              <Form layout="vertical">
                <Form.Item label="IP Address">
                  <Input
                    value={ip_address}
                    onChange={(e) =>
                      dispatch(setMibIp(e.target.value.trimEnd()))
                    }
                  />
                </Form.Item>
                <Form.Item label="OID">
                  <Input
                    value={oid}
                    onChange={(e) => dispatch(setMibOID(e.target.value))}
                  />
                </Form.Item>
                <Form.Item label="Operations">
                  <Select
                    style={{ display: "block" }}
                    value={operation}
                    onChange={(value) => dispatch(setMibOperation(value))}
                    options={operations}
                  />
                </Form.Item>
                <Space>
                  {operation === "get_bulk" ? (
                    <Form.Item label="Max Repeators">
                      <InputNumber
                        value={maxRepetors}
                        onChange={(value) =>
                          dispatch(setMibMaxRepeators(value))
                        }
                      />
                    </Form.Item>
                  ) : null}
                  <Button type="primary" onClick={handleGoClick}>
                    go
                  </Button>
                </Space>
              </Form>
            </Card>
          </Col>
          <Col xs={24} md={24} lg={12}>
            <Card variant="borderless" title="Mib Browser result">
              <List
                itemLayout="horizontal"
                dataSource={cmdResponse}
                renderItem={(item) => (
                  <List.Item
                    extra={
                      <Button
                        type="primary"
                        size="small"
                        onClick={() => {
                          setResultData("");
                          setPageSize(10);
                          setTotalPage(5);
                          setTotalPageSelected(5);
                          setPageNum(1);
                          handleViewResult(item);
                        }}
                      >
                        View Result
                      </Button>
                    }
                    style={{
                      display: "flex",
                      alignItems: "flex-start",
                      wordWrap: "break-word",
                    }}
                  >
                    <List.Item.Meta
                      title={item}
                      description={
                        Array.isArray(cmdResult) &&
                        cmdResult.length !== 0 &&
                        cmdResult &&
                        cmdResult[0].command === item ? (
                          <Space direction="vertical" style={{ width: "100%" }}>
                            <ModalResult
                              title="Command:"
                              value={cmdResult[0].command}
                            />
                            <ModalResult
                              title="Service Name:"
                              value={
                                cmdResult[0].client !== ""
                                  ? cmdResult[0].client
                                  : "NO name assigned"
                              }
                            />

                            <ModalResult
                              title="Status:"
                              value={
                                cmdResult[0].status === "" ||
                                cmdResult[0].status === "running" ? (
                                  <Tag
                                    icon={<SyncOutlined spin />}
                                    color="processing"
                                  >
                                    processing
                                  </Tag>
                                ) : cmdResult[0].status === "ok" ? (
                                  <Tag
                                    icon={<CheckCircleOutlined />}
                                    color="success"
                                  >
                                    ok
                                  </Tag>
                                ) : (
                                  <Tag
                                    icon={<CloseCircleOutlined />}
                                    color="error"
                                  >
                                    {cmdResult[0].status}
                                  </Tag>
                                )
                              }
                            />
                            <ModalResult
                              title="Result:"
                              value={
                                cmdResult[0].result !== "" ? (
                                  isSNMPCommand ? (
                                    <>{resultData}</>
                                  ) : (
                                    cmdResult[0].result
                                  )
                                ) : (
                                  "NO result assigned"
                                )
                              }
                            />
                            <ModalResult
                              value={
                                resultData !== "" &&
                                isSNMPCommand && (
                                  <>
                                    <Pagination
                                      defaultCurrent={1}
                                      total={totalCountResult}
                                      onChange={handlePagination}
                                    />
                                    <div
                                      style={{
                                        display: "flex",
                                        justifyContent: "end",
                                        marginTop: "10px",
                                      }}
                                    >
                                      Total Page:
                                      <Input
                                        type="number"
                                        value={totalPage}
                                        placeholder="Page no."
                                        onChange={handleTotalPage}
                                        style={{
                                          width: "4.5rem",
                                          height: "1.6rem",
                                          marginRight: "10px",
                                          marginLeft: "5px",
                                        }}
                                      />
                                      <Button
                                        type="primary"
                                        size="small"
                                        onClick={handleViewBtnClick}
                                      >
                                        View
                                      </Button>
                                    </div>
                                    <h5
                                      style={{
                                        color: "red",
                                        display: "flex",
                                        justifyContent: "end",
                                        marginTop: "6px",
                                      }}
                                    >
                                      {totalPageError}
                                    </h5>
                                  </>
                                )
                              }
                            />
                          </Space>
                        ) : (
                          <Space direction="vertical" style={{ width: "100%" }}>
                            <ModalResult
                              title="Result:"
                              value="NO result assigned"
                            />
                          </Space>
                        )
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>
      </Col>

      <AdvancedSetting
        isAdcancedSettingOpen={openPopupSettings}
        handleCloseAdvancedSetting={() => setOpenPopupSettings(false)}
      />
      <SetOptionPoup
        isSnmpSetOpen={openPopupSetOpt}
        handleSnmpSetOkClick={handleSnmpSetOkClick}
        handleSnmpSetCancelClick={handleSnmpSetCancelClick}
      />
    </Row>
  );
};

export default MibbrowserPage;
