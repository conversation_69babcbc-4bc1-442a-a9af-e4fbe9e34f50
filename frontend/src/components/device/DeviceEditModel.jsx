import { Alert, App, Form, Input, Modal } from "antd";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  clearDeviceDataToEdit,
  inventorySliceSelector,
} from "../../features/inventory/inventorySlice";
import { useSendCommandMutation } from "../../app/services/commandApi";

const ipPattern =
  /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
const spaceCheck = /^\S*$/;

const DeviceEditModel = () => {
  const [form] = Form.useForm();
  const { notification } = App.useApp();
  const dispatch = useDispatch();
  const { deviceDataToEdit, openDeviceEditModel } = useSelector(
    inventorySliceSelector
  );

  const [sendCommand, { isLoading }] = useSendCommandMutation();

  React.useEffect(() => {
    if (deviceDataToEdit) {
      form.setFieldsValue({
        modelname: deviceDataToEdit.modelname,
        netmask: deviceDataToEdit.netmask,
        gateway: deviceDataToEdit.gateway,
        hostname: deviceDataToEdit.hostname,
        kernel: deviceDataToEdit.kernel,
        ap: deviceDataToEdit.ap,
      });
    }
  }, [deviceDataToEdit]);

  const handleDeviceEdit = async (value) => {
    const { modelname, netmask, hostname } = value;
    try {
      const command = [
        {
          command: `device edit ${deviceDataToEdit?.mac} ${modelname} ${netmask} ${hostname}`,
        },
      ];
      await sendCommand(command).unwrap();
      notification.success({
        message: "successfully command sent to edit device!",
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data || error });
    } finally {
      dispatch(clearDeviceDataToEdit());
    }
  };

  return (
    <Modal
      open={openDeviceEditModel}
      width={400}
      forceRender
      maskClosable={false}
      style={{ top: 20 }}
      title="Edit manual device"
      okText="save"
      cancelText="Cancel"
      loading={isLoading}
      onCancel={() => {
        form.resetFields();
        dispatch(clearDeviceDataToEdit());
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            handleDeviceEdit(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        name="edit_device_form"
        style={{
          maxWidth: 400,
        }}
        form={form}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="modelname"
          label="Model name"
          rules={[
            {
              required: true,
              message: "Missing model name!",
            },
            {
              pattern: spaceCheck,
              message: "space not allowed!",
            },
          ]}
        >
          <Input placeholder="model name" />
        </Form.Item>
        <Form.Item
          name="netmask"
          label="Netmask"
          rules={[
            {
              required: true,
              message: "Missing netmask!",
            },
            {
              pattern: ipPattern,
              message: "input should be ip address!",
            },
          ]}
        >
          <Input placeholder="netmask" />
        </Form.Item>
        <Form.Item
          name="hostname"
          label="Hostname"
          rules={[
            {
              required: true,
              message: "Missing hostname!",
            },
            {
              pattern: spaceCheck,
              message: "space not allowed!",
            },
          ]}
        >
          <Input placeholder="hostname" />
        </Form.Item>
        <Alert message="please do not use white-space" banner />
      </Form>
    </Modal>
  );
};

export default DeviceEditModel;
