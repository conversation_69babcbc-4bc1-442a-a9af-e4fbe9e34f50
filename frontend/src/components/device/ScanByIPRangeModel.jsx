import { Form, Input, InputNumber, Modal } from "antd";
import React from "react";

const CIDR_PATTERN =
  /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:3[0-2]|[12]?[0-9])$/;

const ScanByIPRangeModel = ({ open, onCancel, onOk }) => {
  const [form] = Form.useForm();
  return (
    <Modal
      open={open}
      width={400}
      forceRender
      maskClosable={false}
      title="Scan device by IP Range (CIDR)"
      okText="scan"
      cancelText="cancel"
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onOk(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        name="scan_ip_range_form"
        style={{
          maxWidth: 400,
        }}
        form={form}
        autoComplete="off"
        layout="vertical"
        initialValues={{ refreshTimeout: 10 }}
      >
        <Form.Item
          name="cidr"
          label="IP Range (CIDR)"
          rules={[
            {
              required: true,
              message: "Missing IP Range (CIDR)!",
            },
            {
              pattern: CIDR_PATTERN,
              message: "input should be IP Range (CIDR)!",
            },
          ]}
        >
          <Input placeholder="eg. xxx.xxx.xxx.xxx/xx" />
        </Form.Item>
        <Form.Item
          label="Refresh timeout"
          name="refreshTimeout"
          tooltip="The automatic refresh will be stopped after the specified time. The minimum value is 2 minutes and the maximum value is 20 minutes. you can do another scan after the automatic refresh is stopped."
        >
          <InputNumber
            max={20}
            min={2}
            suffix="minutes"
            style={{ width: "100%" }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ScanByIPRangeModel;
