import { Form, Input, InputNumber, Modal } from "antd";
import React from "react";

const ipPattern =
  /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

const TrapSettingModel = ({ open, onCancel, onOk, loading }) => {
  const [form] = Form.useForm();
  return (
    <Modal
      open={open}
      width={400}
      forceRender
      maskClosable={false}
      title="Devices Trap setting"
      cancelText="Cancel"
      loading={loading}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onOk(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_trapSetting">
        <Form.Item
          name="serverIp"
          label="Server IP"
          required
          rules={[
            {
              required: true,
              message: "Please input the server ip !",
            },
            {
              pattern: ipPattern,
              message: "input should be ip address!",
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="serverPort"
          label="Server Port"
          required
          rules={[{ required: true, message: "Please input the port !" }]}
        >
          <InputNumber style={{ width: "100%" }} />
        </Form.Item>
        <Form.Item
          name="comString"
          label="Community String"
          required
          rules={[
            { required: true, message: "Please input the community string !" },
          ]}
        >
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TrapSettingModel;
