import {
  A<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
  theme as antdTheme,
} from "antd";
import { InfoCircleOutlined, SettingOutlined } from "@ant-design/icons";
import packageInfo from "../../../package.json";
import Chang<PERSON>aseUR<PERSON> from "./ChangBaseURL";
import ChangeWSUrl from "./ChangeWSUrl";

const { Text } = Typography;

const SettingsComp = () => {
  const { modal } = App.useApp();
  const { token } = antdTheme.useToken();
  const handleAboutClick = () => {
    modal.info({
      icon: null,
      width: 360,
      className: "confirm-class",
      content: (
        <Flex align="center" vertical>
          <InfoCircleOutlined
            style={{
              color: token.colorInfo,
              fontSize: 64,
            }}
          />
          <Typography.Title level={4}>
            {packageInfo?.name?.replace(/_/g, " ")}
          </Typography.Title>
          <Text strong>{packageInfo?.version}</Text>
          <Text strong>&#169; 2025 - BlackBear TechHive</Text>
        </Flex>
      ),
    });
  };
  const content = (
    <Flex vertical align="center" gap={4}>
      <Typography.Text>Base URL</Typography.Text>
      <ChangBaseURL />
      <Divider style={{ margin: "8px 0" }} />
      <Typography.Text>WebSocket URL</Typography.Text>
      <ChangeWSUrl />
      <Divider style={{ margin: "8px 0" }} />
      <Button
        type="text"
        icon={<InfoCircleOutlined />}
        onClick={handleAboutClick}
        block
      >
        about
      </Button>
    </Flex>
  );
  return (
    <Popover
      placement="bottom"
      title="NIMBL Settings"
      content={content}
      trigger="click"
      showArrow={false}
      style={{ width: "300px" }}
    >
      <Button
        type="primary"
        aria-label="url setting"
        icon={<SettingOutlined />}
      />
    </Popover>
  );
};

export default SettingsComp;
