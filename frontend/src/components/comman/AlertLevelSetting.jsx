import React, { useState } from "react";
import { <PERSON><PERSON>, Select, Space, message } from "antd";
import { useUpdateGlobalSettingsMutation } from "../../app/services/globalSettingsApi";

const { Option } = Select;

// Syslog severity levels based on RFC3164
const ALERT_LEVELS = [
  { value: 0, label: "0 - Emergency", description: "System is unusable" },
  {
    value: 1,
    label: "1 - Alert",
    description: "Action must be taken immediately",
  },
  { value: 2, label: "2 - Critical", description: "Critical conditions" },
  { value: 3, label: "3 - Error", description: "Error conditions" },
  { value: 4, label: "4 - Warning", description: "Warning conditions" },
  {
    value: 5,
    label: "5 - Notice",
    description: "Normal but significant condition",
  },
  {
    value: 6,
    label: "6 - Informational",
    description: "Informational messages",
  },
  { value: 7, label: "7 - Debug", description: "Debug-level messages" },
];

const AlertLevelSetting = () => {
  const [selectedLevel, setSelectedLevel] = useState(1); // Default to Alert level
  const [updateGlobalSettings, { isLoading }] =
    useUpdateGlobalSettingsMutation();

  const handleSave = async () => {
    try {
      const response = await updateGlobalSettings({
        alert_level: selectedLevel,
      }).unwrap();

      message.success("Alert level updated successfully!");
      console.log("Alert level update response:", response);
    } catch (error) {
      console.error("Error updating alert level:", error);
      message.error(
        `Failed to update alert level: ${error?.data?.message || error.message}`
      );
    }
  };

  const handleLevelChange = (value) => {
    setSelectedLevel(value);
  };

  return (
    <Space.Compact style={{ width: "100%" }}>
      <Select
        value={selectedLevel}
        onChange={handleLevelChange}
        style={{ flex: 1 }}
        placeholder="Select alert level"
        size="small"
      >
        {ALERT_LEVELS.map((level) => (
          <Option
            key={level.value}
            value={level.value}
            title={level.description}
          >
            {level.label}
          </Option>
        ))}
      </Select>
      <Button
        type="primary"
        onClick={handleSave}
        loading={isLoading}
        size="small"
      >
        Save
      </Button>
    </Space.Compact>
  );
};

export default AlertLevelSetting;
