import { Button, Input, Space } from "antd";
import React, { useState } from "react";
import { useThemeStore } from "../../utils/themes/useStore";

const ChangBaseURL = () => {
  const { baseURL, changeBaseURL } = useThemeStore();
  const [inputBaseUrl, setInputBaseUrl] = useState(baseURL);
  return (
    <Space.Compact>
      <Input
        value={inputBaseUrl}
        onChange={(e) => setInputBaseUrl(e.target.value)}
      />
      <Button type="primary" onClick={() => changeBaseURL(inputBaseUrl)}>
        save
      </Button>
    </Space.Compact>
  );
};

export default ChangBaseURL;
