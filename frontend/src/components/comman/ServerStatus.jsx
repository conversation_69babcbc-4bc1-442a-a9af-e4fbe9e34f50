import { Bad<PERSON>, Col, Flex, <PERSON>, Tooltip, Typography } from "antd";
import { useThemeStore } from "../../utils/themes/useStore";
import { useSelector } from "react-redux";
import { serverStatusSelector } from "../../features/comman/serverStatusSlice";

const ServerStatus = ({ isDashboardPage }) => {
  const { baseURL } = useThemeStore();
  const { serverOnlineStatus } = useSelector(serverStatusSelector);
  return (
    <Tooltip title={baseURL}>
      <Flex gap={10} align="center" height="100%">
        {serverOnlineStatus ? (
          <Badge color="green" className="cutomBadge" status="processing" />
        ) : (
          <Badge status="error" className="cutomBadge" />
        )}
        {isDashboardPage ? (
          <Row>
            <Col xs={{ span: 0 }} sm={{ span: 24 }}>
              <Typography.Text>{baseURL}</Typography.Text>
            </Col>
          </Row>
        ) : (
          <Typography.Text>{baseURL}</Typography.Text>
        )}
      </Flex>
    </Tooltip>
  );
};

export default ServerStatus;
