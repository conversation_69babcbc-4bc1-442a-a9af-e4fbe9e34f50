import { Form, Input, Modal } from "antd";
import React from "react";

const ManualTopoSaveRestoreForm = ({ open, loading, onCancel, onOk }) => {
  const [form] = Form.useForm();
  return (
    <Modal
      open={open}
      width={400}
      forceRender
      maskClosable={false}
      title="save/restore manual topology"
      okText="ok"
      cancelText="Cancel"
      loading={loading}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            console.log(values);
            onOk(values);
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        name="save_restore_form"
        style={{
          maxWidth: 400,
        }}
        form={form}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="filename"
          label="Filename"
          rules={[
            {
              required: true,
              message: "Missing filename!",
            },
          ]}
        >
          <Input placeholder="filename" suffix=".json" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ManualTopoSaveRestoreForm;
