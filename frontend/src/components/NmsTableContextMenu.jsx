import { Menu, theme } from "antd";
import React from "react";
import { useCallback } from "react";

const NmsTableContextMenu = ({
  menuItems = [],
  record,
  onMenuClick,
  selectedKey = [],
  position,
}) => {
  const { token } = theme.useToken();
  const onClick = useCallback((e, record, selectedKey) => {
    onMenuClick(e.key, record, selectedKey);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps
  const { showMenu, xPos, yPos } = position;
  return showMenu ? (
    <div
      className="menu-container"
      style={{
        position: "absolute",
        background: token.colorBgBase,
        top: yPos + 2 + "px",
        left: xPos + 4 + "px",
        boxShadow: token?.boxShadowCard,
        zIndex: 3,
      }}
    >
      <Menu
        onClick={(e) => onClick(e, record, selectedKey)}
        items={menuItems}
        mode="inline"
      />
    </div>
  ) : null;
};

export default NmsTableContextMenu;
