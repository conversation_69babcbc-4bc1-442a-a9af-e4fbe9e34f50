import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  Button,
  Space,
  Tooltip,
  theme as antdTheme,
  Typography,
  Card,
  Empty,
  message,
  Checkbox,
  InputNumber
} from "antd";
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  Pic<PERSON>enterOutlined,
  DownloadOutlined,
  SettingOutlined,
  PlusOutlined,
  MinusOutlined,
  ExpandOutlined,
  CompressOutlined,
  SaveOutlined,
  ShareAltOutlined,
  InfoCircleOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import G6 from "@antv/g6";
import { useSelector, useDispatch } from "react-redux";
import {
  customTopologySelector,
  selectNode,
  toggleTopologyNodeExpand,
  expandAllTopologyNodes,
  collapseAllTopologyNodes,
  resetTopologyExpandState,
} from "../../features/customTopology/customTopologySlice";
import {
  useUpdateCustomTopologyNodeMutation,
  useDiscoverCustomTopologyLLDPMutation,
  useGetCustomTopologyConnectionsQuery,
} from "../../app/services/customTopologyApi";
import { useGetInventriesQuery } from "../../app/services/commandApi";
import { api } from "../../app/services/api";
import { TopologyImage } from "../topology/TopologyImage";
import TopologyDisplaySettings from "./TopologyDisplaySettings";
import TopologyLegend from "./TopologyLegend";
import useAutoRefresh from "../../hooks/useAutoRefresh";

const { Text } = Typography;

// Graph configuration constants
const GRAPH_CONFIG = {
  DEFAULT_NODE_SIZE: [60, 60], // Fixed default size
  FORCE_LAYOUT: {
    linkDistance: 150,
    nodeStrength: -80,
    edgeStrength: 0.1,
    collideStrength: 1.0,
    nodeSize: 60, // Fixed node size for layout calculations
    alpha: 0.3,
    alphaDecay: 0.028,
    alphaMin: 0.01,
    preventOverlap: true,
  },
  RADIAL_LAYOUT: {
    type: "radial",
    center: [400, 300],
    radius: 150, // Slightly larger radius for better spacing
    focusNode: null, // Will be set dynamically to the central node
    unitRadius: 100, // Larger unit radius to prevent overcrowding
    preventOverlap: true,
    strictRadial: false,
    nodeSize: 60, // Fixed node size
  },
  ANIMATION: {
    repeat: true,
    duration: 3000,
  },
};

// Helper function to normalize MAC addresses for comparison
// Converts various formats (00:11:22:33:44:55, 00-11-22-33-44-55) to lowercase with dashes
const normalizeMac = (mac) => {
  if (!mac) return "";
  // Remove all separators and convert to lowercase
  const cleaned = mac.toLowerCase().replace(/[:\-]/g, "");
  // Add dashes between each pair of characters
  const normalized = cleaned.match(/.{2}/g)?.join("-") || "";
  return normalized;
};

// Register custom rounded rectangle node for nodes
G6.registerNode(
  "rounded-rect-node",
  {
    draw(cfg, group) {
      const { size = [100, 40], label } = cfg;
      const [width, height] = size;
      const radius = 8;

      // Create rounded rectangle
      const rect = group.addShape("rect", {
        attrs: {
          x: -width / 2,
          y: -height / 2,
          width,
          height,
          radius,
          fill: cfg.color || "#1890ff",
          stroke: cfg.stroke || "#fff",
          lineWidth: cfg.lineWidth || 2,
        },
        name: "main-box",
      });

      // Add text label inside the rectangle
      if (label) {
        const hasNavIcon =
          cfg.originalData &&
          cfg.originalData.type === "node" &&
          cfg.hasChildren;
        const maxTextWidth = hasNavIcon ? width - 40 : width - 20; // Reserve space for icon

        // Truncate text if it's too long
        let displayText = label;
        const avgCharWidth = 7; // Approximate character width at font size 12
        const maxChars = Math.floor(maxTextWidth / avgCharWidth);

        if (displayText.length > maxChars) {
          displayText = displayText.substring(0, maxChars - 3) + "...";
        }

        group.addShape("text", {
          attrs: {
            x: hasNavIcon ? -15 : 0, // Shift text more left when icon present
            y: 0,
            textAlign: "center",
            textBaseline: "middle",
            text: displayText,
            fill: "#fff",
            fontSize: 12,
            fontWeight: "bold",
            fontFamily: "Arial, sans-serif",
          },
          name: "label-text",
        });
      }

      // Add expand/collapse toggle icon only for non-device nodes that have children
      if (
        cfg.originalData &&
        cfg.originalData.type === "node" &&
        cfg.hasChildren
      ) {
        const iconX = width / 2 - 16; // Position near right edge

        // Check if node is expanded (default collapsed)
        const isExpanded = cfg.isExpanded || false;

        // Create a small circle background for the icon
        group.addShape("circle", {
          attrs: {
            x: iconX,
            y: 0,
            r: 8,
            fill: "rgba(255, 255, 255, 0.3)",
            stroke: "#fff",
            strokeWidth: 1,
            cursor: "pointer",
          },
          name: "expand-icon-bg",
        });

        if (isExpanded) {
          // Create minus icon for collapse
          group.addShape("rect", {
            attrs: {
              x: iconX - 4,
              y: -1,
              width: 8,
              height: 2,
              fill: "#fff",
              cursor: "pointer",
            },
            name: "expand-icon",
          });
        } else {
          // Create plus icon for expand (vertical line)
          group.addShape("rect", {
            attrs: {
              x: iconX - 4,
              y: -1,
              width: 8,
              height: 2,
              fill: "#fff",
              cursor: "pointer",
            },
            name: "expand-icon-horizontal",
          });
          // Vertical line for plus
          group.addShape("rect", {
            attrs: {
              x: iconX - 1,
              y: -4,
              width: 2,
              height: 8,
              fill: "#fff",
              cursor: "pointer",
            },
            name: "expand-icon-vertical",
          });
        }
      }

      return rect;
    },
    update(cfg, item) {
      const group = item.getContainer();
      const rect = group.find((e) => e.get("name") === "main-box");
      const text = group.find((e) => e.get("name") === "label-text");

      if (rect) {
        rect.attr({
          fill: cfg.color || "#1890ff",
          stroke: cfg.stroke || "#fff",
          lineWidth: cfg.lineWidth || 2,
        });
      }

      if (text && cfg.label) {
        const { size = [100, 40] } = cfg;
        const [width] = size;
        const hasNavIcon =
          cfg.originalData &&
          cfg.originalData.type === "node" &&
          cfg.hasChildren;
        const maxTextWidth = hasNavIcon ? width - 40 : width - 20;

        // Truncate text if it's too long
        let displayText = cfg.label;
        const avgCharWidth = 7;
        const maxChars = Math.floor(maxTextWidth / avgCharWidth);

        if (displayText.length > maxChars) {
          displayText = displayText.substring(0, maxChars - 3) + "...";
        }

        text.attr({
          text: displayText,
          x: hasNavIcon ? -15 : 0,
        });
      }
    },
  },
  "rect"
);

// Register custom edge with animation for device connections
G6.registerEdge(
  "custom-circle-running",
  {
    afterDraw(cfg, group) {
      const shape = group.get("children")[0];
      const startPoint = shape.getPoint(0);
      const color = cfg.circleColor || "#1890ff";

      if (!startPoint || color === "transparent") return;

      // Remove any existing circles to prevent duplicates
      const existingCircles = group.findAll(
        (element) => element.get("name") === "running-circle"
      );
      existingCircles.forEach((circle) => circle.remove());

      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint.x,
          y: startPoint.y,
          fill: color,
          r: 3,
        },
        name: "running-circle",
      });

      circle.animate((ratio) => {
        const tmpPoint = shape.getPoint(ratio);
        return tmpPoint ? { x: tmpPoint.x, y: tmpPoint.y } : { x: 0, y: 0 };
      }, GRAPH_CONFIG.ANIMATION);
    },
    afterUpdate(cfg, item) {
      const group = item.getContainer();
      this.afterDraw(cfg, group);
    },
    setState(name, value, item) {
      if (name === "refresh" && value) {
        const group = item.getContainer();
        const cfg = item.getModel();
        this.afterDraw(cfg, group);
      }
    },
  },
  "line"
);

// Register custom edge for hierarchical relationships (no animation)
G6.registerEdge(
  "hierarchical-edge",
  {
    draw(cfg, group) {
      const { startPoint, endPoint } = cfg;
      const style = cfg.style || {};

      // Create a dashed line for hierarchical connections
      const line = group.addShape("path", {
        attrs: {
          path: [
            ["M", startPoint.x, startPoint.y],
            ["L", endPoint.x, endPoint.y],
          ],
          stroke: style.stroke || cfg.color || "#8c8c8c",
          lineWidth: style.lineWidth || cfg.lineWidth || 2,
          lineDash: style.lineDash || [5, 5], // Use style.lineDash or default to [5, 5]
          opacity: style.opacity !== undefined ? style.opacity : 0.8,
        },
        name: "hierarchical-line",
      });

      return line;
    },
    update(cfg, item) {
      const group = item.getContainer();
      const line = group.find((e) => e.get("name") === "hierarchical-line");

      if (line) {
        const { startPoint, endPoint } = cfg;
        const style = cfg.style || {};

        line.attr({
          path: [
            ["M", startPoint.x, startPoint.y],
            ["L", endPoint.x, endPoint.y],
          ],
          stroke: style.stroke || cfg.color || "#8c8c8c",
          lineWidth: style.lineWidth || cfg.lineWidth || 2,
          lineDash: style.lineDash || [5, 5],
          opacity: style.opacity !== undefined ? style.opacity : 0.8,
        });
      }
    },
  },
  "line"
);

// Toolbar configuration
const createToolbar = () =>
  new G6.ToolBar({
    position: { x: 10, y: 10 },
    getContent: () => `
    <ul class='g6-component-toolbar'>
      <li code='zoomOut'>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
          <path d="M658.432 428.736a33.216 33.216 0 0 1-33.152 33.152H525.824v99.456a33.216 33.216 0 0 1-66.304 0V461.888H360.064a33.152 33.152 0 0 1 0-66.304H459.52V296.128a33.152 33.152 0 0 1 66.304 0V395.52H625.28c18.24 0 33.152 14.848 33.152 33.152z m299.776 521.792a43.328 43.328 0 0 1-60.864-6.912l-189.248-220.992a362.368 362.368 0 0 1-215.36 70.848 364.8 364.8 0 1 1 364.8-364.736 363.072 363.072 0 0 1-86.912 235.968l192.384 224.64a43.392 43.392 0 0 1-4.8 61.184z m-465.536-223.36a298.816 298.816 0 0 0 298.432-298.432 298.816 298.816 0 0 0-298.432-298.432A298.816 298.816 0 0 0 194.24 428.8a298.816 298.816 0 0 0 298.432 298.432z"></path>
        </svg>
      </li>
      <li code='zoomIn'>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
          <path d="M639.936 416a32 32 0 0 1-32 32h-256a32 32 0 0 1 0-64h256a32 32 0 0 1 32 32z m289.28 503.552a41.792 41.792 0 0 1-58.752-6.656l-182.656-213.248A349.76 349.76 0 0 1 480 768 352 352 0 1 1 832 416a350.4 350.4 0 0 1-83.84 227.712l185.664 216.768a41.856 41.856 0 0 1-4.608 59.072zM479.936 704c158.784 0 288-129.216 288-288S638.72 128 479.936 128a288.32 288.32 0 0 0-288 288c0 158.784 129.216 288 288 288z" p-id="3853"></path>
        </svg>
      </li>
      <li code='realZoom'>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="24">
          <path d="M384 320v384H320V320h64z m256 0v384H576V320h64zM512 576v64H448V576h64z m0-192v64H448V384h64z m355.968 576H92.032A28.16 28.16 0 0 1 64 931.968V28.032C64 12.608 76.608 0 95.168 0h610.368L896 192v739.968a28.16 28.16 0 0 1-28.032 28.032zM704 64v128h128l-128-128z m128 192h-190.464V64H128v832h704V256z"></path>
        </svg>
      </li>
    </ul>`,
    handleClick: (code, graph) => {
      switch (code) {
        case "zoomOut":
          graph.zoom(1.2, undefined, true, {
            duration: 200,
            easing: "easeCubic",
          });
          break;
        case "zoomIn":
          graph.zoom(0.8, undefined, true, {
            duration: 200,
            easing: "easeCubic",
          });
          break;
        case "realZoom":
          graph.zoomTo(1, undefined, true, {
            duration: 200,
            easing: "easeCubic",
          });
          break;
        default:
          break;
      }
    },
  });

const TopologyView = () => {
  const { token } = antdTheme.useToken();
  const dispatch = useDispatch();
  const containerRef = useRef(null);
  const [graph, setGraph] = useState(null);
  const [isRendered, setIsRendered] = useState(false);
  const [settingsModalOpen, setSettingsModalOpen] = useState(false);
  const [legendVisible, setLegendVisible] = useState(true);
  // Note: layoutChanges and expandCollapseChanges removed since we now auto-save
  const [updateNodeMutation] = useUpdateCustomTopologyNodeMutation();
  const [discoverLLDPMutation, { isLoading: isDiscoveringLLDP }] =
    useDiscoverCustomTopologyLLDPMutation();

  const {
    topologyData,
    selectedNode,
    displaySettings,
    topologyExpandedNodes,
    allNodesExpanded,
  } = useSelector(customTopologySelector);

  // Get LLDP connections for the current tree
  const { data: lldpConnections = [], isLoading: isLoadingConnections } =
    useGetCustomTopologyConnectionsQuery(
      "default", // Tree name - using "default" for now
      {
        skip: !selectedNode, // Only fetch when a node is selected
        refetchOnMountOrArgChange: true,
      }
    );

  // Get device inventory data for real device status
  const { data: deviceInventory = [], isLoading: isLoadingDevices } =
    useGetInventriesQuery({
      inventoryType: "device",
      groupid: "all",
    });

  // Convert array to Set for backward compatibility with existing logic
  const expandedNodes = new Set(topologyExpandedNodes);

  // Only reset expanded state when a DIFFERENT node is selected
  const [previousSelectedNodeId, setPreviousSelectedNodeId] = useState(null);

  // Restore expand state when topology data changes or when selecting different node
  useEffect(() => {
    if (selectedNode && topologyData?.nodes) {
      // Always restore expand state from backend when topology data is available
      const savedExpandedNodes = [];

      // Function to recursively collect expanded nodes from topology data
      const collectExpandedNodes = (nodes) => {
        nodes.forEach((node) => {
          if (node.originalData?.properties?.expanded === true) {
            savedExpandedNodes.push(node.id);
            console.log(
              `Found expanded node from backend: ${node.id}`,
              node.originalData.properties
            );
          }
        });
      };

      // Collect expanded state from current topology data
      collectExpandedNodes(topologyData.nodes);

      // Only update expand state when it's a different node selection
      // Don't reset on topology data updates to avoid collapsing during auto-save
      const isNewNodeSelection =
        !previousSelectedNodeId || previousSelectedNodeId !== selectedNode.id;
      if (isNewNodeSelection) {
        console.log(
          `Restoring expand state for new node selection: ${savedExpandedNodes.length} nodes to expand`
        );

        // Reset and restore the expand state only for new node selections
        dispatch(resetTopologyExpandState());
        if (savedExpandedNodes.length > 0) {
          savedExpandedNodes.forEach((nodeId) => {
            dispatch(toggleTopologyNodeExpand(nodeId));
          });
        }

        // Update the previous selected node ID
        setPreviousSelectedNodeId(selectedNode.id);
      }
    }
  }, [selectedNode, dispatch, previousSelectedNodeId]);

  // Create node label for rounded rectangle nodes
  const createNodeLabel = useCallback((node) => {
    // For nodes, show just the name without extra descriptors
    if (node.originalData && node.originalData.type === "node") {
      return node.originalData.name;
    }
    return node.label || node.name || "Node";
  }, []);

  // Check if a node has children
  const nodeHasChildren = useCallback((node) => {
    return (
      node && node.type === "node" && node.children && node.children.length > 0
    );
  }, []);

  // Handle node expand/collapse with auto-save
  const handleNodeToggle = useCallback(
    async (nodeId) => {
      console.log(
        `Toggling node ${nodeId}, currently expanded:`,
        expandedNodes.has(nodeId)
      );

      // Update Redux state
      dispatch(toggleTopologyNodeExpand(nodeId));

      // Auto-save expand/collapse state immediately
      const isCurrentlyExpanded = expandedNodes.has(nodeId);
      const newExpandedState = !isCurrentlyExpanded;

      // Find the node in topologyData to get its original data
      const nodeData = topologyData.nodes.find((n) => n.id === nodeId);
      if (nodeData && nodeData.originalData && selectedNode) {
        try {
          console.log(
            `Saving expand state for node ${nodeId} (originalId: ${nodeData.originalData.id}): ${newExpandedState}`
          );
          await updateNodeMutation({
            treeName: "default", // assuming default tree name for now
            nodeId: nodeData.originalData.id,
            properties: {
              expanded: newExpandedState,
            },
          }).unwrap();
          console.log(
            `✅ Expand/collapse state saved successfully for node ${nodeId}: ${newExpandedState}`
          );
        } catch (error) {
          console.error(
            `❌ Failed to auto-save expand/collapse state for node ${nodeId}:`,
            error
          );
        }
      } else {
        console.warn(`⚠️ Cannot save expand state - missing data:`, {
          nodeData: !!nodeData,
          originalData: !!nodeData?.originalData,
          selectedNode: !!selectedNode,
          nodeId,
        });
      }
    },
    [dispatch, expandedNodes, topologyData, selectedNode, updateNodeMutation]
  );

  // Expand all nodes that have children (including first-level nodes)
  const handleExpandAll = useCallback(() => {
    const allExpandableNodeIds = [];

    // Get all nodes from the topology that can be expanded
    if (topologyData.nodes) {
      topologyData.nodes.forEach((node) => {
        if (
          node.originalData?.type === "node" &&
          nodeHasChildren(node.originalData)
        ) {
          allExpandableNodeIds.push(node.id);
        }
      });
    }

    dispatch(expandAllTopologyNodes(allExpandableNodeIds));
  }, [topologyData, nodeHasChildren, dispatch]);

  // Collapse all nodes
  const handleCollapseAll = useCallback(() => {
    dispatch(collapseAllTopologyNodes());
  }, [dispatch]);

  // Filter topology data based on expanded state and hierarchy
  const getFilteredTopologyData = useCallback(
    (data) => {
      if (!data || !data.nodes) return { nodes: [], edges: [] };

      // Helper function to find the immediate parent of a node in the tree hierarchy
      const findImmediateParent = (nodeToFind, currentNode = selectedNode) => {
        if (!currentNode || !currentNode.children) return null;

        // Check if nodeToFind is a direct child of currentNode
        const directChild = currentNode.children.find(
          (child) => child.id === nodeToFind.id
        );
        if (directChild) {
          return currentNode;
        }

        // Recursively search in children
        for (const child of currentNode.children) {
          if (child.type === "node") {
            const found = findImmediateParent(nodeToFind, child);
            if (found) return found;
          }
        }

        return null;
      };

      const filteredNodes = data.nodes.filter((node) => {
        // Always show the selected central node
        if (node.isSelected) return true;

        const nodeData = node.originalData;
        if (!nodeData) return false;

        // Find the immediate parent of this node
        const immediateParent = findImmediateParent(nodeData);

        if (!immediateParent) return false;

        // Only show children if their immediate parent is expanded (no default expansion)
        return expandedNodes.has(immediateParent.id);
      });

      // Filter edges to only show connections between visible nodes
      const visibleNodeIds = new Set(filteredNodes.map((n) => n.id));
      const filteredEdges = data.edges.filter(
        (edge) =>
          visibleNodeIds.has(edge.source) && visibleNodeIds.has(edge.target)
      );

      return { nodes: filteredNodes, edges: filteredEdges };
    },
    [expandedNodes, selectedNode]
  );

  // Helper function to get device status by MAC address
  const getDeviceStatus = useCallback(
    (macAddress) => {
      if (!deviceInventory || !macAddress) {
        console.log("getDeviceStatus: Missing data", {
          deviceInventory: !!deviceInventory,
          macAddress,
        });
        return "unknown";
      }

      console.log("getDeviceStatus: Looking up MAC", macAddress);

      // Find device in inventory by MAC address
      const device = deviceInventory.find(
        (d) => d.mac && d.mac.toLowerCase() === macAddress.toLowerCase()
      );

      if (!device) {
        console.log(
          "getDeviceStatus: Device not found for MAC",
          macAddress,
          "Available devices:",
          deviceInventory.map((d) => d.mac)
        );
        return "unknown";
      }

      console.log("getDeviceStatus: Found device", { mac: macAddress, device });
      // Use the status field directly (set by backend syslog polling)
      // Fallback to isonline if status field is not available
      const status = device.status || (device.isonline ? "online" : "offline");

      console.log("getDeviceStatus: Found device status", {
        mac: macAddress,
        status,
        device,
      });
      return status;
    },
    [deviceInventory]
  );

  // Generate device label based on display settings
  const createDeviceLabel = useCallback((deviceData, settings) => {
    const parts = [];

    if (settings.deviceName && deviceData.name) {
      // For devices, name is currently MAC address - could be improved to show hostname if available
      parts.push(deviceData.name);
    }

    if (settings.ipAddress && deviceData.ipAddress) {
      parts.push(deviceData.ipAddress);
    }

    if (settings.macAddress && deviceData.macAddress) {
      parts.push(deviceData.macAddress);
    }

    if (settings.deviceModel && deviceData.modelName) {
      parts.push(deviceData.modelName);
    }

    // Note: Status is now handled separately in device processing, not in the label

    if (settings.portInfo) {
      // Add port info if available (placeholder for now)
      parts.push("eth0");
    }

    return parts.length > 0 ? parts.join("\n") : deviceData.name || "Device";
  }, []);

  // Store node positions to maintain stable layout
  const [nodePositions, setNodePositions] = useState(new Map());

  // Calculate stable positions for nodes to prevent layout jumping
  const calculateStablePositions = useCallback(
    (nodes) => {
      const positions = new Map();
      // Use current graph center or default values
      const centerX = graph ? graph.getWidth() / 2 : 400;
      const centerY = graph ? graph.getHeight() / 2 : 300;
      const radius = 200; // Increased radius for more spacing

      // Find central node and direct children
      const centralNode = nodes.find((n) => n.isSelected);
      const directChildren = nodes.filter(
        (n) =>
          !n.isSelected &&
          selectedNode?.children?.some(
            (child) => child.id === n.originalData?.id
          )
      );

      // Position central node at center (but use saved position if available)
      if (centralNode) {
        const savedLayout = centralNode.originalData?.properties?.layout;
        if (
          savedLayout &&
          savedLayout.x !== undefined &&
          savedLayout.y !== undefined
        ) {
          positions.set(centralNode.id, { x: savedLayout.x, y: savedLayout.y });
        } else {
          positions.set(centralNode.id, { x: centerX, y: centerY });
        }
      }

      // Position direct children in a circle around center
      directChildren.forEach((child, index) => {
        const savedLayout = child.originalData?.properties?.layout;
        if (
          savedLayout &&
          savedLayout.x !== undefined &&
          savedLayout.y !== undefined
        ) {
          positions.set(child.id, { x: savedLayout.x, y: savedLayout.y });
        } else {
          const angle = (2 * Math.PI * index) / directChildren.length;
          const x = centerX + radius * Math.cos(angle);
          const y = centerY + radius * Math.sin(angle);
          positions.set(child.id, { x, y });
        }
      });

      // Position deeper-level nodes near their parents
      const positionDeepNodes = (parentNodes, level = 2) => {
        parentNodes.forEach((parentNode) => {
          const parentPos = positions.get(parentNode.id);
          if (!parentPos || !parentNode.originalData?.children) return;

          const children = nodes.filter((n) =>
            parentNode.originalData.children.some(
              (child) => child.id === n.originalData?.id
            )
          );

          children.forEach((child, index) => {
            if (!positions.has(child.id)) {
              const savedLayout = child.originalData?.properties?.layout;
              if (
                savedLayout &&
                savedLayout.x !== undefined &&
                savedLayout.y !== undefined
              ) {
                positions.set(child.id, { x: savedLayout.x, y: savedLayout.y });
              } else {
                // Position children around their parent with more spacing
                const childRadius = 120; // Increased radius for sub-children spacing
                const angle = (2 * Math.PI * index) / children.length;
                const baseAngle = Math.atan2(
                  parentPos.y - centerY,
                  parentPos.x - centerX
                );
                const finalAngle = baseAngle + angle * 0.5; // Spread around parent's direction

                const x = parentPos.x + childRadius * Math.cos(finalAngle);
                const y = parentPos.y + childRadius * Math.sin(finalAngle);
                positions.set(child.id, { x, y });
              }
            }
          });

          // Recursively position deeper nodes
          const childNodes = children.filter(
            (c) => c.originalData?.type === "node"
          );
          if (childNodes.length > 0) {
            positionDeepNodes(childNodes, level + 1);
          }
        });
      };

      // Position deeper level nodes
      const parentNodes = directChildren.filter(
        (n) => n.originalData?.type === "node"
      );
      if (parentNodes.length > 0) {
        positionDeepNodes(parentNodes);
      }

      return positions;
    },
    [selectedNode, graph]
  );

  // Process topology data for G6
  const processTopologyData = useCallback(
    (data, connections = []) => {
      if (!data || !data.nodes || data.nodes.length === 0) {
        return { nodes: [], edges: [] };
      }

      // Calculate stable positions for all nodes, but don't override current positions if graph exists
      const stablePositions = calculateStablePositions(data.nodes);

      const labelStyle = {
        style: {
          fill: token.colorText,
          fontSize: 11,
          background: {
            fill: "transparent",
            padding: [2, 2, 2, 2],
          },
        },
      };

      const nodes = data.nodes.map((node) => {
        // Priority: 1. Current graph position 2. Stable position from backend
        let position = stablePositions.get(node.id) || { x: 400, y: 300 };

        // If graph exists and node exists in graph, use its current position to avoid snapping back
        if (graph && !graph.destroyed) {
          const graphNode = graph.findById(node.id);
          if (graphNode) {
            const currentModel = graphNode.getModel();
            if (currentModel.x !== undefined && currentModel.y !== undefined) {
              position = { x: currentModel.x, y: currentModel.y };
            }
          }
        }

        // Determine node properties based on original data
        if (node.originalData && node.originalData.type === "device") {
          // Get real device status by MAC address lookup
          // Don't show "unknown" while device data is still loading
          const deviceStatus = isLoadingDevices
            ? "loading"
            : node.originalData.macAddress
            ? getDeviceStatus(node.originalData.macAddress)
            : "unknown";
          const isOnline = deviceStatus === "online";
          const isOffline = deviceStatus === "offline";

          // Define status colors
          const statusColors = {
            online: "#52c41a", // Green
            offline: "#ff4d4f", // Red
            loading: "#1890ff", // Blue
            unknown: "#8c8c8c", // Gray
          };

          const deviceStroke =
            statusColors[deviceStatus] || statusColors.unknown;
          const deviceOpacity = isOnline ? 1 : 0.7; // Dimmed for offline/unknown devices
          const strokeWidth = isOffline ? 3 : 2; // Thicker border for offline devices

          // Create enhanced device label with status information
          let deviceLabel = createDeviceLabel(
            node.originalData,
            displaySettings
          );

          // Add status indicator to label if status display is enabled
          if (displaySettings.status) {
            const statusText = deviceStatus.toUpperCase();
            const statusEmoji = isOnline ? "🟢" : isOffline ? "🔴" : "🟡";
            deviceLabel = deviceLabel
              ? `${deviceLabel}\n${statusEmoji} ${statusText}`
              : `${statusEmoji} ${statusText}`;
          }

          // Devices use image type with device images from main topology
          return {
            ...node,
            type: "image",
            img: TopologyImage(node.originalData.modelname),
            size: [56, 56], // Same size as main topology
            label: deviceLabel,
            x: position.x, // Set fixed position
            y: position.y, // Set fixed position
            labelCfg: {
              ...labelStyle,
              position: "bottom",
              style: {
                ...labelStyle.style,
                opacity: deviceOpacity,
                fill: isOffline ? statusColors.offline : labelStyle.style.fill, // Red text for offline devices
                fontWeight: isOffline ? "bold" : "normal", // Bold text for offline devices
              },
            },
            style: {
              stroke: deviceStroke,
              lineWidth: strokeWidth,
              opacity: deviceOpacity,
              ...node.style,
            },
          };
        } else {
          // Nodes use custom rounded rectangle
          let nodeColor = "#1890ff"; // Default blue
          let nodeSize = [100, 40]; // Rectangle size

          // Make the selected/central node larger and red
          if (node.isSelected) {
            nodeColor = "#ff4d4f"; // Red for selected node
            nodeSize = [120, 50]; // Larger rectangle
          }

          return {
            ...node,
            type: "rounded-rect-node",
            label: createNodeLabel(node),
            size: nodeSize,
            color: nodeColor,
            stroke: "#fff",
            lineWidth: 2,
            hasChildren: nodeHasChildren(node.originalData),
            isExpanded: expandedNodes.has(node.id), // Add expanded state
            x: position.x, // Set fixed position
            y: position.y, // Set fixed position
            labelCfg: {
              style: {
                fill: "transparent", // Hide bottom label since text is inside the rectangle
              },
            },
            style: {
              stroke: nodeColor,
              lineWidth: 2,
              ...node.style,
            },
          };
        }
      });

      // Process existing edges from topology data based on display settings
      const existingEdges = data.edges.map((edge) => {
        // Determine connection types from the topology data
        const sourceNode = data.nodes.find((n) => n.id === edge.source);
        const targetNode = data.nodes.find((n) => n.id === edge.target);

        // Check if both nodes exist and determine their types
        const isNodeToNodeConnection =
          sourceNode?.originalData?.type === "node" &&
          targetNode?.originalData?.type === "node";

        const isHierarchicalConnection =
          sourceNode?.originalData?.type === "node" ||
          targetNode?.originalData?.type === "node";

        const isDeviceToDeviceConnection =
          sourceNode?.originalData?.type === "device" &&
          targetNode?.originalData?.type === "device";

        // Get LinkType from edge data (from backend topology)
        const linkType = edge.linkType || "default";
        const isConnectionLost = linkType === "dashed";

        // Define colors for different LinkTypes
        const linkTypeColors = {
          dashed: "#ff4d4f", // Red - connection lost
          snmp: "#1890ff", // Blue - SNMP discovered
          agent: "#52c41a", // Green - Agent discovered
          manual: "#fa8c16", // Orange - Manual configuration
          default: "#8c8c8c", // Gray - Unknown/default
        };

        const linkColor = linkTypeColors[linkType] || linkTypeColors.default;

        if (isHierarchicalConnection) {
          // Check if hierarchical connections should be shown
          if (!displaySettings.hierarchicalConnections) {
            return null; // Skip hierarchical connections if disabled
          }

          // Both node-to-node and node-to-device connections use hierarchical edge (no animation, dashed)
          return {
            ...edge,
            type: "hierarchical-edge",
            color: "#8c8c8c", // Gray for hierarchical connections (unchanged)
            label: "", // Remove label text
            labelCfg: {
              style: {
                fill: "transparent", // Hide any remaining label
              },
            },
            style: {
              lineWidth: 2,
              stroke: "#8c8c8c",
              lineDash: [5, 5],
              opacity: 0.8,
            },
          };
        } else {
          // Device-to-device connections - apply LinkType styling
          if (isConnectionLost) {
            // Connection lost - show as red dashed line
            return {
              ...edge,
              type: "hierarchical-edge", // Use hierarchical-edge for dashed line
              color: linkColor,
              label: "", // Remove label text
              labelCfg: {
                style: {
                  fill: "transparent", // Hide any remaining label
                },
              },
              style: {
                lineWidth: 3, // Thicker line to emphasize problem
                stroke: linkColor,
                //lineDash: [8, 8], // Larger dash pattern for emphasis
                opacity: 0.7,
              },
            };
          } else {
            // Normal connection - use animated edge with LinkType color
            return {
              ...edge,
              type: "custom-circle-running",
              color: linkColor,
              circleColor: linkColor,
              label: "", // Remove label text
              labelCfg: {
                style: {
                  fill: "transparent", // Hide any remaining label
                },
              },
              style: {
                lineWidth: 2,
                stroke: linkColor,
              },
            };
          }
        }
      });

      // Debug logging: Show all available device nodes and their MAC addresses
      console.log(
        "📊 Available device nodes in topology:",
        data.nodes
          .filter((n) => n.originalData?.type === "device")
          .map((n) => ({
            id: n.id,
            name: n.originalData?.name,
            macAddress: n.originalData?.macAddress,
            normalizedMac: normalizeMac(n.originalData?.macAddress),
          }))
      );

      console.log(
        `🔗 Processing ${connections.length} LLDP connections...`
      );

      // Deduplicate bidirectional LLDP connections
      // Keep only one edge per device pair, prioritizing: blocked > inactive > active
      const connectionMap = new Map();

      connections.forEach((connection) => {
        const normalizedSourceMac = normalizeMac(connection.sourceMac);
        const normalizedTargetMac = normalizeMac(connection.targetMac);

        // Create a sorted key so A-B and B-A use the same key
        const pairKey = [normalizedSourceMac, normalizedTargetMac].sort().join('|');

        const existing = connectionMap.get(pairKey);

        if (!existing) {
          // No existing connection for this pair, add it
          connectionMap.set(pairKey, connection);
        } else {
          // Connection exists, keep the higher priority one
          const isBlocked = connection.blockedPort === "true" || connection.blockedPort === true;
          const existingBlocked = existing.blockedPort === "true" || existing.blockedPort === true;
          const isActive = connection.status === "active";
          const existingActive = existing.status === "active";

          // Priority: blocked > inactive > active
          if (isBlocked && !existingBlocked) {
            // Current is blocked, existing is not - replace
            connectionMap.set(pairKey, connection);
          } else if (!isBlocked && existingBlocked) {
            // Existing is blocked, current is not - keep existing
            // Do nothing
          } else if (!isActive && existingActive) {
            // Current is inactive, existing is active - replace
            connectionMap.set(pairKey, connection);
          }
          // Otherwise keep existing
        }
      });

      const deduplicatedConnections = Array.from(connectionMap.values());

      console.log(
        `🔄 Deduplicated ${connections.length} connections to ${deduplicatedConnections.length} unique edges`
      );

      // Process LLDP connections as edges based on display settings
      const lldpEdges = deduplicatedConnections
        .map((connection) => {
          // Find nodes by MAC address using normalized comparison
          // This handles different MAC formats (00:11:22:33:44:55, 00-11-22-33-44-55)
          const normalizedSourceMac = normalizeMac(connection.sourceMac);
          const normalizedTargetMac = normalizeMac(connection.targetMac);

          const sourceNode = data.nodes.find(
            (n) =>
              n.originalData?.type === "device" &&
              normalizeMac(n.originalData?.macAddress) === normalizedSourceMac
          );
          const targetNode = data.nodes.find(
            (n) =>
              n.originalData?.type === "device" &&
              normalizeMac(n.originalData?.macAddress) === normalizedTargetMac
          );

          if (!sourceNode) {
            console.warn(
              `❌ Source node not found for MAC: ${connection.sourceMac} (normalized: ${normalizedSourceMac})`
            );
          }
          if (!targetNode) {
            console.warn(
              `❌ Target node not found for MAC: ${connection.targetMac} (normalized: ${normalizedTargetMac})`
            );
          }

          // Only create edge if both devices are visible in current topology
          if (sourceNode && targetNode) {
            // Use MACs and ports to create unique edge ID (connection.id may not be unique)
            const edgeId = `lldp_${connection.sourceMac}_${connection.targetMac}_${connection.sourcePort}_${connection.targetPort}`.replace(
              /[:\-\s]/g,
              "_"
            );

            // Determine visual properties based on connection status and blocked port
            const isActive = connection.status === "active";
            // Handle both string "true" and boolean true
            const isBlocked = connection.blockedPort === "true" || connection.blockedPort === true;

            // Check display settings to determine if we should show inactive connections
            if (!isActive && !displaySettings.inactiveConnections) {
              return null; // Skip inactive connections if disabled
            }

            // Check if LLDP connections are enabled
            if (!displaySettings.lldpConnections) {
              return null; // Skip LLDP connections if disabled
            }

            // Priority: BlockedPort > Status
            // Determine visual properties based on priority
            let connectionColor;
            let edgeType;
            let lineStyle;
            let opacity;
            let lineWidth;

            if (isBlocked) {
              // Blocked port - highest priority (STP/RSTP blocked)
              connectionColor = "#722ed1"; // Purple for blocked port
              edgeType = "hierarchical-edge"; // Dashed line
              lineStyle = [4, 4]; // Short dashes
              opacity = 0.8;
              lineWidth = 3; // Thick line for emphasis
            } else if (!isActive) {
              // Inactive connection (device offline)
              connectionColor = "#ff4d4f"; // Red for inactive
              edgeType = "hierarchical-edge"; // Dashed line
              lineStyle = [8, 8]; // Longer dashes
              opacity = 0.6;
              lineWidth = 2;
            } else {
              // Active connection
              connectionColor = "#52c41a"; // Green for active
              edgeType = "custom-circle-running"; // Animated line
              lineStyle = undefined;
              opacity = 1;
              lineWidth = 3;
            }

            // Build connection label based on display settings
            let connectionLabel = "";
            if (displaySettings.connectionLabels) {
              const labelParts = [];
              if (displaySettings.connectionPorts) {
                labelParts.push(
                  `${connection.sourcePort} ↔ ${connection.targetPort}`
                );
              }
              // Show blocked status first, then inactive
              if (isBlocked) {
                labelParts.push("[BLOCKED]");
              } else if (displaySettings.connectionStatus && !isActive) {
                labelParts.push("[INACTIVE]");
              }
              connectionLabel = labelParts.join(" ");
            }

            const edgeObj = {
              id: edgeId,
              source: sourceNode.id,
              target: targetNode.id,
              type: edgeType,
              color: connectionColor,
              circleColor: connectionColor,
              label: connectionLabel,
              labelCfg: {
                position: "middle",
                style: {
                  fill: connectionColor,
                  fontSize: 10,
                  fontWeight: "bold",
                  opacity: opacity,
                  background: {
                    fill: "rgba(255, 255, 255, 0.8)",
                    padding: [2, 4, 2, 4],
                    radius: 2,
                  },
                },
              },
              style: {
                lineWidth: lineWidth,
                stroke: connectionColor,
                lineDash: lineStyle,
                opacity: opacity,
              },
              // Add connection metadata for tooltips
              connectionData: connection,
            };

            return edgeObj;
          }
          return null;
        })
        .filter(Boolean); // Remove null entries

      console.log(
        `✅ Successfully created ${lldpEdges.length} LLDP edges out of ${deduplicatedConnections.length} unique connections`
      );
      if (lldpEdges.length < deduplicatedConnections.length) {
        console.warn(
          `⚠️ ${deduplicatedConnections.length - lldpEdges.length} LLDP connections were not rendered (check warnings above)`
        );
      }

      // Combine all edges and filter out null values
      const edges = [...existingEdges.filter(Boolean), ...lldpEdges];

      return { nodes, edges };
    },
    [
      token,
      createNodeLabel,
      createDeviceLabel,
      displaySettings,
      nodeHasChildren,
      expandedNodes,
      calculateStablePositions,
      graph,
    ]
  );

  // Note: saveLayoutChanges function removed - now using auto-save

  // Handle node drag start - now allows free movement
  const handleNodeDragStart = useCallback((e) => {
    // Allow free movement - no restrictions
  }, []);

  // Handle node drag end - store positions for manual save
  const handleNodeDragEnd = useCallback(async (e) => {
    const { item } = e;
    const model = item.getModel();
    const position = { x: model.x, y: model.y };

    // Store position changes - will be saved when user clicks Save button
    // For now, just log the position change
    console.log(`Node ${model.id} moved to position:`, position);
  }, []);

  // Initialize graph
  useEffect(() => {
    if (!containerRef.current || graph) return;

    const container = containerRef.current;
    const width = container.clientWidth;
    const height = container.clientHeight || 500;

    const newGraph = new G6.Graph({
      container: container,
      width,
      height,
      plugins: [createToolbar()],
      renderer: "svg",
      linkCenter: true,
      animate: true,
      modes: {
        default: ["zoom-canvas", "drag-canvas", "click-select", "drag-node"],
      },
      defaultNode: {
        type: "image",
        size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
      },
      defaultEdge: {
        type: "custom-circle-running",
      },
      // Remove layout - use fixed positions from node data
      layout: null,
    });

    // Add node tooltip
    newGraph.on("node:mouseenter", (e) => {
      const { item } = e;
      const model = item.getModel();

      if (model.originalData) {
        const tooltip = document.createElement("div");
        tooltip.id = "custom-topology-tooltip";
        tooltip.style.cssText = `
          position: absolute;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 8px 12px;
          border-radius: 4px;
          font-size: 12px;
          max-width: 300px;
          z-index: 1000;
          pointer-events: none;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        `;

        const deviceData = model.originalData;
        if (deviceData.type === "device") {
          tooltip.innerHTML = `
            <div><strong>Name:</strong> ${deviceData.name}</div>
            <div><strong>Type:</strong> Device</div>
            <div><strong>IP:</strong> ${deviceData.ipAddress || "N/A"}</div>
            <div><strong>MAC:</strong> ${deviceData.macAddress || "N/A"}</div>
          `;
        } else {
          tooltip.innerHTML = `
            <div><strong>Name:</strong> ${deviceData.name}</div>
            <div><strong>Type:</strong> Node</div>
          `;
        }

        document.body.appendChild(tooltip);

        const updateTooltipPosition = (event) => {
          tooltip.style.left = event.clientX + 10 + "px";
          tooltip.style.top = event.clientY - 10 + "px";
        };

        updateTooltipPosition(e.originalEvent || e);
        item._tooltip = tooltip;
        item._tooltipHandler = updateTooltipPosition;
        document.addEventListener("mousemove", updateTooltipPosition);
      }
    });

    newGraph.on("node:mouseleave", (e) => {
      const { item } = e;
      if (item._tooltip) {
        document.body.removeChild(item._tooltip);
        document.removeEventListener("mousemove", item._tooltipHandler);
        item._tooltip = null;
        item._tooltipHandler = null;
      }
    });

    // Add node click handler for expand/collapse
    newGraph.on("node:click", (e) => {
      const { item } = e;
      const model = item.getModel();

      // Only handle clicks on nodes (not devices) that have children
      if (
        model.originalData &&
        model.originalData.type === "node" &&
        model.hasChildren
      ) {
        // Toggle expand/collapse for the clicked node
        handleNodeToggle(model.id);
      }
    });

    // Add drag handlers
    newGraph.on("node:dragstart", handleNodeDragStart);
    newGraph.on("node:dragend", handleNodeDragEnd);

    setGraph(newGraph);

    return () => {
      if (newGraph && !newGraph.destroyed) {
        newGraph.destroy();
      }
    };
  }, []); // Remove dependencies that cause graph recreation

  // No need to change modes dynamically - drag-node is always enabled
  // but we control whether dragging actually saves positions through editMode

  // Handle window resize
  useEffect(() => {
    if (!graph) return;

    const handleResize = () => {
      if (!containerRef.current) return;
      const width = containerRef.current.clientWidth;
      const height = containerRef.current.clientHeight;
      if (width > 0 && height > 0) {
        graph.changeSize(width, height);

        // Just re-render without updating layout to maintain stable positions
        graph.render();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [graph]);

  // Update graph data when topology data changes
  useEffect(() => {
    if (!graph || graph.destroyed) return;

    // Apply filtering first, then process for G6
    const filteredData = getFilteredTopologyData(topologyData);
    const processedData = processTopologyData(filteredData, lldpConnections);

    try {
      graph.clear();
      graph.data(processedData);

      // Find the central node (the one marked as selected)
      const centralNode = processedData.nodes.find((node) => node.isSelected);

      graph.render();

      if (processedData.nodes.length > 0) {
        setTimeout(() => {
          // Instead of fitView, focus on the selected node to maintain proper centering
          if (centralNode) {
            graph.focusItem(centralNode.id);
          }
          // Refresh edge animations
          graph.getEdges().forEach((edge) => {
            edge.setState("refresh", true);
            edge.setState("refresh", false);
          });
        }, 200);
      }

      setIsRendered(true);
    } catch (error) {
      console.error("Error updating topology graph:", error);
    }
  }, [
    graph,
    topologyData,
    processTopologyData,
    getFilteredTopologyData,
    lldpConnections,
  ]);

  // Handle save layout - save all node positions and expand states
  const handleSaveLayout = useCallback(async () => {
    if (!graph || graph.destroyed || !selectedNode || !topologyData.nodes)
      return;

    try {
      // Get current node positions from the graph
      const nodes = graph.getNodes();
      const nodePositions = new Map();

      nodes.forEach((node) => {
        const model = node.getModel();
        if (model.id && model.x !== undefined && model.y !== undefined) {
          nodePositions.set(model.id, { x: model.x, y: model.y });
        }
      });

      // Save positions and expand states for all nodes
      const savePromises = topologyData.nodes.map(async (nodeData) => {
        if (!nodeData.originalData) return;

        const position = nodePositions.get(nodeData.id);
        const isExpanded = expandedNodes.has(nodeData.id);

        const properties = {
          layout: position ? { x: position.x, y: position.y } : undefined,
          expanded: isExpanded,
        };

        // Only save if there are properties to update
        if (properties.layout || properties.expanded !== undefined) {
          try {
            await updateNodeMutation({
              treeName: "default", // assuming default tree name
              nodeId: nodeData.originalData.id,
              properties,
            }).unwrap();
          } catch (error) {
            console.warn(
              `Failed to save layout for node ${nodeData.id}:`,
              error
            );
          }
        }
      });

      await Promise.all(savePromises);

      message.success("Layout saved successfully!");
      console.log("Layout and expand states saved for all nodes");
    } catch (error) {
      console.error("Failed to save layout:", error);
      message.error("Failed to save layout");
    }
  }, [graph, selectedNode, topologyData, expandedNodes, updateNodeMutation]);

  // Control handlers
  const handleZoomIn = useCallback(() => {
    if (graph) {
      graph.zoomTo(graph.getZoom() * 1.2);
    }
  }, [graph]);

  const handleZoomOut = useCallback(() => {
    if (graph) {
      graph.zoomTo(graph.getZoom() * 0.8);
    }
  }, [graph]);

  const handleFitView = useCallback(() => {
    if (graph) {
      // Find the selected/central node
      const nodes = graph.getNodes();
      const centralNode = nodes.find((node) => node.getModel().isSelected);

      if (centralNode) {
        // Focus on the selected node to maintain proper centering
        graph.focusItem(centralNode.getID());
      } else {
        // Fallback to fitView if no central node found
        graph.fitView();
      }
    }
  }, [graph]);

  const handleDownload = useCallback(() => {
    if (graph) {
      graph.downloadFullImage("custom-topology", "image/png");
    }
  }, [graph]);

  // Handle LLDP discovery and device status refresh
  // Initialize auto-refresh hook
  const {
    enabled: autoEnabled,
    toggle: toggleAuto,
    intervalMs,
    setIntervalMs
  } = useAutoRefresh(() => {
    if (selectedNode) {
      handleDiscoverLLDP();
    }
  }, false, 60000, 'topology');

  // Handle LLDP discovery and device status refresh
  const handleDiscoverLLDP = useCallback(async () => {
    if (!selectedNode) {
      message.error("Please select a topology tree first");
      return;
    }

    try {
      // Discover LLDP connections
      await discoverLLDPMutation("default").unwrap(); // Using "default" as tree name for now

      // Wait 5 seconds for backend to complete discovery and update data

      // Force refresh device inventory data to update status
      // This will trigger a re-fetch of device data which includes current status
      dispatch(api.util.invalidateTags(["devices"]));
      dispatch(api.util.invalidateTags(["customTopology"]));
      dispatch(api.util.invalidateTags(["customTopologyConnections"]));

      // Note: WebSocket will also trigger automatic data updates
    } catch (error) {
      console.error("Failed to refresh topology and device status:", error);
      message.error("Failed to refresh topology and device status");
    }
  }, [discoverLLDPMutation, selectedNode, dispatch]);

  // Note: Auto-trigger logic removed - LLDP discovery now relies on WebSocket events
  // The backend already sends 'custom_topology_lldp_updated' WebSocket messages
  // which are handled by socketControlSlice.js to invalidate cache and update data

  // Backup expand state to localStorage as a fallback
  useEffect(() => {
    if (selectedNode && topologyExpandedNodes.length >= 0) {
      const expandStateKey = `topology_expand_state_${selectedNode.id}`;
      localStorage.setItem(
        expandStateKey,
        JSON.stringify(topologyExpandedNodes)
      );
      console.log(
        `💾 Backed up expand state to localStorage for node ${selectedNode.id}:`,
        topologyExpandedNodes
      );
    }
  }, [selectedNode, topologyExpandedNodes]);

  // Restore expand state from localStorage if backend data is not available
  useEffect(() => {
    if (
      selectedNode &&
      (!topologyData?.nodes || topologyData.nodes.length === 0)
    ) {
      const expandStateKey = `topology_expand_state_${selectedNode.id}`;
      const savedExpandState = localStorage.getItem(expandStateKey);

      if (savedExpandState) {
        try {
          const expandedNodeIds = JSON.parse(savedExpandState);
          console.log(
            `📂 Restoring expand state from localStorage for node ${selectedNode.id}:`,
            expandedNodeIds
          );

          dispatch(resetTopologyExpandState());
          if (expandedNodeIds.length > 0) {
            expandedNodeIds.forEach((nodeId) => {
              dispatch(toggleTopologyNodeExpand(nodeId));
            });
          }
        } catch (error) {
          console.error(
            "Failed to restore expand state from localStorage:",
            error
          );
        }
      }
    }
  }, [selectedNode, topologyData?.nodes, dispatch]);

  return (
    <Card
      title={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div>
            <Text strong>Topology View</Text>
            {selectedNode && (
              <Text
                type="secondary"
                style={{ marginLeft: 16, fontSize: "14px" }}
              >
                ({selectedNode.name} - {topologyData.nodes.length} devices)
              </Text>
            )}
          </div>
          <Space>
            <Tooltip title="Zoom In">
              <Button
                size="small"
                icon={<ZoomInOutlined />}
                onClick={handleZoomIn}
              />
            </Tooltip>
            <Tooltip title="Zoom Out">
              <Button
                size="small"
                icon={<ZoomOutOutlined />}
                onClick={handleZoomOut}
              />
            </Tooltip>
            <Tooltip title="Fit View">
              <Button
                size="small"
                icon={<PicCenterOutlined />}
                onClick={handleFitView}
              />
            </Tooltip>
            <Tooltip title="Download">
              <Button
                size="small"
                icon={<DownloadOutlined />}
                onClick={handleDownload}
              />
            </Tooltip>
            <Tooltip title="Display Settings">
              <Button
                size="small"
                icon={<SettingOutlined />}
                onClick={() => setSettingsModalOpen(true)}
              />
            </Tooltip>
            <Tooltip title={legendVisible ? "Hide Legend" : "Show Legend"}>
              <Button
                size="small"
                icon={<InfoCircleOutlined />}
                onClick={() => setLegendVisible(!legendVisible)}
                type={legendVisible ? "primary" : "default"}
              />
            </Tooltip>
            <Tooltip title={allNodesExpanded ? "Collapse All" : "Expand All"}>
              <Button
                size="small"
                icon={
                  allNodesExpanded ? <CompressOutlined /> : <ExpandOutlined />
                }
                onClick={allNodesExpanded ? handleCollapseAll : handleExpandAll}
              />
            </Tooltip>
            <Tooltip title="Refresh Topology & Device Status">
              <Button
                size="small"
                icon={<SyncOutlined />}
                onClick={handleDiscoverLLDP}
                loading={isDiscoveringLLDP}
                disabled={!selectedNode}
              />
            </Tooltip>
            <Tooltip title="Save Layout">
              <Button
                size="small"
                icon={<SaveOutlined />}
                onClick={handleSaveLayout}
                type="primary"
              />
            </Tooltip>
            <Checkbox checked={autoEnabled} onChange={toggleAuto}>
              Auto
            </Checkbox>
            <InputNumber
              size="small"
              min={5}
              step={5}
              value={Math.round(intervalMs / 1000)}
              onChange={(v) => setIntervalMs((v || 60) * 1000)}
              style={{ width: 90 }}
              addonAfter="s"
            />
          </Space>
        </div>
      }
      styles={{
        body: {
          padding: 8,
          height: "calc(100vh - 145px)",
        },
      }}
    >
      <div
        ref={containerRef}
        style={{
          width: "100%",
          height: "100%",
          border: "1px solid #d9d9d9",
          borderRadius: "6px",
          position: "relative",
          background: token.colorBgContainer,
        }}
      >
        {/* Topology Legend */}
        {legendVisible && selectedNode && topologyData.nodes.length > 0 && (
          <TopologyLegend
            visible={legendVisible}
            onVisibilityChange={setLegendVisible}
            compact={true}
          />
        )}
        {(!selectedNode || topologyData.nodes.length === 0) && (
          <div
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              textAlign: "center",
            }}
          >
            <Empty
              description={
                !selectedNode
                  ? "Select a node from the tree to view its topology"
                  : "No devices found in the selected node"
              }
            />
          </div>
        )}
      </div>

      {/* Display Settings Modal */}
      <TopologyDisplaySettings
        open={settingsModalOpen}
        onClose={() => setSettingsModalOpen(false)}
      />
    </Card>
  );
};

export default TopologyView;
