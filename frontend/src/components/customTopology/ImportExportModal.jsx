import { useState } from "react";
import {
  Modal,
  Button,
  Space,
  message,
  Typography,
  Upload,
  Divider,
  Input
} from "antd";
import {
  DownloadOutlined,
  UploadOutlined,
  FileTextOutlined,
  CopyOutlined
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import {
  customTopologySelector,
  exportTreeData,
  restoreTreeData
} from "../../features/customTopology/customTopologySlice";
import {
  useImportCustomTopologyTreeMutation
} from "../../app/services/customTopologyApi";

const { Text, Paragraph } = Typography;
const { TextArea } = Input;

const ImportExportModal = ({ open, onCancel, currentTreeName = "default" }) => {
  const dispatch = useDispatch();
  const { treeData, expandedKeys, selectedKeys, topologyExpandedNodes } = useSelector(customTopologySelector);
  const [activeTab, setActiveTab] = useState("export");
  const [importData, setImportData] = useState("");
  const [importLoading, setImportLoading] = useState(false);
  const [importTreeMutation] = useImportCustomTopologyTreeMutation();

  // Handle export to file
  const handleExportToFile = () => {
    try {
      const jsonData = exportTreeData(treeData, expandedKeys, selectedKeys, topologyExpandedNodes);

      if (!jsonData || jsonData === "{}") {
        message.error("No data to export");
        return;
      }

      // Create download filename
      const filename = `topology-tree-${new Date().toISOString().split('T')[0]}.json`;

      // Method 1: Traditional approach
      const blob = new Blob([jsonData], { type: "application/json" });
      const url = URL.createObjectURL(blob);

      // Create temporary link element
      const link = document.createElement("a");
      link.style.display = "none";
      link.href = url;
      link.download = filename;

      // Add to document, click, and remove
      document.body.appendChild(link);
      link.click();

      // Clean up after a short delay
      setTimeout(() => {
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }, 100);

      message.success("Tree data exported successfully");
    } catch (error) {
      console.error("Export error:", error);
      message.error("Failed to export tree data: " + error.message);

      // Fallback: try alternative download method
      try {
        const jsonData = exportTreeData(treeData, expandedKeys, selectedKeys, topologyExpandedNodes);
        const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(jsonData);
        const downloadAnchor = document.createElement('a');
        downloadAnchor.setAttribute("href", dataStr);
        downloadAnchor.setAttribute("download", `topology-tree-${new Date().toISOString().split('T')[0]}.json`);
        downloadAnchor.style.display = "none";
        document.body.appendChild(downloadAnchor);
        downloadAnchor.click();
        document.body.removeChild(downloadAnchor);
        message.success("Tree data exported successfully (fallback method)");
      } catch (fallbackError) {
        console.error("Fallback export error:", fallbackError);
        message.error("Failed to export tree data with fallback method");
      }
    }
  };

  // Handle copy to clipboard
  const handleCopyToClipboard = async () => {
    try {
      const jsonData = exportTreeData(treeData, expandedKeys, selectedKeys, topologyExpandedNodes);
      await navigator.clipboard.writeText(jsonData);
      message.success("Tree data copied to clipboard");
    } catch (error) {
      message.error("Failed to copy to clipboard");
    }
  };

  // Handle file upload
  const handleFileUpload = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target.result;
        setImportData(content);
        message.success("File loaded successfully");
      } catch (error) {
        message.error("Failed to read file");
      }
    };
    reader.readAsText(file);
    return false; // Prevent default upload behavior
  };

  // Handle import from text area
  const handleImportData = async () => {
    if (!importData.trim()) {
      message.error("Please provide data to import");
      return;
    }

    setImportLoading(true);
    try {
      // First validate JSON
      const parsedData = JSON.parse(importData);
      if (!parsedData.treeData) {
        throw new Error("Invalid data format - missing treeData");
      }

      // Import via backend API to sync with server
      await importTreeMutation({
        treeName: currentTreeName,
        jsonData: importData
      }).unwrap();

      // Also update local Redux state for immediate UI update
      dispatch(restoreTreeData(importData));

      message.success("Tree data imported and synced with backend successfully");
      setImportData("");
      onCancel();
    } catch (error) {
      console.error("Import failed:", error);
      if (error.data?.error) {
        message.error("Failed to import data: " + error.data.error);
      } else if (error.message) {
        message.error("Failed to import data: " + error.message);
      } else {
        message.error("Failed to import data: Unknown error");
      }
    } finally {
      setImportLoading(false);
    }
  };

  // Handle paste from clipboard
  const handlePasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setImportData(text);
      message.success("Data pasted from clipboard");
    } catch (error) {
      message.error("Failed to paste from clipboard");
    }
  };

  const exportContent = (
    <div>
      <Text>Export your current tree structure to save or share it.</Text>
      <Divider />
      <Space direction="vertical" style={{ width: "100%" }} size="large">
        <div>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleExportToFile();
            }}
            size="large"
            block
          >
            Download as JSON File
          </Button>
          <Text type="secondary" style={{ fontSize: "12px", marginTop: "8px", display: "block" }}>
            Downloads a JSON file with your tree structure
          </Text>
        </div>

        <div>
          <Button
            icon={<CopyOutlined />}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleCopyToClipboard();
            }}
            size="large"
            block
          >
            Copy to Clipboard
          </Button>
          <Text type="secondary" style={{ fontSize: "12px", marginTop: "8px", display: "block" }}>
            Copies the JSON data to your clipboard
          </Text>
        </div>
      </Space>

      <Divider />
      <div>
        <Text strong>Current Tree Summary:</Text>
        <ul style={{ marginTop: "8px", fontSize: "14px" }}>
          <li>Total Nodes: {treeData.length}</li>
          <li>Expanded Keys: {expandedKeys.length}</li>
          <li>Selected Keys: {selectedKeys.length}</li>
          <li>Topology Expanded: {topologyExpandedNodes.length}</li>
          <li>Created: {new Date().toLocaleDateString()}</li>
        </ul>
      </div>
    </div>
  );

  const importContent = (
    <div>
      <Text>Import tree structure from a previously exported file or JSON data.</Text>
      <Divider />
      <Space direction="vertical" style={{ width: "100%" }} size="large">
        <div>
          <Upload
            accept=".json"
            beforeUpload={handleFileUpload}
            showUploadList={false}
          >
            <Button icon={<UploadOutlined />} size="large" block>
              Upload JSON File
            </Button>
          </Upload>
          <Text type="secondary" style={{ fontSize: "12px", marginTop: "8px", display: "block" }}>
            Select a previously exported JSON file
          </Text>
        </div>

        <div>
          <Button
            icon={<CopyOutlined />}
            onClick={handlePasteFromClipboard}
            size="large"
            block
          >
            Paste from Clipboard
          </Button>
          <Text type="secondary" style={{ fontSize: "12px", marginTop: "8px", display: "block" }}>
            Paste JSON data from your clipboard
          </Text>
        </div>

        <div>
          <Text strong>Or paste JSON data directly:</Text>
          <TextArea
            rows={8}
            placeholder="Paste your JSON tree data here..."
            value={importData}
            onChange={(e) => setImportData(e.target.value)}
            style={{ marginTop: "8px" }}
          />
        </div>

        {importData && (
          <Button
            type="primary"
            onClick={handleImportData}
            size="large"
            block
            loading={importLoading}
          >
            Import Data
          </Button>
        )}
      </Space>

      <Divider />
      <div style={{ backgroundColor: "#fff7e6", padding: "12px", borderRadius: "6px" }}>
        <Text type="warning" strong>⚠️ Warning:</Text>
        <Paragraph style={{ margin: "8px 0 0 0", fontSize: "12px" }}>
          Importing will replace your current tree structure. Make sure to export your current data first if you want to keep it.
        </Paragraph>
      </div>
    </div>
  );

  return (
    <Modal
      title={
        <div style={{ display: "flex", alignItems: "center" }}>
          <FileTextOutlined style={{ marginRight: "8px" }} />
          Import / Export Tree Data
        </div>
      }
      open={open}
      onCancel={onCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <div style={{ marginBottom: "20px" }}>
        <Space>
          <Button
            type={activeTab === "export" ? "primary" : "default"}
            onClick={() => setActiveTab("export")}
          >
            Export
          </Button>
          <Button
            type={activeTab === "import" ? "primary" : "default"}
            onClick={() => setActiveTab("import")}
          >
            Import
          </Button>
        </Space>
      </div>

      {activeTab === "export" ? exportContent : importContent}
    </Modal>
  );
};

export default ImportExportModal;