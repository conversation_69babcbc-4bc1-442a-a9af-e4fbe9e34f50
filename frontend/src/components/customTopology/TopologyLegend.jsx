import React, { useState } from "react";
import {
  Card,
  Collapse,
  Typography,
  <PERSON>,
  Row,
  Col,
  Divider,
  Tag,
  But<PERSON>,
  Tooltip
} from "antd";
import {
  InfoCircleOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from "@ant-design/icons";

const { Text, Title } = Typography;
const { Panel } = Collapse;

const TopologyLegend = ({ visible = true, onVisibilityChange, compact = false }) => {
  const [isVisible, setIsVisible] = useState(visible);

  const handleToggleVisibility = () => {
    const newVisibility = !isVisible;
    setIsVisible(newVisibility);
    if (onVisibilityChange) {
      onVisibilityChange(newVisibility);
    }
  };

  // Legend item component
  const LegendItem = ({ color, label, description, pattern = "solid", thickness = 2, emoji }) => (
    <Row align="middle" gutter={8} style={{ marginBottom: 8 }}>
      <Col span={4}>
        <div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
          {pattern === "dashed" ? (
            <svg width="30" height="3" style={{ overflow: "visible" }}>
              <line
                x1="0" y1="1.5" x2="30" y2="1.5"
                stroke={color}
                strokeWidth={thickness}
                strokeDasharray="4,4"
                opacity="0.8"
              />
            </svg>
          ) : (
            <div
              style={{
                width: "30px",
                height: `${thickness}px`,
                backgroundColor: color,
                borderRadius: "1px"
              }}
            />
          )}
        </div>
      </Col>
      <Col span={20}>
        <Space>
          {emoji && <span style={{ fontSize: "14px" }}>{emoji}</span>}
          <Text strong>{label}</Text>
          {description && (
            <Text type="secondary" style={{ fontSize: "12px" }}>
              - {description}
            </Text>
          )}
        </Space>
      </Col>
    </Row>
  );

  // Device status item component
  const DeviceStatusItem = ({ color, status, emoji, description }) => (
    <Row align="middle" gutter={8} style={{ marginBottom: 8 }}>
      <Col span={4}>
        <div style={{ display: "flex", alignItems: "center", justifyContent: "center" }}>
          <div
            style={{
              width: "20px",
              height: "20px",
              border: `2px solid ${color}`,
              borderRadius: "4px",
              backgroundColor: "rgba(255, 255, 255, 0.1)"
            }}
          />
        </div>
      </Col>
      <Col span={20}>
        <Space>
          <span style={{ fontSize: "14px" }}>{emoji}</span>
          <Text strong>{status}</Text>
          <Text type="secondary" style={{ fontSize: "12px" }}>
            - {description}
          </Text>
        </Space>
      </Col>
    </Row>
  );

  if (!isVisible && compact) {
    return (
      <Tooltip title="Show Topology Legend">
        <Button
          size="small"
          icon={<InfoCircleOutlined />}
          onClick={handleToggleVisibility}
          style={{ position: "absolute", top: 10, right: 10, zIndex: 1000 }}
        />
      </Tooltip>
    );
  }

  return (
    <Card
      size="small"
      title={
        <Space>
          <InfoCircleOutlined />
          <span>Topology Legend</span>
          {compact && (
            <Tooltip title="Hide Legend">
              <Button
                size="small"
                type="text"
                icon={<EyeInvisibleOutlined />}
                onClick={handleToggleVisibility}
              />
            </Tooltip>
          )}
        </Space>
      }
      style={{
        width: compact ? 280 : 320,
        position: compact ? "absolute" : "relative",
        top: compact ? 10 : 0,
        right: compact ? 10 : 0,
        zIndex: 1000,
        maxHeight: compact ? "80vh" : "none",
        overflow: compact ? "auto" : "visible"
      }}
      styles={{
        body: {
          padding: "12px 16px",
          fontSize: "12px"
        }
      }}
    >
      <Collapse
        ghost
        size="small"
        defaultActiveKey={compact ? [] : ["connections", "devices", "types"]}
        items={[
          {
            key: "connections",
            label: (
              <Text strong style={{ fontSize: "13px" }}>
                Connection Status
              </Text>
            ),
            children: (
              <div>
                <LegendItem
                  color="#52c41a"
                  label="Active (Agent)"
                  description="Agent discovered connection"
                  thickness={2}
                  emoji="🟢"
                />
                <LegendItem
                  color="#1890ff"
                  label="Active (SNMP)"
                  description="SNMP discovered connection"
                  thickness={2}
                  emoji="🔵"
                />
                <LegendItem
                  color="#fa8c16"
                  label="Active (Manual)"
                  description="Manually configured connection"
                  thickness={2}
                  emoji="🟠"
                />
                <LegendItem
                  color="#722ed1"
                  label="Blocked Port"
                  description="STP/RSTP blocked port"
                  pattern="dashed"
                  thickness={3}
                  emoji="🚫"
                />
                <LegendItem
                  color="#ff4d4f"
                  label="Connection Lost"
                  description="Device offline or unreachable"
                  pattern="dashed"
                  thickness={3}
                  emoji="🔴"
                />
                <LegendItem
                  color="#8c8c8c"
                  label="Hierarchical"
                  description="Parent-child relationship"
                  pattern="dashed"
                  thickness={2}
                  emoji="🔗"
                />
              </div>
            )
          },
          {
            key: "devices",
            label: (
              <Text strong style={{ fontSize: "13px" }}>
                Device Status
              </Text>
            ),
            children: (
              <div>
                <DeviceStatusItem
                  color="#52c41a"
                  status="Online"
                  emoji="🟢"
                  description="Device is responding"
                />
                <DeviceStatusItem
                  color="#ff4d4f"
                  status="Offline"
                  emoji="🔴"
                  description="Device not responding"
                />
                <DeviceStatusItem
                  color="#8c8c8c"
                  status="Unknown"
                  emoji="🟡"
                  description="Status unavailable"
                />
              </div>
            )
          },
          {
            key: "types",
            label: (
              <Text strong style={{ fontSize: "13px" }}>
                Connection Types
              </Text>
            ),
            children: (
              <div>
                <Space direction="vertical" size="small" style={{ width: "100%" }}>
                  <Row>
                    <Col span={24}>
                      <Tag color="blue" style={{ fontSize: "11px" }}>LLDP Discovered</Tag>
                      <Text type="secondary" style={{ fontSize: "11px", marginLeft: 8 }}>
                        Auto-discovered via LLDP protocol
                      </Text>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={24}>
                      <Tag color="green" style={{ fontSize: "11px" }}>Physical Connection</Tag>
                      <Text type="secondary" style={{ fontSize: "11px", marginLeft: 8 }}>
                        Direct device-to-device link
                      </Text>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={24}>
                      <Tag color="orange" style={{ fontSize: "11px" }}>Logical Connection</Tag>
                      <Text type="secondary" style={{ fontSize: "11px", marginLeft: 8 }}>
                        Network topology relationship
                      </Text>
                    </Col>
                  </Row>
                </Space>
              </div>
            )
          }
        ]}
      />

      <Divider style={{ margin: "12px 0" }} />

      <div style={{ textAlign: "center" }}>
        <Text type="secondary" style={{ fontSize: "11px" }}>
          Lines with animation indicate active data flow
        </Text>
      </div>
    </Card>
  );
};

export default TopologyLegend;