import React, { useState, useCallback } from "react";
import {
  Tree,
  Button,
  Input,
  Modal,
  Form,
  Select,
  Dropdown,
  Tooltip,
  Space,
  Typography,
  Spin,
  App,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  FolderOutlined,
  LaptopOutlined,
  MoreOutlined,
  ImportOutlined,
  ExpandOutlined,
  CompressOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import {
  customTopologySelector,
  addNode,
  removeNode,
  renameNode,
  updateDevice,
  selectNode,
  setExpandedKeys,
  setSelectedKeys,
  resetTree,
  setTreeData,
} from "../../features/customTopology/customTopologySlice";
import {
  useGetAllCustomTopologyTreesQuery,
  useGetCustomTopologyTreeQuery,
  useGetAvailableDevicesForCustomTopologyQuery,
  useAddCustomTopologyNodeMutation,
  useUpdateCustomTopologyNodeMutation,
  useDeleteCustomTopologyNodeMutation,
  useCreateCustomTopologyTreeMutation,
  useDeleteCustomTopologyTreeMutation,
} from "../../app/services/customTopologyApi";
import ImportExportModal from "./ImportExportModal";
import DeviceAssignmentModal from "./DeviceAssignmentModal";

const { Text } = Typography;
const { Option } = Select;

const NodeTreeView = () => {
  const dispatch = useDispatch();
  const { message, modal } = App.useApp();
  const { treeData, expandedKeys, selectedKeys, selectedNodeId } = useSelector(
    customTopologySelector
  );

  // API hooks for custom topology integration
  const { data: allTrees = [], isLoading: treesLoading } =
    useGetAllCustomTopologyTreesQuery();
  const [currentTreeName, setCurrentTreeName] = useState("default");

  // Only fetch tree data if we have a valid tree name
  const {
    data: treeApiData,
    isLoading: treeLoading,
    error: treeError,
    refetch: refetchTree,
  } = useGetCustomTopologyTreeQuery(currentTreeName, {});

  const { refetch: refetchDevices } =
    useGetAvailableDevicesForCustomTopologyQuery();
  const [addNodeMutation] = useAddCustomTopologyNodeMutation();
  const [updateNodeMutation] = useUpdateCustomTopologyNodeMutation();
  const [deleteNodeMutation] = useDeleteCustomTopologyNodeMutation();
  const [createTreeMutation] = useCreateCustomTopologyTreeMutation();
  const [deleteTreeMutation] = useDeleteCustomTopologyTreeMutation();

  // Local state for modals and forms
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isDeviceModalVisible, setIsDeviceModalVisible] = useState(false);
  const [isImportExportVisible, setIsImportExportVisible] = useState(false);
  const [isDeviceAssignmentVisible, setIsDeviceAssignmentVisible] =
    useState(false);
  const [currentNode, setCurrentNode] = useState(null);
  const [selectedNodeForDevices, setSelectedNodeForDevices] = useState(null);
  const [form] = Form.useForm();
  const [editForm] = Form.useForm();
  const [deviceForm] = Form.useForm();

  // Handle tree initialization and auto-creation
  React.useEffect(() => {
    const initializeTree = async () => {
      console.log(
        "Tree initialization - allTrees:",
        allTrees,
        "currentTreeName:",
        currentTreeName,
        "treesLoading:",
        treesLoading
      );

      // If we have trees, use the first one or keep current selection
      if (allTrees.length > 0) {
        const treeNames = allTrees.map((tree) => tree.name);
        console.log("Available tree names:", treeNames);
        if (!treeNames.includes(currentTreeName)) {
          console.log("Setting tree name to:", treeNames[0]);
          setCurrentTreeName(treeNames[0]);
        }
        return;
      }

      // If no trees exist and we're not loading, create a default tree
      if (!treesLoading && allTrees.length === 0 && !treeError) {
        console.log("Creating default tree:", currentTreeName);
        try {
          await createTreeMutation({
            treeName: currentTreeName,
            description: "Default custom topology tree",
          }).unwrap();
          // Tree will be refetched automatically due to invalidatesTags
        } catch (error) {
          console.error("Failed to create default tree:", error);
          message.error("Failed to create default topology tree");
        }
      }
    };

    initializeTree();
  }, [allTrees, treesLoading, treeError, currentTreeName, createTreeMutation]);

  // Load custom topology tree data and sync with Redux
  React.useEffect(() => {
    if (treeApiData) {
      // Update Redux state with API data
      dispatch(setTreeData(treeApiData.nodes));
    }
  }, [treeApiData, dispatch]);

  // Convert tree data to Ant Design Tree format
  const convertToAntTreeData = useCallback((nodes) => {
    if (!nodes || !Array.isArray(nodes)) return [];

    return nodes
      .filter((node) => node !== null && node !== undefined)
      .map((node) => ({
        title: (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              minWidth: 0,
            }}
            onContextMenu={(e) => handleContextMenu(e, node)}
          >
            <div style={{ display: "flex", alignItems: "center", minWidth: 0 }}>
              {node?.type === "device" ? (
                <LaptopOutlined style={{ marginRight: 8, color: "#1890ff" }} />
              ) : (
                <FolderOutlined style={{ marginRight: 8, color: "#faad14" }} />
              )}
              <Text
                ellipsis={{ tooltip: node?.name || "Unnamed" }}
                style={{ flex: 1, minWidth: 0 }}
              >
                {node?.name || "Unnamed"}
              </Text>
            </div>
            <Dropdown
              menu={{
                items: getContextMenuItems(node),
                onClick: ({ key }) => handleMenuClick(key, node),
              }}
              trigger={["click"]}
              onClick={(e) => e.stopPropagation()}
            >
              <Button
                type="text"
                size="small"
                icon={<MoreOutlined />}
                style={{ marginLeft: 4 }}
                onClick={(e) => e.stopPropagation()}
              />
            </Dropdown>
          </div>
        ),
        key: node?.id || `unknown-${Math.random()}`,
        children: node?.children
          ? convertToAntTreeData(node.children)
          : undefined,
        isLeaf: !node?.children || node.children.length === 0,
        nodeData: node,
      }));
  }, []);

  // Get context menu items based on node type
  const getContextMenuItems = useCallback((node) => {
    if (!node) return [];

    const items = [
      {
        key: "edit",
        label: "Rename",
        icon: <EditOutlined />,
      },
    ];

    if (node?.type === "device") {
      items.push({
        key: "editDevice",
        label: "Edit Device",
        icon: <LaptopOutlined />,
      });
    } else {
      items.push(
        {
          key: "addNode",
          label: "Add Sub-node",
          icon: <FolderOutlined />,
        },
        {
          key: "addDevice",
          label: "Add Device",
          icon: <LaptopOutlined />,
        },
        {
          key: "assignDevices",
          label: "Assign Real Devices",
          icon: <LaptopOutlined />,
        }
      );
    }

    items.push({
      key: "delete",
      label: "Delete",
      icon: <DeleteOutlined />,
      danger: true,
    });

    return items;
  }, []);

  // Handle context menu
  const handleContextMenu = useCallback((e) => {
    e.preventDefault();
    // Context menu functionality can be added here if needed
  }, []);

  // Handle menu clicks
  const handleMenuClick = useCallback(
    (key, node) => {
      switch (key) {
        case "addNode":
          setCurrentNode(node);
          setIsAddModalVisible(true);
          form.setFieldsValue({ nodeType: "node", name: "" });
          break;
        case "addDevice":
          setCurrentNode(node);
          setIsAddModalVisible(true);
          form.setFieldsValue({ nodeType: "device", name: "" });
          break;
        case "edit":
          setCurrentNode(node);
          setIsEditModalVisible(true);
          editForm.setFieldsValue({ name: node.name });
          break;
        case "editDevice":
          if (node?.type === "device") {
            setCurrentNode(node);
            setIsDeviceModalVisible(true);
            deviceForm.setFieldsValue({
              name: node.name,
              ipAddress: node.ipAddress || "",
              macAddress: node.macAddress || "",
            });
          }
          break;
        case "assignDevices":
          setSelectedNodeForDevices(node);
          setIsDeviceAssignmentVisible(true);
          break;
        case "delete":
          modal.confirm({
            title: "Confirm Delete",
            content: `Are you sure you want to delete "${node.name}"? This action cannot be undone.`,
            okText: "Delete",
            okType: "danger",
            cancelText: "Cancel",
            onOk: async () => {
              try {
                // Delete node via API
                await deleteNodeMutation({
                  treeName: currentTreeName,
                  nodeId: node.id,
                }).unwrap();

                message.success(`"${node.name}" has been deleted`);

                // Refetch tree data - this will automatically update topology via setTreeData
                await refetchTree();

                // Clear tree selection if the deleted node was selected in the tree
                if (selectedKeys.includes(node.id)) {
                  dispatch(setSelectedKeys([]));
                }

                // Only clear topology selection if the deleted node was the selected topology node
                // Otherwise, let setTreeData auto-refresh the topology with updated data
                if (node.id === selectedNodeId) {
                  dispatch(selectNode(null));
                }
              } catch (error) {
                console.error("Failed to delete node:", error);
                message.error(
                  `Failed to delete node: ${
                    error.data?.error || error.message || "Unknown error"
                  }`
                );
              }
            },
          });
          break;
        default:
          break;
      }
    },
    [
      form,
      editForm,
      deviceForm,
      dispatch,
      deleteNodeMutation,
      currentTreeName,
      refetchTree,
      selectedKeys,
      setSelectedKeys,
      selectNode,
      selectedNodeId,
    ]
  );

  // Handle tree selection
  const handleSelect = useCallback(
    (selectedKeys) => {
      dispatch(setSelectedKeys(selectedKeys));
      if (selectedKeys.length > 0) {
        dispatch(selectNode(selectedKeys[0]));
      }
    },
    [dispatch]
  );

  // Handle tree expansion
  const handleExpand = useCallback(
    (expandedKeys) => {
      dispatch(setExpandedKeys(expandedKeys));
    },
    [dispatch]
  );

  // Handle add node form submission
  const handleAddNode = useCallback(
    async (values) => {
      try {
        await form.validateFields();

        console.log("Adding node with params:", {
          treeName: currentTreeName,
          parentId: currentNode?.id || "",
          nodeType: values.nodeType,
          name: values.name,
        });

        // Add node via API
        await addNodeMutation({
          treeName: currentTreeName,
          parentId: currentNode?.id || "",
          nodeType: values.nodeType,
          name: values.name,
        }).unwrap();

        message.success(
          `${
            values.nodeType === "device" ? "Device" : "Node"
          } added successfully`
        );
        setIsAddModalVisible(false);
        form.resetFields();
        setCurrentNode(null);

        // Refetch tree data - this will automatically update topology via setTreeData
        refetchTree();
      } catch (error) {
        console.error("Failed to add node:", error);
        message.error(
          `Failed to add ${values.nodeType}: ${
            error.data?.error || error.message || "Unknown error"
          }`
        );
      }
    },
    [form, dispatch, currentNode, addNodeMutation, refetchTree, currentTreeName]
  );

  // Handle rename form submission
  const handleRename = useCallback(
    async (values) => {
      try {
        await editForm.validateFields();

        // Update node via API
        await updateNodeMutation({
          treeName: currentTreeName,
          nodeId: currentNode.id,
          name: values.name,
        }).unwrap();

        message.success("Node renamed successfully");
        setIsEditModalVisible(false);
        editForm.resetFields();
        setCurrentNode(null);

        // Refetch tree data - this will automatically update topology via setTreeData
        refetchTree();
      } catch (error) {
        console.error("Failed to rename node:", error);
        message.error(
          `Failed to rename node: ${
            error.data?.error || error.message || "Unknown error"
          }`
        );
      }
    },
    [editForm, dispatch, currentNode, updateNodeMutation, refetchTree]
  );

  // Handle device update form submission
  const handleUpdateDevice = useCallback(
    async (values) => {
      try {
        await deviceForm.validateFields();
        dispatch(
          updateDevice({
            deviceId: currentNode.id,
            properties: {
              name: values.name,
              ipAddress: values.ipAddress,
              macAddress: values.macAddress,
            },
          })
        );
        message.success("Device updated successfully");
        setIsDeviceModalVisible(false);
        deviceForm.resetFields();
        setCurrentNode(null);
      } catch (error) {
        console.error("Validation failed:", error);
      }
    },
    [deviceForm, dispatch, currentNode]
  );

  // Handle adding root node
  const handleAddRootNode = useCallback(() => {
    setCurrentNode(null);
    setIsAddModalVisible(true);
    form.setFieldsValue({ nodeType: "node", name: "" });
  }, [form]);

  // Handle device assignment updates
  const handleDevicesUpdated = useCallback(async () => {
    // Refetch both devices and tree data
    await refetchDevices();
    await refetchTree();

    // Refresh the topology view
    if (selectedNodeForDevices) {
      dispatch(selectNode(selectedNodeForDevices.id));
    }
  }, [refetchDevices, refetchTree, selectedNodeForDevices, dispatch]);

  // Get all node keys for expand/collapse all
  const getAllNodeKeys = useCallback((nodes) => {
    const keys = [];
    const traverse = (nodeList) => {
      if (!nodeList || !Array.isArray(nodeList)) return;

      nodeList.forEach((node) => {
        if (node && node.type === "node") {
          keys.push(node.id);
          if (node.children && node.children.length > 0) {
            traverse(node.children);
          }
        }
      });
    };
    traverse(nodes);
    return keys;
  }, []);

  // Handle expand all
  const handleExpandAll = useCallback(() => {
    const allKeys = getAllNodeKeys(treeData);
    dispatch(setExpandedKeys(allKeys));
  }, [treeData, getAllNodeKeys, dispatch]);

  // Handle collapse all
  const handleCollapseAll = useCallback(() => {
    dispatch(setExpandedKeys([]));
  }, [dispatch]);

  return (
    <div
      style={{
        height: "calc(100vh - 165px)",
        border: "1px solid #d9d9d9",
        borderRadius: "6px",
        padding: "12px",
        background: "transparent",
      }}
    >
      {/* Header with actions */}
      <div
        style={{
          marginBottom: 16,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Text strong>Tree Structure</Text>
        <Space>
          <Tooltip title="Add Root Node">
            <Button
              type="primary"
              size="small"
              icon={<PlusOutlined />}
              onClick={handleAddRootNode}
            />
          </Tooltip>
          <Tooltip title="Import/Export Data">
            <Button
              size="small"
              icon={<ImportOutlined />}
              onClick={() => setIsImportExportVisible(true)}
            />
          </Tooltip>
          <Tooltip title="Expand All">
            <Button
              size="small"
              icon={<ExpandOutlined />}
              onClick={handleExpandAll}
            />
          </Tooltip>
          <Tooltip title="Collapse All">
            <Button
              size="small"
              icon={<CompressOutlined />}
              onClick={handleCollapseAll}
            />
          </Tooltip>
        </Space>
      </div>

      {/* Tree component */}
      {treesLoading || treeLoading ? (
        <div style={{ textAlign: "center", padding: "50px" }}>
          <Spin size="large" />
          <div style={{ marginTop: "16px" }}>
            {treesLoading ? "Loading topology data..." : "Loading tree data..."}
          </div>
        </div>
      ) : treeData.length === 0 ? (
        <div style={{ textAlign: "center", padding: "50px", color: "#999" }}>
          <FolderOutlined style={{ fontSize: "48px", marginBottom: "16px" }} />
          <div>No topology data found</div>
          <div style={{ marginTop: "8px", fontSize: "12px" }}>
            Click &quot;Add Node&quot; to create your first topology node
          </div>
        </div>
      ) : (
        <Tree
          treeData={convertToAntTreeData(treeData)}
          onSelect={handleSelect}
          onExpand={handleExpand}
          selectedKeys={selectedKeys}
          expandedKeys={expandedKeys}
          showIcon={false}
          style={{
            fontSize: "14px",
            height: "calc(100vh - 230px)",
            overflow: "auto",
          }}
        />
      )}

      {/* Add Node Modal */}
      <Modal
        title={`Add ${currentNode ? "Child" : "Root"} ${
          form.getFieldValue("nodeType") === "device" ? "Device" : "Node"
        }`}
        open={isAddModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setIsAddModalVisible(false);
          form.resetFields();
          setCurrentNode(null);
        }}
        destroyOnClose
      >
        <Form form={form} layout="vertical" onFinish={handleAddNode}>
          <Form.Item
            name="nodeType"
            label="Type"
            rules={[{ required: true, message: "Please select a type" }]}
          >
            <Select placeholder="Select type">
              <Option value="node">Node</Option>
              <Option value="device">Device</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="name"
            label="Name"
            rules={[
              { required: true, message: "Please enter a name" },
              {
                min: 1,
                max: 50,
                message: "Name must be between 1 and 50 characters",
              },
            ]}
          >
            <Input placeholder="Enter name" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Rename Modal */}
      <Modal
        title="Rename Node"
        open={isEditModalVisible}
        onOk={() => editForm.submit()}
        onCancel={() => {
          setIsEditModalVisible(false);
          editForm.resetFields();
          setCurrentNode(null);
        }}
        destroyOnClose
      >
        <Form form={editForm} layout="vertical" onFinish={handleRename}>
          <Form.Item
            name="name"
            label="Name"
            rules={[
              { required: true, message: "Please enter a name" },
              {
                min: 1,
                max: 50,
                message: "Name must be between 1 and 50 characters",
              },
            ]}
          >
            <Input placeholder="Enter new name" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Device Modal */}
      <Modal
        title="Edit Device"
        open={isDeviceModalVisible}
        onOk={() => deviceForm.submit()}
        onCancel={() => {
          setIsDeviceModalVisible(false);
          deviceForm.resetFields();
          setCurrentNode(null);
        }}
        destroyOnClose
        width={500}
      >
        <Form form={deviceForm} layout="vertical" onFinish={handleUpdateDevice}>
          <Form.Item
            name="name"
            label="Device Name"
            rules={[
              { required: true, message: "Please enter a device name" },
              {
                min: 1,
                max: 50,
                message: "Name must be between 1 and 50 characters",
              },
            ]}
          >
            <Input placeholder="Enter device name" />
          </Form.Item>
          <Form.Item
            name="ipAddress"
            label="IP Address"
            rules={[
              { required: true, message: "Please enter an IP address" },
              {
                pattern:
                  /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
                message: "Please enter a valid IP address",
              },
            ]}
          >
            <Input placeholder="***********" />
          </Form.Item>
          <Form.Item
            name="macAddress"
            label="MAC Address"
            rules={[
              { required: true, message: "Please enter a MAC address" },
              {
                pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
                message:
                  "Please enter a valid MAC address (e.g., 00:11:22:33:44:55)",
              },
            ]}
          >
            <Input placeholder="00:11:22:33:44:55" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Import/Export Modal */}
      <ImportExportModal
        open={isImportExportVisible}
        onCancel={() => setIsImportExportVisible(false)}
        currentTreeName={currentTreeName}
      />

      {/* Device Assignment Modal */}
      <DeviceAssignmentModal
        open={isDeviceAssignmentVisible}
        onClose={() => {
          setIsDeviceAssignmentVisible(false);
          setSelectedNodeForDevices(null);
        }}
        selectedNode={selectedNodeForDevices}
        onDevicesUpdated={handleDevicesUpdated}
        treeName={currentTreeName}
      />
    </div>
  );
};

export default NodeTreeView;
