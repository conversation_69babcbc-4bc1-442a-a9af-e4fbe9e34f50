import React, { useState } from "react";
import {
  Mo<PERSON>,
  Checkbox,
  Button,
  Space,
  Divider,
  Typography,
  Row,
  Col,
  Select,
  Card,
  Tooltip
} from "antd";
import { useSelector, useDispatch } from "react-redux";
import {
  customTopologySelector,
  updateDisplaySettings,
  resetDisplaySettings,
  displayPresets
} from "../../features/customTopology/customTopologySlice";
import { SettingOutlined, EyeOutlined, ReloadOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;
const { Option } = Select;

const TopologyDisplaySettings = ({ open, onClose }) => {
  const dispatch = useDispatch();
  const { displaySettings } = useSelector(customTopologySelector);
  const [tempSettings, setTempSettings] = useState(displaySettings);

  // Update local state when modal opens
  React.useEffect(() => {
    if (open) {
      setTempSettings(displaySettings);
    }
  }, [open, displaySettings]);

  const handleSettingChange = (key, checked) => {
    setTempSettings(prev => ({
      ...prev,
      [key]: checked
    }));
  };

  const handlePresetSelect = (presetKey) => {
    if (presetKey && displayPresets[presetKey]) {
      setTempSettings(displayPresets[presetKey]);
    }
  };

  const handleApply = () => {
    dispatch(updateDisplaySettings(tempSettings));
    onClose();
  };

  const handleReset = () => {
    dispatch(resetDisplaySettings());
    setTempSettings(displayPresets.standard);
  };

  const handleCancel = () => {
    setTempSettings(displaySettings); // Reset to current settings
    onClose();
  };

  // Generate preview text based on current temp settings
  const generatePreviewText = () => {
    const parts = [];
    if (tempSettings.deviceName) parts.push("Router-01");
    if (tempSettings.ipAddress) parts.push("***********");
    if (tempSettings.macAddress) parts.push("00:11:22:33:44:55");
    if (tempSettings.deviceModel) parts.push("Cisco 2960");
    if (tempSettings.status) parts.push("Online");
    if (tempSettings.portInfo) parts.push("eth0");

    return parts.length > 0 ? parts.join("\n") : "No information selected";
  };

  // Generate connection preview text
  const generateConnectionPreview = () => {
    const parts = [];
    if (tempSettings.connectionLabels) {
      if (tempSettings.connectionPorts) {
        parts.push("eth0 ↔ eth1");
      } else {
        parts.push("Connection");
      }
      if (tempSettings.connectionStatus) {
        parts.push("[ACTIVE]");
      }
    }
    return parts.join(" ");
  };

  const deviceSettingsOptions = [
    {
      key: "deviceName",
      label: "Device Name",
      description: "Show the name of each device",
      icon: "🏷️"
    },
    {
      key: "ipAddress",
      label: "IP Address",
      description: "Display device IP addresses",
      icon: "🌐"
    },
    {
      key: "macAddress",
      label: "MAC Address",
      description: "Show MAC addresses for devices",
      icon: "🔧"
    },
    {
      key: "deviceModel",
      label: "Device Model",
      description: "Display device model information",
      icon: "📱"
    },
    {
      key: "status",
      label: "Status",
      description: "Show online/offline status",
      icon: "🟢"
    },
    {
      key: "portInfo",
      label: "Port Information",
      description: "Display connection port details",
      icon: "🔌"
    }
  ];

  const connectionSettingsOptions = [
    {
      key: "connectionLabels",
      label: "Connection Labels",
      description: "Show labels on connection lines",
      icon: "🏷️"
    },
    {
      key: "connectionPorts",
      label: "Port Numbers",
      description: "Display port numbers in connection labels",
      icon: "🔌"
    },
    {
      key: "connectionStatus",
      label: "Connection Status",
      description: "Show active/inactive status in labels",
      icon: "📡"
    },
    {
      key: "inactiveConnections",
      label: "Show Inactive Connections",
      description: "Display offline/inactive connections",
      icon: "❌"
    },
    {
      key: "hierarchicalConnections",
      label: "Hierarchical Connections",
      description: "Show parent-child relationships",
      icon: "🌳"
    },
    {
      key: "lldpConnections",
      label: "LLDP Connections",
      description: "Display discovered LLDP connections",
      icon: "🔗"
    }
  ];

  const presetOptions = [
    { key: "minimal", label: "Minimal", description: "Name only" },
    { key: "standard", label: "Standard", description: "Name + IP" },
    { key: "detailed", label: "Detailed", description: "Name + IP + MAC" },
    { key: "security", label: "Security", description: "Name + MAC + Status" },
    { key: "admin", label: "Admin", description: "Name + IP + Model + Status" },
    { key: "full", label: "Full", description: "All information" }
  ];

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          <span>Topology Display Settings</span>
        </Space>
      }
      open={open}
      onCancel={handleCancel}
      width={700}
      footer={[
        <Button key="reset" onClick={handleReset} icon={<ReloadOutlined />}>
          Reset to Default
        </Button>,
        <Button key="cancel" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button key="apply" type="primary" onClick={handleApply}>
          Apply Settings
        </Button>
      ]}
    >
      <Row gutter={[24, 16]}>
        {/* Settings Controls */}
        <Col span={14}>
          <Title level={5}>Information to Display</Title>
          <Space direction="vertical" size="middle" style={{ width: "100%" }}>
            {/* Preset Selection */}
            <div>
              <Text strong>Quick Presets:</Text>
              <Select
                placeholder="Choose a preset..."
                style={{ width: "100%", marginTop: 8 }}
                onChange={handlePresetSelect}
                allowClear
              >
                {presetOptions.map(preset => (
                  <Option key={preset.key} value={preset.key}>
                    <Space>
                      <Text strong>{preset.label}</Text>
                      <Text type="secondary">({preset.description})</Text>
                    </Space>
                  </Option>
                ))}
              </Select>
            </div>

            <Divider />

            {/* Device Settings */}
            <div>
              <Text strong>Device Information:</Text>
              <div style={{ marginTop: 12 }}>
                <Row gutter={[0, 8]}>
                  {deviceSettingsOptions.map(option => (
                    <Col span={24} key={option.key}>
                      <Checkbox
                        checked={tempSettings[option.key]}
                        onChange={(e) => handleSettingChange(option.key, e.target.checked)}
                      >
                        <Space>
                          <span>{option.icon}</span>
                          <span>{option.label}</span>
                          <Tooltip title={option.description}>
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                              ℹ️
                            </Text>
                          </Tooltip>
                        </Space>
                      </Checkbox>
                    </Col>
                  ))}
                </Row>
              </div>
            </div>

            <Divider />

            {/* Connection Settings */}
            <div>
              <Text strong>Connection Display:</Text>
              <div style={{ marginTop: 12 }}>
                <Row gutter={[0, 8]}>
                  {connectionSettingsOptions.map(option => (
                    <Col span={24} key={option.key}>
                      <Checkbox
                        checked={tempSettings[option.key]}
                        onChange={(e) => handleSettingChange(option.key, e.target.checked)}
                      >
                        <Space>
                          <span>{option.icon}</span>
                          <span>{option.label}</span>
                          <Tooltip title={option.description}>
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                              ℹ️
                            </Text>
                          </Tooltip>
                        </Space>
                      </Checkbox>
                    </Col>
                  ))}
                </Row>
              </div>
            </div>
          </Space>
        </Col>

        {/* Preview */}
        <Col span={10}>
          <Title level={5}>
            <Space>
              <EyeOutlined />
              <span>Preview</span>
            </Space>
          </Title>
          <Card
            size="small"
            style={{
              backgroundColor: "#f5f5f5",
              textAlign: "center",
              minHeight: "180px",
              padding: "16px"
            }}
          >
            <div style={{ position: "relative" }}>
              {/* Two devices with connection */}
              <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "12px" }}>
                {/* Left device */}
                <div style={{ textAlign: "center" }}>
                  <div style={{
                    width: "32px",
                    height: "32px",
                    backgroundColor: "#52c41a",
                    borderRadius: "6px",
                    margin: "0 auto 4px auto",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "white",
                    fontSize: "16px"
                  }}>
                    📱
                  </div>
                  <Text
                    style={{
                      whiteSpace: "pre-line",
                      fontSize: "9px",
                      lineHeight: "1.2"
                    }}
                  >
                    {generatePreviewText()}
                  </Text>
                </div>

                {/* Connection line */}
                <div style={{ flex: 1, margin: "0 8px", position: "relative" }}>
                  <div style={{
                    height: "2px",
                    backgroundColor: tempSettings.lldpConnections ? "#52c41a" : "#d9d9d9",
                    position: "relative",
                    opacity: tempSettings.inactiveConnections ? 1 : 0.5
                  }}>
                    {tempSettings.connectionLabels && (
                      <Text style={{
                        position: "absolute",
                        top: "-16px",
                        left: "50%",
                        transform: "translateX(-50%)",
                        fontSize: "8px",
                        color: "#52c41a",
                        whiteSpace: "nowrap",
                        backgroundColor: "rgba(255,255,255,0.8)",
                        padding: "1px 3px",
                        borderRadius: "2px"
                      }}>
                        {generateConnectionPreview()}
                      </Text>
                    )}
                  </div>
                </div>

                {/* Right device */}
                <div style={{ textAlign: "center" }}>
                  <div style={{
                    width: "32px",
                    height: "32px",
                    backgroundColor: "#52c41a",
                    borderRadius: "6px",
                    margin: "0 auto 4px auto",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "white",
                    fontSize: "16px"
                  }}>
                    📱
                  </div>
                  <Text
                    style={{
                      whiteSpace: "pre-line",
                      fontSize: "9px",
                      lineHeight: "1.2"
                    }}
                  >
                    {generatePreviewText()}
                  </Text>
                </div>
              </div>

              {/* Connection type indicators */}
              <div style={{ marginTop: "12px", fontSize: "8px" }}>
                <Space size={4}>
                  {tempSettings.lldpConnections && (
                    <span style={{ color: "#52c41a" }}>● LLDP</span>
                  )}
                  {tempSettings.hierarchicalConnections && (
                    <span style={{ color: "#8c8c8c" }}>● Hierarchy</span>
                  )}
                  {tempSettings.inactiveConnections && (
                    <span style={{ color: "#ff4d4f" }}>● Inactive</span>
                  )}
                </Space>
              </div>
            </div>
          </Card>

          <div style={{ marginTop: 16 }}>
            <Text type="secondary" style={{ fontSize: "12px" }}>
              This shows how device information will appear in the topology view.
            </Text>
          </div>
        </Col>
      </Row>
    </Modal>
  );
};

export default TopologyDisplaySettings;