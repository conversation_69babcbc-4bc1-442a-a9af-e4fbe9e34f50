import React, { useState, useEffect } from "react";
import {
  Modal,
  Transfer,
  message,
  Spin,
  Typography,
  Space,
  Divider
} from "antd";
import { useSelector, useDispatch } from "react-redux";
import {
  useGetAvailableDevicesForCustomTopologyQuery,
  useAssignDeviceToNodeMutation,
  useRemoveDeviceFromNodeMutation
} from "../../app/services/customTopologyApi";
import { selectNode } from "../../features/customTopology/customTopologySlice";

const { Text } = Typography;

const DeviceAssignmentModal = ({
  open,
  onClose,
  selectedNode,
  onDevicesUpdated,
  treeName = "default"
}) => {
  const dispatch = useDispatch();
  const [transferTargetKeys, setTransferTargetKeys] = useState([]);

  // API hooks
  const { data: devicesForTransfer = [], isLoading, refetch: refetchDevices } = useGetAvailableDevicesForCustomTopologyQuery();
  const [assignDeviceToNode] = useAssignDeviceToNodeMutation();
  const [removeDeviceFromNode] = useRemoveDeviceFromNodeMutation();

  // Initialize transfer keys and refetch devices when modal opens
  useEffect(() => {
    if (open && selectedNode) {
      // Refetch latest device list when modal opens
      refetchDevices();

      // Get currently assigned devices for this node
      const currentDevices = selectedNode.children?.filter(child => child.type === "device")
        .map(device => device.macAddress) || [];


      setTransferTargetKeys(currentDevices);
    }
  }, [open, selectedNode, refetchDevices]);

  const handleDeviceTransfer = async () => {
    if (!selectedNode) return;


    try {
      // Get current device assignments
      const currentDevices = selectedNode.children?.filter(child => child.type === "device")
        .map(device => device.macAddress) || [];

      // Find devices to remove (in current but not in target)
      const devicesToRemove = currentDevices.filter(
        deviceMac => !transferTargetKeys.includes(deviceMac)
      );

      // Find devices to add (in target but not in current)
      const devicesToAdd = transferTargetKeys.filter(
        deviceMac => !currentDevices.includes(deviceMac)
      );

      // Remove devices from node
      for (const deviceMac of devicesToRemove) {
        try {
          await removeDeviceFromNode({
            treeName: treeName,
            nodeId: selectedNode.id,
            deviceMac
          }).unwrap();
        } catch (error) {
          console.warn(`Failed to remove device ${deviceMac}:`, error);
        }
      }

      // Add devices to node
      for (const deviceMac of devicesToAdd) {
        try {
          await assignDeviceToNode({
            treeName: treeName,
            nodeId: selectedNode.id,
            deviceMac
          }).unwrap();
        } catch (deviceError) {
          const errorMessage = deviceError.data?.error || deviceError.data || deviceError.message || "";
          if (typeof errorMessage === "string" && errorMessage.includes("already assigned")) {
            // Show the specific location from the error message
            message.warning(errorMessage);
          } else {
            console.warn(`Failed to add device ${deviceMac}:`, deviceError);
            message.error(`Failed to add device ${deviceMac}: ${errorMessage}`);
          }
        }
      }

      message.success("Device assignments updated successfully");

      // Notify parent to refresh data
      if (onDevicesUpdated) {
        onDevicesUpdated();
      }

      // Refresh the topology by re-selecting the node
      dispatch(selectNode(selectedNode.id));

      onClose();
    } catch (error) {
      message.error(
        `Error updating device assignments: ${error.data?.error || error.message || ""}`
      );
    }
  };

  const handleCancel = () => {
    setTransferTargetKeys([]);
    onClose();
  };

  if (!selectedNode) {
    return null;
  }

  return (
    <Modal
      title={
        <div>
          <Text strong>Assign Devices to Node</Text>
          <br />
          <Text type="secondary" style={{ fontSize: "14px" }}>
            {selectedNode.name}
          </Text>
        </div>
      }
      open={open}
      onCancel={handleCancel}
      onOk={handleDeviceTransfer}
      width="70%"
      style={{ top: 20 }}
      confirmLoading={isLoading}
    >
      {isLoading ? (
        <div style={{ textAlign: "center", padding: "50px" }}>
          <Spin size="large" />
          <div style={{ marginTop: "16px" }}>Loading devices...</div>
        </div>
      ) : (
        <>
          <div style={{ marginBottom: "16px" }}>
            <Text type="secondary">
              Select devices to assign to this node. Devices can only be assigned to one node at a time.
            </Text>
          </div>

          <Divider />

          <div style={{ height: "500px" }}>
            <Transfer
              dataSource={(() => {
                // Get all available devices (unassigned + currently assigned to this node)
                const unassignedDevices = devicesForTransfer?.filter(device => {
                  const isUnassigned = !device.subnetName || device.subnetName === "";
                  return isUnassigned;
                }) || [];

                // Get devices currently assigned to this node
                const currentDeviceMacs = selectedNode.children?.filter(child => child.type === "device")
                  .map(d => d.macAddress) || [];

                const currentNodeDevices = devicesForTransfer?.filter(device => {
                  return currentDeviceMacs.includes(device.macAddress || device.key);
                }) || [];

                // Combine both arrays, removing duplicates
                const combined = [...unassignedDevices];
                currentNodeDevices.forEach(device => {
                  if (!combined.find(d => (d.macAddress || d.key) === (device.macAddress || device.key))) {
                    combined.push(device);
                  }
                });

                // Ensure all devices have the key property set to macAddress for Transfer component
                return combined.map(device => ({
                  ...device,
                  key: device.macAddress || device.key || device.id
                }));
              })()}
              targetKeys={transferTargetKeys}
              onChange={setTransferTargetKeys}
              render={(item) => (
                <div>
                  <div style={{ fontWeight: "bold" }}>
                    {item.title}
                  </div>
                  <div style={{ fontSize: "12px", color: "#666" }}>
                    {item.description}
                  </div>
                  {item.group &&
                   item.group !== "" &&
                   item.group !== selectedNode.name && (
                    <div style={{ fontSize: "10px", color: "#ff4d4f" }}>
                      Currently assigned to: {item.group}
                    </div>
                  )}
                </div>
              )}
              titles={["Available Devices", "Assigned to Node"]}
              showSearch
              listStyle={{
                width: "50%",
                height: "450px",
              }}
              filterOption={(inputValue, option) =>
                option.title?.toLowerCase().includes(inputValue.toLowerCase()) ||
                option.description?.toLowerCase().includes(inputValue.toLowerCase())
              }
            />
          </div>
        </>
      )}
    </Modal>
  );
};

export default DeviceAssignmentModal;