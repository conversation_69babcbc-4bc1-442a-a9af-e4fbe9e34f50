import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ForwardConfigModal from "./ForwardConfigModal";
import "@testing-library/jest-dom";

const mockInitialConfig = {
	"forward-1": {
		whatsapp: {
			enabled: true,
			account_sid: "test_sid",
			auth_token: "test_token",
			from_number: "+**********",
			to_numbers: ["+**********"],
			alert_config: {
				min_severity: 0,
				max_severity: 3,
				rate_limit_seconds: 300,
				max_alerts_per_minute: 5,
				keywords: ["error", "critical"],
				exclude_keywords: ["debug"],
			},
		},
		telegram: {
			enabled: false,
			bot_token: "",
			chat_ids: [],
			alert_config: {
				min_severity: 0,
				max_severity: 7,
				rate_limit_seconds: 180,
				max_alerts_per_minute: 10,
				keywords: [],
				exclude_keywords: [],
			},
		},
		mqtt: {
			enabled: false,
			broker_host: "localhost",
			broker_port: 1883,
			username: "",
			password: "",
			topic: "mnms/alerts",
			qos: 1,
			retain: false,
			alert_config: {
				min_severity: 0,
				max_severity: 7,
				rate_limit_seconds: 60,
				max_alerts_per_minute: 20,
				keywords: [],
				exclude_keywords: [],
			},
		},
	},
};

describe("ForwardConfigModal", () => {
	const mockOnOk = jest.fn();
	const mockOnCancel = jest.fn();

	beforeEach(() => {
		jest.clearAllMocks();
	});	it("renders modal when visible", () => {
		render(
			<ForwardConfigModal
				visible={true}
				onCancel={mockOnCancel}
				onOk={mockOnOk}
				initialConfig={mockInitialConfig}
				loading={false}
				selectedForwardService="forward-1"
			/>
		);

		expect(screen.getByText("Configure Log Forwarding")).toBeInTheDocument();
		expect(screen.getByText("Configure Forward Service: forward-1")).toBeInTheDocument();
	});
	it("does not render modal when not visible", () => {
		render(
			<ForwardConfigModal
				visible={false}
				onCancel={mockOnCancel}
				onOk={mockOnOk}
				initialConfig={mockInitialConfig}
				loading={false}
				selectedForwardService="forward-1"
			/>
		);

		expect(screen.queryByText("Configure Log Forwarding")).not.toBeInTheDocument();
	});

	it("switches between services correctly", async () => {
		render(
			<ForwardConfigModal
				visible={true}
				onCancel={mockOnCancel}
				onOk={mockOnOk}
				initialConfig={mockInitialConfig}
				loading={false}
				selectedForwardService="forward-1"
			/>
		);

		// Default should show WhatsApp
		expect(screen.getByText("WhatsApp Configuration")).toBeInTheDocument();

		// Switch to Telegram
		const serviceSelect = screen.getByDisplayValue("whatsapp");
		fireEvent.mouseDown(serviceSelect);
		
		await waitFor(() => {
			fireEvent.click(screen.getByText("Telegram"));
		});

		expect(screen.getByText("Telegram Configuration")).toBeInTheDocument();
	});

	it("calls onCancel when cancel button is clicked", () => {		render(
			<ForwardConfigModal
				visible={true}
				onCancel={mockOnCancel}
				onOk={mockOnOk}
				initialConfig={mockInitialConfig}
				loading={false}
				selectedForwardService="forward-1"
			/>
		);

		const cancelButton = screen.getByRole("button", { name: /cancel/i });
		fireEvent.click(cancelButton);

		expect(mockOnCancel).toHaveBeenCalledTimes(1);
	});

	it("validates required fields", async () => {		render(
			<ForwardConfigModal
				visible={true}
				onCancel={mockOnCancel}
				onOk={mockOnOk}
				initialConfig={{}}
				loading={false}
				selectedForwardService="forward-1"
			/>
		);

		const okButton = screen.getByRole("button", { name: /ok/i });
		fireEvent.click(okButton);

		await waitFor(() => {
			expect(screen.getByText("Account SID is required")).toBeInTheDocument();
		});
	});

	it("shows loading state", () => {		render(
			<ForwardConfigModal
				visible={true}
				onCancel={mockOnCancel}
				onOk={mockOnOk}
				initialConfig={mockInitialConfig}
				loading={true}
				selectedForwardService="forward-1"
			/>
		);

		const okButton = screen.getByRole("button", { name: /ok/i });
		expect(okButton).toBeDisabled();
	});
});
