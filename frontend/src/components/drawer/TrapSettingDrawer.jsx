import {
  <PERSON><PERSON>,
  <PERSON>fi<PERSON><PERSON><PERSON><PERSON>,
  Drawer,
  Form,
  Input,
  InputNumber,
  Space,
  Table,
  theme,
  Typography,
} from "antd";
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  closeTrapSettingDrawer,
  RequestDeviceTrapSetting,
  singleTrapSettingSelector,
} from "../../features/singleDeviceConfigurations/singleTrapSetting";

const { Text } = Typography;

const ipPattern =
  /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

const TrapSettingDrawer = () => {
  const { token } = theme.useToken();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const { visible, mac_address, model, trapData } = useSelector(
    singleTrapSettingSelector
  );

  const onFinish = () => {
    form
      .validateFields()
      .then((values) => {
        console.log(values);
        dispatch(
          RequestDeviceTrapSetting({
            mac_address,
            serverIP: values.serverIP,
            serverPort: values.serverPort,
            comString: values.comString,
          })
        );
        dispatch(closeTrapSettingDrawer());
        form.resetFields();
      })
      .catch((info) => {
        console.log("Validate Failed:", info);
      });
  };
  const onReset = () => {
    dispatch(closeTrapSettingDrawer());
    form.resetFields();
  };

  useEffect(() => {
    form.setFieldsValue({
      serverIP: "",
      serverPort: 162,
      comString: "",
    });
    return () => {
      form.resetFields();
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <Drawer
      title="Trap Setting"
      forceRender
      placement="right"
      closable={false}
      open={visible}
      footer={
        <Space
          align="end"
          style={{ display: "flex", justifyContent: "flex-end" }}
        >
          <Button onClick={onReset}>cancel</Button>
          <Button type="primary" onClick={onFinish}>
            save
          </Button>
        </Space>
      }
    >
      <Space direction="vertical" size="large" style={{ width: "100%" }}>
        <Text
          strong
          style={{ marginBottom: "20px" }}
        >{model?`${model} (${mac_address})`:''}</Text>
        <Form
          form={form}
          onFinish={onFinish}
          layout="vertical"
          name="trap-form"
        >
          <Form.Item
            name="serverIP"
            label="Server IP"
            rules={[
              { required: true, message: "Please input the server ip !" },
              {
                pattern: ipPattern,
                message: "input should be server ip",
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="serverPort"
            label="Server Port"
            rules={[{ required: true, message: "Please input the server port !" }]}
          >
            <InputNumber style={{ width: "100%" }} />
          </Form.Item>
          <Form.Item
            name="comString"
            label="Community String"
            rules={[
              {
                required: true,
                message: "Please input the community string !",
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
        <Table
          size="small"
          dataSource={trapData || []}
          columns={[
            { key: "serverIp", dataIndex: "serverIp", title: "Server IP" },
            {
              key: "serverPort",
              dataIndex: "serverPort",
              title: "Server Port",
            },
            { key: "community", dataIndex: "community", title: "Community" },
          ]}
          scroll={{
            y: 180,
          }}
          pagination={false}
        />
      </Space>
    </Drawer>
  );
};

export default TrapSettingDrawer;
