import React, { useEffect, useState } from "react";
import { Table, App, Tooltip } from "antd";
import {
  debugCmdSelector,
  GetDebugCommandResult,
} from "../../features/debugPage/debugPageSlice";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";

const DashboardTable = () => {
  const { refreshDebugCommandResult } = useSelector(debugCmdSelector);
  const dispatch = useDispatch();
  const [tableData, setTableData] = useState([]);
  const { modal } = App.useApp();

  useEffect(() => {
    getTableData();
  }, [refreshDebugCommandResult]);

  const getTableData = () => {
    const paramObj = {
      cValue: "all",
    };
    dispatch(GetDebugCommandResult(paramObj))
      .unwrap()
      .then((result) => {
        const dataArray = Object.values(result);
        dataArray.sort((a, b) => {
          return new Date(b.timestamp) - new Date(a.timestamp);
        });
        const data = [];
        dataArray.map((item, index) => {
          data.push({
            key: index,
            command: item.command,
            timestamp: item.timestamp,
            status: item.status,
            client: item.client,
          });
        });
        setTableData(data);
      })
      .catch((error) => {
        modal.error({
          title: "All Command Result",
          content: error,
        });
      });
  };

  const columns = [
    {
      title: "Command",
      dataIndex: "command",
      key: "command",
      width: 300, // Increase width slightly
      sorter: (a, b) => (a.command > b.command ? 1 : -1),
      render: (text) => {
        // If command is longer than 50 characters, show with ellipsis and tooltip
        if (text && text.length > 50) {
          return (
            <Tooltip title={text} placement="topLeft">
              <span style={{ 
                display: "block", 
                whiteSpace: "nowrap", 
                overflow: "hidden", 
                textOverflow: "ellipsis",
                maxWidth: "280px" // Slightly less than column width
              }}>
                {text}
              </span>
            </Tooltip>
          );
        }
        return text;
      },
    },
    {
      title: "Timestamp",
      dataIndex: "timestamp",
      key: "timestamp",
      sorter: (a, b) => (a.timestamp > b.timestamp ? 1 : -1),
      render: (data) => {
        return dayjs(data).format("YYYY/MM/DD HH:mm:ss");
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      sorter: (a, b) => (a.status > b.status ? 1 : -1),
      render: (data) => {
        const splitStatus = data?.split(":");
        const lastStatusString = splitStatus[splitStatus.length - 1];
        const secondLastStatusString = splitStatus[splitStatus.length - 2];
        let finalStatus = data;
        if (secondLastStatusString?.includes("pending")) {
          finalStatus = `pending: ${lastStatusString}`;
        }
        return finalStatus;
      },
    },
    {
      title: "Service Name",
      dataIndex: "client",
      key: "client",
      sorter: (a, b) => (a.client > b.client ? 1 : -1),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={tableData}
      pagination={{
        defaultPageSize: 5,
        pageSizeOptions: [5, 10, 15, 20, 25],
      }}
      scroll={{ x: "max-content" }} // Enable horizontal scroll if needed
      size="small" // Make table more compact
    />
  );
};
export default DashboardTable;
