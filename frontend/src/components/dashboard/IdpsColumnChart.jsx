import { useTheme } from "antd-style";
import React, { useEffect, useState } from "react";
import { theme as antdTheme, DatePicker, Flex, Form, Space } from "antd";
import dayjs from "dayjs";
import ReactApexChart from "react-apexcharts";
import { useSendCommandMutation } from "../../app/services/commandApi";
import { useSelector } from "react-redux";
import { idpsSelector } from "../../features/dashboard/idpsSlice";

const IdpsColumnChart = ({ data = [], id, onOkClick, onFocusClick }) => {
  const { selectedRecordDate } = useSelector(idpsSelector);
  const { appearance } = useTheme();
  const { token } = antdTheme.useToken();
  const [sendCommand, {}] = useSendCommandMutation();

  const handleSendSearchCommand = async (filename, selectedRecordDate) => {
    try {
      const command = [
        {
          command: `idps records search -f ${filename} -st ${selectedRecordDate}-00:00 -et ${selectedRecordDate}-23:59`,
          client: id,
        },
      ];
      await sendCommand(command).unwrap();
    } catch (error) {
      console.log(error);
    }
  };

  const [columnChartData, setColumnChartData] = useState({
    series: [
      {
        name: "logs",
        data: [0],
      },
    ],
    options: {
      chart: {
        type: "bar",
        background: token.colorBgContainer,
        height: 400,
        toolbar: {
          show: false,
        },
        events: {
          click: (event, chartContext, opts) => {
            const filename = opts.config.xaxis.categories[opts.dataPointIndex];
            const selectedRecordDate = opts.config.selectedRecordDate;
            handleSendSearchCommand(filename, selectedRecordDate);
          },
        },
      },
      colors: [token.colorPrimary],
      theme: {
        mode: appearance,
      },
      title: {
        text: "file size chart",
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "55%",
          endingShape: "rounded",
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ["transparent"],
      },
      xaxis: {
        categories: [],
      },
      yaxis: {
        title: {
          text: "Log Size(KB)",
        },
      },
      fill: {
        opacity: 1,
      },
      legend: {
        show: true,
        showForSingleSeries: true,
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + " KB";
          },
        },
      },
      grid: {
        show: true,
        xaxis: {
          lines: {
            show: false,
          },
        },
        yaxis: {
          lines: {
            show: true,
          },
        },
      },
    },
  });
  useEffect(() => {
    setColumnChartData((prev) => ({
      ...prev,
      options: {
        ...prev.options,
        theme: { mode: appearance },
        chart: { ...prev.options.chart, background: token.colorBgContainer },
        colors: [token.colorPrimary],
      },
    }));
  }, [token, appearance]);

  useEffect(() => {
    setColumnChartData((prev) => ({
      ...prev,
      series: [
        {
          data:
            data.length > 0
              ? data.map((item) => Math.round(item.size / 1024))
              : [0],
        },
      ],
      options: {
        ...prev.options,
        xaxis: {
          categories: data.length > 0 ? data.map((item) => item.name) : [],
        },
        selectedRecordDate: selectedRecordDate
      },
    }));
  }, [data]);

  return (
    <Flex vertical gap={10}>
      <Form.Item label="Select date" layout="vertical">
        <DatePicker
          style={{ width: "100%" }}
          value={dayjs(selectedRecordDate)}
          onChange={(_, dateString) => onOkClick(dateString)}
          allowClear={false}
          onFocus={(e) => {
            onFocusClick();
          }}
          disabledDate={(currentDate) => {
            return currentDate && currentDate > dayjs().endOf('day');
          }}
        />
      </Form.Item>

      <ReactApexChart
        options={columnChartData.options}
        series={columnChartData.series}
        type="bar"
        width="100%"
      />
    </Flex>
  );
};

export default IdpsColumnChart;
