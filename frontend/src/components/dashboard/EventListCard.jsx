import {
  Card,
  List,
  Space,
  Typography,
  theme as antdTheme,
  But<PERSON>,
  Divider,
} from "antd";
import dayjs from "dayjs";
// eslint-disable-next-line no-unused-vars
import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  clearSocketResultData,
  getInitialAlertData,
  socketControlSelector,
} from "../../features/socketControl/socketControlSlice";
import relativeTime from "dayjs/plugin/relativeTime";
import AlertLevelSetting from "../comman/AlertLevelSetting";

dayjs.extend(relativeTime);

const EventListCard = () => {
  const dispatch = useDispatch();
  const { token } = antdTheme.useToken();
  const { socketResultData, fetchingInitalAlertData } = useSelector(
    socketControlSelector
  );
  useEffect(() => {
    if (socketResultData?.length === 0) dispatch(getInitialAlertData());
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    // Alert level settings
    <Card
      title="Alert Message"
      variant="borderless"
      styles={{
        header: { minHeight: 40 },
        body: {
          height: "calc(100vh - 128px)",
          overflow: "auto",
          padding: "0 10px",
        },
      }}
      extra={
        <Space>
          <Button
            type="primary"
            onClick={() => dispatch(clearSocketResultData())}
          >
            clear all
          </Button>
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: "100%", marginBottom: 16 }}>
        <Typography.Text strong>Alert Level Setting</Typography.Text>
        <AlertLevelSetting />
      </Space>
      <Divider style={{ margin: "16px 0" }} />
      <List
        itemLayout="horizontal"
        dataSource={socketResultData}
        loading={fetchingInitalAlertData}
        renderItem={(item) => (
          <List.Item>
            <Space direction="vertical" style={{ width: "100%" }}>
              <List.Item.Meta
                title={
                  <Typography.Text style={{ color: token.colorPrimary }} strong>
                    {item.title}
                  </Typography.Text>
                }
                description={item.message}
              />
              <Typography.Text italic>
                {dayjs(item.time_stamp).fromNow()}
              </Typography.Text>
            </Space>
          </List.Item>
        )}
      />
    </Card>
  );
};

export default EventListCard;
