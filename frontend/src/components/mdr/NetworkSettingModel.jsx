import { Checkbox, Form, Input, Modal } from "antd";
import React, { useEffect } from "react";

const IPFormat =
  /^((?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){1}$/;

const NetworkSettingModel = ({ open, onCancel, onOk, loading, record }) => {
  const { ipaddress, mac, netmask, gateway, hostname, isdhcp, modelname } =
    record;
  const [form] = Form.useForm();
  const defaultTitle='MDR Network Setting'
  useEffect(() => {
    form.setFieldsValue({
      newipaddress: ipaddress,
      netmask,
      gateway,
      hostname,
      isdhcp,
    });
    return () => {
      form.resetFields();
    };
  }, [ipaddress]); // eslint-disable-line react-hooks/exhaustive-deps
  return (
    <Modal
      open={open}
      width={500}
      forceRender
      maskClosable={false}
      title={mac ? `${defaultTitle} (${mac} ${modelname})`: defaultTitle}
      cancelText="Cancel"
      loading={loading}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onOk({ ...values, ipaddress, mac });
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_mdrNetSet">
        <Form.Item name="isdhcp" valuePropName="checked">
          <Checkbox>DHCP</Checkbox>
        </Form.Item>
        <Form.Item
          name="newipaddress"
          label="IP Address"
          rules={[
            {
              required: true,
              message: "Please input the ipaddress!",
            },
            {
              pattern: IPFormat,
              message: "incorrect ip",
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="netmask"
          label="Subnet Mask"
          rules={[
            {
              required: true,
              message: "Please input the net mask!",
            },
            {
              pattern: IPFormat,
              message: "incorrect subnet mask",
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          name="gateway"
          label="Gateway"
          rules={[
            {
              required: true,
              message: "Please input the gateway!",
            },
            {
              pattern: IPFormat,
              message: "incorrect gateway",
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item 
        name="hostname" 
        label="Host Name"
        rules={[
          {
            required: true,
            message: "Please input the host name!",
          },
        ]}
        >
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default NetworkSettingModel;
