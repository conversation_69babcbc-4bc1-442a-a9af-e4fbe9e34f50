import { Form, Input, Modal } from "antd";
import React from "react";

const MdrProfinetModel = ({ open, onCancel, onOk, loading, record }) => {
  const { mac, modelname } = record;
  const [form] = Form.useForm();
  const defaultTitle='MDR Profinet Setting'
  return (
    <Modal
      open={open}
      width={500}
      forceRender
      maskClosable={false}
      title={mac ? `${defaultTitle} (${mac} ${modelname})`: defaultTitle}
      cancelText="Cancel"
      loading={loading}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onOk({ ...values, mac });
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form form={form} layout="vertical" name="form_in_modal_mdrProfinetSet">
        <Form.Item
          name="profinet"
          label="Profinet name"
          rules={[
            {
              required: true,
              message: "Please input the profinet name!",
            },
          ]}
        >
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default MdrProfinetModel;
