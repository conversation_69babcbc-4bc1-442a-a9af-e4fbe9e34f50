import { Form, Modal, Radio } from "antd";
import React from "react";

const MdrSetLedModel = ({ open, onCancel, onOk, loading, record }) => {
  const { mac, modelname } = record;
  const [form] = Form.useForm();
  const defaultTitle='MDR LED Setting'
  return (
    <Modal
      open={open}
      width={500}
      forceRender
      maskClosable={false}
      title={mac ? `${defaultTitle} (${mac} ${modelname})`: defaultTitle}
      cancelText="Cancel"
      loading={loading}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      onOk={() => {
        form
          .validateFields()
          .then((values) => {
            onOk({ ...values, mac });
            form.resetFields();
          })
          .catch((info) => {
            console.log("Validate Failed:", info);
          });
      }}
    >
      <Form
        form={form}
        layout="vertical"
        name="form_in_modal_mdrLedSet"
        initialValues={{ led: "on" }}
      >
        <Form.Item label="Select LED type" name="led">
          <Radio.Group>
            <Radio.Button value="on">ON</Radio.Button>
            <Radio.Button value="off">OFF</Radio.Button>
            <Radio.Button value="blink">BLINK</Radio.Button>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default MdrSetLedModel;
