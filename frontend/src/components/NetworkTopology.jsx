import React, { useEffect, useRef, useState } from "react";
import {
  Card,
  Button,
  Space,
  Tooltip,
  Badge,
  Spin,
  Tree,
  Row,
  Col,
  theme as antdTheme,
  message,
  Popconfirm,
  Flex,
} from "antd";
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  DownloadOutlined,
  DeleteOutlined,
  ApartmentOutlined,
  GlobalOutlined,
  EnvironmentOutlined,
  ClusterOutlined,
  GroupOutlined,
  LaptopOutlined,
  Pic<PERSON>enterOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import G6 from "@antv/g6";
import {
  useGetNetworkTopologyDataQuery,
  useCleanupGroupsMutation,
} from "../app/services/groupsApi";
import { TopologyImage } from "./topology/TopologyImage";

// Graph configuration constants
const GRAPH_CONFIG = {
  DEFAULT_NODE_SIZE: [56, 56],
  FORCE_LAYOUT: {
    linkDistance: 250,
    nodeStrength: -50,
    edgeStrength: 0.1,
    collideStrength: 0.8,
    nodeSize: 30,
    alpha: 0.3,
    alphaDecay: 0.028,
    alphaMin: 0.01,
  },
  ANIMATION: {
    repeat: true,
    duration: 3000,
  },
  TREE_LAYOUT: {
    type: "compactBox",
    direction: "LR", // Left to Right
    getId: (d) => d.id,
    getHeight: () => 50, // Match node height
    getWidth: () => 180, // Match node width
    getVGap: () => 10, // Increased vertical gap
    getHGap: () => 50, // Increased horizontal gap
    radial: false,
  },
};

// Custom node with text wrapping for TreeGraph
G6.registerNode(
  "tree-node-with-wrap",
  {
    draw(cfg, group) {
      const { label = "", style = {} } = cfg;
      const {
        width = 150,
        height = 40,
        fill = "#f0f0f0",
        stroke = "#d9d9d9",
        lineWidth = 2,
        radius = 8,
      } = style;

      // Create the main rectangle
      const rect = group.addShape("rect", {
        attrs: {
          x: -width / 2,
          y: -height / 2,
          width,
          height,
          fill,
          stroke,
          lineWidth,
          radius,
        },
        name: "main-rect",
      });

      // Helper function to wrap text
      const wrapText = (text, maxWidth, fontSize = 10) => {
        // First, split by explicit newlines (\n)
        const textLines = text.split("\n");
        const lines = [];

        // Approximate character width (this is a rough estimate)
        const charWidth = fontSize * 0.6;
        const maxCharsPerLine = Math.floor(maxWidth / charWidth);

        textLines.forEach((line) => {
          // If the line is empty (from consecutive \n), add empty line
          if (line.trim() === "") {
            lines.push("");
            return;
          }

          // Split each line by spaces for word wrapping
          const words = line.split(/\s+/);
          let currentLine = "";

          words.forEach((word) => {
            // If a single word is too long, break it
            if (word.length > maxCharsPerLine) {
              if (currentLine) {
                lines.push(currentLine.trim());
                currentLine = "";
              }
              // Break long word into chunks
              for (let i = 0; i < word.length; i += maxCharsPerLine - 1) {
                const chunk = word.slice(i, i + maxCharsPerLine - 1);
                if (i + maxCharsPerLine - 1 < word.length) {
                  lines.push(chunk + "-");
                } else {
                  lines.push(chunk);
                }
              }
            } else {
              const testLine = currentLine + (currentLine ? " " : "") + word;
              if (testLine.length <= maxCharsPerLine) {
                currentLine = testLine;
              } else {
                if (currentLine) {
                  lines.push(currentLine.trim());
                }
                currentLine = word;
              }
            }
          });

          if (currentLine) {
            lines.push(currentLine.trim());
          }
        });

        return lines;
      };

      // Process the label text
      const fontSize = cfg.labelCfg?.style?.fontSize || 10;
      const textColor = cfg.labelCfg?.style?.fill || "#000";
      const fontWeight = cfg.labelCfg?.style?.fontWeight || "normal";

      // Reserve some padding for the text
      const textMaxWidth = width - 16; // 8px padding on each side
      const lines = wrapText(label, textMaxWidth, fontSize);

      // Limit to maximum 3 lines to prevent overflow
      const maxLines = 3;
      const displayLines = lines.slice(0, maxLines);
      if (lines.length > maxLines) {
        // Add ellipsis to the last line
        displayLines[maxLines - 1] =
          displayLines[maxLines - 1].slice(0, -3) + "...";
      }

      // Calculate text positioning
      const lineHeight = fontSize + 2;
      const totalTextHeight = displayLines.length * lineHeight;
      const startY = -totalTextHeight / 2 + fontSize / 2;

      // Add text lines
      displayLines.forEach((line, index) => {
        group.addShape("text", {
          attrs: {
            x: 0,
            y: startY + index * lineHeight,
            text: line,
            fontSize,
            fill: textColor,
            fontWeight,
            textAlign: "center",
            textBaseline: "middle",
          },
          name: `text-line-${index}`,
        });
      });

      return rect;
    },
    update(cfg, item) {
      // Get the group and redraw
      const group = item.getContainer();
      group.clear();
      this.draw(cfg, group);
    },
  },
  "rect"
);

// G6 Edge Registration
G6.registerEdge(
  "circle-running",
  {
    afterDraw(cfg, group) {
      const shape = group.get("children")[0];
      const startPoint = shape.getPoint(0);
      const color = cfg.circleColor || "#1890ff";

      if (!startPoint || color === "transparent") return;

      // Remove any existing circles to prevent duplicates
      const existingCircles = group.findAll(
        (element) => element.get("name") === "running-circle"
      );
      existingCircles.forEach((circle) => circle.remove());

      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint.x,
          y: startPoint.y,
          fill: color,
          r: 3,
        },
        name: "running-circle",
      });

      circle.animate((ratio) => {
        const tmpPoint = shape.getPoint(ratio);
        return tmpPoint ? { x: tmpPoint.x, y: tmpPoint.y } : { x: 0, y: 0 };
      }, GRAPH_CONFIG.ANIMATION);
    },
    afterUpdate(cfg, item) {
      const group = item.getContainer();
      this.afterDraw(cfg, group);
    },
    setState(name, value, item) {
      if (name === "refresh" && value) {
        const group = item.getContainer();
        const cfg = item.getModel();
        this.afterDraw(cfg, group);
      }
    },
  },
  "line"
);

const NetworkTopology = () => {
  const { token } = antdTheme.useToken();
  const containerRef = useRef(null);
  const [graph, setGraph] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedSubnetGroup, setSelectedSubnetGroup] = useState(null);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [currentGraphType, setCurrentGraphType] = useState("device"); // "tree" or "device"
  const [selectedNode, setSelectedNode] = useState(null);

  const {
    data: networkData,
    isLoading,
    refetch,
  } = useGetNetworkTopologyDataQuery();

  const [cleanupGroups] = useCleanupGroupsMutation();

  console.log("Network data:", networkData);

  // Extract data from the combined response
  const groups = networkData?.groups || [];
  const topologyData = networkData?.topologyData || {};
  const subnetGroups = networkData?.subnetGroups || [];

  // Convert groups data to tree format for left panel
  const convertToTreeData = (groupsData) => {
    if (!groupsData || groupsData.length === 0) return [];

    const treeData = [];

    groupsData.forEach((relm) => {
      const relmNode = {
        title: (
          <span>
            <ApartmentOutlined style={{ marginRight: 8, color: "#1890ff" }} />
            {relm.name}
          </span>
        ),
        key: `relm-${relm.name}`,
        type: "relm",
        data: relm,
        children: [],
      };

      relm.regions?.forEach((region) => {
        const regionNode = {
          title: (
            <span>
              <GlobalOutlined style={{ marginRight: 8, color: "#52c41a" }} />
              {region.name}
            </span>
          ),
          key: `region-${relm.name}-${region.name}`,
          type: "region",
          data: region,
          children: [],
        };

        region.zones?.forEach((zone) => {
          const zoneNode = {
            title: (
              <span>
                <EnvironmentOutlined
                  style={{ marginRight: 8, color: "#faad14" }}
                />
                {zone.name}
              </span>
            ),
            key: `zone-${relm.name}-${region.name}-${zone.name}`,
            type: "zone",
            data: zone,
            children: [],
          };

          zone.subnets?.forEach((subnet) => {
            const subnetNode = {
              title: (
                <span>
                  <ClusterOutlined
                    style={{ marginRight: 8, color: "#f5222d" }}
                  />
                  {subnet.name}
                </span>
              ),
              key: `subnet-${relm.name}-${region.name}-${zone.name}-${subnet.name}`,
              type: "subnet",
              data: subnet,
              children: [],
            };

            subnet.subnet_groups?.forEach((subnetGroup) => {
              const deviceCount = subnetGroup.devices?.length || 0;
              const subnetGroupNode = {
                title: (
                  <span>
                    <GroupOutlined
                      style={{ marginRight: 8, color: "#722ed1" }}
                    />
                    {subnetGroup.name}
                    <Badge count={deviceCount} style={{ marginLeft: 8 }} />
                  </span>
                ),
                key: `subnetgroup-${relm.name}-${region.name}-${zone.name}-${subnet.name}-${subnetGroup.name}`,
                type: "subnetGroup",
                data: subnetGroup,
                isLeaf: true,
              };

              subnetNode.children.push(subnetGroupNode);
            });

            zoneNode.children.push(subnetNode);
          });

          regionNode.children.push(zoneNode);
        });

        relmNode.children.push(regionNode);
      });

      treeData.push(relmNode);
    });

    return treeData;
  };

  const getDeviceDataByMac = (macAddress) => {
    for (const item of subnetGroups) {
      if (item.deviceData && item.deviceData.hasOwnProperty(macAddress)) {
        return item.deviceData[macAddress];
      }
    }
    return null;
  };

  // Convert hierarchical data to tree graph format
  const convertToTreeGraph = (nodeData, nodeType) => {
    if (!nodeData) return null;

    console.log("Converting to tree graph with node data:", nodeData);

    const createTreeNode = (data, type, level = 0) => {
      const getNodeStyle = (nodeType, level) => {
        const colors = {
          relm: "#188fff52",
          region: "#52c41a52",
          zone: "#faad1452",
          subnet: "#f5222d52",
          subnetGroup: "#722ed152",
          device: token.colorPrimaryBg,
        };

        return {
          fill: colors[nodeType] || "#d9d9d9",
          stroke: colors[nodeType] || "#d9d9d9",
          lineWidth: 2,
          radius: 8,
        };
      };

      const getIcon = (type) => {
        const icons = {
          relm: "🏢",
          region: "🌍",
          zone: "📍",
          subnet: "🔗",
          subnetGroup: "👥",
          device: "💻",
        };
        return icons[type] || "📁";
      };

      const node = {
        id: data.name || `${type}-${Math.random()}`,
        //collapsed: type === "subnetGroup",
        label:
          type === "subnetGroup"
            ? `${getIcon(type)} ${data.name}\n${
                data.devices?.length || 0
              } devices`
            : type === "device"
            ? `${getIcon(type)} ${data}\n${
                getDeviceDataByMac(data)?.ipAddress || "Unknown"
              }\n${getDeviceDataByMac(data)?.modelname || "Unknown"}`
            : `${getIcon(type)} ${data.name}`,
        type: "tree-node-with-wrap", // Use our custom node type
        style: {
          ...getNodeStyle(type, level),
          width: 180, // Slightly wider to accommodate wrapped text
          height: 50, // Taller to accommodate multiple lines
        },
        labelCfg: {
          style: {
            fill: token.colorText,
            fontSize: 10,
            fontWeight: "bold",
          },
        },
        children: [],
        // Add tooltip data for G6 tree graph nodes
        tooltipData: type === "device" ? data : data,
        nodeType: type,
      };
      return node;
    };

    // Build tree structure based on node type
    const buildTreeFromNode = (data, type) => {
      const rootNode = createTreeNode(data, type);

      if (type === "relm" && data.regions) {
        rootNode.children = data.regions.map((region) => {
          const regionNode = createTreeNode(region, "region", 1);
          if (region.zones) {
            regionNode.children = region.zones.map((zone) => {
              const zoneNode = createTreeNode(zone, "zone", 2);
              if (zone.subnets) {
                zoneNode.children = zone.subnets.map((subnet) => {
                  const subnetNode = createTreeNode(subnet, "subnet", 3);
                  if (subnet.subnet_groups) {
                    subnetNode.children = subnet.subnet_groups.map((sg) => {
                      const subnetGroupNode = createTreeNode(
                        sg,
                        "subnetGroup",
                        4
                      );
                      if (sg.devices) {
                        subnetGroupNode.children = sg.devices.map((device) =>
                          createTreeNode(device, "device", 5)
                        );
                      }
                      return subnetGroupNode;
                    });
                  }
                  return subnetNode;
                });
              }
              return zoneNode;
            });
          }
          return regionNode;
        });
      } else if (type === "region" && data.zones) {
        rootNode.children = data.zones.map((zone) => {
          const zoneNode = createTreeNode(zone, "zone", 1);
          if (zone.subnets) {
            zoneNode.children = zone.subnets.map((subnet) => {
              const subnetNode = createTreeNode(subnet, "subnet", 2);
              if (subnet.subnet_groups) {
                subnetNode.children = subnet.subnet_groups.map((sg) => {
                  const subnetGroupNode = createTreeNode(sg, "subnetGroup", 3);
                  if (sg.devices) {
                    subnetGroupNode.children = sg.devices.map((device) =>
                      createTreeNode(device, "device", 4)
                    );
                  }
                  return subnetGroupNode;
                });
              }
              return subnetNode;
            });
          }
          return zoneNode;
        });
      } else if (type === "zone" && data.subnets) {
        rootNode.children = data.subnets.map((subnet) => {
          const subnetNode = createTreeNode(subnet, "subnet", 1);
          if (subnet.subnet_groups) {
            subnetNode.children = subnet.subnet_groups.map((sg) => {
              const subnetGroupNode = createTreeNode(sg, "subnetGroup", 2);
              if (sg.devices) {
                subnetGroupNode.children = sg.devices.map((device) =>
                  createTreeNode(device, "device", 3)
                );
              }
              return subnetGroupNode;
            });
          }
          return subnetNode;
        });
      } else if (type === "subnet" && data.subnet_groups) {
        rootNode.children = data.subnet_groups.map((sg) => {
          const subnetGroupNode = createTreeNode(sg, "subnetGroup", 1);
          if (sg.devices) {
            subnetGroupNode.children = sg.devices.map((device) =>
              createTreeNode(device, "device", 2)
            );
          }
          return subnetGroupNode;
        });
      }

      return rootNode;
    };

    return buildTreeFromNode(nodeData, nodeType);
  };

  // Convert topology data to G6 graph format for right panel
  const convertToTopologyGraph = (selectedDevices, topologyData) => {
    const nodes = [];
    const edges = [];

    if (!selectedDevices || selectedDevices.length === 0) {
      return { nodes, edges };
    }

    const labelStyle = {
      style: {
        fill: token.colorText,
        fontSize: 12,
        background: {
          fill: "transparent",
          padding: [2, 2, 2, 2],
        },
      },
    };

    // Create nodes for selected devices
    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};

      nodes.push({
        id: deviceMac,
        label: `${
          deviceTopologyData.ipAddress || deviceTopologyData.IpAddress || "N/A"
        }\n${deviceMac}\n${
          deviceTopologyData.modelname ||
          deviceTopologyData.ModelName ||
          "Unknown"
        }`,
        type: "image",
        img: TopologyImage(
          deviceTopologyData.modelname || deviceTopologyData.ModelName
        ),
        size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        labelCfg: {
          ...labelStyle,
          position: "bottom",
        },
        originalData: {
          mac: deviceMac,
          ipAddress:
            deviceTopologyData.ipAddress || deviceTopologyData.IpAddress,
          modelName:
            deviceTopologyData.modelname || deviceTopologyData.ModelName,
          services: deviceTopologyData.services || deviceTopologyData.Services,
          links:
            deviceTopologyData.linkData || deviceTopologyData.LinkData || [],
        },
      });
    });

    // Create edges between selected devices with duplicate handling
    const edgeMap = new Map(); // To track unique edges and prioritize blocked ports

    selectedDevices.forEach((deviceMac) => {
      const deviceTopologyData = topologyData[deviceMac] || {};
      const linkData =
        deviceTopologyData.linkData || deviceTopologyData.LinkData || [];

      if (linkData && linkData.length > 0) {
        linkData.forEach((link) => {
          const source = link.source || link.Source;
          const target = link.target || link.Target;
          const sourcePort = link.sourcePort || link.SourcePort;
          const targetPort = link.targetPort || link.TargetPort;
          const linkType = link.linkType || link.LinkType;
          const blockedPort = link.blockedPort || link.BlockedPort;

          if (
            source &&
            target &&
            source !== target &&
            selectedDevices.includes(target)
          ) {
            // Create unique edge key (bidirectional)
            const edgeKey =
              source < target ? `${source}-${target}` : `${target}-${source}`;

            const edgeData = {
              id: `${source}-${target}`,
              source: source,
              target: target,
              label: `${source}_${sourcePort}\n${target}_${targetPort}`,
              type: "circle-running",
              color:
                blockedPort === "true"
                  ? "#faad14"
                  : linkType === "manual"
                  ? "#722ed1"
                  : token.colorTextDisabled,
              circleColor:
                blockedPort === "true" ||
                linkType === "dashed" ||
                linkType === "manual"
                  ? "transparent"
                  : token.colorPrimary,
              labelCfg: labelStyle,
              style: {
                lineWidth: 2,
                lineDash:
                  linkType === "dashed" || linkType === "manual"
                    ? [4, 4]
                    : undefined,
              },
              originalData: {
                sourcePort: sourcePort,
                targetPort: targetPort,
                linkType: linkType,
                blockedPort: blockedPort,
              },
              priority: blockedPort === "true" ? 1 : 0, // Higher priority for blocked ports
            };

            // Check if edge already exists
            if (edgeMap.has(edgeKey)) {
              const existingEdge = edgeMap.get(edgeKey);
              // Replace if current edge has higher priority (blocked port)
              if (edgeData.priority > existingEdge.priority) {
                edgeMap.set(edgeKey, edgeData);
              }
            } else {
              edgeMap.set(edgeKey, edgeData);
            }
          }
        });
      }
    });

    // Convert map to array and remove priority field
    edgeMap.forEach((edgeData) => {
      delete edgeData.priority;
      edges.push(edgeData);
    });

    return { nodes, edges };
  };

  // Handle tree selection
  const handleTreeSelect = (selectedKeys, info) => {
    setSelectedKeys(selectedKeys);

    if (info.node && info.node.type === "subnetGroup") {
      // SubnetGroup selected - show device topology (existing behavior)
      const subnetGroupData = info.node.data;
      setSelectedNode(subnetGroupData);
      setCurrentGraphType("device");

      // Find the corresponding subnet group topology data from the API response
      const keyParts = info.node.key.split("-");
      const subnetGroupTopology = subnetGroups.find(
        (sg) =>
          sg.name === subnetGroupData.name &&
          sg.relmName === keyParts[1] &&
          sg.regionName === keyParts[2] &&
          sg.zoneName === keyParts[3] &&
          sg.subnetName === keyParts[4]
      );

      if (subnetGroupTopology) {
        setSelectedSubnetGroup(subnetGroupTopology);
        // Graph update will be handled by useEffect after graph type switching
      } else {
        setSelectedSubnetGroup(subnetGroupData);
        // Graph update will be handled by useEffect after graph type switching
      }
    } else if (
      info.node &&
      ["relm", "region", "zone", "subnet"].includes(info.node.type)
    ) {
      // Hierarchical node selected - show tree graph
      const nodeData = info.node.data;
      const nodeType = info.node.type;

      setSelectedNode(nodeData);
      setSelectedSubnetGroup(null);
      setCurrentGraphType("tree");

      // The graph switching will be handled by the useEffect hook
      // We don't call updateTreeGraph directly here to avoid conflicts
    } else {
      // Clear selection
      setSelectedSubnetGroup(null);
      setSelectedNode(null);
      setCurrentGraphType("device");

      // Clear the graph if it exists and is the right type
      if (graph && !(graph instanceof G6.TreeGraph)) {
        updateTopologyGraph([]);
      }
    }
  };

  // Auto-expand all tree nodes
  useEffect(() => {
    if (groups && groups.length > 0) {
      const treeData = convertToTreeData(groups);
      const allKeys = [];

      const extractKeys = (nodes) => {
        nodes.forEach((node) => {
          allKeys.push(node.key);
          if (node.children && node.children.length > 0) {
            extractKeys(node.children);
          }
        });
      };

      extractKeys(treeData);
      setExpandedKeys(allKeys);
    }
  }, [groups]);

  // Clean container before initializing new graph
  const cleanContainer = () => {
    if (containerRef.current) {
      try {
        // Use innerHTML to clear all content safely
        containerRef.current.innerHTML = "";
        console.log("Container cleaned successfully");
      } catch (error) {
        console.error("Error cleaning container:", error);
        // Fallback: try to remove children one by one
        try {
          const children = Array.from(containerRef.current.children);
          children.forEach((child) => {
            if (containerRef.current.contains(child)) {
              containerRef.current.removeChild(child);
            }
          });
        } catch (fallbackError) {
          console.error("Fallback cleanup also failed:", fallbackError);
        }
      }
    }
  };

  // Initialize device topology graph
  const initDeviceGraph = () => {
    if (!containerRef.current) {
      console.error("Container ref not available for graph initialization");
      return null;
    }

    try {
      const width = containerRef.current.offsetWidth || 800;
      const height = containerRef.current.offsetHeight || 400;

      console.log(
        `Initializing device graph in container with dimensions: ${width}x${height}`
      );

      const newGraph = new G6.Graph({
        container: "network-topology-container",
        width,
        height,
        renderer: "svg",
        linkCenter: true,
        animate: true,
        modes: {
          default: ["zoom-canvas", "drag-canvas", "click-select", "drag-node"],
        },
        defaultNode: {
          type: "image",
          size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        },
        defaultEdge: {
          type: "circle-running",
        },
        layout: {
          type: "force",
          ...GRAPH_CONFIG.FORCE_LAYOUT,
        },
      });

      // Add tooltip functionality to device graph nodes
      newGraph.on("node:mouseenter", (e) => {
        const { item } = e;
        const model = item.getModel();

        if (model.originalData) {
          // Create tooltip element
          const tooltip = document.createElement("div");
          tooltip.id = "g6-device-tooltip";
          tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 300px;
            z-index: 1000;
            pointer-events: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          `;

          // Create device tooltip content
          const deviceData = model.originalData;
          tooltip.innerHTML = `
            <div><strong>MAC:</strong> ${deviceData.mac || model.id}</div>
            <div><strong>IP Address:</strong> ${
              deviceData.ipAddress || deviceData.IpAddress || "N/A"
            }</div>
            <div><strong>Model:</strong> ${
              deviceData.modelName ||
              deviceData.modelname ||
              deviceData.ModelName ||
              "Unknown"
            }</div>
            ${
              deviceData.services || deviceData.Services
                ? `<div><strong>Services:</strong> ${
                    deviceData.services || deviceData.Services
                  }</div>`
                : ""
            }
          `;

          document.body.appendChild(tooltip);

          // Position tooltip
          const updateTooltipPosition = (event) => {
            tooltip.style.left = event.clientX + 10 + "px";
            tooltip.style.top = event.clientY - 10 + "px";
          };

          updateTooltipPosition(e.originalEvent || e);

          // Store tooltip reference for cleanup
          item._tooltip = tooltip;
          item._tooltipHandler = updateTooltipPosition;

          // Add mouse move listener to update tooltip position
          document.addEventListener("mousemove", updateTooltipPosition);
        }
      });

      newGraph.on("node:mouseleave", (e) => {
        const { item } = e;
        if (item._tooltip) {
          document.body.removeChild(item._tooltip);
          document.removeEventListener("mousemove", item._tooltipHandler);
          item._tooltip = null;
          item._tooltipHandler = null;
        }
      });

      setGraph(newGraph);
      return newGraph;
    } catch (error) {
      console.error("Error initializing device graph:", error);
      return null;
    }
  };

  // Initialize tree graph
  const initTreeGraph = () => {
    if (!containerRef.current) {
      console.error(
        "Container ref not available for tree graph initialization"
      );
      return null;
    }

    try {
      const width = containerRef.current.offsetWidth || 800;
      const height = containerRef.current.offsetHeight || 400;

      console.log(
        `Initializing tree graph in container with dimensions: ${width}x${height}`
      );

      const newGraph = new G6.TreeGraph({
        container: "network-topology-container",
        width,
        height,
        renderer: "svg",
        animate: true,
        modes: {
          default: [
            {
              type: "collapse-expand",
              onChange: function onChange(item, collapsed) {
                console.log("Tree node collapsed:", item, collapsed);
                console.log("Tree node collapsed1:", item.getModel());
                const data = item.getModel();
                data.collapsed = collapsed;
                return true;
              },
            },
            "drag-canvas",
            "zoom-canvas",
          ],
        },
        defaultNode: {
          type: "tree-node-with-wrap", // Use our custom node type
          size: [180, 50], // Match the updated dimensions
          anchorPoints: [
            [0, 0.5],
            [1, 0.5],
          ],
          labelCfg: {
            style: {
              fill: "#000",
              fontSize: 10,
              fontWeight: "bold",
            },
          },
        },
        defaultEdge: {
          type: "cubic-horizontal",
          style: {
            stroke: "#A3B1BF",
            lineWidth: 2,
          },
        },
        layout: GRAPH_CONFIG.TREE_LAYOUT,
      });

      // Add tooltip functionality to tree graph nodes
      newGraph.on("node:mouseenter", (e) => {
        const { item } = e;
        const model = item.getModel();

        if (model.tooltipData) {
          // Create tooltip element
          const tooltip = document.createElement("div");
          tooltip.id = "g6-tooltip";
          tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            max-width: 300px;
            z-index: 1000;
            pointer-events: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          `;

          // Create tooltip content based on node type
          const data = model.tooltipData;
          const type = model.nodeType;

          let tooltipContent = "";

          if (type === "device") {
            const deviceData = getDeviceDataByMac(data);
            if (deviceData) {
              tooltipContent = `
                <div><strong>MAC:</strong> ${data}</div>
                <div><strong>IP Address:</strong> ${
                  deviceData.ipAddress || deviceData.IpAddress || "N/A"
                }</div>
                <div><strong>Model:</strong> ${
                  deviceData.modelname || deviceData.ModelName || "Unknown"
                }</div>
                ${
                  deviceData.hostname || deviceData.Hostname
                    ? `<div><strong>Hostname:</strong> ${
                        deviceData.hostname || deviceData.Hostname
                      }</div>`
                    : ""
                }
                ${
                  deviceData.services ||
                  deviceData.Services ||
                  deviceData.scannedby ||
                  deviceData.ScannedBy
                    ? `<div><strong>Services:</strong> ${
                        deviceData.services ||
                        deviceData.Services ||
                        deviceData.scannedby ||
                        deviceData.ScannedBy
                      }</div>`
                    : ""
                }
              `;
            } else {
              tooltipContent = `
                <div><strong>MAC:</strong> ${data}</div>
                <div><strong>Type:</strong> Device</div>
              `;
            }
          } else {
            // For hierarchical nodes (relm, region, zone, subnet, subnetGroup)
            tooltipContent = `
              <div><strong>Name:</strong> ${data.name}</div>
              <div><strong>Type:</strong> ${
                type.charAt(0).toUpperCase() + type.slice(1)
              }</div>
              ${
                data.comment
                  ? `<div><strong>Comment:</strong> ${data.comment}</div>`
                  : ""
              }
            `;

            // Add specific counts based on type
            if (type === "relm" && data.regions) {
              tooltipContent += `<div><strong>Location:</strong> ${
                data.location || "N/A"
              }</div>`;
              tooltipContent += `<div><strong>Regions:</strong> ${data.regions.length}</div>`;
            } else if (type === "region" && data.zones) {
              tooltipContent += `<div><strong>Zones:</strong> ${data.zones.length}</div>`;
            } else if (type === "zone" && data.subnets) {
              tooltipContent += `<div><strong>Subnets:</strong> ${data.subnets.length}</div>`;
            } else if (type === "subnet" && data.subnet_groups) {
              tooltipContent += `<div><strong>Subnet Groups:</strong> ${data.subnet_groups.length}</div>`;
            } else if (type === "subnetGroup" && data.devices) {
              tooltipContent += `<div><strong>Devices:</strong> ${data.devices.length}</div>`;
            }

            // Add timestamps if available
            if (data.created_at) {
              try {
                const createdDate = new Date(data.created_at).toLocaleString();
                tooltipContent += `<div><strong>Created:</strong> ${createdDate}</div>`;
              } catch {
                tooltipContent += `<div><strong>Created:</strong> ${data.created_at}</div>`;
              }
            }
            if (data.updated_at) {
              try {
                const updatedDate = new Date(data.updated_at).toLocaleString();
                tooltipContent += `<div><strong>Updated:</strong> ${updatedDate}</div>`;
              } catch {
                tooltipContent += `<div><strong>Updated:</strong> ${data.updated_at}</div>`;
              }
            }
          }

          tooltip.innerHTML = tooltipContent;
          document.body.appendChild(tooltip);

          // Position tooltip
          const updateTooltipPosition = (event) => {
            tooltip.style.left = event.clientX + 10 + "px";
            tooltip.style.top = event.clientY - 10 + "px";
          };

          updateTooltipPosition(e.originalEvent || e);

          // Store tooltip reference for cleanup
          item._tooltip = tooltip;
          item._tooltipHandler = updateTooltipPosition;

          // Add mouse move listener to update tooltip position
          document.addEventListener("mousemove", updateTooltipPosition);
        }
      });

      newGraph.on("node:mouseleave", (e) => {
        const { item } = e;
        if (item._tooltip) {
          document.body.removeChild(item._tooltip);
          document.removeEventListener("mousemove", item._tooltipHandler);
          item._tooltip = null;
          item._tooltipHandler = null;
        }
      });

      setGraph(newGraph);
      return newGraph;
    } catch (error) {
      console.error("Error initializing tree graph:", error);
      return null;
    }
  };

  // Initialize appropriate graph based on type
  const initGraph = (graphType = "device") => {
    if (graphType === "tree") {
      return initTreeGraph();
    } else {
      return initDeviceGraph();
    }
  };

  // Validate graph data before rendering
  const validateGraphData = (graphData) => {
    if (!graphData || typeof graphData !== "object") {
      console.error("Invalid graph data: not an object");
      return false;
    }

    if (!Array.isArray(graphData.nodes)) {
      console.error("Invalid graph data: nodes is not an array");
      return false;
    }

    if (!Array.isArray(graphData.edges)) {
      console.error("Invalid graph data: edges is not an array");
      return false;
    }

    // Validate nodes
    for (let i = 0; i < graphData.nodes.length; i++) {
      const node = graphData.nodes[i];
      if (!node || typeof node !== "object") {
        console.error(`Invalid node at index ${i}: not an object`);
        return false;
      }
      if (!node.id || typeof node.id !== "string") {
        console.error(`Invalid node at index ${i}: missing or invalid id`);
        return false;
      }
    }

    // Validate edges
    for (let i = 0; i < graphData.edges.length; i++) {
      const edge = graphData.edges[i];
      if (!edge || typeof edge !== "object") {
        console.error(`Invalid edge at index ${i}: not an object`);
        return false;
      }
      if (!edge.id || typeof edge.id !== "string") {
        console.error(`Invalid edge at index ${i}: missing or invalid id`);
        return false;
      }
      if (!edge.source || typeof edge.source !== "string") {
        console.error(`Invalid edge at index ${i}: missing or invalid source`);
        return false;
      }
      if (!edge.target || typeof edge.target !== "string") {
        console.error(`Invalid edge at index ${i}: missing or invalid target`);
        return false;
      }
    }

    return true;
  };

  // Update topology graph with selected devices
  const updateTopologyGraph = (selectedDevices) => {
    if (!graph) {
      console.error("Graph not initialized");
      return;
    }

    try {
      const graphData = convertToTopologyGraph(selectedDevices, topologyData);

      // Validate data before rendering
      if (!validateGraphData(graphData)) {
        console.error("Graph data validation failed, skipping render");
        return;
      }

      if (graphData && graphData.nodes) {
        // Clear existing data first
        graph.clear();

        // Set new data with error handling
        try {
          graph.data(graphData);
          graph.render();
          graph.updateLayout();

          // Fit view and refresh animations after a short delay
          setTimeout(() => {
            try {
              graph.fitView();
              graph.getEdges().forEach((edge) => {
                try {
                  edge.setState("refresh", true);
                  edge.setState("refresh", false);
                } catch (edgeError) {
                  console.error("Error refreshing edge animation:", edgeError);
                }
              });
            } catch (fitViewError) {
              console.error("Error in fitView or edge refresh:", fitViewError);
            }
          }, 200);
        } catch (renderError) {
          console.error("Error rendering graph:", renderError);
          graph.clear();
        }
      }
    } catch (error) {
      console.error("Error in updateTopologyGraph:", error);
    }
  };

  // Update tree graph with hierarchical data
  const updateTreeGraph = (nodeData, nodeType) => {
    if (!graph) {
      console.error("Graph not initialized");
      return;
    }

    // Only proceed if current graph is a TreeGraph
    if (!(graph instanceof G6.TreeGraph)) {
      console.error("Current graph is not a TreeGraph");
      return;
    }

    try {
      const treeData = convertToTreeGraph(nodeData, nodeType);

      if (treeData) {
        // Clear existing data first
        graph.clear();

        // Set new tree data
        try {
          graph.data(treeData);
          graph.render();
          graph.fitView();
        } catch (renderError) {
          console.error("Error rendering tree graph:", renderError);
          graph.clear();
        }
      } else {
        // Handle empty state
        graph.clear();
        const emptyData = {
          id: "no-data",
          label: `No ${nodeType} data available`,
          type: "rect",
          style: {
            fill: "#f0f0f0",
            stroke: "#d9d9d9",
          },
        };

        try {
          graph.data(emptyData);
          graph.render();
          graph.fitView();
        } catch (emptyStateError) {
          console.error("Error rendering empty tree state:", emptyStateError);
        }
      }
    } catch (error) {
      console.error("Error in updateTreeGraph:", error);
    }
  };

  // Update topology graph with pre-processed subnet group data
  const updateTopologyGraphWithSubnetGroup = (subnetGroupTopology) => {
    if (!graph) {
      console.error("Graph not initialized");
      return;
    }

    try {
      const nodes = [];
      const edges = [];

      const labelStyle = {
        style: {
          fill: token.colorText,
          fontSize: 12,
          background: {
            fill: "transparent",
            padding: [2, 2, 2, 2],
          },
        },
      };

      // Create nodes from pre-processed device data
      if (
        subnetGroupTopology.devices &&
        Array.isArray(subnetGroupTopology.devices)
      ) {
        subnetGroupTopology.devices.forEach((deviceMac) => {
          if (!deviceMac || typeof deviceMac !== "string") {
            console.warn("Invalid device MAC:", deviceMac);
            return;
          }

          const deviceData = subnetGroupTopology.deviceData?.[deviceMac] || {};

          nodes.push({
            id: deviceMac,
            label: `${
              deviceData.ipAddress || deviceData.IpAddress || "N/A"
            }\n${deviceMac}\n${
              deviceData.modelname || deviceData.ModelName || "Unknown"
            }`,
            type: "image",
            img: TopologyImage(deviceData.modelname || deviceData.ModelName),
            size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
            labelCfg: {
              ...labelStyle,
              position: "bottom",
            },
            originalData: deviceData,
          });
        });
      }

      // Create edges from pre-processed connections with duplicate handling
      const edgeMap = new Map(); // To track unique edges and prioritize blocked ports

      if (
        subnetGroupTopology.connections &&
        Array.isArray(subnetGroupTopology.connections)
      ) {
        subnetGroupTopology.connections.forEach((connection) => {
          if (!connection || typeof connection !== "object") {
            console.warn("Invalid connection:", connection);
            return;
          }

          if (!connection.source || !connection.target) {
            console.warn("Connection missing source or target:", connection);
            return;
          }

          // Create unique edge key (bidirectional)
          const edgeKey =
            connection.source < connection.target
              ? `${connection.source}-${connection.target}`
              : `${connection.target}-${connection.source}`;

          const edgeData = {
            id: `${connection.source}-${connection.target}`,
            source: connection.source,
            target: connection.target,
            label: `${connection.source}_${connection.sourcePort || "N/A"}\n${
              connection.target
            }_${connection.targetPort || "N/A"}`,
            type: "circle-running",
            color:
              connection.blockedPort === "true"
                ? "#faad14"
                : connection.linkType === "manual"
                ? "#722ed1"
                : token.colorTextDisabled,
            circleColor:
              connection.blockedPort === "true" ||
              connection.linkType === "dashed" ||
              connection.linkType === "manual"
                ? "transparent"
                : token.colorPrimary,
            labelCfg: labelStyle,
            style: {
              lineWidth: 2,
              lineDash:
                connection.linkType === "dashed" ||
                connection.linkType === "manual"
                  ? [4, 4]
                  : undefined,
            },
            originalData: connection,
            priority: connection.blockedPort === "true" ? 1 : 0, // Higher priority for blocked ports
          };

          // Check if edge already exists
          if (edgeMap.has(edgeKey)) {
            const existingEdge = edgeMap.get(edgeKey);
            // Replace if current edge has higher priority (blocked port)
            if (edgeData.priority > existingEdge.priority) {
              edgeMap.set(edgeKey, edgeData);
            }
          } else {
            edgeMap.set(edgeKey, edgeData);
          }
        });
      }

      // Convert map to array and remove priority field
      edgeMap.forEach((edgeData) => {
        delete edgeData.priority;
        edges.push(edgeData);
      });

      const graphData = { nodes, edges };

      // Validate data before rendering
      if (!validateGraphData(graphData)) {
        console.error(
          "Subnet group graph data validation failed, skipping render"
        );
        return;
      }

      if (graphData && graphData.nodes && graphData.nodes.length > 0) {
        // Clear existing data first
        graph.clear();

        // Set new data with error handling
        try {
          graph.data(graphData);
          graph.render();
          graph.updateLayout();

          // Fit view and refresh animations after a short delay
          setTimeout(() => {
            try {
              graph.fitView();
              graph.getEdges().forEach((edge) => {
                try {
                  edge.setState("refresh", true);
                  edge.setState("refresh", false);
                } catch (edgeError) {
                  console.error("Error refreshing edge animation:", edgeError);
                }
              });
            } catch (fitViewError) {
              console.error("Error in fitView or edge refresh:", fitViewError);
            }
          }, 200);
        } catch (renderError) {
          console.error("Error rendering subnet group graph:", renderError);
          graph.clear();
        }
      } else if (graphData && graphData.nodes && graphData.nodes.length === 0) {
        // Handle empty state
        try {
          graph.clear();
          const emptyData = {
            nodes: [
              {
                id: "no-devices",
                label: "No devices in this SubnetGroup",
                type: "rect",
                style: {
                  fill: "#f0f0f0",
                  stroke: "#d9d9d9",
                },
              },
            ],
            edges: [],
          };

          if (validateGraphData(emptyData)) {
            graph.data(emptyData);
            graph.render();
            graph.fitView();
          }
        } catch (emptyStateError) {
          console.error("Error rendering empty state:", emptyStateError);
        }
      }
    } catch (error) {
      console.error("Error in updateTopologyGraphWithSubnetGroup:", error);
    }
  };

  // Control handlers
  const handleZoomIn = () => {
    if (graph) {
      graph.zoomTo(graph.getZoom() * 1.2);
    }
  };

  const handleZoomOut = () => {
    if (graph) {
      graph.zoomTo(graph.getZoom() * 0.8);
    }
  };

  const handleFitView = () => {
    if (graph && containerRef.current) {
      console.log("Manual fitView triggered");

      // Update graph size to current container dimensions
      const width = containerRef.current.offsetWidth;
      const height = containerRef.current.offsetHeight;

      if (width > 0 && height > 0) {
        graph.changeSize(width, height);
        graph.render();
        graph.fitView();
      }
    }
  };
  const handleDownload = () => {
    if (graph) {
      graph.downloadFullImage("network-topology", "image/png");
    }
  };

  const handleCleanupGroups = async () => {
    try {
      const result = await cleanupGroups().unwrap();
      if (result.hasChanges) {
        message.success(result.message);
      } else {
        message.info(result.message);
      }
    } catch (error) {
      message.error(`Error during cleanup: ${error.data || error.message}`);
    }
  };

  // Initialize graph on mount
  useEffect(() => {
    if (containerRef.current && !graph && !isLoading && networkData) {
      try {
        // Always start with device graph initially
        const newGraph = initGraph("device");
        if (newGraph) {
          const graphData = convertToTopologyGraph([], topologyData);

          // Validate initial data
          if (validateGraphData(graphData)) {
            newGraph.data(graphData);
            newGraph.render();
            setLoading(false);
          } else {
            console.error("Initial graph data validation failed");
            setLoading(false);
          }
        } else {
          console.error("Failed to initialize graph");
          setLoading(false);
        }
      } catch (error) {
        console.error("Error during graph initialization:", error);
        setLoading(false);
      }
    }
  }, [graph, isLoading, networkData, topologyData]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (graph) {
        try {
          graph.destroy();
        } catch (error) {
          console.error("Error destroying graph on unmount:", error);
        }
      }
      // Clean container on unmount with a small delay
      setTimeout(() => {
        cleanContainer();
      }, 10);
    };
  }, []);

  // Handle container resize
  useEffect(() => {
    if (!containerRef.current || !graph) return;

    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;

        if (width > 0 && height > 0) {
          // Update graph dimensions
          graph.changeSize(width, height);
          graph.render();
          graph.fitView();
        }
      }
    });

    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [graph]);

  // Handle window resize as fallback
  useEffect(() => {
    if (!graph) return;

    const handleWindowResize = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        const height = containerRef.current.offsetHeight;

        if (width > 0 && height > 0) {
          graph.changeSize(width, height);
          graph.render();
          graph.fitView();
        }
      }
    };

    window.addEventListener("resize", handleWindowResize);

    return () => {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, [graph]);

  // Handle graph type changes - reinitialize graph when switching between tree and device
  useEffect(() => {
    if (graph && containerRef.current) {
      const isCurrentlyTreeGraph = graph instanceof G6.TreeGraph;
      const shouldBeTreeGraph = currentGraphType === "tree";

      if (isCurrentlyTreeGraph !== shouldBeTreeGraph) {
        console.log(`Switching graph type to ${currentGraphType}`);

        // Graph type mismatch - destroy and reinitialize
        try {
          graph.destroy();
        } catch (destroyError) {
          console.error("Error destroying graph:", destroyError);
        }

        setGraph(null);

        // Use a longer timeout to ensure proper cleanup
        setTimeout(() => {
          if (!containerRef.current) {
            console.error("Container ref lost during graph switching");
            return;
          }

          // Clean the container after graph destruction
          cleanContainer();

          console.log(
            `Container children count before init: ${containerRef.current.children.length}`
          );

          // Additional timeout to ensure DOM cleanup is complete
          setTimeout(() => {
            try {
              const newGraph = initGraph(currentGraphType);
              if (newGraph) {
                console.log(
                  `Successfully switched to ${currentGraphType} graph`
                );
                console.log(
                  `Container children count after init: ${containerRef.current.children.length}`
                );

                // Data updates will be handled by dedicated useEffect hooks
                console.log(
                  `Graph switching complete for ${currentGraphType} type`
                );
              } else {
                console.error(`Failed to initialize ${currentGraphType} graph`);
              }
            } catch (initError) {
              console.error("Error initializing new graph:", initError);
            }
          }, 50);
        }, 100);
      }
    }
  }, [currentGraphType]);

  // Update tree graph when selected node changes
  useEffect(() => {
    if (
      graph &&
      selectedNode &&
      currentGraphType === "tree" &&
      graph instanceof G6.TreeGraph
    ) {
      const nodeType = selectedKeys[0]?.split("-")[0];
      if (nodeType && ["relm", "region", "zone", "subnet"].includes(nodeType)) {
        updateTreeGraph(selectedNode, nodeType);
      }
    }
  }, [selectedNode, currentGraphType, graph, selectedKeys, token]);

  // Update topology when data changes
  useEffect(() => {
    if (graph && selectedSubnetGroup && currentGraphType === "device") {
      // Ensure the graph is actually a regular Graph, not TreeGraph
      if (!(graph instanceof G6.TreeGraph)) {
        console.log(
          "Updating device topology with subnet group:",
          selectedSubnetGroup.name
        );

        // Add a small delay to ensure graph switching is complete
        setTimeout(() => {
          // Check if selectedSubnetGroup has pre-processed data
          if (
            selectedSubnetGroup.deviceData &&
            selectedSubnetGroup.connections
          ) {
            updateTopologyGraphWithSubnetGroup(selectedSubnetGroup);
          } else {
            updateTopologyGraph(selectedSubnetGroup.devices || []);
          }
        }, 100);
      } else {
        console.log("Graph is still TreeGraph, waiting for switch to complete");
      }
    }
  }, [networkData, selectedSubnetGroup, currentGraphType, graph]);

  // Timeout mechanism to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timeout);
  }, [loading]);

  if (isLoading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Spin size="large" />
        <span style={{ marginLeft: 16 }}>Loading network data...</span>
      </div>
    );
  }

  return (
    <div>
      <Row gutter={16} style={{ height: "100%" }}>
        {/* Left Panel - Relm Hierarchy Tree */}
        <Col span={7}>
          <Card
            title={
              <span>
                <ApartmentOutlined style={{ marginRight: 8 }} />
                Relm Hierarchy
              </span>
            }
            style={{ height: "100%" }}
            styles={{
              body: {
                height: "calc(100vh - 158px)",
                overflow: "auto",
                padding: 10,
              },
            }}
            extra={
              <Flex gap={10} align="center" wrap="wrap">
                <Popconfirm
                  title="Cleanup Groups"
                  description="Remove devices from subnet groups that no longer exist in the system. This action cannot be undone."
                  onConfirm={handleCleanupGroups}
                  okText="Yes, Cleanup"
                  cancelText="Cancel"
                >
                  <Button
                    icon={<DeleteOutlined />}
                    title="Remove non-existent devices from subnet groups"
                  >
                    Cleanup
                  </Button>
                </Popconfirm>
                <Button icon={<ReloadOutlined />} onClick={refetch} />
              </Flex>
            }
          >
            {groups && groups.length > 0 ? (
              <Tree
                showIcon
                defaultExpandAll
                selectedKeys={selectedKeys}
                onSelect={handleTreeSelect}
                treeData={convertToTreeData(groups)}
                style={{ fontSize: "14px" }}
              />
            ) : (
              <div
                style={{ textAlign: "center", padding: "20px", color: "#999" }}
              >
                <ApartmentOutlined
                  style={{ fontSize: "48px", marginBottom: "16px" }}
                />
                <div>No relm hierarchy available</div>
              </div>
            )}
          </Card>
        </Col>

        {/* Right Panel - Topology Visualization */}
        <Col span={17}>
          <Card
            title={
              <span>
                {currentGraphType === "tree" ? (
                  <ApartmentOutlined style={{ marginRight: 8 }} />
                ) : (
                  <LaptopOutlined style={{ marginRight: 8 }} />
                )}
                {currentGraphType === "tree"
                  ? "Hierarchy Tree"
                  : "Device Topology"}
                {selectedSubnetGroup && currentGraphType === "device" && (
                  <span
                    style={{
                      marginLeft: 16,
                      color: "#722ed1",
                      fontSize: "14px",
                    }}
                  >
                    ({selectedSubnetGroup.name} -{" "}
                    {selectedSubnetGroup.devices?.length || 0} devices)
                  </span>
                )}
                {selectedNode && currentGraphType === "tree" && (
                  <span
                    style={{
                      marginLeft: 16,
                      color: "#1890ff",
                      fontSize: "14px",
                    }}
                  >
                    ({selectedNode.name} - {selectedKeys[0]?.split("-")[0]}{" "}
                    hierarchy)
                  </span>
                )}
              </span>
            }
            style={{ height: "100%" }}
            styles={{ body: { padding: 5 } }}
            extra={
              <Space>
                <Tooltip title="Zoom In">
                  <Button
                    size="small"
                    icon={<ZoomInOutlined />}
                    onClick={handleZoomIn}
                  />
                </Tooltip>

                <Tooltip title="Zoom Out">
                  <Button
                    size="small"
                    icon={<ZoomOutOutlined />}
                    onClick={handleZoomOut}
                  />
                </Tooltip>

                <Tooltip title="Fit View">
                  <Button
                    size="small"
                    icon={<PicCenterOutlined />}
                    onClick={handleFitView}
                  />
                </Tooltip>

                <Tooltip title="Download">
                  <Button
                    size="small"
                    icon={<DownloadOutlined />}
                    onClick={handleDownload}
                  />
                </Tooltip>
              </Space>
            }
          >
            <div
              id="network-topology-container"
              ref={containerRef}
              style={{
                width: "100%",
                height: "calc(100vh - 150px)",
                minHeight: "400px",
                border: "1px solid #d9d9d9",
                borderRadius: "6px",
                position: "relative",
              }}
            >
              {loading && (
                <div
                  style={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    zIndex: 1000,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: "12px",
                  }}
                >
                  <Spin size="large" />
                  <span style={{ color: "#666", fontSize: "14px" }}>
                    Loading topology...
                  </span>
                </div>
              )}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default NetworkTopology;
