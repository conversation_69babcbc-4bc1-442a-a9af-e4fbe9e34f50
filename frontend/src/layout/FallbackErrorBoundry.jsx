import { Result } from "antd";
import React, { useEffect, useRef } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { useLocation } from "react-router-dom";

const FallbackErrorBoundry = ({ error, resetErrorBoundary }) => {
  const { pathname } = useLocation();
  const originalPathname = useRef(pathname);

  useEffect(() => {
    if (pathname !== originalPathname.current) {
      resetErrorBoundary();
    }
  }, [pathname, resetErrorBoundary]);

  return (
    <Result
      status="error"
      title="Somthing went wrong"
      subTitle="A client error occurred and your request couldn't be completed."
    >
      {process.env.NODE_ENV !== "production" && (
        <details className="error-details">
          <h4>{error.name}</h4>
          <h4>{error.message}</h4>
          <summary>Click for error details</summary>
          <div>{error && error.stack.toString()}</div>
        </details>
      )}
    </Result>
  );
};

export const ErrorBoundries = ({ children }) => {
  return (
    <ErrorBoundary FallbackComponent={FallbackErrorBoundry}>
      {children}
    </ErrorBoundary>
  );
};
