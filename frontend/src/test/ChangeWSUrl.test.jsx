import { fireEvent, render, screen } from "@testing-library/react";
import ChangeWSUrl from "../components/comman/ChangeWSUrl";
import { describe, expect, it, vi } from "vitest";

describe("ChangWSUrl", () => {
  it("renders input field and button", () => {
    render(<ChangeWSUrl />);
    const inputElement = screen.getByRole("textbox");
    const buttonElement = screen.getByRole("button", { name: /save/i });
    expect(inputElement).toBeInTheDocument();
    expect(buttonElement).toBeInTheDocument();
  });

  it("initial input field value is set correctly", () => {
    render(<ChangeWSUrl />);
    const inputElement = screen.getByRole("textbox");
    expect(inputElement).toHaveValue("ws://localhost:27182");
  });

  it("input field value changes correctly", () => {
    render(<ChangeWSUrl />);
    const inputElement = screen.getByRole("textbox");
    fireEvent.change(inputElement, { target: { value: "ws://localhost:27183" } });
    expect(inputElement).toHaveValue("ws://localhost:27183");
  });

  it("button click triggers changeWsURL function with correct value", () => {
    const changeWsURLMock = vi.fn();
    vi.mock("../../utils/themes/useStore", () => ({
      useThemeStore: () => ({
        wsURL: "ws://localhost:27182",
        changeWsURL: changeWsURLMock,
      }),
    }));

    render(<ChangeWSUrl />);
    const inputElement = screen.getByRole("textbox");
    const buttonElement = screen.getByRole("button", { name: /save/i });

    fireEvent.change(inputElement, { target: { value: "ws://localhost:27181" } });
    fireEvent.click(buttonElement);
  });
});
