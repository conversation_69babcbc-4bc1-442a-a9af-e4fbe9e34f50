import { render, fireEvent, waitFor, screen } from "@testing-library/react";
import CategoriesTable, { IdpsRulesAddModel } from "../components/dashboard/CategoriesTable";
import { Provider } from "react-redux";
import { store } from "../app/store";
import { beforeEach, describe, expect, it, vi } from "vitest";

import {
  useAddRulesMutation,
  useDeleteRulesMutation,
  useImportRulesMutation,
} from "../app/services/idpsApi";

vi.mock("antd", async () => {
  const actual = await vi.importActual("antd");
  return {
    ...actual,
    App: {
      useApp: () => ({
        modal: {
          error: vi.fn(),
        },
        notification: {
          error: vi.fn(),
          success: vi.fn(),
        },
      }),
    },
  };
});

vi.mock("../app/services/idpsApi", () => ({
  useAddRulesMutation: vi.fn(),
  useDeleteRulesMutation: vi.fn(),
  useImportRulesMutation: vi.fn(),
}));

const mockDeleteRulesMutation = vi.fn();
const mockAddRulesMutation = vi.fn();
const mockImportRulesMutation = vi.fn();

describe("CategoriesTable component", () => {
  beforeEach(() => {
    useDeleteRulesMutation.mockReturnValue([
      mockDeleteRulesMutation,
      { isLoading: false },
    ]);
    useAddRulesMutation.mockReturnValue([
      mockAddRulesMutation,
      { isLoading: false },
    ]);
    useImportRulesMutation.mockReturnValue([
      mockImportRulesMutation,
      { isLoading: false },
    ]);
  });

  const data = [
    {
      name: "Rule 1",
      created_time: "2024-06-20T12:00:00Z",
      contents: [{ value: "abc" }, { value: "abcd" }],
    },
  ];

  it("renders CategoriesTable component", () => {
    render(
      <Provider store={store}>
        <CategoriesTable data={data} id="exampleId" onOkClick={() => {}} />
      </Provider>
    );
    expect(screen.getByText("Rules client wise")).toBeInTheDocument();
  });

  it("opens import rules modal", () => {
    render(
      <Provider store={store}>
        <CategoriesTable data={data} id="exampleId" onOkClick={() => {}} />
      </Provider>
    );
    fireEvent.click(screen.getByText("import rules"));
    expect(screen.getByLabelText("Rules URL")).toBeInTheDocument();
  });

  it("opens add rules modal", () => {
    render(
      <Provider store={store}>
        <CategoriesTable data={data} id="exampleId" onOkClick={() => {}} />
      </Provider>
    );
    fireEvent.click(screen.getByText("add rules"));
    expect(screen.getByLabelText("Rule Name")).toBeInTheDocument();
    expect(screen.getByLabelText("Rules")).toBeInTheDocument();
  });

  it("handles add rules", async () => {
    mockAddRulesMutation.mockResolvedValue({});
    render(
      <Provider store={store}>
        <IdpsRulesAddModel open={true} onClose={() => {}} svcId="idpsSvc" />
      </Provider>
    );
    fireEvent.change(screen.getByLabelText("Rule Name"), {
      target: { value: "Test Rule" },
    });
    fireEvent.change(screen.getByLabelText("Rules"), {
      target: { value: "Test Rules" },
    });
    fireEvent.click(screen.getByText("Add"));
    await waitFor(() =>
      expect(mockAddRulesMutation).toHaveBeenCalledWith({
        ruleName: "Test Rule",
        rules: "Test Rules",
        svc: "idpsSvc",
      })
    );
  });

  it("submits the import rules form", async () => {
    render(
      <Provider store={store}>
        <CategoriesTable data={data} id="svc-id" onOkClick={() => {}} />
      </Provider>
    );
    fireEvent.click(screen.getByText("import rules"));
    await waitFor(() =>
      expect(screen.getByText("Import Rules for svc-id")).toBeInTheDocument()
    );
    fireEvent.change(screen.getByLabelText("Rules URL"), {
      target: { value: "https://drive.google.com/uc?export=download&id=16Z2D9gFhMCi0p7jYN5rXh5dnE6b34P1p" },
    });
    fireEvent.click(screen.getByText("import"));
    await waitFor(() =>
      expect(mockImportRulesMutation).toHaveBeenCalledWith({
        url: "https://drive.google.com/uc?export=download&id=16Z2D9gFhMCi0p7jYN5rXh5dnE6b34P1p",
        svc: "svc-id",
      })
    );
  });

  it("submits the add rules form", async () => {
    render(
      <Provider store={store}>
        <CategoriesTable data={data} id="svc-id" onOkClick={() => {}} />
      </Provider>
    );
    fireEvent.click(screen.getByText("add rules"));
    await waitFor(() =>
      expect(screen.getByText("Add Rules for svc-id")).toBeInTheDocument()
    );
    fireEvent.change(screen.getByLabelText("Rule Name"), {
      target: { value: "New Rule" },
    });
    fireEvent.change(screen.getByLabelText("Rules"), {
      target: { value: "Rule details" },
    });
    fireEvent.click(screen.getByText("Add"));
    await waitFor(() =>
      expect(mockAddRulesMutation).toHaveBeenCalledWith({
        ruleName: "New Rule",
        rules: "Rule details",
        svc: "svc-id",
      })
    );
  });

  it("sends delete request on delete icon click", async () => {
    render(
      <Provider store={store}>
        <CategoriesTable data={data} id="svc-id" onOkClick={() => {}} />
      </Provider>
    );
    const deleteIcon = screen.getByRole("img", { name: /delete/i });
    fireEvent.click(deleteIcon);
    await waitFor(() =>
      expect(mockDeleteRulesMutation).toHaveBeenCalledWith({
        ruleName: "Rule 1",
        svc: "svc-id",
      })
    );
  });
});
