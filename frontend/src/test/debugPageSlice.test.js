import { configureStore } from "@reduxjs/toolkit";
import debugPageSlice, {
  clearDebugCmdData,
  debugCmdSelector,
  inputCommandChange,
  refreshDebugResult,
} from "../features/debugPage/debugPageSlice";
import { beforeEach, describe, expect, it } from "vitest";

describe("debugPageSlice reducers", () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        debugCmd: debugPageSlice.reducer,
      },
    });
  });

  let dispatchedActions = [];

  beforeEach(() => {
    dispatchedActions = [];
    store.dispatch = (action) => dispatchedActions.push(action);
  });

  it("should handle clearDebugCmdData", () => {
    store.dispatch(clearDebugCmdData());

    const expectedPayload = { type: "debugPageSlice/clearDebugCmdData" };

    expect(dispatchedActions).toEqual([expectedPayload]);
  });

  it("should handle refreshDebugResult", () => {
    const payload = true;
    store.dispatch(refreshDebugResult(payload));

    const expectedPayload = {
      type: "debugPageSlice/refreshDebugResult",
      payload: true,
    };

    expect(dispatchedActions).toEqual([expectedPayload]);
  });

  it("should handle inputCommandChange", () => {
    const payload = "debug command";
    store.dispatch(inputCommandChange(payload));

    const expectedPayload = {
      type: "debugPageSlice/inputCommandChange",
      payload: "debug command",
    };

    expect(dispatchedActions).toEqual([expectedPayload]);
  });

  it("clearDebugCmdData reducer", () => {
    store.dispatch(clearDebugCmdData());

    const state = store.getState().debugCmd;

    expect(state.debugCmdStatus).toBe("in_progress");
    expect(state.errorDebugCmd).toBe("");
  });
});

describe("debugCmdSelector selector", () => {
  it("debugCmdSelector returns the correct state", () => {
    const mockState = {
      debugCmd: {
        debugCmdStatus: "success",
        errorDebugCmd: "",
        cmdResponse: ["result1", "result2"],
        refreshDebugCommandResult: false,
        inputCommand: "test command",
      },
    };

    const selectedState = debugCmdSelector(mockState);

    expect(selectedState.debugCmdStatus).toBe("success");
    expect(selectedState.cmdResponse).toEqual(["result1", "result2"]);
    expect(selectedState.inputCommand).toBe("test command");
  });
});
