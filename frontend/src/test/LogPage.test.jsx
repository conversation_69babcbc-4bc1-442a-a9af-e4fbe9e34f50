import { it, describe, expect } from "vitest";
import { store } from "../app/store";
import { RequestEventlog } from "../features/eventLog/eventLogSlice";
import { loginUser } from "../features/auth/userAuthSlice";
import { Provider } from "react-redux";
import LogPage from "../pages/logsPage/LogPage";
import { render } from "@testing-library/react";

describe("LogPage", () => {

  it("Check if the Log Page component render", () => {
    render(
      <Provider store={store}>
        <LogPage />
      </Provider>
    );
  });

  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });

  it("Request Log Page API ", async () => {
    await store.dispatch(RequestEventlog([]));
    const updatedState = store.getState().eventLog;
    expect(updatedState.eventLogData).to.not.equal([]);
  },10000);
});
