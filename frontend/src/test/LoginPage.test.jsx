import { render, fireEvent } from "@testing-library/react";
import LoginPage from "../pages/auth/LoginPage";
import { describe, expect, it } from "vitest";
import { Provider } from "react-redux";
import { store } from "../app/store";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import { loginUser } from "../features/auth/userAuthSlice";

vi.importActual("axios", () => ({
  create: () => ({
    defaults: { headers: { common: {} } },
    post: vi.fn(),
  }),
}));

describe("LoginPage", () => {
  it("renders the LoginPage component without errors", () => {
    render(
      <Provider store={store}>
        <Router>
          <LoginPage />
        </Router>
      </Provider>
    );
  });

  it("submits the login form with valid credentials", async () => {
    const { getAllByPlaceholderText, getByRole } = render(
      <Provider store={store}>
        <Router>
          <LoginPage />
        </Router>
      </Provider>
    );

    const usernameInput = getAllByPlaceholderText("Username")[0];
    const passwordInput = getAllByPlaceholderText("Password")[0];
    const submitButton = getByRole("button", { name: "Sign in" });

    fireEvent.change(usernameInput, { target: { value: "admin" } });
    fireEvent.change(passwordInput, { target: { value: "default" } });
    fireEvent.click(submitButton);

    expect(usernameInput.value).toBe("admin");
    expect(passwordInput.value).toBe("default");
  });

  it("submits the login form with invalid credentials", async () => {
    const { getAllByPlaceholderText, getByRole } = render(
      <Provider store={store}>
        <Router>
          <LoginPage />
        </Router>
      </Provider>
    );

    const usernameInput = getAllByPlaceholderText("Username")[0];
    const passwordInput = getAllByPlaceholderText("Password")[0];
    const submitButton = getByRole("button", { name: "Sign in" });

    fireEvent.change(usernameInput, { target: { value: "admin1" } });
    fireEvent.change(passwordInput, { target: { value: "default1" } });
    fireEvent.click(submitButton);

    expect(usernameInput.value).not.to.equal("admin");
    expect(passwordInput.value).not.to.equal("default");
  });

  it("submits the login form with invalid credentials", async () => {
    const { getAllByPlaceholderText, getByRole } = render(
      <Provider store={store}>
        <Router>
          <LoginPage />
        </Router>
      </Provider>
    );

    const usernameInput = getAllByPlaceholderText("Username")[0];
    const passwordInput = getAllByPlaceholderText("Password")[0];
    const submitButton = getByRole("button", { name: "Sign in" });

    fireEvent.change(usernameInput, { target: { value: "admin1" } });
    fireEvent.change(passwordInput, { target: { value: "default1" } });
    fireEvent.click(submitButton);

    expect(usernameInput.value).not.to.equal("admin");
    expect(passwordInput.value).not.to.equal("default");
  });
});

describe("Login - API test", () => {

  it("Login API - Success with valid credentials & expected role = admin", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
    const updatedState = store.getState().userAuth;
    expect(updatedState.user).toEqual("admin");
    expect(updatedState.role).toEqual("admin");
    expect(updatedState.isFetching).toEqual(false);
    expect(updatedState.isSuccess).toEqual(true);
  });

 
  it("Login API - Failed with Wrong User Name", async () => {
    await store.dispatch(loginUser({ user: "invalidUser", password: "default" }));
    const updatedState = store.getState().userAuth;

    expect(updatedState.errorMessage).toEqual("user invalidUser not found");
    expect(updatedState.isFetching).toEqual(false);
    expect(updatedState.isError).toEqual(true);
  });

  it("Login API - Failed with Wrong Password", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "invalidPassword" }));
    const updatedState = store.getState().userAuth;

    expect(updatedState.errorMessage).toEqual("password not match");
    expect(updatedState.isFetching).toEqual(false);
    expect(updatedState.isError).toEqual(true);
  });
});
