import locateDeviceSlice, { RequestLocateDevice, clearBeepData, locateDeviceSelector } from '../features/singleDeviceConfigurations/locateDeviceSlice';
import { describe, expect, it, vi } from "vitest";
it('initial state', () => {
  const initialState = locateDeviceSlice.reducer(undefined, {});
  expect(initialState).toEqual({
    beepStatus: "in_progress",
    errorLocate: "",
  });
});
vi.mock('../../utils/apis/protectedApis', () => ({
    post: vi.fn(() => Promise.resolve({ data: "Response data", status: 200 })),
  }));
  
  describe("licenseAlertSlice", () => {

  it('locateDeviceSelector', () => {
    const state = {
      beepSingleDevice: {
        beepStatus: "success",
        errorLocate: "Config beep device success !",
      },
    };
    const selectedState = locateDeviceSelector(state);
    expect(selectedState).toEqual({
      beepStatus: "success",
      errorLocate: "Config beep device success !",
    });
  });
  it('clearBeepData', () => {
    const initialState = {
      beepStatus: "success",
      errorLocate: "Config beep device success !",
    };
    const newState = locateDeviceSlice.reducer(initialState, clearBeepData());
    expect(newState).toEqual({
      beepStatus: "in_progress",
      errorLocate: "",
    });
  });
  it('extraReducers - RequestLocateDevice fulfilled', () => {
    const initialState = {
      beepStatus: "in_progress",
      errorLocate: "",
    };
    const payload = "Response data";
    const newState = locateDeviceSlice.reducer(initialState, RequestLocateDevice.fulfilled(payload));
    expect(newState).toEqual({
      beepStatus: "success",
      errorLocate: "Config beep device success !",
    });
  });
  
  it('extraReducers - RequestLocateDevice pending', () => {
    const initialState = {
      beepStatus: "success",
      errorLocate: "Config beep device success !",
    };
    const newState = locateDeviceSlice.reducer(initialState, RequestLocateDevice.pending());
    expect(newState).toEqual({
      beepStatus: "in_progress",
      errorLocate: "",
    });
  });
  
});
