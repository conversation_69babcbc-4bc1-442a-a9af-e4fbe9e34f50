import licenseAlertSlice, {
  setMessage,
} from "../features/socketControl/licenseAlertSlice";
import { describe, expect, it } from "vitest";

describe("licenseAlertSlice", () => {
  it("initial state", () => {
    const initialState = licenseAlertSlice.reducer(undefined, {});
    expect(initialState).toEqual({
      licenseErrorMsg: "",
    showLicenseError: false,
    showLicenseWaterMark: false,
    featureEnabled: [],
    });
  });
  it("setMessage - unrecognized kind", () => {
    const initialState = {
      licenseErrorMsg: "",
      showLicenseError: false,
      showLicenseWaterMark: false,
      featureEnabled: [],
    };
    const payload = {
      kind: "unknown_kind",
      message: "Unknown message",
    };
    const newState = licenseAlertSlice.reducer(
      initialState,
      setMessage(payload)
    );
    expect(newState).toEqual(initialState);
  });
});
