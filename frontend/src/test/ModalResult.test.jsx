import { render } from "@testing-library/react";
import ModalResult from "../components/debug/ModalResult";
import { describe, expect, it } from "vitest";

describe("ModalResult", () => {
  it("should render the component with title and value", () => {
    const title = "Title";
    const value = "Value";

    const { getByText } = render(<ModalResult title={title} value={value} />);

    expect(getByText(title)).toBeInTheDocument();
    expect(getByText(value)).toBeInTheDocument();
  });

  it("should render the component with the provided title and value", () => {
    const title = "Custom Title";
    const value = "Custom Value";

    const { getByText } = render(<ModalResult title={title} value={value} />);

    expect(getByText(title)).toBeInTheDocument();
    expect(getByText(value)).toBeInTheDocument();
  });

});
