import { describe, expect, it } from "vitest";
import { store } from "../app/store";
import { CheckServerStatus } from "../features/comman/serverStatusSlice";

describe("serverStatus ", () => {

it("Should handle serverStatus get correctly when successful", async () => {
  await store.dispatch(CheckServerStatus());
  const updatedState = store.getState().serverStatus;
  expect(updatedState.serverOnlineStatus).toEqual(true);
});
})
