import { render, screen, fireEvent } from "@testing-library/react";
import { Provider } from "react-redux";
import NetworkSettingDrawer from "../components/drawer/NetworkSettingDrawer";
import { closeNetworkSettingDrawer } from "../features/singleDeviceConfigurations/singleNetworkSetting";
import { describe, expect, it } from "vitest";
import { store } from "../app/store";

describe("NetworkSettingDrawer", () => {
  it("renders the component", () => {
    render(
      <Provider store={store}>
        <NetworkSettingDrawer />
      </Provider>
    );

    expect(screen.getByText("Network Setting")).toBeInTheDocument();
  });

  it("dispatches closeNetworkSettingDrawer when cancel button is clicked", () => {
    render(
      <Provider store={store}>
        <NetworkSettingDrawer />
      </Provider>
    );

    fireEvent.click(screen.getByText("cancel"));

    const action = closeNetworkSettingDrawer();
    store.dispatch(action);

    expect(action).toEqual(closeNetworkSettingDrawer());
  });

  it("sets form fields values correctly on component mount", () => {
    render(
      <Provider store={store}>
        <NetworkSettingDrawer />
      </Provider>
    );

    expect(screen.getByLabelText("IP Address").value).toBe("");
    expect(screen.getByLabelText("Subnet Mask").value).toBe("");
    expect(screen.getByLabelText("Gateway").value).toBe("");
    expect(screen.getByLabelText("Host Name").value).toBe("");
    expect(screen.getByLabelText("DHCP").checked).toBe(false);
  });
});
