import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import ExportData, { getFilename } from "../components/exportData/ExportData";

vi.mock("react-csv", () => ({
  CSVLink: ({ onClick, ...props }) => <a {...props} onClick={onClick} />,
}));

describe("ExportData", () => {
  it("renders ExportData component with buttons", () => {
    const Columns = [
      { title: "Name", key: "name" },
      { title: "Age", key: "age" },
    ];
    const DataSource = [
      { name: "<PERSON>", age: 30 },
      { name: "<PERSON>", age: 25 },
    ];
    const title = "UserData";

    render(
      <ExportData Columns={Columns} DataSource={DataSource} title={title} />
    );

    expect(screen.getByText("Export")).toBeInTheDocument();
  });

  it("calls handlePdfDownload when PDF button is clicked", () => {
    const Columns = [
      { title: "Name", key: "name" },
      { title: "Age", key: "age" },
    ];
    const DataSource = [
      { name: "John Doe", age: 30 },
      { name: "Jane Doe", age: 25 },
    ];
    const title = "UserData";

    render(
      <ExportData Columns={Columns} DataSource={DataSource} title={title} />
    );

    const pdfButton = screen.getByText("Export");
    fireEvent.click(pdfButton);

  });

  it("calls handleCsvDownload and triggers CSVLink click when CSV button is clicked", () => {
    const Columns = [
      { title: "Name", key: "name" },
      { title: "Age", key: "age" },
    ];
    const DataSource = [
      { name: "John Doe", age: 30 },
      { name: "Jane Doe", age: 25 },
    ];
    const title = "UserData";

    render(
      <ExportData Columns={Columns} DataSource={DataSource} title={title} />
    );

    const csvButton = screen.getByText("Export");
    fireEvent.click(csvButton);

  });
});

describe("getFilename", () => {
  it("generates a filename with the current date and time", () => {
    const mockDate = new Date("2023-01-01T12:34:56Z");
    global.Date = vi.fn(() => mockDate);

    const title = "UserData";
    const filename = getFilename(title);

    expect(filename).toBe("UserData_01012023_180456");
  });
});
