import { render, screen } from "@testing-library/react";
import { describe, expect, it } from 'vitest';
import QRCodeValidator from '../pages/two_factor_auth/QRCodeValidator';
import { Provider } from 'react-redux';
import { store } from '../app/store';
import { BrowserRouter } from 'react-router-dom';

describe('QRCodeValidator', () => {
  it('renders QRCodeValidator component', () => {
    render(<Provider store={store}>
        <BrowserRouter>
          <QRCodeValidator />
        </BrowserRouter>
      </Provider>);
    const qrCodeValidatorForm = screen.getByTestId('qrcode-validator-form');
    expect(qrCodeValidatorForm).toBeInTheDocument();
  });

  it('displays alert message', () => {
    render(<Provider store={store}>
        <BrowserRouter>
          <QRCodeValidator />
        </BrowserRouter>
      </Provider>);
    const alertMessage = screen.getByText(/Scan QR code with Google Authenticator/i);
    expect(alertMessage).toBeInTheDocument();
  });
});
