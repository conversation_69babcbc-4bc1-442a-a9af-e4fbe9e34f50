import { render, fireEvent } from "@testing-library/react";
import SettingsComp from "../components/comman/SettingsComp";
import { it, describe, expect } from "vitest";

describe("SettingsComp", () => {
  it("should render the component without errors", () => {
    render(<SettingsComp />);
  });

  it("should open the popover when the button is clicked", () => {
    const { getByRole } = render(<SettingsComp />);
    const button = getByRole("button");
    fireEvent.click(button);
  });

  it("should display the correct title in the popover", () => {
    const { getByRole, getByText } = render(<SettingsComp />);
    const button = getByRole("button");
    fireEvent.click(button);

    const popoverTitle = getByText("NIMBL Settings");
    expect(popoverTitle).toBeInTheDocument();
  });

  it("should display the 'SettingsComp' button with the correct icon", () => {
    const { getByRole } = render(<SettingsComp />);
    const button = getByRole("button");
    const icon = button.querySelector("svg");
    expect(icon).toBeInTheDocument();
  });
});
