import SaveRuunningConfigDrawer from "../components/drawer/SaveRuunningConfigDrawer";
import { it, describe } from "vitest";
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../app/store";

describe("SaveRuunningConfigDrawer", () => {
  it("Check if the SaveRuunningConfigDrawer component render", () => {
    render(
      <Provider store={store}>
        <SaveRuunningConfigDrawer />{" "}
      </Provider>
    );
    screen.debug();
  });
  it("Check save button ", () => {
    const { getByText } = render(
      <Provider store={store}>
        <SaveRuunningConfigDrawer />{" "}
      </Provider>
    );
    const saveButton = getByText("save");
    fireEvent.click(saveButton);
  });

  it("Check cancel button ", () => {
    const { getByText } = render(
      <Provider store={store}>
        <SaveRuunningConfigDrawer />{" "}
      </Provider>
    );
    const cancelButton = getByText("cancel");
    fireEvent.click(cancelButton);
  });
});
