import { describe, expect, it } from "vitest";
import { render, screen } from "@testing-library/react";
import IdpsEventTable from "../components/dashboard/IdpsEventTable";
import { Provider } from "react-redux";
import { store } from "../app/store";
import dayjs from 'dayjs';

describe("IdpsEventTable Component", () => {
  it("renders the table with correct headers", () => {
    const mockData = {
      event: [
        {
          rulename: "Test Rule",
          id: "123",
          inInterface: "eth0",
          type: "alert",
          protocol: "TCP",
          srcip: "***********",
          srcPort: "8080",
          destip: "***********",
          destPort: "80",
          description: "Test Description",
          timestamp: "2024-09-03T12:00:00Z",
        },
      ],
    };

    render(
      <Provider store={store}>
        <IdpsEventTable data={mockData} id="sampleId" />
      </Provider>
    );

    expect(screen.getByText('Rule_name')).toBeInTheDocument();
    expect(screen.getByText('Id')).toBeInTheDocument();
    expect(screen.getByText('Interface')).toBeInTheDocument();
    expect(screen.getByText('Type')).toBeInTheDocument();
    expect(screen.getByText('Protocol')).toBeInTheDocument();
    expect(screen.getAllByText('Port')).toHaveLength(2); 
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Timestamp')).toBeInTheDocument();
  });

  it("displays data correctly in the table rows", () => {
    const mockData = {
      event: [
        {
          rulename: "Test Rule",
          id: "123",
          inInterface: "eth0",
          type: "alert",
          protocol: "TCP",
          srcip: "***********",
          srcPort: "8080",
          destip: "***********",
          destPort: "80",
          description: "Test Description",
          timestamp: dayjs('2023-09-03T12:34:56').toISOString(),
        },
      ],
    };

    render(
      <Provider store={store}>
        <IdpsEventTable data={mockData} id="sampleId" />
      </Provider>
    );

    expect(screen.getByText("Test Rule")).toBeInTheDocument();
    expect(screen.getByText("***********")).toBeInTheDocument();
    expect(screen.getByText("***********")).toBeInTheDocument();
    expect(screen.getByText("Test Description")).toBeInTheDocument();
  });
});
