import { render, screen } from "@testing-library/react";
import TwoFAValidator from "../pages/two_factor_auth/2FAValidator";
import { describe, expect, it } from "vitest";
import { Provider } from "react-redux";
import { store } from "../app/store";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import QRCodeValidator from "../pages/two_factor_auth/QRCodeValidator";
import twoFactorAuthSlice, { generatSecretKey } from "../features/auth/twoFactorAuthSlice";
import { loginUser } from "../features/auth/userAuthSlice";

describe("2FAValidator", () => {
  it("renders the TwoFAValidator component without errors", () => {
    render(
      <Provider store={store}>
        <BrowserRouter>
          <TwoFAValidator />
        </BrowserRouter>
      </Provider>
    );
  });

  it("renders the QRCodeValidatorForm component ", () => {
    render(
      <Provider store={store}>
        <BrowserRouter>
          <QRCodeValidator />
        </BrowserRouter>
      </Provider>
    );

    const openAuthenticatorMessage = screen.getByText(
      "Scan QR code with Google Authenticator to get 2FA code for next time login!"
    );
    expect(openAuthenticatorMessage).toBeInTheDocument();
  });
});
describe('Two Factor Authentication Slice', () => {
  it('should set clearState correctly', () => {
    const initialState = {
      isError: true,
      isSuccess: true,
      isFetching: true,
    };
    const action = twoFactorAuthSlice.actions.clearState();
    const nextState = twoFactorAuthSlice.reducer(initialState, action);
    expect(nextState).toEqual({
      isError: false,
      isSuccess: false,
      isFetching: false,
    });
  });

  it('should set clearAuthData correctly', () => {
    const initialState = {
      user: 'testUser',
      role: 'admin',
      isError: true,
      isSuccess: true,
      isFetching: true,
    };
    const action = twoFactorAuthSlice.actions.clearAuthData();
    const nextState = twoFactorAuthSlice.reducer(initialState, action);
    expect(nextState).toEqual({
      user: '',
      role: '',
      isError: false,
      isSuccess: false,
      isFetching: false,
    });
  });
  
  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });
  it("Device Page - Success", async () => {
    await store.dispatch(generatSecretKey());
    const state = store.getState().inventory;
    expect(state.deviceData).not.to.equal("");
  });
});


