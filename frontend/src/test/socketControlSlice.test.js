import { configureStore } from "@reduxjs/toolkit";
import { it, describe, expect, beforeEach } from "vitest";
import SocketControlSlice, {
  setSocketResultData,
  clearSocketResultData,
  setSocketErrorMessage,
  setSocketLoading,
  socketControlSelector,
} from "../features/socketControl/socketControlSlice";

describe("SocketControlSlice", () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        socketControl: SocketControlSlice.reducer,
      },
    });
  });

  it("should set socket result data correctly", () => {
    const resultData = {
      title: "Success",
      message: "Socket data received",
      time_stamp: Date.now(),
    };
    store.dispatch(setSocketResultData(resultData));
    const state = store.getState().socketControl;
    expect(state.socketResultData).toEqual([resultData]);
  });

  it("should clear socket result data correctly", () => {
    const initialState = {
      socketResultData: [{ title: "Result 1" }, { title: "Result 2" }],
    };
    store = configureStore({
      reducer: {
        socketControl: SocketControlSlice.reducer,
      },
      preloadedState: initialState,
    });

    store.dispatch(clearSocketResultData());
    const state = store.getState().socketControl;
    expect(state.socketResultData).toEqual([]);
  });

  it("should set socket error message correctly", () => {
    const errorMessage = "Socket error occurred";
    store.dispatch(setSocketErrorMessage(errorMessage));
    const state = store.getState().socketControl;
    expect(state.socketErrorMsg).toEqual(errorMessage);
  });

  it("should set socket loading correctly", () => {
    const loading = true;
    store.dispatch(setSocketLoading(loading));
    const state = store.getState().socketControl;
    expect(state.socketLoading).toEqual(loading);
  });

  it("should select socket control data correctly", () => {
    const socketResultData = [{ title: "Result 1" }, { title: "Result 2" }];
    const socketErrorMsg = "Socket error occurred";
    const socketLoading = true;
    const state = {
      socketControl: {
        socketResultData,
        socketErrorMsg,
        socketLoading,
      },
    };
    const selectedData = socketControlSelector(state);
    expect(selectedData.socketResultData).toEqual(socketResultData);
    expect(selectedData.socketErrorMsg).toEqual(socketErrorMsg);
    expect(selectedData.socketLoading).toEqual(socketLoading);
  });
});
