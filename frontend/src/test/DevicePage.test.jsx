import { createEvent, fireEvent, render, screen, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import DevicePage from "../pages/device/DevicePage";
import { describe, expect, it, vi } from "vitest";
import { store } from "../app/store";
import { getInventoryData } from "../features/inventory/inventorySlice";
import { loginUser } from "../features/auth/userAuthSlice";
import { RequestEnableSNMP } from "../features/singleDeviceConfigurations/enableSNMPDeciceSlice";
import { RequestRebootDevice } from "../features/singleDeviceConfigurations/rebootDeviceSlice";
import { RequestLocateDevice } from "../features/singleDeviceConfigurations/locateDeviceSlice";
import { RequestDeviceSyslogSetting } from "../features/singleDeviceConfigurations/singleSyslogSetting";

// Mock the custom topology API
vi.mock("../app/services/customTopologyApi", () => ({
  useGetFlattenedCustomTopologyNodesQuery: vi.fn(() => ({
    data: [
      {
        id: "test-tree-node1",
        name: "Test Node 1",
        displayName: "Test Node 1",
        treeName: "test-tree",
        nodeId: "node1",
        type: "node",
        level: 0,
        devices: ["00-60-E9-01-BD-B4"],
        deviceCount: 1,
        comment: "Test topology node"
      },
      {
        id: "test-tree-node2",
        name: "Test Node 1/Child Node 1",
        displayName: "Child Node 1",
        treeName: "test-tree",
        nodeId: "node2",
        type: "node",
        level: 1,
        devices: ["00-60-E9-01-BD-B5"],
        deviceCount: 1,
        comment: "Child topology node"
      }
    ],
    refetch: vi.fn()
  }))
}));

describe("DevicePage", () => {
  it("Set an authentication token in a request header", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));
  });

  it("renders the DevicePage component", () => {
    const { getByTestId } = render(
      <Provider store={store}>
        <DevicePage />
      </Provider>
    );
    const testComp = getByTestId("device-table-div");
    const contextMenuEvent = createEvent.contextMenu(testComp);
    fireEvent(testComp, contextMenuEvent);
  });
});
it("handles context menu click", () => {
  render(
    <Provider store={store}>
      <DevicePage />
    </Provider>
  );
  const tableRow = screen.getByTestId("device-table-div");
  const contextMenuEvent = new MouseEvent("contextmenu", {
    bubbles: true,
    cancelable: true,
    button: 2,
  });
  if (tableRow) {
    tableRow.dispatchEvent(contextMenuEvent);
  } 
});

it("renders topology nodes and device table correctly", () => {
  window.sessionStorage.setItem("nmsuserrole", "user");

  const {getByTestId}=render(
    <Provider store={store}>
      <DevicePage />
    </Provider>
  );

  const deviceTableDiv = getByTestId("device-table-div");

  expect(deviceTableDiv).toBeInTheDocument();

});

it("Device Page - Success", async () => {
  await store.dispatch(getInventoryData());
  const state = store.getState().inventory;
  expect(state.deviceData).not.to.equal("");
});
it("RequestEnableSNMP - Success", async () => {
  const paramObj ={
    mac:"00-60-E9-01-BD-B4",
    ipaddress:"*************"
  };
  await store.dispatch(RequestEnableSNMP(paramObj));
  const state = store.getState().enableSNMPDevice;
  expect(state.enableSNMPStatus).toEqual("success");
});
it("RequestRebootDevice - Success", async () => {
  const paramObj ={
    mac:"00-60-E9-01-BD-B4",
    ipaddress:"*************"
  };
  await store.dispatch(RequestRebootDevice(paramObj));
  const state = store.getState().rebootSingleDevice;
  expect(state.rebootStatus).toEqual("success");
});
it("RequestLocateDevice - Success", async () => {
  const paramObj ={
    mac:"00-60-E9-01-BD-B4",
    ipaddress:"*************"
  };
  await store.dispatch(RequestLocateDevice(paramObj));
  const state = store.getState().beepSingleDevice;
  expect(state.beepStatus).toEqual("success");
});
it("RequestDeviceSyslogSetting - Success", async () => {
  const paramObj ={
    mac_address: "",
    logToFlash: true,
    logLevel: 7,
    logToServer: true,
    serverIP: "",
    serverPort: 514,
  };
  await store.dispatch(RequestDeviceSyslogSetting(paramObj));
  const state = store.getState().singleSyslogSetting;
  expect(state.syslogSettingStatus).toEqual("success");
});


