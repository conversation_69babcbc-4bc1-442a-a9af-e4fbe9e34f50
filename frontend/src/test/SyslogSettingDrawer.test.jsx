import SyslogSettingDrawer from "../components/drawer/SyslogSettingDrawer";
import { it, describe } from "vitest";
import { fireEvent, render } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../app/store";

describe("SyslogSettingDrawer", () => {
  it("check if the SyslogSettingDrawer component render", () => {
    render(
      <Provider store={store}>
        <SyslogSettingDrawer />
      </Provider>
    );
  });
  it("Check save button ", () => {
    const { getByText } = render(
      <Provider store={store}>
        <SyslogSettingDrawer />{" "}
      </Provider>
    );
    const saveButton = getByText("save");
    fireEvent.click(saveButton);
  });

  it("Check cancel button ", () => {
    const { getByText } = render(
      <Provider store={store}>
        <SyslogSettingDrawer />{" "}
      </Provider>
    );
    const cancelButton = getByText("cancel");
    fireEvent.click(cancelButton);
  });
});
