import React from "react";
import { render, fireEvent, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { it, describe, expect, vi } from "vitest";
import AlertLevelSetting from "../components/comman/AlertLevelSetting";
import { api } from "../app/services/api";

// Mock the API
const mockUpdateGlobalSettings = vi.fn();

// Create a mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      api: api.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(api.middleware),
  });
};

// Mock the hook
vi.mock("../app/services/globalSettingsApi", () => ({
  useUpdateGlobalSettingsMutation: () => [
    mockUpdateGlobalSettings,
    { isLoading: false }
  ],
}));

describe("AlertLevelSetting", () => {
  let store;

  beforeEach(() => {
    store = createMockStore();
    mockUpdateGlobalSettings.mockClear();
  });

  it("should render the component without errors", () => {
    render(
      <Provider store={store}>
        <AlertLevelSetting />
      </Provider>
    );
    
    expect(screen.getByRole("combobox")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /save/i })).toBeInTheDocument();
  });

  it("should display all alert level options", () => {
    render(
      <Provider store={store}>
        <AlertLevelSetting />
      </Provider>
    );
    
    const select = screen.getByRole("combobox");
    fireEvent.mouseDown(select);
    
    // Check if all 8 severity levels are present
    expect(screen.getByText("0 - Emergency")).toBeInTheDocument();
    expect(screen.getByText("1 - Alert")).toBeInTheDocument();
    expect(screen.getByText("2 - Critical")).toBeInTheDocument();
    expect(screen.getByText("3 - Error")).toBeInTheDocument();
    expect(screen.getByText("4 - Warning")).toBeInTheDocument();
    expect(screen.getByText("5 - Notice")).toBeInTheDocument();
    expect(screen.getByText("6 - Informational")).toBeInTheDocument();
    expect(screen.getByText("7 - Debug")).toBeInTheDocument();
  });

  it("should have default value of 1 (Alert)", () => {
    render(
      <Provider store={store}>
        <AlertLevelSetting />
      </Provider>
    );
    
    const select = screen.getByRole("combobox");
    expect(select).toHaveValue("1");
  });

  it("should call the API when save button is clicked", async () => {
    mockUpdateGlobalSettings.mockResolvedValue({ unwrap: () => Promise.resolve({}) });
    
    render(
      <Provider store={store}>
        <AlertLevelSetting />
      </Provider>
    );
    
    const saveButton = screen.getByRole("button", { name: /save/i });
    fireEvent.click(saveButton);
    
    expect(mockUpdateGlobalSettings).toHaveBeenCalledWith({
      alert_level: 1
    });
  });

  it("should update selected level when option is changed", () => {
    render(
      <Provider store={store}>
        <AlertLevelSetting />
      </Provider>
    );
    
    const select = screen.getByRole("combobox");
    fireEvent.mouseDown(select);
    
    const criticalOption = screen.getByText("2 - Critical");
    fireEvent.click(criticalOption);
    
    expect(select).toHaveValue("2");
  });
});
