import { render, screen, waitFor } from "@testing-library/react";
import AnomaliesPage from "../pages/dashboard/AnomaliesPage";
import { Provider } from "react-redux";
import { store } from "../app/store";
import { expect, it, vi } from "vitest";
vi.mock("rc-resize-observer");
vi.mock("react-apexcharts");

it("renders the AnomaliesPage without errors", () => {
  const { container } = render(
    <Provider store={store}>
      <AnomaliesPage />
    </Provider>
  );
  expect(container).toBeTruthy();
});

it("renders anomaly data after loading", async () => {
  render(
    <Provider store={store}>
      <AnomaliesPage />
    </Provider>
  );

  await waitFor(() => {
    const anomalyData = screen.getByText((content, element) => {
      const hasText = (node) => node.textContent === "Anomaly Stats and Report";
      const nodeHasText = hasText(element);
      const childrenDontHaveText = !Array.from(element.children).some(hasText);
      return nodeHasText && childrenDontHaveText;
    });
    expect(anomalyData).toBeInTheDocument();
  });
});
it('renders "No anomaly services found" message when no data is available', async () => {
  const anomalyApi = await import('../app/services/anomalyApi');

  vi.spyOn(anomalyApi, 'useGetAnomalyStatisticsQuery').mockReturnValueOnce({
    data: [],
    isLoading: false,
  });
  vi.spyOn(anomalyApi, 'useGetAnomalyReportsAllQuery').mockReturnValueOnce({
    data: [],
    isLoading: false,
  });

  render(
    <Provider store={store}>
      <AnomaliesPage />
    </Provider>
  );

  await waitFor(() => {
    const noServicesMessage = screen.getByText('No anomaly services found, Please run anomaly services');
    expect(noServicesMessage).toBeInTheDocument();
  });
});