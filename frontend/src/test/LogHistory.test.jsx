import { it, describe, expect } from "vitest";
import { render } from "@testing-library/react";
import LogHistory from "../components/dashboard/LogHistory";
import { Provider } from "react-redux";
import { store } from "../app/store";
describe('LogHistory component', () => {
  const logsData = [
    {
      id: 1,
      since: "2024-05-01T08:00:00.000Z",
      total_count: 10,
      source: "Source 1",
      error_messages: ["Error 1"],
      anomaly_messages: ["Anomaly 1"],
    },
    {
      id: 2,
      since: "2024-05-02T08:00:00.000Z",
      total_count: 20,
      source: "Source 2",
      error_messages: ["Error 2"],
      anomaly_messages: ["Anomaly 2"],
    },
  ];

  it('renders without crashing', () => {
    render(<Provider store={store}><LogHistory logsData={logsData} /></Provider>);
  });

  it('renders columns correctly', () => {
    const { getByText } = render(<Provider store={store}><LogHistory logsData={logsData} /></Provider>);
    expect(getByText('Time')).toBeInTheDocument();
    expect(getByText('Total count')).toBeInTheDocument();
    expect(getByText('Source')).toBeInTheDocument();
  });

  it('displays header title correctly', () => {
    const { getByText } = render(<Provider store={store}><LogHistory logsData={logsData} /></Provider>);
    expect(getByText('Anomaly detect history')).toBeInTheDocument();
  });
});
