import { render, waitFor, screen } from "@testing-library/react";
import IdpsPage from "../pages/dashboard/IdpsPage";
import { Provider } from "react-redux";
import { store } from "../app/store";
import { expect, it, vi } from "vitest";

it("renders the IdpsPage without errors", () => {
  const { container } = render(
    <Provider store={store}>
      <IdpsPage />
    </Provider>
  );
  expect(container).toBeTruthy();
});

  it('renders "No IDPS services found" message when no data is available', async () => {
    const idpsApiModule = await import('../app/services/idpsApi');
    vi.spyOn(idpsApiModule, 'useGetIdpsReportQuery').mockReturnValueOnce({
      data: { idp_client: [], report: {} },
      isLoading: false,
    });

    render(<Provider store={store}>
      <IdpsPage />
    </Provider>);
    await waitFor(() => {
      const noServicesMessage = screen.getByText('No IDPS services found, Please run IDPS services');
      expect(noServicesMessage).toBeInTheDocument();
      const paginationElement = screen.queryByText(/1 of/); 
      expect(paginationElement).toBeNull();
        });
  });
