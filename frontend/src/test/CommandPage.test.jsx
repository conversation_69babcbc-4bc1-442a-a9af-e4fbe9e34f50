import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { Provider } from "react-redux";
import CommandPage from "../pages/command/CommandPage";
import { store } from "../app/store";
import {
  useGetAllCommandsQuery,
  useSendCommandMutation,
} from "../app/services/commandApi";
import { App } from "antd";
import { loginUser } from "../features/auth/userAuthSlice";

// Mock Ant Design's App Context at the top level
vi.mock("antd", async () => {
  const actual = await vi.importActual("antd");
  return {
    ...actual,
    App: {
      ...actual.App,
      useApp: vi.fn().mockReturnValue({
        notification: {
          success: vi.fn(),
          error: vi.fn(),
        },
      }),
    },
  };
});

// Mock the API hooks
vi.mock("../app/services/commandApi", () => ({
  useGetAllCommandsQuery: vi.fn(),
  useSendCommandMutation: vi.fn(),
}));

describe("CommandPage Component", () => {
  const mockRefetch = vi.fn();
  const mockSendCommand = vi.fn();
  const mockNotificationSuccess = vi.fn();
  const mockNotificationError = vi.fn();

  beforeEach(() => {
    useGetAllCommandsQuery.mockReturnValue({
      data: [
        {
          cmd_key: "@1",
          command: "beep 00-60-E9-C7-0B-57",
          status: "ok",
          timestamp: "2024-08-21T10:20:30Z",
          result: "",
          client: "client1",
          index: 0,
          name: "Client 1",
        },
      ],
      refetch: mockRefetch,
    });

    useSendCommandMutation.mockReturnValue([mockSendCommand]);

    // Set up notification mocks
    const { notification } = App.useApp();
    notification.success = mockNotificationSuccess;
    notification.error = mockNotificationError;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("authenticates and renders the CommandPage with table and data", async () => {
    await store.dispatch(loginUser({ user: "admin", password: "default" }));

    render(
      <Provider store={store}>
        <CommandPage />
      </Provider>
    );

    expect(await screen.findByText("Command List")).toBeInTheDocument();
  });

  it("filters commands based on search input", () => {
    render(
      <Provider store={store}>
        <CommandPage />
      </Provider>
    );
  
    const searchInput = screen.getByRole("textbox"); 
  
    fireEvent.change(searchInput, { target: { value: "beep" } });
  
    expect(screen.getByText("beep 00-60-E9-C7-0B-57")).toBeInTheDocument();
  });

  it("deletes a command and displays success notification", async () => {
    render(
      <Provider store={store}>
        <CommandPage />
      </Provider>
    );

    const deleteButton = screen.getAllByLabelText("delete cmd")[0];
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(mockNotificationSuccess).toHaveBeenCalledWith({
        message: "successfully deleted command!",
      });
    });
  });
  it("reruns a command and displays success notification", async () => {
    render(
      <Provider store={store}>
        <CommandPage />
      </Provider>
    );

    const rerunButton = screen.getAllByLabelText("rerun cmd")[0];
    fireEvent.click(rerunButton);

    await waitFor(() => {
      expect(mockNotificationSuccess).toHaveBeenCalledWith({
        message: "successfully sent command to rerun!",
      });
    });
  });
  it("refetches data when reload button is clicked", () => {
    render(
      <Provider store={store}>
        <CommandPage />
      </Provider>
    );
  
    const reloadButton = screen.getByLabelText("reload");
    fireEvent.click(reloadButton);
  
    expect(mockRefetch).toHaveBeenCalled();
  });
});
