import FirmwareDrawer from "../components/drawer/FirmwareDrawer";
import { it, describe } from "vitest";
import { fireEvent, render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../app/store";

describe("FirmwareDrawer", () => {
  it("Check if the FirmwareDrawer component render", () => {
    render(
      <Provider store={store}>
        <FirmwareDrawer />{" "}
      </Provider>
    );
  });

  it("Check save button ", () => {
    const { getByText } = render(
      <Provider store={store}>
        <FirmwareDrawer />{" "}
      </Provider>
    );
    const saveButton = getByText("save");
    fireEvent.click(saveButton);
  });

  it("Check cancel button ", () => {
    const { getByText } = render(
      <Provider store={store}>
        <FirmwareDrawer />{" "}
      </Provider>
    );
    const cancelButton = getByText("cancel");
    fireEvent.click(cancelButton);
  });
});
