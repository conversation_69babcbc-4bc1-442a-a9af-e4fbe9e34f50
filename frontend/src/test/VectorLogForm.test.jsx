import { render, screen, fireEvent } from "@testing-library/react";
import '@testing-library/jest-dom/extend-expect'; 

import VectorLogForm from "../components/VectorLogForm";

describe("VectorLogForm", () => {
  const mockOnCreate = vi.fn();
  const mockOnEdit = vi.fn();
  const mockOnCancel = vi.fn();

  const initialFormValues = {
    text: "Sample Log",
    normal: true,
    comment: "Sample Comment",
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders form correctly", () => {
    render(
      <VectorLogForm
        open={true}
        onCreate={mockOnCreate}
        onEdit={mockOnEdit}
        onCancel={mockOnCancel}
        initialValues={initialFormValues}
        loadingSave={false}
        isEdit={false}
      />
    );

    expect(screen.getByLabelText("Log")).toBeInTheDocument();
    expect(screen.getByLabelText("Normal")).toBeInTheDocument();
    expect(screen.getByLabelText("Comment")).toBeInTheDocument();
  });

  it("calls onCancel when the modal is closed", () => {
    render(
      <VectorLogForm
        open={true}
        onCreate={mockOnCreate}
        onEdit={mockOnEdit}
        onCancel={mockOnCancel}
        initialValues={initialFormValues}
        loadingSave={false}
        isEdit={true}
      />
    );

    fireEvent.click(screen.getByText("CANCEL"));

    expect(mockOnCancel).toHaveBeenCalled();
  });
});
