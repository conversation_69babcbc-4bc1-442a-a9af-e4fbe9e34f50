import { render } from '@testing-library/react';
import AnomalySettingStats from '../components/dashboard/AnomalySettingStats';
import { describe, expect, it, vi } from 'vitest';
import { Provider } from 'react-redux';
import { store } from '../app/store';
vi.mock("rc-resize-observer");
vi.mock("react-apexcharts");

describe('renders component', () => {
    it("renders the AnomalySettingStats without errors", () => {
        const { container } = render(
            <Provider store={store}>
              <AnomalySettingStats />
            </Provider>
          );
            expect(container).toBeTruthy();
      });
});
