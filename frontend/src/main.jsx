import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.jsx";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { store } from "./app/store";
import dayjs from "dayjs";
import { addServiceWorkerErrorHandlers } from "./utils/serviceWorkerUtils.js";

import "antd/dist/reset.css";
import "./index.css";
import "react-contexify/dist/ReactContexify.css";
import "highlight.js/styles/monokai.css";

dayjs.locale("en");

// Add service worker error handlers to prevent async message errors
addServiceWorkerErrorHandlers();

// Clean up any existing service workers in development
if (process.env.NODE_ENV === "development") {
  import("./utils/serviceWorkerUtils.js").then(({ cleanupServiceWorkers }) => {
    cleanupServiceWorkers()
      .then(() => {
        console.log("🧹 Development: Service workers cleaned up");
      })
      .catch((error) => {
        console.warn("⚠️ Could not clean up service workers:", error);
      });
  });
}

// Additional debugging for the message channel error
if (process.env.NODE_ENV === "development") {
  // Override console.error to catch the specific error
  const originalConsoleError = console.error;
  console.error = function (...args) {
    const errorMessage = args.join(" ");
    if (
      errorMessage.includes(
        "A listener indicated an asynchronous response by returning true"
      )
    ) {
      console.warn("🔍 DEBUGGING: Message channel error detected!");
      console.warn("🔍 This might be caused by:");
      console.warn("🔍 1. Browser extensions (React DevTools, etc.)");
      console.warn("🔍 2. Service worker registration issues");
      console.warn("🔍 3. Chrome extension APIs being called");
      console.warn("🔍 Stack trace:", new Error().stack);

      // Still log the original error but with context
      originalConsoleError.apply(console, ["🔍 ORIGINAL ERROR:", ...args]);
      return;
    }
    // Call original console.error for other errors
    originalConsoleError.apply(console, args);
  };
}

if (process.env.NODE_ENV === "production") {
  console.log = function () {};
}

ReactDOM.createRoot(document.getElementById("root")).render(
  <Provider store={store}>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </Provider>
);
