import {
  createAsyncThunk,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import protectedApis from "../../utils/apis/protectedApis";

export const RequestDeviceTrapSetting = createAsyncThunk(
  "singleTrapSetting/RequestDeviceTrapSetting",
  async (params, thunkAPI) => {
    try {
      const response = await protectedApis.post("/api/v1/commands", [
        {
          command: `snmp trap add ${params.mac_address} ${params.serverIP} ${params.serverPort} ${params.comString}`,
        },
      ]);
      const data = await response.data;
      let responseResult = data;
      if (response.status === 200) {
        return responseResult;
      } else {
        return thunkAPI.rejectWithValue("Config trap device failed !");
      }
    } catch (e) {
      if (e.response && e.response.statusText !== "") {
        return thunkAPI.rejectWithValue(e.response.statusText);
      } else return thunkAPI.rejectWithValue(e.message);
    }
  }
);

const singleTrapSetting = createSlice({
  name: "singleTrapSetting",
  initialState: {
    visible: false,
    mac_address: "",
    model: "",
    trapData: [],
    trapSettingStatus: "in_progress",
    errorTrapSetting: "",
    resultCommand: [],
  },
  reducers: {
    openTrapSettingDrawer: (state, { payload }) => {
      state.mac_address = payload.mac;
      state.model = payload.modelname;
      state.trapData = payload.trapSetting || [];
      state.visible = true;
    },
    closeTrapSettingDrawer: (state, { payload }) => {
      state.visible = false;
      state.mac_address = "";
      state.trapData = [];
      state.model = "";
    },
    clearTrapData: (state) => {
      state.trapSettingStatus = "in_progress";
      state.errorTrapSetting = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(RequestDeviceTrapSetting.fulfilled, (state, { payload }) => {
        state.trapSettingStatus = "success";
        state.errorTrapSetting = "Config device trap success !";
        state.resultCommand = Object.keys(payload);
      })
      .addCase(RequestDeviceTrapSetting.pending, (state, { payload }) => {
        state.trapSettingStatus = "in_progress";
        state.errorTrapSetting = "";
        state.resultCommand = [];
      })
      .addCase(RequestDeviceTrapSetting.rejected, (state, { payload }) => {
        state.trapSettingStatus = "failed";
        state.errorTrapSetting = payload;
        state.resultCommand = [];
      });
  },
});

export const singleTrapSettingSelector = createSelector(
  (state) => state.singleTrapSetting,
  ({
    visible,
    mac_address,
    model,
    trapData,
    trapSettingStatus,
    errorTrapSetting,
    resultCommand,
  }) => ({
    visible,
    mac_address,
    model,
    trapData,
    trapSettingStatus,
    errorTrapSetting,
    resultCommand,
  })
);

export const { clearTrapData, closeTrapSettingDrawer, openTrapSettingDrawer } =
  singleTrapSetting.actions;

export default singleTrapSetting;
