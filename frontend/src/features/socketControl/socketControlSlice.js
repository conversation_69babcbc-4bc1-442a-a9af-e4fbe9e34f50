import {
  createAsyncThunk,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import { showFirmwareNotification } from "../eventLog/eventLogSlice";
import { getTopologyData } from "../topology/topologySlice";
import { setMessage } from "./licenseAlertSlice";
import protectedApis from "../../utils/apis/protectedApis";
import { api } from "../../app/services/api";

export const extractSocketResult = (results, handleDevicePolling) => {
  return async (dispatch) => {
    const res = JSON.parse(results);
    let resultData = {
      title: res.kind,
      message: res.message,
      time_stamp: Date.now(),
    };
    if (res.message.includes("devicePolling")) {
      dispatch(api.util.invalidateTags(["devices"]));
      dispatch(api.util.invalidateTags(["groups"]));
      dispatch(api.util.invalidateTags(["customTopology"]));
      dispatch(api.util.invalidateTags(["customTopologyConnections"]));
      dispatch(getTopologyData());
    }
    if (res.message.includes("firmware:"))
      dispatch(showFirmwareNotification(resultData));
    if (res.message.includes("InsertTopo:")) {
      dispatch(getTopologyData());
    } else if (res.kind.includes("topology_links_updated")) {
      // Topology links have been updated due to device status changes
      // This also covers custom topology since connections are now dynamically computed from QC.TopologyData
      dispatch(getTopologyData());
      dispatch(api.util.invalidateTags(["customTopology"]));
      dispatch(api.util.invalidateTags(["customTopologyConnections"]));
    } else if (res.kind.includes("device_status_changed")) {
      // Device status has changed - refresh topology and device data
      dispatch(getTopologyData());
      dispatch(api.util.invalidateTags(["customTopology"]));
      dispatch(api.util.invalidateTags(["devices"]));
    } else if (res.kind.includes("license_alert")) {
      dispatch(setMessage(res));
    } else if (res.message.includes("license error")) {
      return;
    } else {
      dispatch(setSocketResultData(resultData));
      handleDevicePolling(res.message);
    }
  };
};

export const getInitialAlertData = createAsyncThunk(
  "socketControlSlice/getInitialAlertData",
  async (_, thunkAPI) => {
    try {
      const response = await protectedApis.get("/api/v1/syslogs/alert");
      const data = await response.data;
      if (response.status === 200) {
        return data;
      }
    } catch (e) {
      if (e.response && e.response.statusText !== "") {
        return thunkAPI.rejectWithValue(e.response.statusText);
      } else return thunkAPI.rejectWithValue(e.message);
    }
  }
);

const SocketControlSlice = createSlice({
  name: "socketControlSlice",
  initialState: {
    socketErrorMsg: "",
    socketLoading: false,
    fetchingInitalAlertData: false,
    socketResultData:
      JSON.parse(sessionStorage.getItem("socketmessage")) === null
        ? []
        : JSON.parse(sessionStorage.getItem("socketmessage")),
  },
  reducers: {
    setSocketResultData: (state, { payload }) => {
      state.socketResultData = [payload, ...state.socketResultData].slice(
        0,
        30
      );
      sessionStorage.setItem(
        "socketmessage",
        JSON.stringify(state.socketResultData)
      );
    },
    setSocketErrorMessage: (state, { payload }) => {
      state.socketErrorMsg = payload;
    },
    setSocketLoading: (state, { payload }) => {
      state.socketLoading = payload;
    },
    clearSocketResultData: (state) => {
      state.socketResultData = [];
      sessionStorage.setItem(
        "socketmessage",
        JSON.stringify(state.socketResultData)
      );
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getInitialAlertData.fulfilled, (state, { payload }) => {
        state.socketResultData = (payload || [])
          .map((item) => ({
            title: item.kind,
            message: item.message,
            time_stamp: new Date(item.timestamp)
              .toUTCString()
              .replace("GMT", ""),
          }))
          .sort((a, b) => b.time_stamp - a.time_stamp);
        state.fetchingInitalAlertData = false;
      })
      .addCase(getInitialAlertData.pending, (state) => {
        state.fetchingInitalAlertData = true;
      })
      .addCase(getInitialAlertData.rejected, (state, { payload }) => {
        state.fetchingInitalAlertData = false;
      });
  },
});

export const {
  setSocketResultData,
  clearSocketResultData,
  setSocketErrorMessage,
  setSocketLoading,
} = SocketControlSlice.actions;

export const socketControlSelector = createSelector(
  (state) => state.socketControl,
  ({
    socketResultData,
    socketErrorMsg,
    socketLoading,
    fetchingInitalAlertData,
  }) => ({
    socketResultData,
    socketErrorMsg,
    socketLoading,
    fetchingInitalAlertData,
  })
);

export default SocketControlSlice;
