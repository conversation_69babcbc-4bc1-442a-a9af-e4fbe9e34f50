import { createSelector, createSlice } from "@reduxjs/toolkit";

const licenseAlertSlice = createSlice({
  name: "licenseAlert",
  initialState: {
    licenseErrorMsg: "",
    showLicenseError: false,
    showLicenseWaterMark: false,
    featureEnabled: [],
  },
  reducers: {
    setMessage: (state, { payload }) => {
      switch (payload.kind) {
        case "license_alert_features":
          state.featureEnabled = payload.message.split(",");
          break;
        case "license_alert_error":
          if (state.licenseErrorMsg === "") {
            state.licenseErrorMsg = payload.message;
            state.showLicenseError = true;
            state.showLicenseWaterMark = true;
          }
          break;
        case "license_alert_clear":
          state.licenseErrorMsg = "";
          state.showLicenseError = false;
          state.showLicenseWaterMark = false;
          break;

        default:
          state.showLicenseError = false;
          break;
      }
    },
    setFeatueEnabled: (state, { payload }) => {
      state.featureEnabled = payload?.license?.enabledFeatures?.split(",");
    },
  },
});

export const { setMessage, setFeatueEnabled } = licenseAlertSlice.actions;

export const licenseAlertSelector = createSelector(
  (state) => state.licenseAlert,
  ({
    licenseErrorMsg,
    showLicenseError,
    showLicenseWaterMark,
    featureEnabled,
  }) => ({
    licenseErrorMsg,
    showLicenseError,
    showLicenseWaterMark,
    featureEnabled,
  })
);

export default licenseAlertSlice;
