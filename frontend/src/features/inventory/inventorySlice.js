import {
  createSlice,
  createAsyncThunk,
  createSelector,
} from "@reduxjs/toolkit";
import protectedApis from "../../utils/apis/protectedApis";
import { checkTimestampDiff } from "../../utils/comman/dataMapping";

const sleep = (ms) => new Promise((r) => setTimeout(r, ms));

export const getInventoryData = createAsyncThunk(
  "inventorySlice/getInventoryData",
  async (_, thunkAPI) => {
    try {
      const response = await protectedApis.get("/api/v1/devices");
      const data = await response.data;
      let responseResult = data;
      if (response.status === 200) {
        await sleep(2000);
        return responseResult;
      }
    } catch (e) {
      if (e.response && e.response.statusText !== "") {
        return thunkAPI.rejectWithValue(e.response.statusText);
      } else return thunkAPI.rejectWithValue(e.message);
    }
  }
);

const inventorySlice = createSlice({
  name: "inventorySlice",
  initialState: {
    deviceData: [],
    scanning: false,
    selectedRowKeys: [],
    mdrDeviceData: [],
    deviceDataToEdit: null,
    openDeviceEditModel: false,
    // Enhanced selected states management
    selectedDevicesByType: {
      device: [],
      mdr: [],
    },
    // Scanning states
    isScanning: false,
    scanTimeRemaining: 0,
    scanTimerInterval: null,
    scanTimeoutId: null,
    pollingInventory: 0,
    scanStartTime: null,
    scanTotalDuration: 0,
    // UI states
    inventoryType: "device",
    searchFilter: "",
    // Persistence states
    lastSelectedType: "device",
    selectionHistory: [],
  },
  reducers: {
    clearInventoryData: (state) => {
      state.deviceData = [];
      state.scanning = false;
    },
    setSelectedRowKeys: (state, { payload }) => {
      state.selectedRowKeys = payload;
      // Also update type-specific selections
      if (state.inventoryType) {
        state.selectedDevicesByType[state.inventoryType] = payload;
      }
      // Add to selection history
      if (payload.length > 0) {
        state.selectionHistory.push({
          type: state.inventoryType,
          keys: [...payload],
          timestamp: Date.now(),
        });
        // Keep only last 10 selections
        if (state.selectionHistory.length > 10) {
          state.selectionHistory = state.selectionHistory.slice(-10);
        }
      }
    },
    setDeviceDataToEdit: (state, { payload }) => {
      state.deviceDataToEdit = payload;
      state.openDeviceEditModel = true;
    },
    clearDeviceDataToEdit: (state, { payload }) => {
      state.openDeviceEditModel = false;
      state.deviceDataToEdit = null;
    },
    // Enhanced selected states management
    setInventoryType: (state, { payload }) => {
      // Save current selections before switching
      if (state.inventoryType && state.selectedRowKeys.length > 0) {
        state.selectedDevicesByType[state.inventoryType] = [
          ...state.selectedRowKeys,
        ];
      }

      state.lastSelectedType = state.inventoryType;
      state.inventoryType = payload;

      // Restore selections for the new type
      state.selectedRowKeys = state.selectedDevicesByType[payload] || [];
    },
    clearSelectedRowKeys: (state) => {
      state.selectedRowKeys = [];
      if (state.inventoryType) {
        state.selectedDevicesByType[state.inventoryType] = [];
      }
    },
    restoreLastSelection: (state) => {
      if (state.selectionHistory.length > 0) {
        const lastSelection =
          state.selectionHistory[state.selectionHistory.length - 1];
        state.selectedRowKeys = [...lastSelection.keys];
        state.inventoryType = lastSelection.type;
        state.selectedDevicesByType[lastSelection.type] = [
          ...lastSelection.keys,
        ];
      }
    },
    // Scanning states management
    setIsScanning: (state, { payload }) => {
      state.isScanning = payload;
    },
    setScanTimeRemaining: (state, { payload }) => {
      state.scanTimeRemaining = payload;
    },
    setScanTimerInterval: (state, { payload }) => {
      state.scanTimerInterval = payload;
    },
    setScanTimeoutId: (state, { payload }) => {
      state.scanTimeoutId = payload;
    },
    setPollingInventory: (state, { payload }) => {
      state.pollingInventory = payload;
    },
    startScan: (state, { payload }) => {
      const { totalDurationMs } = payload;
      // Always restart the scan with new duration (allows multiple scan requests)
      state.isScanning = true;
      state.scanStartTime = Date.now();
      state.scanTotalDuration = totalDurationMs;
      state.scanTimeRemaining = Math.ceil(totalDurationMs / 1000);
      state.pollingInventory = 60000;
    },
    updateScanTimer: (state) => {
      if (state.isScanning && state.scanStartTime) {
        const elapsed = Date.now() - state.scanStartTime;
        const remaining = Math.max(
          0,
          Math.ceil((state.scanTotalDuration - elapsed) / 1000)
        );
        state.scanTimeRemaining = remaining;

        if (remaining <= 0) {
          state.isScanning = false;
          state.scanTimeRemaining = 0;
          state.pollingInventory = 0;
          state.scanStartTime = null;
          state.scanTotalDuration = 0;
        }
      }
    },
    // UI states management
    setSearchFilter: (state, { payload }) => {
      state.searchFilter = payload;
    },
    // Bulk operations
    clearAllSelections: (state) => {
      state.selectedRowKeys = [];
      state.selectedDevicesByType = {
        device: [],
        mdr: [],
      };
      state.selectionHistory = [];
    },
    resetScanningStates: (state) => {
      state.isScanning = false;
      state.scanTimeRemaining = 0;
      state.scanTimerInterval = null;
      state.scanTimeoutId = null;
      state.pollingInventory = 0;
      state.scanStartTime = null;
      state.scanTotalDuration = 0;
    },
    clearExistingScan: (state) => {
      // Clear existing scan states but don't reset polling immediately
      // This allows for a smoother transition when starting a new scan
      state.isScanning = false;
      state.scanTimeRemaining = 0;
      state.scanTimerInterval = null;
      state.scanTimeoutId = null;
      state.scanStartTime = null;
      state.scanTotalDuration = 0;
      // Keep pollingInventory active briefly for transition
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getInventoryData.fulfilled, (state, { payload }) => {
        const allDevices = checkTimestampDiff(Object.values(payload));
        state.deviceData = allDevices;
        state.mdrDeviceData = checkTimestampDiff(
          Object.values(payload).filter(
            (item) => item.type === "motor-ctrl-card"
          )
        );
        state.scanning = false;

        // Clean up selected keys that no longer exist in the data
        const currentDeviceKeys = allDevices.map((device) => device.mac);
        const currentMdrKeys = state.mdrDeviceData.map((device) => device.mac);

        // Clean device selections
        state.selectedDevicesByType.device =
          state.selectedDevicesByType.device.filter((key) =>
            currentDeviceKeys.includes(key)
          );

        // Clean MDR selections
        state.selectedDevicesByType.mdr =
          state.selectedDevicesByType.mdr.filter((key) =>
            currentMdrKeys.includes(key)
          );

        // Update current selectedRowKeys if they're for the current inventory type
        if (state.inventoryType === "device") {
          state.selectedRowKeys = state.selectedDevicesByType.device;
        } else if (state.inventoryType === "mdr") {
          state.selectedRowKeys = state.selectedDevicesByType.mdr;
        }
      })
      .addCase(getInventoryData.pending, (state, { payload }) => {
        state.scanning = true;
      })
      .addCase(getInventoryData.rejected, (state, { payload }) => {
        state.scanning = false;
      });
  },
});

export const {
  clearInventoryData,
  setSelectedRowKeys,
  setDeviceDataToEdit,
  clearDeviceDataToEdit,
  // Enhanced selected states management
  setInventoryType,
  clearSelectedRowKeys,
  restoreLastSelection,
  // Scanning states management
  setIsScanning,
  setScanTimeRemaining,
  setScanTimerInterval,
  setScanTimeoutId,
  setPollingInventory,
  startScan,
  updateScanTimer,
  // UI states management
  setSearchFilter,
  // Bulk operations
  clearAllSelections,
  resetScanningStates,
  clearExistingScan,
} = inventorySlice.actions;

export const inventorySliceSelector = createSelector(
  (state) => state.inventory,
  ({
    deviceData,
    scanning,
    selectedRowKeys,
    mdrDeviceData,
    deviceDataToEdit,
    openDeviceEditModel,
    // Enhanced selected states
    selectedDevicesByType,
    // Scanning states
    isScanning,
    scanTimeRemaining,
    scanTimerInterval,
    scanTimeoutId,
    pollingInventory,
    scanStartTime,
    scanTotalDuration,
    // UI states
    inventoryType,
    searchFilter,
    // Persistence states
    lastSelectedType,
    selectionHistory,
  }) => ({
    deviceData,
    scanning,
    selectedRowKeys,
    mdrDeviceData,
    deviceDataToEdit,
    openDeviceEditModel,
    // Enhanced selected states
    selectedDevicesByType,
    // Scanning states
    isScanning,
    scanTimeRemaining,
    scanTimerInterval,
    scanTimeoutId,
    pollingInventory,
    scanStartTime,
    scanTotalDuration,
    // UI states
    inventoryType,
    searchFilter,
    // Persistence states
    lastSelectedType,
    selectionHistory,
  })
);

export default inventorySlice;
