import { createSlice } from "@reduxjs/toolkit";

// Helper function to generate unique IDs
const generateId = () =>
  `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Initial tree data with some sample nodes
const initialTreeData = [
  {
    id: "node_1",
    name: "Node 1",
    type: "node",
    children: [
      {
        id: "subnode_1_1",
        name: "Sub-node 1.1",
        type: "node",
        children: [
          {
            id: "device_1_1_1",
            name: "Device 1.1.1",
            type: "device",
            ipAddress: "***********",
            macAddress: "00:11:22:33:44:55",
          },
          {
            id: "device_1_1_2",
            name: "Device 1.1.2",
            type: "device",
            ipAddress: "***********",
            macAddress: "00:11:22:33:44:56",
          },
        ],
      },
      {
        id: "device_1_2",
        name: "Device 1.2",
        type: "device",
        ipAddress: "***********",
        macAddress: "00:11:22:33:44:57",
      },
    ],
  },
  {
    id: "node_2",
    name: "Node 2",
    type: "node",
    children: [
      {
        id: "device_2_1",
        name: "Device 2.1",
        type: "device",
        ipAddress: "***********",
        macAddress: "00:11:22:33:44:58",
      },
    ],
  },
];

// Recursive function to find a node by ID
const findNodeById = (nodes, id) => {
  for (const node of nodes) {
    if (node.id === id) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) return found;
    }
  }
  return null;
};

// Recursive function to remove a node by ID
const removeNodeById = (nodes, id) => {
  return nodes.filter((node) => {
    if (node.id === id) {
      return false;
    }
    if (node.children) {
      node.children = removeNodeById(node.children, id);
    }
    return true;
  });
};

// Function to get direct children (both devices and nodes) from a node
const getDirectChildren = (node) => {
  const children = [];

  if (node.type === "device") {
    // A device has no children
    return children;
  } else if (node.children) {
    // Return all direct children (devices and nodes)
    return [...node.children];
  }

  return children;
};

// Function to get all children recursively (for full hierarchy topology)
const getAllChildren = (node) => {
  const allChildren = [];

  if (node.type === "device") {
    // A device has no children
    return allChildren;
  } else if (node.children) {
    // Add direct children
    node.children.forEach((child) => {
      allChildren.push(child);
      // Recursively add children of sub-nodes
      if (child.type === "node") {
        allChildren.push(...getAllChildren(child));
      }
    });
  }

  return allChildren;
};

// Generate hub-and-spoke topology connections where all children connect to the selected node
const generateHubSpokeConnections = (centralNode, children) => {
  const connections = [];

  children.forEach((child) => {
    connections.push({
      id: `${centralNode.id}-${child.id}`,
      source: centralNode.id,
      target: child.id,
      sourcePort: "eth0",
      targetPort: "eth0",
      linkType: "solid",
      blockedPort: "false",
    });
  });

  return connections;
};

// Default display settings
const defaultDisplaySettings = {
  // Device display settings
  deviceName: true,
  ipAddress: true,
  macAddress: false,
  deviceModel: false,
  status: true,
  portInfo: false,
  // Connection display settings
  connectionLabels: false,
  connectionPorts: true,
  connectionStatus: true,
  inactiveConnections: true,
  hierarchicalConnections: true,
  lldpConnections: true,
};

// Preset configurations
export const displayPresets = {
  minimal: {
    deviceName: true,
    ipAddress: false,
    macAddress: false,
    deviceModel: false,
    status: false,
    portInfo: false,
    connectionLabels: false,
    connectionPorts: false,
    connectionStatus: false,
    inactiveConnections: true,
    hierarchicalConnections: true,
    lldpConnections: true,
  },
  standard: {
    deviceName: true,
    ipAddress: true,
    macAddress: false,
    deviceModel: false,
    status: false,
    portInfo: false,
    connectionLabels: true,
    connectionPorts: true,
    connectionStatus: true,
    inactiveConnections: true,
    hierarchicalConnections: true,
    lldpConnections: true,
  },
  detailed: {
    deviceName: true,
    ipAddress: true,
    macAddress: true,
    deviceModel: false,
    status: false,
    portInfo: false,
    connectionLabels: true,
    connectionPorts: true,
    connectionStatus: true,
    inactiveConnections: true,
    hierarchicalConnections: true,
    lldpConnections: true,
  },
  security: {
    deviceName: true,
    ipAddress: false,
    macAddress: true,
    deviceModel: false,
    status: true,
    portInfo: false,
    connectionLabels: false,
    connectionPorts: false,
    connectionStatus: true,
    inactiveConnections: true,
    hierarchicalConnections: false,
    lldpConnections: true,
  },
  admin: {
    deviceName: true,
    ipAddress: true,
    macAddress: false,
    deviceModel: true,
    status: true,
    portInfo: false,
    connectionLabels: true,
    connectionPorts: true,
    connectionStatus: true,
    inactiveConnections: true,
    hierarchicalConnections: true,
    lldpConnections: true,
  },
  full: {
    deviceName: true,
    ipAddress: true,
    macAddress: true,
    deviceModel: true,
    status: true,
    portInfo: true,
    connectionLabels: true,
    connectionPorts: true,
    connectionStatus: true,
    inactiveConnections: true,
    hierarchicalConnections: true,
    lldpConnections: true,
  },
};

// Load display settings from localStorage
const loadDisplaySettings = () => {
  try {
    const saved = localStorage.getItem("customTopologyDisplaySettings");
    return saved
      ? { ...defaultDisplaySettings, ...JSON.parse(saved) }
      : defaultDisplaySettings;
  } catch {
    return defaultDisplaySettings;
  }
};

const customTopologySlice = createSlice({
  name: "customTopology",
  initialState: {
    treeData: [],
    selectedNodeId: null,
    selectedNode: null,
    expandedKeys: [],
    selectedKeys: [],
    topologyData: {
      nodes: [],
      edges: [],
    },
    // LLDP connections for the selected tree
    lldpConnections: [],
    displaySettings: loadDisplaySettings(),
    // Topology view expand/collapse state
    topologyExpandedNodes: [],
    allNodesExpanded: false,
  },
  reducers: {
    // Set tree data
    setTreeData: (state, action) => {
      const treeData = action.payload;
      if (!treeData) {
        state.treeData = [];
        state.selectedNodeId = null;
        state.selectedNode = null;
        state.expandedKeys = [];
        state.selectedKeys = [];
        state.topologyData = { nodes: [], edges: [] };
        return;
      }
      state.treeData = action.payload;
      localStorage.setItem(
        "customTopologyTree",
        JSON.stringify(action.payload)
      );

      // Auto-refresh topology if a node is currently selected
      if (state.selectedNodeId && state.selectedNode) {
        const updatedNode = findNodeById(state.treeData, state.selectedNodeId);
        if (updatedNode) {
          // Regenerate topology data for the currently selected node
          state.selectedNode = updatedNode;

          // Get all children at all levels for complete topology data
          const allChildren = getAllChildren(updatedNode);
          const directChildren = getDirectChildren(updatedNode);

          // Create topology nodes - include the selected node as central hub
          const nodes = [];

          // Add the selected/central node
          nodes.push({
            id: updatedNode.id,
            label: updatedNode.name,
            type: "image",
            img: "/device-icons/default.png",
            originalData: updatedNode,
            isSelected: true,
            style: {
              stroke: "#ff4d4f",
              lineWidth: 3,
            },
          });

          // Add all children to the topology data
          allChildren.forEach((child) => {
            nodes.push({
              id: child.id,
              label: child.name,
              type: "image",
              img: "/device-icons/default.png",
              originalData: child,
              isSelected: false,
              style:
                child.type === "device"
                  ? { stroke: "#52c41a", lineWidth: 2 }
                  : { stroke: "#1890ff", lineWidth: 2 },
            });
          });

          // Generate connections
          const edges = [];

          // Add hub-and-spoke connections for direct children
          const hubSpokeEdges = generateHubSpokeConnections(
            updatedNode,
            directChildren
          ).map((conn) => ({
            id: conn.id,
            source: conn.source,
            target: conn.target,
            label: `${conn.sourcePort} - ${conn.targetPort}`,
            type: "circle-running",
            originalData: conn,
          }));
          edges.push(...hubSpokeEdges);

          // Add parent-child connections for deeper levels
          const addDeepLevelConnections = (parentNode) => {
            if (parentNode.children) {
              parentNode.children.forEach((child) => {
                edges.push({
                  id: `${parentNode.id}-${child.id}`,
                  source: parentNode.id,
                  target: child.id,
                  label: "eth0 - eth0",
                  type: "circle-running",
                  originalData: {
                    id: `${parentNode.id}-${child.id}`,
                    source: parentNode.id,
                    target: child.id,
                    sourcePort: "eth0",
                    targetPort: "eth0",
                    linkType: "solid",
                    blockedPort: "false",
                  },
                });

                if (child.type === "node") {
                  addDeepLevelConnections(child);
                }
              });
            }
          };

          directChildren.forEach((child) => {
            if (child.type === "node") {
              addDeepLevelConnections(child);
            }
          });

          state.topologyData = { nodes, edges };
        }
      }
    },

    // Add a new node
    addNode: (state, action) => {
      const { parentId, nodeType, name } = action.payload;
      const newNode = {
        id: generateId(),
        name: name || (nodeType === "device" ? "New Device" : "New Node"),
        type: nodeType,
        children: nodeType === "device" ? undefined : [],
      };

      if (nodeType === "device") {
        newNode.ipAddress = "*************";
        newNode.macAddress = "00:00:00:00:00:00";
      }

      if (parentId) {
        const parentNode = findNodeById(state.treeData, parentId);
        if (parentNode && parentNode.children) {
          parentNode.children.push(newNode);
        }
      } else {
        // Add to root level
        state.treeData.push(newNode);
      }

      localStorage.setItem(
        "customTopologyTree",
        JSON.stringify(state.treeData)
      );
    },

    // Remove a node
    removeNode: (state, action) => {
      const nodeId = action.payload;
      state.treeData = removeNodeById(state.treeData, nodeId);

      // Clear selection if the removed node was selected
      if (state.selectedNodeId === nodeId) {
        state.selectedNodeId = null;
        state.selectedNode = null;
        state.topologyData = { nodes: [], edges: [] };
      }

      localStorage.setItem(
        "customTopologyTree",
        JSON.stringify(state.treeData)
      );
    },

    // Rename a node
    renameNode: (state, action) => {
      const { nodeId, newName } = action.payload;
      const node = findNodeById(state.treeData, nodeId);
      if (node) {
        node.name = newName;
        localStorage.setItem(
          "customTopologyTree",
          JSON.stringify(state.treeData)
        );
      }
    },

    // Update device properties
    updateDevice: (state, action) => {
      const { deviceId, properties } = action.payload;
      const device = findNodeById(state.treeData, deviceId);
      if (device && device.type === "device") {
        Object.assign(device, properties);
        localStorage.setItem(
          "customTopologyTree",
          JSON.stringify(state.treeData)
        );
      }
    },

    // Select a node and generate topology
    selectNode: (state, action) => {
      const nodeId = action.payload;
      const node = findNodeById(state.treeData, nodeId);

      state.selectedNodeId = nodeId;
      state.selectedNode = node;

      if (node) {
        // Get all children at all levels for complete topology data
        const allChildren = getAllChildren(node);
        const directChildren = getDirectChildren(node);

        // Create topology nodes - include the selected node as central hub
        const nodes = [];

        // Add the selected/central node
        nodes.push({
          id: node.id,
          label: node.name, // Just the name, no descriptors
          type: "image",
          img: "/device-icons/default.png", // Default icon
          originalData: node,
          isSelected: true, // Flag to identify as selected node
          style: {
            // Make the central node visually distinct
            stroke: "#ff4d4f",
            lineWidth: 3,
          },
        });

        // Add all children (at all levels) to the topology data
        // The TopologyView will handle filtering based on expand/collapse state
        allChildren.forEach((child) => {
          nodes.push({
            id: child.id,
            label: child.name, // Just the name, no descriptors
            type: "image",
            img: "/device-icons/default.png", // Default icon
            originalData: child,
            isSelected: false, // Flag to identify as child node
            style:
              child.type === "device"
                ? { stroke: "#52c41a", lineWidth: 2 } // Green for devices
                : { stroke: "#1890ff", lineWidth: 2 }, // Blue for sub-nodes
          });
        });

        // Generate connections:
        // 1. Direct children connect to central node (hub-and-spoke)
        // 2. Deeper level nodes connect to their immediate parents
        const edges = [];

        // Add hub-and-spoke connections for direct children
        const hubSpokeEdges = generateHubSpokeConnections(
          node,
          directChildren
        ).map((conn) => ({
          id: conn.id,
          source: conn.source,
          target: conn.target,
          label: `${conn.sourcePort} - ${conn.targetPort}`,
          type: "circle-running",
          originalData: conn,
        }));
        edges.push(...hubSpokeEdges);

        // Add parent-child connections for deeper levels
        const addDeepLevelConnections = (parentNode) => {
          if (parentNode.children) {
            parentNode.children.forEach((child) => {
              // Add connection from parent to child
              edges.push({
                id: `${parentNode.id}-${child.id}`,
                source: parentNode.id,
                target: child.id,
                label: "eth0 - eth0",
                type: "circle-running",
                originalData: {
                  id: `${parentNode.id}-${child.id}`,
                  source: parentNode.id,
                  target: child.id,
                  sourcePort: "eth0",
                  targetPort: "eth0",
                  linkType: "solid",
                  blockedPort: "false",
                },
              });

              // Recursively add connections for sub-nodes
              if (child.type === "node") {
                addDeepLevelConnections(child);
              }
            });
          }
        };

        // Add deep level connections for direct children that are nodes
        directChildren.forEach((child) => {
          if (child.type === "node") {
            addDeepLevelConnections(child);
          }
        });

        state.topologyData = { nodes, edges };
      } else {
        state.topologyData = { nodes: [], edges: [] };
      }
    },

    // Set expanded keys
    setExpandedKeys: (state, action) => {
      state.expandedKeys = action.payload;
    },

    // Set selected keys
    setSelectedKeys: (state, action) => {
      state.selectedKeys = action.payload;
    },

    // Reset to initial state
    resetTree: (state) => {
      state.treeData = initialTreeData;
      state.selectedNodeId = null;
      state.selectedNode = null;
      state.expandedKeys = [];
      state.selectedKeys = [];
      state.topologyData = { nodes: [], edges: [] };
      localStorage.setItem(
        "customTopologyTree",
        JSON.stringify(initialTreeData)
      );
    },

    // Restore tree data from JSON string (for import)
    restoreTreeData: (state, action) => {
      try {
        const parsedData = JSON.parse(action.payload);

        // Validate the data structure
        if (
          parsedData &&
          parsedData.treeData &&
          Array.isArray(parsedData.treeData)
        ) {
          state.treeData = parsedData.treeData;
          state.selectedNodeId = null;
          state.selectedNode = null;

          // Restore expanded/selected state if available (v2.0+ format)
          if (
            parsedData.version === "2.0" ||
            parseFloat(parsedData.version) >= 2.0
          ) {
            state.expandedKeys = parsedData.expandedKeys || [];
            state.selectedKeys = parsedData.selectedKeys || [];
            state.topologyExpandedNodes =
              parsedData.topologyExpandedNodes || [];
          } else {
            // Legacy format - reset expand state
            state.expandedKeys = [];
            state.selectedKeys = [];
            state.topologyExpandedNodes = [];
          }

          state.topologyData = { nodes: [], edges: [] };

          // Save to localStorage
          localStorage.setItem(
            "customTopologyTree",
            JSON.stringify(parsedData.treeData)
          );
        } else {
          throw new Error("Invalid data format");
        }
      } catch (error) {
        throw new Error("Failed to parse JSON data");
      }
    },

    // Load tree data from localStorage
    loadTreeData: (state) => {
      try {
        const storedData = localStorage.getItem("customTopologyTree");
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          state.treeData = parsedData;
          state.selectedNodeId = null;
          state.selectedNode = null;
          state.expandedKeys = [];
          state.selectedKeys = [];
          state.topologyData = { nodes: [], edges: [] };
        } else {
          throw new Error("No saved data found");
        }
      } catch (error) {
        throw new Error("Failed to load saved data");
      }
    },

    // Update display settings
    updateDisplaySettings: (state, action) => {
      state.displaySettings = {
        ...state.displaySettings,
        ...action.payload,
      };
      localStorage.setItem(
        "customTopologyDisplaySettings",
        JSON.stringify(state.displaySettings)
      );
    },

    // Reset display settings to default
    resetDisplaySettings: (state) => {
      state.displaySettings = defaultDisplaySettings;
      localStorage.setItem(
        "customTopologyDisplaySettings",
        JSON.stringify(defaultDisplaySettings)
      );
    },

    // Load real data from groups API
    loadRealGroupsData: (state, action) => {
      const groupsData = action.payload;

      // Transform groups data to tree structure
      const transformedData = groupsData.map((group) => ({
        id: `group_${group.name}`,
        name: group.name,
        type: "node",
        relmName: group.name,
        comment: group.comment,
        location: group.location,
        children:
          group.regions?.map((region) => ({
            id: `region_${group.name}_${region.name}`,
            name: region.name,
            type: "node",
            relmName: group.name,
            regionName: region.name,
            comment: region.comment,
            children:
              region.zones?.map((zone) => ({
                id: `zone_${group.name}_${region.name}_${zone.name}`,
                name: zone.name,
                type: "node",
                relmName: group.name,
                regionName: region.name,
                zoneName: zone.name,
                comment: zone.comment,
                children:
                  zone.subnets?.map((subnet) => ({
                    id: `subnet_${group.name}_${region.name}_${zone.name}_${subnet.name}`,
                    name: subnet.name,
                    type: "node",
                    relmName: group.name,
                    regionName: region.name,
                    zoneName: zone.name,
                    subnetName: subnet.name,
                    comment: subnet.comment,
                    children: [
                      // Add subnet groups as nodes
                      ...(subnet.subnet_groups?.map((subnetGroup) => ({
                        id: `subnetgroup_${group.name}_${region.name}_${zone.name}_${subnet.name}_${subnetGroup.name}`,
                        name: subnetGroup.name,
                        type: "node",
                        relmName: group.name,
                        regionName: region.name,
                        zoneName: zone.name,
                        subnetName: subnet.name,
                        subnetGroupName: subnetGroup.name,
                        comment: subnetGroup.comment,
                        children: [], // Devices will be loaded separately via API
                      })) || []),
                    ],
                  })) || [],
              })) || [],
          })) || [],
      }));

      state.treeData = transformedData;
      // Don't save to localStorage since this is API data
    },

    // Topology view expand/collapse actions
    toggleTopologyNodeExpand: (state, action) => {
      const nodeId = action.payload;
      const expandedIndex = state.topologyExpandedNodes.indexOf(nodeId);

      if (expandedIndex > -1) {
        // Remove from expanded nodes
        state.topologyExpandedNodes.splice(expandedIndex, 1);
      } else {
        // Add to expanded nodes
        state.topologyExpandedNodes.push(nodeId);
      }
    },

    expandAllTopologyNodes: (state, action) => {
      state.topologyExpandedNodes = action.payload || []; // Array of all expandable node IDs
      state.allNodesExpanded = true;
    },

    collapseAllTopologyNodes: (state) => {
      state.topologyExpandedNodes = [];
      state.allNodesExpanded = false;
    },

    resetTopologyExpandState: (state) => {
      state.topologyExpandedNodes = [];
      state.allNodesExpanded = false;
    },

    // LLDP Connection Management
    setLLDPConnections: (state, action) => {
      state.lldpConnections = action.payload || [];
    },

    addLLDPConnection: (state, action) => {
      const connection = action.payload;
      // Avoid duplicates based on connection ID
      const existingIndex = state.lldpConnections.findIndex(
        (c) => c.id === connection.id
      );
      if (existingIndex >= 0) {
        state.lldpConnections[existingIndex] = connection;
      } else {
        state.lldpConnections.push(connection);
      }
    },

    removeLLDPConnection: (state, action) => {
      const connectionId = action.payload;
      state.lldpConnections = state.lldpConnections.filter(
        (c) => c.id !== connectionId
      );
    },

    clearLLDPConnections: (state) => {
      state.lldpConnections = [];
    },

    // Update node with real device data
    updateNodeDevices: (state, action) => {
      const { nodeId, devices } = action.payload;
      const node = findNodeById(state.treeData, nodeId);

      if (node && devices) {
        // Transform device data to match our structure
        const deviceNodes = devices.map((device) => ({
          id: `device_${device.key}`,
          name: device.title,
          type: "device",
          ipAddress: device.ipAddress,
          macAddress: device.key, // device.key is the MAC address
          modelname: device.modelname || "default",
        }));

        // Replace existing devices with new ones
        if (node.children) {
          node.children = node.children.filter(
            (child) => child.type !== "device"
          );
          node.children.push(...deviceNodes);
        } else {
          node.children = deviceNodes;
        }
      }
    },
  },
});

export const {
  setTreeData,
  addNode,
  removeNode,
  renameNode,
  updateDevice,
  selectNode,
  setExpandedKeys,
  setSelectedKeys,
  resetTree,
  restoreTreeData,
  loadTreeData,
  updateDisplaySettings,
  resetDisplaySettings,
  loadRealGroupsData,
  updateNodeDevices,
  toggleTopologyNodeExpand,
  expandAllTopologyNodes,
  collapseAllTopologyNodes,
  resetTopologyExpandState,
  setLLDPConnections,
  addLLDPConnection,
  removeLLDPConnection,
  clearLLDPConnections,
} = customTopologySlice.actions;

// Helper functions for import/export
export const exportTreeData = (
  treeData,
  expandedKeys = [],
  selectedKeys = [],
  topologyExpandedNodes = []
) => {
  const dataToStore = {
    treeData: treeData,
    expandedKeys: expandedKeys,
    selectedKeys: selectedKeys,
    topologyExpandedNodes: topologyExpandedNodes,
    timestamp: new Date().toISOString(),
    version: "2.0",
  };
  return JSON.stringify(dataToStore, null, 2);
};

// Selectors
export const customTopologySelector = (state) => state.customTopology;

export default customTopologySlice;
