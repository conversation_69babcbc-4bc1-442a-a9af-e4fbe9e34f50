import { api } from "./api";

export const idpsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getIdpsReport: builder.query({
      query: () => `api/v1/idps/report`,
      transformResponse: (res) => {
        const idp_client = Object.keys(res);
        return { idp_client, report: res };
      },
      providesTags: ["Idps"],
    }),
    importRules: builder.mutation({
      query: (data) => ({
        url: `/api/v1/commands`,
        method: "POST",
        body: JSON.stringify([
          { command: `idps rules import ${data.url}`, client: data.svc },
        ]),
      }),
      invalidatesTags: ["Idps"],
    }),
    requestIDPEvents: builder.mutation({
      query: (data) => ({
        url: `/api/v1/commands`,
        method: "POST",
        body: JSON.stringify([
          {
            command: `idps event ${data.protocol} ${data.count}`,
            client: data.svc,
          },
        ]),
      }),
      invalidatesTags: ["Idps"],
    }),
    deleteRules: builder.mutation({
      query: (data) => ({
        url: `/api/v1/commands`,
        method: "POST",
        body: JSON.stringify([
          {
            command: `idps rules delete ${data.ruleName}`,
            client: data.svc,
          },
        ]),
      }),
      invalidatesTags: ["Idps"],
    }),
    addRules: builder.mutation({
      query: (data) => ({
        url: `/api/v1/commands`,
        method: "POST",
        body: JSON.stringify([
          {
            command: `idps rules add ${data.ruleName} ${data.rules}`,
            client: data.svc,
          },
        ]),
      }),
      invalidatesTags: ["Idps"],
    }),
  }),
});

export const {
  useGetIdpsReportQuery,
  useImportRulesMutation,
  useRequestIDPEventsMutation,
  useDeleteRulesMutation,
  useAddRulesMutation,
} = idpsApi;
