import { api } from "./api";

export const customTopologyApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Tree management
    getAllCustomTopologyTrees: builder.query({
      query: () => "api/v1/custom-topology",
      providesTags: ["customTopology"],
      transformResponse: (resp) => {
        // API returns an object, but we can work with either object or array
        return resp;
      },
    }),

    getCustomTopologyTree: builder.query({
      query: (treeName) => `api/v1/custom-topology/${treeName}`,
      providesTags: (result, error, treeName) => [
        "customTopology",
        { type: "customTopology", id: treeName },
      ],
    }),

    createCustomTopologyTree: builder.mutation({
      query: (treeData) => ({
        url: "api/v1/custom-topology",
        method: "POST",
        body: treeData,
      }),
      invalidatesTags: ["customTopology"],
    }),

    updateCustomTopologyTree: builder.mutation({
      query: ({ treeName, ...updates }) => ({
        url: `api/v1/custom-topology/${treeName}`,
        method: "PUT",
        body: updates,
      }),
      invalidatesTags: (result, error, { treeName }) => [
        "customTopology",
        { type: "customTopology", id: treeName },
      ],
    }),

    deleteCustomTopologyTree: builder.mutation({
      query: (treeName) => ({
        url: `api/v1/custom-topology/${treeName}`,
        method: "DELETE",
      }),
      invalidatesTags: ["customTopology"],
    }),

    // Node operations
    addCustomTopologyNode: builder.mutation({
      query: ({ treeName, ...nodeData }) => ({
        url: `api/v1/custom-topology/${treeName}/nodes`,
        method: "POST",
        body: nodeData,
      }),
      invalidatesTags: (result, error, { treeName }) => [
        { type: "customTopology", id: treeName },
      ],
    }),

    updateCustomTopologyNode: builder.mutation({
      query: ({ treeName, nodeId, ...updates }) => ({
        url: `api/v1/custom-topology/${treeName}/nodes/${nodeId}`,
        method: "PUT",
        body: updates,
      }),
      invalidatesTags: (result, error, { treeName }) => [
        { type: "customTopology", id: treeName },
      ],
    }),

    deleteCustomTopologyNode: builder.mutation({
      query: ({ treeName, nodeId }) => ({
        url: `api/v1/custom-topology/${treeName}/nodes/${nodeId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { treeName }) => [
        "customTopology",
        { type: "customTopology", id: treeName },
      ],
    }),

    // Device assignment
    assignDeviceToNode: builder.mutation({
      query: ({ treeName, nodeId, deviceMac }) => ({
        url: `api/v1/custom-topology/${treeName}/nodes/${nodeId}/devices`,
        method: "POST",
        body: { deviceMac },
      }),
      invalidatesTags: (result, error, { treeName }) => [
        { type: "customTopology", id: treeName },
        "customTopologyDevices",
      ],
    }),

    removeDeviceFromNode: builder.mutation({
      query: ({ treeName, nodeId, deviceMac }) => ({
        url: `api/v1/custom-topology/${treeName}/nodes/${nodeId}/devices/${deviceMac}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, { treeName }) => [
        { type: "customTopology", id: treeName },
        "customTopologyDevices",
      ],
    }),

    // Device management
    getAvailableDevicesForCustomTopology: builder.query({
      query: () => "api/v1/custom-topology/devices/available",
      providesTags: ["customTopologyDevices"],
    }),

    // LLDP Discovery
    discoverCustomTopologyLLDP: builder.mutation({
      query: (treeName) => ({
        url: `api/v1/custom-topology/${treeName}/discover-lldp`,
        method: "POST",
      }),
      invalidatesTags: (result, error, treeName) => [
        { type: "customTopology", id: treeName },
        "customTopologyConnections",
      ],
    }),

    getCustomTopologyConnections: builder.query({
      query: (treeName) => `api/v1/custom-topology/${treeName}/connections`,
      providesTags: (result, error, treeName) => [
        { type: "customTopologyConnections", id: treeName },
      ],
    }),

    // Import/Export
    exportCustomTopologyTree: builder.query({
      query: (treeName) => `api/v1/custom-topology/${treeName}/export`,
    }),

    importCustomTopologyTree: builder.mutation({
      query: ({ treeName, jsonData }) => ({
        url: `api/v1/custom-topology/${treeName}/import`,
        method: "POST",
        body: { jsonData },
      }),
      invalidatesTags: ["customTopology"],
    }),

    // Flattened nodes endpoint - optimized for DevicePage
    getFlattenedCustomTopologyNodes: builder.query({
      query: () => "api/v1/custom-topology/nodes/flat",
      providesTags: ["customTopology", "customTopologyNodes"],
      transformResponse: (resp) => {
        // Response is already an array of flattened nodes
        return resp;
      },
    }),
  }),
});

export const {
  useGetAllCustomTopologyTreesQuery,
  useGetCustomTopologyTreeQuery,
  useCreateCustomTopologyTreeMutation,
  useUpdateCustomTopologyTreeMutation,
  useDeleteCustomTopologyTreeMutation,
  useAddCustomTopologyNodeMutation,
  useUpdateCustomTopologyNodeMutation,
  useDeleteCustomTopologyNodeMutation,
  useAssignDeviceToNodeMutation,
  useRemoveDeviceFromNodeMutation,
  useGetAvailableDevicesForCustomTopologyQuery,
  useDiscoverCustomTopologyLLDPMutation,
  useGetCustomTopologyConnectionsQuery,
  useExportCustomTopologyTreeQuery,
  useImportCustomTopologyTreeMutation,
  useGetFlattenedCustomTopologyNodesQuery,
} = customTopologyApi;
