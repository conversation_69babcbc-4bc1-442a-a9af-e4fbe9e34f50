import { api } from "./api";

export const globalSettingsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Update global settings
    updateGlobalSettings: builder.mutation({
      query: (settings) => ({
        url: "api/v1/globalSettings",
        method: "POST",
        body: settings,
      }),
      invalidatesTags: ["globalSettings"],
    }),

    // Get current global settings (if needed in the future)
    getGlobalSettings: builder.query({
      query: () => "api/v1/globalSettings",
      providesTags: ["globalSettings"],
    }),
  }),
});

export const {
  useUpdateGlobalSettingsMutation,
  useGetGlobalSettingsQuery,
} = globalSettingsApi;
