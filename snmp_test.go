package mnms

import (
	"testing"
	"time"
)

func TestSnmpCommunity(t *testing.T) {
	// q.P = "snmp_test"
	// ctx, cancel := context.WithCancel(context.Background())
	// defer cancel()
	// err := runTestSnmpSimulators(ctx)
	// if err != nil {
	// 	t.<PERSON>al(err)
	// }
	// defer func() {
	// 	for _, v := range testSnmpSimulators {
	// 		_ = v.Shutdown()
	// 	}
	// }()
	// time.Sleep(time.Millisecond * 1000)
	t.Skip("skipping test, simulator has issue can't work, this test just for local test")
	go func() {
		GwdMain()
	}()
	err := GwdInvite()
	if err != nil {
		t.Fatal(err)
	}

	time.Sleep(time.Millisecond * 1000)
	t.Log(QC.DevData)
	// dev, ok := QC.DevData[targetMac]
	// if !ok {
	// 	t.<PERSON>al("no such device: ", targetMac)
	// }
	// t.Log(dev)

	r, rw, err := GetSNMPCommunity("admin", "default", "**************")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(r, rw)
}

/*
// 1. SNMP GET Operations
func TestSnmpCmd_Get(t *testing.T) {
	tests := []struct {
		name     string
		cmd      string
		wantErr  bool
		expected string
	}{
		{
			name:     "Valid GET request",
			cmd:      "snmp get ********* *******.*******.0",
			wantErr:  false,
			expected: "ok",
		},
		{
			name:     "Invalid IP address",
			cmd:      "snmp get 256.0.0.1 *******.*******.0",
			wantErr:  true,
			expected: "error:",
		},
		{
			name:     "Invalid OID format",
			cmd:      "snmp get ********* *******.2..1.1.0",
			wantErr:  true,
			expected: "error:",
		},
		{
			name:     "Missing OID",
			cmd:      "snmp get *********",
			wantErr:  true,
			expected: "error: invalid command arguments",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmdInfo := &CmdInfo{Command: tt.cmd}
			result := SnmpCmd(cmdInfo)

			if tt.wantErr {
				if !strings.Contains(result.Status, "error:") {
					t.Errorf("Expected error but got: %s", result.Status)
				}
			} else {
				if result.Status != tt.expected {
					t.Errorf("Expected status %q but got %q", tt.expected, result.Status)
				}
			}
		})
	}
}

// SNMP SET Operations
func TestSnmpCmd_Set(t *testing.T) {
	tests := []struct {
		name     string
		cmd      string
		wantErr  bool
		expected string
	}{
		{
			name:     "Valid SET request (OctetString)",
			cmd:      "snmp set ********* *******.*******.0 test OctetString",
			wantErr:  false,
			expected: "ok",
		},
		{
			name:     "Valid SET request (Integer)",
			cmd:      "snmp set ********* *******.*******.0 42 Integer",
			wantErr:  false,
			expected: "ok",
		},
		{
			name:     "Invalid value type",
			cmd:      "snmp set ********* *******.*******.0 test InvalidType",
			wantErr:  true,
			expected: "error: value type not supported",
		},
		{
			name:     "Missing arguments",
			cmd:      "snmp set ********* *******.*******.0",
			wantErr:  true,
			expected: "error: invalid command arguments",
		},
		{
			name:     "Invalid IP address",
			cmd:      "snmp set 300.0.0.1 *******.*******.0 test OctetString",
			wantErr:  true,
			expected: "error:",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmdInfo := &CmdInfo{Command: tt.cmd}
			result := SnmpCmd(cmdInfo)

			if tt.wantErr {
				if !strings.Contains(result.Status, "error:") {
					t.Errorf("Expected error but got: %s", result.Status)
				}
			} else {
				if result.Status != tt.expected {
					t.Errorf("Expected status %q but got %q", tt.expected, result.Status)
				}
			}
		})
	}
}

// SNMP WALK Operations
func TestSnmpCmd_Walk(t *testing.T) {
	tests := []struct {
		name    string
		cmd     string
		wantErr bool
	}{
		{
			name:    "Valid WALK request",
			cmd:     "snmp walk ********* *******.2.1.1",
			wantErr: false,
		},
		{
			name:    "Invalid OID",
			cmd:     "snmp walk ********* *******.2..1.1",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmdInfo := &CmdInfo{Command: tt.cmd}
			result := SnmpCmd(cmdInfo)

			if tt.wantErr {
				if !strings.Contains(result.Status, "error:") {
					t.Errorf("Expected error but got: %s", result.Status)
				}
			} else {
				if result.Status != "ok" {
					t.Errorf("Expected status 'ok' but got %q", result.Status)
				}
			}
		})
	}
}

// SNMP BULK Operations
func TestSnmpCmd_Bulk(t *testing.T) {
	tests := []struct {
		name    string
		cmd     string
		wantErr bool
	}{
		{
			name:    "Valid BULK request",
			cmd:     "snmp bulk ********* *******.2.1.1",
			wantErr: false,
		},
		{
			name:    "Invalid OID",
			cmd:     "snmp bulk ********* *******.2..1.1",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmdInfo := &CmdInfo{Command: tt.cmd}
			result := SnmpCmd(cmdInfo)

			if tt.wantErr {
				if !strings.Contains(result.Status, "error:") {
					t.Errorf("Expected error but got: %s", result.Status)
				}
			} else {
				if result.Status != "ok" {
					t.Errorf("Expected status 'ok' but got %q", result.Status)
				}
			}
		})
	}
}

// Invalid Command Cases
func TestSnmpCmd_InvalidCommands(t *testing.T) {
	tests := []struct {
		name     string
		cmd      string
		expected string
	}{
		{
			name:     "Invalid operation",
			cmd:      "snmp invalid ********* *******.2.1.1",
			expected: "error: invalid command",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cmdInfo := &CmdInfo{Command: tt.cmd}
			result := SnmpCmd(cmdInfo)

			if !strings.Contains(result.Status, "error:") {
				t.Errorf("Expected error but got: %s", result.Status)
			}
		})
	}
}

// SnmpGet
func TestSnmpGet(t *testing.T) {
	tests := []struct {
		name    string
		address string
		oids    []string
		wantErr bool
	}{
		{
			name:    "Valid request",
			address: "*********",
			oids:    []string{"*******.*******.0"},
			wantErr: false,
		},
		{
			name:    "Invalid IP",
			address: "300.0.0.1",
			oids:    []string{"*******.*******.0"},
			wantErr: true,
		},
		{
			name:    "Empty OID",
			address: "*********",
			oids:    []string{""},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := SnmpGet(tt.address, tt.oids)
			if (err != nil) != tt.wantErr {
				t.Errorf("SnmpGet() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// SnmpSet
func TestSnmpSet(t *testing.T) {
	tests := []struct {
		name      string
		address   string
		oid       string
		value     string
		valueType string
		wantErr   bool
	}{
		{
			name:      "Valid OctetString",
			address:   "*********",
			oid:       "*******.*******.0",
			value:     "test",
			valueType: "OctetString",
			wantErr:   false,
		},
		{
			name:      "Invalid value type",
			address:   "*********",
			oid:       "*******.*******.0",
			value:     "test",
			valueType: "InvalidType",
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := SnmpSet(tt.address, tt.oid, tt.value, tt.valueType)
			if (err != nil) != tt.wantErr {
				t.Errorf("SnmpSet() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// ExpandCommandKVValue
func TestExpandCommandKVValue(t *testing.T) {
	tests := []struct {
		name    string
		command string
		wantErr bool
	}{
		{
			name:    "No placeholders",
			command: "snmp get ********* *******.*******.0",
			wantErr: false,
		},
		{
			name:    "With valid placeholder",
			command: "snmp get :device_ip *******.*******.0",
			wantErr: false,
		},
		{
			name:    "With invalid placeholder format",
			command: "snmp get :invalid$ip *******.*******.0",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := ExpandCommandKVValue(tt.command)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExpandCommandKVValue() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
*/
