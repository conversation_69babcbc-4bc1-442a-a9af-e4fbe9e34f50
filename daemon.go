package mnms

import (
	"fmt"
	"os"

	"github.com/kardianos/service"
)

var Usage = "run | start | stop | restart | install |  uninstall  | status "
var servicName = "nimbl"

const displayName = "nimbl software"
const description = "atop nimbl software application."
const DaemonFlag = "daemon"

func filterArgs(args []string) []string {
	found := false
	foundindex := []int{}
	for i, v := range args {
		if v == "-"+DaemonFlag {
			next := i + 1
			if len((args)) > next {
				found = true
				foundindex = append(foundindex, i, next)
			}
		}
	}
	if found {
		array := []string{}
		for i, v := range args {
			if i == foundindex[0] || i == foundindex[1] {
				continue
			}
			array = append(array, v)
		}
		return array
	} else {
		return args
	}
}

func NewDaemon(name string, args []string) (*Daemon, error) {
	args = filterArgs(args)
	if len(name) != 0 {
		servicName = name
	}
	options := make(service.KeyValue)
	options["OnFailure"] = "restart"
	svcConfig := &service.Config{
		Name:         servicName,
		DisplayName:  fmt.Sprintf("%v %v", servicName, displayName),
		Description:  description,
		Dependencies: []string{},
		Option:       options,
		Arguments:    args[1:],
	}
	prg := &program{}
	s, err := service.New(prg, svcConfig)
	if err != nil {
		return nil, err
	}
	d := &Daemon{srv: s, prog: prg}
	return d, nil
}

type Daemon struct {
	srv  service.Service
	prog *program
}

func (d *Daemon) RunMode(mode string) error {
	switch mode {
	case "run":
		if err := d.srv.Run(); err != nil {
			return err
		}
	case "start":
		fmt.Fprintln(os.Stderr, "Starting service..")
		if err := d.srv.Start(); err != nil {
			return err
		}
		fmt.Fprintln(os.Stderr, "Service started successfully.")
	case "stop":
		fmt.Fprintln(os.Stderr, "Stopping service..")
		if err := d.srv.Stop(); err != nil {
			return err
		}
		fmt.Fprintln(os.Stderr, "Service stopped successfully.")
	case "restart":
		fmt.Fprintln(os.Stderr, "Restarting service..")
		d.srv.Stop()
		if err := d.srv.Start(); err != nil {
			return err
		}
		fmt.Fprintln(os.Stderr, "Service restarted successfully.")
	case "install":
		fmt.Fprintln(os.Stderr, "Installing service..")
		if err := d.srv.Install(); err != nil {
			return err
		}
		if err := d.srv.Start(); err != nil {
			return err
		}
		fmt.Fprintln(os.Stderr, "Service installed and started successfully.")
	case "uninstall":
		fmt.Fprintln(os.Stderr, "Uninstalling service..")
		d.srv.Stop()
		if err := d.srv.Uninstall(); err != nil {
			return err
		}
		fmt.Fprintln(os.Stderr, "Service stopped and uninstalled successfully.")
	case "status":
		s, err := d.srv.Status()
		if err != nil {
			return err
		}
		switch s {
		case service.StatusUnknown:
			fmt.Fprintln(os.Stderr, "Unknown")
		case service.StatusRunning:
			fmt.Fprintln(os.Stderr, "Running")
		case service.StatusStopped:
			fmt.Fprintln(os.Stderr, "Stopped")
		}
	default:
		if len(mode) != 0 {
			m := fmt.Sprintf("error command: %v,execute run command", mode)
			fmt.Fprintln(os.Stderr, m)
		}
		if err := d.srv.Run(); err != nil {
			return err
		}
	}

	return nil
}

// RegisterRunEvent register run service
func (d *Daemon) RegisterRunEvent(run RunEvent) {
	d.prog.registerRunEvent(run)
}

// RegisterStopEvent register stop event like defer
func (d *Daemon) RegisterStopEvent(stop StopEvent) {
	d.prog.registerStopEvent(stop)
}

type program struct {
	runevent  RunEvent
	stopevent StopEvent
}

type RunEvent func()
type StopEvent func()

func (p *program) registerRunEvent(run RunEvent) {
	p.runevent = run
}
func (p *program) registerStopEvent(stop StopEvent) {
	p.stopevent = stop
}
func (p *program) Start(s service.Service) error {
	// Start should not block. Do the actual work async.
	go p.run()
	return nil
}

func (p *program) run() error {
	if p.runevent != nil {
		p.runevent()
	}
	return nil

}
func (p *program) Stop(s service.Service) error {
	// Stop should not block. Return with a few seconds.
	if p.stopevent != nil {
		p.stopevent()
	}
	return nil
}
