package idpsystem

import (
	"encoding/json"
	"fmt"
	"mnms"
	"mnms/idpsystem/idps"
	"testing"
	"time"

	"github.com/influxdata/go-syslog/v3/rfc3164"
	"github.com/stretchr/testify/assert"
)

func creatingIdpsForTest() (*IdpSystem, error) {
	return initIdpSystem()
}

// integrated test, ImportFileUr
func TestImportFileUrl(t *testing.T) {

	idps, err := creatingIdpsForTest()
	if err != nil {
		t.Fatal(err)
	}
	start := time.Now()

	err = idps.ImportFilesFromUrl([]string{"https://nms.blackbeartechhive.com/api/v1/files/exampleRule.rar"})
	if err != nil {
		end := time.Now()
		duration := end.Sub(start)
		fmt.Printf("Imported time:%vs\n", duration.Seconds())
		t.Fatal(err)
	}

	end := time.Now()
	duration := end.Sub(start)
	fmt.Printf("Imported time:%vs\n", duration.Seconds())
	start = time.Now()
	err = idps.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	end = time.Now()
	duration = end.Sub(start)
	fmt.Printf("Applyed time:%vs\n", duration.Seconds())
}

func TestMessage(t *testing.T) {
	m := idps.EventMessage{Id: 123, Timestamp: "2024-03-18T15:05:06+08:00",
		Type: "alert", InInterface: "WIFI", Srcip: "*************", SrcPort: 137, Destip: "*************", DestPort: 137, Protocol: "DNS", Description: "test"}
	jsonBytes, err := json.Marshal(m)
	if err != nil {
		t.Fatal(err)
	}
	msg := covertToSyslog(mnms.LOG_INFO, string(jsonBytes))
	r := rfc3164.NewParser()
	v, err := r.Parse([]byte(msg))
	if err != nil {
		t.Fatal(err)
	}
	if r, ok := v.(*rfc3164.SyslogMessage); ok {
		assert.Equal(t, *r.Message, string(jsonBytes), "they should be equal")
	} else {
		t.Fatal("format error")
	}
}
