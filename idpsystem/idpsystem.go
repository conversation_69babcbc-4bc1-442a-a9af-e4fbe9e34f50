package idpsystem

import (
	"archive/zip"
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"mnms"
	"mnms/idpsystem/idps"
	"os"
	"path"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/cavaliergopher/grab/v3"
	"github.com/google/gonids"
	"github.com/jinzhu/copier"
	"github.com/mholt/archiver/v3"
	"github.com/qeof/q"
)

var idpsystem *IdpSystem

func getIdpsystem() (*IdpSystem, error) {
	if idpsystem != nil {
		return idpsystem, nil
	}
	return nil, ErrorNotStartUpIdpSystem
}

// if IdpSystem is nil, init IdpSystem
func InitIdpSystem() error {
	i, err := initIdpSystem()
	if err != nil {
		return err
	}
	initRule(i)
	idpsystem = i
	return nil
}
func initRule(i *IdpSystem) error {
	atopgwd := `alert udp any any <> any 55954 (msg:"alert atop device from GWD";sid:50000000;)`
	atopagent := `alert tls any any <> any 56026 (msg:"alert atop device from agent";sid:50000001;)`
	err := i.AddRule("atop_device", atopgwd)
	if err != nil {
		return err
	}
	err = i.AddRule("atop_device", atopagent)
	if err != nil {
		return err
	}
	err = i.ApplyRules()
	if err != nil {
		return err
	}
	return nil
}

func initIdpSystem() (*IdpSystem, error) {
	i, err := NewIdpSystem(false, false)
	if err != nil {
		return nil, err
	}
	i.RegisterEvent(idpsystemEvent())
	return i, nil
}

type IdpSystem struct {
	startTime       string
	m               *sync.Mutex
	running         bool
	i               *idps.Idps
	defaultRules    map[Category]*categoryInfo
	temp            []RuleInfo
	ruleTotalCounts sync.Map
	managedRecord   *ManagedRecord
}

func NewIdpSystem(pcap bool, json bool) (*IdpSystem, error) {
	i := &IdpSystem{
		defaultRules: map[Category]*categoryInfo{}, m: new(sync.Mutex),
	}

	idps, err := idps.NewIdps(pcap, json)
	if err != nil {
		return nil, err
	}
	i.i = idps
	m, err := NewManagedRecord(idpsdir)
	if err != nil {
		return nil, err
	}
	i.managedRecord = m
	return i, nil
}

func (i *IdpSystem) WriteIdpsRecord(name string, v []byte) error {
	return i.managedRecord.Write(name, v)

}

func (i *IdpSystem) DeleteSpecificRecord(date, filename string) error {
	return i.managedRecord.DeleteSpecificRecord(date, filename)
}
func (i *IdpSystem) DeleteAllRecord(date string) error {
	return i.managedRecord.DeleteAllRecord(date)
}

func (i *IdpSystem) getRuleInfoTotal(id uint32) (*RulePacketTotal, error) {
	if c, ok := i.ruleTotalCounts.Load(id); ok {
		return c.(*RulePacketTotal), nil
	}
	return &RulePacketTotal{}, ErrorNoSid
}

func (i *IdpSystem) addRuleInfoTotal(r *RulePacketTotal) {
	i.ruleTotalCounts.Store(r.Sid, r)
}

func (i *IdpSystem) deleteRuleInfoTotal(id uint32) {
	i.ruleTotalCounts.Delete(id)
}
func (i *IdpSystem) ReadAllRulePacketTotal() []mnms.RulePacketsTotal {
	v := []mnms.RulePacketsTotal{}
	i.ruleTotalCounts.Range(func(key, value any) bool {
		r := value.(*RulePacketTotal)
		mr := mnms.RulePacketsTotal{}
		mr.Action = r.Action
		mr.Counts = r.Counts
		mr.Name = string(r.Name)
		mr.Sid = uint64(r.Sid)
		v = append(v, mr)
		return true
	})
	return v
}

func (i *IdpSystem) getStats() bool {
	i.m.Lock()
	defer i.m.Unlock()
	return i.running
}

func (i *IdpSystem) setStatus(b bool) {
	i.m.Lock()
	defer i.m.Unlock()
	i.running = b
}
func (i *IdpSystem) getCategorys() map[Category]*categoryInfo {
	i.m.Lock()
	defer i.m.Unlock()
	r := make(map[Category]*categoryInfo)
	for k, v := range i.defaultRules {
		r[k] = v

	}
	return r
}
func (i *IdpSystem) getCategory(name Category) (*categoryInfo, error) {
	i.m.Lock()
	defer i.m.Unlock()
	if c, ok := i.defaultRules[name]; ok {
		return c, nil
	}
	return nil, Errorcategory
}

func (i *IdpSystem) deleteCategoryInfo(name Category) error {
	i.m.Lock()
	defer i.m.Unlock()
	if _, ok := i.defaultRules[name]; ok {
		delete(i.defaultRules, name)
	} else {
		return Errorcategory
	}
	return nil
}

func (i *IdpSystem) setRules(r RuleInfo) {
	i.m.Lock()
	defer i.m.Unlock()
	if v, ok := i.defaultRules[r.Name]; ok {
		v.addInfo(r)
	} else {
		info := newCategoryInfo()
		info.Name = r.Name
		info.addInfo(r)
		i.defaultRules[r.Name] = info
	}
}

func (i *IdpSystem) Running() bool {
	return i.running
}

func (i *IdpSystem) Start() error {
	if i.getStats() {
		return nil
	}
	err := i.i.Start()
	if err != nil {
		return err
	}
	i.setStatus(true)
	i.startTime = time.Now().Format(time.DateTime)
	return nil
}

func (i *IdpSystem) RegisterEvent(e idps.ReceiveEvent) error {
	err := i.i.RegisterEvent(e)
	if err != nil {
		return err
	}
	return nil
}

func (i *IdpSystem) Close() error {
	if !i.getStats() {
		return nil
	}
	err := i.i.Close()
	if err != nil {
		return err
	}
	i.setStatus(false)
	return nil
}

func (i *IdpSystem) loadFile(filename string, f io.Reader) error {
	str := []string{}
	fileScanner := bufio.NewScanner(f)
	buf := make([]byte, 0, 64*1024)
	fileScanner.Buffer(buf, 1024*1024)
	rules := []string{}
	for fileScanner.Scan() {
		if strings.HasPrefix(string(fileScanner.Bytes()), "#") {
			continue
		}
		s := string(fileScanner.Bytes())
		if len(s) == 0 {
			continue
		}
		str = append(str, s)
		if strings.HasSuffix(s, "\\") {
			continue
		} else {
			s = strings.Join(str, "")
			str = []string{}
		}
		if len(s) != 0 {
			rules = append(rules, s)

		}
	}
	if err := fileScanner.Err(); err != nil {
		return err
	}

	for _, value := range rules {
		err := i.AddRule(Category(getfilename(filename)), value)
		if err != nil {
			v := fmt.Sprintf("filename:%v,error:%v,value:%v", filename, err, value)
			return fmt.Errorf("%v", v)
		}
	}

	return nil
}
func (i *IdpSystem) RecordList() ([]mnms.RecordList, error) {
	v := []mnms.RecordList{}
	d, err := i.managedRecord.List()
	if err != nil {
		return v, err
	}
	js, err := json.Marshal(d)
	if err != nil {
		return v, err
	}
	err = json.Unmarshal(js, &v)
	if err != nil {
		return v, err
	}
	return v, nil
}

func (i *IdpSystem) AddRule(name Category, s string) error {
	r, err := gonids.ParseRule(s)
	if err != nil {
		return err
	}
	err = i.i.AddRule(r)
	if err != nil {
		i.temp = nil
		return err
	}
	i.temp = append(i.temp, RuleInfo{Name: name, Sid: uint32(r.SID), Value: s, Action: string(r.Action), gonid: r, Disable: r.Disabled})
	return nil
}

// GetAllCategory  Category mean file name when import file
func (i *IdpSystem) GetAllCategory() ([]categoryInfo, error) {
	cs := []categoryInfo{}
	for _, v := range i.getCategorys() {
		c := categoryInfo{}
		copier.Copy(&c, &v)
		c.m = new(sync.RWMutex)
		cs = append(cs, c)
	}
	if len(cs) == 0 {
		return cs, Errorcategory
	}
	return cs, nil
}

func (i *IdpSystem) GetRules(name Category) ([]RuleInfo, error) {
	c, err := i.getCategory(name)
	if err != nil {
		return nil, err
	}
	rs := c.getInfos()

	return rs, nil
}

func (i *IdpSystem) DeleteCategory(name Category) error {
	rules, err := i.GetRules(name)
	if err != nil {
		return err
	}
	for _, rule := range rules {
		i.deleteRuleInfoTotal(rule.Sid)
	}
	err = i.deleteCategoryInfo(name)
	if err != nil {
		return err
	}
	err = i.ApplyRules()
	if err != nil {
		return err
	}
	return nil
}

func (i *IdpSystem) addOldRules() error {
	oldrules := []*gonids.Rule{}
	cs, _ := i.GetAllCategory()
	for _, c := range cs {
		rs := c.getInfos()
		for _, r := range rs {
			oldrules = append(oldrules, r.gonid)
		}
	}
	for _, r := range oldrules {
		err := i.i.AddRule(r)
		if err != nil {
			return err
		}
	}
	return nil
}

func (i *IdpSystem) ApplyRules() error {
	defer func() {
		i.temp = nil
	}()
	err := i.addOldRules()
	if err != nil {
		return err
	}
	err = i.i.ApplyRules()
	if err != nil {
		return err
	}

	for _, rule := range i.temp {
		i.setRules(rule)
		r := &RulePacketTotal{}
		r.Action = rule.Action
		r.Sid = rule.Sid
		r.Counts = 0
		r.Name = rule.Name
		i.addRuleInfoTotal(r)
	}
	return nil
}

func (i *IdpSystem) ImportFiles(files []string) error {
	for _, file := range files {
		err := i.importFile(file)
		if err != nil {
			return err
		}
	}
	return nil
}

func (i *IdpSystem) ImportFilesFromUrl(urls []string) error {
	for _, url := range urls {
		rsp, err := grab.Get(".", url)
		if err != nil {
			return err
		}
		defer os.Remove(rsp.Filename)
		err = i.importFile(rsp.Filename)
		if err != nil {
			return err
		}
	}
	return nil
}

func (i *IdpSystem) importFile(file string) error {
	ext := filepath.Ext(file)
	switch ext {
	case ".rules":
		f, err := os.Open(file)
		if err != nil {
			return err
		}
		defer f.Close()
		err = i.loadFile(file, f)
		if err != nil {
			return err
		}
	default:
		err := archiver.Walk(file, func(f archiver.File) error {
			ext := filepath.Ext(f.Name())
			if ext != ".rules" {
				return nil
			}
			_, err := i.getCategory(Category(getfilename(f.Name())))
			if err == nil {
				return fmt.Errorf("please delete Name:%v ,first", getfilename(f.Name()))
			}
			err = i.loadFile(f.Name(), f)
			if err != nil {
				return err
			}
			return nil
		})
		remove := func(message string) string {
			for i := 0; i < len(message); i++ {
				if message[i] == ':' {
					return message[i:]
				}
			}
			return message
		}

		if err != nil {
			message := err.Error()
			message = strings.TrimPrefix(remove(message), ":")
			return fmt.Errorf("%v", message)
		}

	}
	return nil
}
func (i *IdpSystem) CreateFile(filename string) error {
	err := i.createFile(filename)
	if err != nil {
		return err
	}
	return nil
}

func (i *IdpSystem) createFile(filename string) error {
	fZip, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer fZip.Close()
	zipWriter := zip.NewWriter(fZip)
	defer zipWriter.Close()

	for _, v := range i.getCategorys() {
		path := filepath.Join(string(v.Name))
		file, err := zipWriter.Create(path + ".rules")
		if err != nil {
			return err
		}
		rules := v.getInfos()
		for i := range rules {
			rule := rules[i]
			_, err = file.Write([]byte(rule.Value + "\n"))
			if err != nil {
				return err
			}
		}

	}
	return nil
}

func getfilename(fn string) string {
	fn = filepath.Base(fn)
	return strings.TrimSuffix(fn, path.Ext(fn))
}

func idpsystemEvent() func(idps.EventMessage) {
	v := func(message idps.EventMessage) {
		idpsys, err := getIdpsystem()
		if err != nil {
			return
		}

		info := mnms.EventMessage{}
		copier.Copy(&info, &message)
		var category Category
		ruletotal, err := idpsys.getRuleInfoTotal(info.Id)
		if err != nil {
			q.Q(err)
			return
		}
		ruletotal.Counts++

		if ruletotal.Md5 == message.HashMd5() {
			return
		} else {
			ruletotal.Md5 = message.HashMd5()
		}

		info.Rulename = string(ruletotal.Name)
		jsonBytes, err := json.Marshal(info)
		if err != nil {
			q.Q(err)
			return
		}

		msg := ""
		switch info.Type {
		case "alert":
			msg = covertToSyslog(mnms.LOG_WARNING, string(jsonBytes))
		case "pass":
			msg = covertToSyslog(mnms.LOG_INFO, string(jsonBytes))
		case "drop":
			msg = covertToSyslog(mnms.LOG_NOTICE, string(jsonBytes))
		}

		mnms.SendMessageToRemoteSyslog([]byte(msg))

		//event.Type: alert,drop
		err = idpsys.WriteIdpsRecord(strings.ToLower(info.Type), []byte(msg+"\n"))
		if err != nil {
			q.Q(err)
		}
		//event.Protocol: icmp,dns
		err = idpsys.WriteIdpsRecord(strings.ToLower(info.Protocol), []byte(msg+"\n"))
		if err != nil {
			q.Q(err)
		}
		//Category
		if len(category) != 0 {
			err = idpsys.WriteIdpsRecord(string(category), []byte(msg+"\n"))
			if err != nil {
				q.Q(err)
			}
		}

	}
	return v
}

func covertToSyslog(severity int, msg string) string {
	timestamp := time.Now().Format(time.Stamp)
	var name string
	if len(mnms.QC.Name) == 0 {
		name, _ = os.Hostname()
	} else {
		name = mnms.QC.Name
	}
	syslogmsg := fmt.Sprintf("<%d>%s %s %s: %s", severity, timestamp, name, "idps", msg)

	return syslogmsg
}
