package idpsystem

import "github.com/google/gonids"

type Command struct {
	Command string `json:"command"`
}
type Category string
type RuleAction struct {
	Command string   `json:"command"`
	ID      uint64   `json:"id"`
	Name    Category `json:"name"`
	Sid     uint32   `json:"sid"`
	Value   string   `json:"value"`
}

type RuleInfo struct {
	Name    Category     `json:"name"`
	Disable bool         `json:"enable"`
	Sid     uint32       `json:"sid"`
	Value   string       `json:"value"`
	Action  string       `json:"action"`
	gonid   *gonids.Rule `json:"-"`
}

type RulePacketTotal struct {
	Sid    uint32   `json:"sid"`
	Name   Category `json:"name"`
	Action string   `json:"action"`
	Counts uint64   `json:"counts"`
	Md5    string   `json:"-"`
}

type dirStruct struct {
	Date  string     `json:"date,omitempty"`
	Files []fileInfo `json:"files,omitempty"`
}

type fileInfo struct {
	Name string `json:"name,omitempty"`
	Size int    `json:"size,omitempty"`
}

func Compare(a, b []byte) bool {
	a = append(a, b...)
	c := 0
	for _, x := range a {
		c ^= int(x)
	}
	return c == 0
}
