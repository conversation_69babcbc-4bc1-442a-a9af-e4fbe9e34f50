package idpsystem

import (
	"bytes"
	"encoding/gob"
	"encoding/json"
	"fmt"
	"io/fs"
	"mnms"
	"mnms/idpsystem/idps"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/icza/backscanner"
	"github.com/influxdata/go-syslog/v3/rfc3164"
	"github.com/qeof/q"
)

const exten = ".log"
const idpsdir = "./idpsRecord"
const dateformat = "2006-01-02"

func NewManagedRecord(dir string) (*ManagedRecord, error) {
	m := &ManagedRecord{dir: dir,
		todayRecordMap: map[string]*idpsRecord{}, m: new(sync.Mutex)}
	m.cdate = NewCurrentDate()
	err := m.init()
	if err != nil {
		return nil, err
	}
	return m, nil
}

type ManagedRecord struct {
	m              *sync.Mutex
	dir            string
	tdate          string // today date
	cdate          *currentDate
	todayRecordMap map[string]*idpsRecord
}

func (m *ManagedRecord) init() error {
	m.tdate = m.cdate.Date()
	entries, err := os.ReadDir(m.todayDir())
	if err != nil {
		q.Q(err)
		return nil
	}
	for _, f := range entries {
		if filepath.Ext(f.Name()) == exten {
			m.addRunningRecord(f.Name())
		}
	}
	return nil
}

func (m *ManagedRecord) Write(filename string, b []byte) error {
	err := m.checkDate()
	if err != nil {
		return err
	}
	record, _ := m.getRunningRecord(filename)
	if record != nil {
		err := record.Write(b)
		if err != nil {
			return err
		}
	} else {
		record := m.addRunningRecord(filename)
		err := record.Write(b)
		if err != nil {
			return err
		}
	}
	return nil
}

func (m *ManagedRecord) todayDir() string {
	return filepath.Join(m.dir, fmt.Sprint(m.cdate.Year()), fmt.Sprintf("%02d", m.cdate.Month()), m.cdate.MonthandDay())
}
func (m *ManagedRecord) todayfilePath(filename string) string {
	return filepath.Join(m.todayDir(), fmt.Sprintf("%v%v", filename, exten))
}
func (m *ManagedRecord) checkDate() error {
	m.m.Lock()
	defer m.m.Unlock()
	if m.tdate != m.cdate.Date() {
		m.tdate = m.cdate.Date()
		for k, v := range m.todayRecordMap {
			err := v.Close()
			if err != nil {
				return err
			}
			delete(m.todayRecordMap, k)
		}
	}
	return nil
}

func (m *ManagedRecord) addRunningRecord(filename string) *idpsRecord {
	m.m.Lock()
	defer m.m.Unlock()
	filename = strings.TrimSuffix(filename, exten)
	if l, ok := m.todayRecordMap[filename]; ok {
		return l
	} else {
		log := NewIdpsRecord(m.todayfilePath(filename))
		m.todayRecordMap[filename] = log
		return log
	}
}

func (m *ManagedRecord) getRunningRecord(filename string) (*idpsRecord, error) {
	m.m.Lock()
	defer m.m.Unlock()
	filename = strings.TrimSuffix(filename, exten)
	if log, ok := m.todayRecordMap[filename]; ok {
		return log, nil
	}
	return nil, fmt.Errorf("not find name:%v", filename)
}

func (m *ManagedRecord) DeleteAllRecord(date string) error {
	m.m.Lock()
	defer m.m.Unlock()
	//delet running
	dt := func() error {
		for k, v := range m.todayRecordMap {
			err := v.Delete()
			if err != nil {
				q.Q(err)
			}
			delete(m.todayRecordMap, k)
		}
		return nil
	}
	switch {
	case date == m.tdate:
		dt()
		return nil
	case len(date) == 0:
		dt()
		os.RemoveAll(m.dir)
		return nil
	case date != m.tdate:
		path, err := m.selectSpecificPathByDate(date)
		if err != nil {
			return err
		}
		os.RemoveAll(path)
		return nil
	}
	return nil
}

func (m *ManagedRecord) DeleteSpecificRecord(date string, name string) error {
	if m.tdate == date {
		err := m.deleteRunningRecord(name)
		if err != nil {
			return err
		}
		return nil
	}
	path, err := m.selectSpecificPathByDate(date)
	if err != nil {
		return err
	}
	os.Remove(filepath.Join(path, name))
	return nil
}
func (m *ManagedRecord) selectSpecificPathByDate(date string) (string, error) {
	dates := strings.Split(date, "-")
	switch {
	case len(dates) == 1:
		return filepath.Join(m.dir, date), nil
	case len(dates) == 2:
		return filepath.Join(m.dir, fmt.Sprintf("%v", dates[0]), fmt.Sprintf("%02v", dates[1])), nil
	case len(dates) == 3:
		return filepath.Join(m.dir,
			fmt.Sprintf("%v", dates[0]), fmt.Sprintf("%02v", dates[1]), fmt.Sprintf("%02v-%02v", dates[1], dates[2])), nil
	default:
		return "", fmt.Errorf("error %v format", date)
	}
}

func (m *ManagedRecord) deleteRunningRecord(filename string) error {
	m.m.Lock()
	defer m.m.Unlock()
	filename = strings.TrimSuffix(filename, exten)
	if r, ok := m.todayRecordMap[filename]; ok {
		err := r.Delete()
		if err != nil {
			return err
		}
		delete(m.todayRecordMap, filename)
		return nil
	}
	return fmt.Errorf("not find name:%v", filename)
}

func (m *ManagedRecord) List() ([]dirStruct, error) {
	ls, err := m.readList()
	if err != nil {
		return nil, err
	}
	ds := m.covertToLevel(ls)
	//order
	timeOrder(ds)

	return ds, nil
}

func timeOrder(dir []dirStruct) []dirStruct {
	sort.Slice(dir, func(i, j int) bool {
		t1, err := time.Parse(dateformat, dir[i].Date)
		if err != nil {
			return false
		}
		t2, err := time.Parse(dateformat, dir[j].Date)
		if err != nil {
			return false
		}

		return t1.Before(t2)
	})
	return dir
}

func (m *ManagedRecord) covertToLevel(ls []fileInfo) []dirStruct {
	datemap := map[string][]fileInfo{}
	for _, l := range ls {
		vs := strings.Split(l.Name, string(os.PathSeparator))
		if len(vs) != 5 {
			continue
		}
		date := fmt.Sprintf("%v-%v", vs[1], vs[3])
		if fs, ok := datemap[date]; ok {
			fs = append(fs, fileInfo{Name: vs[4], Size: l.Size})
			datemap[date] = fs
		} else {
			datemap[date] = []fileInfo{{Name: vs[4], Size: l.Size}}
		}
	}
	r := []dirStruct{}
	for k, v := range datemap {
		r = append(r, dirStruct{Date: k, Files: v})
	}
	return r
}

func (m *ManagedRecord) readList() ([]fileInfo, error) {
	l := []fileInfo{}
	err := filepath.WalkDir(m.dir, func(path string, d fs.DirEntry, err error) error {
		if path == m.dir {
			return nil
		}

		if !d.IsDir() && filepath.Ext(path) == exten {
			fi, err := os.Stat(path)
			if err != nil {
				return nil
			}
			l = append(l, fileInfo{Name: path, Size: int(fi.Size())})
		}
		return nil
	})
	if err != nil {
		return l, err
	}
	return l, nil
}

func (m *ManagedRecord) TimeSearch(file string, st string, end string, layout string) ([]mnms.EventMessage, error) {
	messages := []mnms.EventMessage{}
	t, err := NewTimeSearch(st, end, layout)
	if err != nil {
		return messages, err
	}
	dirs := m.selectDir(t.starttime, t.endTime)
	for i := len(dirs) - 1; i >= 0; i-- {
		path := filepath.Join(dirs[i], file)
		f, err := os.Open(path)
		if err != nil {
			continue
		}
		finfo, err := f.Stat()
		if err != nil {
			q.Q(err)
			continue
		}
		scanner := backscanner.New(f, int(finfo.Size()))
		v := m.search(t, scanner)
		if len(v) != 0 {
			messages = append(messages, v...)
		}

	}

	return messages, nil
}
func (m *ManagedRecord) search(t *timeSearch, scan *backscanner.Scanner) []mnms.EventMessage {
	messages := []mnms.EventMessage{}
	for {
		line, _, err := scan.LineBytes()
		if err != nil {
			q.Q("err:", err)
			break
		}
		if len(line) == 0 {
			continue
		}

		r := rfc3164.NewParser()
		v, err := r.Parse(line)
		if err != nil {
			q.Q(err)
			continue
		}
		if r, ok := v.(*rfc3164.SyslogMessage); ok {
			mess := []byte(*r.Message)
			m := mnms.EventMessage{}
			err = json.Unmarshal(mess, &m)
			if err != nil {
				q.Q(err)
				continue
			}

			if t != nil {
				b, err := t.search(idps.TimeFormat, m.Timestamp)
				if err != nil {
					return messages
				}
				if !b {
					continue
				}
			}
			messages = append(messages, m)
		} else {
			q.Q("syslog format error")
			continue
		}
	}
	return messages
}

func (m *ManagedRecord) selectDir(st, et time.Time) []string {
	ns := []string{}
	diff := et.Sub(st)
	stfie := st.Format(dateformat)
	f, err := m.selectSpecificPathByDate(stfie)
	if err == nil {
		ns = append(ns, f)
	}
	diffday := int(diff.Hours() / 24)
	for i := 0; i < diffday; i++ {
		st = st.AddDate(0, 0, 1)
		data, err := m.selectSpecificPathByDate(st.Format(dateformat))
		if err != nil {
			q.Q(err)
			continue
		}
		ns = append(ns, data)
	}
	return ns
}

var osStat = os.Stat

func NewIdpsRecord(filename string) *idpsRecord {
	return &idpsRecord{name: filename, m: new(sync.Mutex)}
}

type idpsRecord struct {
	name string
	file *os.File
	size int64
	m    *sync.Mutex
}

func (i *idpsRecord) openExistingOrNew() error {

	filename := i.filename()
	info, err := osStat(filename)
	if os.IsNotExist(err) {
		return i.openNew()
	}
	if err != nil {
		return fmt.Errorf("error getting log file info: %s", err)
	}

	file, err := os.OpenFile(filename, os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		// if we fail to open the old log file for some reason, just ignore
		// it and open a new log file.
		return i.openNew()
	}
	i.file = file
	i.size = info.Size()
	return nil
}

func (i *idpsRecord) openNew() error {
	err := os.MkdirAll(i.dir(), 0755)
	if err != nil {
		return fmt.Errorf("can't make directories for new logfile: %s", err)
	}

	name := i.filename()
	mode := os.FileMode(0600)
	f, err := os.OpenFile(name, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, mode)
	if err != nil {
		return fmt.Errorf("can't open new logfile: %s", err)
	}
	i.file = f
	i.size = 0
	return nil
}
func (i *idpsRecord) Write(b []byte) error {
	i.m.Lock()
	defer i.m.Unlock()
	if i.file == nil {
		if err := i.openExistingOrNew(); err != nil {
			return err
		}
	}
	n, err := i.file.Write(b)
	i.size += int64(n)
	return err
}

func (i *idpsRecord) Close() error {
	i.m.Lock()
	defer i.m.Unlock()
	if i.file == nil {
		return nil
	}
	err := i.file.Close()
	if err != nil {
		return err
	}
	i.file = nil
	return nil
}
func (i *idpsRecord) Delete() error {
	i.m.Lock()
	defer i.m.Unlock()
	if i.file == nil {
		return nil
	}
	err := i.file.Close()
	if err != nil {
		return err
	}
	i.file = nil
	dname := i.filename() + "_delete"
	err = os.Rename(i.filename(), dname)
	if err != nil {
		return err
	}
	go func() {
		os.Remove(dname)
	}()
	return nil
}

// filename generates the name of the logfile from the current time.
func (i *idpsRecord) filename() string {
	if i.name != "" {
		return i.name
	}
	return filepath.Join(os.TempDir(), i.name)
}

// dir returns the directory for the current filename.
func (i *idpsRecord) dir() string {
	return filepath.Dir(i.filename())
}

func NewCurrentDate() *currentDate {
	return &currentDate{layout: dateformat}
}

type currentDate struct {
	time   time.Time
	data   string
	layout string
}

func (c *currentDate) check() {
	currentTime := time.Now()
	date := currentTime.Format(c.layout)
	if c.data != date {
		c.data = date
		c.time = currentTime
	}
}

func (c *currentDate) Date() string {
	c.check()
	d := c.time.Format(c.layout)
	return d
}

func (c *currentDate) Year() int {
	c.check()
	return c.time.Year()
}

func (c *currentDate) Month() int {
	c.check()
	return int(c.time.Month())
}

func (c *currentDate) MonthandDay() string {
	c.check()
	return fmt.Sprintf("%02d-%02d", c.time.Month(), c.time.Day())
}

func Hash(e any) []byte {
	var b bytes.Buffer
	gob.NewEncoder(&b).Encode(e)
	return b.Bytes()
}
