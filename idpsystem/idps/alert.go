package idps

import (
	"crypto/md5"
	"encoding/json"
	"fmt"
)

type EventMessage struct {
	Id          int    `json:"id"`
	Timestamp   string `json:"timestamp"`
	Type        string `json:"type"`
	InInterface string `json:"inInterface"`
	Srcip       string `json:"srcip"`
	SrcPort     uint16 `json:"srcPort"`
	Destip      string `json:"destip"`
	DestPort    uint16 `json:"destPort"`
	Protocol    string `json:"protocol"`
	Description string `json:"description"`
}

func (e EventMessage) HashMd5() string {
	b, _ := json.Marshal(e)
	v := md5.Sum(b)
	return fmt.Sprintf("%x", v)
}

type ReceiveEvent func(e EventMessage)
