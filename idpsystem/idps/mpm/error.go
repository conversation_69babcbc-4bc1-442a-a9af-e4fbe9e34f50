package mpm

import "errors"

var ErrorStopMatching = errors.New("stop matching")

var ErrorContent = errors.New("content format error")
var ErrorsWithin = errors.New("error within")

var ErrorStartWith = errors.New("error startswith cannot be mixed with depth, offset, within or distance for the same pattern")
var ErrorEndswithtWith = errors.New("error endswith cannot be mixed with offset, within or distance for the same pattern")
