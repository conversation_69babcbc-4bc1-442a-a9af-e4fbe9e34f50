package ids

import (
	"mnms/idpsystem/idps/detect"
	"time"
)

func convertToEvent(name string, d detect.InfoMatched) Event {
	e := Event{
		Id:        d.Id,
		Timestamp: time.Now().UnixMicro(),
		EthName:   name,
		Action:    d.Action,
		Protocol:  d.Protocol,
		Srcip:     d.Srcip,
		SrcPort:   d.<PERSON>c<PERSON>,
		Destip:    d.<PERSON>,
		DestPort:  d.<PERSON>,
		Message:   d.Message,
	}

	return e
}
