//go:build windows
// +build windows

package ids

import (
	"github.com/google/gopacket/pcap"
	"github.com/qeof/q"
	"golang.org/x/sys/windows"
	"golang.zx2c4.com/wireguard/windows/tunnel/winipcfg"
)

func BPFFilter(handle *pcap.Handle) {
	err := handle.SetBPFFilter("ip || ip6")
	if err != nil {
		q.Q(err)
	}
}

func FindNameByDescrption(name string) string {
	ifs, err := winipcfg.GetAdaptersAddresses(windows.AF_INET, winipcfg.GAAFlagDefault)
	if err != nil {
		return name
	}
	for _, i := range ifs {
		if i.Description() == name {
			n := i.FriendlyName()
			return n
		}
	}
	return name
}
