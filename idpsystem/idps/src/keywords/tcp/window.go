package tcp

import (
	"fmt"
	utilies "mnms/idpsystem/idps/src"
	"mnms/idpsystem/idps/src/protocol"
	"strconv"

	"github.com/google/gopacket"
)

func NewWindow() *window {
	return &window{}
}

type window struct {
	modifier uint8
	value    uint16
	ck       func(pvalue uint16, dvalue uint16) bool
}

func (w *window) SetUp(s string) error {
	v := &utilies.Lenmatch{}
	err := v.Parse(s)
	if err != nil {
		return err
	}
	w.modifier = v.Modifier
	n, err := strconv.ParseUint(v.Str, 10, 16)
	if err != nil {
		return err
	}
	w.value = uint16(n)
	f, err := w.creatFunc(v.Modifier)
	if err != nil {
		return err
	}
	w.ck = f
	return nil
}
func (w *window) Match(packet gopacket.Packet) bool {
	tp := protocol.NewTcpParser()
	b := tp.Parse(packet)
	if !b {
		return false
	}
	tcp := tp.GetTcp()
	if tcp == nil {
		return false
	}
	return w.ck(tcp.Window, w.value)
}

func (w *window) creatFunc(m uint8) (func(p uint16, d uint16) bool, error) {
	switch m {
	case '!':
		return func(p uint16, d uint16) bool {
			return p != d
		}, nil
	case 0:
		return func(p uint16, d uint16) bool {
			return p == d
		}, nil
	default:
		return nil, fmt.Errorf("not supported:%v", m)
	}

}
