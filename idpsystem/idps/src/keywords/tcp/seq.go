package tcp

import (
	"mnms/idpsystem/idps/src/keywords/gonidutil"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
)

func NewSeq() *seq {
	return &seq{}
}

type seq struct {
	v uint32
}

func (s *seq) SetUp(v any) error {
	l, err := gonidutil.ConvertToLenMatch(v)
	if err != nil {
		return err
	}
	s.v = uint32(l.Num)
	return nil
}
func (s *seq) Match(packet gopacket.Packet) bool {
	tp := protocol.NewTcpParser()
	b := tp.Parse(packet)
	if !b {
		return false
	}
	tcp := tp.GetTcp()
	if tcp == nil {
		return false
	}
	return s.v == tcp.Seq
}
