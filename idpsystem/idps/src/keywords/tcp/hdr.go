package tcp

import (
	"mnms/idpsystem/idps/mpm"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
)

func Newhdr() *hdr {
	return &hdr{}

}

type hdr struct {
	mpm.Algorithm
}

func (v *hdr) RetrieveBffer(packet gopacket.Packet) []byte {
	return v.getData(packet)
}

// mpm build
func (v *hdr) Build() error {
	if v.Algorithm != nil {
		err := v.Algorithm.Build()
		if err != nil {
			return err
		}
	}
	return nil
}

// mpm MatcheIds
func (v *hdr) MatchIds(packet gopacket.Packet) []int {
	if v.Algorithm == nil {
		return []int{}
	}
	p := v.getData(packet)
	if p == nil {
		return []int{}
	}
	return v.Algorithm.MatcheIds(p)
}

// // mpm AddContent
func (v *hdr) AddContent(contents mpm.Content) error {
	if v.Algorithm == nil {
		a, err := mpm.NewMpm()
		if err != nil {
			return err
		}
		v.Algorithm = a
	}
	return v.Algorithm.AddContent(contents)
}

func (v *hdr) getData(packet gopacket.Packet) []byte {
	tp := protocol.NewTcpParser()
	r := tp.Parse(packet)
	if !r {
		return nil
	}
	tcp := tp.GetTcp()
	if tcp == nil {
		return nil
	}
	return tcp.Contents
}
