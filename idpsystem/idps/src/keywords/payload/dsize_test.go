package payload

import (
	"fmt"
	"testing"

	"github.com/google/gonids"
)

func TestDsize(t *testing.T) {
	var tests = []struct {
		content  string
		expected bool
		packet   []byte
	}{
		{
			content:  `alert ip any any -> any any (msg:"IPv4 header keyword example";content:"6ab"; dsize:10; sid:1;)`,
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
		},
		{
			content:  `alert ip any any -> any any (msg:"IPv4 header keyword example";content:"23"; dsize:5; sid:1;)`,
			expected: captured,
			packet:   []byte("12345"),
		},
		{
			content:  `alert ip any any -> any any (msg:"IPv4 header keyword example";content:"23"; dsize:!5; sid:1;)`,
			expected: uncaptured,
			packet:   []byte("12345"),
		},
		{
			content:  `alert ip any any -> any any (msg:"IPv4 header keyword example"; content:"45"; dsize:>=6; sid:1;)`,
			expected: captured,
			packet:   []byte("12345sdf"),
		},

		{
			content:  `alert ip any any -> any any (msg:"IPv4 header keyword example"; content:"45s; dsize:10<>20; sid:1;)`,
			expected: captured,
			packet:   []byte("12345sdfsdfsdf"),
		},
		{
			content:  `alert ip any any -> any any (msg:"IPv4 header keyword example";content:"4"; dsize:>8; sid:1;)`,
			expected: uncaptured,
			packet:   []byte("1234567"),
		},
		{
			content:  `alert ip any any -> any any (msg:"IPv4 header keyword example";content:"6"; dsize:<8; sid:1;)`,
			expected: captured,
			packet:   []byte("1234567"),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			packet, err := createUdpPacket(test.packet)
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, packet)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}
