package payload

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/google/gonids"
)

const (
	byteMathFlagRelative  = 1 << iota // 1
	byteMathFlagString                // 2
	byteMathFlagBitmask               // 4
	byteMathFlagEndian                // 8
	byteMathFlagRValueVar             // 16
	byteMathFlagNBytesVar             // 32
)
const byteMathEndIanDefault = bigEndian
const byteMathBaseDefault = baseDec

type byteMath struct {
	rvalue            uint32
	result            string
	offset            int32
	bitmaskVal        uint32
	bitmaskShiftCount uint16
	flags             uint8
	localID           uint8
	nbytes            uint8
	op                string
	endian            byteEndian
	base              byteBase
}

func newByteMath(l *list, gb *gonids.ByteMatch) error {
	b, err := parseByteMath(l, gb)
	if err != nil {
		return err
	}

	d := &Detect{
		id:         l.id,
		postition:  gb.DataPosition,
		detectedID: detectByteMath,
		data:       b,
	}

	if (b.flags & byteMathFlagBitmask) > 0 {
		if (b.bitmaskVal) > 0 {
			bmask := b.bitmaskVal
			for (bmask & 0x1) == 0 {
				bmask = bmask >> 1
				b.bitmaskShiftCount++
			}
		}
	}

	err = l.appendList(d)
	if err != nil {
		return err
	}
	return nil
}

func (b *byteMath) byteMathValidateNbytesOnly() {

}

func (b *byteMath) inspect(p *Packet, nbytes uint8, rvalue uint64, value *uint64, endian uint8) int {
	if p.bufferLen == 0 {
		return 0
	}

	//buf := p.payload
	len := int32(0)
	ptr := uint32(0)
	var extbytes int
	if b.flags&byteMathFlagRelative > 0 {
		ptr += p.bufferOffset
		len = int32(p.bufferLen) - int32(p.bufferOffset)

		ptr += uint32(b.offset)
		len -= b.offset
		if int32(ptr) < 0 || len <= 0 {
			return 0
		}

	} else {
		ptr = uint32(b.offset)
		len = int32(p.bufferLen) - b.offset
	}
	if int32(ptr) < 0 || int32(nbytes) > len {
		return 0
	}
	var val uint64
	buf := p.buffer[ptr:]
	if b.flags&byteMathFlagString > 0 {
		extbytes = byteExtractStringUint64(buf, int(b.base), int32(nbytes), &val)
		if extbytes <= 0 {
			if val == 0 {
				return 0
			} else {
				return -1
			}

		}

	} else {
		var endianness int
		bmd := byteEndian(endian)
		if bmd == bigEndian {
			endianness = byteBigEndiad
		} else {
			endianness = byteLittleEndiad
		}
		extbytes = byteExtractUint64(buf, endianness, uint16(nbytes), &val)
		if extbytes != int(nbytes) {
			return 0
		}
	}

	ptr += uint32(extbytes)

	switch b.op {
	case "+":
		val += rvalue
	case "-":
		val -= rvalue
	case "/":
		if rvalue == 0 {
			return 0
		}
		val /= rvalue
	case "*":
		val *= rvalue
	case "<<":
		if rvalue < 64 {
			val <<= rvalue
		} else {
			val = 0
		}
	case ">>":
		val >>= rvalue
	}

	p.bufferOffset = ptr
	if b.flags&byteMathFlagBitmask == byteMathFlagBitmask {
		val &= uint64(b.bitmaskVal)
		if val > 0 && b.bitmaskShiftCount > 0 {
			val = val >> uint64(b.bitmaskShiftCount)
		}
	}
	*value = val
	return 1
}

func parseByteMath(l *list, gb *gonids.ByteMatch) (*byteMath, error) {
	b := &byteMath{
		offset: int32(gb.Offset),
		op:     gb.Operator,
		result: gb.Result,
		base:   byteMathBaseDefault,
		endian: byteMathEndIanDefault,
	}

	//offset
	if b.offset > 65535 || b.offset < -65535 {
		return nil, fmt.Errorf("invalid offset value: must be between -65535 and 65535: %v", gb.Offset)
	}

	//NumBytes
	if gb.NumBytes[0] != '-' && isalpha(gb.NumBytes[0]) {
		index := uint8(0)
		if !byteRetrieveSMVar(gb.NumBytes, l, &index) {
			return nil, fmt.Errorf("unknown byte_ keyword var seen in byte_math - %s", gb.NumBytes)
		}
		b.nbytes = index
		b.flags |= byteMathFlagNBytesVar
	} else {
		n, err := strconv.ParseUint(gb.NumBytes, 0, 8)
		if err != nil {
			return nil, err
		}
		b.nbytes = uint8(n)
	}

	//rvalue
	if gb.Rvalue[0] != '-' && isalpha(gb.Rvalue[0]) {
		index := uint8(0)
		if !byteRetrieveSMVar(gb.Rvalue, l, &index) {
			return nil, fmt.Errorf("unknown byte_ keyword var seen in byte_math - %s", gb.NumBytes)
		}
		b.rvalue = uint32(index)
		b.flags |= byteMathFlagRValueVar
	} else {
		n, err := strconv.ParseUint(gb.Rvalue, 0, 32)
		if err != nil {
			return nil, err
		}
		b.rvalue = uint32(n)
	}
	for _, op := range gb.Options {
		switch {
		case op == "relative":
			b.flags |= byteMathFlagRelative
		case strings.HasPrefix(op, "string"):
			v := strings.Fields(op)
			if len(v) != 2 {
				return nil, fmt.Errorf("invalid string format: %s", op)
			}
			val := v[1]
			switch val {
			case "hex":
				b.base = baseHex
			case "oct":
				b.base = baseOct
			case "dec":
				b.base = baseDec
			default:
				return nil, fmt.Errorf("invalid string value: %s", val)
			}
			b.flags |= byteMathFlagString
		case strings.HasPrefix(op, "endian"):
			v := strings.Fields(op)
			if len(v) != 2 {
				return nil, fmt.Errorf("invalid endian format: %s", op)
			}
			b.flags |= byteMathFlagEndian
			val := v[1]
			switch val {
			case "big":
				b.endian = bigEndian
			case "little":
				b.endian = littleEndian
			case "dce":
				b.endian = endianDCE
			default:
				return nil, fmt.Errorf("invalid endian value: %s", val)
			}
		case op == "dce":
			b.flags |= byteMathFlagEndian
			b.endian = endianDCE
		case strings.HasPrefix(op, "bitmask"):
			b.flags |= byteMathFlagBitmask
			args := strings.Fields(op)
			if len(args) != 2 {
				return nil, fmt.Errorf("invalid bitmask format: %s", op)
			}
			v, err := strconv.ParseUint(args[1], 0, 32)
			if err != nil {
				return nil, err
			}
			b.bitmaskVal = uint32(v)
		}
	}
	return b, nil

}
