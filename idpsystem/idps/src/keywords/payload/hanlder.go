package payload

import "github.com/google/gonids"

func mapContentHandler(l *list, co *gonids.ContentOption) error {
	m := map[string]func(*list, string) error{
		"nocase": func(l *list, s string) error {
			return noCaseSetup(l, s)
		},
		"offset": func(l *list, s string) error {
			return offsetSetUp(l, s)
		},
		"depth": func(l *list, s string) error {
			return depthSetUp(l, s)
		},
		"startswith": func(l *list, s string) error {
			return startsWithSetUp(l, s)
		},
		"endswith": func(l *list, s string) error {
			return endWithSetUp(l, s)
		},
		"distance": func(l *list, s string) error {
			return distanceSetUp(l, s)
		},
		"within": func(l *list, s string) error {
			return withinSetUp(l, s)
		},
		"replace": func(l *list, s string) error {
			return replaceSetUp(l, s)
		},
	}
	if f, ok := m[co.Name]; ok {
		return f(l, co.Value)
	}
	return nil
}

func mapByteHandler(l *list, bm *gonids.ByteMatch, context any) error {
	m := map[string]func(*list, *gonids.ByteMatch, any) error{
		"isdataat": func(l *list, bm *gonids.ByteMatch, _ any) error {
			return newIsDataAt(l, bm)
		},
		"byte_test": func(l *list, bm *gonids.ByteMatch, _ any) error {
			return newByteTest(l, bm)
		},
		"byte_math": func(l *list, bm *gonids.ByteMatch, _ any) error {
			return newByteMath(l, bm)
		},
		"byte_jump": func(l *list, bm *gonids.ByteMatch, _ any) error {
			return newByteJump(l, bm)
		},
		"byte_extract": func(l *list, bm *gonids.ByteMatch, context any) error {
			return newByteExtract(l, bm, context)
		},
		"entropy": func(l *list, bm *gonids.ByteMatch, context any) error {
			return newEntropy(l, bm)
		},
	}
	if f, ok := m[bm.Kind.String()]; ok {
		return f(l, bm, context)
	}
	return nil
}

func mapSizeHandler(l *list, lm *gonids.LenMatch) error {
	m := map[string]func(l *list, lm *gonids.LenMatch) error{
		"bsize": func(l *list, lm *gonids.LenMatch) error {
			return newBsize(l, lm)
		},
		"dsize": func(l *list, lm *gonids.LenMatch) error {
			return newDsize(l, lm)
		},
	}
	if f, ok := m[lm.Kind.String()]; ok {
		return f(l, lm)
	}
	return nil
}
