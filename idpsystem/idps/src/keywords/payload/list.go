package payload

type list struct {
	id   int
	head *Detect
	tail *Detect
}

func newList(id int) *list {
	return &list{id: id}
}
func (l *list) appendList(node *Detect) error {
	if l.head == nil {
		l.head = node
		l.tail = node
		return nil
	}
	cur := l.head
	for cur != nil {
		if cur.next == nil {
			cur.next = node
			node.next = nil
			node.prev = cur
			l.tail = node
			return nil
		}
		cur = cur.next
	}
	return nil
}
