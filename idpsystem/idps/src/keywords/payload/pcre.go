package payload

import (
	"errors"
	"fmt"
	"runtime"
	"unicode"

	"github.com/google/gonids"
	"github.com/greyreo/pcre2"
)

const (
	pcreRelative = 0x00001
	pcreRawBytes = 0x00002
	pcreCaseLess = 0x00004

	pcreRelativeNext = 0x00040
	pcreNegate       = 0x00080

	pcreCaptureMax = 8
)

type pcreData struct {
	pcre  *pcre2.Regexp
	m     *pcre2.Matcher
	flags uint32
}

func newPcreData(l *list, pc *gonids.PCRE) error {
	pd, err := parsePcre(pc)
	if err != nil {
		return err
	}
	d := &Detect{
		id:         l.id,
		detectedID: detectPCRE,
		postition:  pc.DataPosition,
		data:       pd,
	}
	err = l.appendList(d)
	if err != nil {
		return err
	}
	if !(pd.flags&pcreRelative > 0) {
		return nil
	}

	prev := detectGetLastSMFromLists(l, []detectedId{detectContent, detectPCRE})
	if prev == nil {
		return fmt.Errorf("%v", "pcre with /R (relative) needs "+
			"preceding match in the same buffer")
	}
	if prev.detectedID == detectContent {
		ct, _ := prev.data.(*content)
		ct.flags |= relativeNext
	} else if prev.detectedID == detectPCRE {
		tmp := prev.data.(*pcreData)
		tmp.flags |= pcreRelativeNext
	}

	return nil
}

func parsePcre(pc *gonids.PCRE) (*pcreData, error) {
	pd := &pcreData{}
	checkhostheader := 0
	if pc.Negate {
		pd.flags |= pcreNegate
	}
	//to do support http
	var flags uint32
	for _, v := range pc.Options {
		switch v {
		case 'A':
			flags |= pcre2.ANCHORED
		case 'E':
			flags |= pcre2.DOLLAR_ENDONLY
		case 'G':
			flags |= pcre2.UNGREEDY
		case 'i':
			flags |= pcre2.CASELESS
			pd.flags |= pcreCaseLess
		case 'm':
			flags |= pcre2.MULTILINE
		case 's':
			flags |= pcre2.DOTALL
		case 'x':
			flags |= pcre2.EXTENDED
		case 'o':
		case 'B': /* snort's option */
			pd.flags |= pcreRawBytes
		case 'R': /* snort's option */
			pd.flags |= pcreRelative
		case 'U': /* snort's option */
			if pd.flags&pcreRawBytes > 0 {
				return nil, errors.New("regex modifier 'U' inconsistent with 'B")
			}
		case 'V':
			if pd.flags&pcreRawBytes > 0 {
				return nil, errors.New("regex modifier 'V' inconsistent with 'B'")
			}
		case 'W':
			{
				if pd.flags&pcreRawBytes > 0 {
					return nil, errors.New("regex modifier 'W' inconsistent with 'B'")

				}
				//int list = DetectBufferTypeGetByName("http_host");
				//*sm_list = DetectPcreSetList(*sm_list, list);
				//*alproto = ALPROTO_HTTP1;
				checkhostheader = 1

			}
		case 'Z':
			{
				if pd.flags&pcreRawBytes > 0 {
					return nil, errors.New("regex modifier 'Z' inconsistent with 'B'")

				}
				//int list = DetectBufferTypeGetByName("http_raw_host");
				//	*sm_list = DetectPcreSetList(*sm_list, list);
				//*alproto = ALPROTO_HTTP1;

			}
		case 'H':
			{ /* snort's option */
				if pd.flags&pcreRawBytes > 0 {
					return nil, errors.New("regex modifier 'H' inconsistent with 'B'")

				}
				/*int list = DetectBufferTypeGetByName("http_header");
				*sm_list = DetectPcreSetList(*sm_list, list);
				*alproto = ALPROTO_HTTP1;*/

			}
		case 'I':
			{ /* snort's option */
				if pd.flags&pcreRawBytes > 0 {
					return nil, errors.New("regex modifier 'I' inconsistent with 'B'")

				}
				/*int list = DetectBufferTypeGetByName("http_raw_uri");
				*sm_list = DetectPcreSetList(*sm_list, list);
				*alproto = ALPROTO_HTTP1;*/

			}
		case 'D':
			{ /* snort's option */
				/*	int list = DetectBufferTypeGetByName("http_raw_header");
				*sm_list = DetectPcreSetList(*sm_list, list);
				*alproto = ALPROTO_HTTP1;*/

			}
		case 'M':
			{ /* snort's option */
				if pd.flags&pcreRawBytes > 0 {
					return nil, errors.New("regex modifier 'M' inconsistent with 'B'")

				}
				/*int list = DetectBufferTypeGetByName("http_method");
				*sm_list = DetectPcreSetList(*sm_list, list);
				*alproto = ALPROTO_HTTP1;*/

			}
		case 'C':
			{ /* snort's option */
				if pd.flags&pcreRawBytes > 0 {
					return nil, errors.New("regex modifier 'C' inconsistent with 'B'")

				}
				/*int list = DetectBufferTypeGetByName("http_cookie");
				*sm_list = DetectPcreSetList(*sm_list, list);
				*alproto = ALPROTO_HTTP1;*/

			}
		case 'P':
			{
				/* snort's option (http request body inspection) */
				/*int list = DetectBufferTypeGetByName("http_client_body");
				*sm_list = DetectPcreSetList(*sm_list, list);
				*alproto = ALPROTO_HTTP1;*/

			}
		case 'Q':
			{
				/*int list = DetectBufferTypeGetByName("file_data");
				/* suricata extension (http response body inspection) */
				/**sm_list = DetectPcreSetList(*sm_list, list);
				*alproto = ALPROTO_HTTP1;*/

			}
		case 'Y':
			{
				/* snort's option */
				/*	int list = DetectBufferTypeGetByName("http_stat_msg");
				*sm_list = DetectPcreSetList(*sm_list, list);
				*alproto = ALPROTO_HTTP1;*/

			}
		case 'S':
			{
				/* snort's option */
				/*int list = DetectBufferTypeGetByName("http_stat_code");
				*sm_list = DetectPcreSetList(*sm_list, list);
				*alproto = ALPROTO_HTTP1;*/

			}
		default:
			return nil, fmt.Errorf("unknown regex modifier '%c'", v)
		}

	}

	if (checkhostheader) == 1 {
		if (pd.flags & pcreCaseLess) > 0 {
			return nil, errors.New("http host pcre(\"W\") " +
				"specified along with \"i(caseless)\" modifier.  " +
				"Since the hostname buffer we match against " +
				"is actually lowercase, having a " +
				"nocase is redundant")
		} else if isAllUpper(string(pc.Pattern)) {
			return nil, errors.New("pcre host(\"W\") " +
				"specified has an uppercase char.  " +
				"Since the hostname buffer we match against " +
				"is actually lowercase, please specify an " +
				"all lowercase based pcre")

		}
	}
	pe, err := pcre2.CompileJIT(string(pc.Pattern), flags, pcre2.JIT_COMPLETE)
	if err != nil {
		return nil, err
	}
	m := pe.NewMatcher()
	pd.pcre = pe
	pd.m = m
	runtime.SetFinalizer(m, func(match *pcre2.Matcher) {
		match.Free()
	})
	return pd, nil
}

func isAllUpper(s string) bool {
	for _, r := range s {
		if unicode.IsLetter(r) && !unicode.IsUpper(r) {
			return false
		}
	}
	return true
}

func detectPcrePayload(d *Detect, p *Packet) matchFlag {
	pe := d.data.(*pcreData)
	prebuffoffset := p.bufferOffset
	prevoffset := uint32(0)
	p.pcreMatchStartOffset = 0
	for {
		r := detectPcrePayloadMatch(d, p)
		if r == 0 {
			return noMatch
		}
		if !(pe.flags&pcreRelativeNext > 0) {
			return matched(d, p)
		}
		prevoffset = p.pcreMatchStartOffset
		res := detectEngineContentInspectionInternal(d, p)
		if res == match {
			return match
		} else if res == noMatchDiscontinue {
			return noMatchDiscontinue
		}
		if prevoffset == 0 {
			return noMatch
		}
		p.bufferOffset = prebuffoffset
		p.pcreMatchStartOffset = prevoffset
	}

}

func detectPcrePayloadMatch(d *Detect, p *Packet) int {
	pe := d.data.(*pcreData)
	ptr := uint32(0)
	ret := 0
	length := uint32(0)
	if pe.flags&pcreRelative > 0 {
		ptr = p.bufferOffset
		length = p.bufferLen + p.bufferOffset
	} else {
		length = p.bufferLen
	}
	startoffset := 0
	if p.pcreMatchStartOffset != 0 {
		startoffset = int(p.pcreMatchStartOffset) - int(ptr)
	}
	buf := p.buffer[startoffset:]
	mathcer := pe.m
	b := mathcer.Match(buf[:length], 0)
	if b {
		if pe.flags&pcreNegate > 0 {
			ret = 0
		} else {
			loc := mathcer.Index()
			p.bufferOffset = ptr + uint32(loc[1])
			p.pcreMatchStartOffset = ptr + uint32(loc[0]+1)
			ret = 1
		}
	} else {
		if pe.flags&pcreNegate > 0 {
			ret = 1
		} else {
			ret = 0
		}
	}

	return ret
}
