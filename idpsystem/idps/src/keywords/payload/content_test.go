package payload

import (
	"fmt"
	"mnms/idpsystem/idps/src/protocol"
	"net"
	"testing"

	"github.com/google/gonids"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

const captured = true
const uncaptured = false

func TestContent(t *testing.T) {

	var tests = []struct {
		content  string
		expected bool
		packet   []byte
	}{

		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"66";sid:1;)`,
			expected: true,
			packet:   []byte("88546abcsdfdssdf666dfg"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"6666";sid:1;)`,
			expected: false,
			packet:   []byte("88546abcsdfds66sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:!"abc";content:"6666";sid:1;)`,
			expected: false,
			packet:   []byte("88546abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:!"6666";sid:1;)`,
			expected: false,
			packet:   []byte("88546abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:!"abc";content:!"6666";sid:1;)`,
			expected: true,
			packet:   []byte("88546sdfdssdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"|0a|";sid:1;)`,
			expected: true,
			packet:   []byte{0x01, 0x02, 0x05, 0x90, 0x0A, 0x55, 0x01, 0x3},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestDetectContent:%v", index), func(t *testing.T) {

			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestContentOffsetDepthStartwith(t *testing.T) {
	var tests = []struct {
		title    string
		content  string
		expected bool
		packet   []byte
	}{
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:2;depth:5;sid:1;)`,
			expected: false,
			packet:   []byte("1abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:2;depth:5;sid:1;)`,
			expected: true,
			packet:   []byte("12abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:2;depth:5;sid:1;)`,
			expected: true,
			packet:   []byte("123abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:2;depth:5;sid:1;)`,
			expected: true,
			packet:   []byte("1234abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:2;depth:5;sid:1;)`,
			expected: false,
			packet:   []byte("12345abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:10;content:"66";sid:1;)`,
			expected: false,
			packet:   []byte("123456789abcsdfdssdf666dfg"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"6666";offset:2;sid:1;)`,
			expected: true,
			packet:   []byte("qq6666sabcdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"6666";offset:20;sid:1;)`,
			expected: false,
			packet:   []byte("88546abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";depth:10;sid:1;)`,
			expected: true,
			packet:   []byte("88546abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";depth:5;sid:1;)`,
			expected: false,
			packet:   []byte("88546abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:2;depth:5;sid:1;)`,
			expected: true,
			packet:   []byte("46abcsdfds6666sdf"),
		},

		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:2;depth:5;sid:1;)`,
			expected: false,
			packet:   []byte("88546abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:0;depth:3;sid:1;)`,
			expected: true,
			packet:   []byte("abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";offset:0;depth:3;sid:1;)`,
			expected: false,
			packet:   []byte("88546abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";startswith;sid:1;)`,
			expected: false,
			packet:   []byte("88546abcsdfds6666sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";startswith;sid:1;)`,
			expected: true,
			packet:   []byte("abcsdfds6666sdf"),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestEndswithIsdataat(t *testing.T) {

	var tests = []struct {
		content  string
		expected bool
		packet   []byte
	}{
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";isdataat:5;sid:1;)`,
			expected: false,
			packet:   []byte("ffabc"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";isdataat:6;sid:1;)`,
			expected: true,
			packet:   []byte("ffabc123"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";isdataat:5,relative;sid:1;)`,
			expected: true,
			packet:   []byte("asabc12345"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";isdataat:5,relative;sid:1;)`,
			expected: false,
			packet:   []byte("asabc1234"),
		},

		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";isdataat:!5,relative;sid:1;)`,
			expected: true,
			packet:   []byte("asabc1234"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";isdataat:!5;sid:1;)`,
			expected: true,
			packet:   []byte("qabc"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";isdataat:!5;sid:1;)`,
			expected: true,
			packet:   []byte("aaabc"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";isdataat:!5;sid:1;)`,
			expected: false,
			packet:   []byte("123abcsdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";isdataat:!5;sid:1;)`,
			expected: false,
			packet:   []byte("123abdfg"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";endswith;sid:1;)`,
			expected: false,
			packet:   []byte("ffabc123"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";endswith;sid:1;)`,
			expected: false,
			packet:   []byte("sdfdfxcv"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";endswith;sid:1;)`,
			expected: true,
			packet:   []byte("ffdfgabc"),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestDistancewithin(t *testing.T) {
	var tests = []struct {
		content  string
		expected bool
		packet   []byte
	}{
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"66";distance:3;sid:1;)`,
			expected: false,
			packet:   []byte("sdfabczx66sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"66";distance:3;sid:1;)`,
			expected: true,
			packet:   []byte("sdfabczx966sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"66";distance:3;sid:1;)`,
			expected: true,
			packet:   []byte("sdfabczx123966sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";within:5;sid:1;)`,
			expected: true,
			packet:   []byte("sdfabc1888sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";within:5;sid:1;)`,
			expected: true,
			packet:   []byte("sdfabc12888sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";within:5;sid:1;)`,
			expected: false,
			packet:   []byte("sdfabc123888sdf"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";distance:2;within:5;sid:1;)`,
			expected: false,
			packet:   []byte("abc888"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";distance:2;within:5;sid:1;)`,
			expected: false,
			packet:   []byte("abc1888"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";distance:2;within:5;sid:1;)`,
			expected: true,
			packet:   []byte("abc12888"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";distance:2;within:5;sid:1;)`,
			expected: true,
			packet:   []byte("abc1234888"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";distance:2;within:5;sid:1;)`,
			expected: false,
			packet:   []byte("abc12345888"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";distance:2;within:5;sid:1;)`,
			expected: false,
			packet:   []byte("abc123456888"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"66";distance:3;within:2;sid:1;)`,
			expected: true,
			packet:   []byte("dfdfabc12366"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"abc";content:"888";distance:2;within:3;;sid:1;)`,
			expected: true,
			packet:   []byte("sdfabcsdfabcas888dfg"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"aBc"; nocase; content:"abca"; distance:-10; within:4; sid:1;)`,
			expected: true,
			packet:   []byte("abcaBcd"),
		},
		{
			content:  `alert icmp any any -> any any (msg:"test";content:"User-Agent|3A| Mozilla/5.0 |28|Macintosh|3B| "; content:"Firefox/3."; distance:0; content:!"Firefox/3.6.12"; distance:-10; content:!"Mozilla/5.0 |28|Macintosh|3B| U|3B| Intel Mac OS X 10.5|3B| en-US|3B| rv|3A|1.9.1b4|29| Gecko/20090423 Firefox/3.6 GTB5"; sid:1; rev:1;)`,
			expected: false,
			packet:   []byte("User-Agent: Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10.5; en-US; rv:1.9.1b4) Gecko/20090423 Firefox/3.6 GTB5"),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func createUdpPacket(payload []byte) (gopacket.Packet, error) {
	ipLayer := &layers.IPv4{
		SrcIP:    net.ParseIP("127.0.0.1"),
		DstIP:    net.ParseIP("127.0.0.1"),
		Version:  4,
		Protocol: layers.IPProtocolUDP,
	}

	udpLayer := &layers.UDP{
		SrcPort: layers.UDPPort(10),
		DstPort: layers.UDPPort(20),
	}

	udpLayer.SetNetworkLayerForChecksum(ipLayer)

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, udpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return protocol.ConvertGoNetWorkPacket(buffer.Bytes())
}
