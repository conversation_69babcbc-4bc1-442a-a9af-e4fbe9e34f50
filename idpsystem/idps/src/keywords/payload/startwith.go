package payload

import (
	"errors"
)

func startsWithSetUp(l *list, _ string) error {
	dc := detectGetLastSMFromLists(l, []detectedId{detectContent})
	if dc == nil {
		return errors.New("startswith needs a " +
			"preceding content option")
	}
	d, _ := dc.data.(*content)
	if (d.flags & depth) > 0 {
		return errors.New("can't use multiple " +
			"depth/startswith settings for the same content")

	}
	if (d.flags&within > 0) || (d.flags&distance > 0) {
		return errors.New("can't use a relative " +
			"keyword like within/distance with a absolute " +
			"relative keyword like depth/offset for the same " +
			"content")

	}
	if d.flags&contentNegated > 0 && d.flags&fastpPattern > 0 {
		return errors.New("can't have a relative " +
			"negated keyword set along with a 'fast_pattern'")

	}
	if (d.flags & fastpPatternOnly) > 0 {
		return errors.New("can't have a relative " +
			"keyword set along with 'fast_pattern:only;'")

	}

	if d.flags&offset > 0 {
		return errors.New("can't mix offset " +
			"with startswith")
	}
	d.depth = d.contentLen
	d.flags |= depth
	d.flags |= startsWith

	return nil
}
