package payload

import (
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
)

type PacketConfig struct {
	GoPacket   gopacket.Packet
	Payload    []byte   //data we want to detect
	PayloadLen uint32   //data length
	Buffer     []byte   //instance data we detect
	BufferLen  uint32   //instance data length
	ByteValues []uint64 //byte used
}

type Packet struct {
	packet               gopacket.Packet
	payload              []byte //data we want detect
	payloadLen           uint32
	buffer               []byte //instance data we detect
	bufferLen            uint32
	bufferOffset         uint32
	pcreMatchStartOffset uint32
	flags                uint8
	byteValues           []uint64 //byte used
	replace              *replaceList
}

// NewPacket creates a new Packet instance.
// p is the gopacket.Packet instance.
// buffer is the data we want to detect.
func NewPacket(p PacketConfig) *Packet {
	packet := &Packet{packet: p.GoPacket, payload: p.Payload,
		payloadLen: p.PayloadLen, byteValues: p.ByteValues, buffer: p.<PERSON>uff<PERSON>, bufferLen: p.<PERSON>uffer<PERSON>en}
	return packet
}

func NewPacketByByte(b []byte) (*Packet, error) {
	packet, err := protocol.ConvertGoNetWorkPacket(b)
	if err != nil {
		return nil, err
	}
	p := &Packet{packet: packet}
	if app := packet.ApplicationLayer(); app != nil {
		p.payload = app.LayerContents()
		p.payloadLen = uint32(len(p.payload))
		p.buffer = p.payload
		p.bufferLen = uint32(len(p.payload))
	}
	return p, nil
}

func newTestPacket(pakcet []byte, ByteValuesLeh int, gopacket gopacket.Packet) *Packet {
	confg := PacketConfig{Payload: pakcet, PayloadLen: uint32(len(pakcet)),
		Buffer: pakcet, BufferLen: uint32(len(pakcet)), ByteValues: make([]uint64, ByteValuesLeh),
		GoPacket: gopacket}

	return NewPacket(confg)
}
