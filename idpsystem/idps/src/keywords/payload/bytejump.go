package payload

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/google/gonids"
)

type byteJumpBase int

const (
	byteJumpBaseUnset byteJumpBase = 0
	byteJumpBaseOct   byteJumpBase = 8
	byteJumpBaseDec   byteJumpBase = 10
	byteJumpBaseHex   byteJumpBase = 16
)

type byteJumpFlag uint16

const (
	byteJumpBegin     = 1 << iota // 0 -> 1  ("from_beginning" jump)
	byteJumpLittle                // 1 -> 2  ("little" endian value)
	byteJumpBig                   // 2 -> 4  ("big" endian value)
	byteJumpString                // 3 -> 8  ("string" value)
	byteJumpRelative              // 4 -> 16 ("relative" offset)
	byteJumpAlign                 // 5 -> 32 ("align" offset)
	byteJumpDCE                   // 6 -> 64 ("dce" enabled)
	byteJumpOffsetBE              // 7 -> 128 ("byte extract" enabled)
	byteJumpEnd                   // 8 -> 256 ("from_end" jump)
	byteJumpNBytesVar             // 9 -> 512 ("nbytes string")
	byteJumpOffsetVar             // 10 -> 1024 ("byte extract value enabled")
)

type byteJump struct {
	nbytes      uint8        /**< Number of bytes to compare */
	base        byteJumpBase /**< String value base (oct|dec|hex) */
	flags       byteJumpFlag /**< Flags (big|little|relative|string) */
	offset      int32        /**< Offset in payload to extract value */
	post_offset int32        /**< Offset to adjust post-jump */
	multiplier  uint16       /**< Multiplier for nbytes (multiplier n)*/
}

func newByteJump(l *list, gb *gonids.ByteMatch) error {
	byt, err := strconv.Atoi(gb.NumBytes)
	if err != nil {
		return err
	}
	b := &byteJump{
		nbytes:     uint8(byt),
		offset:     int32(gb.Offset),
		multiplier: 1,
	}
	for _, op := range gb.Options {
		switch {
		case op == "relative":
			b.flags |= byteJumpRelative
		case op == "string":
			b.flags |= byteJumpString
		case op == "dec":
			b.base |= byteJumpBaseDec
		case op == "hex":
			b.base |= byteJumpBaseHex
		case op == "oct":
			b.base |= byteJumpBaseOct
		case op == "big":
			if b.flags&byteJumpLittle == byteJumpLittle {
				b.flags ^= byteJumpLittle
			}
			b.flags |= byteJumpBig
		case op == "little":
			b.flags |= byteJumpLittle
		case op == "from_beginning":
			b.flags |= byteJumpBegin
		case op == "from_end":
			b.flags |= byteJumpEnd
		case op == "align":
			b.flags |= byteJumpAlign
		case strings.HasPrefix(op, "multiplier"):
			args := strings.Split(op, " ")
			if len(args) != 2 {
				return fmt.Errorf("invalid multiplier format: %s", op)
			}
			n := stringParseU16RangeCheck(&b.multiplier, 10, args[1], 1, 65535)
			if n <= 0 {
				return fmt.Errorf("malformed multiplier: %s", op)
			}
		case strings.HasPrefix(op, "post_offset"):
			args := strings.Split(op, " ")
			if len(args) != 2 {
				return fmt.Errorf("invalid post_offset format: %s", op)
			}
			n := stringParseI32RangeCheck(&b.post_offset, 10, args[1], -65535, 65535)
			if n <= 0 {
				return fmt.Errorf("malformed multiplier: %s", op)
			}
		case op == "dce":
			b.flags |= byteJumpDCE
		default:
			return fmt.Errorf("unknown option:%v", op)
		}
	}

	if b.flags&byteJumpEnd == byteJumpEnd && b.flags&byteJumpBegin == byteJumpBegin {
		return errors.New("'from_end' and 'from_beginning'cannot be used in the same byte_jump statement")
	}

	if !(b.flags&byteJumpString == byteJumpString) && b.base != byteJumpBaseUnset {
		return errors.New("cannot use a base without \"string\"")
	}

	d := &Detect{
		id:         l.id,
		postition:  gb.DataPosition,
		detectedID: detectByteJump,
	}
	d.data = b

	err = l.appendList(d)
	if err != nil {
		return err
	}
	return nil
}

func (b *byteJump) inspect(p *Packet, flags uint16, offset, nbytes int32) bool {
	if offset >= int32(p.bufferLen) {
		return false
	}
	var val uint64
	var extbytes int
	buffer := p.buffer[offset:]
	len := int32(p.bufferLen) - offset
	if flags&byteJumpRelative == byteJumpRelative {
		if int32(p.bufferOffset) >= len {
			return false
		}
		buffer = buffer[p.bufferOffset:]
		len -= int32(p.bufferOffset)
		if nbytes != 0 && len <= 0 {
			return false
		}
	}
	if nbytes > len {
		return false
	}
	if flags&byteJumpString > 0 {
		extbytes = byteExtractStringUint64(buffer, int(b.base), nbytes, &val)
		if extbytes <= 0 {
			return false
		}
	} else {
		var end int
		if flags&byteJumpLittle > 0 {
			end = byteLittleEndiad
		} else {
			end = byteBigEndiad
		}
		extbytes = byteExtractUint64(buffer, end, uint16(nbytes), &val)
		if extbytes != int(nbytes) {
			return false
		}
	}
	val *= uint64(b.multiplier)
	if flags&byteJumpAlign > 0 {
		if val%4 != 0 {
			val += 4 - (val % 4)
		}
	}
	v := int64(val) + int64(b.post_offset)
	val = uint64(v)

	jump := int64(0)
	if flags&byteJumpBegin > 0 {
		jump = int64(val)
	} else if flags&byteJumpEnd > 0 {
		jump = int64(p.bufferLen) + int64(val)
	} else {
		jump = int64(p.bufferLen) - int64(len) + int64(val) + int64(extbytes)
	}

	if jump < 0 {
		jump = 0
	} else if jump > int64(p.bufferLen) {
		return false
	}
	p.bufferOffset = uint32(jump)

	return true
}
