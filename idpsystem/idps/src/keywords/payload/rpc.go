package payload

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"mnms/idpsystem/idps/src/protocol"
	"strconv"
	"strings"

	"github.com/google/gonids"
	"github.com/google/gopacket"
)

const (
	detectRPCCheckProgram   = 1 << iota // 0x01
	detectRPCCheckVersion               // 0x02
	detectRPCCheckProcedure             // 0x04
)

func NewRpc() *Rpc {
	return &Rpc{}
}

type Rpc struct {
	program        uint32
	programVersion uint32
	procedure      uint32
	flags          uint8
}

func (r *Rpc) SetUp(input any) error {
	if v, ok := input.(*gonids.RPC); ok {
		n, err := strconv.ParseUint(v.Application, 0, 32)
		if err != nil {
			return err
		}
		r.program = uint32(n)
		r.flags |= detectRPCCheckProgram
		v.Version = strings.TrimSpace(v.Version)
		if v.Version != "*" {
			n, err := strconv.ParseUint(v.Version, 0, 32)
			if err != nil {
				return err
			}
			r.programVersion = uint32(n)
			r.flags |= detectRPCCheckVersion
		}
		v.Procedure = strings.TrimSpace(v.Procedure)
		if v.Procedure != "*" {
			n, err := strconv.ParseUint(v.Procedure, 0, 32)
			if err != nil {
				return err
			}
			r.procedure = uint32(n)
			r.flags |= detectRPCCheckProcedure
		}
	} else {
		return fmt.Errorf("error type,should be : %v", "gonids.RPC")
	}
	return nil
}

func (r *Rpc) Match(p gopacket.Packet) bool {
	var payload []byte
	t := protocol.NewTcpParser()
	if t.Parse(p) {
		tcp := t.GetTcp()
		if len(tcp.Payload) < 28 {
			return false
		}
		payload = tcp.Payload[4:]
	} else {
		u := protocol.NewUdpParser()
		if u.Parse(p) {
			udp := u.GetUdp()
			if len(udp.Payload) < 24 {
				return false
			}
			payload = udp.Payload
		}
	}
	if payload == nil {
		return false
	}
	rpc := newRpcMsg()
	rpc.parse(payload)
	if r.program != rpc.prog {
		return false
	}
	if r.flags&detectRPCCheckVersion != 0 && r.programVersion != rpc.vers {
		return false
	}
	if r.flags&detectRPCCheckProcedure != 0 && r.procedure != rpc.proc {
		return false
	}

	return true
}

type rpcMsg struct {
	xid     uint32
	msgType uint32
	rpcvers uint32
	prog    uint32
	vers    uint32
	proc    uint32
}

func newRpcMsg() *rpcMsg {
	return &rpcMsg{}
}

func (r *rpcMsg) parse(packet []byte) error {
	buf := bytes.NewReader(packet)
	_ = binary.Read(buf, binary.BigEndian, &r.xid)
	_ = binary.Read(buf, binary.BigEndian, &r.msgType)
	_ = binary.Read(buf, binary.BigEndian, &r.rpcvers)
	_ = binary.Read(buf, binary.BigEndian, &r.prog)
	_ = binary.Read(buf, binary.BigEndian, &r.vers)
	_ = binary.Read(buf, binary.BigEndian, &r.proc)
	return nil
}
