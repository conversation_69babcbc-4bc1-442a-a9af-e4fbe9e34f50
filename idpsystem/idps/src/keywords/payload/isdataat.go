package payload

import (
	"fmt"

	"github.com/google/gonids"
)

type isDataAtFlag uint8

const (
	isDataAtRleative isDataAtFlag = 1 << iota
	isDataAtRawBytes
	isDataAtNEGATED
	isDataOffsetVar
)

type isDataAt struct {
	dataat uint16       /* data offset to match */
	flags  isDataAtFlag /* isdataat options*/
}

func newIsDataAt(l *list, gb *gonids.ByteMatch) error {
	offset := ""
	d, err := parseIsDataAt(l.id, gb, &offset)
	if err != nil {
		return err
	}
	var prev *Detect
	idad, _ := d.data.(*isDataAt)
	if idad.flags&isDataAtRleative > 0 {
		prev = detectGetLastSMFromLists(l, []detectedId{detectContent,
			detectPCRE, detectByteTest, detectByteJump, detectByteExtract,
			detectIsDataAt, detectByteMath})
	}

	if len(offset) != 0 {
		index := uint8(0)
		if !byteRetrieveSMVar(offset, l, &index) {
			return fmt.Errorf("unknown byte_ keyword var seen in isdataat - %s", offset)
		}
		data, _ := d.data.(*isDataAt)
		data.dataat = uint16(index)
		data.flags |= isDataOffsetVar
	}
	/* 'ends with' scenario */
	if prev != nil && prev.detectedID == detectContent && idad.dataat == 1 &&
		(idad.flags&isDataAtRleative|isDataAtNEGATED) == isDataAtRleative|isDataAtNEGATED {
		cd, _ := prev.data.(*content)
		cd.flags |= endsWith
		return nil
	}

	err = l.appendList(d)
	if err != nil {
		return err
	}
	if !(idad.flags&isDataAtRleative > 0) {
		return nil
	}
	if prev == nil {
		return nil
	}
	if prev.detectedID == detectContent {
		cd, _ := prev.data.(*content)
		cd.flags |= relativeNext
	} else if prev.detectedID == detectPCRE {
		//todo
	}

	return nil
}
func parseIsDataAt(id int, gb *gonids.ByteMatch, offset *string) (*Detect, error) {

	d := &Detect{
		id:         id,
		postition:  gb.DataPosition,
		detectedID: detectIsDataAt,
	}
	isDataAt := &isDataAt{}
	if gb.Negate {
		isDataAt.flags |= isDataAtNEGATED
	}
	if gb.NumBytes[0] != '-' && isalpha(gb.NumBytes[0]) {
		*offset = gb.NumBytes
	} else {
		if stringParseUint16(&isDataAt.dataat, 10, gb.NumBytes) < 0 {
			return nil, fmt.Errorf("isdataat out of range,%v", gb.NumBytes)
		}
	}
	for _, v := range gb.Options {
		switch v {
		case "relative":
			isDataAt.flags |= isDataAtRleative
		}
	}
	d.data = isDataAt
	return d, nil
}

func detectIsDataAtID(d *Detect, p *Packet) matchFlag {
	isdataat, _ := d.data.(*isDataAt)
	dataat := uint32(isdataat.dataat)
	if isdataat.flags&isDataAtRleative == isDataAtRleative {
		if p.bufferOffset+dataat > p.bufferLen {
			if isdataat.flags&isDataAtNEGATED == isDataAtNEGATED {
				return matched(d, p)
			}
			return noMatch
		} else {
			if isdataat.flags&isDataAtNEGATED == isDataAtNEGATED {
				return noMatch
			}
			return matched(d, p)
		}
	} else {
		if dataat < p.bufferLen {
			if isdataat.flags&isDataAtNEGATED == isDataAtNEGATED {
				return noMatch
			}
			return matched(d, p)
		} else {
			if isdataat.flags&isDataAtNEGATED == isDataAtNEGATED {
				return matched(d, p)
			}
			return noMatch
		}
	}

}
