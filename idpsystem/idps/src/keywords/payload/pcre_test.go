package payload

import (
	"fmt"
	"testing"

	"github.com/google/gonids"
)

func TestPcre(t *testing.T) {
	var tests = []struct {
		title    string
		content  string
		expected bool
		packet   []byte
	}{
		{
			content: "alert tcp any any -> any any (msg:\"pcre with an ending slash\"; pcre:\"/ " +
				"lalala\\\\/\"; sid:1;)",
			expected: true,
			packet:   []byte("lalala lalala\\ lala\n"),
		},
		{
			content: "alert tcp any any -> any any (msg:\"pcre with an ending slash\"; " +
				"pcre:\"/^(la)+$/\"; sid:1;)",
			expected: true,
			packet:   []byte("lalala\n"),
		},
		{
			content: "alert tcp any any -> any any (msg:\"pcre with an ending slash\"; " +
				"pcre:\"/^(la)+$/\"; sid:1;)",
			expected: true,
			packet:   []byte("lalala"),
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v,rule:%v", index, test.content), func(t *testing.T) {
			r, err := gonids.ParseRule(test.content)
			if err != nil {
				t.Fatal(err)
			}
			ret := Data{}
			d, err := NewdetectContent(r.SID, *r, &ret)
			if err != nil {
				t.Fatal(err)
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			p := newTestPacket(test.packet, 1, nil)
			b := d.DetectContentInspection(p)
			if b != test.expected {
				t.Error("not found packet")
			}
		})
	}
}
