package payload

import (
	"errors"
	"fmt"
)

func offsetSetUp(l *list, v string) error {
	dc := detectGetLastSMFromLists(l, []detectedId{detectContent})
	if dc == nil {
		return errors.New("offset needs preceding content option")
	}
	ct, _ := dc.data.(*content)
	if ct.flags&startsWith > 0 {
		return errors.New("can't use offset with startswith")
	}
	if ct.flags&offset > 0 {
		return errors.New("can't use multiple offsets for the same content")
	}

	if ct.flags&(within|distance) > 0 {
		return errors.New("can't use a relative " +
			"keyword like within/distance with a absolute " +
			"relative keyword like depth/offset for the same " +
			"content")
	}
	if ct.flags&contentNegated > 0 && ct.flags&fastpPattern > 0 {
		return errors.New("can't have a relative " +
			"negated keyword set along with 'fast_pattern'")

	}
	if (ct.flags & fastpPatternOnly) > 0 {
		return errors.New("can't have a relative " +
			"keyword set along with 'fast_pattern:only;'")

	}
	if v[0] != '-' && isalpha(v[0]) {
		index := uint8(0)
		if !byteRetrieveSMVar(v, l, &index) {
			return fmt.Errorf("unknown byte_ keyword var seen in offset - %s", v)
		}
		ct.offset = uint16(index)
		ct.flags |= offsetVar
	} else {
		if stringParseUint16(&ct.offset, 0, v) < 0 {
			return fmt.Errorf("invalid value for offset:%v", v)
		}
		if ct.depth != 0 {
			if ct.depth < ct.contentLen {
				ct.depth = ct.contentLen
			}
			ct.depth += ct.offset
		}
	}
	ct.flags |= offset

	return nil
}
