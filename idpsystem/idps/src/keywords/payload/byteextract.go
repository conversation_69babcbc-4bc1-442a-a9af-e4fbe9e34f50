package payload

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/google/gonids"
)

const (
	stringMaxBytesToExtractForOct = 23
	stringMaxBytesToExtractForDec = 20
	stringMaxBytesToExtractForHex = 14
	noStringMaxBytesToExtract     = 8
)
const (
	byteExtractBaseHex = baseHex
	byteExtractBaseDec = baseDec
	byteExtractBaseOct = baseOct
)

const byteExtractMultiplierDefault = 1

type byteExtractFlag uint16

const (
	byteExtractFlagRelative   byteExtractFlag = 1 << iota // 1
	byteExtractFlagString                                 // 2
	byteExtractFlagAlign                                  // 4
	byteExtractFlagEndian                                 // 8
	byteExtractFlagSlice                                  // 16
	byteExtractFlagMultiplier                             // 32
	byteExtractFlagNBytes                                 // 64
	byteExtractFlagOffset                                 // 128
	byteExtractFlagBase                                   // 256
)

type byteExtract struct {
	localID         uint8
	nbytes          uint8
	offset          int16
	name            string
	flags           byteExtractFlag
	byteEndian      byteEndian
	base            byteBase
	alignValue      uint8
	multiplierValue uint16
}

func newByteExtract(l *list, gb *gonids.ByteMatch, context any) error {
	var d *Data
	if data, ok := context.(*Data); !ok {
		return fmt.Errorf("data type error,should be context *Data")
	} else {
		d = data
	}
	b, err := parseByteExtract(gb)
	if err != nil {
		return err
	}
	var prev *Detect
	if b.byteEndian == endianDCE {
		if b.flags&byteExtractFlagRelative > 0 {
			prev = detectGetLastSMFromLists(l, []detectedId{detectContent, detectPCRE,
				detectByteTest, detectByteJump, detectByteExtract, detectByteMath, detectIsDataAt})
		}
	} else if b.flags&byteExtractFlagRelative > 0 {
		prev = detectGetLastSMFromLists(l, []detectedId{detectContent, detectPCRE,
			detectByteTest, detectByteJump, detectByteExtract,
			detectByteMath, detectIsDataAt})
	}

	if b.byteEndian == endianDCE {
		if byteExtractFlagBase|byteExtractFlagString == b.flags&byteExtractFlagBase|byteExtractFlagString {
			return errors.New("invalid option A byte_jump keyword with dce holds other invalid modifiers")
		}
	}

	prev_bed := detectGetLastSMFromLists(l, []detectedId{detectByteExtract})
	if prev_bed == nil {
		b.localID = 0
	} else {
		bed, _ := prev_bed.data.(byteExtract)
		b.localID = bed.localID + 1
		if b.localID > d.ByteExtractMaxLocalID {
			d.ByteExtractMaxLocalID = b.localID
		}
	}
	det := &Detect{
		id:         l.id,
		postition:  gb.DataPosition,
		detectedID: detectByteExtract,
	}
	det.data = b
	err = l.appendList(det)
	if err != nil {
		return err
	}
	if !(b.flags&byteExtractFlagRelative > 0) {
		return nil
	}

	if prev == nil {
		return nil
	}
	if prev.detectedID == detectContent {
		cd, _ := prev.data.(*content)
		cd.flags |= relativeNext
	} else if prev.detectedID == detectPCRE {
		//todo
	}

	return nil
}

func (b *byteExtract) inspect(p *Packet, value *uint64, endian int8) int {
	if p.bufferLen == 0 {
		return 0
	}
	buffer := p.buffer
	len := int32(0)
	ptr := uint32(0)
	if b.flags&byteExtractFlagRelative > 0 {
		ptr = p.bufferOffset
		len = int32(p.bufferLen) - int32(p.bufferOffset)

		ptr += uint32(b.offset)
		len -= int32(b.offset)
		if len <= 0 {
			return 0
		}
	} else {
		ptr = uint32(b.offset)
		len = int32(p.bufferLen) - int32(b.offset)
	}
	if int32(ptr) < 0 || ptr > uint32(p.bufferLen) || int32(b.nbytes) > len {
		return 0
	}
	val := uint64(0)
	extbytes := int(0)

	if b.flags&byteExtractFlagString > 0 {
		extbytes = byteExtractStringUint64(buffer[ptr:], int(b.base), int32(b.nbytes), &val)
		if extbytes <= 0 {
			if extbytes == 0 {
				return 0
			} else {
				return -1
			}
		}
	} else {
		end := 0
		if endian == int8(bigEndian) {
			end = byteBigEndiad
		} else {
			end = byteLittleEndiad
		}
		extbytes = byteExtractUint64(buffer[ptr:], end, uint16(b.nbytes), &val)
		if extbytes != int(b.nbytes) {
			return 0
		}
	}
	val *= uint64(b.multiplierValue)
	if b.flags&byteExtractFlagAlign > 0 {
		if val%uint64(b.alignValue) != 0 {
			val += uint64(b.alignValue) - val%uint64(b.alignValue)
		}
	}
	ptr += uint32(extbytes)
	p.bufferOffset = ptr
	*value = val
	return 1
}

func parseByteExtract(gb *gonids.ByteMatch) (*byteExtract, error) {
	byt, err := strconv.Atoi(gb.NumBytes)
	if err != nil {
		return nil, err
	}

	b := &byteExtract{
		name:            strings.TrimSpace(gb.VarName),
		offset:          int16(gb.Offset),
		nbytes:          uint8(byt),
		byteEndian:      bigEndian,
		base:            byteExtractBaseDec,
		multiplierValue: byteExtractMultiplierDefault,
	}
	for _, op := range gb.Options {
		switch {
		case strings.HasPrefix(op, "align"):
			args := strings.Split(op, " ")
			if len(args) != 2 {
				return nil, fmt.Errorf("invalid align format: %s", op)
			}
			n, err := strconv.ParseInt(args[1], 0, 0)
			if err != nil {
				return nil, err
			}
			if !(n == 2 || n == 4) {
				return nil, fmt.Errorf("invalid align value: must be 2 or 4: %v", n)
			}
			b.alignValue = uint8(n)
			b.flags |= byteExtractFlagAlign
		case op == "slice":
			b.flags |= byteExtractFlagSlice
		case op == "dce", op == "big", op == "little":
			switch op {
			case "dce":
				b.byteEndian = endianDCE
			case "big":
				b.byteEndian = bigEndian
			case "little":
				b.byteEndian = littleEndian
			}
			b.flags |= byteExtractFlagEndian
		case op == "relative":
			b.flags |= byteExtractFlagRelative
		case op == "string":
			if b.flags&byteExtractFlagBase > 0 {
				return nil, errors.New("base specified before string; use \"string, base\"")
			}
			b.flags |= byteExtractFlagString
		case op == "oct", op == "dec", op == "hex":
			if b.flags&byteExtractFlagString == 0 {
				return nil, errors.New("string must be set first")
			}
			if b.flags&byteExtractFlagBase != 0 {
				return nil, errors.New("base already set")
			}
			switch op {
			case "oct":
				b.base = byteExtractBaseOct
			case "dec":
				b.base = byteExtractBaseDec
			case "hex":
				b.base = byteExtractBaseHex
			default:
				return nil, fmt.Errorf("invalid string value: %s", op)
			}
			b.flags |= byteExtractFlagBase

		case strings.HasPrefix(op, "multiplier"):
			args := strings.Split(op, " ")
			if len(args) != 2 {
				return nil, fmt.Errorf("invalid multiplier format: %s", op)
			}
			n, err := strconv.ParseInt(args[1], 0, 0)
			if err != nil {
				return nil, err
			}
			if n == 0 || n > math.MaxUint16 {
				return nil, fmt.Errorf("invalid multiplier value: must be between 0 and %v: %v", math.MaxUint16, n)
			}
			b.multiplierValue = uint16(n)
			b.flags |= byteExtractFlagMultiplier
		}
	}
	if b.flags&byteExtractFlagBase != 0 && b.flags&byteExtractFlagString == 0 {
		return nil, errors.New("must specify string with base")
	}
	if b.flags&byteExtractFlagString != 0 && b.flags&byteExtractFlagEndian != 0 {
		return nil, errors.New("can't specify string and an endian value")
	}

	if byteExtractFlagString|byteExtractFlagSlice == b.flags&(byteExtractFlagEndian|byteExtractFlagSlice) {
		return nil, errors.New("string and slice are mutually exclusive")
	}

	if b.flags&byteExtractFlagSlice > 0 {
		return nil, errors.New("byte_extract slice not yet supported")
	}
	if b.flags&byteExtractFlagString > 0 {
		if b.base == byteExtractBaseOct {
			if b.nbytes > stringMaxBytesToExtractForOct {
				return nil, fmt.Errorf("byte_extract can't process more than %d bytes in \"string\" extraction", stringMaxBytesToExtractForOct)
			}
		} else if b.base == byteExtractBaseDec {
			if b.nbytes > stringMaxBytesToExtractForDec {
				return nil, fmt.Errorf("byte_extract can't process more than %d bytes in \"string\" extraction", stringMaxBytesToExtractForDec)
			}
		} else if b.base == byteExtractBaseHex {
			if b.nbytes > stringMaxBytesToExtractForHex {
				return nil, fmt.Errorf("byte_extract can't process more than %d bytes in \"string\" extraction", stringMaxBytesToExtractForHex)
			}
		}
	} else {
		if b.nbytes > noStringMaxBytesToExtract {
			return nil, fmt.Errorf("byte_extract can't process more than %d bytes in \"non-string\" extraction", noStringMaxBytesToExtract)
		}
		if !(b.flags&byteExtractFlagEndian > 0) {
			b.byteEndian = bigEndian
		}
	}
	return b, nil
}
