package payload

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/google/gonids"
)

// detectFloatMode is equivalent to Rust's DetectFloatMode
type detectFloatMode int

const (
	detectFloatModeEqual detectFloatMode = iota
	detectFloatModeNe
	detectFloatModeGt
	detectFloatModeGte
	detectFloatModeLt
	detectFloatModeLte
)

func newEntropy(l *list, gb *gonids.ByteMatch) error {
	data, err := parseEntropy(gb)
	if err != nil {
		return err
	}
	d := &Detect{
		id:         l.id,
		postition:  gb.DataPosition,
		detectedID: detectEntropy,
		data:       data,
	}
	err = l.appendList(d)
	if err != nil {
		return err
	}
	return nil
}

// detectFloatData is equivalent to Rust's DetectFloatData<f64>
type detectFloatData struct {
	arg1 float64
	arg2 float64 // Corresponds to FloatCore::min_value() or similar if used for ranges
	mode detectFloatMode
}

// newDetectFloatData creates a default detectFloatData.
// In the Rust tests, arg2 was often FloatCore::min_value().
// For entropy, only arg1 seems to be actively parsed and used.
func newDetectFloatData() detectFloatData {
	return detectFloatData{
		// arg2: math.SmallestNonzeroFloat64, // Example if a default for arg2 is needed
	}
}

// DetectEntropyData holds the parsed entropy rule parameters.
type entropyData struct {
	Offset int32
	Nbytes int32
	Value  detectFloatData
}

// NewDetectEntropyData creates a new DetectEntropyData with default values.
func newEntropyData() entropyData {
	return entropyData{
		Offset: 0,
		Nbytes: 0,
		Value:  newDetectFloatData(),
	}
}

// detectParseFloat64 parses a string like "7", ">= 7", "< 7", etc., into detectFloatData.
func detectParseFloat64(s string, val float64) (detectFloatData, error) {
	s = strings.TrimSpace(s)
	var data detectFloatData
	switch {
	case strings.HasPrefix(s, "!="):
		data.mode = detectFloatModeNe
	case strings.HasPrefix(s, "<="):
		data.mode = detectFloatModeLte

	case strings.HasPrefix(s, ">="):
		data.mode = detectFloatModeGte

	case strings.HasPrefix(s, "<"):
		data.mode = detectFloatModeLt

	case strings.HasPrefix(s, ">"):
		data.mode = detectFloatModeGt

	case strings.HasPrefix(s, "="): // Support "= 7" as well as "7"
		data.mode = detectFloatModeEqual
	default:
		data.mode = detectFloatModeEqual
	}

	data.arg1 = val
	return data, nil
}

func parseEntropy(gb *gonids.ByteMatch) (*entropyData, error) {
	entropy := newEntropyData()
	if len(gb.NumBytes) != 0 {
		n, err := strconv.ParseInt(gb.NumBytes, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("invalid bytes value: %s", gb.NumBytes)
		}
		entropy.Nbytes = int32(n)
	}

	if gb.Offset > 65535 || gb.Offset < -65535 {
		return nil, fmt.Errorf("invalid offset value: must be between -65535 and 65535: %v", gb.Offset)
	}
	entropy.Offset = int32(gb.Offset)

	data, err := detectParseFloat64(gb.Operator, gb.Entropy)
	if err != nil {
		return nil, err
	}
	entropy.Value = data
	return &entropy, nil
}

// calculateEntropy computes the Shannon entropy for a slice of bytes.
func calculateEntropy(data []byte) float64 {
	if len(data) == 0 {
		return 0.0
	}

	frequency := [256]uint32{}
	for _, b := range data {
		frequency[b]++
	}

	var entropy float64
	lengthF64 := float64(len(data))
	for _, count := range frequency {
		if count > 0 {
			probability := float64(count) / lengthF64
			entropy -= probability * math.Log2(probability)
		}
	}
	return entropy
}

// detectMatchFloat64 compares a value against the criteria in DetectFloatData.
// Equivalent to Rust's detect_match_float.
func detectMatchFloat64(dfd detectFloatData, value float64) bool {
	switch dfd.mode {
	case detectFloatModeEqual:
		return dfd.arg1 == value
	case detectFloatModeNe:
		return dfd.arg1 != value
	case detectFloatModeGt:
		return value > dfd.arg1
	case detectFloatModeGte:
		return value >= dfd.arg1
	case detectFloatModeLt:
		return value < dfd.arg1
	case detectFloatModeLte:
		return value <= dfd.arg1
	}
	return false
}
func detectEntropyPayload(d *Detect, p *Packet) bool {
	return detectEntropyMatch(p.buffer, *d.data.(*entropyData))
}

func detectEntropyMatch(data []byte, ctx entropyData) bool {
	if data == nil {
		return false
	}
	startIndex := 0
	bufferLength := len(data)
	offsetVal := int(ctx.Offset)
	if offsetVal > 0 {
		if offsetVal > bufferLength {
			return false
		}
		startIndex = offsetVal
	}

	effectiveData := data[startIndex:]
	currentLength := len(effectiveData)

	dataToProcess := effectiveData
	if ctx.Nbytes > 0 {
		nbytesVal := int(ctx.Nbytes)
		if nbytesVal > currentLength {
			return false
		}
		dataToProcess = effectiveData[:nbytesVal]
	}

	entropyValue := calculateEntropy(dataToProcess)

	return detectMatchFloat64(ctx.Value, entropyValue)
}
