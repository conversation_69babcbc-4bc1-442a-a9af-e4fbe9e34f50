package keywords

import (
	"fmt"
	"mnms/idpsystem/idps/src/keywords/ip"
	"strings"
)

type Statementer interface {
	SetUp(v string) error
}

func NewStatement(v string) (Statementer, error) {
	v = strings.ToLower(v)
	if v, ok := statementMap()[v]; ok {
		return v, nil
	}
	return nil, fmt.Errorf("not supported KeyWord:%v", v)
}

func statementMap() map[string]Statementer {
	keywordmap := map[string]Statementer{
		"sameip": ip.NewSameIp(),
	}
	return keywordmap
}
