package ip

import (
	"mnms/idpsystem/idps/src/keywords/gonidutil"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func NewTTL() *ttl {
	return &ttl{ipLayer: protocol.NewIpLayer()}
}

type ttl struct {
	ipLayer protocol.IPLayer
	value   uint8
}

func (t *ttl) SetUp(v any) error {
	l, err := gonidutil.ConvertToLenMatch(v)
	if err != nil {
		return err
	}
	t.value = uint8(l.Num)
	return nil
}

func (t *ttl) Match(packet gopacket.Packet) bool {
	ver, net, err := t.ipLayer.ParseIP(packet)
	if err != nil {
		return false
	}
	switch ver {
	case protocol.IPV4:
		v4 := net.(*layers.IPv4)
		return t.value == v4.TTL
	case protocol.IPV6:
		v6 := net.(*layers.IPv6)
		return t.value == v6.HopLimit
	}

	return false
}
