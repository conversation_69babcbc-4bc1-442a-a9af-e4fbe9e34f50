package ip

import (
	"fmt"
	"mnms/idpsystem/idps/src/protocol"
	"strconv"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func NewTos() *tos {
	return &tos{ipLayer: protocol.NewIpLayer()}
}

type tos struct {
	ipLayer   protocol.IPLayer
	value     uint8
	negatived bool
}

func (t *tos) SetUp(s string) error {
	if len(s) == 0 {
		return fmt.Errorf("invalid format for Tos: %s", s)
	}
	negat := s[0]
	if negat == '!' {
		t.negatived = true
		num := s[1:]
		n, err := strconv.ParseUint(num, 10, 8)
		if err != nil {
			return err
		}
		t.value = uint8(n)
	} else {
		num := s
		n, err := strconv.ParseUint(num, 10, 8)
		if err != nil {
			return err
		}
		t.value = uint8(n)
	}

	return nil
}

func (t *tos) Match(packet gopacket.Packet) bool {
	ver, net, err := t.ipLayer.ParseIP(packet)
	if err != nil {
		return false
	}
	switch ver {
	case protocol.IPV4:
		v4 := net.(*layers.IPv4)
		if t.negatived {
			return t.value != v4.TOS
		} else {
			return t.value == v4.TOS
		}
	}

	return false
}
