package ip

import (
	"mnms/idpsystem/idps/mpm"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func NewV6hdr() *v6hdr {
	return &v6hdr{ipLayer: protocol.NewIpLayer()}

}

type v6hdr struct {
	ipLayer protocol.IPLayer
	mpm.Algorithm
}

func (v *v6hdr) RetrieveBffer(packet gopacket.Packet) []byte {
	return v.getData(packet)
}

// mpm build
func (v *v6hdr) Build() error {
	if v.Algorithm != nil {
		err := v.Algorithm.Build()
		if err != nil {
			return err
		}
	}
	return nil
}

// mpm MatcheIds
func (v *v6hdr) MatchIds(packet gopacket.Packet) []int {
	if v.Algorithm == nil {
		return []int{}
	}
	p := v.getData(packet)
	if p == nil {
		return []int{}
	}
	return v.Algorithm.MatcheIds(p)
}

// // mpm AddContent
func (v *v6hdr) AddContent(contents mpm.Content) error {
	if v.Algorithm == nil {
		a, err := mpm.NewMpm()
		if err != nil {
			return err
		}
		v.Algorithm = a
	}
	return v.Algorithm.AddContent(contents)
}

func (v *v6hdr) getData(packet gopacket.Packet) []byte {
	ver, net, err := v.ipLayer.ParseIP(packet)
	if err != nil {
		return nil
	}
	switch ver {
	case protocol.IPV6:
		v6 := net.(*layers.IPv6)
		return v6.Contents
	}
	return nil
}
