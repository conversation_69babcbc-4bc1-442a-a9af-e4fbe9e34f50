package ip

import (
	_ "embed"
	"fmt"
	utilies "mnms/idpsystem/idps/src"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

const (
	modifiernot uint8 = iota + 1
	modifieplus
	modifieany
)

const (
	mf = 0x01
	df = 0x02
	rf = 0x04
)

func NewFragbits() *fragbits {
	return &fragbits{ipLayer: protocol.NewIpLayer()}
}

type fragbits struct {
	fragbits uint8
	modifier uint8
	ipLayer  protocol.IPLayer
}

func (f *fragbits) SetUp(s string) error {
	v := &utilies.Lenmatch{}
	err := v.Parse(s)
	if err != nil {
		return err
	}
	switch v.Modifier {
	case '!':
		f.modifier = modifiernot
	case '+':
		f.modifier = modifieplus
	case '*':
		f.modifier = modifieany
	}
	found := 0
	for _, v := range v.Str {
		switch v {
		case 'm':
			f.fragbits |= mf
			found++
		case 'd':
			f.fragbits |= df
			found++
		case 'r':
			f.fragbits |= rf
			found++
		default:
			found = 0
		}
	}
	if found == 0 {
		return fmt.Errorf("error value: %v", v.Str)
	}

	return nil
}

func (f *fragbits) Match(packet gopacket.Packet) bool {
	ver, net, err := f.ipLayer.ParseIP(packet)
	if err != nil {
		return false
	}

	switch ver {
	case protocol.IPV4:
		v4 := net.(*layers.IPv4)
		pbits := uint8(v4.Flags)
		switch f.modifier {
		case modifieany:
			return pbits&f.fragbits > 0
		case modifieplus:
			return pbits&f.fragbits == f.fragbits && pbits-f.fragbits > 0
		case modifiernot:
			return pbits&f.fragbits != f.fragbits
		default:
			return pbits == f.fragbits
		}
	}

	return false
}
