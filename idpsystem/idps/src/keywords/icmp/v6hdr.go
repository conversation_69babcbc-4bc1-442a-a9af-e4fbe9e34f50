package icmp

import (
	"mnms/idpsystem/idps/mpm"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"golang.org/x/net/icmp"
	"golang.org/x/net/ipv6"
)

func NewV6hdr() *v6hdr {
	return &v6hdr{layer: protocol.NewIcmpParser()}
}

type v6hdr struct {
	mpm.Algorithm
	layer *protocol.IcmpParser
}

func (v *v6hdr) RetrieveBffer(packet gopacket.Packet) []byte {
	return v.getData(packet)
}

// mpm build
func (v *v6hdr) Build() error {
	if v.Algorithm != nil {
		err := v.Algorithm.Build()
		if err != nil {
			return err
		}
	}
	return nil
}

// mpm MatcheIds
func (v *v6hdr) MatchIds(packet gopacket.Packet) []int {
	if v.Algorithm == nil {
		return []int{}
	}
	p := v.getData(packet)
	if p == nil {
		return []int{}
	}
	return v.Algorithm.MatcheIds(p)
}

// // mpm AddContent
func (v *v6hdr) AddContent(contents mpm.Content) error {
	if v.Algorithm == nil {
		a, err := mpm.NewMpm()
		if err != nil {
			return err
		}
		v.Algorithm = a
	}
	return v.Algorithm.AddContent(contents)
}

func (v *v6hdr) getData(packet gopacket.Packet) []byte {
	b := v.layer.Parse(packet)
	if !b {
		return nil
	}
	proto, lay, err := v.layer.GetIcmp()
	if err != nil {
		return nil
	}
	if proto == layers.IPProtocolICMPv6 {
		lc := len(lay.LayerContents())
		lp := len(lay.LayerPayload())
		b := make([]byte, 0, lc+lp)
		b = append(b, lay.LayerContents()...)
		b = append(b, lay.LayerPayload()...)
		msg, err := icmp.ParseMessage(int(layers.IPProtocolICMPv6), b)
		if err != nil {
			return nil
		}
		switch msg.Type {
		case ipv6.ICMPTypePacketTooBig, ipv6.ICMPTypeParameterProblem, ipv6.ICMPTypeEchoRequest,
			ipv6.ICMPTypeEchoReply, ipv6.ICMPTypeExtendedEchoRequest, ipv6.ICMPTypeExtendedEchoReply:
			b := make([]byte, 0, lc+4)
			b = append(b, lay.LayerContents()...)
			b = append(b, lay.LayerPayload()[:4]...)
			return b
		default:
			return lay.LayerContents()
		}

	}
	return nil
}
