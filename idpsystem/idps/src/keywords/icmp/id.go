package icmp

import (
	"mnms/idpsystem/idps/src/keywords/gonidutil"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"golang.org/x/net/icmp"
)

func NewId() *Id {
	return &Id{layer: protocol.NewIcmpParser()}
}

type Id struct {
	layer *protocol.IcmpParser
	num   uint16
}

func (i *Id) SetUp(input any) error {
	v, err := gonidutil.ConvertToLenMatch(input)
	if err != nil {
		return err
	}
	i.num = uint16(v.Num)
	return nil
}

func (i *Id) Match(packet gopacket.Packet) bool {
	b := i.layer.Parse(packet)
	if !b {
		return false
	}
	proto, lay, err := i.layer.GetIcmp()
	if err != nil {
		return false
	}
	if proto == layers.IPProtocolICMPv4 {
		if icmp, ok := lay.(*layers.ICMPv4); ok {
			return icmp.Id == i.num
		}

	} else if proto == layers.IPProtocolICMPv6 {
		msg, err := icmp.ParseMessage(58, lay.LayerContents())
		if err != nil {
			return false
		}
		switch pkt := msg.Body.(type) {
		case *icmp.Echo:
			return pkt.ID == int(i.num)
		default:
			return false
		}
	}

	return false
}
