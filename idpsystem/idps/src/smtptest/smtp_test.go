package smtp_test

import (
	"errors"
	"fmt"
	"io"
	"log"
	"mnms/idpsystem/idps/ids"
	"testing"
	"time"

	"github.com/emersion/go-smtp"
	"github.com/google/gonids"
)

const testproto = "smtp"

var count = 0

const addr = "localhost:25"

// The Backend implements SMTP server methods.
type Backend struct{}

// NewSession is called after client greeting (EHLO, HELO).
func (bkd *Backend) NewSession(c *smtp.Conn) (smtp.Session, error) {
	return &Session{}, nil
}

// A Session is returned after successful login.
type Session struct{}

// <PERSON>th<PERSON><PERSON> implements authentication using SASL PLAIN.
func (s *Session) AuthPlain(username, password string) error {
	if username != "username" || password != "password" {
		return errors.New("Invalid username or password")
	}
	return nil
}

func (s *Session) Mail(from string, opts *smtp.MailOptions) error {
	log.Println("Mail from:", from)
	return nil
}

func (s *Session) Rcpt(to string, opts *smtp.RcptOptions) error {
	log.Println("Rcpt to:", to)
	return nil
}

func (s *Session) Data(r io.Reader) error {
	if b, err := io.ReadAll(r); err != nil {
		return err
	} else {
		log.Println("Data:", string(b))
	}
	return nil
}

func (s *Session) Reset() {}

func (s *Session) Logout() error {
	return nil
}
func TestMain(m *testing.M) {
	be := &Backend{}

	s := smtp.NewServer(be)

	s.Addr = addr
	s.Domain = "localhost"
	s.WriteTimeout = 10 * time.Second
	s.ReadTimeout = 10 * time.Second
	s.MaxMessageBytes = 1024 * 1024
	s.MaxRecipients = 50
	s.AllowInsecureAuth = true
	log.Println("Starting server at", s.Addr)
	go func() {
		if err := s.ListenAndServe(); err != nil {
			log.Fatal(err)
		}
	}()
	m.Run()
}
func TestDetect(t *testing.T) {
	i, err := ids.NewIds(false, false)
	if err != nil {
		t.Fatal(err)
	}
	i.Enablelo(true)
	s := `alert smtp $HOME_NET any <> $HOME_NET any (msg:"test smtp";sid:1;)`
	r, err := gonids.ParseRule(s)
	if err != nil {
		t.Fatal(err)
	}
	err = i.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	i.RegisterMatchEvent(event())

	err = i.Run()
	if err != nil {
		t.Fatal(err)
	}
	err = i.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = i.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	defer i.Close()
	err = runSmtp()
	if err != nil {
		t.Fatal(err)
	}
	time.Sleep(time.Second * 3)
	if count == 0 {
		t.Fatalf("test fail,not detect proto:%v", testproto)
	}

}
func event() func(ids.Event) {
	v := func(event ids.Event) {
		count++
		log.Printf("%v", event)
	}
	return v
}
func runSmtp() error {
	c, err := smtp.Dial(addr)
	if err != nil {
		return err
	}

	// Set the sender and recipient first
	if err := c.Mail("<EMAIL>", nil); err != nil {
		return err
	}
	if err := c.Rcpt("<EMAIL>", nil); err != nil {
		return err
	}
	// Send the email body.
	wc, err := c.Data()
	if err != nil {
		return err
	}
	_, err = fmt.Fprintf(wc, "9999")
	if err != nil {
		return err
	}
	err = wc.Close()
	if err != nil {
		return err
	}

	// Send the QUIT command and close the connection.
	err = c.Quit()
	if err != nil {
		return err
	}
	return nil
}
