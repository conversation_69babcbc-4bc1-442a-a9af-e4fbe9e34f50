package src

import "strings"

type Lenmatch struct {
	Modifier uint8
	Str      string
	Str2     string
}

func (l *Lenmatch) Parse(s string) error {
	m := s[0]
	switch m {
	case '!', '+', '*', '>', '<':
		l.Modifier = uint8(m)
	default:
		l.Modifier = 0
	}
	v := strings.ToLower(s)
	if l.Modifier != 0 {
		v = strings.ToLower(s[1:])
	}
	arr := strings.Split(v, ",")
	if len(arr) == 1 {
		l.Str = arr[0]
	} else if len(arr) == 2 {
		l.Str = arr[0]
		l.Str2 = arr[1]
	}
	return nil
}
