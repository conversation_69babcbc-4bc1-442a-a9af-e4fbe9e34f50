package imaptest_test

import (
	"fmt"
	"log"
	"net"
	"testing"
	"time"

	"mnms/idpsystem/idps/ids"

	"github.com/emersion/go-imap/v2"
	"github.com/emersion/go-imap/v2/imapclient"
	"github.com/emersion/go-imap/v2/imapserver"
	"github.com/emersion/go-imap/v2/imapserver/imapmemserver"
	"github.com/google/gonids"
)

const local = "localhost:143"
const testproto = "imap"

var count = 0

func TestMain(m *testing.M) {

	ln, err := net.Listen("tcp", local)
	if err != nil {
		log.Fatalf("Failed to listen: %v", err)
	}
	log.Printf("IMAP server listening on %v", ln.Addr())
	memServer := imapmemserver.New()
	user := imapmemserver.NewUser("admin", "default")
	user.Create("INBOX", nil)
	memServer.AddUser(user)
	server := imapserver.New(&imapserver.Options{
		NewSession: func(conn *imapserver.Conn) (imapserver.Session, *imapserver.GreetingData, error) {
			return memServer.NewSession(), nil, nil
		},
		Caps: imap.CapSet{
			imap.CapIMAP4rev1: {},
			imap.CapIMAP4rev2: {},
		},
		InsecureAuth: true,
	})
	go func() {
		if err := server.Serve(ln); err != nil {
			log.Fatalf("Serve() = %v", err)
		}
	}()
	time.Sleep(time.Second * 1)
	m.Run()
}

func TestDetect(t *testing.T) {
	i, err := ids.NewIds(false, false)
	if err != nil {
		t.Fatal(err)
	}
	i.Enablelo(true)
	s := `alert imap $HOME_NET any <> $HOME_NET any (msg:"test modbus";sid:1;)`
	r, err := gonids.ParseRule(s)
	if err != nil {
		t.Fatal(err)
	}
	err = i.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	i.RegisterMatchEvent(event())

	err = i.Run()
	if err != nil {
		t.Fatal(err)
	}
	err = i.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = i.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	defer i.Close()
	err = runImap()
	if err != nil {
		t.Fatal(err)
	}
	time.Sleep(time.Second * 3)
	if count == 0 {
		t.Fatalf("test fail,not detect proto:%v", testproto)
	}

}
func event() func(ids.Event) {
	v := func(event ids.Event) {
		count++
		log.Printf("%v", event)
	}
	return v
}

func runImap() error {
	conn, err := net.Dial("tcp", local)
	if err != nil {
		return err
	}
	defer conn.Close()

	c := imapclient.New(conn, nil)
	if err := c.Login("admin", "default").Wait(); err != nil {
		return fmt.Errorf("failed to login: %v", err)
	}
	selectCmd := c.Select("INBOX", nil)
	if _, err := selectCmd.Wait(); err != nil {
		return fmt.Errorf("failed to select INBOX: %v", err)
	}
	defer c.Close()
	return nil
}
