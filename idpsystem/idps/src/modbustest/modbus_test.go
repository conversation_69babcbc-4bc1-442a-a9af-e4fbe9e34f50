package modbus_test

import (
	"fmt"
	"log"
	"mnms/idpsystem/idps/ids"
	"testing"
	"time"

	"github.com/google/gonids"
	"github.com/simonvetter/modbus"
)

const testproto = "modbus"

var count = 0

type tcpTestHandler struct {
	coils   [10]bool
	di      [10]bool
	input   [10]uint16
	holding [10]uint16
}

func (th *tcpTestHandler) HandleCoils(req *modbus.CoilsRequest) (res []bool, err error) {
	if req.UnitId != 9 {
		// only reply to unit ID #9
		err = modbus.ErrIllegalFunction
		return
	}

	if req.Addr+req.Quantity > uint16(len(th.coils)) {
		err = modbus.ErrIllegalDataAddress
		return
	}

	for i := 0; i < int(req.Quantity); i++ {
		if req.IsWrite {
			th.coils[int(req.Addr)+i] = req.Args[i]
		}
		res = append(res, th.coils[int(req.Addr)+i])
	}

	return
}

func (th *tcpTestHandler) HandleDiscreteInputs(req *modbus.DiscreteInputsRequest) (res []bool, err error) {
	if req.UnitId != 9 {
		// only reply to unit ID #9
		err = modbus.ErrIllegalFunction
		return
	}

	if req.Addr+req.Quantity > uint16(len(th.di)) {
		err = modbus.ErrIllegalDataAddress
		return
	}

	for i := 0; i < int(req.Quantity); i++ {
		res = append(res, th.di[int(req.Addr)+i])
	}

	return
}

func (th *tcpTestHandler) HandleHoldingRegisters(req *modbus.HoldingRegistersRequest) (res []uint16, err error) {
	if req.UnitId != 9 {
		// only reply to unit ID #9
		err = modbus.ErrIllegalFunction
		return
	}

	if req.Addr+req.Quantity > uint16(len(th.holding)) {
		err = modbus.ErrIllegalDataAddress
		return
	}

	for i := 0; i < int(req.Quantity); i++ {
		if req.IsWrite {
			th.holding[int(req.Addr)+i] = req.Args[i]
		}
		res = append(res, th.holding[int(req.Addr)+i])
	}

	return
}

func (th *tcpTestHandler) HandleInputRegisters(req *modbus.InputRegistersRequest) (res []uint16, err error) {
	if req.UnitId != 9 {
		// only reply to unit ID #9
		err = modbus.ErrIllegalFunction
		return
	}

	if req.Addr+req.Quantity > uint16(len(th.input)) {
		err = modbus.ErrIllegalDataAddress
		return
	}

	for i := 0; i < int(req.Quantity); i++ {
		res = append(res, th.input[int(req.Addr)+i])
	}

	return
}

func TestDetect(t *testing.T) {
	i, err := ids.NewIds(false, false)
	if err != nil {
		t.Fatal(err)
	}
	i.Enablelo(true)

	s := `alert modbus $HOME_NET any <> $HOME_NET any (msg:"test modbus";sid:1;)`
	r, err := gonids.ParseRule(s)
	if err != nil {
		t.Fatal(err)
	}
	err = i.AddGonidsRule(r)
	if err != nil {
		t.Fatal(err)
	}
	i.RegisterMatchEvent(event())

	err = i.Run()
	if err != nil {
		t.Fatal(err)
	}
	err = i.Build()
	if err != nil {
		t.Fatal(err)
	}
	err = i.ApplyRules()
	if err != nil {
		t.Fatal(err)
	}
	defer i.Close()
	err = runModBus()
	if err != nil {
		t.Fatal(err)
	}
	time.Sleep(time.Second * 3)
	if count == 0 {
		t.Fatalf("test fail,not detect proto:%v", testproto)
	}
}

func event() func(ids.Event) {
	v := func(event ids.Event) {
		count++
		log.Printf("%v", event)
	}
	return v
}

func runModBus() error {
	th := &tcpTestHandler{}
	server, err := modbus.NewServer(&modbus.ServerConfiguration{
		URL:        "tcp://localhost:502",
		MaxClients: 2,
	}, th)
	if err != nil {
		return fmt.Errorf("failed to create server: %v", err)
	}

	err = server.Start()
	if err != nil {
		return fmt.Errorf("failed to start server: %v", err)
	}
	time.Sleep(1 * time.Second)
	// for a TCP endpoint
	// (see examples/tls_client.go for TLS usage and options)
	client, err := modbus.NewClient(&modbus.ClientConfiguration{
		URL:     "tcp://localhost:502",
		Timeout: 1 * time.Second,
	})
	if err != nil {
		return err
	}
	defer client.Close()
	err = client.Open()
	if err != nil {
		return err
	}
	client.SetUnitId(9)
	var reg16 uint16
	reg16, err = client.ReadRegister(0, modbus.INPUT_REGISTER)
	if err != nil {
		// error out
		return err
	} else {
		// use value
		fmt.Printf("value: %v", reg16)        // as unsigned integer
		fmt.Printf("value: %v", int16(reg16)) // as signed integer
	}
	return nil
}
