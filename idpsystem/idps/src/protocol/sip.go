package protocol

import (
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

//var Layersip = gopacket.RegisterLayerType(int(sipProto), gopacket.LayerTypeMetadata{Name: sipProto.String(), Decoder: gopacket.DecodeFunc(decodesip)})

type sipParser struct {
}

func (p *sipParser) Parse(pack gopacket.Packet) bool {
	if sip := pack.Layer(layers.LayerTypeSIP); sip != nil {
		return true
	}
	return false
}
