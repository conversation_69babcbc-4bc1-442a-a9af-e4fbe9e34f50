package protocol

import (
	"bytes"
	"encoding/binary"
	"errors"
	"fmt"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type tftpParser struct {
}

func (p *tftpParser) Parse(pack gopacket.Packet) bool {
	up := udpParser{}
	b := up.Parse(pack)
	if !b {
		return false
	}
	_, err := decodeTftp(up.GetUdp().Payload)
	return err == nil

}

//var LayerTftp = gopacket.RegisterLayerType(int(tftpProto), gopacket.LayerTypeMetadata{Name: tftpProto.String(), Decoder: gopacket.DecodeFunc(decodeTftp)})

type tftp struct {
	layers.BaseLayer
}

/*
func (s *tftp) LayerType() gopacket.LayerType {
	return LayerTftp
}*/

func decodeTftp(data []byte) (*tftp, error) {
	tftp := &tftp{}
	err := tftp.DecodeFromBytes(data)
	if err != nil {
		return nil, err
	}
	return tftp, nil
}

func (s *tftp) DecodeFromBytes(data []byte) error {
	p, err := parsePacket(data)
	if err != nil {
		return err
	}
	switch p := p.(type) {
	case pDATA:
		return nil
	case pOACK:
		_, err := unpackOACK(p)
		if err != nil {
			return err
		}
	case pWRQ:
		_, _, _, err := unpackRQ(p)
		if err != nil {
			return fmt.Errorf("unpack WRQ: %v", err)
		}
	case pRRQ:
		_, _, _, err := unpackRQ(p)
		if err != nil {
			return fmt.Errorf("unpack RRQ: %v", err)
		}
	case pERROR:
		return errors.New(p.message())
	}

	s.BaseLayer = layers.BaseLayer{Contents: data, Payload: data}
	return nil
}

// NextLayerType returns the layer type of the ModbusTCP payload, which is LayerTypePayload.
func (s *tftp) NextLayerType() gopacket.LayerType {
	return gopacket.LayerTypePayload
}
func (s *tftp) LayerContents() []byte { return s.Contents }

func (s *tftp) Payload() []byte {
	return s.BaseLayer.Payload
}

const (
	opRRQ   = uint16(1) // Read request (RRQ)
	opWRQ   = uint16(2) // Write request (WRQ)
	opDATA  = uint16(3) // Data
	opACK   = uint16(4) // Acknowledgement
	opERROR = uint16(5) // Error
	opOACK  = uint16(6) // Options Acknowledgment
)

type options map[string]string

// RRQ/WRQ packet
//
//	2 bytes     string    1 byte    string    1 byte
//
// --------------------------------------------------
// | Opcode |  Filename  |   0  |    Mode    |   0  |
// --------------------------------------------------
type pRRQ []byte
type pWRQ []byte

func unpackRQ(p []byte) (filename, mode string, opts options, err error) {
	bs := bytes.Split(p[2:], []byte{0})
	if len(bs) < 2 {
		return "", "", nil, fmt.Errorf("missing filename or mode")
	}
	filename = string(bs[0])
	mode = string(bs[1])
	if len(bs) < 4 {
		return filename, mode, nil, nil
	}
	opts = make(options)
	for i := 2; i+1 < len(bs); i += 2 {
		opts[string(bs[i])] = string(bs[i+1])
	}
	return filename, mode, opts, nil
}

// OACK packet
//
// +----------+---~~---+---+---~~---+---+---~~---+---+---~~---+---+
// |  Opcode  |  opt1  | 0 | value1 | 0 |  optN  | 0 | valueN | 0 |
// +----------+---~~---+---+---~~---+---+---~~---+---+---~~---+---+
type pOACK []byte

func unpackOACK(p []byte) (opts options, err error) {
	bs := bytes.Split(p[2:], []byte{0})
	opts = make(options)
	for i := 0; i+1 < len(bs); i += 2 {
		opts[string(bs[i])] = string(bs[i+1])
	}
	return opts, nil
}

// ERROR packet
//
//	2 bytes     2 bytes      string    1 byte
//
// ------------------------------------------
// | Opcode |  ErrorCode |   ErrMsg   |  0  |
// ------------------------------------------
type pERROR []byte

func (p pERROR) message() string {
	return string(p[4:])
}

// DATA packet
//
//	2 bytes    2 bytes     n bytes
//
// ----------------------------------
// | Opcode |   Block #  |   Data   |
// ----------------------------------
type pDATA []byte

// ACK packet
//
//	2 bytes    2 bytes
//
// -----------------------
// | Opcode |   Block #  |
// -----------------------
type pACK []byte

func parsePacket(p []byte) (interface{}, error) {
	l := len(p)
	if l < 2 {
		return nil, fmt.Errorf("short packet")
	}
	opcode := binary.BigEndian.Uint16(p)
	switch opcode {
	case opRRQ:
		if l < 4 {
			return nil, fmt.Errorf("short RRQ packet: %d", l)
		}
		return pRRQ(p), nil
	case opWRQ:
		if l < 4 {
			return nil, fmt.Errorf("short WRQ packet: %d", l)
		}
		return pWRQ(p), nil
	case opDATA:
		if l < 4 {
			return nil, fmt.Errorf("short DATA packet: %d", l)
		}
		return pDATA(p), nil
	case opACK:
		if l < 4 {
			return nil, fmt.Errorf("short ACK packet: %d", l)
		}
		return pACK(p), nil
	case opERROR:
		if l < 5 {
			return nil, fmt.Errorf("short ERROR packet: %d", l)
		}
		return pERROR(p), nil
	case opOACK:
		if l < 6 {
			return nil, fmt.Errorf("short OACK packet: %d", l)
		}
		return pOACK(p), nil
	default:
		return nil, fmt.Errorf("unknown opcode: %d", opcode)
	}
}
