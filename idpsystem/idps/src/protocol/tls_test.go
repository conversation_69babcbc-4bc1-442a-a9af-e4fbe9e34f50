package protocol

import (
	"testing"
)

func TestTLs(t *testing.T) {
	packet := []byte{
		0x45, 0x00, 0x00, 0x43, 0xe6, 0x01, 0x40, 0x00,
		0x6b, 0x06, 0xa4, 0x17, 0x14, 0xbd, 0xac, 0x21,
		0xc0, 0xa8, 0x04, 0x15, 0x01, 0xbb, 0xd4, 0xab, 0x27, 0x7f, 0x4f, 0xf0,
		0x94, 0xbc, 0x4c, 0x63, 0x50, 0x19, 0x3f, 0xfc,
		0x53, 0xe2, 0x00, 0x00, 0x17, 0x03, 0x03, 0x00, 0x16, 0xd6, 0xca, 0xcc,
		0xb0, 0x52, 0x89, 0x8d, 0x80, 0x42, 0x55, 0xcc,
		0x60, 0x34, 0x75, 0x9b, 0x40, 0x5c, 0x10, 0xce,
		0x46, 0xb0, 0xee,
	}
	p, err := ConvertGoNetWorkPacket(packet)
	if err != nil {
		t.Error("Failed to decode packet:", p.<PERSON><PERSON>r<PERSON>ayer().Error())
	}
	tls := tlsParser{}
	if !tls.Parse(p) {
		t.Error("Failed to decode  tlsParser")
	}
}
