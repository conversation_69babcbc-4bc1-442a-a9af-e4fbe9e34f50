package protocol

import (
	"encoding/hex"
	"fmt"
	"testing"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

/*
0000   ff ff ff ff ff ff 00 60 e9 18 3c 3c 08 00 45 00   .......`..<<..E.
0010   01 48 00 00 40 00 40 11 74 e0 c0 a8 04 1d ff ff   .H..@.@.t.......
0020   ff ff da 92 da 92 01 34 70 0c 01 00 01 00 92 da   .......4p.......
0030   00 00 00 00 00 00 c0 a8 04 1d 00 00 00 00 00 00   ................
0040   00 00 c0 a8 04 fe 00 60 e9 18 3c 3c 00 00 00 00   .......`..<<....
0050   00 00 00 00 00 00 45 48 37 35 32 30 2d 34 47 2d   ......EH7520-4G-
0060   34 53 46 50 00 00 00 00 00 00 00 00 00 00 00 00   4SFP............
0070   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................
0080   00 00 04 00 37 38 39 00 00 00 00 00 00 00 00 00   ....789.........
0090   00 00 00 00 00 00 3c 05 45 48 37 35 32 30 2d 34   ......<.EH7520-4
00a0   47 2d 38 50 6f 45 2d 34 53 46 50 20 41 70 70 6c   G-8PoE-4SFP Appl
00b0   69 63 61 74 69 6f 6e 3a 20 56 35 2e 36 30 2d 73   ication: V5.60-s
00c0   76 6e 32 35 30 38 00 00 00 00 00 00 00 00 00 00   vn2508..........
00d0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................
00e0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................
00f0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................
0100   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................
0110   00 00 00 00 00 00 ff ff ff 00 00 00 00 00 00 00   ................
0120   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................
0130   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................
0140   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00   ................
0150   00 00 00 00 00 00                                 ......
*/
func TestUdp(t *testing.T) {
	data := "ffffffffffff0060e9183c3c08004500014800004000401174e0c0a8041dffffffffda92da920134700c0100010092da000000000000c0a8041d0000000000000000c0a804fe0060e9183c3c000000000000000000004548373532302d34472d3453465000000000000000000000000000000000000000000000000000000000000004003738390000000000000000000000000000003c054548373532302d34472d38506f452d34534650204170706c69636174696f6e3a2056352e36302d73766e323530380000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ffffff00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
	testUDPPacketgwd, err := hex.DecodeString(data)
	if err != nil {
		t.Fatal(err)
	}
	p := gopacket.NewPacket(testUDPPacketgwd, layers.LinkTypeEthernet, gopacket.DecodeOptions{Lazy: true, NoCopy: true})

	u := NewUdpParser()
	u.Parse(p)
	udp := u.GetUdp()
	fmt.Print(udp)
}
