package protocol

import (
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type udpParser struct {
	udp *layers.UDP
}

func (u *udpParser) Parse(pack gopacket.Packet) bool {
	layer := pack.Layer(layers.LayerTypeUDP)
	if layer == nil {
		return false
	}
	u.udp = layer.(*layers.UDP)
	return true
}
func (u *udpParser) ParseByte(b []byte) bool {
	p, err := ConvertGoNetWorkPacket(b)
	if err != nil {
		return false
	}
	return u.Parse(p)
}

func (u *udpParser) GetUdp() *layers.UDP {
	return u.udp
}

func NewUdpParser() *udpParser {
	return &udpParser{}
}
