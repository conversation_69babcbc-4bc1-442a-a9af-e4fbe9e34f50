package protocol

import (
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type TcpParser struct {
	tcp *layers.TCP
}

func (t *TcpParser) Parse(pack gopacket.Packet) bool {
	layer := pack.Layer(layers.LayerTypeTCP)
	if layer == nil {
		return false
	}
	t.tcp = layer.(*layers.TCP)
	return true
}
func (t *TcpParser) ParseByte(b []byte) bool {
	p, err := ConvertGoNetWorkPacket(b)
	if err != nil {
		return false
	}
	return t.Parse(p)
}

func (t *TcpParser) GetTcp() *layers.TCP {
	return t.tcp
}

func NewTcpParser() *TcpParser {
	return &TcpParser{}
}
