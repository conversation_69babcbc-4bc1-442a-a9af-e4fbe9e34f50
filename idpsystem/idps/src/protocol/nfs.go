package protocol

import (
	"bytes"
	"errors"
	"fmt"
	"io"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	xdr2 "github.com/rasky/go-xdr/xdr2"
	"github.com/willscott/go-nfs-client/nfs/xdr"
)

type nfsParser struct {
}

func (p *nfsParser) Parse(pack gopacket.Packet) bool {
	if tran := pack.TransportLayer(); tran != nil {
		_, err := decodeNfs(tran.LayerPayload())
		return err == nil
	}

	return false
}

/*
var LayerTypeNfs = gopacket.RegisterLayerType(int(nfsProto), gopacket.LayerTypeMetadata{Name: nfsProto.String(), Decoder: gopacket.DecodeFunc(decodeNfs)})
*/
const (
	MsgAccepted = iota
	MsgDenied
)
const (
	Success = iota
	ProgUnavail
	ProgMismatch
	ProcUnavail
	GarbageArgs
	SystemErr
)
const (
	RpcMismatch = iota
)

type NfsLayer struct {
	layers.BaseLayer
}

func decodeNfs(data []byte) (*NfsLayer, error) {
	f := &NfsLayer{}
	err := f.DecodeFromBytes(data)
	if err != nil {
		return nil, err
	}

	return f, nil

}

/*
// LayerType returns gopacket.LayerTypeFTP
func (f *NfsLayer) LayerType() gopacket.LayerType { return LayerTypeNfs }

func (f *NfsLayer) LayerContents() []byte { return f.Contents }

func (f *NfsLayer) LayerPayload() []byte {
	var r []byte
	return r
}

// Payload returns the base layer payload (nil)
func (f *NfsLayer) Payload() []byte { return nil }
/*
// CanDecode returns gopacket.LayerTypeFTP
func (f *NfsLayer) CanDecode() gopacket.LayerClass { return LayerTypeFTP }
*/
// NextLayerType returns gopacket.LayerTypeZero
func (f *NfsLayer) NextLayerType() gopacket.LayerType { return gopacket.LayerTypeZero }

// DecodeFromBytes decodes the slice into the FTP struct.
func (f *NfsLayer) DecodeFromBytes(data []byte) error {
	if len(data) == 0 {
		return errors.New("error")
	}
	reader := bytes.NewReader(data)
	fragment, err := xdr.ReadUint32(reader)
	if err != nil {
		if xdrErr, ok := err.(*xdr2.UnmarshalError); ok {
			if xdrErr.Err == io.EOF {
				return errors.New("error")
			}
		}
		return err
	}
	if fragment&(1<<31) == 0 {
		return errors.New("error")
	}
	reqLen := fragment - uint32(1<<31)
	if reqLen < 40 {
		return errors.New("error")
	}
	r := io.LimitedReader{R: reader, N: int64(reqLen)}

	_, err = xdr.ReadUint32(&r)
	if err != nil {
		return err
	}
	reqType, err := xdr.ReadUint32(&r)
	if err != nil {
		return err
	}
	switch reqType {
	case 0:
	case 1:
		status, err := xdr.ReadUint32(reader)
		if err != nil {
			return err
		}

		switch status {
		case MsgAccepted:

			// padding
			_, err = xdr.ReadUint32(reader)
			if err != nil {
				return err
			}

			opaque_len, err := xdr.ReadUint32(reader)
			if err != nil {
				return err
			}

			_, err = reader.Seek(int64(opaque_len), io.SeekCurrent)
			if err != nil {
				return err
			}

			acceptStatus, _ := xdr.ReadUint32(reader)

			switch acceptStatus {
			case Success:
				return nil
			case ProgUnavail:
				return fmt.Errorf("rpc: PROG_UNAVAIL - server does not recognize the program number")
			case ProgMismatch:
				return fmt.Errorf("rpc: PROG_MISMATCH - program version does not exist on the server")
			case ProcUnavail:
				return fmt.Errorf("rpc: PROC_UNAVAIL - unrecognized procedure number")
			case GarbageArgs:
				// emulate Linux behaviour for GARBAGE_ARGS
				return fmt.Errorf("rpc: GARBAGE_ARGS - rpc arguments cannot be XDR decoded")
			case SystemErr:
				return fmt.Errorf("rpc: SYSTEM_ERR - unknown error on server")
			default:
				return fmt.Errorf("rpc: unknown accepted status error: %d", acceptStatus)
			}

		case MsgDenied:
			rejectStatus, _ := xdr.ReadUint32(reader)
			switch rejectStatus {
			case RpcMismatch:
				return errors.New("error")
			default:
				return fmt.Errorf("rejectedStatus was not valid: %d", rejectStatus)
			}

		default:
			return fmt.Errorf("rejectedStatus was not valid: %d", status)
		}
	default:
		return errors.New("error")
	}

	return nil
}
