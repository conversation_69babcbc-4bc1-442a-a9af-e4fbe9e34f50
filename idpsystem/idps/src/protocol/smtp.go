package protocol

import (
	"bytes"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

//var Layersmtp = gopacket.RegisterLayerType(int(smtpProto), gopacket.LayerTypeMetadata{Name: smtpProto.String(), Decoder: gopacket.DecodeFunc(decodeSmtp)})

type smtpParser struct {
}

func (p *smtpParser) Parse(pack gopacket.Packet) bool {
	tp := NewTcpParser()
	b := tp.Parse(pack)
	if !b {
		return false
	}
	_, err := decodeSmtp(tp.GetTcp().Payload)
	return err == nil
}

type smtp struct {
	layers.BaseLayer
}

func decodeSmtp(data []byte) (*smtp, error) {

	smtp := &smtp{}
	err := smtp.DecodeFromBytes(data)
	if err != nil {
		return nil, err
	}

	return smtp, nil
}

func (s *smtp) DecodeFromBytes(data []byte) error {
	if len(data) == 0 {
		return errors.New("smtp error")
	}

	var content, payload []byte
	cmd, ar, err := parseCmd(string(data)) // returns Email struct and error
	if err != nil {
		code, _, ms, err := parseCodeLine(string(data))
		if err != nil {
			pay, err := parseSmtpMessage(data)
			if err != nil {
				return err
			}
			content = []byte(pay)
			payload = []byte(pay)
		} else {
			code := strconv.Itoa(code)
			content = []byte(code)
			payload = []byte(ms)
		}
	} else {
		content = []byte(cmd)
		payload = []byte(ar)
	}

	s.BaseLayer = layers.BaseLayer{Contents: content, Payload: payload}
	return nil
}

// NextLayerType returns the layer type of the ModbusTCP payload, which is LayerTypePayload.
func (s *smtp) NextLayerType() gopacket.LayerType {
	return gopacket.LayerTypePayload
}
func (s *smtp) LayerContents() []byte { return s.Contents }

func (s *smtp) Payload() []byte {
	return s.BaseLayer.Payload
}

func parseCmd(line string) (cmd string, arg string, err error) {
	line = strings.TrimRight(line, "\r\n")

	l := len(line)
	switch {
	case strings.HasPrefix(strings.ToUpper(line), "STARTTLS"):
		return "STARTTLS", "", nil
	case l == 0:
		return "", "", nil
	case l < 4:
		return "", "", fmt.Errorf("command too short: %q", line)
	case l == 4:
		v := strings.ToUpper(line)
		if _, ok := cmdlist[v]; ok {
			return strings.ToUpper(line), "", nil
		}
		return "", "", fmt.Errorf("not support command: %q", line)
	case l == 5:
		// Too long to be only command, too short to have args
		return "", "", fmt.Errorf("mangled command: %q", line)
	}

	// If we made it here, command is long enough to have args
	if line[4] != ' ' {
		// There wasn't a space after the command?
		return "", "", fmt.Errorf("mangled command: %q", line)
	}
	if _, ok := cmdlist[line[0:4]]; ok {
		return strings.ToUpper(line[0:4]), strings.TrimSpace(line[5:]), nil
	} else {
		return "", "", fmt.Errorf("not support command: %q", line)
	}

}

type ProtocolError string

func (p ProtocolError) Error() string {
	return string(p)
}
func parseCodeLine(line string) (code int, continued bool, message string, err error) {
	if len(line) < 4 || line[3] != ' ' && line[3] != '-' {
		err = ProtocolError("short response: " + line)
		return
	}
	continued = line[3] == '-'
	code, err = strconv.Atoi(line[0:3])
	if err != nil || code < 100 {
		err = ProtocolError("invalid response code: " + line)
		return
	}
	if _, ok := codeist[code]; !ok {
		err = ProtocolError("invalid response code: " + line)
		return
	}
	message = line[4:]

	return
}

var codeist = map[int]bool{
	101: true,
	211: true,
	214: true,
	220: true,
	221: true,
	235: true,
	250: true,
	251: true,
	252: true,
	334: true,
	354: true,
	421: true,
	422: true,
	431: true,
	441: true,
	442: true,
	446: true,
	450: true,
	451: true,
	452: true,
	454: true,
	455: true,
	471: true,
	500: true,
	501: true,
	502: true,
	503: true,
	504: true,
	510: true,
	512: true,
	523: true,
	530: true,
	535: true,
	538: true,
	541: true,
	550: true,
	551: true,
	552: true,
	553: true,
	554: true,
	555: true,
}

var cmdlist = map[string]bool{
	"EXPN":     true,
	"HELP":     true,
	"HELO":     true,
	"EHLO":     true,
	"LHLO":     true,
	"MAIL":     true,
	"RCPT":     true,
	"VRFY":     true,
	"NOOP":     true,
	"RSET":     true,
	"BDAT":     true,
	"DATA":     true,
	"QUIT":     true,
	"AUTH":     true,
	"STARTTLS": true,
}

func parseSmtpMessage(line []byte) ([]byte, error) {

	b := bytes.HasSuffix(line, []byte{0x0d, 0x0a, 0x2e, 0x0d, 0x0a})
	if b {
		messag := line[:len(line)-5]
		return messag, nil
	} else {
		return line, errors.New("format error")
	}

}
