package protocol

import (
	"bytes"
	"errors"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"golang.org/x/net/http2"
)

//var Layerhttp2 = gopacket.RegisterLayerType(int(http2Proto), gopacket.LayerTypeMetadata{Name: http2Proto.String(), Decoder: gopacket.DecodeFunc(decodeHttp2)})

type http2Parser struct {
}

func (p *http2Parser) Parse(pack gopacket.Packet) bool {
	tp := NewTcpParser()
	b := tp.Parse(pack)
	if !b {
		return false
	}
	_, err := decodeHttp2(tp.GetTcp().Payload)
	return err == nil
}

type hpackhttp2 struct {
	layers.BaseLayer // Stores the packet bytes and payload (Modbus PDU) bytes .
}

func decodeHttp2(data []byte) (*hpackhttp2, error) {

	h := &hpackhttp2{}
	err := h.DecodeFromBytes(data)
	if err != nil {
		return nil, err
	}

	return h, nil
}
func (h *hpackhttp2) DecodeFromBytes(data []byte) error {
	l := len(data)
	if l == 0 {
		return errors.New("http2 packet error")
	}
	var payload, content []byte
	reader := bytes.NewReader(data)
	f := http2.NewFramer(nil, reader)
	f1, err := f.ReadFrame()
	if err != nil {
		return err
	}
	switch v := f1.(type) {
	case *http2.DataFrame:
		payload = v.Data()
	default:
		content = data
	}
	h.BaseLayer = layers.BaseLayer{Contents: content, Payload: payload}
	return nil
}

// NextLayerType returns the layer type of the ModbusTCP payload, which is LayerTypePayload.
func (h *hpackhttp2) NextLayerType() gopacket.LayerType {
	return gopacket.LayerTypePayload
}

func (h *hpackhttp2) Payload() []byte {
	return h.BaseLayer.Payload
}
