package protocol

import (
	"encoding/binary"
	"errors"
	"strings"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

/*
var Layersmb = gopacket.RegisterLayerType(int(smbProto), gopacket.LayerTypeMetadata{Name: smbProto.String(), Decoder: gopacket.DecodeFunc(decodeSmb)})
*/

type smbParser struct {
}

func (p *smbParser) Parse(pack gopacket.Packet) bool {
	tp := NewTcpParser()
	b := tp.Parse(pack)
	if !b {
		return false
	}
	_, err := decodeSmb(tp.GetTcp().Payload)
	return err == nil
}

type smb struct {
	layers.BaseLayer
}

/*
func (s *smb) LayerType() gopacket.LayerType {
	return Layersmb
}*/

func decodeSmb(data []byte) (*smb, error) {
	smb := &smb{}
	err := smb.DecodeFromBytes(data)
	if err != nil {
		return nil, err
	}

	return smb, nil
}

func (s *smb) DecodeFromBytes(data []byte) error {
	var payload []byte
	// skip netbios layer if it exists
	if len(data) > 4 && data[0] == 0 {
		netbiosLen := binary.BigEndian.Uint32(data[:4])
		if int(netbiosLen) == len(data[4:]) {
			payload = data[4:]
		}
	}
	if len(payload) < 10 {
		return errors.New("size error")
	}
	// SMB protocol prefix
	hasSMBPrefix := strings.HasPrefix(string(payload), "\xFFSMB")
	// SMB protocol negotiation code
	isNegotiateProtocol := payload[4] == 0x72
	// error code must be zero
	errCode := binary.BigEndian.Uint32(payload[5:9])
	// if flag is 0 this packet is from the server to the client
	directionFlag := payload[9] & 0x80
	if hasSMBPrefix && isNegotiateProtocol && errCode == 0 && directionFlag == 0 {
		s.BaseLayer = layers.BaseLayer{Contents: data, Payload: data}
		return nil
	} else {
		return errors.New("smb error")
	}
}

// NextLayerType returns the layer type of the ModbusTCP payload, which is LayerTypePayload.
func (s *smb) NextLayerType() gopacket.LayerType {
	return gopacket.LayerTypePayload
}
func (s *smb) LayerContents() []byte { return s.Contents }

func (s *smb) Payload() []byte {
	return s.BaseLayer.Payload
}
