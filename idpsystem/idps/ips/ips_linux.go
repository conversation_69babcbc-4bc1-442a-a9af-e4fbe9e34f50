//go:build !windows
// +build !windows

package ips

import (
	"mnms/idpsystem/idps/detect"

	"os"

	"github.com/AkihiroSuda/go-netfilter-queue"
	"github.com/google/gonids"
	"gopkg.in/yaml.v2"
)

const defaultid uint16 = 0 //for linux
const idpstest = "idps_test"
const confyaml = "conf.yaml"

type Environment struct {
	Conf confQueue `yaml:"env"`
}

type confQueue struct {
	QueueNum uint `yaml:"queue-num"`
}

type UIps struct {
	id     uint16
	queue  *netfilter.NFQueue
	detect *detect.DetectEngineCtx
	event  Eventfunc
}

func loadYaml() (uint16, error) {
	yfile, err := os.ReadFile(confyaml)
	if err != nil {
		e := Environment{}
		c := confQueue{QueueNum: uint(defaultid)}
		e.Conf = c
		data, err := yaml.Marshal(&e)
		if err != nil {
			return defaultid, err
		}
		err2 := os.WriteFile(confyaml, data, 0)
		if err2 != nil {
			return defaultid, err
		}
		return defaultid, nil
	}
	e := Environment{}
	err2 := yaml.Unmarshal(yfile, &e)
	if err2 != nil {
		return defaultid, err
	}
	return uint16(e.Conf.QueueNum), nil

}

func NewIps() (Ipser, error) {
	n, err := loadYaml()
	if err != nil {
		return nil, err
	}
	q := &UIps{
		id: n,
	}
	d, err := detect.NewDetectEngineCtx()
	if err != nil {
		return nil, err
	}
	q.detect = d
	return q, nil
}

func (u *UIps) Start() error {

	v := os.Getenv(idpstest)
	if len(v) != 0 {
		u.id = defaultid
	}
	queue, err := netfilter.NewNFQueue(u.id, 30000, netfilter.NF_DEFAULT_PACKET_SIZE)
	if err != nil {
		return err
	}
	u.queue = queue
	go u.checkPacket(u.queue.GetPackets())
	return nil
}
func (w *UIps) Enablelo(b bool) {

}

func (w *UIps) AddGonidsRule(r *gonids.Rule) error {
	return w.detect.LoadGoNidRule(*r)
}

func (u *UIps) ApplyRules() error {
	u.detect.Apply()
	return nil
}

// RunAllRules make all rules work
func (U *UIps) Build() error {
	return U.detect.Build()
}

func (u *UIps) RegisterMatchEvent(e Eventfunc) {
	u.event = e
}

func (u *UIps) Close() error {
	u.queue.Close()
	return nil

}

func (u *UIps) checkPacket(packetChan <-chan netfilter.NFPacket) {
	for nfpacket := range packetChan {
		pools.Submit(func() {
			func(nf netfilter.NFPacket) {
				u.detect.DetectPacket(newNetLinker(nf, u.event), nf.Packet.Data())
			}(nfpacket)
		})
	}
}

func newNetLinker(nf netfilter.NFPacket, event Eventfunc) detect.NetLinker {
	return &netlink{nf: nf, event: event}
}

type netlink struct {
	nf    netfilter.NFPacket
	event Eventfunc
}

func (n *netlink) Default() {
	n.nf.SetVerdict(netfilter.NF_ACCEPT)
}

func (n *netlink) Pass() {
	n.nf.SetVerdict(netfilter.NF_ACCEPT)
}

func (n *netlink) Drop() {
	n.nf.SetVerdict(netfilter.NF_DROP)
}

func (n *netlink) Alert(e detect.InfoMatched) {
	n.match(e)
}
func (n *netlink) match(e detect.InfoMatched) {
	if n.event != nil {
		go n.event(convertToEvent(int(n.nf.Idx), e))
	}
}
