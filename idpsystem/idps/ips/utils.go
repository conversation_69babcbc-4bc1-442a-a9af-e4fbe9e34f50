package ips

import (
	"mnms/idpsystem/idps/detect"
	"time"
)

func convertToEvent(idx int, d detect.InfoMatched) Event {
	name := findInterfaceNameByIndex(idx)
	e := Event{
		Id:        d.Id,
		Timestamp: time.Now().UnixMicro(),
		EthName:   name,
		Action:    d.Action,
		Protocol:  d.Protocol,
		Srcip:     d.Srcip,
		Src<PERSON>ort:   d.<PERSON>c<PERSON>,
		Destip:    d.<PERSON>,
		DestPort:  d.<PERSON>,
		Message:   d.Message,
	}
	return e
}
