package detect

import (
	"mnms/idpsystem/idps/src/keywords"
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gonids"
)

func newsignatures(size int) []*signature {
	return make([]*signature, 0, size)
}

type signature struct {
	protDetecter protocol.ProcotolAnalyzer //verified protocol
	net          *Internet                 //verified all network
	sigtype      sigtype
	nidRule      gonids.Rule
	action       Action
	sid          int    // sid of rule
	interid      uint32 //order id for search
	detect       keywords.Detecter
	flags        sigflags
	headermatch  *headMatcher //verified header
}

func (s *signature) BuildAll(info ExtraData) error {
	err := s.buildKeyWords(info)
	if err != nil {
		return err
	}
	err = s.buildDetectPorto()
	if err != nil {
		return err
	}
	err = s.buildInternet()
	if err != nil {
		return err
	}

	s.tagType()
	return nil
}
func (s *signature) headertail() *headMatcher {
	if s.headermatch == nil {
		return nil
	}
	temp := s.headermatch
	for temp.next != nil {
		temp = temp.next
	}
	return temp
}

func (s *signature) buildKeyWords(info ExtraData) error {
	err := s.buildContent(info)
	if err != nil {
		return err
	}
	err = s.buildListMatch()
	if err != nil {
		return err
	}
	err = s.buildStatements()
	if err != nil {
		return err
	}
	err = s.buildTags()
	if err != nil {
		return err
	}
	return nil
}

func (s *signature) buildContent(info ExtraData) error {
	d, err := keywords.NewDetecter(int(s.interid), s.nidRule, info.payloadInfo)
	if err != nil {
		return err
	}
	if d == nil {
		return nil
	}
	err = d.Build()
	if err != nil {
		return err
	}
	s.detect = d

	return nil
}
func (s *signature) buildListMatch() error {
	h := s.headertail()
	if h == nil {
		h = &headMatcher{}
		defer func() {
			s.headermatch = h.next
		}()
	}
	temp := h
	for _, v := range s.nidRule.LenMatchers() {
		switch v.Kind.String() {
		case "ttl", "id", "seq", "ack", "tcp.mss", "itype", "icode", "icmp_id", "icmp_seq", "icmpv6.mtu":
			k, err := keywords.NewListMatch(v.Kind.String())
			if err != nil {
				return err
			}
			err = k.SetUp(v)
			if err != nil {
				return err
			}
			if m, ok := k.(keywords.Matcher); ok {
				temp.next = &headMatcher{key: m}
				temp = temp.next
			}
		}
	}
	for _, v := range s.nidRule.Matchers {
		switch v.(type) {
		case *gonids.RPC:
			k, err := keywords.NewListMatch("rpc")
			if err != nil {
				return err
			}
			err = k.SetUp(v)
			if err != nil {
				return err
			}
			if m, ok := k.(keywords.Matcher); ok {
				temp.next = &headMatcher{key: m}
				temp = temp.next
			}
		}

	}

	return nil
}

func (s *signature) buildStatements() error {
	h := s.headertail()
	if h == nil {
		h = &headMatcher{}
		defer func() {
			s.headermatch = h.next
		}()
	}
	temp := h
	for _, v := range s.nidRule.Statements {
		switch v {
		case "sameip":
			k, err := keywords.NewStatement(v)
			if err != nil {
				return err
			}
			if m, ok := k.(keywords.Matcher); ok {
				temp.next = &headMatcher{key: m}
				temp = temp.next
			}
		}
	}
	return nil
}

func (s *signature) buildTags() error {
	h := s.headertail()
	if h == nil {
		h = &headMatcher{}
		defer func() {
			s.headermatch = h.next
		}()
	}
	temp := h
	for k, v := range s.nidRule.Tags {
		switch k {
		case "ipopts", "ip_proto", "geoip", "fragbits", "fragoffset", "tos", "tcp.flags", "window":
			key, err := keywords.NewTags(k)
			if err != nil {
				return err
			}
			err = key.SetUp(v)
			if err != nil {
				return err
			}
			if m, ok := key.(keywords.Matcher); ok {
				temp.next = &headMatcher{key: m}
				temp = temp.next
			}
		}
	}
	return nil
}

func (s *signature) buildDetectPorto() error {
	p, err := protocol.FindPacketAnalyzer(s.nidRule.Protocol)
	if err != nil {
		return err
	}
	s.protDetecter = p
	return nil
}

func (s *signature) buildInternet() error {
	net, err := NewInternet(s.nidRule.Source.Nets, s.nidRule.Source.Ports, s.nidRule.Destination.Nets, s.nidRule.Destination.Ports)
	if err != nil {
		return err
	}
	s.net = net
	return nil
}

func (s *signature) tagType() {
	if s.detect == nil && s.headermatch == nil {
		if s.net.containsNegation {
			s.sigtype = sig_type_like_iponly
		} else {
			s.sigtype = sig_type_iponly
		}
	} else if s.detect != nil && s.detect.DataPos() == gonids.PayloadData {
		s.sigtype = sig_type_stream
	} else {
		s.sigtype = sig_type_pkt
	}

	if s.detect != nil && s.detect.PrefilterData() != nil {
		s.flags |= sig_flag_prefilter
	} else {
		s.flags |= sig_flag_non_prefilter
	}

}

func (s *signature) IsBidirectional() *signature {
	if s.nidRule.Bidirectional {
		new := *s
		new.nidRule.Source.Nets, new.nidRule.Destination.Nets = new.nidRule.Destination.Nets, new.nidRule.Source.Nets
		new.nidRule.Source.Ports, new.nidRule.Destination.Ports = new.nidRule.Destination.Ports, new.nidRule.Source.Ports
		return &new
	}
	return nil
}

func (s *signature) DetectInspection(detctx *DetectEngineCtx, packet *Packet) bool {
	if s.detect == nil {
		return true
	}

	pc := keywords.PacketConfig{GoPacket: packet.goPacket, Payload: packet.payload, ByteValues: detctx.byteValues}
	return s.detect.DetectContentInspection(pc)
}

func (s *signature) DetectProto(p *Packet) bool {
	return s.protDetecter.Parse(p.goPacket) && s.detectheader(p)
}

func (s *signature) DetectIPAndPort(p *Packet) bool {
	return s.DetectIP(p) && s.DetectPort(p)
}

func (s *signature) detectheader(packet *Packet) bool {
	if s.headermatch == nil {
		return true
	}
	return s.headermatch.detect(packet)
}

func (s *signature) DetectIP(p *Packet) bool {
	return s.net.verifyIP(p.srcip, p.dstip, p.family)
}

func (s *signature) DetectPort(p *Packet) bool {
	return s.net.verifyPort(p.srcport, p.dstport)
}
