package detect

const AnyPort = "any"
const anyport = 0

type port<PERSON>hecker interface {
	detect(uint16) bool
}

type portflag uint

const (
	portFlag portflag = 1 << iota
	negativePortFlag
	rangePortFlag
	negativeRangePortFlag
	anyPortFlag
	negativeAnyPortFlag
)

type portInfoDetected struct {
	ports             map[uint16]empty
	negativeports     map[uint16]empty
	portrange         []portRange
	neagtivePortRange []portRange
	flag              portflag
	postivechecker    []portChecker
	negativechecker   []portChecker
}

type portRange struct {
	max uint16
	min uint16
}

func NewPortInfoDetected(info *portInfo) (*portInfoDetected, error) {
	r := &portInfoDetected{ports: map[uint16]empty{}, negativeports: map[uint16]empty{}, portrange: []portRange{}, neagtivePortRange: []portRange{}}
	curr := info
	for curr != nil {
		ports, err := parsePortRange(curr.port)
		if err != nil {
			return r, err
		}
		switch len(ports) {
		case 1:
			if ports[0] == anyport {
				if curr.negated {
					r.flag |= negativeAnyPortFlag
				} else {
					r.flag |= anyPortFlag
				}
			} else {
				if curr.negated {
					r.flag |= negativePortFlag
					r.negativeports[ports[0]] = empty{}
				} else {
					r.flag |= portFlag
					r.ports[ports[0]] = empty{}
				}
			}
		case 2:
			pr := portRange{}
			pr.min = ports[0]
			pr.max = ports[1]
			if curr.negated {
				r.flag |= negativeRangePortFlag
				r.neagtivePortRange = append(r.neagtivePortRange, pr)

			} else {
				r.flag |= rangePortFlag
				r.portrange = append(r.portrange, pr)
			}
		}
		curr = curr.next
	}
	if r.flag&portFlag == portFlag {
		c := &ports{ports: r.ports}
		r.postivechecker = append(r.postivechecker, c)
	}
	if r.flag&negativePortFlag == negativePortFlag {
		c := &negativePorts{ports: r.negativeports}
		r.negativechecker = append(r.negativechecker, c)
	}
	if r.flag&rangePortFlag == rangePortFlag {
		c := &portRnge{portrange: r.portrange}
		r.postivechecker = append(r.postivechecker, c)
	}
	if r.flag&negativeRangePortFlag == negativeRangePortFlag {
		c := &neagtivePortRnge{portrange: r.neagtivePortRange}
		r.negativechecker = append(r.negativechecker, c)
	}

	if r.flag&anyPortFlag == anyPortFlag {
		c := &anyPorts{}
		r.postivechecker = append(r.postivechecker, c)
	}
	if r.flag&negativeAnyPortFlag == negativeAnyPortFlag {
		c := &negativeAnyPorts{}
		r.negativechecker = append(r.negativechecker, c)
	}

	return r, nil
}
func (p *portInfoDetected) verifyPort(port uint16) bool {
	for _, checker := range p.negativechecker {
		r := checker.detect(port)
		if !r {
			return false
		}
	}
	if len(p.postivechecker) == 0 {
		return true
	}
	for _, checker := range p.postivechecker {
		r := checker.detect(port)
		if r {
			return true
		}
	}
	return false
}

type ports struct {
	ports map[uint16]empty
}

func (p *ports) detect(port uint16) bool {
	if p.ports == nil {
		return true
	}
	_, ok := p.ports[port]
	return ok
}

type negativePorts struct {
	ports map[uint16]empty
}

func (n *negativePorts) detect(port uint16) bool {
	if n.ports == nil {
		return true
	}
	_, ok := n.ports[port]
	return !ok
}

type portRnge struct {
	portrange []portRange
}

func (p *portRnge) detect(port uint16) bool {
	if p.portrange == nil {
		return true
	}
	for _, v := range p.portrange {
		if v.min <= port && port <= v.max {
			return true
		}
	}
	return false
}

type neagtivePortRnge struct {
	portrange []portRange
}

func (n *neagtivePortRnge) detect(port uint16) bool {
	if n.portrange == nil {
		return true
	}
	for _, v := range n.portrange {
		if v.min <= port && port <= v.max {
			return false
		}
	}
	return true
}

type anyPorts struct {
}

func (a *anyPorts) detect(port uint16) bool {

	return true
}

type negativeAnyPorts struct {
}

func (a *negativeAnyPorts) detect(port uint16) bool {
	return false
}
