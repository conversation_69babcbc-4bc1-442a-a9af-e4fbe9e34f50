package detect

import (
	"mnms/idpsystem/idps/src/protocol"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type Packet struct {
	family         int
	srcip          string
	srcport        uint16
	dstip          string
	dstport        uint16
	payload        []byte
	payloadLen     uint32
	networkLayer   gopacket.NetworkLayer
	transportLayer gopacket.TransportLayer
	goPacket       gopacket.Packet
	protocol       layers.IPProtocol
	record         packetConf
}
type packetConf struct {
	maxAlertSzie int
	acted        Action
}

func newPacketConf() packetConf {
	return packetConf{maxAlertSzie: 15, acted: ActionNone}
}

func ConvertBytesToPacket(b []byte) (*Packet, error) {
	packet, err := protocol.ConvertGoNetWorkPacket(b)
	if err != nil {
		return &Packet{}, err
	}
	p := &Packet{record: newPacketConf(), goPacket: packet}
	err = NetWorkLayer(p)
	if err != nil {
		return nil, err
	}
	err = TransportLayer(p)
	if err != nil {
		return nil, err
	}
	err = ApplicationLayer(p)
	if err != nil {
		return nil, err
	}
	return p, nil

}

func NetWorkLayer(p *Packet) error {
	if netLayer := p.goPacket.NetworkLayer(); netLayer != nil {
		switch layer := netLayer.(type) {
		case *layers.IPv4:
			p.family = AFInet
			p.srcip = layer.SrcIP.String()
			p.dstip = layer.DstIP.String()
			p.networkLayer = netLayer
			return nil
		case *layers.IPv6:
			p.family = AFInet6
			p.srcip = layer.SrcIP.String()
			p.dstip = layer.DstIP.String()
			p.networkLayer = netLayer
			return nil
		}
	}
	return ErrorLayer
}

func TransportLayer(p *Packet) error {
	if transport := p.goPacket.TransportLayer(); transport != nil {
		switch layer := transport.(type) {
		case *layers.TCP:
			p.srcport = uint16(layer.SrcPort)
			p.dstport = uint16(layer.DstPort)
			p.transportLayer = layer
			p.payload = layer.Payload
			p.protocol = layers.IPProtocolTCP
			return nil
		case *layers.UDP:
			p.srcport = uint16(layer.SrcPort)
			p.dstport = uint16(layer.DstPort)
			p.transportLayer = layer
			p.payload = layer.Payload
			p.protocol = layers.IPProtocolUDP
			return nil
			//
		}
	}
	return nil
}

func ApplicationLayer(p *Packet) error {
	if app := p.goPacket.ApplicationLayer(); app != nil {
		p.payload = app.LayerContents()
		p.payloadLen = uint32(len(p.payload))
	}

	return nil
}
