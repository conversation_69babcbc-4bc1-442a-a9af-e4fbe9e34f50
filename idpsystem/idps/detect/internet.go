package detect

import (
	"fmt"
	"strings"
)

type internetFlag uint

const (
	srcipflag internetFlag = 1 << iota
	dstipflag
	srcportflag
	dstportflag
)

type Internet struct {
	srcIPDetected    *IpDetected
	dstIPDetected    *IpDetected
	srcPortDetected  *portInfoDetected
	dstPortDetected  *portInfoDetected
	srcipinfos       *iPInfo
	dstipinfos       *iPInfo
	containsNegation bool
}

func NewInternet(srcip, srcport, dstip, dstport []string) (*Internet, error) {
	i := &Internet{}
	err := internetAdd(i, srcip, srcipflag)
	if err != nil {
		return nil, err
	}
	err = internetAdd(i, dstip, dstipflag)
	if err != nil {
		return nil, err
	}
	err = internetAdd(i, srcport, srcportflag)
	if err != nil {
		return nil, err
	}
	err = internetAdd(i, dstport, dstportflag)
	if err != nil {
		return nil, err
	}
	if i.srcIPDetected.negated || i.dstIPDetected.negated {
		i.containsNegation = true
	}
	return i, nil
}

func internetAdd(i *Internet, value []string, f internetFlag) error {
	switch f {
	case srcipflag, dstipflag:
		ipinfo := newIPInfo()
		ips := addsquarebrackets(value)
		for _, ip := range ips {
			err := parseIpAndPort(ip, ipinfo, false, iPType)
			if err != nil {
				return err
			}
		}

		d, err := NewIpAddressDetected(ipinfo)
		if err != nil {
			return err
		}
		if f == srcipflag {
			i.srcIPDetected = d
			i.srcipinfos = ipinfo
		} else {
			i.dstIPDetected = d
			i.dstipinfos = ipinfo
		}
	case srcportflag, dstportflag:
		portinfos := newPortInfo()
		ports := addsquarebrackets(value)
		for _, port := range ports {
			err := parseIpAndPort(port, portinfos, false, portType)
			if err != nil {
				return err
			}
		}
		portinfo := portinfos.(*portInfo)
		d, err := NewPortInfoDetected(portinfo)
		if err != nil {
			return err
		}
		if f == srcportflag {
			i.srcPortDetected = d
		} else {
			i.dstPortDetected = d
		}
	}

	return nil
}

func (i *Internet) verifyIP(srcip string, dstip string, family int) bool {
	verify := func(srcip string, dstip string) bool {
		if ok := i.srcIPDetected.verify(family, srcip); !ok {
			return false
		}
		if ok := i.dstIPDetected.verify(family, dstip); !ok {
			return false
		}
		return true
	}
	r := verify(srcip, dstip)
	return r
}

func (i *Internet) verifyPort(srport uint16, dstport uint16) bool {
	verify := func(srport uint16, dstport uint16) bool {
		if ok := i.srcPortDetected.verifyPort(srport); !ok {
			return false
		}
		if ok := i.dstPortDetected.verifyPort(dstport); !ok {
			return false
		}
		return true
	}
	r := verify(srport, dstport)
	return r
}

func addsquarebrackets(s []string) []string {
	r := make([]string, len(s))
	for i, v := range s {
		if !strings.HasPrefix(v, "[") && !strings.HasSuffix(v, "]") {
			r[i] = fmt.Sprintf("[%v]", v)
		} else {
			r[i] = v
		}
	}
	return r
}
