package detect

import "net"

//config which maybe set by file in furutre

var configuration *Config

// all ip of localhost
const HomeNet = "$HOME_NET"

// all ip of external
const ExternalNet = "$EXTERNAL_NET"

// alny ip
const AnyNet = "any"

func init() {
	configuration = NewConfig()
	err := configuration.Init()
	if err != nil {
		panic(err)
	}
}

type Config struct {
	homeIP []string
	//externalIP []string qeaul to !homeIP
	anyIP []string
}

func NewConfig() *Config {
	c := &Config{}
	return c
}

//read config from file in furture
func (c *Config) Init() error {
	err := c.readIP()
	if err != nil {
		return err
	}
	return nil
}

func (c *Config) readIP() error {
	ips, err := getHomeIps()
	if err != nil {
		return err
	}
	c.homeIP = ips
	c.anyIP = []string{"0.0.0.0/0", "::/0"}
	return nil
}

func getHomeIps() ([]string, error) {
	ips := make([]string, 0, 8)
	inters, _ := net.Interfaces()
	for _, inter := range inters {
		if inter.Flags&net.FlagUp == net.FlagUp {
			addrs, _ := inter.Addrs()
			for _, addr := range addrs {
				if ipnet, ok := addr.(*net.IPNet); ok {
					ips = append(ips, ipnet.IP.String())
				}
			}
		}
	}
	return ips, nil
}
