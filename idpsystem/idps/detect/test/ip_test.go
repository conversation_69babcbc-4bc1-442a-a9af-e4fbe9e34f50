package test

import (
	"fmt"
	"mnms/idpsystem/idps/detect"
	"net"
	"testing"

	"github.com/google/gonids"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

const captured = true
const uncaptured = false

type testNetLinker struct {
	value bool
	id    int
}

func (t *testNetLinker) Pass() {
	t.value = true
}

func (t *testNetLinker) Drop() {
	t.value = true
}

func (t *testNetLinker) Alert(d detect.InfoMatched) {
	t.value = true
	t.id = d.Id
}

func (t *testNetLinker) Default() {

}

type extraV6value struct {
	hopLimit uint8
}

func IPv6PacketWithValue(srcip string, srcport uint16, dstip string, dstport uint16, extra extraV6value, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv6{
		SrcIP:      net.ParseIP(srcip),
		DstIP:      net.ParseIP(dstip),
		Version:    6,
		NextHeader: layers.IPProtocolTCP,
		HopLimit:   extra.hopLimit,
	}

	tcpLayer := &layers.TCP{
		SrcPort: layers.TCPPort(srcport),
		DstPort: layers.TCPPort(dstport),
	}

	tcpLayer.SetNetworkLayerForChecksum(ipLayer)

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, tcpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

type extravalue struct {
	ipnumber uint8
	ttl      uint8
	opt      uint8
	id       uint16
	fragbit  uint8
	offset   uint16
	tos      uint8
}

func IPv4PacketWithValue(srcip string, srcport uint16, dstip string, dstport uint16, extra extravalue, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv4{
		SrcIP:      net.ParseIP(srcip),
		DstIP:      net.ParseIP(dstip),
		Version:    4,
		TTL:        extra.ttl,
		Id:         extra.id,
		TOS:        extra.tos,
		Protocol:   layers.IPProtocolTCP,
		Flags:      layers.IPv4Flag(extra.fragbit),
		FragOffset: extra.offset,
	}
	if extra.ipnumber != 0 {
		ipLayer.Protocol = layers.IPProtocol(extra.ipnumber)
	}
	if extra.opt > 0 {
		ipLayer.Options = []layers.IPv4Option{{OptionType: extra.opt,
			OptionLength: 12,
			OptionData:   make([]byte, 10),
		}}
	}

	tcpLayer := &layers.TCP{
		SrcPort: layers.TCPPort(srcport),
		DstPort: layers.TCPPort(dstport),
	}

	tcpLayer.SetNetworkLayerForChecksum(ipLayer)

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, tcpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

func TestTTl(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";content:"sdfd";ttl:10;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			extra: extravalue{
				ttl: 10,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";content:"sdfd";ttl:9;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			extra: extravalue{
				ttl: 10,
			},
			link: testNetLinker{},
		},

		{
			rule:     []string{`alert ip any any -> any any (msg:"test";content:"sdfd";ttl:5;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			extra: extravalue{
				ttl: 5,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestTTL:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestIopts(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ipopts:ts;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			extra: extravalue{
				opt: 0x44,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ipopts:lsrr;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			extra: extravalue{
				opt: 0x44,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ipopts:esec;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			extra: extravalue{
				opt: 0x85,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIopts:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestSameIP(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";sameip;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"*********", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";sameip;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"************", "************"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ttl:10;sameip;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"************", "************"},
			port:     []uint16{10, 20},
			extra:    extravalue{ttl: 5},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestSameIP:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpProto(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ip_proto:5;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"*********", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ip_proto:6;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"************", "************"},
			port:     []uint16{10, 20},
			extra:    extravalue{offset: 5},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ip_proto:59;content:"sdfd";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"************", "************"},
			port:     []uint16{10, 20},
			extra:    extravalue{offset: 5, ipnumber: 60},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";ip_proto:ICMP;content:"sdfd";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"************", "************"},
			port:     []uint16{10, 20},
			extra:    extravalue{offset: 5, ipnumber: 1},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIpProto:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpv4hdr(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule: []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; content:"|06|"; offset:9; depth:1; sid:1; rev:1;)`,
				`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; content:"|06|"; offset:9; depth:1; sid:40; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"*********", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv4.hdr; content:"|11|"; offset:9; depth:1; sid:1; rev:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"*********", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIpv4hdr:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpv6hdr(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extraV6value
		link     testNetLinker
	}{

		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv6.hdr; content:"|17|"; offset:6; depth:1; sid:1; rev:1;)`},
			expected: uncaptured,
			packet:   []byte("456"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},
			port:     []uint16{10, 20},
			extra:    extraV6value{},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; ipv6.hdr; content:"|06|"; offset:6; depth:1; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},
			port:     []uint16{10, 20},
			extra:    extraV6value{},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("index:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv6PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpid(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; id:10; content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"*********", "127.0.0.1"},
			port:     []uint16{10, 20},
			extra:    extravalue{id: 10},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; id:20; content:"sd"; sid:1; rev:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"*********", "127.0.0.1"},
			port:     []uint16{10, 20},
			extra:    extravalue{id: 9},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";ttl:100;id:122; content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"*********", "127.0.0.1"},
			port:     []uint16{10, 20},
			extra:    extravalue{id: 122, ttl: 100},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIpid:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpGeoip(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";geoip:both,UK;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{id: 10},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";geoip:both,UK;content:"sd"; sid:1; rev:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"************", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{id: 10},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";geoip:any,UK;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"************", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{id: 10},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";geoip:dst,UK;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"************", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{id: 10},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";geoip:src,UK;content:"sd"; sid:1; rev:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"************", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{id: 10},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIpGeoip:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpv6Geoip(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extraV6value
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";geoip:both,US;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2600:1406:abcd::1234", "2600:1406:abcd::1234"},
			port:     []uint16{10, 20},
			extra:    extraV6value{},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";geoip:both,CN;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2408:4002:8001::1", "2408:4002:8001::1"},
			port:     []uint16{10, 20},
			extra:    extraV6value{},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIpv6Geoip:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv6PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpfragbits(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";fragbits:D;content:"sd"; sid:1; rev:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";fragbits:D;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{fragbit: 2},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";fragbits:M;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{fragbit: 1},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";fragbits:!M;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{fragbit: 2},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";fragbits:*M;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{fragbit: 1},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";fragbits:+M;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{fragbit: 5},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("Testfragbits:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpfragoffset(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";fragoffset:>10;content:"sd"; sid:1; rev:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{offset: 5},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIpfragoffset:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpv6fragoffset(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extraV6value
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example";fragoffset:>10;content:"sd"; sid:1; rev:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2408:4002:8001::1", "2408:4002:8001::1"},
			port:     []uint16{10, 20},
			extra:    extraV6value{},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIpv6fragoffset:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv6PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestIpTos(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		extra    extravalue
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; tos:8;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{tos: 8},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; tos:8;content:"sd"; sid:1; rev:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{tos: 5},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"IPv4 header keyword example"; tos:!10;content:"sd"; sid:1; rev:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"***********", "***********"},
			port:     []uint16{10, 20},
			extra:    extravalue{tos: 5},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIpTos:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := IPv4PacketWithValue(test.ip[0], test.port[0], test.ip[1], test.port[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}
