package test

import (
	"fmt"
	"mnms/idpsystem/idps/detect"
	"net"
	"testing"

	"github.com/google/gonids"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

func UDPPacketIPv4(srcip string, srcport uint16, dstip string, dstport uint16, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv4{
		SrcIP:    net.ParseIP(srcip),
		DstIP:    net.ParseIP(dstip),
		Version:  4,
		Protocol: layers.IPProtocolUDP,
	}

	udpLayer := &layers.UDP{
		SrcPort: layers.UDPPort(srcport),
		DstPort: layers.UDPPort(dstport),
	}

	udpLayer.SetNetworkLayerForChecksum(ipLayer)

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, udpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

func UDPPacketIPv6(srcip string, srcport uint16, dstip string, dstport uint16, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv6{
		SrcIP:      net.ParseIP(srcip),
		DstIP:      net.ParseIP(dstip),
		Version:    6,
		NextHeader: layers.IPProtocolUDP,
		HopLimit:   64,
	}

	udpLayer := &layers.UDP{
		SrcPort: layers.UDPPort(srcport),
		DstPort: layers.UDPPort(dstport),
	}

	udpLayer.SetNetworkLayerForChecksum(ipLayer)

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, udpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}
func TestUdphdr(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";udp.hdr; content:"|00 08|"; offset:4; depth:2;sid:1;)`},
			expected: captured,
			packet:   []byte(""),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";udp.hdr; content:"|00 08|"; offset:4; depth:2;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("sdfsdffsd"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestUdphdr:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := UDPPacketIPv4(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestUdpV6hdr(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";udp.hdr; content:"|00 08|"; offset:4; depth:2;sid:1;)`},
			expected: captured,
			packet:   []byte(""),
			ip:       []string{"2001:db8::1", "2001:db8::2"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";udp.hdr; content:"|00 08|"; offset:4; depth:2;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("sdfsdffsd"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestUdpV6hdr:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := UDPPacketIPv6(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}

func TestHdrMixed(t *testing.T) {
	var tests = []struct {
		rule       []string
		existed    []int
		notexisted []int
		packet     []byte
		ip         []string
		port       []uint16
		link       *testNetLinkerID
		id         int
	}{
		{
			rule: []string{`alert ip any any -> any any (msg:"test";udp.hdr; content:"|00 08|"; offset:4; depth:2;sid:1;)`,
				`alert ip any any -> any any (msg:"test";ipv4.hdr; content:"|11|"; offset:9; depth:1;sid:10;)`,
			},
			existed: []int{1, 10},
			packet:  []byte(""),
			ip:      []string{"127.0.0.1", "127.0.0.1"},
			port:    []uint16{10, 20},
			link:    newTestNetLinkerID(),
			id:      1,
		},
		{
			rule: []string{`alert ip any any -> any any (msg:"test";ipv4.hdr; content:"|11|"; offset:9; depth:1;sid:10;)`,
				`alert ip any any -> any any (msg:"test";udp.hdr; content:"|00 08|"; offset:4; depth:2;sid:1;)`,
			},
			existed: []int{1, 10},
			packet:  []byte(""),
			ip:      []string{"127.0.0.1", "127.0.0.1"},
			port:    []uint16{10, 20},
			link:    newTestNetLinkerID(),
			id:      10,
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestHdrMixed:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := UDPPacketIPv4(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(test.link, p)
			for _, v := range test.existed {
				if !test.link.checkIDExisted(v) {
					t.Fatalf("not find id:%v", v)
				}
			}
			for _, v := range test.notexisted {
				if !test.link.checkIDNotExisted(v) {
					t.Fatalf("id:%v should not found", v)
				}
			}
		})
	}
}
