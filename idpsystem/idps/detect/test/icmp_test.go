package test

import (
	"fmt"
	"mnms/idpsystem/idps/detect"
	"net"
	"testing"

	"github.com/google/gonids"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type icmp struct {
	icmpType layers.ICMPv4TypeCode
	id       uint16
	seq      uint16
}

func ICMPv4Packet(srcip string, dstip string, value icmp, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv4{
		SrcIP:    net.ParseIP(srcip),
		DstIP:    net.ParseIP(dstip),
		Version:  4,
		Protocol: layers.IPProtocolICMPv4,
	}
	icmpLayer := &layers.ICMPv4{
		TypeCode: value.icmpType,
		Id:       value.id,
		Seq:      value.seq,
	}

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, icmpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

func createICMPv6MtuPacket(srcip string, dstip string, mtu int, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv6{
		SrcIP:      net.ParseIP(srcip),
		DstIP:      net.ParseIP(dstip),
		Version:    6,
		NextHeader: layers.IPProtocolICMPv6,
		HopLimit:   64,
	}

	icmpLayer := &layers.ICMPv6{
		TypeCode: layers.CreateICMPv6TypeCode(layers.ICMPv6TypePacketTooBig, 0),
	}
	originalIPv6Header := []byte{
		0, 0, 0, 0, 0x60, 0x00, 0x00, 0x00, // IPv6 版本 + Flow Label
		0x00, 0x14, 0x3a, 0xff, // Payload Length + Next Header + Hop Limit
	}

	originalIPv6Header[0] = byte(mtu >> 24)
	originalIPv6Header[1] = byte(mtu >> 16)
	originalIPv6Header[2] = byte(mtu >> 8)
	originalIPv6Header[3] = byte(mtu)
	icmpLayer.SetNetworkLayerForChecksum(ipLayer)
	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, icmpLayer, gopacket.Payload(originalIPv6Header), gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

func TestIcmpItype(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		extra    icmp
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";itype:>10;;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x0c00,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";itype:<10;;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x0c00,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";itype:10;;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x0c00,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";itype:10;;sid:1;)`},
			expected: true,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x0a00,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";itype:2<>10;;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x0800,
			},
			link: testNetLinker{},
		},
		/*{
			rule:     []string{`alert ip any any -> any any (msg:"test";itype:2<>10;;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x0e00,
			},
			link: testNetLinker{},
		},*/
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIcmpItype:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := ICMPv4Packet(test.ip[0], test.ip[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestIcmpIcode(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		extra    icmp
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icode:>10;;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x000c,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icode:<10;;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x000c,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icode:10;;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x000c,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icode:10;;sid:1;)`},
			expected: true,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x000a,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icode:2<>10;;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x0008,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icode:2<>10;;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				icmpType: 0x000e,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIcmpIcode:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := ICMPv4Packet(test.ip[0], test.ip[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestIcmpId(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		extra    icmp
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmp_id:15;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				id: 20,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmp_id:20;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				id: 20,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIcmpId:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := ICMPv4Packet(test.ip[0], test.ip[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestIcmpSeq(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		extra    icmp
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmp_seq:15;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				seq: 10,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmp_seq:10;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				seq: 10,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIcmpSeq:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := ICMPv4Packet(test.ip[0], test.ip[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestIcmpv4hdr(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		extra    icmp
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv4.hdr;content:"|06|";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				seq: 6,
			},
			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv4.hdr;content:"|06|";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			extra: icmp{
				seq: 10,
			},
			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIcmpv4hdr:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := ICMPv4Packet(test.ip[0], test.ip[1], test.extra, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestIcmpV6hdr(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		mtu      int
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv6.hdr;content:"|05|";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},
			mtu:      1280,
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv6.hdr;content:"|05|";sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},
			mtu:      10,
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv6.hdr;content:"|0a|";sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},
			mtu:      10,
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIcmpV6hdr:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := createICMPv6MtuPacket(test.ip[0], test.ip[1], test.mtu, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}

func TestIcmpV6Mtu(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		mtu      int
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv6.mtu:>50;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},

			mtu: 1280,

			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv6.mtu:10<>50;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},

			mtu: 1280,

			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv6.mtu:10<>50;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},

			mtu: 40,

			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv6.mtu:60;sid:1;)`},
			expected: captured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},

			mtu: 60,

			link: testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";icmpv6.mtu:<60;sid:1;)`},
			expected: uncaptured,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"2001:db8::1", "2001:db8::2"},

			mtu: 90,

			link: testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestIcmpV6Mtu:%v", index), func(t *testing.T) {
			d, err := detect.NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := createICMPv6MtuPacket(test.ip[0], test.ip[1], test.mtu, test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Error("not found packet")
			}
		})
	}
}
