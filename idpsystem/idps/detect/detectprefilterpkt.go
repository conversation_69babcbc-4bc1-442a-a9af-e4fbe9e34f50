package detect

import (
	"mnms/idpsystem/idps/mpm"
	"mnms/idpsystem/idps/src/keywords"

	"github.com/google/gonids"
)

type inspectPkt struct {
	pktfilter keywords.MpmPkter
	pos       gonids.DataPos
	next      *inspectPkt
}

func newInspectPkt(pos gonids.DataPos) (*inspectPkt, error) {
	pkt, err := keywords.NewMpmPkt(pos.String())
	if err != nil {
		return nil, err
	}
	return &inspectPkt{pos: pos, pktfilter: pkt}, nil

}

func (i *inspectPkt) Build() error {
	cur := i
	for cur != nil {
		err := cur.pktfilter.Build()
		if err != nil {
			return err
		}
		cur = cur.next
	}
	return nil
}

func (i *inspectPkt) Add(pos gonids.DataPos, m mpm.Content) error {
	cur := i
	for cur != nil {
		if cur.pos == pos {
			return cur.pktfilter.AddContent(m)
		}
		if cur.next == nil {
			newInspect, err := newInspectPkt(pos)
			if err != nil {
				return err
			}
			cur.next = newInspect
			return newInspect.pktfilter.AddContent(m)
		}
		cur = cur.next
	}
	return nil
}
func (i *inspectPkt) Matches(packet *Packet, count int) []int {
	res := make([]int, 0, count)
	cur := i
	for cur != nil {
		ids := cur.pktfilter.MatchIds(packet.goPacket)
		res = append(res, ids...)
		cur = cur.next
	}
	return res
}

type detectprefilterpkt struct {
	tempinspect *inspectPkt
	tempcount   int
	count       int
	inspect     *inspectPkt
}

func newDetectprefilterpkt() (*detectprefilterpkt, error) {
	return &detectprefilterpkt{}, nil
}

func (d *detectprefilterpkt) Build() error {
	if d.tempinspect != nil {
		err := d.tempinspect.Build()
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *detectprefilterpkt) loadContents(pos gonids.DataPos, m mpm.Content) error {
	d.tempcount++
	if d.tempinspect == nil {
		new, err := newInspectPkt(pos)
		if err != nil {
			return err
		}
		d.tempinspect = new
	}
	err := d.tempinspect.Add(pos, m)
	if err != nil {
		return err
	}
	return nil
}

func (d *detectprefilterpkt) Matches(packet *Packet) []int {
	if d.inspect != nil {
		return d.inspect.Matches(packet, d.count)
	}
	return nil
}

func (d *detectprefilterpkt) Apply() {
	defer func() {
		d.tempinspect = nil
		d.tempcount = 0
	}()
	d.inspect = d.tempinspect
	d.count = d.tempcount
}

func (d *detectprefilterpkt) LoadSignature(s *signature) error {
	if s.sigtype == sig_type_pkt {
		return d.loadContents(s.detect.DataPos(), *s.detect.PrefilterData())
	}
	return nil
}
