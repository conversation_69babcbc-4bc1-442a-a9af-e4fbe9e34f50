package detect

type InfoMatched struct {
	Id       int
	Action   string
	Protocol string
	Srcip    string
	SrcPort  uint16
	Destip   string
	DestPort uint16
	Message  string
}

func convertToInfoMatched(packet Packet, sig *signature) InfoMatched {
	info := InfoMatched{
		Id:       sig.sid,
		Action:   sig.nidRule.Action,
		Protocol: sig.nidRule.Protocol,
		Srcip:    packet.srcip,
		SrcPort:  packet.srcport,
		Destip:   packet.dstip,
		DestPort: packet.dstport,
		Message:  sig.nidRule.Description,
	}
	return info
}
