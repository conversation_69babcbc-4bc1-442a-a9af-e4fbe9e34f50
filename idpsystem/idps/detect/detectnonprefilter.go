package detect

type detectnonprefilter struct {
	tempsignatures     []*signature
	instancesignatures []*signature
}

func newdetectnonprefilter() *detectnonprefilter {
	return &detectnonprefilter{tempsignatures: newsignatures(defaultsize)}
}

func (d *detectnonprefilter) LoadSignature(s *signature) error {
	d.tempsignatures = append(d.tempsignatures, s)
	return nil
}

func (d *detectnonprefilter) Build() error {

	return nil
}

func (d *detectnonprefilter) Apply() error {
	defer func() {
		d.tempsignatures = newsignatures(defaultsize)
	}()
	d.instancesignatures = d.tempsignatures
	return nil
}
