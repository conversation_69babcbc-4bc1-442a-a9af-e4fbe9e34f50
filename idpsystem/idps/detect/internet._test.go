package detect

import (
	"errors"
	"fmt"
	"strconv"
	"testing"
)

func TestInternet(t *testing.T) {

	var tests = []struct {
		sourceip   []string
		soruceport []string
		destip     []string
		destport   []string
		testvalue  []string
		expect     bool
	}{
		{
			sourceip:   []string{"[************,************]"},
			soruceport: []string{"20"},
			destip:     []string{"************"},
			destport:   []string{"60"},
			testvalue:  []string{"************", "20", "->", "************", "60"},
			expect:     true,
		},
		{
			sourceip:   []string{"************"},
			soruceport: []string{"20"},
			destip:     []string{"************"},
			destport:   []string{"60"},
			testvalue:  []string{"************", "20", "->", "************", "60"},
			expect:     false,
		},
		{
			sourceip:   []string{"$EXTERNAL_NET"},
			soruceport: []string{"20"},
			destip:     []string{"************"},
			destport:   []string{"60"},
			testvalue:  []string{"***********", "20", "->", "************", "60"},
			expect:     true,
		},
	}
	for index, v := range tests {
		t.Run(fmt.Sprintf("TestManage:%v", index), func(t *testing.T) {

			i, err := NewInternet(v.sourceip, v.soruceport, v.destip, v.destport)
			if err != nil {
				t.Fatal(err)
			}
			//src port
			srcp, err := strconv.Atoi(v.testvalue[1])
			if err != nil {
				t.Fatal(err)
			}
			//dst port
			dp, err := strconv.Atoi(v.testvalue[4])
			if err != nil {
				t.Fatal(err)
			}
			r := i.verifyIP(v.testvalue[0], v.testvalue[3], AFInet) && i.verifyPort(uint16(srcp), uint16(dp))
			if r != v.expect {
				t.Fatal(errors.New("expect error"))
			}
		})
	}

}

func TestIPInfo(t *testing.T) {
	var tests = []struct {
		ip     string
		expect string
	}{
		{
			ip:     "************",
			expect: "************",
		}, {
			ip:     "***********/24",
			expect: "***********/24",
		},
		{
			ip:     "2001:02c0:0100:0427:0053:cafe:02ac:0001",
			expect: "2001:2c0:100:427:53:cafe:2ac:1",
		},
		{
			ip:     "2001:02c0:0100:0427:0053:cafe:02ac:0001",
			expect: "2001:2c0:100:427:53:cafe:2ac:1",
		},
		{
			ip:     "0.0.0.0/0",
			expect: "0.0.0.0/0",
		},
		{
			ip:     "::/0",
			expect: "::/0",
		},
	}
	for index, v := range tests {
		t.Run(fmt.Sprintf("TestIPInfo:%v", index+1), func(t *testing.T) {
			info := newIPInfo()
			err := info.AddInfo(v.ip, false)
			if err != nil {
				t.Fatal(err)
			}
			if info.String() != v.expect {
				t.Fatal(fmt.Errorf("expect:%v,actual:%v", v.expect, info.String()))
			}

		})
	}

}
