package detect

import (
	"fmt"
	"net"
	"testing"

	"github.com/google/gonids"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
)

type testNetLinker struct {
	value bool
}

func (t *testNetLinker) Pass() {
	t.value = true
}

func (t *testNetLinker) Drop() {
	t.value = true
}

func (t *testNetLinker) Alert(InfoMatched) {
	t.value = true
}

func (t *testNetLinker) Default() {

}

func createTestIPv4Packet(srcip string, srcport uint16, dstip string, dstport uint16, payload []byte) ([]byte, error) {
	ipLayer := &layers.IPv4{
		SrcIP:    net.ParseIP(srcip),
		DstIP:    net.ParseIP(dstip),
		Version:  4,
		Protocol: layers.IPProtocolTCP,
	}

	tcpLayer := &layers.TCP{
		SrcPort: layers.TCPPort(srcport),
		DstPort: layers.TCPPort(dstport),
	}

	tcpLayer.SetNetworkLayerForChecksum(ipLayer)

	buffer := gopacket.NewSerializeBuffer()
	options := gopacket.SerializeOptions{ComputeChecksums: true, FixLengths: true}

	err := gopacket.SerializeLayers(buffer, options, ipLayer, tcpLayer, gopacket.Payload(payload))
	if err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

func TestDetectContent(t *testing.T) {
	var tests = []struct {
		rule     []string
		expected bool
		packet   []byte
		ip       []string
		port     []uint16
		link     testNetLinker
	}{
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";content:"abc";content:"66";sid:1;)`},
			expected: true,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
		{
			rule:     []string{`alert ip any any -> any any (msg:"test";content:"abc";content:"9999";sid:1;)`},
			expected: false,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},

		{
			rule:     []string{`alert ip any any -> any any (msg:"test";content:!"abc";content:!"9999";sid:1;)`},
			expected: false,
			packet:   []byte("88546abcsdfdssdf666dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},

		{
			rule: []string{`alert ip any any -> any any (msg:"test";content:"abc";content:"66";sid:1;)`,
				`alert ip any any -> any any (msg:"test";content:"test";content:"sdf";sid:2;)`},
			expected: true,
			packet:   []byte("8855abctest644sdf65dfg"),
			ip:       []string{"127.0.0.1", "127.0.0.1"},
			port:     []uint16{10, 20},
			link:     testNetLinker{},
		},
	}

	for index, test := range tests {
		t.Run(fmt.Sprintf("TestDetectContent:%v", index), func(t *testing.T) {
			d, err := NewDetectEngineCtx()
			if err != nil {
				t.Fatal(err)
			}
			for _, ru := range test.rule {
				r, err := gonids.ParseRule(ru)
				if err != nil {
					t.Fatal(err)
				}
				err = d.LoadGoNidRule(*r)
				if err != nil {
					t.Fatal(err)
				}
			}
			err = d.Build()
			if err != nil {
				t.Fatal(err)
			}
			err = d.Apply()
			if err != nil {
				t.Fatal(err)
			}
			p, err := createTestIPv4Packet(test.ip[0], test.port[0], test.ip[1], test.port[1], test.packet)
			if err != nil {
				t.Fatal(err)
			}
			d.DetectPacket(&test.link, p)
			if test.link.value != test.expected {
				t.Fatal("not found packet")
			}
		})
	}
}
