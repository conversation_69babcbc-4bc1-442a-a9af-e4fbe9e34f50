// unused
package idpsystem

import (
	"encoding/json"
	"fmt"
	"io"
	"mnms"
	"net/http"
	"net/url"

	"github.com/qeof/q"
)

/*
func IdpSystemRouter(r chi.Router) http.Handler {
	r.Use(verfiyIdpSystem())
	//r.Get("/rules", HandleIdpsRules)
	//	r.Post("/rules", HandleIdpsRules)
	r.Post("/rules/import", HandleImportIdpsFiles)
	r.Get("/rules/command", HandleIdpsBehavior)
	r.Post("/rules/command", HandleIdpsBehavior)
	r.Get("/event", HandleIdpsEvent)
	r.Get("/export", HandleIdpsExport)
	return r
}*/

func verfiyIdpSystem() func(http.Handler) http.Handler {
	checkIdps := func(h http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			err := InitIdpSystem()
			if err != nil {
				q.Q(err)
				mnms.RespondWithError(w, err)
				return
			}
			h.ServeHTTP(w, r)
		}
		return http.HandlerFunc(fn)
	}
	return checkIdps
}

/*
HandleIdpsRules handles the rule of idps,update or get

POST /api/v1/idps/rules

	update rule
	[
		{"command":"update","id":1761608725,"name":"selftest_rules","value":"alert icmp $HOME_NET any -\u003e $HOME_NET any (msg:\"ET selftest\";sid:123456;)havechanged............."}
	]
	enable rule
	[
		{"command":"enable","id":1761608725,"name":"selftest_rules",sid:789}
	]
	disbale rule
	[
		{"command":"disable","id":1761608725,"name":"selftest_rules",sid:789}
	]

GET /api/v1/idps/rules

	retrieve all categories(file name)
	resp:
	[
		{"id": 2382365717,"created time":"2023-08-24T14:28:16+08:00","name":"emerging-dns"}
	]

GET /api/v1/idps/rules?id=2382365717&categories=dns-events

	retrieve all rules of categories(file name)
	resp:
	[{"enable":true,"sid":123456,"value":"drop icmpV4 $EXTERNAL_NET any -\u003e $HOME_NET any (msg:\"icmpv4 selftest\";sid:123456;)"}]

GET /api/v1/idps/rules?id=2382365717&categories=dns-events&sid=123456

	retrieve one rule with sid of categories(file name)
	resp:
	{"enable":true,"sid":123456,"value":"drop icmpV4 $EXTERNAL_NET any -\u003e $HOME_NET any (msg:\"icmpv4 selftest\";sid:123456;)"}
*/
/*
func HandleIdpsRules(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		rules := []RuleAction{}
		err = json.Unmarshal(body, &rules)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		idpsys, err := getIdpsystem()
		if err != nil {
			RespondWithError(w, err)
			return
		}

		for _, rule := range rules {
			switch rule.Command {
			case "enable":
				err := idpsys.EnableRule(rule.Name, rule.Sid)
				if err != nil {
					RespondWithError(w, err)
					return
				}
			case "disable":
				err := idpsys.DisableRule(rule.Name, rule.Sid)
				if err != nil {
					RespondWithError(w, err)
					return
				}
			case "update":
				err := idpsys.UpdateRule(rule.Name, rule.Value)
				if err != nil {
					RespondWithError(w, err)
					return
				}
			default:
				RespondWithError(w, fmt.Errorf("not support command:%v", rule.Command))
				return
			}
		}

		err = idpsys.ApplyRules()
		if err != nil {
			RespondWithError(w, err)
			return
		}
		cmd := Command{}
		cmd.Command = "ok"
		jsonBytes, err := json.Marshal(&cmd)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
		return
	}
	idpsys, err := getIdpsystem()
	if err != nil {
		RespondWithError(w, err)
		return
	}
	c := r.URL.Query().Get("categories")
	sid := r.URL.Query().Get("sid")
	switch {
	case len(c) == 0 && len(sid) == 0:
		cs, err := idpsys.GetAllCategory()
		if err != nil {
			RespondWithError(w, err)
			return
		}
		jsonBytes, err := json.Marshal(&cs)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
	case len(c) != 0 && len(sid) == 0:
		rules, err := idpsys.GetRules(Category(c))
		if err != nil {
			RespondWithError(w, err)
			return
		}
		jsonBytes, err := json.Marshal(&rules)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
	case len(c) != 0 && len(sid) != 0:
		sid, err := strconv.ParseUint(sid, 10, 32)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		rules, err := idpsys.GetDefaultRule(Category(c), uint32(sid))
		if err != nil {
			RespondWithError(w, err)
			return
		}
		jsonBytes, err := json.Marshal(&rules)
		if err != nil {
			RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
	default:
		RespondWithError(w, errors.New("error parameters"))
		return
	}
}
*/
/*
HandleImportIdpsFiles import files fron url into idps

POST /api/v1/idps/rules/import

	client sends a array files with the following format:
	["url1","url2","path1"]
*/
func HandleImportIdpsFiles(w http.ResponseWriter, r *http.Request) {

	body, err := io.ReadAll(r.Body)
	if err != nil {
		mnms.RespondWithError(w, err)
		return
	}
	paths := []string{}
	err = json.Unmarshal(body, &paths)
	if err != nil {
		mnms.RespondWithError(w, err)
		return
	}

	idpsys, err := getIdpsystem()
	if err != nil {
		mnms.RespondWithError(w, err)
		return
	}
	for _, u := range paths {
		isurl := false
		ur, _ := url.ParseRequestURI(u)
		if ur != nil && ur.Scheme != "" && ur.Host != "" {
			isurl = true
		}
		switch isurl {
		//parse ulr file
		case true:
			err := idpsys.ImportFilesFromUrl([]string{u})
			if err != nil {
				mnms.RespondWithError(w, err)
				return
			}
			//parse  common file
		default:
			err := idpsys.ImportFiles([]string{u})
			if err != nil {
				mnms.RespondWithError(w, err)
			}
		}

	}

	err = idpsys.ApplyRules()
	if err != nil {
		mnms.RespondWithError(w, err)
		return
	}
	_, err = w.Write(body)
	if err != nil {
		q.Q(err)
	}

}

/*
HandleIdpsBehavior get and set status or behavior of idps

POST /api/v1/idps/rules/command

	client sends a json with the following format:
	start:
	{"command":"start"}
	close:
	{"command":"close"}

GET /api/v1/idps/rules/command?cmd=status

	return status of idps
	true:running
	false:not running
*/
func HandleIdpsBehavior(w http.ResponseWriter, r *http.Request) {
	if r.Method == "POST" {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			mnms.RespondWithError(w, err)
			return
		}
		cmd := Command{}
		err = json.Unmarshal(body, &cmd)
		if err != nil {
			mnms.RespondWithError(w, err)
			return
		}
		idpsys, err := getIdpsystem()
		if err != nil {
			mnms.RespondWithError(w, err)
			return
		}
		switch cmd.Command {
		case "start":
			err := idpsys.Start()
			if err != nil {
				mnms.RespondWithError(w, err)
				return
			}
		case "close":
			err := idpsys.Close()
			if err != nil {
				mnms.RespondWithError(w, err)
				return
			}
		default:
			mnms.RespondWithError(w, fmt.Errorf("not support command:%v", cmd))
			return
		}
		_, err = w.Write(body)
		if err != nil {
			q.Q(err)
		}
		return
	}

	idpsys, err := getIdpsystem()
	if err != nil {
		mnms.RespondWithError(w, err)
		return
	}
	cmd := r.URL.Query().Get("cmd")
	switch cmd {
	case "status":
		b := idpsys.Running()
		jsonBytes, err := json.Marshal(&b)
		if err != nil {
			mnms.RespondWithError(w, err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			q.Q(err)
		}
	default:
		mnms.RespondWithError(w, fmt.Errorf("not support command:%v", cmd))
		return
	}
}

// HandleIdpsEvent get event message of idps
//
// Get /api/v1/idps/event?cmd&start&end
//
// resp:[{"id":12388,"imestamp":"2023-08-28T14:41:48+08:00","type":"alert","inInterface":"\\Device\\NPF_{3526FD89-5440-41E7-B415-10B194D41F1D}","srcip":"*************","srcPort":443,"destip":"************","destPort":60466,"protocol":"TCP","description":"\"tcp message selftest\""}]
//
// Example parameter: cmd=accept or cmd=alert&start=2023/10/19 14:30:00&end=2023/10/19 14:32:00 or cmd=drop&start=2023/10/19 14:30:00&end=2023/10/19 14:32:00
//
// Get /api/v1/idps/event
//
// resp:["modbus","tls","dns", "ntp",  "dhcp", "sip","icmp","http", "ftp","http2","imap","smb","smtp","snmp","ssh","alert", "drop"]
/*
func HandleIdpsEvent(w http.ResponseWriter, r *http.Request) {
	event := r.URL.Query().Get("cmd")
	start := r.URL.Query().Get("start")
	end := r.URL.Query().Get("end")
	if len(event) == 0 {
		events := idps.GetProtocolSupported()
		jsonBytes, err := json.Marshal(events)
		if err != nil {
			mnms.RespondWithError(w, err)
			q.Q(err)
			return
		}
		_, err = w.Write(jsonBytes)
		if err != nil {
			mnms.RespondWithError(w, err)
			q.Q(err)
			return
		}
		return
	}
	idpsys, err := getIdpsystem()
	if err != nil {
		mnms.RespondWithError(w, err)
		q.Q(err)
		return
	}
	log, err := idpsys.GetIdpsRecord(event)
	if err != nil {
		mnms.RespondWithError(w, err)
		q.Q(err)
		return
	}

	dir := filepath.Dir(log.name)
	files, err := ioutil.ReadDir(dir)
	if err != nil {
		mnms.RespondWithError(w, err)
		return
	}

	sort.Slice(files, func(i, j int) bool {
		return files[i].ModTime().After(files[j].ModTime())
	})
	messages := []idps.EventMessage{}
	for _, file := range files {
		f, err := os.Open(filepath.Join(dir, file.Name()))
		if err != nil {
			q.Q(err)
			continue
		}
		scanner := backscanner.New(f, int(file.Size()))
		for {
			line, _, err := scanner.LineBytes()
			if err != nil {
				if err == io.EOF {
					q.Q(file.Name(), "found to EOF")
				} else {
					q.Q("err:", err)
				}
				break
			}
			if len(line) == 0 {
				continue
			}

			m := idps.EventMessage{}
			err = json.Unmarshal(line, &m)
			if err != nil {
				if err != nil {
					q.Q(err)
				}
				continue
			}
			date, err := time.Parse(time.RFC3339, m.Timestamp)
			if err != nil {
				continue
			}
			r, err := compareTime(start, end, date.Format(timeformat))
			if err != nil {
				messages = append(messages, m)
				continue
			}
			if r {
				messages = append(messages, m)
			}
		}
		f.Close()
	}
	jsonBytes, err := json.Marshal(&messages)
	if err != nil {
		_, err = w.Write([]byte(""))
		if err != nil {
			q.Q(err)
		}
		return
	}
	_, err = w.Write(jsonBytes)
	if err != nil {
		q.Q(err)
	}
}

// HandleIdpsExport export rule file
func HandleIdpsExport(w http.ResponseWriter, r *http.Request) {
	idpsys, err := getIdpsystem()
	if err != nil {
		mnms.RespondWithError(w, err)
		return
	}
	if len(idpsys.getCategorys()) == 0 {
		mnms.RespondWithError(w, errors.New("no rule existed"))
		return
	}
	err = idpsys.CreateFile(idpsOutFile)
	if err != nil {
		mnms.RespondWithError(w, err)
		return
	}
	defer os.Remove(idpsOutFile)
	w.Header().Set("Content-Disposition", "attachment; filename="+idpsOutFile)
	http.ServeFile(w, r, idpsOutFile)

}

// compareTime compare time size with start time and end time
func compareTime(start, end, target string) (bool, error) {
	s, err := time.Parse(timeformat, start)
	if err != nil {
		return false, err
	}
	e, err := time.Parse(timeformat, end)
	if err != nil {
		return false, err
	}
	if e.Before(s) {
		return false, fmt.Errorf("end time:%v should than start time:%v ", end, s)
	}
	t, err := time.Parse(timeformat, target)
	if err != nil {
		return false, err
	}

	if (s.Before(t) || s.Equal(t)) && (e.After(t) || e.Equal(t)) {
		return true, nil
	}

	return false, nil
}
*/
