package snmp

import (
	"net"

	snmpvalue "mnms/simulator/snmp/bindvalue"

	"mnms/simulator/snmp/mib/atmib"
	"mnms/simulator/snmp/mib/atopmib"
	dot1dbridgemib "mnms/simulator/snmp/mib/dot1dBridgemib"
	"mnms/simulator/snmp/mib/icmpmib"
	"mnms/simulator/snmp/mib/ifmib"
	"mnms/simulator/snmp/mib/interfacesmib"
	"mnms/simulator/snmp/mib/ipmib"
	"mnms/simulator/snmp/mib/rmonmib"
	"mnms/simulator/snmp/mib/snmpmib"
	"mnms/simulator/snmp/mib/systemmib"
	"mnms/simulator/snmp/mib/tcpmib"
	"mnms/simulator/snmp/mib/transmissionmib"
	"mnms/simulator/snmp/mib/udpmib"

	"github.com/slayercat/GoSNMPServer"
	"github.com/slayercat/GoSNMPServer/mibImps/dismanEventMib"
	"github.com/slayercat/GoSNMPServer/mibImps/ucdMib"
)

const port = "161"

type Snmp struct {
	agent  GoSNMPServer.MasterAgent
	server *GoSNMPServer.SNMPServer
	data   *snmpvalue.Value
}

func NewSnmp(community []string, data *snmpvalue.Value) *Snmp {
	toRet := []*GoSNMPServer.PDUValueControlItem{}
	toRet = append(toRet, ucdMib.All()...) //dismanEventMib
	toRet = append(toRet, dismanEventMib.All()...)
	toRet = append(toRet, systemmib.All(data)...)
	toRet = append(toRet, interfacesmib.All(data)...)
	toRet = append(toRet, atmib.All(data)...)
	toRet = append(toRet, ipmib.All(data)...)
	toRet = append(toRet, icmpmib.All()...)
	toRet = append(toRet, tcpmib.All()...)
	toRet = append(toRet, udpmib.All()...)
	toRet = append(toRet, transmissionmib.All(data)...)
	toRet = append(toRet, snmpmib.All()...)
	toRet = append(toRet, rmonmib.All(data)...)
	toRet = append(toRet, dot1dbridgemib.All(data)...)
	toRet = append(toRet, ifmib.All(data)...)
	toRet = append(toRet, atopmib.All(data)...)
	master := GoSNMPServer.MasterAgent{
		//Logger: GoSNMPServer.NewDefaultLogger(),
		SecurityConfig: GoSNMPServer.SecurityConfig{
			AuthoritativeEngineBoots: 1,
		},
		SubAgents: []*GoSNMPServer.SubAgent{
			{
				CommunityIDs:        community,
				OIDs:                toRet,
				UserErrorMarkPacket: true,
			},
		},
	}
	snmp := &Snmp{agent: master, data: data}

	return snmp
}

func (s *Snmp) Run(ip string) error {
	addr := net.JoinHostPort(ip, port)
	s.server = GoSNMPServer.NewSNMPServer(s.agent)
	err := s.server.ListenUDP("udp", addr)
	if err != nil {
		return err
	}

	return s.server.ServeForever()
}

func (s *Snmp) Shutdown() {
	s.server.Shutdown()
}

func (s *Snmp) GetData() *snmpvalue.Value {
	return s.data
}
