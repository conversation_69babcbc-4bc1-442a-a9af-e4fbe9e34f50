# monocypher

the monocypher.c and monocypher.h are used by C code in the agent client.
agent.go uses crypto_lock and crypto_unlock in monocypher.c via CGO.

main.c is just an example.

## Sample Encryption tool

main.c is just an example that shows you how to use monocypher

This encryption tool use `monocypher` to encrypt and decrypt. 

## Usage

### Encrypt and decrypt
For encryption, please use the `crypt_lock` function. To decrypt, please use the `crypt_unlock` function. 
Please note that the same values ​​for `key`, `nonce` and `mac` should be used for encryption and decryption.

### Peer key
To use peer key you need two different pricvate keys. If their names are privateKeyA and privateKeyB.
1. Please use `crypto_x25519_public_key` to create public key. If their names are publicKeyA and publicKeyB.
2. Please use `crypto_x25519` to create shared key. The privateKeyA and publicKeyB will create sharedKey1. The privateKeyB and publicKeyA will create sharedKey2. The sharedKey1 and sharedKey2 are same value.

## Example
This example will encrypt string and decrypt string.
```shell
# build code
cd ~/.../mnms/agent/
make
# run example 
./monocypher
```
