package mnms

import (
	"fmt"
	"regexp"
	"testing"
	"time"

	"github.com/awcullen/opcua/client"
	"github.com/awcullen/opcua/ua"
	"github.com/pkg/errors"
	"github.com/qeof/q"
)

func TestOpcFindServers(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()

	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	r, err := opacuclient.FindServers(&ua.FindServersRequest{EndpointURL: endpointURL})
	if err != nil {
		t.Fatal(err)
	}
	q.Q(r)
}

func TestOpcGetEndpoints(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	r, err := opacuclient.GetEndpoints(&ua.GetEndpointsRequest{EndpointURL: endpointURL})
	if err != nil {
		t.Fatal(err)
	}
	q.Q(r)
}

func TestOpcWrite(t *testing.T) {
	fv := 42.0
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	id := ua.ParseNodeID("i=1002")
	value := ua.NewDataValue(float32(fv), 0, time.Time{}, 0, time.Time{}, 0)
	req := &ua.WriteRequest{
		NodesToWrite: []ua.WriteValue{
			{
				NodeID:      id,
				AttributeID: ua.AttributeIDValue,
				Value:       value,
			},
		},
	}
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()

	res, err := opacuclient.WriteNodeID(req) //client.WithClientCertificateFile("./pki/client.crt", "./pki/client.key"),

	if err != nil {
		t.Fatal(err)
	}
	if res.Results[0].IsBad() {
		t.Error(errors.Wrap(res.Results[0], "Error Write"))
		return
	}
	readreq := &ua.ReadRequest{
		NodesToRead: []ua.ReadValueID{
			{NodeID: id, AttributeID: ua.AttributeIDValue},
		},
	}

	r, err := opacuclient.ReadNodeID(readreq)
	if err != nil {
		t.Fatal(err)
	}

	if r.Results[0].StatusCode.IsBad() {
		t.Error(errors.Wrap(r.Results[0].StatusCode, "Error reading"))
		return
	}
	for _, result := range r.Results {
		if result.StatusCode.IsGood() {
			if value.Value == result.Value {
				q.Q(result.Value)
			} else {
				t.Fatalf("expect%v,actual:%v", value.Value, result.Value)
			}

		} else {
			t.Error(errors.Wrap(result.StatusCode, "Error reading node"))
		}
	}

}

func TestOpcNegativeWrite(t *testing.T) {
	fv := 42.0
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	id := ua.ParseNodeID("i=10025")
	value := ua.NewDataValue(float32(fv), 0, time.Time{}, 0, time.Time{}, 0)
	req := &ua.WriteRequest{
		NodesToWrite: []ua.WriteValue{
			{
				NodeID:      id,
				AttributeID: ua.AttributeIDValue,
				Value:       value,
			},
		},
	}
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()

	res, err := opacuclient.WriteNodeID(req) //client.WithClientCertificateFile("./pki/client.crt", "./pki/client.key"),

	if err != nil {
		t.Fatal(err)
	}
	if !res.Results[0].IsBad() {
		t.Error("should error write beacause of wrong node ip")
		return
	}

}

func TestOpcBrowse(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()

	req := &ua.BrowseRequest{
		NodesToBrowse: []ua.BrowseDescription{
			{
				NodeID:          ua.ParseNodeID("i=85"),
				BrowseDirection: ua.BrowseDirectionForward,
				ReferenceTypeID: ua.ReferenceTypeIDHierarchicalReferences,
				IncludeSubtypes: true,
				ResultMask:      uint32(ua.BrowseResultMaskAll),
			},
		},
	}
	res, err := opacuclient.Browse(req)
	if err != nil {
		t.Fatal(err)
	}
	if res.Results[0].StatusCode.IsBad() {
		t.Error(errors.Wrap(res.Results[0].StatusCode, "Error browsing"))
		return
	}
	for _, r := range res.Results[0].References {
		q.Q(r)
	}
}

func TestOpcNegativeBrowse(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()

	req := &ua.BrowseRequest{
		NodesToBrowse: []ua.BrowseDescription{
			{
				NodeID:          ua.ParseNodeID("i=99"),
				BrowseDirection: ua.BrowseDirectionForward,
				ReferenceTypeID: ua.ReferenceTypeIDHierarchicalReferences,
				IncludeSubtypes: true,
				ResultMask:      uint32(ua.BrowseResultMaskAll),
			},
		},
	}
	res, err := opacuclient.Browse(req)
	if err != nil {
		t.Fatal(err)
	}
	if res.Results[0].StatusCode.IsBad() {
		t.Error(errors.Wrap(res.Results[0].StatusCode, "Error browsing"))
		return
	}
	if len(res.Results[0].References) != 0 {
		t.Error("should nothing")
	}
}

func TestOpcReadServerStatus(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	req := &ua.ReadRequest{
		NodesToRead: []ua.ReadValueID{
			{NodeID: ua.VariableIDServerServerStatus, AttributeID: ua.AttributeIDValue},
		},
	}
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()
	res, err := opacuclient.ReadNodeID(req)
	if err != nil {
		t.Fatal(err)
	}
	if res.Results[0].StatusCode.IsBad() {
		t.Error(errors.Wrap(res.Results[0].StatusCode, "Error reading ServerStatus"))
		return
	}
	status, ok := res.Results[0].Value.(ua.ServerStatusDataType)
	if !ok {
		t.Error(errors.New("Error decoding ServerStatusDataType"))
		return
	}
	q.Q("Server status:")
	q.Q(status.BuildInfo.ProductName)
	q.Q(status.BuildInfo.SoftwareVersion)
	q.Q(status.BuildInfo.ManufacturerName)
	q.Q(status.State)
	q.Q(status.CurrentTime)
}

func TestOpcRead(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	id := ua.ParseNodeID("i=1002")
	req := &ua.ReadRequest{
		NodesToRead: []ua.ReadValueID{
			{NodeID: id, AttributeID: ua.AttributeIDValue},
		},
	}
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()
	res, err := opacuclient.ReadNodeID(req)
	if err != nil {
		t.Fatal(err)
	}
	if res.Results[0].StatusCode.IsBad() {
		t.Error(errors.Wrap(res.Results[0].StatusCode, "Error reading"))
		return
	}
	for _, result := range res.Results {
		if result.StatusCode.IsGood() {
			q.Q(result.Value)
		} else {
			t.Error(errors.Wrap(result.StatusCode, "Error reading node"))
		}
	}

}

func TestOpcNegativeRead(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	id := ua.ParseNodeID("i=1099")
	req := &ua.ReadRequest{
		NodesToRead: []ua.ReadValueID{
			{NodeID: id, AttributeID: ua.AttributeIDValue},
		},
	}
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()
	res, err := opacuclient.ReadNodeID(req)
	if err != nil {
		t.Fatal(err)
	}
	if !res.Results[0].StatusCode.IsBad() {
		t.Error("should reading error,bacause wrong node id")
		return
	}

}

func TestOpcReadAttributes(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	id := ua.ParseNodeID("i=1002")
	req := &ua.ReadRequest{
		NodesToRead: []ua.ReadValueID{
			{NodeID: id, AttributeID: ua.AttributeIDNodeID},
			{NodeID: id, AttributeID: ua.AttributeIDNodeClass},
			{NodeID: id, AttributeID: ua.AttributeIDBrowseName},
			{NodeID: id, AttributeID: ua.AttributeIDDisplayName},
			{NodeID: id, AttributeID: ua.AttributeIDDescription},
			{NodeID: id, AttributeID: ua.AttributeIDValue},
			{NodeID: id, AttributeID: ua.AttributeIDRolePermissions},
		},
	}
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()
	res, err := opacuclient.ReadNodeID(req)
	if err != nil {
		t.Fatal(err)
	}
	for i, result := range res.Results {
		if result.StatusCode.IsGood() {
			q.Q(req.NodesToRead[i].AttributeID, result.Value)
		} else {
			q.Q("unsupport", req.NodesToRead[i].AttributeID, result.StatusCode)
		}
	}

}

func TestOpcNegativeReadAttributes(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	id := ua.ParseNodeID("i=10022")
	req := &ua.ReadRequest{
		NodesToRead: []ua.ReadValueID{
			{NodeID: id, AttributeID: ua.AttributeIDNodeID},
			{NodeID: id, AttributeID: ua.AttributeIDNodeClass},
			{NodeID: id, AttributeID: ua.AttributeIDBrowseName},
			{NodeID: id, AttributeID: ua.AttributeIDDisplayName},
			{NodeID: id, AttributeID: ua.AttributeIDDescription},
			{NodeID: id, AttributeID: ua.AttributeIDValue},
			{NodeID: id, AttributeID: ua.AttributeIDRolePermissions},
		},
	}
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()
	res, err := opacuclient.ReadNodeID(req)
	if err != nil {
		t.Fatal(err)
	}
	for i, result := range res.Results {
		if result.StatusCode.IsGood() {
			t.Logf("%d: %v", req.NodesToRead[i].AttributeID, result.Value)
			q.Q(req.NodesToRead[i].AttributeID, result.Value)
			t.Errorf("should unsupport, because of wrong node id")
		} else {
			q.Q("unsupport", req.NodesToRead[i].AttributeID, result.StatusCode)
		}
	}

}

func TestSubscribe(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()
	req := &ua.CreateSubscriptionRequest{
		RequestedPublishingInterval: 1000.0,
		RequestedMaxKeepAliveCount:  30,
		RequestedLifetimeCount:      30 * 3,
		PublishingEnabled:           true,
	}
	res, err := opacuclient.CreateSubscription(req)
	if err != nil {
		t.Error(errors.Wrap(err, "Error creating subscription"))
		return
	}
	id := ua.ParseNodeID("i=1002")
	req2 := &ua.CreateMonitoredItemsRequest{
		SubscriptionID:     res.SubscriptionID,
		TimestampsToReturn: ua.TimestampsToReturnBoth,
		ItemsToCreate: []ua.MonitoredItemCreateRequest{
			{
				ItemToMonitor: ua.ReadValueID{
					AttributeID: ua.AttributeIDValue,
					NodeID:      id,
				},
				MonitoringMode: ua.MonitoringModeReporting,
				RequestedParameters: ua.MonitoringParameters{
					ClientHandle: 42, QueueSize: 1, DiscardOldest: true, SamplingInterval: 500.0,
				},
			},
		},
	}
	res2, err := opacuclient.CreateMonitoredItems(req2)
	if err != nil {
		t.Error(errors.Wrap(err, "Error creating item"))
	}
	_ = res2
	req3 := &ua.PublishRequest{
		RequestHeader:                ua.RequestHeader{TimeoutHint: 60000},
		SubscriptionAcknowledgements: []ua.SubscriptionAcknowledgement{},
	}
	numChanges := 0
	for numChanges < 3 {
		res3, err := opacuclient.Publish(req3)
		if err != nil {
			t.Error(errors.Wrap(err, "Error publishing"))
			break
		}

		// loop thru all the notifications.
		for _, data := range res3.NotificationMessage.NotificationData {
			switch body := data.(type) {
			case ua.DataChangeNotification:
				for _, z := range body.MonitoredItems {
					if z.ClientHandle == 42 {
						q.Q("value:", z.Value.Value)
						numChanges++
					}
				}
			}
			err := Opcwrite(id, 50+numChanges)
			if err != nil {
				t.Fatal(err)
			}
		}

	}

}

func Opcwrite(id ua.NodeID, nmuber int) error {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	opacuclient := NewOpcuaClient()
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		return err
	}
	defer opacuclient.Close()
	value := ua.NewDataValue(float32(nmuber), 0, time.Time{}, 0, time.Time{}, 0)
	req := &ua.WriteRequest{
		NodesToWrite: []ua.WriteValue{
			{
				NodeID:      id,
				AttributeID: ua.AttributeIDValue,
				Value:       value,
			},
		},
	}
	_, err = opacuclient.WriteNodeID(req)

	if err != nil {
		return err
	}
	return nil
}

func TestOpcReadVariableAttributes(t *testing.T) {
	id := ua.ParseNodeID("i=1002")
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()
	res, err := opacuclient.ReadVariableAttributes(id)
	if err != nil {
		t.Fatal(err)
	}
	q.Q(res)

}

func TestOpcNegativeReadVariableAttributes(t *testing.T) {
	id := ua.ParseNodeID("i=10022")
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify() /*,client.WithUserNameIdentity("test", "test")*/)
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()
	res, err := opacuclient.ReadVariableAttributes(id)
	if err != nil {
		t.Fatal(err)
	}
	if res.Value != nil {
		t.Error("Value should be nil, because of worn node id")
	}
}

func TestOpcBrowseReference(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify())
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()

	v, err := opacuclient.BrowseReference(ua.ParseNodeID("i=85"), ua.BrowseDirectionForward)
	if err != nil {
		t.Fatal(err)
	}
	for _, value := range v {
		q.Q(value)
	}

}

func TestOpcNegativeBrowseReference(t *testing.T) {
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	opacuclient := NewOpcuaClient()
	opacuclient.timeout = time.Second * 20
	err := opacuclient.Connect(endpointURL, client.WithInsecureSkipVerify())
	if err != nil {
		t.Fatal(err)
	}
	defer opacuclient.Close()

	v, err := opacuclient.BrowseReference(ua.ParseNodeID("i=850"), ua.BrowseDirectionForward)
	if err != nil {
		t.Fatal(err)
	}

	if len(v) != 0 {
		t.Error("shouldn't get value,bacause of wrong node id")
	}

}

func opcConnect(url string) error {
	cmd := &CmdInfo{Command: "opcua connect " + url}
	RunCmd(cmd)
	/*urlpath := fmt.Sprintf("http://localhost:%d/api/v1/commands", QC.Port)
	resp, err := PostWithToken(urlpath, token, bytes.NewBuffer([]byte(cmdJson)))
	if err != nil {
		return err
	}
	if resp == nil {
		return fmt.Errorf("error: post resp nil")
	}
	if resp != nil && resp.StatusCode != 200 {
		return fmt.Errorf("error: post status %v", resp.StatusCode)
	}
	// save close, resp should be nil here
	defer resp.Body.Close()*/
	if cmd.Status != "ok" {
		return fmt.Errorf("%v", cmd.Status)
	}
	return nil
}

func TestOpcConnectCmd(t *testing.T) {
	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()

	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	err := opcConnect(endpointURL)
	if err != nil {
		t.Fatal(err)
	}
}

func TestOpcReadCmd(t *testing.T) {
	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	err := opcConnect(endpointURL)
	if err != nil {
		t.Fatal(err)
	}

	cmd := &CmdInfo{Command: "opcua read i=1002"}
	RunCmd(cmd)
	if cmd.Status != "ok" {
		t.Fatalf("%v", cmd.Status)
	}
}

func TestOpcBrowseCmd(t *testing.T) {
	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	err := opcConnect(endpointURL)
	if err != nil {
		t.Fatal(err)
	}
	cmd := &CmdInfo{Command: "opcua browse i=85"}
	RunCmd(cmd)
	if cmd.Status != "ok" {
		t.Fatalf("%v", cmd.Status)
	}
}

func TestOpcSubscribeCmd(t *testing.T) {
	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	err := opcConnect(endpointURL)
	if err != nil {
		t.Fatal(err)
	}
	cmd := &CmdInfo{Command: "opcua sub i=1002"}
	RunCmd(cmd)
	if cmd.Status != "ok" {
		t.Fatalf("%v", cmd.Status)
	}

}

func TestOpcDeleteSubscribeCmd(t *testing.T) {
	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()

	err := opcConnect(endpointURL)
	if err != nil {
		t.Fatal(err)
	}

	cmdsub := &CmdInfo{Command: "opcua sub i=1002"}
	RunCmd(cmdsub)
	if cmdsub.Status != "ok" {
		t.Fatalf("%v", cmdsub.Status)
	}
	number, err := regexp.Compile(`\d`)
	if err != nil {
		t.Fatal(err)
	}
	nums := number.FindAllString(cmdsub.Result, 2)
	if len(nums) != 2 {
		t.Fatalf("%v", "sub id error")
	}

	cmddelete := &CmdInfo{Command: fmt.Sprintf("opcua deletesub %v %v", nums[0], nums[1])}
	RunCmd(cmddelete)
	if cmddelete.Status != "ok" {
		t.Fatalf("%v", cmddelete.Status)
	}

}

func TestOpcCloseCmd(t *testing.T) {
	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	endpointURL := fmt.Sprintf("opc.tcp://localhost:%d", opcuaPort)
	s := NewOpcuaServer(OpcurServerURL)
	s.OpcuaRun()
	defer func() {
		_ = s.OpcuaShutdown()

	}()
	err := opcConnect(endpointURL)
	if err != nil {
		t.Fatal(err)
	}
	cmd := &CmdInfo{Command: "opcua close"}
	RunCmd(cmd)
	if cmd.Status != "ok" {
		t.Fatalf("%v", cmd.Status)
	}

}
