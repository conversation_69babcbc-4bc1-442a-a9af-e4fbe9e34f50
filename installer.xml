<?xml version="1.0" encoding="UTF-8"?>
<project>
  <fullName>${project.shortName}</fullName>
  <vendor>Atop Technologies, Inc.</vendor>
  <disableSplashScreen>1</disableSplashScreen>

  <readyToInstallActionList>
    <runProgram>
        <program>sc</program>
            <programArguments>stop WinDivert</programArguments>
                <ruleList>
                    <platformTest type="windows"/>
                </ruleList>
            <abortOnError>0</abortOnError>
            <showMessageOnError>0</showMessageOnError>
    </runProgram>
  </readyToInstallActionList>


  <enableRollback>0</enableRollback>
     <postInstallationActionList>
        <findFile>
            <baseDirectory>${installdir}</baseDirectory>
            <pattern>npcap-1.72.exe</pattern>
            <variable>npcap</variable>
            <ruleList>
            <platformTest type="windows"/>
            </ruleList>
        </findFile>
        <runProgram>
            <program>${npcap}</program>
            <ruleList>
                <platformTest type="windows"/>
            </ruleList>
            <abortOnError>0</abortOnError>
            <showMessageOnError>0</showMessageOnError>
        </runProgram>
         <findFile>
            <baseDirectory>${installdir}</baseDirectory>
            <pattern>install_lib.sh</pattern>
            <variable>install_lib</variable>
            <ruleList>
            <platformTest type="linux"/>
            </ruleList>
        </findFile>
           <runProgram>
            <program>chmod</program>
            <programArguments>+x ${install_lib}</programArguments>
            <ruleList>
                <platformTest type="linux"/>
            </ruleList>
            <abortOnError>0</abortOnError>
        </runProgram>
       <runProgram>
            <program>/bin/sh</program>
            <programArguments>${install_lib}</programArguments>
            <ruleList>
                <platformTest type="linux"/>
            </ruleList>
            <abortOnError>0</abortOnError>
        </runProgram>
         <showInfo>
<text><![CDATA[
install library....
${program_stdout}]]>
</text>
             <ruleList>
                <platformTest type="linux"/>
            </ruleList>
        </showInfo>
        
    </postInstallationActionList>


    <componentList>
      <component>
            <name>default</name>
            <description>Main Application Files</description>
            <canBeEdited>0</canBeEdited> 
            <selected>1</selected>
            <show>1</show>
            <folderList>
                <folder>
                    <description>Application Files</description>
                    <destination>${installdir}</destination>
                    <name>Application</name>
                    <platforms>all</platforms>
                    <distributionFileList>
                      <distributionDirectory origin="${sourceDir}" includeFiles="**" allowWildcards="1" >
                      </distributionDirectory>
                      <distributionDirectory origin="${releaseDir}/*" includeFiles="*.txt;*.md;*.pdf" allowWildcards="1" >
                      </distributionDirectory>
                    </distributionFileList>      
                </folder>
            </folderList>
        </component>
    </componentList>
    
   <parameterList>
        <directoryParameter>
            <name>installdir</name>
            <description>Installation Directory</description>
            <explanation>Please specify the directory where ${project.fullName} will be installed.</explanation>
            <value></value>
            <default>${platform_install_prefix}/${project.shortName}</default>
            <allowEmptyValue>0</allowEmptyValue>
            <mustBeWritable>1</mustBeWritable>
            <mustExist>0</mustExist>
            <width>40</width>
        </directoryParameter>
    </parameterList>



    <platformOptionsList>
        <platformOptions>
            <platform>windows</platform>
        </platformOptions>
        <platformOptions>
            <platform>linux</platform>
        </platformOptions>

    </platformOptionsList>

    <preUninstallationActionList>
        <runProgram>
            <program>sc</program>
            <programArguments>stop WinDivert</programArguments>
            <ruleList>
                <platformTest type="windows"/>
            </ruleList>
            <abortOnError>0</abortOnError>
            <showMessageOnError>0</showMessageOnError>
        </runProgram>

    </preUninstallationActionList>

    <uninstallerDirectory>${installdir}</uninstallerDirectory>
    <uninstallerName>Uninstall</uninstallerName>
    <askForConfirmationOnUninstall>0</askForConfirmationOnUninstall>
</project>
