package mnms

import (
	"fmt"
	"strings"

	"github.com/qeof/q"
)

type Log struct {
	Kind     string   `json:"kind"`
	Messages []string `json:"messages"`
}

//TODO the log messages are appended to Messages slice which must be dumped
// to files regularly and kept to a limited size.
//TODO need ways to load dumped snapshots to view them, search, via api.

// log kinds: syslog, traps, alerts
//TODO implement syslog server, trap server, alert messaging, integrate q.Q debug logs

func init() {
	QC.Logs = make(map[string]Log)
}

func InsertLogKind(log *Log) {
	QC.Logs[log.Kind] = *log
}

// Configure debug setting.
//
// Usage : debug log off
//
//	turns all debugging messages off, sets debug pattern to ''
//
// Usage: debug log on
//
//	turns all debugging messages on, sets debug pattern to .*
//
// Usage : log pattern [pattern]
//
//		[pattern]     : debug log pattern
//	     sets debug message pattern to [pattern]
//
// Usage : log output [output]
//
//		[output]      : debug output
//	     sets debug log output to [output]
//	     [output] can be stdout, stderr, or filename
//
// Example :
//
//	     debug log on
//			debug log off
//			debug log pattern .*
//			debug log output stderr
//		    debug log clear
func DebugCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	if cmd == "debug log clear" {
		QC.Logs = make(map[string]Log)
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	if cmd == "debug log off" {
		//use q.Q stuff to control logs for debugging
		q.P = ""
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	if cmd == "debug log on" {
		q.P = ".*"
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	if strings.HasPrefix(cmd, "debug log pattern ") {
		ws := strings.Split(cmd, " ")
		if len(ws) < 4 {
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		}
		q.P = ws[3]
		cmdinfo.Status = "ok"
		return cmdinfo
	}
	if strings.HasPrefix(cmd, "debug log output ") {
		ws := strings.Split(cmd, " ")
		if len(ws) < 4 {
			cmdinfo.Status = "error: invalid command"
			return cmdinfo
		}
		q.O = ws[3]
		cmdinfo.Status = "ok"
		//TODO ways to retrieve different log output files
		return cmdinfo
	}
	cmdinfo.Status = "error: invalid command"
	return cmdinfo
}
