# GitHub Copilot Instructions for bbtechhive/mnms

- Use double quotes and tabs for indentation when generating JavaScript code.
- Avoid using large binary files in the repository; provide links or instructions to download them instead.
- When providing examples or explanations, use the context of network management systems and related technologies.
- Always ensure that any generated code includes appropriate tests, as testing is a priority for this project.
- Follow the repository's branch workflow: do not directly check into main branches; provide a PR from a branch and get it reviewed and approved before merging.
- Document all features and bug fixes using GitHub issues and pull requests.

# Additional Context
- The project integrates OpenAI API for intelligent log analysis and anomaly detection.
- User interface support and Intrusion Detection and Prevention System (IDPS) are key features of the project.