package mnms

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/denisbrodbeck/machineid"
	"github.com/fsnotify/fsnotify"
	"github.com/qeof/q"
)

const license_alert_error = "license_alert_error"
const license_alert_clear = "license_alert_clear"
const license_alert_features = "license_alert_features"

// NimblLicense is the license struct
type NimblLicense struct {
	Path                    string `json:"path"`
	Program                 string `json:"program"`
	Version                 string `json:"version"`
	Generated               string `json:"generated"`
	NumClients              int    `json:"numClients"`
	NumDevice               int    `json:"numOfDevice"`
	MachineID               string `json:"machineID"`
	EnabledFeatures         string `json:"enabledFeatures"`
	FeatureAnomalyDetection bool   `json:"featureAnomalyDetection"`
	FeatureIdps             bool   `json:"featureIdps"`
	Timeout                 int    `json:"timeout"`
}

// HasFeatureAnomalyDetection checks if the anomaly detection feature is enabled.
func (l *NimblLicense) HasFeatureAnomalyDetection() bool {
	return l.FeatureAnomalyDetection
}

// HasFeatureIdps checks if the idps feature is enabled.
func (l *NimblLicense) HasFeatureIdps() bool {
	return l.FeatureIdps
}

// GetMaxmumNetworkService returns the maximum number of network services allowed.
func (l *NimblLicense) GetMaxmumNetworkService() int {
	return l.NumClients
}

// GetMaxmunDevice returns the maximum number of devices allowed.
func (l *NimblLicense) GetMaxmunDevice() int {
	return l.NumDevice
}

// SetupFeatures sets up the features.
func (l *NimblLicense) SetupFeatures() error {
	featureEnabled := strings.Split(l.EnabledFeatures, ",")

	// check anomalies in featureEnabled
	for _, feature := range featureEnabled {
		feature = strings.TrimSpace(feature)
		switch feature {
		case "anomalies":
			l.FeatureAnomalyDetection = true
		case "idps":
			l.FeatureIdps = true
		}
	}
	return nil
}

// UnmarshalJSON unmarshals the license json.
func (l *NimblLicense) UnmarshalJSON(data []byte) error {
	type Alias NimblLicense
	aux := &struct {
		*Alias
	}{
		Alias: (*Alias)(l),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	// deal with the features
	err := l.SetupFeatures()
	if err != nil {
		return err
	}
	return nil
}

// CheckLicense checks if the license is valid.
func (l *NimblLicense) checkContent() error {
	// Check machineID exist
	if len(l.MachineID) == 0 {
		return errors.New("machineID is empty")
	}
	// check machineID
	err := CheckMachineID(l.MachineID)
	if err != nil {
		return err
	}
	// check client number
	if NetworkServicesAliveCount() > l.NumClients {
		err := fmt.Errorf("you have license for %v network services but using %v network services", l.NumClients, NetworkServicesAliveCount())

		return err
	}
	// check device number
	if len(QC.DevData)-1 > l.NumDevice {
		err := fmt.Errorf("you have license for %v device but using %v device", l.NumDevice, len(QC.DevData)-1)

		return err
	}

	return nil
}

// readLicenseFile attempts to read the content of a file
func readLicenseFile(name string) ([]byte, error) {
	// Try reading from the working directory first
	content, err := os.ReadFile(name)
	if err != nil {
		return nil, err
	}

	return content, nil
}

// LoadLicenseFile loads license file
func LoadLicenseFile(fileName string) (*NimblLicense, error) {
	key := []byte("nmskeygeneratoruniquekey")
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// try to read license file from working directory and home directory
	data, err := readLicenseFile(fileName)
	if err != nil {
		return nil, err
	}
	if len(data) == 0 {
		return nil, errors.New("license file data is too short")
	}

	iv := data[:aes.BlockSize]

	if len(data) < aes.BlockSize {
		return nil, errors.New("license file is corrupt and cannot be opened")
	}
	plaintext2 := make([]byte, len(data)-aes.BlockSize)
	stream := cipher.NewCTR(block, iv)
	stream.XORKeyStream(plaintext2, data[aes.BlockSize:])

	license := &NimblLicense{
		Path:                    fileName,
		FeatureAnomalyDetection: false,
		FeatureIdps:             false,
		NumClients:              0,
		NumDevice:               0,
	}
	err = json.Unmarshal(plaintext2, license)
	if err != nil {
		return nil, err
	}
	return license, nil
}

var daemon *Daemon

func RegisterDaemon(d *Daemon) {
	daemon = d
}

var once sync.Once

// LoadLicenseToQC load license file and store it in QC
func LoadLicenseToQC(fileName string) error {
	var err error
	once.Do(func() {
		var lic *NimblLicense
		lic, err = loadLicenseToQC(fileName)
		//if error
		if err != nil {
			t1 := time.Minute * 50
			tend := time.Minute * 60
			go licenseTimerExit(fileName, t1, tend)
		} else {
			if lic.Timeout != 0 {
				at := lic.Timeout - 10
				tend := time.Minute * time.Duration(lic.Timeout)
				if at <= 0 {
					go licenseTimerExit(fileName, 0, tend)
				} else {
					go licenseTimerExit(fileName, time.Minute*time.Duration(at), tend)
				}
			}
		}
	})
	return err
}

// licenseTimerExit
//
// step1: if license error,tell user rhe root shut down after end
//
// step2: send message to alert user after at(alert time)
//
// step3: root shut down at end
func licenseTimerExit(file string, at time.Duration, end time.Duration) {
	var t1Channel <-chan time.Time
	if at != 0 {
		t1Channel = time.After(at)
	}
	tendChannel := time.After(end)
	m := fmt.Sprintf("root wiil shut down after %v minutes,the reason is license file:%v error,please reload validated license", end.Minutes(), file)
	q.Q("license alert", m)
	SendSyslog(LOG_ALERT, "license error", m)
	sendLicenseSocketMessage(license_alert_error, m)
	for {
		select {
		case <-t1Channel:
			left := end - at
			m := fmt.Sprintf("root will shut down after %v minutes,the reason is license file:%v error, please reload validated license", left.Minutes(), file)
			q.Q("license alert", m)
			SendSyslog(LOG_ALERT, "license error", m)
			sendLicenseSocketMessage(license_alert_error, m)
		case <-tendChannel:
			m := fmt.Sprintf("root shut down,the reason is license file:%v error, please reload validated license", file)
			q.Q("license error", m)
			SendSyslog(LOG_ALERT, "license error", m)
			sendLicenseSocketMessage(license_alert_error, m)
			SyslogExit()
			if daemon != nil {
				daemon.RunMode("stop")
			}
			DoExit(1)
		}
	}
}

// loadLicenseToQC load license file and store it in QC
func loadLicenseToQC(fileName string) (*NimblLicense, error) {
	var err error
	lic, err := LoadLicenseFile(fileName)
	if err != nil {
		q.Q("license load error", err.Error())
		SendSyslog(LOG_ALERT, "license load error", err.Error())
		return nil, err
	}

	q.Q("license LoadLicenseToQC")
	SendSyslog(LOG_INFO, "license:", "LoadLicenseToQC")
	QC.License = lic
	go licenseNotify(fileName)
	return lic, nil
}

// licenseNotify license detected
func licenseNotify(fileName string) error {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		q.Q("license error", err.Error())
		SendSyslog(LOG_ALERT, "license error", err.Error())
		sendLicenseSocketMessage(license_alert_error, fmt.Sprintf("license error: %v, please restart", err.Error()))
		return err
	}
	defer watcher.Close()
	go func() {
		for {
			select {
			case event, ok := <-watcher.Events:
				if !ok {
					return
				}
				q.Q("license event:", strings.ToLower(event.Op.String()))
				SendSyslog(LOG_INFO, "license event", strings.ToLower(event.Op.String()))
				sendLicenseSocketMessage(license_alert_error, fmt.Sprintf("license event: %v, please restart", strings.ToLower(event.Op.String())))
				switch event.Op {
				case fsnotify.Remove:
					return
				}
			case err, ok := <-watcher.Errors:
				if !ok {
					return
				}
				q.Q("license error", err.Error())
				SendSyslog(LOG_ALERT, "license error", err.Error())
				sendLicenseSocketMessage(license_alert_error, fmt.Sprintf("license error: %v ,please restart", err.Error()))
			}
		}
	}()
	err = watcher.Add(fileName)
	if err != nil {
		q.Q("license error", err.Error())
		SendSyslog(LOG_ALERT, "license error", err.Error())
		sendLicenseSocketMessage(license_alert_error, fmt.Sprintf("license error: %v, please restart", err.Error()))
		return err
	}
	<-make(chan struct{})
	return nil
}

func CheckQCLicense() {
	err := QC.License.checkContent()
	if err != nil {
		q.Q(err)
		SendSyslog(LOG_ALERT, "license error", err.Error())
		sendLicenseSocketMessage(license_alert_error, err.Error())
	} else {
		sendLicenseSocketMessage(license_alert_clear, "")
		sendLicenseSocketMessage(license_alert_features, QC.License.EnabledFeatures)
	}
}

// func VerifyLicense() error {
// 	key := []byte("nmskeygeneratoruniquekey")
// 	block, err := aes.NewCipher(key)
// 	if err != nil {
// 		return err
// 	}
// 	// try to read license file from working directory and home directory

// 	data, err := readFileInWDorHome("nmskey")
// 	if err != nil {
// 		FailedFeatureEnabled()
// 		return errors.New("license file is corrupt and cannot be opened")
// 	}
// 	if len(data) == 0 {
// 		FailedFeatureEnabled()
// 		return errors.New("license file is corrupt and cannot be opened")
// 	}
// 	iv := data[:aes.BlockSize]

// 	if len(data) < aes.BlockSize {
// 		FailedFeatureEnabled()
// 		return errors.New("license file is corrupt and cannot be opened")
// 	}
// 	plaintext2 := make([]byte, len(data)-aes.BlockSize)
// 	stream := cipher.NewCTR(block, iv)
// 	stream.XORKeyStream(plaintext2, data[aes.BlockSize:])
// 	keyText := make(map[string]interface{})
// 	err = json.Unmarshal(plaintext2, &keyText)
// 	if err != nil {
// 		FailedFeatureEnabled()
// 		return errors.New("license file is corrupt and cannot be opened")
// 	}
// 	if len(keyText) == 0 {
// 		FailedFeatureEnabled()
// 		return errors.New("license file is corrupt and cannot be opened")
// 	}
// 	enabledFeatures, ok := keyText["enabledFeatures"]
// 	if ok {
// 		featureEnabled := strings.Split((enabledFeatures.(string)), ",")
// 		q.Q(featureEnabled)
// 		QC.AnomalyLicense = false
// 		QC.IdpsLicense = false
// 		// check anomalies in featureEnabled
// 		for _, feature := range featureEnabled {
// 			if feature == "anomalies" {
// 				QC.AnomalyLicense = true
// 			}
// 			if feature == "idps" {
// 				QC.IdpsLicense = true
// 			}
// 		}

// 		q.Q("anomaly license", QC.AnomalyLicense)
// 		q.Q("idps license", QC.IdpsLicense)

// 		sendLicenseSocketMessage("license_alert_features", (keyText["enabledFeatures"].(string)))
// 	} else {
// 		FailedFeatureEnabled()
// 	}
// 	err = CheckMachineID(keyText["machineID"].(string))
// 	if err != nil {
// 		return err
// 	}
// 	if NetworkServicesAliveCount() > int((keyText["numClients"]).(float64)) {
// 		return fmt.Errorf("you have license for %v network services but using %v network services", keyText["numClients"], NetworkServicesAliveCount())
// 	}
// 	if len(QC.DevData)-1 > int((keyText["numOfDevice"]).(float64)) {
// 		return fmt.Errorf("you have license for %v device but using %v device", keyText["numOfDevice"], len(QC.DevData)-1)
// 	}
// 	sendLicenseSocketMessage("license_alert_clear", "")
// 	return nil
// }

func sendLicenseSocketMessage(kind string, message string) {
	wsMessage := WebSocketMessage{
		Kind:    kind,
		Level:   5,
		Message: message,
	}
	QC.WebSocketMessageBroadcast <- wsMessage
}

func NetworkServicesAliveCount() int {
	activeClientCount := 0
	QC.ClientMutex.Lock()
	defer QC.ClientMutex.Unlock()
	for _, client := range QC.Clients {
		if client.Status == "active" {
			activeClientCount++
		}
	}
	return activeClientCount
}

func CheckMachineID(machineID string) error {
	id, err := machineid.ID()
	if err != nil {
		return errors.New("Machine ID error")
	}
	if id == machineID {
		return nil
	} else {
		return errors.New("Machine ID is diffrent from license machine id")
	}
}

// func FailedFeatureEnabled() {
// 	QC.AnomalyLicense = false
// 	QC.IdpsLicense = false
// 	sendLicenseSocketMessage("license_alert_features", "")
// }
