# Nimbl AI React

A sophisticated React-based chat interface that provides an advanced AI assistant experience. Built with modern web technologies, this application offers a rich set of features for AI-powered conversations, session management, and interactive content rendering.

## Key Features

### Chat Interface

- Real-time chat interface with support for user and assistant messages
- Markdown rendering with syntax highlighting for code blocks
- Interactive Mermaid diagram generation and rendering
- Error handling and display with visual feedback
- Message actions (delete, rerun)
- Chat history with automatic scrolling
- Multi-line message input with Shift+Enter support

### Session Management

- Multiple chat sessions support
- Session configuration with customizable settings:
  - Provider selection (OpenAI, etc.)
  - Model selection (GPT-4 Turbo, GPT-4, GPT-3.5 Turbo)
  - API key configuration
  - Base URL customization
  - Nimbl token integration

### Authentication

- Secure login system with username/password authentication
- Error handling for authentication failures
- Protected routes and features

### UI/UX Features

- Dark/Light theme support with dynamic Mermaid diagram theming
- Responsive sidebar for session navigation
- Material Design components with custom styling
- Loading states and progress indicators
- Session management interface with:
  - Create new sessions
  - Delete existing sessions
  - Switch between active sessions

## Tech Stack

### Core Technologies

- React 19
- Vite 6
- Material-UI (MUI) 7

### Key Libraries

- `markdown-it` - Advanced markdown processing
- `highlight.js` - Syntax highlighting for code blocks
- `mermaid` - Diagram generation and rendering
- `@emotion/react` & `@emotion/styled` - Styling solution
- `uuid` - Unique identifier generation

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Configure environment variables:

   - API endpoints
   - Authentication settings
   - Default model settings

4. Start the development server:
   ```bash
   npm run dev
   ```

## Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint for code quality checks

## Project Structure

```
src/
├── components/
│   ├── AssistantMessage/
│   │   ├── AssistantMessage.jsx    # Message rendering with markdown & diagrams
│   │   └── AssistantMessage.css    # Custom styles for messages
│   ├── ChatWindow.jsx              # Main chat interface
│   ├── NimbleLoginModal.jsx        # Authentication UI
│   ├── SessionSettingsModal.jsx    # Session configuration
│   └── Sidebar.jsx                 # Navigation and session management
├── services/
│   ├── apiService.js               # API communication
│   ├── errors.js                   # Error handling
│   └── toolDefinitions.js          # Tool configurations
├── assets/                         # Static assets
└── App.jsx                         # Main application component

```

## Contributing

1. Ensure you have the latest dependencies installed
2. Create a new branch for your feature
3. Follow the established code style:
   - Use functional components with hooks
   - Implement proper error handling
   - Add appropriate TypeScript types (if using TS)
   - Follow Material-UI best practices
4. Test your changes thoroughly
5. Submit a pull request

## Technical Notes

### Mermaid Configuration

- Supports both light and dark themes
- Custom styling for diagrams
- Automatic theme switching with app theme
- Supports multiple diagram types (flowchart, sequence, gantt)

### Message Handling

- Supports markdown formatting
- Code syntax highlighting
- Error message formatting
- Tool call responses
- Interactive elements

### State Management

- Session persistence
- Theme preferences
- Authentication state
- Message history

## User Guide

### Getting Started with Nimbl AI

1. **Initial Access**

   - Launch the application in your browser
   - You'll be presented with the chat interface
   - No initial login is required to start using the basic features

2. **Creating a New Chat Session**

   - **Important:** A unique filename is required before you can create a new session. Make sure to enter a filename or import sessions to create new sessions from the form.
   - Click the "+ New Chat" button in the sidebar
   - Configure your session settings:
     - Choose a name for your session
     - Select your AI provider (e.g., OpenAI)
     - Choose your preferred model (e.g., GPT-4 Turbo)
     - Enter your API key if required
     - Configure any additional URLs or tokens
   - Click "Save" to create the session

3. **File Section Features**

   - In each chat session, you can manage files related to your session
   - Features include:
     - input filename
     - import and export sessions

4. **Using Markdown Files as Context**

   - You can add `.md` (Markdown) files as context in your chat sessions
   - When a Markdown file is added, its content can be referenced or used to inform the assistant's responses
   - This is useful for sharing documentation, notes, or instructions with the AI

5. **Nimble Tools Authentication**

   - When using Nimble-specific tools or features:
     - A login modal will appear automatically
     - Enter your Nimble credentials
     - The authentication will be stored for the session
     - If login fails, check your credentials and try again
   - Nimble authentication is only required for:
     - Using Nimble-specific tools
     - Accessing Nimble-protected features
   - Regular chat features work without Nimble login

6. **Using the Chat Interface**

   - Type your message in the input field at the bottom
   - Press Enter to send (or Shift+Enter for new line)
   - Wait for the assistant's response
   - Messages support:
     - Regular text
     - Markdown formatting
     - Code blocks with syntax highlighting
     - Mermaid diagrams
     - Error messages with clear formatting

7. **Working with Messages**

   - Delete a message: Click the delete icon next to any message
   - Rerun a message: Click the replay icon to regenerate a response
   - Clear history: Use the "Clear History" button to start fresh
   - Messages are automatically saved within your session

8. **Managing Chat Sessions**

   - Switch between sessions using the sidebar
   - Edit session settings by creating a new session with the same configuration
   - Delete sessions using the delete icon in the sidebar
   - Each session maintains its own:
     - Message history
     - Model configuration
     - API settings

9. **Using Advanced Features**

   **Markdown Formatting**

   - Use `*italic*` for _italic text_
   - Use `**bold**` for **bold text**
   - Use `# Heading` for headings
   - Create lists with `- item` or `1. item`
   - Insert links with `[text](url)`

   **Code Blocks**

   ````
   ```python
   def example():
       return "Hello, World!"
   ````

   ```

   **Mermaid Diagrams**
   ```

   ```mermaid
   graph TD
       A[Start] --> B[Process]
       B --> C[End]
   ```

   ```

   ```

10. **Theme Customization**

    - Toggle between light and dark themes using the theme switch button
    - Theme changes affect:
      - Application interface
      - Code highlighting
      - Mermaid diagrams
      - Message formatting

11. **Troubleshooting**

    - If a message fails, an error message will be displayed
    - Check your API key if you receive authentication errors
    - Ensure your session settings are correct
    - Try reloading the page if diagrams don't render
    - Clear browser cache if experiencing persistent issues

12. **Best Practices**

    - Create separate sessions for different topics or projects
    - Use meaningful session names for easy navigation
    - Regularly clear chat history in long-running sessions
    - Save important information as it's not permanently stored
    - Test Mermaid diagrams in small chunks first
    - Use appropriate models for your use case:
      - GPT-4 Turbo: Complex tasks, latest features
      - GPT-4: Stable, high-quality responses
      - GPT-3.5 Turbo: Faster, more economical

13. **Keyboard Shortcuts**

    - Enter: Send message
    - Shift + Enter: New line in message
    - Ctrl/Cmd + Up/Down: Navigate message history
    - Escape: Close modals

14. **Privacy and Security**
    - Never share your API keys in messages
    - Log out when finished with sensitive work
    - Clear chat history containing sensitive information
    - Be cautious with sharing session URLs
    - Don't include sensitive data in Mermaid diagrams

## License

This project is private and confidential. All rights reserved.
