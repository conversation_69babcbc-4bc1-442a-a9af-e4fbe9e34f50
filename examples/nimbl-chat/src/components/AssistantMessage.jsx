import React, { useRef, useEffect, useState, useMemo } from "react";
import ReactDOM from "react-dom/client";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import mermaid from "mermaid";
import { Paper, Typography, Alert } from "@mui/material";
import { ErrorOutline as ErrorIcon } from "@mui/icons-material";
import "./AssistantMessage.css";

// Initialize Mermaid with default config
const initializeMermaid = (mode) => {
  const theme = mode === "dark" ? "dark" : "default";
  const themeVariables =
    mode === "dark"
      ? {
          primaryColor: "#BB86FC",
          primaryTextColor: "#fff",
          primaryBorderColor: "#9D4EDD",
          lineColor: "#666",
          textColor: "#fff",
          mainBkg: "#2F2F2F",
          nodeBkg: "#383838",
          clusterBkg: "#282828",
          edgeLabelBackground: "#2F2F2F",
          titleColor: "#fff",
        }
      : undefined;

  mermaid.initialize({
    startOnLoad: false,
    theme,
    securityLevel: "loose",
    flowchart: {
      useMaxWidth: true,
      htmlLabels: true,
      curve: "basis",
    },
    sequence: {
      useMaxWidth: true,
      width: "auto",
    },
    gantt: {
      useMaxWidth: true,
    },
    themeVariables,
    fontFamily: '"Roboto Mono", monospace',
    fontSize: 14,
    suppressErrorRendering: true,
  });
};

// Function to check if text is a Mermaid diagram with improved detection
const isMermaidDiagram = (text) => {
  const mermaidKeywords = [
    "graph",
    "flowchart",
    "sequenceDiagram",
    "classDiagram",
    "stateDiagram",
    "erDiagram",
    "journey",
    "gantt",
    "pie",
    "mindmap",
    "timeline",
  ];

  const trimmedText = text.trim();
  return mermaidKeywords.some((keyword) => {
    const regex = new RegExp(
      `^${keyword}\\s*(LR|TD|TB|RL|BT|\\w+)?[\\s;]`,
      "i"
    );
    return regex.test(trimmedText) || trimmedText.startsWith(keyword);
  });
};

// Create markdown renderer instance
const createMarkdownRenderer = (mode) => {
  return new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    highlight: function (str, lang) {
      // Handle Mermaid diagrams
      if (isMermaidDiagram(str)) {
        return `<div class="mermaid-placeholder" data-mermaid-content="${encodeURIComponent(
          str
        )}"></div>`;
      }

      // Regular code highlighting
      if (lang) {
        try {
          return hljs.highlight(str, { language: lang }).value;
        } catch (e) {
          console.warn(`Failed to highlight ${lang} code:`, e);
        }
      }

      // Fallback to auto-detection
      try {
        return hljs.highlightAuto(str).value;
      } catch (e) {
        console.warn("Failed to auto-highlight code:", e);
        return str; // Return original string if highlighting fails
      }
    },
  });
};

const MermaidError = ({ error, content }) => (
  <Alert
    severity="error"
    icon={<ErrorIcon />}
    sx={{
      my: 2,
      "& .MuiAlert-message": {
        width: "100%",
      },
    }}
  >
    <Typography variant="subtitle1" component="div" gutterBottom>
      Failed to render diagram
    </Typography>
    <Typography variant="body2" color="text.secondary" gutterBottom>
      {error.message}
    </Typography>
    <Paper
      sx={{
        mt: 1,
        p: 1,
        bgcolor: "background.default",
        maxHeight: "100px",
        overflow: "auto",
      }}
    >
      <Typography variant="caption" component="pre" sx={{ m: 0 }}>
        {content}
      </Typography>
    </Paper>
  </Alert>
);

const AssistantMessage = ({ content, messageIndex, mode }) => {
  const contentRef = useRef(null);
  const [renderedGraphs, setRenderedGraphs] = useState(new Set());
  const [renderErrors, setRenderErrors] = useState(new Map());
  // Update Mermaid config when theme changes
  useEffect(() => {
    initializeMermaid(mode);
    // Clear rendered graphs when theme changes to force re-render
    setRenderedGraphs(new Set());
    setRenderErrors(new Map());
  }, [mode]);

  // Memoize markdown renderer and content
  const md = useMemo(() => createMarkdownRenderer(mode), [mode]);
  const htmlContent = useMemo(() => md.render(content || ""), [content, md]);
  const contentKey = useMemo(
    () => `${messageIndex}-${content}-${mode}`,
    [messageIndex, content, mode]
  );

  useEffect(() => {
    if (!contentRef.current) return;

    // Reset error state for new content
    setRenderErrors(new Map());

    // Only set innerHTML if content hasn't been processed
    if (!renderedGraphs.has(contentKey)) {
      contentRef.current.innerHTML = htmlContent;
    }

    const processPlaceholders = async () => {
      // Re-initialize mermaid with current theme
      initializeMermaid(mode);

      const mermaidPlaceholders = contentRef.current.querySelectorAll(
        ".mermaid-placeholder:not([data-processed])"
      );

      if (mermaidPlaceholders.length === 0) return;

      const newErrors = new Map();

      // Process each placeholder
      await Promise.all(
        Array.from(mermaidPlaceholders).map(async (placeholder, index) => {
          const mermaidContent = decodeURIComponent(
            placeholder.getAttribute("data-mermaid-content")
          );
          const graphId = `mermaid-${messageIndex}-${index}-${Date.now()}`;

          try {
            const { svg } = await mermaid.render(graphId, mermaidContent);

            const graphContainer = document.createElement("div");
            graphContainer.className = "mermaid-container";
            graphContainer.setAttribute("data-mermaid-rendered", "true");
            graphContainer.innerHTML = svg;

            placeholder.setAttribute("data-processed", "true");
            placeholder.replaceWith(graphContainer);

            // Add theme attribute to parent container
            const container = document.querySelector(
              `div[id="${graphId}"]`
            )?.parentElement;
            if (container) {
              container.setAttribute("data-mui-color-scheme", mode);
            }
          } catch (error) {
            console.error("Mermaid rendering error:", error);
            newErrors.set(graphId, {
              error,
              content: mermaidContent,
            });

            // Create error container
            const errorDiv = document.createElement("div");
            errorDiv.setAttribute("id", graphId);
            errorDiv.setAttribute("data-processed", "true");
            errorDiv.setAttribute("data-mui-color-scheme", mode);
            placeholder.replaceWith(errorDiv);
          }
        })
      );

      setRenderErrors(newErrors);
      setRenderedGraphs((prev) => new Set([...prev, contentKey]));
    };

    processPlaceholders();
  }, [htmlContent, contentKey, renderedGraphs, mode]); // Added mode to dependencies

  // Render error components if needed
  useEffect(() => {
    renderErrors.forEach(({ error, content }, id) => {
      const errorContainer = document.getElementById(id);
      if (
        errorContainer &&
        !errorContainer.hasAttribute("data-error-rendered")
      ) {
        const root = ReactDOM.createRoot(errorContainer);
        root.render(<MermaidError error={error} content={content} />);
        errorContainer.setAttribute("data-error-rendered", "true");
      }
    });
  }, [renderErrors]);

  return <div ref={contentRef} className="message-content" />;
};

export default AssistantMessage;
