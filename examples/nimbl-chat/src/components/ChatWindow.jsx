import React, { useState, useRef, useEffect } from "react";
import {
  Box,
  Paper,
  Typography,
  TextField,
  IconButton,
  List,
  ListItem,
  Avatar,
  Chip,
  CircularProgress,
  Button,
} from "@mui/material";
import {
  Send as SendIcon,
  Person as PersonIcon,
  SmartToy as BotIcon,
  Build as ToolIcon,
  Delete as DeleteIcon,
  Replay as ReplayIcon,
  DeleteSweep as DeleteSweepIcon,
  ErrorOutline as ErrorIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  AttachFile as Paperclip,
} from "@mui/icons-material";
import AssistantMessage from "./AssistantMessage";

const ChatWindow = ({
  session,
  onSendMessage,
  onDeleteMessage,
  onRerunMessage,
  onClearHistory,
  isLoading,
  mode,
  onToggleTheme,
  onFileUpload,
}) => {
  const [inputValue, setInputValue] = useState("");
  const chatEndRef = useRef(null);
  const fileInputRef = useRef(null); // Ref for the hidden file input

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [session?.messages, isLoading]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;
    onSendMessage(inputValue);
    setInputValue("");
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      // Allow new line if Shift is held
      if (e.shiftKey) {
        return;
      }

      // Don't submit if input is empty or loading
      if (!inputValue.trim() || isLoading) {
        e.preventDefault();
        return;
      }

      // Otherwise, submit the message
      e.preventDefault();
      onSendMessage(inputValue);
      setInputValue("");
    }
  };

  const handleIconClick = () => {
    // Trigger the hidden file input click
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      onFileUpload(file);
    }
    // Reset the file input so the same file can be uploaded again
    e.target.value = null;
  };

  const renderContent = (msg, index) => {
    if (msg.role === "user") {
      return <span>{msg.content}</span>;
    }
    if (msg.role === "system") {
      return (
        <AssistantMessage
          content={msg.content}
          messageIndex={index}
          mode={mode}
        />
      );
    }

    if (msg.role === "assistant") {
      // Check if the message starts with "An error occurred:"
      if (
        msg.content &&
        typeof msg.content === "string" &&
        msg.content.startsWith("An error occurred:")
      ) {
        const errorMessage = msg.content
          .replace("An error occurred:", "")
          .trim();
        return (
          <div className="error-message">
            <ErrorIcon className="error-message-icon" />
            <div className="error-message-content">
              <div className="error-message-title">An error occurred</div>
              <div className="error-message-details">{errorMessage}</div>
            </div>
          </div>
        );
      }

      if (msg.tool_calls) {
        return (
          <div className="tool-call">
            <span>
              <strong>Calling tool:</strong>
            </span>
            {msg.tool_calls.map((tc, i) => (
              <pre key={i}>
                {tc.function.name}({JSON.stringify(tc.function.arguments)})
              </pre>
            ))}
          </div>
        );
      }
      return (
        <AssistantMessage
          content={msg.content}
          messageIndex={index}
          mode={mode}
        />
      );
    }

    if (msg.tool_calls) {
      return (
        <div className="tool-call">
          <span>
            <strong>Calling tool:</strong>
          </span>
          {msg.tool_calls.map((tc, i) => (
            <pre key={i}>
              {tc.function.name}({JSON.stringify(tc.function.arguments)})
            </pre>
          ))}
        </div>
      );
    }

    if (msg.role === "tool") {
      let formattedContent;
      try {
        const parsed = JSON.parse(msg.content);
        formattedContent = "```json\n" + JSON.stringify(parsed) + "\n```";
      } catch (e) {
        formattedContent = "```\n" + msg.content + "\n```";
      }

      return (
        <div className="tool-result">
          <span>
            <strong>Tool Result ({msg.name}):</strong>
          </span>
          <AssistantMessage
            content={formattedContent}
            messageIndex={index}
            mode={mode}
          />
        </div>
      );
    }

    return null;
  };

  if (!session) {
    return (
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          bgcolor: "background.paper",
          color: "text.secondary",
          textAlign: "center",
          p: 4,
        }}
      >
        <BotIcon sx={{ fontSize: 64, mb: 2, color: "primary.main" }} />
        <Typography variant="h4" gutterBottom>
          Welcome to the Multi-LLM Playground
        </Typography>
        <Typography variant="body1">
          Select a session from the sidebar or create a new one to get started.
        </Typography>
      </Box>
    );
  }

  const filteredMessages = session.messages.filter((msg) => {
    if (msg.role !== "assistant") {
      return true;
    }

    const hasVisibleToolCalls = msg.tool_calls && msg.tool_calls.length > 0;

    let hasVisibleText = false;
    if (typeof msg.content === "string") {
      hasVisibleText = msg.content.trim().length > 0;
    } else if (Array.isArray(msg.content)) {
      hasVisibleText = msg.content.some(
        (part) => part.type === "text" && part.text.trim().length > 0
      );
    }

    return hasVisibleToolCalls || hasVisibleText;
  });

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        height: "100vh",
        overflow: "hidden",
      }}
    >
      {/* Fixed Header */}
      <Paper
        elevation={1}
        sx={{
          p: 2,
          borderRadius: 0,
          borderBottom: 1,
          borderColor: "divider",
          position: "sticky",
          top: 0,
          zIndex: 100,
          bgcolor: "background.paper",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box>
            <Typography variant="h6" component="h1">
              {session.name}
            </Typography>
            <Chip
              label={`${session.provider}: ${session.model}`}
              size="small"
              variant="outlined"
              sx={{ mt: 0.5 }}
            />
          </Box>
          <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
            <IconButton onClick={onToggleTheme} color="inherit">
              {mode === "dark" ? <LightModeIcon /> : <DarkModeIcon />}
            </IconButton>
            <Chip
              label={
                session.nimblToken ? "Nimbl: Connected" : "Nimbl: Not Connected"
              }
              size="small"
              color={session.nimblToken ? "success" : "default"}
            />
            <Button
              startIcon={<DeleteSweepIcon />}
              onClick={onClearHistory}
              disabled={!session.messages.length || isLoading}
              color="error"
              variant="outlined"
              size="small"
            >
              Clear History
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Scrollable Messages */}
      <Box
        sx={{
          flex: 1,
          overflow: "auto",
          p: 2,
          display: "flex",
          flexDirection: "column",
        }}
      >
        <List sx={{ p: 0, flex: 1 }}>
          {filteredMessages.map((msg, index) => (
            <ListItem
              key={index}
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 2,
                mb: 2,
                p: 0,
                flexDirection: msg.role === "user" ? "row-reverse" : "row",
              }}
            >
              <Avatar
                sx={{
                  bgcolor:
                    msg.role === "user"
                      ? "primary.main"
                      : msg.role === "tool"
                      ? "secondary.main"
                      : "grey.600",
                  width: 32,
                  height: 32,
                  flexShrink: 0,
                }}
              >
                {msg.role === "user" ? (
                  <PersonIcon fontSize="small" />
                ) : msg.role === "tool" ? (
                  <ToolIcon fontSize="small" />
                ) : (
                  <BotIcon fontSize="small" />
                )}
              </Avatar>
              <Box
                sx={{
                  position: "relative",
                  maxWidth: "90%",
                }}
              >
                <Paper
                  elevation={1}
                  sx={{
                    p: 1.5,
                    bgcolor:
                      msg.role === "user"
                        ? "primary.main"
                        : mode === "dark"
                        ? "grey.900"
                        : "grey.100",
                    color:
                      msg.role === "user"
                        ? "primary.contrastText"
                        : "text.primary",
                    borderRadius: 2,
                  }}
                >
                  {renderContent(msg, index)}
                </Paper>
                <Box
                  sx={{
                    bottom: -15,
                    [msg.role === "user" ? "right" : "left"]: 0,
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <Typography variant="caption" color="text.secondary">
                    {msg.timestamp
                      ? new Date(msg.timestamp).toLocaleTimeString()
                      : new Date().toLocaleTimeString()}
                  </Typography>
                  {msg.role === "user" && (
                    <IconButton
                      size="small"
                      onClick={() =>
                        onRerunMessage && onRerunMessage(msg, index)
                      }
                      sx={{ padding: 0.5 }}
                    >
                      <ReplayIcon fontSize="small" />
                    </IconButton>
                  )}
                  <IconButton
                    size="small"
                    onClick={() =>
                      onDeleteMessage && onDeleteMessage(msg, index)
                    }
                    sx={{ padding: 0.5 }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
            </ListItem>
          ))}
          {isLoading && (
            <ListItem
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 2,
                mb: 2,
                p: 0,
              }}
            >
              <Avatar
                sx={{
                  bgcolor: "grey.600",
                  width: 32,
                  height: 32,
                  flexShrink: 0,
                }}
              >
                <BotIcon fontSize="small" />
              </Avatar>
              <Paper
                elevation={1}
                sx={{
                  p: 1.5,
                  maxWidth: "90%",
                  bgcolor: "grey.100",
                  borderRadius: 2,
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                <CircularProgress size={16} />
                <Typography variant="body2" sx={{ fontStyle: "italic" }}>
                  Thinking...
                </Typography>
              </Paper>
            </ListItem>
          )}
        </List>
        <div ref={chatEndRef} />
      </Box>

      {/* Fixed Footer */}
      <Paper
        elevation={1}
        sx={{
          p: 2,
          borderRadius: 0,
          borderTop: 1,
          borderColor: "divider",
          position: "sticky",
          bottom: 0,
          zIndex: 100,
          bgcolor: "background.paper",
        }}
      >
        <Box
          component="form"
          onSubmit={handleSubmit}
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 0.5,
          }}
        >
          <Box sx={{ display: "flex", gap: 1, alignItems: "flex-end" }}>
            <TextField
              fullWidth
              multiline
              maxRows={5}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="e.g., Draw the network topology"
              disabled={isLoading}
              variant="outlined"
              size="small"
            />
            {/* Hidden file input */}
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: "none" }}
              onChange={handleFileChange}
              accept=".md,.txt" // Accept markdown and text files
            />
            {/* Visible icon button */}
            <IconButton
              type="button"
              onClick={handleIconClick}
              disabled={isLoading}
              title="Attach documentation file"
              color="primary"
              sx={{ mb: 0.5 }}
            >
              <Paperclip />
            </IconButton>
            {/* Send button */}
            <IconButton
              type="submit"
              disabled={isLoading || !inputValue.trim()}
              color="primary"
              sx={{ mb: 0.5 }}
            >
              <SendIcon />
            </IconButton>
          </Box>
          <Typography variant="caption" color="text.secondary" sx={{ pl: 1 }}>
            Press Enter to send, Shift+Enter for new line
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default ChatWindow;
