import React from "react";
import "./SessionFileManager.css";

const SessionFileManager = ({
  onImport,
  onExport,
  filename,
  onFilenameChange,
  disableFilenameInput = false,
}) => {
  const importRef = React.useRef(null);
  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const sessions = JSON.parse(e.target.result);
          onImport(sessions);
          onFilenameChange(file.name.replace(".json", ""));
        } catch (error) {
          console.error("Error parsing JSON file:", error);
          alert(
            "Error importing file. Please make sure it's a valid JSON file."
          );
        }
      };
      reader.readAsText(file);
    }
  };

  const handleExport = () => {
    onExport();
  };

  return (
    <div className="session-file-manager">
      <div className="filename-section">
        <label htmlFor="session-filename">Session Filename:</label>
        <input
          type="text"
          id="session-filename"
          value={filename}
          onChange={(e) => onFilenameChange(e.target.value)}
          placeholder="Enter filename for sessions"
          disabled={disableFilenameInput}
        />
        {filename === "" && (
          <div
            className="filename-error"
            style={{ color: "red", fontSize: "0.9em" }}
          >
            Filename is required to create/export sessions.
          </div>
        )}
      </div>
      <div className="file-actions">
        <input
          ref={importRef}
          type="file"
          accept=".json"
          onChange={handleFileImport}
          style={{ display: "none" }}
        />
        <button
          onClick={() => importRef.current.click()}
          className="file-button"
        >
          Import Sessions
        </button>
        <button
          onClick={handleExport}
          className="export-button"
          disabled={!disableFilenameInput}
        >
          Export Sessions
        </button>
      </div>
    </div>
  );
};

export default SessionFileManager;
