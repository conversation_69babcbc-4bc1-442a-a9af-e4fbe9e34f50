import React, { useState, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
} from "@mui/material";

const SessionSettingsModal = ({ onSave, onClose, sessionToEdit }) => {
  const [session, setSession] = useState({
    id: sessionToEdit?.id || uuidv4(),
    name: sessionToEdit?.name || "New Chat Session",
    provider: sessionToEdit?.provider || "openai",
    model: sessionToEdit?.model || "gpt-4-turbo-preview",
    apiKey: sessionToEdit?.apiKey || "",
    baseUrl: sessionToEdit?.baseUrl || "",
    nimbl_base_url: sessionToEdit?.nimbl_base_url || "",
    nimblToken: sessionToEdit?.nimblToken || null,
    messages: sessionToEdit?.messages || [],
  });

  useEffect(() => {
    // If we are editing, populate the form
    if (sessionToEdit) {
      setSession(sessionToEdit);
    }
  }, [sessionToEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setSession((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = (e) => {
    e.preventDefault();
    onSave(session);
  };

  const getModelSuggestions = () => {
    switch (session.provider) {
      case "openai":
        return ["gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"];
      case "anthropic":
        return [
          "claude-3-opus-20240229",
          "claude-3-sonnet-20240229",
          "claude-2.1",
        ];
      case "google":
        return ["gemini-pro", "gemini-1.5-pro-latest", "gemini-2.0-flash"];
      default:
        return [];
    }
  };

  return (
    <Dialog open={true} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {sessionToEdit ? "Edit Session" : "Create New Session"}
      </DialogTitle>
      <DialogContent>
        <Box component="form" onSubmit={handleSave} sx={{ pt: 1 }}>
          <TextField
            fullWidth
            label="Session Name"
            name="name"
            value={session.name}
            onChange={handleChange}
            required
            margin="normal"
          />

          <FormControl fullWidth margin="normal" required>
            <InputLabel>Provider</InputLabel>
            <Select
              name="provider"
              value={session.provider}
              label="Provider"
              onChange={handleChange}
            >
              <MenuItem value="openai">
                OpenAI / Local (OpenAI-compatible)
              </MenuItem>
              <MenuItem value="anthropic">Anthropic</MenuItem>
              <MenuItem value="google">Google</MenuItem>
            </Select>
          </FormControl>

          <TextField
            fullWidth
            label="Model Name"
            name="model"
            value={session.model}
            onChange={handleChange}
            required
            margin="normal"
            helperText={`Suggestions: ${getModelSuggestions().join(", ")}`}
          />

          <TextField
            fullWidth
            label="API Key"
            name="apiKey"
            type="password"
            value={session.apiKey}
            onChange={handleChange}
            required
            margin="normal"
          />

          <TextField
            fullWidth
            label="Base URL (Optional - for local LLMs)"
            name="baseUrl"
            value={session.baseUrl}
            onChange={handleChange}
            placeholder="e.g., http://localhost:1234/v1"
            margin="normal"
          />

          <TextField
            fullWidth
            label="Nimble Base URL (for Topology Tool)"
            name="nimbl_base_url"
            value={session.nimbl_base_url}
            onChange={handleChange}
            placeholder="e.g., http://localhost:8000"
            margin="normal"
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionSettingsModal;
