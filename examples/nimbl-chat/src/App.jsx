import React, { useState, useEffect, useMemo } from "react";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import Box from "@mui/material/Box";
import { useMediaQuery } from "@mui/material";
import Sidebar from "./components/Sidebar";
import ChatWindow from "./components/ChatWindow";
import SessionSettingsModal from "./components/SessionSettingsModal";
import { callLlmApi } from "./services/apiService";
import { availableTools, toolSchemas } from "./services/toolDefinitions";
import NimbleLoginModal from "./components/NimbleLoginModal";
import { NimbleAuthError } from "./services/errors";
import {
  saveSessionsToStorage,
  loadSessionsFromStorage,
} from "./services/fileService";
import { getItem, setItem } from "./services/indexedDbService";
import "./App.css";

const App = () => {
  const prefersDarkMode = useMediaQuery("(prefers-color-scheme: dark)");
  const [mode, setMode] = useState(() => {
    // IndexedDB is async, so we must load theme mode in useEffect
    return prefersDarkMode ? "dark" : "light";
  });

  useEffect(() => {
    (async () => {
      const savedMode = await getItem("theme-mode");
      if (savedMode) setMode(savedMode);
    })();
  }, [prefersDarkMode]);

  const toggleColorMode = () => {
    setMode((prevMode) => {
      const newMode = prevMode === "light" ? "dark" : "light";
      setItem("theme-mode", newMode);
      return newMode;
    });
  };

  // Update highlight.js theme when mode changes
  useEffect(() => {
    const lightTheme = document.querySelector('link[data-theme="light"]');
    const darkTheme = document.querySelector('link[data-theme="dark"]');

    if (mode === "dark") {
      darkTheme?.removeAttribute("disabled");
      lightTheme?.setAttribute("disabled", "");
    } else {
      lightTheme?.removeAttribute("disabled");
      darkTheme?.setAttribute("disabled", "");
    }
  }, [mode]);

  const theme = useMemo(
    () =>
      createTheme({
        palette: {
          mode,
          primary: {
            main: "#1976d2",
          },
          secondary: {
            main: "#dc004e",
          },
          background: {
            default: mode === "light" ? "#f0f2f5" : "#121212",
            paper: mode === "light" ? "#ffffff" : "#1e1e1e",
          },
        },
        components: {
          MuiTextField: {
            defaultProps: {
              size: "small",
            },
          },
        },
        typography: {
          fontFamily: [
            "-apple-system",
            "BlinkMacSystemFont",
            '"Segoe UI"',
            "Roboto",
            '"Helvetica Neue"',
            "Arial",
            "sans-serif",
          ].join(","),
        },
      }),
    [mode]
  );

  const [sessions, setSessions] = useState([]);
  const [activeSessionId, setActiveSessionId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [sessionToEdit, setSessionToEdit] = useState(null);
  const [filename, setFilename] = useState("");

  // --- NEW STATES FOR NIMBLE AUTH ---
  const [isNimbleLoginOpen, setIsNimbleLoginOpen] = useState(false);
  const [nimbleLoginError, setNimbleLoginError] = useState("");
  const [pendingToolAction, setPendingToolAction] = useState(null);

  // Load sessions from localStorage on initial render
  useEffect(() => {
    (async () => {
      try {
        const { sessions: savedSessions, filename: savedFilename } =
          await loadSessionsFromStorage();
        if (savedSessions && savedSessions.length > 0) {
          setSessions(savedSessions);
          setActiveSessionId(savedSessions[0].id);
        }
        if (savedFilename) {
          setFilename(savedFilename);
        }
      } catch (error) {
        console.error("Error loading sessions:", error);
      }
    })();
  }, []);

  // Save sessions to localStorage whenever they change
  useEffect(() => {
    (async () => {
      await saveSessionsToStorage(sessions, filename);
    })();
  }, [sessions, filename]);

  const handleFilenameChange = (newFilename) => {
    setFilename(newFilename);
  };

  const handleImportSessions = (importedSessions) => {
    setSessions(importedSessions);
    if (importedSessions.length > 0) {
      setActiveSessionId(importedSessions[0].id);
    }
  };

  const activeSession = useMemo(
    () => sessions.find((s) => s.id === activeSessionId),
    [sessions, activeSessionId]
  );

  const updateSessionMessages = (sessionId, messages) => {
    setSessions((prevSessions) =>
      prevSessions.map((s) => (s.id === sessionId ? { ...s, messages } : s))
    );
  };

  // A helper function to update a session in the main state array
  const updateSession = (updatedSession) => {
    setSessions((prev) =>
      prev.map((s) => (s.id === updatedSession.id ? updatedSession : s))
    );
  };

  const handleSendMessage = async (userInput) => {
    if (!activeSession) return;

    // Clear any previous authentication errors when starting a new request
    setNimbleLoginError("");

    const userMessage = {
      role: "user",
      content: userInput,
      timestamp: new Date().toISOString(),
    };
    const initialMessages = [...activeSession.messages, userMessage];
    updateSessionMessages(activeSessionId, initialMessages);
    setIsLoading(true);

    try {
      await processLlmRequest(initialMessages, activeSession);
    } catch (error) {
      console.error("Error processing LLM request:", error);
      const errorMessage = {
        role: "assistant",
        content: `An error occurred: ${error.message}`,
        timestamp: new Date().toISOString(),
      };
      updateSessionMessages(activeSessionId, [
        ...initialMessages,
        errorMessage,
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  // processLlmRequest will be slightly modified in its catch block
  const processLlmRequest = async (currentMessages, session) => {
    setIsLoading(true);
    let messagesForApi = [...currentMessages];

    try {
      while (true) {
        const response = await callLlmApi(messagesForApi, session, toolSchemas);
        const responseMessage = {
          ...response.message,
          timestamp: new Date().toISOString(),
        };
        messagesForApi.push(responseMessage);
        updateSessionMessages(session.id, messagesForApi);

        const toolCalls = response.toolCalls;
        if (!toolCalls || toolCalls.length === 0) {
          setPendingToolAction(null); // Clear any pending action on success
          break;
        }

        try {
          const toolResults = await Promise.all(
            toolCalls.map(async (toolCall) => {
              const toolName = toolCall.function.name;
              const toolArgs = toolCall.function.arguments;
              if (availableTools[toolName]) {
                const result = await availableTools[toolName](
                  toolArgs,
                  session,
                  updateSession
                );
                return {
                  role: "tool",
                  tool_call_id: toolCall.id,
                  name: toolName,
                  content: result,
                  timestamp: new Date().toISOString(),
                };
              }
              return {
                role: "tool",
                tool_call_id: toolCall.id,
                name: toolName,
                content: "Tool not found",
                timestamp: new Date().toISOString(),
              };
            })
          );

          messagesForApi.push(...toolResults);
          updateSessionMessages(session.id, messagesForApi);
        } catch (e) {
          if (e instanceof NimbleAuthError) {
            console.log("Authentication required. Pausing execution.");
            // Save the exact context needed to resume
            setPendingToolAction({
              messages: messagesForApi, // History *including* the assistant's request
              toolCalls: toolCalls, // The tools that need to be run
            });
            setIsNimbleLoginOpen(true);
            return; // Exit the function cleanly, we will resume later
          }
          // For other errors, re-throw to be caught by the outer block
          throw e;
        }
      }
    } catch (error) {
      console.error("Error processing LLM request:", error);
      const errorMessage = {
        role: "assistant",
        content: `An error occurred: ${error.message}`,
        timestamp: new Date().toISOString(),
      };
      updateSessionMessages(session.id, [...messagesForApi, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to clean up incomplete tool call sequences
  const cleanupIncompleteToolCalls = () => {
    if (!activeSession || !pendingToolAction) return;

    const { messages } = pendingToolAction;

    // Find the last message and check if it's an assistant message with tool_calls
    const lastMessage = messages[messages.length - 1];
    if (
      lastMessage &&
      lastMessage.role === "assistant" &&
      lastMessage.tool_calls
    ) {
      // Remove the incomplete assistant message with tool_calls
      const cleanedMessages = messages.slice(0, -1);
      updateSessionMessages(activeSession.id, cleanedMessages);

      // Add a user-friendly message explaining what happened
      const cancelMessage = {
        role: "assistant",
        content:
          "An error occurred: Authentication was cancelled. Please try your request again if you'd like to use tools that require authentication.",
      };
      updateSessionMessages(activeSession.id, [
        ...cleanedMessages,
        cancelMessage,
      ]);
    }
  };

  const resumeToolExecution = async (action, updatedSession) => {
    console.log("Resuming tool execution after login.");
    setIsLoading(true);

    let { messages, toolCalls } = action;

    try {
      // Re-run the tools that failed before. This time it should work.
      const toolResults = await Promise.all(
        toolCalls.map(async (toolCall) => {
          const toolName = toolCall.function.name;
          const toolArgs = toolCall.function.arguments;
          if (availableTools[toolName]) {
            const result = await availableTools[toolName](
              toolArgs,
              updatedSession,
              updateSession
            );
            return {
              role: "tool",
              tool_call_id: toolCall.id,
              name: toolName,
              content: result,
            };
          }
          return {
            role: "tool",
            tool_call_id: toolCall.id,
            name: toolName,
            content: "Tool not found",
          };
        })
      );

      // Add the successful tool results to the message history
      let newMessages = [...messages, ...toolResults];
      updateSessionMessages(updatedSession.id, newMessages);

      // Now, continue the LLM process with the complete context
      await processLlmRequest(newMessages, updatedSession);
    } catch (error) {
      console.error("Error resuming LLM request:", error);
      const errorMessage = {
        role: "assistant",
        content: `An error occurred during resume: ${error.message}`,
        timestamp: new Date().toISOString(),
      };
      updateSessionMessages(updatedSession.id, [...messages, errorMessage]);
    } finally {
      setIsLoading(false);
      setPendingToolAction(null); // Clear the pending action
    }
  };

  const handleNimbleLogin = async (username, password) => {
    setNimbleLoginError("");
    if (!activeSession?.nimbl_base_url) return;

    try {
      const response = await fetch(
        `${activeSession.nimbl_base_url}/api/v1/login`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ user: username, password: password }),
        }
      );
      if (!response.ok) throw new Error(`Login failed: ${response.statusText}`);

      const data = await response.json();
      const token = data.token;

      const updatedSessionWithToken = { ...activeSession, nimblToken: token };
      updateSession(updatedSessionWithToken);

      setIsNimbleLoginOpen(false);
      setNimbleLoginError("");

      // MODIFICATION 3: Use the new resume function
      if (pendingToolAction) {
        await resumeToolExecution(pendingToolAction, updatedSessionWithToken);
      }
    } catch (error) {
      console.error("Nimble login failed:", error);
      setNimbleLoginError(error.message);
      // Don't clear pendingToolAction here - let user retry or cancel
    }
  };

  const handleSaveSession = (sessionData) => {
    const existingIndex = sessions.findIndex((s) => s.id === sessionData.id);
    if (existingIndex > -1) {
      // Update existing session
      setSessions((prev) =>
        prev.map((s) => (s.id === sessionData.id ? sessionData : s))
      );
    } else {
      // Add new session
      setSessions((prev) => [...prev, sessionData]);
      setActiveSessionId(sessionData.id);
    }
    setIsModalOpen(false);
    setSessionToEdit(null);
  };

  const handleNewSession = () => {
    setSessionToEdit(null);
    setIsModalOpen(true);
  };

  const handleDeleteSession = (sessionId) => {
    setSessions((prev) => prev.filter((s) => s.id !== sessionId));
    if (activeSessionId === sessionId) {
      setActiveSessionId(
        sessions.length > 1 ? sessions.find((s) => s.id !== sessionId).id : null
      );
    }
  };

  const clearSessionHistory = (sessionId) => {
    setSessions((prevSessions) =>
      prevSessions.map((s) => (s.id === sessionId ? { ...s, messages: [] } : s))
    );
  };

  const handleDeleteMessage = (message, index) => {
    if (!activeSession) return;

    setSessions((prevSessions) =>
      prevSessions.map((s) => {
        if (s.id === activeSessionId) {
          // Create copy of messages and filter out the message at the given index
          const newMessages = [...s.messages];
          newMessages.splice(index, 1);
          return { ...s, messages: newMessages };
        }
        return s;
      })
    );
  };

  const handleRerunMessage = async (message, index) => {
    if (!activeSession || message.role !== "user" || isLoading) return;

    // Get all messages up to the selected message
    const messagesUpToIndex = activeSession.messages.slice(0, index);

    // Update session with truncated message history
    updateSessionMessages(activeSessionId, messagesUpToIndex);

    // Resend the selected message
    await handleSendMessage(message.content);
  };

  // NEW: Handler for file upload
  const handleFileUpload = (file) => {
    if (!activeSession) return;
    setIsLoading(true);

    const reader = new FileReader();

    reader.onload = (e) => {
      const fileContent = e.target.result;

      // Create a special system message with the file content
      const systemMessage = {
        role: "system",
        content: `--- START OF DOCUMENTATION CONTEXT ---\n\n${fileContent}\n\n--- END OF DOCUMENTATION CONTEXT ---`,
      };

      // Create a user-facing message to confirm the upload
      const userFacingMessage = {
        role: "assistant",
        content: `✅ **Documentation Loaded:** I have read the file \`${file.name}\`. I will now use this context to answer your questions and generate commands for this session.`,
      };

      // Check if a system message with documentation already exists.
      const existingSystemDocIndex = activeSession.messages.findIndex(
        (msg) =>
          msg.role === "system" &&
          msg.content.includes("--- START OF DOCUMENTATION CONTEXT ---")
      );

      let newMessages;
      if (existingSystemDocIndex !== -1) {
        // If it exists, replace it with the new one.
        console.log("Replacing existing documentation context.");
        newMessages = [...activeSession.messages];
        newMessages[existingSystemDocIndex] = systemMessage;
        newMessages.push(userFacingMessage);
      } else {
        // Otherwise, add the new system message at the beginning
        console.log("Adding new documentation context.");
        newMessages = [
          systemMessage,
          ...activeSession.messages,
          userFacingMessage,
        ];
      }

      updateSessionMessages(activeSession.id, newMessages);
      setIsLoading(false);
    };

    reader.onerror = (e) => {
      console.error("Error reading file:", e);
      const errorMessage = {
        role: "assistant",
        content: `❌ **Error:** Failed to read the file \`${file.name}\`.`,
      };
      updateSessionMessages(activeSession.id, [
        ...activeSession.messages,
        errorMessage,
      ]);
      setIsLoading(false);
    };

    reader.readAsText(file);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          display: "flex",
          height: "100vh",
          bgcolor: mode === "light" ? "#f0f2f5" : "#121212",
        }}
      >
        <Sidebar
          sessions={sessions}
          activeSessionId={activeSessionId}
          onSelectSession={setActiveSessionId}
          onNewSession={handleNewSession}
          onDeleteSession={handleDeleteSession}
          mode={mode}
          filename={filename}
          onFilenameChange={handleFilenameChange}
          onImportSessions={handleImportSessions}
        />
        <Box
          sx={{
            flex: 1,
            display: "flex",
            flexDirection: "column",
            height: "100vh",
            overflow: "auto",
          }}
        >
          <ChatWindow
            session={activeSession}
            onSendMessage={handleSendMessage}
            onDeleteMessage={handleDeleteMessage}
            onRerunMessage={handleRerunMessage}
            onClearHistory={() => clearSessionHistory(activeSessionId)}
            isLoading={isLoading}
            mode={mode}
            onToggleTheme={toggleColorMode}
            onFileUpload={handleFileUpload}
          />
        </Box>
        {isModalOpen && (
          <SessionSettingsModal
            onSave={handleSaveSession}
            onClose={() => setIsModalOpen(false)}
            sessionToEdit={sessionToEdit}
          />
        )}
        {isNimbleLoginOpen && (
          <NimbleLoginModal
            onLogin={handleNimbleLogin}
            onClose={() => {
              cleanupIncompleteToolCalls(); // Clean up incomplete tool calls
              setIsNimbleLoginOpen(false);
              setNimbleLoginError("");
              setPendingToolAction(null); // Clear pending action on cancel
              setIsLoading(false); // Stop loading state
            }}
            error={nimbleLoginError}
          />
        )}
      </Box>
    </ThemeProvider>
  );
};

export default App;
