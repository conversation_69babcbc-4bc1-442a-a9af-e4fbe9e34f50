/* General Layout */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
}
.app-layout {
  display: flex;
  height: 100vh;
  background-color: #f0f2f5;
}

/* Sidebar */
.sidebar {
  width: 300px;
  background-color: #202123;
  color: white;
  display: flex;
  flex-direction: column;
}
.new-session-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px;
  margin: 15px;
  border-radius: 5px;
  cursor: pointer;
  text-align: left;
}
.new-session-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.session-list {
  flex-grow: 1;
  overflow-y: auto;
}
.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  cursor: pointer;
  border-radius: 5px;
  margin: 5px 15px;
}
.session-item:hover {
  background-color: #343541;
}
.session-item.active {
  background-color: #444654;
}
.session-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.delete-btn {
  background: none;
  border: none;
  color: #acacbe;
  font-size: 1.2em;
  cursor: pointer;
  opacity: 0;
}
.session-item:hover .delete-btn {
  opacity: 1;
}
.delete-btn:hover {
  color: white;
}

/* New container for all the text, allows it to grow and shrink */
.session-info {
  flex-grow: 1;
  min-width: 0; /* CRITICAL for text-overflow to work in a flex item */
  display: flex;
  flex-direction: column;
  gap: 4px; /* Adds a small space between the name and the details */
}

/* Make sure the main name truncates if too long */
.session-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Container for the smaller details */
.session-details {
  font-size: 0.75em; /* Smaller font */
  color: #acacbe; /* Lighter, less prominent color */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Style for the provider/model text to make sure it truncates */
.provider-model {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 8px; /* Space between it and the message count */
}

/* Keep the message count from shrinking */
.message-count {
  flex-shrink: 0;
}

/* Chat Window */
.chat-window {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #ffffff;
}
.chat-window-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
  text-align: center;
}
.chat-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
  font-weight: bold;
}
.model-tag {
  font-weight: normal;
  color: #555;
  font-size: 0.8em;
  margin-left: 8px;
}
.message-list {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.message {
  display: flex;
  gap: 15px;
  max-width: 90%;
}
.role-avatar {
  width: 32px;
  height: 32px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  flex-shrink: 0;
}
.message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}
.message.user .role-avatar {
  background-color: #1976d2;
}
.message.assistant .role-avatar,
.message.tool .role-avatar {
  background-color: #5f6368;
}
.message-content {
  border-radius: 10px;
  background-color: transparent;
  /* Add these to make it work well with markdown */
  line-height: 1.6;
  max-width: 100%;
  overflow-x: auto;
}
.message.user .message-content {
  background-color: #1976d2;
  color: white;
  padding: 10px 15px; /* Keep user padding simple */
}
.message-content p {
  margin: 1em 0;
}
/* Remove extra margins from the first and last elements inside a bubble */
.message-content > *:first-child {
  margin-top: 10px;
}
.message-content > *:last-child {
  margin-bottom: 10px;
}
.tool-call,
.tool-result {
  border-radius: 8px;
}
.message.user .tool-call,
.message.user .tool-result {
  background: rgba(255, 255, 255, 0.1);
}
.message-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: transparent; /* CHANGED from #e8eaed */
  padding: 0; /* CHANGED from 10px */
  border-radius: 5px;
  margin: 1em 0;
  border: none; /* CHANGED from 1px solid #ddd */
  font-size: 0.9em;
}

/* The highlight.js theme wraps code in another <code> tag inside <pre> */
.message-content pre code {
  display: block;
  border-radius: 6px;
  padding: 10px;
}

/* Styling for inline code */
.message-content code {
  font-family: "Courier New", Courier, monospace;
  background-color: rgba(0, 0, 0, 0.08);
  border-radius: 3px;
  font-size: 0.9em;
}

/* Styling for lists */
.message-content ul,
.message-content ol {
  padding-left: 25px;
}

/* Message Form */
.message-form {
  display: flex;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background: #fff;
}
.message-form input {
  flex-grow: 1;
  padding: 12px 18px;
  border: 1px solid #ccc;
  border-radius: 24px;
  margin-right: 15px;
  font-size: 1em;
}
.message-form button {
  padding: 12px 24px;
  border: none;
  background-color: #1976d2;
  color: white;
  border-radius: 24px;
  cursor: pointer;
  font-size: 1em;
}
.message-form button:disabled {
  background-color: #aaa;
}

/* Settings Modal */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
.modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}
.modal-content h2 {
  margin-top: 0;
}
.modal-content form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.modal-content label {
  font-weight: bold;
  margin-bottom: -10px;
}
.modal-content input,
.modal-content select {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
}
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}
.modal-actions button {
  padding: 10px 20px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
}
.modal-actions button[type="submit"] {
  background-color: #1976d2;
  color: white;
}
.modal-actions button[type="button"] {
  background-color: #e0e0e0;
}
/* Style for Mermaid container */
.mermaid-container {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow-x: auto;
  min-height: 100px;
  border: 1px solid #e0e0e0;
}
.mermaid-container svg {
  display: block;
  margin: auto;
  max-width: 100%;
  height: auto;
}

/* Style for Mermaid placeholder while loading */
.mermaid-placeholder {
  padding: 16px;
  background-color: #fafafa;
  border: 2px dashed #bdbdbd;
  border-radius: 8px;
  margin: 16px 0;
  text-align: center;
  color: #757575;
  font-family: "Roboto Mono", monospace;
  white-space: pre-wrap;
  font-size: 14px;
}

/* Improved code block styling */
pre {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  max-width: 100%;
  overflow-x: auto;
}

.hljs {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

/* Tool call and result styling */
.tool-call pre,
.tool-result pre {
  background-color: transparent;
  border-radius: 6px;
  border-left: 4px solid #2196f3;
  margin: 8px 0;
  font-family: "Roboto Mono", monospace;
  font-size: 13px;
  line-height: 1.4;
}

.tool-call pre {
  padding: 12px;
}

/* Error message styling in chat */
.error-message {
  background-color: #fdeded;
  color: #5f2120;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #ef5350;
  margin: 8px 0;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.error-message-icon {
  color: #ef5350;
  flex-shrink: 0;
}

.error-message-content {
  flex: 1;
}

.error-message-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.error-message-details {
  font-size: 0.95em;
  opacity: 0.9;
}

.tool-result {
  margin: 8px 0;
}

/* Style for Modal Error Message */
.modal-error {
  color: #d32f2f;
  background-color: #ffcdd2;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #d32f2f;
}
