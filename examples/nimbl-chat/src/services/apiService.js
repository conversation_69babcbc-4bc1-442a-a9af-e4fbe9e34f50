import { getTopologySystemPrompt } from "./toolDefinitions"; // Import our new function

// This function now takes the session config as an argument
export const callLlmApi = async (messagesForApi, session, toolSchemas) => {
  const { provider, model, apiKey, baseUrl } = session;

  if (!apiKey) {
    throw new Error(
      `API Key for ${provider} is not set in the current session.`
    );
  }

  let finalMessages = [...messagesForApi];
  const lastMessage = finalMessages[finalMessages.length - 1];

  // Check if the last message was the result of our topology tool.
  if (
    lastMessage?.role === "tool" &&
    lastMessage?.name === "get_topology_data"
  ) {
    console.log("Injecting topology system prompt for Mermaid generation.");
    const systemPromptContent = getTopologySystemPrompt(lastMessage.content);

    // --- THIS IS THE FIX ---
    // DO NOT REPLACE the tool message. Simply add the system prompt.
    // The LLM needs the tool message to be present in the history.
    // REMOVED: finalMessages[finalMessages.length - 1] = { ... };

    // Add the powerful system prompt to the start of the conversation for this specific call.
    finalMessages.unshift({ role: "system", content: systemPromptContent });
  }

  // --- A. OpenAI / Local LLM with OpenAI-compatible API ---
  if (provider === "openai") {
    const apiUrl = baseUrl || "https://api.openai.com/v1/chat/completions";
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: model,
        messages: finalMessages, // USE THE MODIFIED MESSAGES
        tools: toolSchemas,
        tool_choice: "auto",
      }),
    });
    if (!response.ok) {
      console.error("OpenAI/Local API Error:", response);
      throw new Error(`OpenAI/Local API error: ${response.statusText}`);
    }

    const data = await response.json();
    const message = data.choices[0].message;

    return {
      message: message,
      toolCalls:
        message.tool_calls?.map((tc) => ({
          ...tc,
          function: {
            ...tc.function,
            arguments: JSON.parse(tc.function.arguments),
          },
        })) || [],
    };
  }

  // --- B. Anthropic (Claude) ---
  if (provider === "anthropic") {
    // Anthropic does not support custom base URLs in the same way
    const systemPrompt = finalMessages.find((m) => m.role === "system");
    const userMessages = finalMessages.filter((m) => m.role !== "system");
    const response = await fetch("https://api.anthropic.com/v1/messages", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": apiKey,
        "anthropic-version": "2023-06-01",
        "anthropic-beta": "tools-2024-04-04",
      },
      body: JSON.stringify({
        model: model,
        max_tokens: 2048,
        system: systemPrompt?.content, // Anthropic's specific way of handling system prompts
        messages: userMessages, // USE THE MODIFIED MESSAGES
        tools: toolSchemas.map((t) => t.function),
      }),
    });
    if (!response.ok)
      throw new Error(`Anthropic API error: ${response.statusText}`);
    const data = await response.json();

    const message = { role: "assistant", content: data.content };
    const toolCalls = data.content
      .filter((c) => c.type === "tool_use")
      .map((tc) => ({
        id: tc.id,
        type: "function",
        function: { name: tc.name, arguments: tc.input },
      }));

    return { message, toolCalls };
  }

  // --- C. Google (Gemini) ---
  if (provider === "google") {
    // Find our system prompt if it exists.
    const systemPrompt = finalMessages.find((m) => m.role === "system");
    const chatHistory = finalMessages.filter((m) => m.role !== "system");

    let geminiContents = [];

    // MODIFICATION: Add the system instruction at the top level of the request body.
    // It's part of the `generateContent` request itself, not the `contents` array.
    const requestBody = {
      tools: [{ functionDeclarations: toolSchemas.map((t) => t.function) }],
    };
    if (systemPrompt) {
      requestBody.systemInstruction = {
        parts: [{ text: systemPrompt.content }],
      };
    }

    // Convert the rest of the chat history to Gemini's format
    requestBody.contents = chatHistory.map((msg) => {
      let role = "user"; // Default role
      if (msg.role === "assistant") role = "model";
      else if (msg.role === "tool") role = "function";

      let parts;
      if (msg.role === "tool") {
        parts = [
          {
            functionResponse: {
              name: msg.name,
              response: { content: msg.content },
            },
          },
        ];
      } else if (msg.role === "assistant" && msg.tool_calls) {
        // Ensure arguments are an object, not a string
        const args =
          typeof msg.tool_calls[0].function.arguments === "string"
            ? JSON.parse(msg.tool_calls[0].function.arguments)
            : msg.tool_calls[0].function.arguments;

        parts = [
          {
            functionCall: { name: msg.tool_calls[0].function.name, args: args },
          },
        ];
      } else {
        parts = [{ text: msg.content || "" }];
      }

      return { role, parts };
    });

    // The API endpoint URL should use the correct model name from the session.
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${apiKey}`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      const errorBody = await response.text();
      console.error("Google API Error Body:", errorBody);
      throw new Error(`Google API error: ${response.error.message}`);
    }

    const data = await response.json();
    // ... rest of the Gemini response handling logic is unchanged ...
    if (!data.candidates || data.candidates.length === 0) {
      throw new Error(
        `Google API returned no content. Response: ${JSON.stringify(data)}`
      );
    }
    const part = data.candidates[0].content.parts[0];

    let message,
      toolCalls = [];
    if (part.functionCall) {
      message = {
        role: "assistant",
        tool_calls: [
          {
            id: part.functionCall.name,
            type: "function",
            function: {
              name: part.functionCall.name,
              arguments: part.functionCall.args,
            },
          },
        ],
      };
      toolCalls = message.tool_calls;
    } else {
      message = { role: "assistant", content: part.text || "" };
    }

    return { message, toolCalls };
  }

  throw new Error("Invalid provider selected");
};
