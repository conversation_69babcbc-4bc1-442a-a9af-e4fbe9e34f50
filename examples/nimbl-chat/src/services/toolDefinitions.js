import { NimbleAuthError } from "./errors"; // Import our custom error

// NEW: Define the detailed system prompt as a template function.
export const getTopologySystemPrompt = (jsonData) => {
  return `You are an intelligent assistant specializing in network topology visualization using the Mermaid charting language.
  You will be given a network topology described in JSON format.
  Your task is to generate a Mermaid graph definition that visually represents the topology,
  using *only* the information present in the provided JSON topology.
  
  The JSON topology data is a map where each key is a device ID (typically a MAC address).
  The value for each key is an object containing details about that device.
  Device object structure:
  - "id": The device identifier (same as the key).
  - "ipAddress": The IP address of the device.
  - "modelname": The model name of the device, which can be used as a label.
  - "linkData": An array of objects, where each object describes an outgoing link from this device.
    - "source": Source device ID for the link.
    - "target": Target device ID for the link.
    - "sourcePort": The port on the source device.
    - "targetPort": The port on the target device.
  
  The JSON topology data is as follows:
  \`\`\`json
  ${jsonData}
  \`\`\`
  
  Instructions for your Mermaid output:
  1.  Start the graph with "graph LR;" for a left-to-right layout.
  2.  For each device, define a Mermaid node.
      -   Use a safe Mermaid node ID by prefixing with "N_" and replacing all non-alphanumeric characters (like colons) with underscores. Example: "00-60-E9-20-C1-63" becomes "N_00_60_E9_20_C1_63".
      -   The node label should be informative. Format: N_Safe_Device_ID["'Model Name'<br/>IP: ip.address"]
          Example: N_00_60_E9_20_C1_63["'EHG7512-8PoE-410GSFP'<br/>IP: ***********"]
          (Note the use of <br/> for newlines inside the label)
  3.  For each link in "linkData", connect the corresponding safe Mermaid node IDs.
      -   Use the "sourcePort" and "targetPort" to create a link label. Format: -->|"sourcePort to targetPort"|
        Example: N_00_60_E9_20_C1_63 -->|"Port2 to Port4"| N_00_60_E9_21_29_31
  4.  Generate *ONLY* the raw Mermaid code. Do not include any explanations, introductory text, or markdown code fences (like \`\`\`mermaid). Your entire response must be only the graph definition itself.
  `;
};
// --- Reusable Auth Helper ---
// This avoids repeating code in every Nimble tool.
const getNimbleAuthHeaders = async (session) => {
  if (!session.nimbl_base_url) {
    throw new Error(
      "Nimble Base URL is not configured in the session settings."
    );
  }
  if (!session.nimblToken) {
    console.log("TOOL: No Nimble token found. Requesting login.");
    throw new NimbleAuthError("Authentication required.");
  }
  return { Authorization: `Bearer ${session.nimblToken}` };
};

const handleNimbleApiCall = async (endpoint, session, updateSession) => {
  let headers = await getNimbleAuthHeaders(session);
  const response = await fetch(endpoint, { headers });

  // Handle token expiration
  if (response.status === 401 || response.status === 403) {
    console.log("TOOL: Token is invalid or expired. Requesting new login.");
    await updateSession({ ...session, nimblToken: null }); // Clear the bad token
    throw new NimbleAuthError(
      "Authentication required, token may have expired."
    );
  }
  if (!response.ok) {
    throw new Error(`API Error: ${response.status} ${response.statusText}`);
  }
  return response.json();
};

const handleNimblePostApiCall = async (
  endpoint,
  session,
  updateSession,
  options
) => {
  let headers = await getNimbleAuthHeaders(session);
  const response = await fetch(endpoint, {
    ...options,
    headers: { ...headers, ...options.headers },
  });

  // Handle token expiration
  if (response.status === 401 || response.status === 403) {
    console.log("TOOL: Token is invalid or expired. Requesting new login.");
    await updateSession({ ...session, nimblToken: null }); // Clear the bad token
    throw new NimbleAuthError(
      "Authentication required, token may have expired."
    );
  }
  if (!response.ok) {
    throw new Error(`API Error: ${response.status} ${response.statusText}`);
  }
  return response.json();
};

const executeGetTopology = async (args, session, updateSession) => {
  console.log("TOOL: Attempting to fetch topology.");
  const endpoint = `${session.nimbl_base_url}/api/v1/topology`;
  const data = await handleNimbleApiCall(endpoint, session, updateSession);
  return JSON.stringify(data);
};

const executeGetDevices = async (args, session, updateSession) => {
  console.log("TOOL: Attempting to fetch devices with args:", args);
  let endpoint = `${session.nimbl_base_url}/api/v1/devices`;
  // You can extend this to handle args.device_name or args.query if your API supports it
  // e.g., if (args.query) { endpoint += `?q=${args.query}` }

  const data = await handleNimbleApiCall(endpoint, session, updateSession);

  // --- CHUNKING LOGIC ---
  const CHUNK_SIZE = 10;
  if (Array.isArray(data) && data.length > CHUNK_SIZE) {
    console.log(
      `TOOL: Found ${data.length} devices. Returning summary and first chunk.`
    );
    const summary = {
      total_devices: data.length,
      showing: CHUNK_SIZE,
      summary_message: `Found ${data.length} devices. Here are the first ${CHUNK_SIZE}. Ask for more details on a specific device by name or ID.`,
      devices: data.slice(0, CHUNK_SIZE),
    };
    return JSON.stringify(summary, null, 2);
  }

  console.log(`TOOL: Found ${data.length} devices. Returning full list.`);
  return JSON.stringify(data, null, 2);
};

const executeSendNimblCommand = async (args, session, updateSession) => {
  // `args.commands` is now an array of objects, exactly what the API needs.
  const { commands } = args;

  if (!commands || commands.length === 0) {
    return "Error: No command objects provided to send.";
  }

  console.log(
    `TOOL: Attempting to send ${commands.length} commands.`,
    commands
  );
  console.log(commands);
  const endpoint = `${session.nimbl_base_url}/api/v1/commands`;
  const options = {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    // We can now directly stringify the `commands` array from the arguments.
    body: JSON.stringify(commands),
  };

  try {
    const responseData = await handleNimblePostApiCall(
      endpoint,
      session,
      updateSession,
      options
    );
    return JSON.stringify(responseData, null, 2);
  } catch (error) {
    throw error;
  }
};

// --- REVISED TOOL: Get Command History and format as Markdown Table ---
const executeGetCommandHistory = async (args, session, updateSession) => {
  console.log("TOOL: Attempting to fetch and format command history.");
  const endpoint = `${session.nimbl_base_url}/api/v1/commands?cmd=all`;

  try {
    const data = await handleNimbleApiCall(endpoint, session, updateSession);

    // The API returns a JSON string, so we need to parse it first.
    let commands = [];
    // Ensure we have an array to work with.
    if (Array.isArray(data)) {
      commands = data;
    } else {
      // Handle cases where the API might wrap the array in an object
      if (typeof data === "object" && data !== null) {
        commands = Object.values(data) || [];
      }
    }
    if (commands.length === 0) {
      return "No command history found.";
    }

    // --- Markdown Table Generation Logic ---
    // Define headers
    const headers = ["Command", "Status", "Client", "Verified"];

    // Create the header and separator rows for the markdown table
    const headerRow = `| ${headers.join(" | ")} |`;
    const separatorRow = `| ${headers.map(() => "---").join(" | ")} |`;

    // Create a row for each command object
    const bodyRows = commands
      .map((item) => {
        // Sanitize command content to prevent breaking the table with '|' characters
        const safeCommand = (item.command || "N/A").replace(/\|/g, "|");

        const row = [
          safeCommand,
          item.status || "N/A", // Use "N/A" if status is null/undefined or missing
          item.client || "", // Use empty string if client is null/undefined or missing
          item.verify || "N/A", // Use "N/A" if verify is null/undefined or missing
        ];
        return `| ${row.join(" | ")} |`;
      })
      .join("\n");

    // Combine all parts into a single markdown string
    const markdownTable = `${headerRow}\n${separatorRow}\n${bodyRows}`;

    return markdownTable;
  } catch (error) {
    console.error("Error processing command history:", error);
    if (error instanceof NimbleAuthError) throw error;
    return `Error fetching or formatting command history: ${error.message}`;
  }
};

// Map tool names to their actual functions
export const availableTools = {
  get_topology_data: executeGetTopology, // Add the new tool
  get_devices: executeGetDevices, // Add the new tool
  send_nimbl_command: executeSendNimblCommand, // Add the new tool
  get_command_history: executeGetCommandHistory, // Add the new tool
};

// --- 2. DEFINE TOOL SCHEMAS FOR THE LLM ---
export const toolSchemas = [
  {
    type: "function",
    function: {
      name: "get_topology_data",
      description: `Fetches network topology data from a configured Nimble instance. After fetching the data, you can answer user queries based on it. If the user asks for a visual representation, a diagram, or a graph, generate a Mermaid graph syntax representation of the topology data.`,
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description:
              'A specific question about the topology, e.g., "how many switches are there?". This is optional.',
          },
        },
        required: [],
      },
    },
  },
  {
    type: "function",
    function: {
      name: "get_devices",
      description:
        "Fetches detailed information about one or all devices from the Nimble instance. This tool is CRITICAL for gathering the current state of a device (like its IP address, subnet mask, gateway, etc.) before attempting to change a setting with the `send_nimbl_command` tool. Always use this first if you need context to build a command. also check capabilities of device before building a command. if agent is available use priority to build the command with agent.",
      parameters: {
        type: "object",
        properties: {
          device_name: {
            type: "string",
            description:
              "Optional. The specific name of a device to query for.",
          },
          query: {
            type: "string",
            description: "Optional. A generic query string to filter devices.",
          },
        },
        required: [],
      },
    },
  },
  {
    type: "function",
    function: {
      name: "send_nimbl_command",
      description: `Executes raw script commands on the NIMBL system. Formulate command strings precisely based on documentation.
      - check capabilities of device before building a command. if agent is available use priority to build the command with agent. 
      - by default do NOT include a 'client' key in the command object.
      - The 'client' key is ONLY required if the user's request.
      - For complex commands needing context (like changing a hostname), you MUST use the 'get_devices' tool first to gather required parameters.`,
      parameters: {
        type: "object",
        properties: {
          commands: {
            type: "array",
            description:
              'An array of command objects. Each object must have a "command" string and may have a "client" string if required.',
            items: {
              type: "object",
              properties: {
                command: {
                  type: "string",
                  description:
                    "The full command string to execute, without 'bbctl/-cc/-ck'. Example: 'beep AA-BB-CC-DD-EE-FF'",
                },
                client: {
                  type: "string",
                  description: "ONLY USE if the user's request.",
                },
              },
              required: ["command"],
            },
          },
        },
        required: ["commands"],
      },
    },
  },
  // --- REVISED Schema for get_command_history ---
  {
    type: "function",
    function: {
      name: "get_command_history",
      description:
        "Retrieves a history of all previously executed NIMBL commands and displays it in a table.",
      // No parameters needed for this tool,
      parameters: { type: "object", properties: {} },
    },
  },
];
