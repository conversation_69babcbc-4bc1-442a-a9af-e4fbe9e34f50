import { setItem, getItem } from "./indexedDbService";

export const exportSessionsToFile = (sessions, filename) => {
  const jsonContent = JSON.stringify(sessions, null, 2);
  const blob = new Blob([jsonContent], { type: "application/json" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = `${filename || "sessions"}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const saveSessionsToStorage = async (sessions, filename) => {
  await setItem("sessions", JSON.stringify(sessions));
  await setItem("sessionsFilename", filename);
};

export const loadSessionsFromStorage = async () => {
  const sessions = await getItem("sessions");
  const filename = await getItem("sessionsFilename");
  return {
    sessions: sessions ? JSON.parse(sessions) : [],
    filename: filename || "",
  };
};
