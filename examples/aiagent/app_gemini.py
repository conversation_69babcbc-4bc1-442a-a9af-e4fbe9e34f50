import os
import requests
import json
import re
import faulthandler
from dotenv import load_dotenv

# Update document
import argparse
import subprocess
import shutil
import stat

# 1. Loading and Splitting
from langchain_community.document_loaders import DirectoryLoader, UnstructuredMarkdownLoader
from langchain_text_splitters import MarkdownHeaderTextSplitter

# 2. Vectorization and Storage
#from langchain_community.vectorstores import Chroma
from langchain_community.vectorstores import FAISS

# 3. Retrieval and Generation
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.chains import create_history_aware_retriever
from langchain_core.messages import AIMessage, HumanMessage

# Load environment variables from the .env file
load_dotenv()

DIR = os.getcwd()
faulthandler.enable()
# --- Main Settings ---
DOCS_DIR = os.path.join(DIR,"docs")
FAISS_DB_DIR = os.path.join(DIR,"faiss_db_gemini")
API_BASE_URL = "http://localhost:27182" # <<< MODIFICATION 1: Central API URL
DEBUG = False

# --- Settings for documentation update ---
USERGUIDE_REPO_URL = "**************:bbtechhive/userguide.git"
USERGUIDE_LOCAL_PATH = os.path.join(DIR,"userguide_repo") # A local folder to clone the repo into
USERGUIDE_SOURCE_DOCS_PATH = os.path.join(USERGUIDE_LOCAL_PATH, "user_guide_chapters")

# Check for Google's API Key
if not os.getenv("GOOGLE_API_KEY"):
    raise ValueError("Please set the GOOGLE_API_KEY environment variable in your .env file")

# <<< MODIFICATION 3: New function to handle documentation updates >>>
def update_documentation():
    """
    Clones the documentation from git, copies the files to DOCS_DIR,
    and then cleans up by deleting the cloned repository.
    This function includes a handler for read-only git files on Windows.
    """
    if DEBUG: print("--- Starting Documentation Update (Fresh Clone) ---")

    # <<< FIX: Define an error handler for shutil.rmtree >>>
    def remove_readonly(func, path, exc_info):
        """
        Error handler for shutil.rmtree.

        If the error is a permission error, it attempts to change the
        file's mode to writable and then re-attempts the removal.
        Otherwise, it re-raises the error.
        """
        # exc_info[1] is the exception instance
        if isinstance(exc_info[1], PermissionError):
            if DEBUG: print(f"PermissionError on {path}. Changing permissions and retrying...")
            try:
                # Change the file to be writable (stat.IWRITE) and retry
                os.chmod(path, stat.S_IWRITE)
                func(path) # Retry the function that failed (e.g., os.unlink)
            except Exception as e:
                if DEBUG: print(f"Failed to remove {path} even after changing permissions: {e}")
        else:
            # Re-raise the error if it's not a PermissionError
            raise

    # 1. Clean up any pre-existing local repository folder to ensure a fresh start.
    if os.path.exists(USERGUIDE_LOCAL_PATH):
        if DEBUG: print(f"Removing existing repository folder: '{USERGUIDE_LOCAL_PATH}'...")
        # Use the error handler when removing the directory
        shutil.rmtree(USERGUIDE_LOCAL_PATH, onerror=remove_readonly)

    # 2. Clone the repository from scratch.
    if DEBUG: print(f"Cloning repository '{USERGUIDE_REPO_URL}' into '{USERGUIDE_LOCAL_PATH}'...")
    try:
        command = f"git clone {USERGUIDE_REPO_URL} {USERGUIDE_LOCAL_PATH}"
        subprocess.run(command, check=True, shell=True)
        if DEBUG: print("Repository cloned successfully.")
    except subprocess.CalledProcessError as e:
        if DEBUG: print(f"Error: Failed to clone repository. {e}")
        return False

    # 3. Ensure the source and destination directories exist.
    if not os.path.exists(USERGUIDE_SOURCE_DOCS_PATH):
        if DEBUG: print(f"Error: Source documentation path does not exist: {USERGUIDE_SOURCE_DOCS_PATH}")
        if os.path.exists(USERGUIDE_LOCAL_PATH):
            shutil.rmtree(USERGUIDE_LOCAL_PATH, onerror=remove_readonly)
        return False

    if not os.path.exists(DOCS_DIR):
        os.makedirs(DOCS_DIR)
        if DEBUG: print(f"Created destination directory: {DOCS_DIR}")

    # 4. Copy all markdown files from the source to the destination.
    if DEBUG: print(f"Copying .md files from '{USERGUIDE_SOURCE_DOCS_PATH}' to '{DOCS_DIR}'...")
    try:
        files_copied = 0
        # ... (file copy logic is the same) ...
        for filename in os.listdir(USERGUIDE_SOURCE_DOCS_PATH):
            if filename.endswith(".md"):
                source_file = os.path.join(USERGUIDE_SOURCE_DOCS_PATH, filename)
                destination_file = os.path.join(DOCS_DIR, filename)
                shutil.copy2(source_file, destination_file)
                files_copied += 1
        if DEBUG: print(f"Copied {files_copied} files.")
    except Exception as e:
        if DEBUG: print(f"Error during file copy: {e}")
        return False
    finally:
        # 5. Clean up the cloned repository folder, using the error handler.
        if DEBUG: print(f"Cleaning up by removing '{USERGUIDE_LOCAL_PATH}'...")
        if os.path.exists(USERGUIDE_LOCAL_PATH):
            shutil.rmtree(USERGUIDE_LOCAL_PATH, onerror=remove_readonly)
            if DEBUG: print("Cleanup complete.")

    if DEBUG: print("--- Documentation Update Finished ---")
    return True


# <<< MODIFICATION 2: API Session Handler for Login and Token Management >>>
class APISession:
    """Manages login, token storage, and execution of API requests."""
    def __init__(self, base_url):
        self.base_url = base_url
        self.token = None

    def login(self):
        """Logs into the API to get a token."""
        if self.token:
            return True # Already logged in

        if DEBUG: print("No active token. Attempting to log in...")
        login_endpoint = "/api/v1/login"
        login_url = self.base_url + login_endpoint
        credentials = {"user": "admin", "password": "default"}
        
        try:
            response = requests.post(login_url, json=credentials)
            response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
            data = response.json()
            self.token = data.get("token")
            if self.token:
                if DEBUG: print("Login successful. Token acquired.")
                return True
            else:
                if DEBUG: print("Login failed: Token not found in response.")
                return False
        except requests.exceptions.RequestException as e:
            if DEBUG: print(f"Login failed: Could not connect to API at {login_url}. Error: {e}")
            return False

    def execute_request(self, method, endpoint, payload=None):
        """Executes a generic, authenticated API request."""
        if not self.login():
            return {"error": "Authentication failed. Cannot proceed with the request."}

        full_url = self.base_url + endpoint
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        print(f"\nExecuting: {method} {full_url}")
        if payload:
            print(f"Payload: {json.dumps(payload, indent=2)}")

        try:
            response = requests.request(method, full_url, headers=headers, json=payload)
            response.raise_for_status()
            # Try to parse JSON, but fall back to text if it fails
            try:
                return response.json()
            except json.JSONDecodeError:
                return response.text
        except requests.exceptions.RequestException as e:
            return {"error": f"API request failed: {e}"}


def setup_rag_chain():
    """
    Sets up and returns a RAG chain that uses Google Gemini and has conversation history capabilities.
    """
    embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
    llm = ChatGoogleGenerativeAI(
        model="gemini-1.5-flash", 
        temperature=0.2, 
        model_kwargs = {
            "response_format": {
                "type": "json_object"
            }
        }
    )

    # --- Database loading/creation logic (no changes here) ---
    if os.path.exists(FAISS_DB_DIR):
        if DEBUG: print(f"Loading existing FAISS index from '{FAISS_DB_DIR}'...")
        vectorstore = FAISS.load_local(FAISS_DB_DIR, embeddings, allow_dangerous_deserialization=True)
    else:
        if DEBUG: print(f"No existing FAISS index found, creating a new one from '{DOCS_DIR}'...")
        md_loader = DirectoryLoader(DOCS_DIR, glob="**/*.md", loader_cls=UnstructuredMarkdownLoader)
        md_docs = md_loader.load()
        if not md_docs: raise ValueError(f"No Markdown docs found in '{DOCS_DIR}'.")
        
        headers_to_split_on = [("#", "Header 1"),("##", "Header 2"), ("###", "Header 3")]
        markdown_splitter = MarkdownHeaderTextSplitter(headers_to_split_on=headers_to_split_on, strip_headers=False)

        all_splits = []
        if DEBUG: print(f"Found {len(md_docs)} document(s) to process.")
        for doc in md_docs:
            splits = markdown_splitter.split_text(doc.page_content)
            all_splits.extend(splits)

        if not all_splits: raise ValueError("No content to process after splitting all documents.")

        if DEBUG: print(f"\nCreated a total of {len(all_splits)} chunks. Storing into FAISS index...")

        vectorstore = FAISS.from_documents(documents=all_splits, embedding=embeddings)
        vectorstore.save_local(FAISS_DB_DIR)
        if DEBUG: print(f"FAISS index creation complete and saved to '{FAISS_DB_DIR}'.")

    retriever = vectorstore.as_retriever(search_type="similarity", search_kwargs={"k": 5})

    contextualize_q_system_prompt = (
        "Given a chat history and the latest user question "
        "which might reference context in the chat history, "
        "formulate a standalone question which can be understood "
        "without the chat history. Do NOT answer the question, "
        "just reformulate it if needed and otherwise return it as is."
    )
    contextualize_q_prompt = ChatPromptTemplate.from_messages(
        [("system", contextualize_q_system_prompt), MessagesPlaceholder("chat_history"), ("human", "{input}")]
    )
    history_aware_retriever = create_history_aware_retriever(llm, retriever, contextualize_q_prompt)

    # <<< FIX 2: Escape all curly braces in the JSON examples with double braces {{...}} >>>
    qa_system_prompt = (
        "You are an AI agent that translates user requests into executable API calls for the 'NIMBL' system. "
        "Based on the user's request and the provided API documentation context, your SOLE task is to generate a JSON object that represents the action to be taken. "
        "The API base URL is fixed at http://localhost:27182 and authentication is handled automatically."
        "\n--- JSON OUTPUT SCHEMA ---\n"
        "Your output MUST be a single, valid JSON object with the following structure:\n"
        # Using double curly braces to escape them
        "{{ "
        '  "action": "api_call" | "respond_text", '
        '  "method": "GET" | "POST" | "PUT" | "DELETE" | null, '
        '  "endpoint": "/api/v1/..." | null, '
        '  "payload": {{ ... }} | null, '
        '  "explanation": "A brief, one-sentence summary for the user about what you are doing." '
        "}}\n"
        "\n--- INSTRUCTIONS & EXAMPLES ---\n"
        "1.  If the user's request can be mapped to an API call, set `action` to `api_call` and fill in `method`, `endpoint`, and `payload` from the context."
        "    - User: 'How do I create a new user named client?'"
        "    - You output: "
        '    {{ "action": "api_call", "method": "POST", "endpoint": "/api/v1/users", "payload": {{ "name": "client", "email": "<EMAIL>", "password": "Pas$Word1", "role": "admin" }}, "explanation": "I will create a new user named \'client\'." }}'
        "2.  If the user's request want to run command, you need to change payload."
        "    - User: 'I want to beep device 00-60-E9-1B-A9-0A'"
        "    - You output: "
        '    {{ "action": "api_call", "method": "POST", "endpoint": "/api/v1/commands", "payload": [{{ "command": "beep 00-60-E9-1B-A9-0A" }}], "explanation": "I will make \'00-60-E9-1B-A9-0A\' a beeping sound." }}'
        "3.  For GET or DELETE requests, `payload` should be `null`."
        "    - User: 'Show me all the devices.'"
        "    - You output: "
        '    {{ "action": "api_call", "method": "GET", "endpoint": "/api/v1/devices", "payload": null, "explanation": "I will retrieve information for all devices." }}'
        "4.  If you need to ask a clarifying question or cannot find a suitable API endpoint in the context, set `action` to `respond_text` and put your question/statement in the `explanation` field."
        "    - User: 'Can you order a pizza?'"
        "    - You output: "
        '    {{ "action": "respond_text", "method": null, "endpoint": null, "payload": null, "explanation": "Based on the provided API documentation, I cannot perform this action." }}'
        "5.  The documentation context may contain code blocks with language identifiers (e.g., ````shell` or ````json`). You MUST ignore these language specifiers and only focus on processing the actual code or command content inside the block. For example, if you see ````shell\nbeep 123````, you should only process `beep 123`."
        "\n--- CONTEXT ---\n"
        "{context}" # This one remains a single brace because it's a real variable
    )

    qa_prompt = ChatPromptTemplate.from_messages(
        [("system", qa_system_prompt), MessagesPlaceholder("chat_history"), ("human", "{input}")]
    )
    
    question_answer_chain = create_stuff_documents_chain(llm, qa_prompt)
    rag_chain = create_retrieval_chain(history_aware_retriever, question_answer_chain)
    return rag_chain

def extract_json_from_response(text: str) -> str:
    """
    Extracts a JSON object from a string, even if it's embedded in a Markdown code block.
    """
    match = re.search(r"```json\s*(\{.*?\})\s*```", text, re.DOTALL)
    if match:
        return match.group(1)
    return text.strip()

def extract_text_from_response(text: str) -> str:
    """
    Extracts a text object from a string, even if it's embedded in a Markdown code block.
    """
    match = re.search(r"```text\s*(.*?)\s*```", text, re.DOTALL)
    if match:
        return match.group(1)
    return text.strip()

def main():
    """
    Main execution function with API call execution logic and doc update feature.
    """
    # Setup command-line argument parser
    parser = argparse.ArgumentParser(description="An API-enabled AI assistant for NIMBL documentation.")
    parser.add_argument(
        '--update-docs',
        action='store_true',
        help='Update the local documentation from the git repository before starting.'
    )
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Display debug messages.'
    )
    args = parser.parse_args()

    global DEBUG
    if args.debug:
        DEBUG = True

    # If the flag is present, run the update process
    if args.update_docs:
        if not update_documentation():
            # If update fails, ask the user if they want to proceed
            proceed = input("Documentation update failed. Do you want to continue with existing local docs? (y/n): ")
            if proceed.lower() != 'y':
                print("Exiting.")
                return
        
        # IMPORTANT: If docs are updated, the vector database is now stale.
        # We should delete it to force a rebuild.
        if os.path.exists(FAISS_DB_DIR):
            if DEBUG: print(f"Documentation updated. Deleting old vector database '{FAISS_DB_DIR}' to force rebuild...")
            shutil.rmtree(FAISS_DB_DIR)
            if DEBUG: print("Old database deleted.")

    # --- The rest of the main function remains the same ---
    print("\nInitializing the API-enabled AI Assistant (using Google Gemini)...")
    rag_chain = setup_rag_chain()
    api_session = APISession(base_url=API_BASE_URL)
    
    print("Initialization complete! You can start making requests (type 'exit' or 'q' to quit).\n")
    
    chat_history = []
    while True:
        user_input = input("Your request: ")

        if not user_input.strip():
            continue

        if user_input.lower() in ["exit", "q"]:
            print("Goodbye!")
            break

        # Get the structured JSON response from the LLM
        response = rag_chain.invoke({"input": user_input, "chat_history": chat_history})
        try:
            # <<< FIX: Use helper function to clean up AI's return content >>>
            raw_answer = response["answer"]
            clean_json_str = extract_json_from_response(raw_answer)
            command = json.loads(clean_json_str)
            action = command.get("action")
            explanation = command.get("explanation", "No explanation provided.")
            print(f"\nAI: {explanation}")
            if action == "api_call":
                method = command.get("method")
                endpoint = command.get("endpoint")
                payload = command.get("payload")

                if not all([method, endpoint]):
                    print("Execution Error: AI response was missing 'method' or 'endpoint'.")
                    api_result_text = "Error: I generated an incomplete API call."
                    continue
                
                result = api_session.execute_request(method, endpoint, payload)
                
                if isinstance(result, (dict, list)):
                    formatted_result = json.dumps(result, indent=2, ensure_ascii=False)
                    print(formatted_result)
                    api_result_text = formatted_result
                else:
                    print(result)
                    api_result_text = str(result)

            elif action == "respond_text":
                api_result_text = explanation
            else:
                print(f"Unknown action: '{action}'. The raw AI response was: {raw_answer}")
                api_result_text = f"Unknown action: '{action}'"
        
        except json.JSONDecodeError:
            api_result_text = extract_text_from_response(response["answer"])
            print(api_result_text)
        except Exception as e:
            print(f"\nAn unexpected error occurred: {e}")
            api_result_text = f"An error occurred: {e}"

        # Update chat history
        chat_history.append(HumanMessage(content=user_input))
        chat_history.append(AIMessage(content=api_result_text))

if __name__ == "__main__":
    main()