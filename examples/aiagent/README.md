# LLM demo application

An API-enabled AI assistant for NIMBL documentation.

## Why to use

Interacting with complex systems like `NIMBL` often involves a steep learning curve. You either spend time digging through user guides for answers or meticulously crafting API calls to get things done. This application was built to eliminate this friction, transforming how you work with `NIMBL` by creating an intelligent, conversational interface.

This application empowers an AI assistant with two core capabilities. First, it ingests the `NIMBL` user guide into its database, allowing it to answer any `NIMBL` questions. Second, and more powerfully, the assistant can execute actions via HTTP API calls. This enables users to simply describe a desired change to a device's settings in plain language, and the assistant will automatically translate the request into the correct command and send it to `NIMBL`.

## Prepare
1. Install python 3.11
2. Install dependency library
```shell
pip install langchain-google-genai langchain chromadb "unstructured[md]" python-dotenv
pip install langchain-community
pip install faiss-cpu
```
3. Set envirument PATH
```shell
export PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python311:$PATH
export PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts:$PATH
```
4. Set up your API key. Open `.env` and edit `GOOGLE_API_KEY`.
```shell
GOOGLE_API_KEY="YOUR_API_KEY_HERE"
```

## How to use

```
usage: app_gemini.py [-h] [--update-docs] [--debug]

An API-enabled AI assistant for NIMBL documentation.

options:
  -h, --help     show this help message and exit
  --update-docs  Update the local documentation from the git repository before starting.
  --debug        Display debug messages.
```

### Update document and run application

Update document from userguide(https://github.com/bbtechhive/userguide) and run application.

```shell
python app_gemini.py --update-docs
```

### Only run application

Only run application.

```shell
python app_gemini.py
```

## Example

1. Run `bbrootsvc`.
```shell
$ bbrootsvc -n root
```
2. Run `bbnmssvc`.
```shell
$ bbnmssvc -n nms -r http://localhost:27182 -rs localhost:5514
```
3. Run `app_gemini.py` and get device list from NIMBL.
```shell
$ python app_gemini.py --update-docs
Cloning into '/home/<USER>/workshop/mnms/examples/aiagent/userguide_repo'...
remote: Enumerating objects: 1491, done.
remote: Counting objects: 100% (342/342), done.
remote: Compressing objects: 100% (267/267), done.
Receiving objects: remote: Total 1491 (delta 138), reused 144 (delta 74), pack-reused 1149 (from 1)
Receiving objects: 100% (1491/1491), 42.26 MiB | 8.52 MiB/s, done.
Resolving deltas: 100% (638/638), done.
Updating files: 100% (209/209), done.

Initializing the API-enabled AI Assistant (using Google Gemini)...
Initialization complete! You can start making requests (type 'exit' or 'q' to quit).

Your request: get device list

AI: I will retrieve the list of all devices.

Executing: GET http://localhost:27182/api/v1/devices
{
  "00-60-E9-1E-93-D4": {
    "mac": "00-60-E9-1E-93-D4",
    "modelname": "EHG7508-4SFP",
    "timestamp": "1750232395",
    "scanproto": "agent",
    "ipaddress": "*********",
    "netmask": "*************",
    "gateway": "0.0.0.0",
    "hostname": "switch12",
    "kernel": "8.10",
    "ap": "EHG7508-4SFP Application: V8.10",
    "scannedby": "danielNms",
    "isonline": true,
    "topologyproto": "agent",
    "svcdiscovia": "ethernet",
    "capabilities": {
      "agent": true,
      "gwd": true
    },
    "snmpSupported": "1",
    "snmpEnabled": "1",
    "gwdModelName": "EHG7508-4SFP",
    "trapSetting": [
      {
        "serverIp": "*********",
        "serverPort": "5162",
        "community": "public"
      }
    ],
    "syslogSetting": {
      "logToServer": "1",
      "serverIp": "*********",
      "serverPort": "4252",
      "logLevel": "1",
      "logToFlash": "1"
    },
    "motorinfo": {
      "profinet": {}
    },
    "gpsInfo": {
      "enabled": "0",
      "lasttime": "N/A",
      "latitude": "N/A",
      "longitude": "N/A"
    },
    "supported": [
      "reboot",
      "snmp",
      "snmpTrap",
      "syslog",
      "network",
      "firmware",
      "topology",
      "ssh",
      "user",
      "saveRunning",
      "portAndPowerInfo"
    ],
    "agentVersion": "v1.0.4"
  },
  "00-60-E9-27-E3-39": {
    "mac": "00-60-E9-27-E3-39",
    "modelname": "EHG7508",
    "timestamp": "1750232400",
    "scanproto": "gwd",
    "ipaddress": "***********00",
    "netmask": "*************",
    "gateway": "***********",
    "hostname": "somedevice",
    "kernel": "99.7",
    "ap": "EHG7508-8PoE Application: V99.07",
    "scannedby": "danielNms",
    "isonline": true,
    "topologyproto": "snmp",
    "svcdiscovia": "udp",
    "capabilities": {
      "gwd": true
    },
    "snmpSupported": "0",
    "snmpEnabled": "0",
    "gwdModelName": "EHG7508",
    "syslogSetting": {},
    "motorinfo": {
      "profinet": {}
    },
    "gpsInfo": {},
    "supported": [
      "reboot",
      "beep",
      "snmp",
      "snmpTrap",
      "syslog",
      "network",
      "firmware"
    ]
  },
  "11-22-33-44-55-66": {
    "mac": "11-22-33-44-55-66",
    "timestamp": "1750232400",
    "syslogSetting": {},
    "motorinfo": {
      "profinet": {}
    },
    "gpsInfo": {}
  }
}
Your request: Organize device list to table
+-----------------+-----------------+-------------+-------------+-----------+----------+--------+-------------+-----------------+
|       mac       |    modelname    |  timestamp  | scanproto  | ipaddress | netmask  | gateway |   kernel   |     hostname     |
+-----------------+-----------------+-------------+-------------+-----------+----------+--------+-------------+-----------------+
| 00-60-E9-1E-93-D4 | EHG7508-4SFP    |  1750232395  | agent       | *********     | ************* | 0.0.0.0     |     8.10  |      switch12  |
| 00-60-E9-27-E3-39 | EHG7508         |  1750232400  | gwd         | ***********00 | ************* | *********** |     99.7  |    somedevice  |
| 11-22-33-44-55-66 |                 |              |             |               |               |             |           |                |
+-----------------+-----------------+-------------+-------------+-----------+----------+--------+-------------+-----------------+
Your request:
```