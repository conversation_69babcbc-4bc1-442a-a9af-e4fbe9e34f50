## 6 Script commands

Following are the commands used by the users to perform different operations on a device.

### 6.1 Authentication Command

The command is configuring a user with the MAC address, granting them administrative privileges.

```
Usage : config user [mac address] [username] [password]
    [mac address] : target device mac address
    [username]    : target device login username
    [password]    : target device login password

Example :

  config user AA-BB-CC-DD-EE-FF admin default
```

### 6.2 MTD Erase Command

Erase MTD or reset to default,before executing this command must need to authenticate the respective device of the above command

```
Usage : mtderase [mac address]
    [mac address] : target device mac address

Example :

  mtderase AA-BB-CC-DD-EE-FF
```

### 6.3 Beep Command

Ask the specific device make sound

```
Usage : beep [mac address]
    [mac address] : target device mac address

Example :

  beep AA-BB-CC-DD-EE-FF
```

### 6.4 Reset Command

Reboot the specific device.

```
Usage : reset [mac address]
    [mac address] : target device mac address

Example :

  reset AA-BB-CC-DD-EE-FF
```

### 6.5 Network Command

```
Usage : config network set [mac address] [ip address] [new ip] [mask] [gateway] [hostname] [dhcp]
    [mac address]     : target device mac address.
    [ip address]      : target device ip address.
    [new ip]          : New IP address to assign to the device.
    [mask]            : Subnet mask for the device's network.
    [gateway]         : Default gateway for the device.
    [hostname]        : Hostname to assign to the device.
    [dhcp]            : Option to enable DHCP.
                        0 - Disable DHCP
                        1 - Enable DHCP

Example :

  config network set AA-BB-CC-DD-EE-FF ********* *********0 *********** 0.0.0.0 switch 0
```

### 6.6 Scan device Commands

```
Use different protocols to scan all devices.
Usage : scan [protocol]
    [protocol]    : use gwd/snmp to scan all devices.

Example :

  scan gwd
  scan snmp
```

### 6.7 GWD Commands

Configure GWD commands.

```
Usage : gwd beep [mac address]
    [mac address] : target device mac address

Example :

  gwd beep AA-BB-CC-DD-EE-FF
```

```
Usage : gwd reset [mac address]
    [mac address] : target device mac address

Example :

  gwd reset AA-BB-CC-DD-EE-FF
```

```
Usage : gwd config network set [mac address] [ip address] [new ip] [mask] [gateway] [hostname]
    [mac address]     : target device mac address.
    [ip address]      : target device ip address.
    [new ip]          : new IP address to assign to the device.
    [mask]            : subnet mask for the device's network.
    [gateway]         : default gateway for the device.
    [hostname]        : hostname to assign to the device.

Example :

  gwd config network set AA-BB-CC-DD-EE-FF ********* *********0 *********** *********** switch.
  gwd config network set AA-BB-CC-DD-EE-FF ********* 0.0.0.0 *********** 0.0.0.0 switch.

```

> Note:For gwd config network set via dhcp enable need todo new ip field must be pass 0.0.0.0 from script or cli(bbctl) section as shown above example.

```
Usage : gwd firmware update [mac address] [file url]
    [mac address] : target device mac address
    [file url]    : file url

Example :

  gwd firmware update AA-BB-CC-DD-EE-FF https://www.atoponline.com/.../EHG750X-K770A770.zip
  gwd firmware update 00-60-E9-21-2B-9E http://*********:27182/api/v1/files/EHG750X-K770A770.dld
```

```
Usage :gwd mtderase [mac address]
    [mac address] : target device mac address

Example :

  gwd mtderase AA-BB-CC-DD-EE-FF
```
> Note: Erase MTD or reset to default via gwd, before executing this command must need to authenticate the respective device
> Note: The URL should not use `localhost`, because the URL will be passed to different devices, which may cause IP address represented by `localhost` to change.

### 6.8 Configure Device Setting Commands

Configure the specific device setting.

```
Usage : config network set [mac address] [ip address] [new ip] [mask] [gateway] [hostname] [dhcp]
    [mac address]     : target device mac address.
    [ip address]      : target device ip address.
    [new ip]          : new IP address to assign to the device.
    [mask]            : subnet mask for the device's network.
    [gateway]         : default gateway for the device.
    [hostname]        : hostname to assign to the device.
    [dhcp]            : option to enable DHCP.
                        0 - Disable DHCP
                        1 - Enable DHCP

Example :

  config network set AA-BB-CC-DD-EE-FF ********* *********0 *********** 0.0.0.0 switch 0
```

```
Usage : config syslog set [mac address] [status] [server ip] [server port] [server level] [log to flash]
    [mac address] : target device mac address
    [status]      : syslog status enable(1)/disable(0)
    [server ip]   : syslog server ip address
    [server port] : syslog server port
    [server level]: syslog server log level
    [log to flash]: syslog log to flash enable(1)/disable(0)

Example :

  config syslog set AA-BB-CC-DD-EE-FF 1 ********* 5514 1 1
```

```
Usage : config syslog get [mac address]
    [mac address] : target device mac address

Example :

  config syslog get 00-60-E9-18-3C-3C
```

```
Usage : config local syslog path [path]
    [path]        : local syslog path

Example : This is root command for this need to execute in flag(-ck) kind field add root

  config local syslog path tmp/log (path is the same)
  config local syslog path /tmp1/log (path is on the the uppermost directory)
```

```
Usage : config local syslog maxsize [maxsize]
    [maxsize]     : local syslog file maxsize size

Example : This is root command for this need to execute in flag(-ck) kind field add root

  config local syslog maxsize 100
```

```
Usage : config local syslog compress [compress]
    [compress]     : would be compressed

Example : This is root command for this need to execute in flag(-ck) kind field add root

  config local syslog compress true
```

```
Usage : config local syslog read [start date] [start time] [end date] [end time] [max line]
    [start date]   : search syslog start date
    [start time]   : search syslog start time
    [end date]     : search syslog end date
    [end time]     : search syslog end time
    [max line]     : max lines, if without max line, that mean read all of lines

Example : This is root command for this need to execute in flag(-ck) kind field add root

  config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00
  config local syslog read 5
  config local syslog read 2023/02/21 22:06:00 2023/02/22 22:08:00 5
```

```
Usage : config local syslog remote [server address]
		[server address] : remote syslog server address

	Example : This is root command for this need to execute in flag(-ck) kind field add root

	config local syslog remote :5514
```

```
	Usage : config local syslog backup-after-forward [enable]
		[enable]      : config local syslog bak after fwd, true/false

	Example : This is root command for this need to execute in flag(-ck) kind field add root

	config local syslog backup-after-forward true
```

```
Usage : config save [mac address]
    [mac address] : target device mac address

Example :

  config save AA-BB-CC-DD-EE-FF
```

### 6.9 Switch commands

Switch commands provide the ability to configure target devices with CLI if the device telnet is enabled.
Note that before executing these commands first need to know the exact switch commands of the respective models, all models will not support same commands model to model devices varies the commands uses

```
Usage : switch [mac address] [cli cmd...]
    [mac address] : target device mac address
    [cli cmd...]  : target device cli command

Example : For EHG75xx device getting configured ip address

  switch AA-BB-CC-DD-EE-FF show ip
```

```
Usage : switch [mac address] snmp trap [server ip] [community] [server port]
    [mac address]  : target device mac address
    [server ip]    : set server ip address
    [server port]  : set server port
    [community]    : community string

Example : For EHG75xx device configuring the trap setting

  switch AA-BB-CC-DD-EE-FF snmp trap ********* public 162
```

```
Usage : switch [mac address] snmp enable
    [mac address]  : target device mac address

Example : For EHG75xx device configuring snmp enable

  switch AA-BB-CC-DD-EE-FF snmp enable
```

```
Usage : switch config save [mac address]
    [mac address]  : target device mac address

Example :

  switch config save AA-BB-CC-DD-EE-FF
```

### 6.10 SNMP command

SNMP commands provide several SNMP related functions, such as get target’s oid value, set up SNMP options.

#### 6.10.1 SNMP read/write command

These commands allow you to get/set value from OID..

- Value types : Supported data types, used for snmp get and snmp set command
  - OctetString
  - BitString
  - SnmpNullVar
  - Counter
  - Counter64
  - Gauge
  - Opaque
  - Integer
  - ObjectIdentifie
  - IpAddress
  - TimeTicks

```
Usage : snmp enable [mac address]
    [mac address]  : target device mac address

Example :

  snmp enable AA-BB-CC-DD-EE-FF
```

```
Usage : snmp disable [mac address]
    [mac address]  : target device mac address

Example :

  snmp disable AA-BB-CC-DD-EE-FF
```

```
Usage : snmp trap add [mac address] [server ip] [server port] [community]
    [mac address] : target device mac address
    [server ip]   : set server ip address
    [server port] : set server port
    [community]   : community string

Example :

  snmp trap add AA-BB-CC-DD-EE-FF ********* 162 public
```

```
Usage : snmp trap del [mac address] [server ip] [server port] [community]
    [mac address] : target device mac address
    [server ip]   : set server ip address
    [server port] : set server port
    [community]   : community string

Example :

  snmp trap del AA-BB-CC-DD-EE-FF ********* 162 public
```

```
Usage : snmp get [ip address] [oid]
    [ip address]  : target device ip address
    [oid]         : target oid

Example :

  snmp get ********* *******.*******.0
```

```
Usage : snmp set [ip address] [oid] [value] [value type]
    [ip address]  : target device ip address
    [oid]         : target oid
    [value]       : would be set value
    [value type]  : would be set value type.

Example :

  snmp set ********* *******.*******.0 [www.atop.com.tw](http://www.atop.com.tw) OctetString
```

#### 6.10.2 SNMP option settings commands

These commands allow you to configure the SNMP options for both the global setting and individual targets. There are three main functions that you can perform:

##### ******** Set global SNMP communities

You can set the global SNMP communities that will be used by default if a target does not have individual SNMP options configured. To set the global SNMP options, use the following command:

```
Usage: snmp communities [mac]
	Read the device's SNMP communities and update the system.

    [mac]      : Device mac address

Example:

  snmp communities 00-60-E9-27-E3-39
```

##### ******** Update SNMP options from the target

You can read the SNMP options from the target and set them in your Nimbl system. Once set, any SNMP command sent to that target will use the configured SNMP options. To read the SNMP options from a target, use the following command:

```
Usage: snmp update community [mac] [read community] [write community]
  Update device’s SNMP communities manually.

    [mac]            : Device mac address
    [read community] : Device snmp read community
    [write community]: Device snmp write community

Example:

  snmp update community 00-60-E9-27-E3-39 public private
```

##### ******** Set individual target SNMP options

You can also manually set individual SNMP options for a target. To set the SNMP options for a target, use the following command:

```
Usage: snmp options [port] [community] [version] [timeout]
  Update global snmp options.

    [port]     : snmp listen port
    [community]: snmp community
    [version]  : snmp version
    [timeout]  : snmp timeout

Example:

  snmp options 161 public 2c 2
```

##### ******** Set snmp config syslog

You can set the manually syslog configuration via snmp for a target device.To set the syslog configuration via snmp for a target device, use the following command:

```
Usage: snmp config syslog set [MAC Address] [Enable] [Server IP] [Server Port] [Log Level] [Log to Flash]
		[mac address] : target device mac address
		[status]      : syslog status enable(1)/disable(2)
		[server ip]   : syslog server ip address
		[server port] : syslog server port
		[server level]: syslog server log level
		[log to flash]: syslog log to flash enable(1)/disable(2)

	Example:
		snmp config syslog set 00-60-E9-27-E3-39 1 ********* 1234 7 1
		snmp config syslog set 00-60-E9-27-E3-39 2 ********* 1234 7 2
```

##### ******** Get snmp config syslog

You can get the manually syslog configuration via snmp for a target device.To get the syslog configuration via snmp for a target device, use the following command:

```
	Usage: snmp config syslog get [MAC Address]
		[mac address] : target device mac address

	Example:
		snmp config syslog get 00-60-E9-27-E3-39
```

**NOTE:**

- Please make sure before run above snmp config syslog set and get commands first need to do the target device snmp enable and community string set via 'snmp communities macaddress'

### 6.11 Configure debug Setting Command

Below each command serves a specific purpose in managing and configuring the logging behavior of a system application.

**NOTE:**

- Nimbl provides several logging functionalities for debugging purposes.
- The log messages are not standard syslogs; they are application-specific logs for debugging Nimbl.
- It's important to note that these logs are distinct from syslogs.

```
Usage : debug log clear
    [clear] : This command will clear all existing logs in memory.

Example :

  debug log clear
```

```
Usage : debug log off
    [off] : This command will stop the logging of debug messages.

Example :

  debug log off
```

```
Usage : debug log pattern [pattern]
    [pattern] : The pattern accepts regular expressions, allowing you to filter debug messages
                based on their content

Example :

  debug log pattern ".*"
```

```
Usage : debug log output [output]
    [output] : This command sets the destination where debug messages will be written.

Example :

  debug log output stderr
```

### 6.12 Syslog Commands

Syslog commands are used for the log service, bblogsvc. Note that only commands with a specific service name can be run by the log service in the flag(-cc) client.

```
Usage : syslog config path [path]
  Set the log service configuration.

    [path]        : The path to the syslog file

Example :

  -cc log1 syslog config path /tmp/syslog.log
```

```
Usage : syslog config maxsize [maxsize]
  Set the log service configuration.

    [maxsize]     : Maximum size of the syslog file in MB

Example :

  syslog config maxsize 100
```

```
Usage : syslog config compress [compress]
  Set whether the log service backup file should be compressed after reaching the maximum size.
    [compress]    : true to compress the file, false otherwise

Example :

  syslog config compress true
```

```
Usage : syslog config backup-after-forward [enable]
  Set whether the log service should back up the logs after forwarding them to another syslog server.

    [enable]      : true to enable backup after forwarding, false to disable

Example :

  syslog config backup-after-forward true
  syslog config backup-after-forward flase
```

```
	Usage : syslog config severity-range-forward [min severity] [max severity]
	Set the log service severity range for forwarding logs to the remote syslog server.
		[min severity] : Minimum severity level to forward to the remote syslog server
		[max severity] : Maximum severity level to forward to the remote syslog server
	Example :
		syslog config severity-range-forward 0 1 // send emergency and alert
		syslog config severity-range-forward -1 5 // send all except notice and debug
		syslog config severity-range-forward -1 7 // send all
		syslog config severity-range-forward -1 -1 // send nothing
```

```
	Usage : syslog introduce
	Log service self-introduction to the remote syslog server in the following format:
    <6>[Timestamp] [Name] bblogsvc: log service is forward
  Example:
    <6>Sep  2 16:46:55 log1 bblogsvc: log service is forwarding
```

```
	Usage : syslog config introduce interval [interval]
	Set the interval for the log service self-introduction to the remote syslog server.
		[interval]    : Interval in seconds for the log service self-introduction, must be greater than 0
	Example :
		syslog config introduce interval 60
```

```
	Usage : syslog config get
	Retrieve the current log service settings into result.
```

```
Usage : syslog config remote [server address]
  Update remote server address.

    [server address] : remote syslog server address

Example :

  syslog config remote :5514
```

```
Usage : syslog list
  Retrieve the log service syslog file list.

Example :

  syslog list
```

```
Usage : syslog geturl [source filename] [spec]
  Get the syslog file content and return a URL. Max size is 10MB.

    [source filename]     : Log file name in the syslog path, can be listed by syslog list,
                            or use all to query all log files
    [spec]                : Spec of log file, can be start date, start time, end date, end time,
                            max line, if without max line, that means read all of the lines.

Example :

  syslog geturl syslog_mnms.log
  syslog geturl syslog_mnms.log 5
  syslog geturl syslog_mnms.log 2023/02/21 22:06:00 2023/02/22 22:08:00
  syslog geturl syslog_mnms.log 2023/02/21 22:06:00 2023/02/22 22:08:00 5
  syslog geturl all 2023/02/21 22:06:00 2023/02/22 22:08:00
  syslog geturl all 2023/02/21 22:06:00 2023/02/22 22:08:00 100
```

### 6.13 Firmware Upgrade Command

```
Usage : firmware update [mac address] [file url]
    [mac address] : target device mac address
    [file url]    : file url

Example :

  firmware update AA-BB-CC-DD-EE-FF https://www.atoponline.com/.../EHG750X-K770A770.zip
```

> Note: The URL should not use `localhost`, because the URL will be passed to different devices, which may cause IP address represented by `localhost` to change.

### 6.14 MQTT for Publish/Subscribe/Unsubscribe/List Topic Commands

We provide basic support for MQTT publish and subscribe messaging. Both MQTT broker and client run on the network service. When a message comes in, the message will be sent to the root service by syslog.

```
Usage : mqtt [mqttcmd] [topic] [data...]
    [mqttcmd]     : pub/sub/unsub/list
                    list is show all subscribe topic
    [tcp address] : would pub/sub/unsub broker tcp address
    [topic]       : topic name
    [data...]     : data is messages, only publish use it.

Examples :

  mqtt pub ************:1883 topictest "this is messages."
  mqtt sub ************:1883 topictest
  mqtt unsub ************:1883 topictest
  mqtt list
```

If there is an MQTT broker at `broker.emqx.io:1883`, we can use the command to public topic.

```shell
$ ./bbctl mqtt pub broker.emqx.io:1883 topictest "this is messages."
```

And we can use the command to subscribe topic. The Subscribed topic messages can be viewed in the log.

```shell
$ ./bbctl mqtt sub broker.emqx.io:1883 topictest
```

If we want to cancel the subscription, we can use the command to subscribe.

```shell
$ ./bbctl mqtt unsub broker.emqx.io:1883 topictest
```

### 6.15 OPC UA Setting Commands

We provide support for OPC UA which is the interoperability standard for the secure and reliable exchange of data in the industrial automation space and in other industries. It is platform independent and ensures the seamless flow of information among devices from multiple vendors.

#### 6.15.1 connect

```
Usage : opcua connect [url]
    [url]   : connect to url

Example :

  opcua connect opc.tcp://127.0.0.1:4840
```

#### 6.15.2 read

```
Usage : opcua read [node id]
    [node id]     : opcua node id

Example :

  opcua read i=1002
```

#### 6.15.3 browse

```
Usage : opcua browse [node id]
    [node id]     : opcua node id

Example :

  opcua browse i=85
```

#### 6.15.4 sub

```
Usage : opcua sub [node id]
    [node id]     : opcua node id

Example :

  opcua sub i=1002
```

#### 6.15.5 delete sub

```
Usage : opcua deletesub [sub id] [monitor id]
    [sub id]      : subscribe id
    [monitor id]  : monitored item id

Example :

  opcua deletesub 1 1
```

#### 6.15.6 close

```
Usage : opcua close

Example :

  opcua close
```

### 6.16 Wg commands

Use wg commands to control NIMBL root and network services WireGuard.

```
Usage : wg config interface addresses set [address] [address...]
    [address]    : set interface address

Example :

  -ck root wg config interface addresses set *********/24 ********/24
  wg config interface addresses set *********/32
```

```
Usage : wg config interface listenport set [port]
    [port]       : set interface listen port

Example :

  wg config interface listenport set 51820
```

```
Usage : wg config interface mtu set [mtu]
    [mtu]        : set interface mtu

Example :

  wg config interface mtu set 1420
```

```
Usage : wg config interface dns set [dns] [dns...]
    [dns]        : set interface dns

Example :

  wg config interface dns set *******
```

```
Usage : wg config interface set [address] [listenport...] [mtu...] [dns...]
    [root]       : run as root command
    [address]    : set interface address
    [listenport] : set interface listen port
    [mtu]        : set interface mtu
    [dns]        : set interface dns

Example :

  wg config interface set **********/32
```

```
Usage : wg config interface [timing[preup|postup|predown|postdown]] add [command]
    [timing]     : preup, postup, predown or postdown
    [command]    : run command while WireGuard preup, postup, predown or postdown

Example :

  wg config interface preup add echo preup
  wg config interface predown add echo predown
```

```
Usage : wg config interface [timing[preup|postup|predown|postdown]] delete [index]
    [timing]    : preup, postup, predown or postdown
    [index]      : the location of the command

Example :

  wg config interface preup delete 0
  wg config interface predown delete 1
```

```
Usage : wg config peer pubkey set [index] [pubkey]
    [index]      : the location of this peer in array
    [pubkey]     : set peer public key

Example :

  wg config peer pubkey set 0 1234567890
  -ck root wg config peer pubkey set 0 1234567890
```

```
Usage : wg config peer allowedips set [index] [address] [address...]
    [index]      : the location of this peer in array
    [address]    : set peer allowed ips

Example :

  wg config peer allowedips set 0 **********/32
```

```
Usage : wg config peer endpoint set [index] [endpoint]
    [index]      : the location of this peer in array
    [endpoint]   : the WireGuard server address

Example :

  wg config peer endpoint set ***************:55820
```

```
Usage : wg config peer persistentkeepalive set [index] [persistentkeepalive]
    [index]              : the location of this peer in array
    [persistentkeepalive]: peer persistent keepalive

Example :

  wg config peer persistentkeepalive set 0 25
```

```
Usage : wg config peer presharedkey set [index] [presharedkey]
    [index]       : the location of this peer in array
    [presharedkey]: peer preshared key

Example :

  wg config peer presharedkey set 0 1234567890
```

```
Usage : wg config peer add [pubkey] [allowedips] [endpoint] [persistentkeepalive] [presharedkey...]
    [pubkey]             : set peer public key
    [allowedips]         : set peer allowed ips
    [endpoint]           : set peer endpoint
    [persistentkeepalive]: set peer persistent keepalive
    [presharedkey]       : set peer preshared key

Example :

  wg config peer add 1234567890 **********/24 ***************:55820 30
```

```
Usage : wg config peer delete [index]
    [index]      : the location of this peer in array

Example :

  wg config peer delete 0
```

```
Usage : wg config generate

Example :

  wg config generate
```

```
Usage : wg [operate[start|stop]]
    [operate]    : start or stop

Example :

  wg start
  wg stop
```

### 6.17 Agent commands

Use agent commands to control devices.

#### 6.17.1 Reset command

Use the agent command to reset the device.

```
Usage : agent reset [mac address]
    [mac address] : target device mac address

Example :

  agent reset AA-BB-CC-DD-EE-FF
```

#### 6.17.2 Snmp command

Use the agent command to control snmp.

```
Usage : agent snmp enable [mac address] [option]
    [mac address] : target device mac address
    [option]      : 1 or 0

Example :

  agent snmp enable AA-BB-CC-DD-EE-FF 1
```

#### 6.17.3 Upgrade device firmware command

Use the agent command to upgrade firmware.

```
Usage : agent firmware update [mac address] [file url]
    [mac address] : target device mac address
    [file url]    : update file url, can put it in file server(example: http://root_or_client_ip_and_port/api/v1/files/)

Example :

  agent firmware update AA-BB-CC-DD-EE-FF http://*************:27183/api/v1/files/ABC.dld
```

> Note: The URL should not use `localhost`, because the URL will be passed to different devices, which may cause IP address represented by `localhost` to change.

#### 6.17.4 Upgrade device agent client command

Use the agent command to upgrade the agent itself. The `agentclient` executable file will contain dependent libraries, so the `agentclient` executable file of each machine is different. When using the command, please be careful whether the `agentclient` execution file corresponds to the right machine.

```
Usage : agent agentclient upgrade [mac address] [file url] [checksum]
    [mac address] : target device mac address
    [file url]    : update file url, can put it in file server(example: http://root_or_client_ip_and_port/api/v1/files/)
    [checksum]    : use sha256 to verify update file

Example :

  agent agentclient upgrade AA-BB-CC-DD-EE-FF http://*************:27183/api/v1/files/agentclient
     12c0ced1a84158357525378ebcfa31967dd9bb3a32600602714bfe2222a1d609
```

#### 6.17.5 Beep command

Use the agent command to beep.

```
Usage : agent beep [mac address]
    [mac address] : target device mac address

Example :

  agent beep AA-BB-CC-DD-EE-FF
```

#### 6.17.6 agent config syslog command

Use the agent command to set the syslog setting.

```
Usage : agent config syslog set [mac address] [enable] [server ip] [server port]
    [log level] [log to flash]
    [mac address] : target device mac address
    [enable]      : enable syslog to remote
    [server ip]   : set syslog server ip
    [server port] : set syslog server port
    [log level]   : set syslog log level
    [log to flash]: set syslog lot to flash

Example :

  agent config syslog set AA-BB-CC-DD-EE-FF 1 ************* 5514 8 1
```

Use the agent command to get the syslog setting.

```
Usage : agent config syslog get [mac address]
    [mac address] : target device mac address

Example :

  agent config syslog get AA-BB-CC-DD-EE-FF
```

#### 6.17.7 Snmp trap command

Use the agent command to set snmp trap setting.(only support snmp v2)

```
Usage : agent snmp trap [trap option] [mac address] [server ip] [server port] [community]
    [mac address] : target device mac address
    [trap option] : set trap add/del
    [server ip]   : set trap server ip
    [server port] : set trap server port
    [community]   : set trap community

Example :

  agent snmp trap add AA-BB-CC-DD-EE-FF ************* 5162 public
  agent snmp trap del AA-BB-CC-DD-EE-FF ************* 5162 public
```

Use agent to get snmp trap setting.

```
Usage : agent snmp trap get [mac address]
    [mac address] : target device mac address

Example :

  agent snmp trap get AA-BB-CC-DD-EE-FF
```

#### 6.17.8 Network command

Use the agent command to set network settings.

```
Usage : agent config network set [mac address] [ip] [mask] [gateway] [hostname] [dhcp]
    [mac address] : target device mac address
    [dhcp]        : set dhcp
    [ip]          : set ip address
    [mask]        : set netmask
    [gateway]     : set gateway
    [hostname]    : set hostname

Example :

  agent config network set AA-BB-CC-DD-EE-FF *********** ************* 0.0.0.0 switch 1
  agent config network set AA-BB-CC-DD-EE-FF *********** ************* *********** switch 0
```

#### 6.17.9 Send device information command

Use the agent command to send device information to NIMBL.

```
Usage : agent devinfo send [mac address]
    [mac address] : target device mac address

Example :

  agent devinfo send AA-BB-CC-DD-EE-FF
```

#### 6.17.10 Send topology information command

Use the agent command to send topology information to NIMBL.

```
Usage : agent topologyinfo send [mac address]
    [mac address] : target device mac address

Example :

  agent topologyinfo send AA-BB-CC-DD-EE-FF
```

#### 6.17.11 Openvpn commands

Use the agent command to set openvpn settings. Only some cellular devices on the Blackbear devices support openvpn and can use these openvpn commands.

##### ********* Openvpn general setting

Use the agent command to set openvpn general settings.

```
Usage :
  agent openvpn set [mac address] [mode] [enable] [protocol] [port] [cipher] [hash] [compress]
  [auth mode] [Push LAN to clients] [virtual IP] [remote IP/FQDN] [local virtual IP] [remote virtual IP]

  # server, authentication mode is SSL/TLS
  agent openvpn set [mac address] server [enable] [protocol] [port] [cipher] [hash] [compress]
  0 [Push LAN to clients] [virtual IP]

  # server, authentication mode is static key
  agent openvpn set [mac address] server [enable] [protocol] [port] [cipher] [hash] [compress]
  1 [Push LAN to clients] [local virtual IP] [remote virtual IP]

  # client, authentication mode is SSL/TLS
  agent openvpn set [mac address] client [enable] [protocol] [port] [cipher] [hash] [compress]
  0 [remote IP/FQDN]

  # client, authentication mode is static key
  agent openvpn set [mac address] client [enable] [protocol] [port] [cipher] [hash] [compress]
  1 [remote IP/FQDN] [local virtual IP] [remote virtual IP]

    [mac address] : target device mac address
    [enable]      : enable openvpn server or client mode, '0' or '1'
    [mode]        : set openvpn server or client, 'server' or 'client'
    [protocol]    : set openvpn protocol, '0' or '1'(0:udp, 1:tcp)
    [cipher]      : set openvpn encryption cipher, '0' to '4'(0:blowfish, 1:AES256,
                    2:AES192, 3:AES128, 4:disable)
    [hash]        : set openvpn hash algorithm, '0' to '4'(0:SHA1, 1:MD5, 2:SHA256,
                    3:SHA512, 4:disable)
    [compress]    : set openvpn compression, '0' to '2'(0:LZ4, 1:LZO, 2:disable)
    [auth mode]   : set openvpn authentication mode, '0' or '1'(0:SSL/TLS, 1:static
                    key),If the 'auth mode' set 'SSL/TLS', the 'server mode' just can set 'virtual IP'.
                    If the 'auth mode' set 'static key', the 'server mode' can set 'local virtual IP'
                    and 'remote virtual IP'. If the 'auth mode' set 'SSL/TLS', the 'client mode' just
                    can set 'remote IP/FQDN'. If the 'auth mode' set 'static key', the 'client mode' can
                    set 'local virtual IP' and 'remote virtual IP'.
    [Push LAN to clients] : set openvpn push the LAN port subnet to the OpenVPN remote
                            client, '0' or '1'(0:disable, 1:enable)
    [virtual IP]          : set openvpn virtual ip, only support server mode
    [remote IP/FQDN]      : set openvpn remote IP/FQDN, only support client mode
    [local virtual IP]    : set openvpn local virtual IP, only support 'auth mode' is 'static key'
    [remote virtual IP]   : set openvpn remote virtual IP, only support 'auth mode' is 'static key'

Example :

  # server, authentication mode is SSL/TLS
  agent openvpn set AA-BB-CC-DD-EE-FF server 1 udp 1194 0 0 2 0 0 ********
  # server, authentication mode is static key
  agent openvpn set AA-BB-CC-DD-EE-FF client 1 udp 1194 0 0 2 0 *********00
  # client, authentication mode is SSL/TLS
  agent openvpn set AA-BB-CC-DD-EE-FF server 1 udp 1194 0 0 2 1 1 ******** ********
  # client, authentication mode is static key
  agent openvpn set AA-BB-CC-DD-EE-FF client 1 udp 1194 0 0 2 1 *********00 ******** ********
```

##### ********* Openvpn keys

Use agent to generate openvpn keys, if the 'client name' field is empty, it will generate 'ca', 'server' and 'static' key. if the 'client name' field is not empty, it will generate 'client' key.

```
Usage : agent openvpn keys generate [mac address] [country code] [country Name]
  [city] [organization] [organizational Unit] [email Address] [client name]
    [mac address]         : target device mac address
    [country code]        : set country code, 2 word
    [country Name]        : set country name
    [city]                : set city
    [organization]        : set organization
    [organizational Unit] : set organizational unit
    [email Address]       : set email address
    [client name]         : set client openvpn key name

Example :

  agent openvpn keys generate AA-BB-CC-DD-EE-FF TW Taiwan Taipei <NAME_EMAIL>
  agent openvpn keys generate AA-BB-CC-DD-EE-FF TW Taiwan Taipei <NAME_EMAIL> client1
```

##### ********* Upload openvpn keys to the agent client

Use the agent command to upload openvpn keys to the agent client.

```
Usage : agent openvpn keys upload [mac address] [keys] [url] [sha256]
    [keys]        : set option, 'ca', 'serverCert', 'serverKey', 'clientCert', 'clientKey', 'staticKey' and 'dh'
    [mac address] : target device mac address
    [url]         : set file url
    [sha256]      : set file sha256

Example :

  agent openvpn keys upload AA-BB-CC-DD-EE-FF ca http://*************:27182/api/v1/files/ca.crt
    3c933f3374c95451ea936df3bc009ed7df98f24cd0a09c40e6603d115d685e38
  agent openvpn keys upload AA-BB-CC-DD-EE-FF dh http://*************:27182/api/v1/files/dh2048.pem
    73ef04589be955555771d6beeed930251fbb98acc446096d2844861860609eb1
```

> Note: The URL should not use `localhost`, because the URL will be passed to different devices, which may cause IP address represented by `localhost` to change.

##### ********* Download openvpn keys to the NIMBL

Use the agent command to download openvpn keys to the NIMBL api '/api/v1/agent/openvpn/keys'

```
Usage : agent openvpn keys download [mac address] [client name]
    [mac address] : target device mac address
    [client name] : set client openvpn key name

Example :

  agent openvpn keys download AA-BB-CC-DD-EE-FF client1
```

##### ********* Get openvpn keys from the NIMBL

Get openvpn keys from the NIMBL api '/api/v1/agent/openvpn/keys'

```
Usage : agent openvpn keylist [client name]
    [client name] : set client openvpn key name

Example :

  agent openvpn keylist client1
```

##### ********* Start/stop openvpn status

Use the agent command to start/stop openvpn status.

```
Usage : agent openvpn status [mac address] [option]
    [mac address] : target device mac address
    [option]      : set openvpn status, 'start' or 'stop'

Example :

  agent openvpn status AA-BB-CC-DD-EE-FF start
  agent openvpn status AA-BB-CC-DD-EE-FF stop
```

#### 6.17.12 Ipsec command

Use the agent command to set Ipsec settings

```
Usage : agent ipsec set [mac address] [enable] [peer addr] [remote subnet] [local subnet]
  [conn type] [share key] [p1 mode] [p1 dh group] [p1 encrypt] [p1 auth] [p1 lifetime] [p2 proto]
  [p2 fwd secrecy] [p2 encrypt] [p2 auth] [p2 lifetime] [dpd action] [dpd interval] [dpd timeout]

    [mac address]    : target device mac address
    [enable]         : set ipsec enable, '0' or '1'(0:disable, 1:enable)
    [peer addr]      : set Peer Address, 'none' or 'x.x.x.x'
    [remote subnet]  : set Remote Subnet, 'none' or 'x.x.x.x/24'
    [local subnet]   : set Local Subnet, 'none' or 'x.x.x.x/24'
    [conn type]      : set Connection Type, '0' or '1'(0:Tunnel, 1:Transport)
    [share key]      : set Shared Key
    [p1 mode]        : Phase 1 SA (ISAKMP) Mode, '0' or '1'(0:Main, 1:Aggressive)
    [p1 dh group]    : Phase 1 SA (ISAKMP) DH Group, '0' or '1'(0:Group-2(1024 bit), 1:Group-5(1536 bit))
    [p1 encrypt]     : Phase 1 SA (ISAKMP) Encryption Algorithm, '0' or '1'(0:3DES, 1:AES-128)
    [p1 auth]        : Phase 1 SA (ISAKMP) Authentication Algorithm, '0' or '1'(0:MD5, 1:sha1)
    [p1 lifetime]    : Phase 1 SA (ISAKMP) SA Life Time, the default SA lifetime is 10800 seconds.
                       The configurable range for SA Life Time is between 300 to 86400 seconds.
    [p2 proto]       : Phase 2 SA Protocol, '0' or '1'(0:ESP, 1:AH)
    [p2 fwd secrecy] : Phase 2 SA Perfect Forward Secrecy, '0' to '2'(0:Disable, 1:Group-2(1024 bit),
                       2:Group-5(1536 bit))
    [p2 encrypt]     : Phase 2 SA Encryption Algorithm, '0' or '1'(0:3DES, 1:AES-128)
    [p2 auth]        : Phase 2 SA Authentication Algorithm, '0' or '1'(0:MD5, 1:sha1)
    [p2 lifetime]    : Phase 2 SA SA Life Time, , the default SA Life Time is 3600 seconds.
                       The configurable range for SA Life Time is between 300 to 86400 seconds.
    [dpd action]     : Dead Peer Detection Settings Action, DPD Action, '0' to '3'(0:None, 1:Hold,
                       2:Restart, 3:Clear)
    [dpd interval]   : DPD Interval. The DPD interval can be ranged from 1 to 65535 seconds.
                       The default value for DPD Interval is 30 seconds.
    [dpd timeout]    : DPD Timeout, the DPD Timeout value range from 1 to 65535. The default
                       value of DPD Timeout is 120 seconds.

Example :

  agent ipsec set AA-BB-CC-DD-EE-FF 1 none none none 0 secrets 0 0 0 0 10800 0 0 0 0 3600 1 30 120
  agent ipsec set AA-BB-CC-DD-EE-FF 1 *********00 ***********/24 *********/24 1 secrets 1 1 1 1 3600 1
    1 1 1 3600 1 30 120
```

Use the agent command to start/stop Ipsec status.

```
Usage : agent ipsec status [mac address] [option]
    [mac address] : target device mac address
    [option]      : set openvppn status, 'start' or 'stop'

Example :

  agent ipsec status AA-BB-CC-DD-EE-FF start
  agent ipsec status AA-BB-CC-DD-EE-FF stop
```

#### 6.17.13 Save running config command

Use the agent command to save running config to device.

```
Usage : agent config save [mac address]
    [mac address] : target device mac address

Example :

  agent config save AA-BB-CC-DD-EE-FF
```

#### 6.17.14 SSH tunnel commands

Start reverse ssh tunnel.
```
Usage: agent ssh reverse start [mac] [server hostname] [listen port] [remote port] [ssh server port]
    [mac]             : device mac address
    [server hostname] : server(root machine) hostname
    [listen port]     : listen port on root machine where the SSH server listens for connections
    [remote port]     : remote port on the client machine
    [ssh server port] : ssh server port

Example:

  agent ssh reverse start AA-BB-CC-DD-EE-FF ******* 12345 443 22
```

Stop ssh reversing in the device.
```
Usage: agent ssh reverse stop [mac] [server hostname] [listen port]
    [mac]          : device mac address
    [server hostname] : server(root machine) hostname
    [listen port]  : listen port on root machine where the SSH server listens for connections

Example:
  agent ssh reverse stop AA-BB-CC-DD-EE-FF ******* 12345
```

Retrieve the last reverse ssh tunnel status after running start/stop command.
```
Usage: agent ssh reverse status [mac]
    [mac]          : device mac address

Example:

  agent ssh reverse status AA-BB-CC-DD-EE-FF
```

Start a ssh tunnel between device web server and Root. Nimbl will handshake with the device to get details to start the tunnel.
Note that please make sure the device has the ability to access Root's ssh server.
```
Usage: agent ssh reverse websrv [mac]
    [mac]          : device mac address

Example:

  agent ssh reverse websrv AA-BB-CC-DD-EE-FF
```

Retrieve all ssh reverse connections on the device in json format.
```
Usage: agent ssh reverse status [mac]
    [mac]          : device mac address

Example:

  agent ssh reverse status AA-BB-CC-DD-EE-FF
```

Close an SSH tunnel connection with the specified port the SSH server is listening on, this can only be run under kind(-ck) is root.
```
Usage: ssh tunnel close [port]
    [port]     : port number

Example:
  ssh tunnel close 2222
```

Send an HTTP request to the SSH tunnel,this can only be run under kind(-ck) is root.
```
Usage: ssh tunnel fetch [port]
    [port]     : port number

Example:
  ssh tunnel fetch 2222
```

List all SSH tunnels,this can only be run under kind(-ck) is root.
```
Usage: ssh tunnels list

Example:
  ssh tunnels list
```

#### 6.17.15 Port command

Use agent to set port enable setting.

```
Usage : agent config port enable [mac address] [port name] [enable]
    [mac address] : target device mac address
    [port name]   : portname
    [enable]      : 1 or 0

Example :

  agent config port enable AA-BB-CC-DD-EE-FF port1 1
  agent config port enable AA-BB-CC-DD-EE-FF port1 0
```

#### 6.17.16 Device user commands

Use agent to set device user setting.

```
Add new user. Unable to add an account with 'admin' name.
Usage : agent config user add [mac address] [username] [passwd] [permission]
    [mac address] : target device mac address
    [username]    : username
    [passwd]      : password
    [permission]  : user or admin

Example :

  agent config user add AA-BB-CC-DD-EE-FF daniel daniel user
  agent config user add AA-BB-CC-DD-EE-FF daniel daniel admin
```

```
Edit user password.
Usage : agent config user edit [mac address] [username] [passwd] [new passwd]
    [mac address] : target device mac address
    [username]    : username
    [passwd]      : password
    [new passwd]  : new password

Example :

  agent config user edit AA-BB-CC-DD-EE-FF daniel daniel default
```

```
Delete user. Unable to delete an account with 'admin' name.
Usage : agent config user del [mac address] [username] [passwd]
    [mac address] : target device mac address
    [username]    : username
    [passwd]      : password

Example :

  agent config user del AA-BB-CC-DD-EE-FF daniel daniel
```

#### 6.17.17 GPS command

Use agent to set GPS setting.

```
Set GPS enable setting. Only some cellular devices have this feature.
Usage : agent config gps enable [mac address] [enable]
    [mac address] : target device mac address
    [enable]      : 1 or 0

Example :

  agent config gps enable AA-BB-CC-DD-EE-FF 1
  agent config gps enable AA-BB-CC-DD-EE-FF 0
```

#### 6.17.18 Send port and power information command

Use the agent command to send port and power information to the Nimbl network service.

```
Usage : agent portpwinfo send [mac address]
    [mac address] : target device mac address

Example :

  agent portpwinfo send AA-BB-CC-DD-EE-FF
```

#### 6.17.19 MtdErase command

The device's Reset to default settings(Factory default).

```
Usage : agent mtderase [mac address]
    [mac address] : target device mac address

Example :

  agent mtderase AA-BB-CC-DD-EE-FF
```

#### 6.17.20 Alarm command

Set port alarm trigger.

```
Usage : agent alarm port set [mac address] [alarm trigger] [port number]
    [mac address]   : target device mac address
    [alarm trigger] : set alarm trigger method, '0' to '3',(0:disable, 1:link down, 2:link up, 3:link down&link up)
    [port number]   : set port number, '1' to 'X' or 'all', 'X' may vary depending on the device, 'all' set all port

Example :

  agent alarm port set AA-BB-CC-DD-EE-FF 2 1
  agent alarm port set AA-BB-CC-DD-EE-FF 3 all
```

Get alarm status.

```
Usage : agent alarm status [mac address]
    [mac address]   : target device mac address

Example :

  agent alarm status AA-BB-CC-DD-EE-FF
```

#### 6.17.21 Token command

Update agent token manually.

```
Usage : agent token refresh [mac address]
    [mac address] : target device mac address

Example :

  agent token refresh AA-BB-CC-DD-EE-FF
```

### 6.18 Idps commands

#### 6.18.1 import command

```
Usage :-cc [clientname] idps rules import [url]
    [url]       : http://xxx
    [clientname]: client1
Description:
  import rules to client

Example :
  -cc client idps rules import http://xxx
```

#### 6.18.2 rules add command

```
Usage :-cc [clientname] idps rules add [category] [rule]
    [clientname]: client1
    [category]  : name
    [rule]      : rule
Description:
  add rules into category,only one rule allowed within command

Example :
  -cc client1 idps rules add icmp_category drop icmp $EXTERNAL_NET any <> $HOME_NET any (msg:"icmpv4 selftest drop";sid:789;)
```

#### 6.18.3 rules delete command

```
Usage : -cc [clientname]  idps rules delete [name]
    [name]      : selftest_icmp
    [clientname]: client1
Description:
  delete rules for client

Example :
  -cc client1 idps rules delete  selftest_icmp
  -cc client1 idps rules delete  selftest_tcp
```

#### 6.18.4 records delete command

```
Usage :-cc [clientname] idps records delete
  [clientname]: client1
			-f: file name of records(option)
			-d: date(format:2024-08-09)(option)
Description:
  delete record

Example :
  Description: delete file name: alert.log data:2024-08-12
  -cc client1 idps records delete -f alert.log -d 2024-08-12

  Description: delete all of file on 2024-08-12
  -cc client1 idps records delete -d 2024-08-12

  Description: delete all of file
  -cc client1 idps records delete
```

#### 6.18.5 records search command

```
Usage :-cc [clientname] idps records search
    [clientname]: client1
    -f: file name of record
    -st: start time (format:2006-01-02-15:04)
    -et end time (format:2006-01-02-15:04)
Description:
  search record

Example :
  -cc client1 idps records search -f alert.log -st 2024-08-14-15:04 -et 2024-09-14-15:04
```

### 6.19 msg command

#### 6.19.1 send syslog command

```
Usage : msg syslog send [facility] [severity] [tag] [message]
  Send a syslog message to the remote syslog server.
    [facility]    : syslog facility
    [severity]    : syslog severity
    [tag]         : syslog was sent from tag what feature name
    [message]     : would send messages

Example :
  msg syslog send 0 1 InsertDev "new device"
```

### 6.20 service command

#### 6.20.1 update command

Update service to latest version. When using this command, remember to add `-cc` or `-ck`.
```
Usage: -ck [root service kind] service update
       -cc [other service name] service update
    [root service kind]  : If your target is root service, requires to set kind(-ck) to root
    [other service name] : If your target is other services, requires to set service name(-cc)

Example:

	# For root service
	-ck root service update
	# For other service
	-cc nms1 service update
```

### 6.20.2 stop command

Stop service. When using this command, remember to add `-cc` or `-ck`.
```
Usage: -ck [root service kind] service stop
       -cc [other service name] service stop
    [root service kind]  : If your target is root service, requires to set kind(-ck) to root
    [other service name] : If your target is other services, requires to set service name(-cc)

Example:

	# For root service
	-ck root service stop
	# For other service
	-cc nms1 service stop
```

### 6.21 Disabling Commands in the Script Area

When writing scripts, you might need to disable certain lines of commands temporarily without deleting them. This can be useful for testing purposes or to prevent specific commands from executing. To do this, you can use the '#' symbol to comment out lines of commands. Lines that begin with '#' are ignored by the script interpreter.
suppose to re-enable the lines of commands means simply remove the '#' symbol at begin and run the script command for example as shown in the Figure 6.21.1.
![Alt text](images/image121.png)
Figure 6.21.1 Disabling Commands in the Script

