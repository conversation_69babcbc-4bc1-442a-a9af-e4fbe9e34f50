#!/usr/bin/env python3
"""
Production test for LLM Network Analyzer with real OpenAI API
Run this when you have a valid OpenAI API key set up
"""
import asyncio
import os
import json
from pathlib import Path

# Add current directory to path for imports
import sys
sys.path.append(str(Path(__file__).parent))

from llm_network_analyzer import LLMNetworkAnalyzer, NetworkAnalysisConfig

async def test_production_llm():
    """Test LLM analyzer with real OpenAI API"""
    print("🚀 Production LLM Network Analyzer Test")
    print("=" * 50)
    
    # Check for API key
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OPENAI_API_KEY environment variable not set")
        print("📖 Please follow the setup guide in OPENAI_SETUP_GUIDE.md")
        return False
    
    if api_key == "your-api-key-here":
        print("❌ Please set a real OpenAI API key")
        print("📖 Please follow the setup guide in OPENAI_SETUP_GUIDE.md")
        return False
    
    print(f"✅ OpenAI API key found (ends with: ...{api_key[-8:]})")
    
    # Configure analyzer
    config = NetworkAnalysisConfig(
        openai_api_key=api_key,
        model="gpt-4o",
        analysis_interval_minutes=30
    )
    
    analyzer = LLMNetworkAnalyzer(config)
    
    # Test complete analysis cycle
    print("\n🔄 Running complete analysis cycle with real OpenAI API...")
    print("⏳ This may take 10-30 seconds depending on network data size...")
    
    try:
        result = await analyzer.run_analysis_cycle()
        
        if result and "error" not in result:
            print("\n🎉 SUCCESS! LLM analysis completed")
            print(f"📊 Summary: {result.get('summary', 'N/A')[:100]}...")
            print(f"💡 Insights: {len(result.get('key_insights', []))} generated")
            print(f"🚨 Alerts: {len(result.get('alerts', []))} generated")
            print(f"📈 Confidence: {result.get('confidence', 0)*100:.1f}%")
            
            # Show sample insights
            insights = result.get('key_insights', [])
            if insights:
                print(f"\n🔍 Sample Insights:")
                for i, insight in enumerate(insights[:2], 1):
                    print(f"   {i}. {insight[:80]}...")
            
            # Show alerts if any
            alerts = result.get('alerts', [])
            if alerts:
                print(f"\n⚠️ Alerts:")
                for alert in alerts:
                    print(f"   • {alert}")
            
            return True
        else:
            print(f"\n❌ Analysis failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"\n💥 Analysis failed with exception: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("   • Check your OpenAI API key is valid")
        print("   • Verify you have sufficient API credits")
        print("   • Ensure the NIMBL MCP server is running")
        return False

async def test_api_connectivity():
    """Test basic OpenAI API connectivity"""
    print("\n🔌 Testing OpenAI API connectivity...")
    
    try:
        from openai import AsyncOpenAI
        
        api_key = os.getenv("OPENAI_API_KEY")
        client = AsyncOpenAI(api_key=api_key)
        
        # Simple test call
        response = await client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say 'API test successful'"}],
            max_tokens=10
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ API test successful: {result}")
        return True
        
    except Exception as e:
        print(f"❌ API connectivity failed: {e}")
        return False

async def main():
    """Main production test"""
    try:
        # Test 1: API connectivity
        api_ok = await test_api_connectivity()
        if not api_ok:
            print("\n💡 Fix API connectivity before running full analysis")
            return
        
        # Test 2: Full analysis
        success = await test_production_llm()
        
        if success:
            print("\n🎊 Production test completed successfully!")
            print("\n📈 Your LLM Network Analyzer is ready for production use!")
            print("\n🔄 To run continuous analysis:")
            print("   python llm_network_analyzer.py")
        else:
            print("\n⚠️ Production test had issues")
            print("📖 Check the logs above and OPENAI_SETUP_GUIDE.md")
            
    except Exception as e:
        print(f"\n💥 Production test failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
