# AI-Powered Network Analysis System

**[PROTOTYPE] AI network management tools with LLM integration**

This project demonstrates AI-assisted analysis of NIMBL network infrastructure using FastMCP integration. It provides automated analysis capabilities using OpenAI integration, focusing on a clean and maintainable approach.

**[WARNING] Status: Prototype/Development - Some features are in development**

## Architecture

```
┌─────────────────┐    ┌──────────────────┐     ┌─────────────────┐
│  AI Analyzer    │───▶│   MCP Server     │───▶│  NIMBL Network  │
│(llm_network_    │    │ (nimbl_mcp.py)   │     │     API         │
│ analyzer.py)    │    │                  │     │                 │
│                 │    │ • Timestamps     │     │ • Device Data   │
│ • OpenAI GPT-4o │    │ • Device Status  │     │ • Commands      │
│ • Change Track  │    │ • Commands       │     │ • Configuration │
│ • Analysis      │    │ • Resources      │     │ • Status        │
└─────────────────┘    └──────────────────┘     └─────────────────┘
         │                       
         ▼                       
┌─────────────────┐    ◀─────────┌─────────────────┐
│ JSON Reports    │              │ Report Manager  │
│ • Snapshots     │              │(view_reports.py)│
│ • Analysis      │              │                 │
│ • Summaries     │              │ • List Reports  │
└─────────────────┘    ─────────▶│ • Latest View   │
         ▲                       │ • Generate Sum  │
         │                       │ • Cleanup       │
         └───────────────────────└─────────────────┘
```

**Core Components:**
- **AI Analyzer** (`llm_network_analyzer.py`): Automated analysis with historical tracking
- **Report Manager** (`view_reports.py`): Report viewing, summary generation, and cleanup utilities
- **MCP Server** (`nimbl_mcp.py`): Network data access and tool integration

## System Components

### 1. **Enhanced MCP Server** (`nimbl_mcp.py`)
- **Temporal Intelligence**: Converts Unix timestamps to human-readable format
- **Device Status Classification**: Active/Recent/Stale/Old/Offline categorization
- **5 Network Management Tools**: Device queries, command execution, analysis
- **4 Contextual Resources**: Time, manuals, API information
- **LLM-Optimized Data**: Structured responses for AI consumption

### 2. **Automated Analysis System** (`llm_network_analyzer.py`)
- **OpenAI GPT-4o Integration**: AI analysis with structured JSON responses
- **JSON File-Based Storage**: Stores network snapshots and analysis results as timestamped JSON files
- **Device Change Detection**: Compares device configurations between snapshots
- **Basic Alerting**: Identifies potential issues and generates recommendations
- **Easy Report Management**: Human-readable JSON files organized by timestamp

### 3. **Report Management System** (`view_reports.py`)
- **Report Listing**: View all snapshots, analyses, and summaries with timestamps
- **Latest Analysis Display**: Quick access to most recent network insights and alerts
- **Manual Summary Generation**: Create trend analysis from multiple analysis reports
- **Automated Cleanup**: Remove old reports with configurable retention periods
- **Cross-Platform Support**: Works on Windows, Linux, and macOS environments

**[NOTE]**: Some advanced features like predictive analysis and complex trend detection are still in development.

## Why Use Network Analysis System

**Key Benefits:**

### **AI-Powered Insights**
- **Smart Analysis**: GPT-4o analyzes your network data and provides human-readable insights
- **Proactive Alerts**: Identifies potential issues before they become critical problems

### **Time-Saving Automation**
- **Automated Monitoring**: Schedule analysis to run hourly, daily, or on-demand
- **Historical Tracking**: Automatic change detection between network snapshots
- **Instant Summaries**: Generate comprehensive reports from weeks of data in seconds
- **No Manual Interpretation**: AI translates technical data into business-relevant insights

### **Comprehensive Reporting**
- **Multi-Format Reports**: JSON files for automation, human-readable summaries for teams
- **Trend Analysis**: Weekly and custom summaries show network health patterns over time
- **Confidence Scoring**: Each analysis includes confidence levels (0.0-1.0) for reliability assessment
- **Cross-Platform**: Works seamlessly on Windows, Linux, and macOS environments

### **Easy Integration & Maintenance**
- **No Database Setup**: Simple JSON file storage eliminates database complexity
- **Lightweight**: Minimal dependencies, easy installation and deployment
- **API Integration**: Built-in OpenAI integration with timeout handling and retry logic


### **Compared to Traditional Tools**

| Traditional Monitoring | AI Network Analysis System    |
| ---------------------- | ----------------------------- |
| Shows raw metrics      | Provides interpreted insights |
| Manual trend analysis  | Automated pattern detection   |
| Reactive alerts        | Proactive recommendations     |
| Technical outputs      | Business-readable reports     |
| Complex setup          | Simple file-based storage     |
| Expensive licensing    | OpenAI API costs only         |


## Quick Start

### Prerequisites
- Python 3.8+
- OpenAI API key (for automated analysis)
- Access to NIMBL API endpoint (running NIMBL instance)
- Network management permissions

**[IMPORTANT]**: Ensure your NIMBL instance is running and accessible before testing the tools.

### Installation

1. **Clone and navigate to the project**:
   ```bash
   cd /path/to/mnms/examples/ai-network-analysis
   ```

2. **Install dependencies**:
   ```bash
   # Core dependencies (required)
   pip install fastmcp>=0.2.0 openai>=1.3.0 httpx>=0.24.0 aiohttp>=3.8.0 json5>=0.9.0 python-dateutil>=2.8.0
   
   # Optional: Testing and development dependencies
   pip install pytest>=7.0.0 pytest-asyncio>=0.21.0 black>=23.0.0 flake8>=6.0.0 mypy>=1.0.0
   ```

3. **Set up environment variables**:
   ```bash
   # Windows (Command Prompt)
   set OPENAI_API_KEY=your-openai-api-key
   
   # Windows (PowerShell)
   $env:OPENAI_API_KEY = "your-openai-api-key"
   
   # Linux/macOS (Bash)
   export OPENAI_API_KEY="your-openai-api-key"
   ```

4. **Test the MCP server**:
   ```bash
   # Verify MCP connectivity and tools
   python test_complete_functionality.py
   ```
   
   **Expected Output:**
   ```
   ✓ Connected successfully
   ✓ Found 4 resources
   ✓ Devices retrieved successfully  
   ✓ Commands retrieved successfully
   ✓ TIMESTAMP ENHANCEMENT TESTING COMPLETED SUCCESSFULLY
   ```

5. **Verify installation and run analysis**:
   ```bash
   # Test basic MCP functionality
   python test_complete_functionality.py
   
   # Run your first analysis (requires OpenAI API key)
   OPENAI_API_KEY=your-openai-api-key python llm_network_analyzer.py
   ```

6. **View and manage your reports**:
   ```bash
   # List all generated reports
   python view_reports.py list
   
   # View the latest analysis with insights and alerts
   python view_reports.py latest
   
   # Generate a comprehensive summary from multiple analyses
   python view_reports.py summary
   ```

**Expected Output After First Run:**
```bash
$ python view_reports.py list
[INFO] Reports in /path/to/mnms/examples/ai-network-analysis/network_reports:

📸 SNAPSHOTS (1 files):
   2025-06-23 10:30:15 - 20250623_103015_snapshot.json

🔍 ANALYSES (1 files):
   2025-06-23 10:30:15 - 20250623_103015_analysis.json

📊 SUMMARIES (0 files):

$ python view_reports.py latest
[LATEST ANALYSIS] 20250623_103015_analysis.json
==================================================
Timestamp: 2025-06-23T10:30:15.123456
Model: gpt-4o
Summary: Network analysis shows X devices with Y alerts...
Key Insights:
  1. Device connectivity patterns identified
  2. Configuration changes detected
Alerts:
  ! Critical: Device offline for extended period
Confidence: 0.95
```

### Next Steps - Automated Analysis

   **Scheduled Automated Analysis (Windows):**
   ```bash
   # Create a scheduled task (run as Administrator)
   schtasks /create /tn "NIMBL Network Analysis" /tr "python D:\path\to\llm_network_analyzer.py" /sc hourly

   # Or use Task Scheduler GUI:
   # 1. Open Task Scheduler
   # 2. Create Basic Task
   # 3. Set trigger (hourly/daily/etc.)
   # 4. Set action: python llm_network_analyzer.py
   # 5. Set working directory to your project folder
   ```

   **Scheduled Automated Analysis (Linux/macOS):**
   ```bash
   # Add to crontab for analysis every 30 minutes
   crontab -e
   # Add this line:
   */30 * * * * cd /path/to/ai-network-analysis && OPENAI_API_KEY=your-key python llm_network_analyzer.py

   # Or run hourly at the top of each hour
   0 * * * * cd /path/to/ai-network-analysis && OPENAI_API_KEY=your-key python llm_network_analyzer.py

   # For daily analysis at 2 AM
   0 2 * * * cd /path/to/ai-network-analysis && OPENAI_API_KEY=your-key python llm_network_analyzer.py
   ```

   **Background Service (Linux):**
   ```bash
   # Run continuously in background with nohup
   nohup python llm_network_analyzer.py &   # Or create a systemd service for persistent operation
   ```

## Understanding Your Reports

After running your first analysis, you'll have reports in the `network_reports/` directory. Here's how to understand and use them:

### 📊 **Quick Report Commands**

```bash
# Get an overview of all your reports
python view_reports.py list

# See your latest analysis with insights
python view_reports.py latest

# Generate trends and patterns summary
python view_reports.py summary

# Clean up old reports (keep last 30 days)
python view_reports.py cleanup 30
```

### 🔍 **What Each Report Type Contains**

**Snapshots** (`snapshots/`): Raw network data
- Device inventory with timestamps
- Command execution logs
- Network topology information
- Used for change detection between runs

**Analyses** (`analysis/`): AI-powered insights
- Network health summary
- Device connectivity issues
- Configuration change alerts
- Strategic recommendations
- Confidence scores (0.0-1.0)

**Summaries** (`summaries/`): Trend analysis
- Multi-analysis patterns
- Network health trends
- Recurring issue identification
- Strategic planning recommendations

### 📈 **Reading Analysis Results**

**Health Status Indicators:**
- 🟢 **Active**: Device seen < 5 minutes ago
- 🟡 **Recent**: Device seen < 30 minutes ago  
- 🟠 **Stale**: Device seen < 1 hour ago
- 🔴 **Old**: Device seen < 24 hours ago
- ⚫ **Offline**: Device not seen > 24 hours

**Alert Priorities:**
- **Critical**: Immediate attention required
- **Warning**: Monitor and plan resolution
- **Info**: Awareness, no action needed

**Network Health Assessment:**
- **stable**: < 5 average alerts per analysis
- **needs_attention**: ≥ 5 average alerts per analysis

### 🎯 **Typical Workflow**

1. **Daily Check**: `python view_reports.py latest`
2. **Weekly Review**: `python view_reports.py summary` 
3. **Monthly Cleanup**: `python view_reports.py cleanup 30`
4. **Issue Investigation**: Check specific timestamp reports in JSON files

### 🚨 **Quick Start Troubleshooting**

**If MCP test fails:**
```bash
# Check if NIMBL is running
curl http://localhost:27182/api/v1/devices
# Should return device data, not connection refused

# Verify Python dependencies
python -c "import fastmcp, openai, httpx; print('Dependencies OK')"
```

**If analysis fails:**
```bash
# Check API key is set
echo $OPENAI_API_KEY  # Linux/macOS
echo %OPENAI_API_KEY%  # Windows CMD
echo $env:OPENAI_API_KEY  # Windows PowerShell

# Test with minimal data
python llm_network_analyzer.py 2>&1 | head -20
```

**If no reports generated:**
```bash
# Check directory permissions
ls -la network_reports/  # Linux/macOS
dir network_reports\  # Windows

# Manual directory creation
mkdir -p network_reports/{snapshots,analysis,summaries}  # Linux/macOS
mkdir network_reports\snapshots network_reports\analysis network_reports\summaries  # Windows
```

**Common Quick Fixes:**
- 🔧 **"No module named 'fastmcp'"**: Run pip install command again
- 🔧 **"OpenAI API key not found"**: Set environment variable correctly for your OS
- 🔧 **"Failed to connect to MCP server"**: Ensure NIMBL is running on localhost:27182
- 🔧 **"Permission denied"**: Run terminal as administrator (Windows) or check folder permissions
- 🔧 **Empty summaries directory**: Normal until you run `python view_reports.py summary`

## Usage Examples

### Automated Analysis

**Current Capabilities:**
- Collects network device snapshots via MCP tools
- Detects basic device configuration changes between snapshots
- Stores historical data as timestamped JSON files
- Generates LLM-powered analysis reports
- Easy backup and inspection of analysis history

**Example Analysis Output:**
```bash
$ OPENAI_API_KEY=your-key python llm_network_analyzer.py
[INFO] Starting LLM-powered network analysis cycle...
[INFO] Reports directory initialized: D:\code\golang\mnms\examples\ai-network-analysis\network_reports
[DATA] Collecting raw network data...
[SAVED] Snapshot: 20250620_185351_snapshot.json
[SUCCESS] Collected data snapshot: network_reports\snapshots\20250620_185351_snapshot.json
[LLM] Analyzing with LLM...
[DEBUG] Initial data size: 15274 characters
[API] Sending request to OpenAI (gpt-4o)...
[SUCCESS] OpenAI API response received
[SAVED] Analysis: 20250620_185351_analysis.json
[SUCCESS] LLM analysis completed
[SUMMARY] The network consists of 9 devices, with a mix of active and inactive statuses. Some devices have not been seen for over 30 minutes, indicating potential connectivity issues.
[SAVED] Analysis report: network_reports\analysis\20250620_185351_analysis.json
[CONFIG] Configuration Changes: No configuration changes detected
[INSIGHTS] Key Insights:
   - Two devices are currently active and have been seen very recently
   - Several devices are marked as 'stale', indicating they have not been seen for over 30 minutes
   - There is a command execution error due to a device not found
[ALERTS] Alerts Generated:
   ! Device 00-60-E9-1F-A6-02 is inactive and has not been seen for over 30 minutes
   ! Device 00-60-E9-2B-17-6A is inactive and has not been seen for over 30 minutes
   (... additional alerts ...)

[SUCCESS] Analysis cycle completed successfully!
```

**Generate Summary from Multiple Analyses:**
```bash
$ python view_reports.py summary
[INFO] Generating summary from 2 analysis reports...
[SUCCESS] Summary generated: 20250620_190124_manual_summary.json
📊 GENERATED SUMMARY
========================================
Reports Analyzed: 2
Date Range: 2025-06-20T18:11:48 to 2025-06-20T18:53:51
Trend Summary: Analyzed 2 network snapshots. Average 9.0 devices tracked.
Key Metrics:
  • Average Devices: 9.0
  • Average Alerts: 6.0
  • Average Confidence: 0.95
  • Network Health: needs_attention
Strategic Recommendations:
  • Investigate devices with consistent connectivity issues
  • Review network infrastructure for intermittent failures
  • Consider device health monitoring improvements
  • Schedule regular device maintenance cycles
```

**[NOTE]:** Advanced trend analysis and predictive insights are planned features.


### MCP Tool Usage
Available tools through the MCP server:
- `get_devices`: Retrieve device information with temporal intelligence
- `get_commands`: List available network commands
- `post_command`: Execute network management commands
- `sleep_seconds`: Timing control for sequential operations
- `analyze_timestamp`: Convert timestamps to human-readable format

### Report Management

**Directory Structure:**
The analyzer creates a `network_reports` directory with the following structure:
```
network_reports/
├── snapshots/           # Raw network data snapshots
│   ├── 20250620_143052_snapshot.json
│   ├── 20250620_153045_snapshot.json
│   └── ...
├── analysis/            # LLM analysis results  
│   ├── 20250620_143052_analysis.json
│   ├── 20250620_153045_analysis.json
│   └── ...
└── summaries/           # Trend summaries and overviews
    ├── 20250620_100000_weekly_summary.json     # Auto-generated (Mondays)
    ├── 20250620_190124_manual_summary.json     # Manual generation
    └── ...
```

**How Summaries Work:**
- **Automatic Weekly Summaries**: Generated every Monday from the past week's analyses
- **Manual Summaries**: Generate anytime with `python view_reports.py summary`
- **Summary Content**: Aggregates trends, metrics, and recommendations from multiple analyses
- **Empty Summaries Directory**: Normal until you generate your first summary or wait for Monday

**When to Generate Summaries:**
- After running several analyses to see trends
- Before weekly team meetings for status reports
- When investigating recurring network issues
- For historical trend analysis and planning

**Report Viewer Utility:**
Use the included `view_reports.py` utility to manage reports:

```bash
# List all available reports
python view_reports.py list

# View the latest analysis report
python view_reports.py latest

# Generate summary from available analyses (NEW!)
python view_reports.py summary

# Clean up reports older than 30 days
python view_reports.py cleanup 30
```

**Summary Generation:**
The `summary` command analyzes all your existing analysis reports and generates comprehensive trend summaries:

```bash
$ python view_reports.py summary
[INFO] Generating summary from 2 analysis reports...
[SUCCESS] Summary generated: 20250620_190124_manual_summary.json
📊 GENERATED SUMMARY
========================================
Reports Analyzed: 2
Date Range: 2025-06-20T18:11:48 to 2025-06-20T18:53:51
Trend Summary: Analyzed 2 network snapshots. Average 9.0 devices tracked.
Key Metrics:
  • Average Devices: 9.0
  • Average Alerts: 6.0
  • Average Confidence: 0.95
  • Network Health: needs_attention
Strategic Recommendations:
  • Investigate devices with consistent connectivity issues
  • Review network infrastructure for intermittent failures
  • Consider device health monitoring improvements
  • Schedule regular device maintenance cycles
```

**What Gets Summarized:**
- Device count trends across all analyses
- Alert frequency and patterns
- Configuration change summaries
- Network health assessment (stable/needs_attention)
- Strategic recommendations based on recurring issues
- Confidence metrics and analysis quality

**Benefits of JSON File Approach:**
- **Easy Backup**: Simply copy the `network_reports` directory
- **Human Readable**: JSON files can be opened in any text editor
- **Scriptable**: Easy to parse and process with scripts
- **No Database Dependencies**: No SQLite file corruption risks
- **Version Control Friendly**: Individual files can be tracked
- **Platform Independent**: Works on Windows, Linux, macOS

**Example JSON Report Structure:**
```json
{
  "metadata": {
    "timestamp": "2025-06-20T14:30:52",
    "analysis_type": "network_health",
    "model_used": "gpt-4o",
    "created_at": "2025-06-20T14:30:55"
  },
  "analysis": {
    "summary": "Network shows 12 active devices, no critical issues detected",
    "key_insights": [
      "All devices responding within normal timeframes",
      "No configuration changes since last snapshot"
    ],
    "alerts": [],
    "configuration_changes": "None",
    "confidence": 0.92
  },
  "raw_data_summary": {
    "devices_count": 12,
    "commands_count": 15
  }
}
```

## Configuration

### Environment Variables

**Required:**
- `OPENAI_API_KEY`: Your OpenAI API key (required for `llm_network_analyzer.py`)

### MCP Client Configuration
```json
{
  "mcpServers": {
    "nimbl-ai-wrapper": {
      "command": "python",
      "args": ["./nimbl_mcp.py"]
    }
  }
}
```

## Current Features

### Device Change Detection
The system monitors and reports:
- Hostname changes
- IP address modifications
- Online/offline status changes
- Basic configuration differences between snapshots

### Temporal Intelligence (MCP Server)
- Automatic timestamp conversion (Unix → "3 minutes ago")
- Device activity status classification (Active/Recent/Stale/Old/Offline)
- Human-readable time analysis
- Context-aware status recommendations

### AI-Powered Analysis
- LLM-based analysis of network data snapshots
- Structured recommendations based on current network state
- Automated historical data collection and comparison

**[DEVELOPMENT STATUS]**: Historical trend analysis and complex predictive insights are planned features under development.

## Testing

**Available Tests:**

```bash
# Test MCP server connectivity and tool discovery
python test_mcp.py

# Test basic functionality (if available)
python test_complete_functionality.py
```

**Manual Testing:**
1. **MCP Server**: Run `python nimbl_mcp.py` and verify tools are available
2. **AI Analysis**: Run `python llm_network_analyzer.py` with OpenAI API key set

**[NOTE]**: Not all test files mentioned in file structure may be fully implemented. Check individual test files before running.

## File Structure

```
ai-network-analysis/
├── README.md                         # This file
├── nimbl_mcp.py                      # Enhanced MCP server
├── llm_network_analyzer.py           # Automated analysis system
├── view_reports.py                   # Report management utility
├── 06-Script-commands.md             # Command documentation (copy from user guide)
│                                     # Test suite
├── test_complete_functionality.py
├── test_mcp.py
├── test_llm_analyzer.py
├── test_device_changes.py
├── test_timestamp_analysis.py
├── test_edge_cases.py
├── test_production_llm.py
│                                     # Demo scripts
├── demo_complete_llm_workflow.py
└── demo_llm_workflow.py
├── network_reports/                  # Generated reports (created at runtime)
│   ├── snapshots/                    # Raw network data snapshots
│   ├── analysis/                     # LLM analysis results
│   └── summaries/                    # Weekly trend summaries
├── docs/                             # Documentation
│   ├── LLM_INTEGRATION_GUIDE.md
│   ├── OPENAI_SETUP_GUIDE.md
│   ├── DEVICE_CHANGE_DETECTION_SUMMARY.md
│   └── TIMESTAMP_ENHANCEMENT_SUMMARY.md
```

## API Integration

### OpenAI Integration
- Uses GPT-4o for advanced analysis
- Handles API rate limits and errors
- Supports streaming responses
- JSON response parsing with markdown handling

### NIMBL API Integration
- RESTful API wrapper through MCP
- Automatic authentication handling
- Device discovery
- Command monitoring

## Troubleshooting

### Common Issues

1. **OpenAI API Key Not Set**:
   ```
   Error: OpenAI API key not found
   Solution: Set OPENAI_API_KEY environment variable
   ```

2. **OpenAI API Timeout Issues** 🧪 **IMPROVED - UNDER TESTING**:
   ```
   Previous Error: Request timed out
   🔧 IMPROVEMENTS MADE (Testing needed for large networks):
   
   Implemented solutions:
   - Reduced max data size from 50,000 to 30,000 characters
   - Added 120-second timeout configuration for API requests  
   - Implemented retry logic with exponential backoff (3 attempts)
   - Smart data truncation (keeps 5 most recent devices, 10 recent commands)
   - Better error handling with specific timeout detection
   - HTTP client optimization with proper connection pooling
   
   📋 TESTING STATUS: Works with small networks (<50 devices)
   ⚠️  NEEDS TESTING: Large networks (500+ devices, 1000+ devices)
   ```

3. **Large Data Payload Issues** 🧪 **IMPROVED - UNDER TESTING**:
   ```
   Previous Problem: Analysis fails with large device/command datasets
   🔧 IMPROVEMENTS MADE (Testing needed for scale):
   
   Implemented solutions:
   - Automatic data truncation when payload exceeds limits
   - Prioritizes most recent devices and commands
   - Preserves critical metadata while reducing payload size
   - Logs truncation details for transparency
   - Progressive truncation strategy (devices → commands → context)
   
   📋 TESTING STATUS: Works with moderate networks (<100 devices)
   ⚠️  NEEDS TESTING: Large networks (1000+ devices, enterprise scale)
   🔍 UNKNOWN: Performance with very large command histories
   ```

4. **MCP Connection Issues**:
   ```
   Error: Failed to connect to MCP server
   Solution: Ensure nimbl is running and accessible with nimbl_mcp.py
   ```

5. **Reports Directory Permission Errors**:
   ```
   Error: Permission denied creating network_reports directory
   Solution: Check write permissions for the current directory
   ```

### Debug Mode
Enable debug logging:
```bash
export DEBUG=1
python llm_network_analyzer.py
```

### API Timeout Configuration
The system now includes robust timeout handling:
- **Connection timeout**: 30 seconds
- **Read timeout**: 120 seconds (configurable)
- **Retry attempts**: 3 with exponential backoff
- **Data size limit**: 30,000 characters (auto-truncated)

### Performance Tuning
For large networks, adjust these settings in `NetworkAnalysisConfig`:
```python
config = NetworkAnalysisConfig(
    openai_api_key="your-key",
    api_timeout=180,        # Increase for slow networks
    max_raw_data_size=20000,  # Decrease for faster processing
    max_tokens=1500         # Reduce for concise responses
)
```

### Large-Scale Testing Needed
**Help us test with large networks!**

The system needs validation with enterprise-scale deployments:

```bash
# Test scenarios we need data on:
# 1. Medium networks (100-500 devices)
# 2. Large networks (500-1000 devices)  
# 3. Enterprise networks (1000+ devices)
# 4. Networks with extensive command histories
# 5. High-frequency analysis (every 5-10 minutes)

# If you test with large networks, please report:
# - Number of devices tested
# - Analysis completion time
# - Any timeout or truncation issues
# - Memory usage patterns
# - API response reliability
```

**Known Testing Gaps:**
- ⚠️ **API timeout behavior** with 1000+ device payloads
- ⚠️ **Memory usage** during large data processing
- ⚠️ **Truncation effectiveness** for very large datasets
- ⚠️ **Analysis quality** when data is heavily truncated

## Limitations and Known Issues

**Current Limitations:**
1. **Historical Analysis**: Basic trend analysis implemented; advanced predictive insights planned
2. **Performance Metrics**: System tracks device connectivity timestamps, not bandwidth/latency/throughput  
3. **Multi-Vendor Support**: Currently optimized for NIMBL devices; other vendors planned

**Resolved Issues:**
- ✅ **Empty Summaries Directory**: Manual summary generation now available

**Improved but Needs Testing:**
- 🧪 **OpenAI API Timeouts**: Improvements made but needs testing with 1000+ devices
- 🧪 **Large Data Payloads**: Smart truncation implemented but needs enterprise-scale testing

**Current Testing Status:**
- ✅ **Tested**: Small networks (1-50 devices)
- 🔄 **Testing Needed**: Medium networks (100-500 devices)
- ❓ **Unknown**: Large networks (1000+ devices, enterprise environments)

**Minor Known Issues:**
1. **File System Space**: JSON files accumulate over time; use `cleanup` utility regularly
2. **API Rate Limits**: No built-in rate limiting; may hit OpenAI quotas with very frequent analysis

**Development Status:**
- ✅ **WORKING**: MCP server, device analysis, configuration change detection
- ✅ **WORKING**: JSON-based storage, report management, manual summary generation
- 🧪 **IMPROVED**: Timeout handling and data truncation (needs large-scale testing)
- 🚧 **IN PROGRESS**: Advanced historical analysis, predictive trend detection
- 📋 **PLANNED**: Performance metrics, multi-vendor support, real-time alerting
- 🚧 **IN PROGRESS**: Advanced historical analysis, predictive trend detection
- 📋 **PLANNED**: Performance metrics, multi-vendor support, real-time alerting

## Contributing

### Development Setup
1. Install dependencies:
   ```bash
   # Core and development dependencies
   pip install fastmcp>=0.2.0 openai>=1.3.0 httpx>=0.24.0 aiohttp>=3.8.0 json5>=0.9.0 python-dateutil>=2.8.0
   pip install pytest>=7.0.0 pytest-asyncio>=0.21.0 black>=23.0.0 flake8>=6.0.0 mypy>=1.0.0
   ```

2. Run code formatting:
   ```bash
   black *.py
   flake8 *.py
   ```

3. Run type checking:
   ```bash
   mypy *.py
   ```

### Testing Guidelines
- Ensure all tests pass before submitting changes
- Follow the existing test patterns and naming conventions

## License

This project is part of the NIMBL Network Management System. See the main project LICENSE.txt for details.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the documentation in the `docs/` directory
3. Run the test suite to verify functionality
4. Submit issues through the project's issue tracker


## Future Plan

### Enhanced Analysis Capabilities
**Planned Features for Advanced Network Intelligence:**

**Model Integration Strategy:**
- **OpenAI GPT-4o**: Complex analysis, predictive insights, critical troubleshooting
- **Anthropic Claude**: Security audits, compliance analysis, risk assessment
- **Google Gemini**: Cost-effective monitoring, routine analysis, bulk operations
- **Local Models**: Privacy-critical operations, offline analysis

### Historical Data Analysis Integration
**Planned Features:**
- Advanced trend analysis over multiple time periods
- Predictive insights based on historical patterns
- Comprehensive device change history tracking
- Automated anomaly detection based on historical baselines
- Basic trend analysis and reporting through enhanced MCP tools

**Current Status:** Historical data collection is implemented, but advanced trend analysis and predictive insights are planned features.

### Network Performance Analysis
**Current Capabilities:**
- Device status based on last-seen timestamps
- Basic connectivity assessment (Active/Recent/Stale/Old/Offline)
- Configuration change detection between snapshots
- LLM-powered insights on current network state

**Example Current Analysis:**
- Device status classification with temporal intelligence
- Configuration change detection between snapshots
- Basic alerting for devices with connectivity issues

**[FUTURE ENHANCEMENT]:** Advanced performance metrics (latency, bandwidth, CPU usage) would require additional data sources beyond the current NIMBL API integration.

### Advanced Features Roadmap
- [ ] Real-time alerting system integration
- [ ] Advanced machine learning models for anomaly detection
- [ ] Multi-vendor network device support
- [ ] Automated remediation capabilities
- [ ] Integration with external monitoring systems (SNMP, Nagios, etc.)
- [ ] Performance metrics collection and analysis
- [ ] Network topology visualization
- [ ] Backup and restore management through chat interface
- [ ] Historical data analysis and trend reporting
- [ ] IDP system integration
---

**Built for intelligent network management**