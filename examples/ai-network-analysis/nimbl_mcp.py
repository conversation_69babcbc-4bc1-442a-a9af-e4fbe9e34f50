"""
FastMCP REST API Wrapper for NIMBL API

This mcp server is an enhancement copy of /examples/nimbl_mcp.py, which provides a FastMCP wrapper for the NIMBL REST API.

You can use the configuration like this in your MCP client:
```json
{
  "mcpServers": {
    "nimble-api-wrapper": {
      "command": "/$HOME/venv-3.11/bin/python",
      "args": ["$HOME/mnms/examples/nimbl_mcp.py"]
    }
  }
}
```

"""

import asyncio
import json
from typing import Any, Dict, List, Optional
import httpx
import requests
import time
from datetime import datetime, timezone
from fastmcp import FastMCP

mcp = FastMCP("NIMBL REST API Wrapper")

BASE_URL = "http://localhost:27182/api/v1"

client = httpx.AsyncClient()
headers = {
        'Content-Type': 'application/json'
}

post_data = {
        'user': 'admin',  # Replace with your Nimbl username
        'password':'default' # Replace with your Nimbl password
}

json_payload = json.dumps(post_data)

url = f"{BASE_URL}/login"
response=requests.post(url,headers=headers, data=json_payload)
json_res= json.loads(response.text)
token=json_res['token']

def format_timestamp_for_llm(unix_timestamp):
    """Convert Unix timestamp to LLM-friendly format with network management context."""
    try:
        timestamp = int(unix_timestamp)
        current_time = int(time.time())
        diff_seconds = current_time - timestamp
        
        # Convert to human-readable relative time
        if diff_seconds < 0:
            return "future timestamp (check system clock)"
        elif diff_seconds < 60:
            return f"{diff_seconds} seconds ago"
        elif diff_seconds < 3600:
            minutes = diff_seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif diff_seconds < 86400:
            hours = diff_seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        else:
            days = diff_seconds // 86400
            return f"{days} day{'s' if days != 1 else ''} ago"
    except (ValueError, TypeError):
        return f"invalid timestamp (raw: {unix_timestamp})"

def format_iso_timestamp_for_llm(iso_timestamp):
    """Convert ISO 8601 timestamp to LLM-friendly format with network management context."""
    try:
        # Parse ISO 8601 timestamp (handles timezone)
        dt = datetime.fromisoformat(iso_timestamp.replace('Z', '+00:00'))
        unix_timestamp = int(dt.timestamp())
        
        current_time = int(time.time())
        diff_seconds = current_time - unix_timestamp
        
        # Convert to human-readable relative time
        if diff_seconds < 0:
            return "future timestamp (check system clock)"
        elif diff_seconds < 60:
            return f"{diff_seconds} seconds ago"
        elif diff_seconds < 3600:
            minutes = diff_seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif diff_seconds < 86400:
            hours = diff_seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        else:
            days = diff_seconds // 86400
            return f"{days} day{'s' if days != 1 else ''} ago"
    except (ValueError, TypeError) as e:
        return f"invalid ISO timestamp (raw: {iso_timestamp}, error: {str(e)})"

def get_device_status_from_timestamp(unix_timestamp):
    """Determine device status based on how old the timestamp is."""
    try:
        timestamp = int(unix_timestamp)
        current_time = int(time.time())
        diff_seconds = current_time - timestamp
        
        if diff_seconds < 0:
            return {
                "status": "clock_issue",
                "urgency": "high",
                "recommendation": "Check system clock - timestamp is in future"
            }
        elif diff_seconds < 300:  # 5 minutes
            return {
                "status": "active",
                "urgency": "low", 
                "recommendation": "Device is recently active and healthy"
            }
        elif diff_seconds < 1800:  # 30 minutes
            return {
                "status": "recent",
                "urgency": "low",
                "recommendation": "Device was recently active"
            }
        elif diff_seconds < 3600:  # 1 hour
            return {
                "status": "stale",
                "urgency": "medium",
                "recommendation": "Device might need attention - check connectivity"
            }
        elif diff_seconds < 86400:  # 24 hours
            return {
                "status": "old",
                "urgency": "high",
                "recommendation": "Device appears inactive - investigate connectivity"
            }
        else:
            return {
                "status": "offline",
                "urgency": "critical",
                "recommendation": "Device appears offline for over a day - immediate investigation needed"
            }
    except (ValueError, TypeError):
        return {
            "status": "unknown",
            "urgency": "medium",
            "recommendation": "Invalid timestamp - verify data integrity"
        }
    except Exception as e:
        return {
            "status": "error",
            "urgency": "high",
            "recommendation": f"Exception occurred: {str(e)}"
        }

def get_command_status_from_timestamp(iso_timestamp):
    """Determine command status based on how old the timestamp is."""
    try:
        dt = datetime.fromisoformat(iso_timestamp.replace('Z', '+00:00'))
        unix_timestamp = int(dt.timestamp())
        current_time = int(time.time())
        diff_seconds = current_time - unix_timestamp
        
        if diff_seconds < 0:
            return {
                "status": "clock_issue",
                "urgency": "high",
                "recommendation": "Check system clock - timestamp is in future"
            }
        elif diff_seconds < 300:  # 5 minutes
            return {
                "status": "recent",
                "urgency": "low",
                "recommendation": "Command was recently created or executed"
            }
        elif diff_seconds < 1800:  # 30 minutes
            return {
                "status": "current",
                "urgency": "low",
                "recommendation": "Command is from current session"
            }
        elif diff_seconds < 3600:  # 1 hour
            return {
                "status": "older",
                "urgency": "medium",
                "recommendation": "Command is from earlier session, check if still relevant"
            }
        elif diff_seconds < 86400:  # 24 hours
            return {
                "status": "old",
                "urgency": "low",
                "recommendation": "Command is from previous day, likely completed"
            }
        else:
            return {
                "status": "historical",
                "urgency": "low",
                "recommendation": "Historical command, useful for audit or trend analysis"
            }
    except (ValueError, TypeError):
        return {
            "status": "unknown",
            "urgency": "medium",
            "recommendation": "Invalid timestamp format - verify data integrity"
        }
    except Exception as e:
        return {
            "status": "error",
            "urgency": "high",
            "recommendation": f"Exception occurred: {str(e)}"
        }

@mcp.tool()
async def get_devices() -> Dict[str, Any]:
    """
    Get devices information from NIMBL with enhanced timestamp analysis.
    
    Returns:
        Dictionary of devices with human-readable timestamps and network management insights
    """
    url = f"{BASE_URL}/devices"
    try:
        response = await client.get(url, headers={'Authorization': f'Bearer {token}'})
        response.raise_for_status()
        devices_data = response.json()
        
        # If it's a list with error, return as-is
        if isinstance(devices_data, list):
            return {"devices": devices_data}
            
        # Transform timestamps for LLM consumption
        enhanced_devices = {}
        current_server_time = int(time.time())
        
        for device_mac, device_info in devices_data.items():
            enhanced_device = dict(device_info)  # Copy original data
            
            # Process timestamp if it exists
            if 'timestamp' in device_info:
                raw_timestamp = device_info['timestamp']
                
                # Add human-readable timestamp info
                enhanced_device['last_seen_human'] = format_timestamp_for_llm(raw_timestamp)
                enhanced_device['last_seen_raw'] = raw_timestamp  # Keep original for debugging
                
                # Add device status analysis
                status_info = get_device_status_from_timestamp(raw_timestamp)
                enhanced_device['device_status'] = status_info['status']
                enhanced_device['urgency_level'] = status_info['urgency']
                enhanced_device['recommendation'] = status_info['recommendation']
                
                # Add absolute timestamp in readable format
                try:
                    dt = datetime.fromtimestamp(int(raw_timestamp), tz=timezone.utc)
                    enhanced_device['last_seen_absolute'] = dt.strftime('%Y-%m-%d %H:%M:%S UTC')
                except (ValueError, TypeError):
                    enhanced_device['last_seen_absolute'] = "Invalid timestamp"
            
            enhanced_devices[device_mac] = enhanced_device
        
        # Add metadata for LLM context
        result = {
            "devices": enhanced_devices,
            "metadata": {
                "total_devices": len(enhanced_devices),
                "server_time": current_server_time,
                "server_time_human": datetime.fromtimestamp(current_server_time, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC'),
                "analysis_note": "Device status based on last_seen timestamp: active (<5min), recent (<30min), stale (<1hr), old (<24hr), offline (>24hr)"
            }
        }
        
        return result
        
    except httpx.RequestError as e:
        return {"error": f"Request failed: {str(e)}"}
    except httpx.HTTPStatusError as e:
        return {"error": f"HTTP error {e.response.status_code}: {e.response.text}"}
    except Exception as e:
        return {"error": f"Unexpected error processing devices: {str(e)}"}


@mcp.tool()
async def get_commands() -> Dict[str, Any]:
    """
    Get commands information from NIMBL with enhanced timestamp analysis.
    
    Returns:
        Dictionary of commands with human-readable timestamps and execution context
    """
    url = f"{BASE_URL}/commands?cmd=all"
    try:
        response = await client.get(url, headers={'Authorization': f'Bearer {token}'})
        response.raise_for_status()
        commands_data = response.json()
        
        # If it's a list with error, return as-is
        if isinstance(commands_data, list):
            return {"commands": commands_data}
            
        # Transform timestamps for LLM consumption
        enhanced_commands = {}
        current_server_time = int(time.time())
        
        for command_key, command_info in commands_data.items():
            enhanced_command = dict(command_info)  # Copy original data
            
            # Process timestamp if it exists
            if 'timestamp' in command_info:
                raw_timestamp = command_info['timestamp']
                
                # Add human-readable timestamp info
                enhanced_command['created_human'] = format_iso_timestamp_for_llm(raw_timestamp)
                enhanced_command['created_raw'] = raw_timestamp  # Keep original for debugging
                
                # Add command status analysis
                status_info = get_command_status_from_timestamp(raw_timestamp)
                enhanced_command['time_status'] = status_info['status']
                enhanced_command['urgency_level'] = status_info['urgency']
                enhanced_command['recommendation'] = status_info['recommendation']
                
                # Add absolute timestamp in UTC format for consistency
                try:
                    dt = datetime.fromisoformat(raw_timestamp.replace('Z', '+00:00'))
                    enhanced_command['created_utc'] = dt.astimezone(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
                    enhanced_command['created_unix'] = int(dt.timestamp())
                except (ValueError, TypeError):
                    enhanced_command['created_utc'] = "Invalid timestamp"
                    enhanced_command['created_unix'] = None
            
            # Add execution context based on status and result
            if 'status' in command_info and 'result' in command_info:
                execution_status = command_info.get('status', 'unknown')
                result_content = command_info.get('result', '')
                
                if execution_status == 'ok' and not result_content:
                    enhanced_command['execution_context'] = "Command completed successfully"
                elif execution_status == 'ok' and result_content:
                    enhanced_command['execution_context'] = f"Command completed with result: {result_content[:100]}..."
                elif execution_status == 'error':
                    enhanced_command['execution_context'] = f"Command failed: {result_content[:100]}..."
                elif execution_status == 'pending':
                    enhanced_command['execution_context'] = "Command is still executing"
                else:
                    enhanced_command['execution_context'] = f"Command status: {execution_status}"
            
            enhanced_commands[command_key] = enhanced_command
        
        # Add metadata for LLM context
        result = {
            "commands": enhanced_commands,
            "metadata": {
                "total_commands": len(enhanced_commands),
                "server_time": current_server_time,
                "server_time_human": datetime.fromtimestamp(current_server_time, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC'),
                "analysis_note": "Command time status: recent (<5min), current (<30min), older (<1hr), old (<24hr), historical (>24hr)"
            }
        }
        
        return result
        
    except httpx.RequestError as e:
        return {"error": f"Request failed: {str(e)}"}
    except httpx.HTTPStatusError as e:
        return {"error": f"HTTP error {e.response.status_code}: {e.response.text}"}
    except Exception as e:
        return {"error": f"Unexpected error processing commands: {str(e)}"}

@mcp.tool()
async def post_command(cmd: str) -> List[Dict[str, Any]]:
    """
    Create a new command in NIMBL.

    Args:
        cmd: Command string to be executed (e.g., "beep AA-BB-CC-DD-EE-FF").
        
    Returns:
        Created commands data in JSON format.
    """
    url = f"{BASE_URL}/commands"
    
    # Create proper JSON structure instead of f-string to avoid format issues
    data = [{"kind": "usercommand", "command": cmd}]

    try:
        response = await client.post(url, json=data, headers={'Authorization': f'Bearer {token}'})
        response.raise_for_status()
        return response.json()
    except httpx.RequestError as e:
        return [{"error": f"Request failed: {str(e)}"}]
    except httpx.HTTPStatusError as e:
        return [{"error": f"HTTP error {e.response.status_code}: {e.response.text}"}]

@mcp.tool()
async def sleep_seconds(seconds: int) -> Dict[str, Any]:
    """
    Sleep/wait for a specified number of seconds.
    
    Args:
        seconds: Number of seconds to wait (1-300, max 5 minutes for safety).
        
    Returns:
        Confirmation message with wait time.
    """
    import asyncio
    
    # Limit sleep time for safety (max 5 minutes)
    if seconds < 1:
        seconds = 1
    elif seconds > 300:
        seconds = 300
        
    await asyncio.sleep(seconds)
    
    return {
        "status": "completed",
        "message": f"Waited for {seconds} seconds",
        "timestamp": asyncio.get_event_loop().time()
    }

@mcp.tool()
async def analyze_timestamp(timestamp: str, context: str = "device_last_seen") -> Dict[str, Any]:
    """
    Analyze Unix timestamp and provide network management context.
    
    Args:
        timestamp: Unix timestamp to analyze (as string)
        context: Analysis context - 'device_last_seen', 'command_created', 'log_entry', etc.
        
    Returns:
        Detailed timestamp analysis with network management insights
    """
    try:
        unix_ts = int(timestamp)
        current_time = int(time.time())
        diff_seconds = current_time - unix_ts
        
        # Human readable time
        dt = datetime.fromtimestamp(unix_ts, tz=timezone.utc)
        readable = dt.strftime('%Y-%m-%d %H:%M:%S UTC')
        
        # Relative time
        relative = format_timestamp_for_llm(timestamp)
        
        # Base analysis
        analysis = {
            "input_timestamp": timestamp,
            "unix_timestamp": unix_ts,
            "human_readable": readable,
            "relative_time": relative,
            "seconds_ago": diff_seconds,
            "context": context
        }
        
        # Context-specific analysis
        if context == "device_last_seen":
            status_info = get_device_status_from_timestamp(timestamp)
            analysis.update({
                "device_status": status_info['status'],
                "urgency": status_info['urgency'],
                "recommendation": status_info['recommendation'],
                "network_impact": "Device communication and network health assessment"
            })
        elif context == "command_created":
            if diff_seconds < 60:
                analysis.update({
                    "command_status": "recent",
                    "urgency": "low",
                    "recommendation": "Command was recently created, normal processing time"
                })
            elif diff_seconds < 600:  # 10 minutes
                analysis.update({
                    "command_status": "processing",
                    "urgency": "medium", 
                    "recommendation": "Command may still be processing, check status"
                })
            else:
                analysis.update({
                    "command_status": "old",
                    "urgency": "high",
                    "recommendation": "Command is old, check execution status and results"
                })
        elif context == "log_entry":
            if diff_seconds < 3600:  # 1 hour
                analysis.update({
                    "log_relevance": "recent",
                    "urgency": "medium",
                    "recommendation": "Recent log entry, may be relevant to current issues"
                })
            else:
                analysis.update({
                    "log_relevance": "historical", 
                    "urgency": "low",
                    "recommendation": "Historical log entry, useful for trend analysis"
                })
        else:
            analysis.update({
                "general_status": "analyzed",
                "recommendation": f"Timestamp analyzed in context: {context}"
            })
        
        return analysis
        
    except ValueError:
        return {
            "error": f"Invalid timestamp: {timestamp}",
            "recommendation": "Provide valid Unix timestamp as string"
        }
    except Exception as e:
        return {
            "error": f"Analysis failed: {str(e)}",
            "recommendation": "Check timestamp format and try again"
        }


# Resource for current time context
@mcp.resource("time://current")
async def get_current_time() -> str:
    """Provide current server time for timestamp interpretation and network management context."""
    current_time = int(time.time())
    readable_time = datetime.fromtimestamp(current_time, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
    
    return f"""Current Server Time Information:

Unix Timestamp: {current_time}
Human Readable: {readable_time}

Device Status Guidelines for Network Management:
- ACTIVE (0-5 minutes ago): Device is healthy and communicating normally
- RECENT (5-30 minutes ago): Device was recently active, normal for some device types  
- STALE (30 minutes - 1 hour ago): May indicate connectivity issues, monitor closely
- OLD (1-24 hours ago): Likely connectivity problems, investigation recommended
- OFFLINE (>24 hours ago): Device appears offline, immediate attention required

Urgency Levels:
- LOW: No immediate action needed
- MEDIUM: Monitor situation, check if pattern continues
- HIGH: Investigation recommended within next few hours
- CRITICAL: Immediate investigation and action required

Use this context when analyzing device timestamps and making network management recommendations."""

# Resource for API information
@mcp.resource("api://info")
async def get_api_info() -> str:
    """Provide information about the wrapped REST API."""
    return json.dumps({
        "name": "NIMBL API Wrapper",
        "description": "MCP wrapper for NIMBL REST API",
        "base_url": BASE_URL,
        "available_endpoints": [
            "GET /devices - Get all devices",
            "POST /commands - Create new commands",
            "GET /commands?cmd=all - Get all commands",
        ],
        "tools": [
            "get_devices", "get_commands", 
            "post_commands"
        ]
    }, indent=2)

# Resource for NIMBL command manual
@mcp.resource("manual://commands")
async def get_command_manual() -> str:
    """Provide the complete NIMBL command manual for reference."""
    manual_content = """
# NIMBL Command Manual

This manual contains all available commands that can be sent via the post_command tool.

## Authentication Command
```
config user [mac address] [username] [password]
Example: config user AA-BB-CC-DD-EE-FF admin default
```

## Device Control Commands
```
beep [mac address]
Example: beep AA-BB-CC-DD-EE-FF

reset [mac address] 
Example: reset AA-BB-CC-DD-EE-FF

mtderase [mac address]
Example: mtderase AA-BB-CC-DD-EE-FF
```

## Network Configuration
```
config network set [mac address] [ip address] [new ip] [mask] [gateway] [hostname] [dhcp]
Example: config network set AA-BB-CC-DD-EE-FF ********* *********0 *********** 0.0.0.0 switch 0
```

## Device Scanning
```
scan [protocol]
Example: scan snmp
```

## GWD Commands
```
gwd beep [mac address]
gwd reset [mac address]
gwd config network set [mac address] [ip address] [new ip] [mask] [gateway] [hostname]
gwd firmware update [mac address] [file url]
gwd mtderase [mac address]
```

## Syslog Configuration
```
config syslog set [mac address] [status] [server ip] [server port] [server level] [log to flash]
Example: config syslog set AA-BB-CC-DD-EE-FF 1 ********* 5514 1 1

config syslog get [mac address]
Example: config syslog get 00-60-E9-18-3C-3C
```

## SNMP Commands
```
snmp enable [mac address]
snmp disable [mac address]
snmp trap add [mac address] [server ip] [server port] [community]
snmp trap del [mac address] [server ip] [server port] [community]
snmp get [ip address] [oid]
snmp set [ip address] [oid] [value] [value type]
```

## Firmware Update
```
firmware update [mac address] [file url]
Example: firmware update AA-BB-CC-DD-EE-FF https://www.atoponline.com/.../EHG750X-K770A770.zip
```

## Agent Commands
```
agent reset [mac address]
agent snmp enable [mac address] [option]
agent firmware update [mac address] [file url]
agent beep [mac address]
agent config syslog set [mac address] [enable] [server ip] [server port] [log level] [log to flash]
agent config syslog get [mac address]
agent config network set [mac address] [ip] [mask] [gateway] [hostname] [dhcp]
```

## Usage with post_command Tool
To execute any of these commands, use the post_command tool with the command string as the parameter.
Example: post_command("beep AA-BB-CC-DD-EE-FF")
"""
    return manual_content

# Resource for NIMBL command manual (loaded from file)
@mcp.resource("manual://commands-from-file")
async def get_command_manual_from_file() -> str:
    """Load the complete NIMBL command manual from the markdown file."""
    try:
        # Path to your markdown file
        manual_path = r"d:\code\golang\mnms\examples\06-Script-commands.md"
        with open(manual_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add usage instructions at the beginning
        usage_note = """
# How to Use This Manual with MCP Tools

This manual contains all available NIMBL commands. To execute any command:
1. Use the post_command tool with the command string
2. Replace [mac address] with actual MAC address (e.g., AA-BB-CC-DD-EE-FF)
3. Replace other parameters with actual values

Example: post_command("beep AA-BB-CC-DD-EE-FF")

---

"""
        return usage_note + content
        
    except FileNotFoundError:
        return "Error: Command manual file not found at specified path."
    except Exception as e:
        return f"Error loading command manual: {str(e)}"

async def cleanup():
    """Clean up resources when shutting down."""
    await client.aclose()

async def main():
    """Run the MCP server."""
    try:
        # Run the MCP server
        await mcp.run_async()
    finally:
        # Clean up HTTP client
        await cleanup()

if __name__ == "__main__":
    # Run the server
    asyncio.run(main())
