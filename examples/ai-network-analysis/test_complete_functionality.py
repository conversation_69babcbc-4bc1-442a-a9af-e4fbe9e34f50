#!/usr/bin/env python3
"""
Test script to demonstrate the complete timestamp enhancement functionality
"""
import asyncio
import json
import sys
from fastmcp import Client as mcp_client

async def test_complete_functionality():
    """Test all enhanced functionality including timestamps"""
    print("Testing complete MCP functionality with timestamp enhancements...")
    
    # Configure servers
    servers = {
        "nimble-api-wrapper": {
            "command": "python",
            "args": ["nimbl_mcp.py"]
        }
    }
    
    try:
        # Create client
        print("Creating MCP client...")
        client = mcp_client(servers)
        
        # Connect
        print("Connecting to MCP server...")
        await client._connect()
        print("✓ Connected successfully")
        
        # List resources
        print("\nListing available resources...")
        try:
            resources = await client.list_resources()
            print(f"✓ Found {len(resources)} resources:")
            for resource in resources:
                print(f"  - {resource.uri}: {resource.description}")
        except Exception as e:
            print(f"✗ Resource listing failed: {e}")
        
        # Test get_devices with timestamp analysis
        print("\nTesting get_devices with timestamp analysis...")
        try:
            result = await asyncio.wait_for(
                client.call_tool("get_devices", arguments={}),
                timeout=30.0
            )
            
            print(f"✓ Devices retrieved successfully")
            print(f"  Result type: {type(result)}")
            
            # If result is a list, it should contain TextContent objects
            if isinstance(result, list) and len(result) > 0:
                content = result[0]
                if hasattr(content, 'text'):
                    devices_data = json.loads(content.text)
                    
                    if 'metadata' in devices_data:
                        metadata = devices_data['metadata']
                        print(f"  Total devices: {metadata.get('total_devices', 'N/A')}")
                        print(f"  Server time: {metadata.get('server_time_human', 'N/A')}")
                    
                    if 'devices' in devices_data:
                        devices = devices_data['devices']
                        print(f"  Sample device timestamp analysis:")
                        
                        for i, (device_mac, device_data) in enumerate(devices.items()):
                            if i >= 1:  # Show only first device
                                break
                                
                            print(f"    Device: {device_mac}")
                            print(f"      Model: {device_data.get('modelname', 'N/A')}")
                            print(f"      Last seen: {device_data.get('last_seen_human', 'N/A')}")
                            print(f"      Status: {device_data.get('device_status', 'N/A')}")
                            print(f"      Urgency: {device_data.get('urgency_level', 'N/A')}")
                            print(f"      Recommendation: {device_data.get('recommendation', 'N/A')}")
            
        except Exception as e:
            print(f"✗ Devices test failed: {e}")
        
        # Test get_commands with timestamp analysis
        print("\nTesting get_commands with timestamp analysis...")
        try:
            result = await asyncio.wait_for(
                client.call_tool("get_commands", arguments={}),
                timeout=30.0
            )
            
            print(f"✓ Commands retrieved successfully")
            
            # Parse result properly
            if isinstance(result, list) and len(result) > 0:
                content = result[0]
                if hasattr(content, 'text'):
                    commands_data = json.loads(content.text)
                    
                    if 'metadata' in commands_data:
                        metadata = commands_data['metadata']
                        print(f"  Total commands: {metadata.get('total_commands', 'N/A')}")
                        print(f"  Server time: {metadata.get('server_time_human', 'N/A')}")
                        print(f"  Analysis note: {metadata.get('analysis_note', 'N/A')}")
                    
                    if 'commands' in commands_data:
                        commands = commands_data['commands']
                        print(f"  Sample command timestamp analysis:")
                        
                        for i, (cmd_key, cmd_data) in enumerate(commands.items()):
                            if i >= 1:  # Show only first command
                                break
                                
                            print(f"    Command: {cmd_data.get('command', 'N/A')}")
                            print(f"      Status: {cmd_data.get('status', 'N/A')}")
                            print(f"      Created: {cmd_data.get('created_human', 'N/A')}")
                            print(f"      Created (UTC): {cmd_data.get('created_utc', 'N/A')}")
                            print(f"      Time Status: {cmd_data.get('time_status', 'N/A')}")
                            print(f"      Urgency: {cmd_data.get('urgency_level', 'N/A')}")
                            print(f"      Recommendation: {cmd_data.get('recommendation', 'N/A')}")
                            print(f"      Execution Context: {cmd_data.get('execution_context', 'N/A')}")
            
        except Exception as e:
            print(f"✗ Commands test failed: {e}")
        
        # Test analyze_timestamp tool
        print("\nTesting analyze_timestamp tool...")
        try:
            result = await asyncio.wait_for(
                client.call_tool("analyze_timestamp", arguments={
                    "timestamp": "1749702320",
                    "context": "device_last_seen"
                }),
                timeout=15.0
            )
            
            print(f"✓ Timestamp analysis successful")
            
            # Parse result properly
            if isinstance(result, list) and len(result) > 0:
                content = result[0]
                if hasattr(content, 'text'):
                    analysis_data = json.loads(content.text)
                    
                    print(f"  Relative time: {analysis_data.get('relative_time', 'N/A')}")
                    print(f"  Device status: {analysis_data.get('device_status', 'N/A')}")
                    print(f"  Urgency: {analysis_data.get('urgency', 'N/A')}")
                    print(f"  Recommendation: {analysis_data.get('recommendation', 'N/A')}")
                    print(f"  Network impact: {analysis_data.get('network_impact', 'N/A')}")
            
        except Exception as e:
            print(f"✗ Timestamp analysis failed: {e}")
        
        # Test sleep_seconds for sequential operations
        print("\nTesting sleep_seconds for sequential operations...")
        try:
            result = await asyncio.wait_for(
                client.call_tool("sleep_seconds", arguments={"seconds": 2}),
                timeout=10.0
            )
            
            print(f"✓ Sleep tool successful")
            
            # Parse result
            if isinstance(result, list) and len(result) > 0:
                content = result[0]
                if hasattr(content, 'text'):
                    sleep_data = json.loads(content.text)
                    print(f"  Message: {sleep_data.get('message', 'N/A')}")
                    print(f"  Wait time: {sleep_data.get('wait_seconds', 'N/A')} seconds")
            
        except Exception as e:
            print(f"✗ Sleep tool failed: {e}")
        
        # Close connection
        print("\nClosing connection...")
        await client.close()
        print("✓ Connection closed")
        
    except Exception as e:
        print(f"✗ Complete functionality test failed: {e}")
        return False
    
    return True

def main():
    """Run the test"""
    try:
        success = asyncio.run(test_complete_functionality())
        if success:
            print("\n" + "="*60)
            print("✓ TIMESTAMP ENHANCEMENT TESTING COMPLETED SUCCESSFULLY")
            print("="*60)
            print("\n🎉 All enhanced MCP functionality is working:")
            print("   • Device timestamps → human-readable with status analysis")
            print("   • Command timestamps → ISO 8601 processing with execution context")
            print("   • Sequential operations → sleep tool for timing control")
            print("   • Timestamp analysis → dedicated tool for detailed insights")
            print("   • MCP resources → time context and command manuals")
            print("\n✅ System ready for LLM integration with temporal intelligence!")
            sys.exit(0)
        else:
            print("\n✗ Complete functionality test failed")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n✗ Test interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n✗ Test error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
