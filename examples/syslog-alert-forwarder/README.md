## Configuration

The configuration file `syslog_forwarder.json.template` contains settings for all platforms and alert rules.


### WhatsApp Configuration (via Twilio)

```json
{
  "whatsapp": {
    "enabled": true,
    "account_sid": "YOUR_TWILIO_ACCOUNT_SID",
    "auth_token": "YOUR_TWILIO_AUTH_TOKEN", 
    "from_number": "+***********",
    "to_numbers": ["+**********"],
    "alert_config": {
      "min_severity": 0,
      "max_severity": 3,
      "rate_limit_seconds": 300,
      "max_alerts_per_minute": 5,
      "keywords": ["error", "fail", "critical"],
      "exclude_keywords": ["test", "debug"]
    }
  }
}
```

#### Setting up Twilio WhatsApp:
1. Create a Twilio account at https://www.twilio.com
2. Get your Account SID and Auth Token from the console
3. Enable WhatsApp sandbox or get approved WhatsApp number
4. Use the sandbox number as `from_number`
5. Send "join &lt;your-sandbox-name&gt;" to the sandbox number from recipient phones

### Telegram Configuration

```json
{
  "telegram": {
    "enabled": true,
    "bot_token": "YOUR_BOT_TOKEN",
    "chat_ids": ["@your_channel", "*********"],
    "alert_config": {
      "min_severity": 0,
      "max_severity": 4,
      "rate_limit_seconds": 180,
      "max_alerts_per_minute": 10,
      "keywords": ["error", "fail", "warning"],
      "exclude_keywords": ["test"]
    }
  }
}
```

#### Setting up Telegram Bot:

**Step 1: Create the Bot**
1. Message @BotFather on Telegram
2. Send `/newbot` command
3. Choose a name and username for your bot
4. Copy the bot token (like `**********:AAFVh90KZ0MpbQg5xaONGOvej9E1V1KsSMY`)

**Step 2: Get Your Chat ID (for personal messages)**
1. Message @userinfobot on Telegram
2. It will reply with your user ID (like `7534357522`)
3. Use this number in the `chat_ids` array

**Step 3: Optional - Set up Channel/Group Alerts**
For channel or group alerts, you have two options:

**Option A: Public Channel**
- Create a public channel (e.g., `@mynms_alerts`)
- Add your bot as an administrator
- Use the channel name: `"@mynms_alerts"`

**Option B: Private Channel/Group**
- Create a private channel or group
- Add your bot as administrator
- Send a message in the channel, then forward it to @userinfobot to get the chat ID
- Use the negative number ID (e.g., `"-100**********"`)

**Current Setup:**
Your config shows:
- Personal alerts: `"7534357522"` ✅ (your user ID)  
- Channel placeholder: `"@your_channel"` ❌ (replace with actual channel name or remove)

### MQTT Configuration

```json
{
  "mqtt": {
    "enabled": true,
    "broker_host": "localhost",
    "broker_port": 1883,
    "username": "",
    "password": "",
    "topic": "mnms/alerts",
    "qos": 1,
    "retain": false,
    "alert_config": {
      "min_severity": 0,
      "max_severity": 7,
      "rate_limit_seconds": 60,
      "max_alerts_per_minute": 20
    }
  }
}
```

## Syslog Severity Levels

The script recognizes standard syslog severity levels:

| Level | Name          | Description                      |
| ----- | ------------- | -------------------------------- |
| 0     | Emergency     | System is unusable               |
| 1     | Alert         | Action must be taken immediately |
| 2     | Critical      | Critical conditions              |
| 3     | Error         | Error conditions                 |
| 4     | Warning       | Warning conditions               |
| 5     | Notice        | Normal but significant condition |
| 6     | Informational | Informational messages           |
| 7     | Debug         | Debug-level messages             |

## Alert Configuration Options

### Severity Filtering
- `min_severity`: Minimum severity level to forward (0-7)
- `max_severity`: Maximum severity level to forward (0-7)

### Rate Limiting
- `rate_limit_seconds`: Minimum time between identical messages
- `max_alerts_per_minute`: Maximum alerts per minute per platform

### Keyword Filtering
- `keywords`: Only forward messages containing these keywords (empty = all)
- `exclude_keywords`: Never forward messages containing these keywords

## Message Formats

### WhatsApp Message
```
🚨 MNMS Alert 🚨
Time: 2025-02-06 12:04:06
Host: root
Severity: ALERT (1)
Tag: AutoUpdateService
Message: The root service root have new version, please update root service.
```

### Telegram Message
```
🚨 *MNMS Alert* 🚨
*Time:* 2025-02-06 12:04:06
*Host:* `root`
*Severity:* 🟠 ALERT
*Tag:* `AutoUpdateService`
*Message:* The root service root have new version, please update root service.
```

### MQTT Message (JSON)
```json
{
  "timestamp": "2025-02-06T12:04:06",
  "hostname": "root",
  "facility": 0,
  "severity": 1,
  "priority": 1,
  "tag": "AutoUpdateService",
  "message": "The root service root have new version, please update root service.",
  "raw_line": "<1>Feb  6 12:04:06 root AutoUpdateService: The root service root have new version, please update root service."
}
```