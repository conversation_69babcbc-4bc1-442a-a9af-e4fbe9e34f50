# Quick Guide
Run following commands step by step in the /mnms directory.

# 0) create bridge 
sudo ./examples/bbsim up

# 1) start root service (in background)
sudo ./bbrootsvc/bbrootsvc -n root >/tmp/bbrootsvc.log 2>&1 &

# 2) start many NMS (each in a namespace)
sudo ./examples/bbsim run nms1 "./bbnmssvc/bbnmssvc -n nms1 -r http://10.20.0.1:27182"
sudo ./examples/bbsim run nms2 "./bbnmssvc/bbnmssvc -n nms2 -r http://10.20.0.1:27182"
## （use loop）
for s in nms1 nms2 nms3; do
  sudo ./examples/bbsim run "$s" "./bbnmssvc/bbnmssvc -n $s -r http://10.20.0.1:27182"
done

# 3) add device simulators sample
ROOT_URL="http://10.20.0.1:27182"
for nms in nms1 nms2 nms3; do
  for i in $(seq -w 01 30); do
    name="${nms}-dev${i}"
    sudo ./examples/bbsim run "$name" "./devicesim --name $name --root $ROOT_URL --nms $nms"
  done
done

# Commands
sudo ./bbsim up            # create bridge (if not exist)
sudo ./bbsim list          # list all namespaces
sudo ./bbsim term nms1     # go to nms1 namespace bash shell
sudo ./bbsim kill nms1     # kill nms1 namespace
sudo ./bbsim kill-all      # kill all namespaces
sudo ./bbsim clean         # kill all namespaces and delete bridge


