#!/bin/bash

URL=http://localhost:27182
if [ $# -lt 1 ]; then
    echo Usage: $0 api_endpoint_url
    echo Example: $0 api/v1/clients
    exit 1
fi
apiendpoint=$*
echo apiendpoint $apiendpoint

if [ "$apiendpoint" == "" ]; then
	echo 'no api endpoint specified'
	exit 1
fi
token=$(./get_token.sh)
if [ "$token" == "" ]; then
	echo 'no token'
	exit 1
fi
dest=$URL/$apiendpoint
resp=$(curl -i -s -N -H "Authorization: Bearer $token" "$dest")
echo "response: $resp"
