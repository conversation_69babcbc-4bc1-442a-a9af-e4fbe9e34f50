#!/bin/bash

# This is an example bash script to show using nms get/post api

# This script gets list of client services from nms,
# it then removes any non active services
# from the list, and then posts the edited version of services with
# only active services to nms, effectively cleaning out all
# non active services in the client services nms keeps track of.

# This script uses other scripts in this directory. Namely
# get_clients.sh which gets the list of client services using
# another script api_get.sh which is used to send GET http request.
# This script also uses jq to edit the JSON content. It uses
# jq to select only active services and reconstructs JSON in
# the format expected by nms.
# This script also uses api_post.sh to send POST http request to nms.

jsonres=$(./get_clients.sh )
active_clients=$(echo $jsonres | jq '.[]|select(.status == "active")|{(.name): .}' | jq -s add|jq -c .)
postres=$(./api_post.sh "api/v1/clients" "$active_clients" )
echo $postres
