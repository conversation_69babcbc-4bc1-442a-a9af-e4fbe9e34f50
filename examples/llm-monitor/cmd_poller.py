import time
import logging
import json
import os
import sys
import threading
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from pathlib import Path

# Platform-specific keyboard input handling
if os.name == 'nt':  # Windows
    import msvcrt
else:  # Unix/Linux/Mac
    import select
    import termios
    import tty

try:
    import requests
    from openai import OpenAI
except ImportError as e:
    missing = str(e).split("'")[1]
    print(f"❌ Missing dependency {missing}. Install with:")
    print("   pip install requests openai")
    sys.exit(1)

# ---------------------------------------------------------------------------
# Configuration – adjust these values for your environment
# ---------------------------------------------------------------------------
BASE_URL = "http://localhost:27182"  # API server base URL
USERNAME = "admin"                   # Login username
PASSWORD = "default"                 # Login password
POLL_INTERVAL_SEC = 300              # 5 minutes
SUMMARY_INTERVAL_SEC = 1800          # 30 minutes for summary generation
DATA_STORAGE_PATH = ".command_data"  # Directory to store command data and summaries

# OpenAI Configuration
OPENAI_MODEL = "o4-mini"  # Using o1-mini as requested
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")  # Set via environment variable

# ---------------------------------------------------------------------------
# Data structures for command monitoring
# ---------------------------------------------------------------------------

@dataclass
class CommandEntry:
    """Represents a single command entry with all its metadata."""
    index: str
    command: str
    timestamp: str
    status: str
    client: str
    verify: Optional[str] = None
    response: Optional[str] = None
    
    def is_failed(self) -> bool:
        """Check if command has failed (status != 'ok')."""
        return self.status != "ok"
    
    def is_unverified(self) -> bool:
        """Check if command is unverified."""
        return self.verify != "ok"

@dataclass
class CommandSummaryData:
    """Container for command summary analysis data."""
    failed_commands: List[CommandEntry]
    client_traffic: Dict[str, int]
    unverified_commands: List[CommandEntry]
    special_attention_items: List[str]
    first_summary_time: Optional[str]
    current_summary_time: str
    total_commands: int
    cumulative_summaries_count: int = 0
    cumulative_failed_commands: int = 0
    cumulative_total_commands: int = 0
    previous_summary_data: Optional[str] = None
    
class CommandMonitor:
    """Command monitoring and reporting system."""
    
    def __init__(self, storage_path: str = DATA_STORAGE_PATH):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        self.command_history_file = self.storage_path / "command_history.json"
        self.summary_history_file = self.storage_path / "summary_history.json"
        self.last_summary_file = self.storage_path / "last_summary.md"
        # Reset to first time on each run
        self.first_summary_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.cumulative_stats = {
            "summaries_count": 0,
            "total_failed_commands": 0,
            "total_commands_processed": 0,
            "last_summary_time": None
        }
        # Only load previous summary content, not cumulative stats
        self._load_previous_summary_only()
        
        # Initialize OpenAI client
        if OPENAI_API_KEY:
            self.openai_client = OpenAI(api_key=OPENAI_API_KEY)
        else:
            self.openai_client = None
            logger.warning("⚠️ OPENAI_API_KEY not set. Summary generation will be disabled.")
    
    def _load_previous_summary_only(self):
        """Load only the previous summary content, reset stats."""
        # Don't load cumulative stats - start fresh each time
        pass
    
    def _save_cumulative_stats(self):
        """Save cumulative statistics to persist across runs."""
        summary_data = {
            "first_summary_time": self.first_summary_time,
            "cumulative_stats": self.cumulative_stats
        }
        with open(self.summary_history_file, 'w') as f:
            json.dump(summary_data, f, indent=2)
    
    def get_last_summary(self) -> Optional[str]:
        """Get the content of the last generated summary."""
        if self.last_summary_file.exists():
            with open(self.last_summary_file, 'r', encoding='utf-8') as f:
                return f.read()
        return None
    
    def display_last_summary_info(self):
        """Display information about the last summary and cumulative statistics."""
        print("\n" + "="*80)
        print("📋 SESSION SUMMARY INFORMATION")
        print("="*80)
        
        print(f"📊 Current Session Statistics:")
        print(f"  • Session summaries generated: {self.cumulative_stats['summaries_count']}")
        print(f"  • Session commands processed: {self.cumulative_stats['total_commands_processed']}")
        print(f"  • Session failed commands: {self.cumulative_stats['total_failed_commands']}")
        print(f"  • Session start time: {self.first_summary_time}")
        if self.cumulative_stats['last_summary_time']:
            print(f"  • Last summary generated: {self.cumulative_stats['last_summary_time']}")
        print()
        
        last_summary = self.get_last_summary()
        if last_summary:
            print("📄 Previous Summary (kept for context):")
            print("-" * 60)
            lines = last_summary.split('\n')
            for i, line in enumerate(lines[:8]):
                print(line)
            if len(lines) > 8:
                print(f"... ({len(lines) - 8} more lines)")
            print("-" * 60)
        else:
            print("📭 No previous summary found - starting fresh.")
        
        print("="*80)

    def store_commands(self, commands: Dict[str, Any]):
        """Store command data with timestamp for analysis."""
        timestamp = datetime.now().isoformat()
        entry = {
            "timestamp": timestamp,
            "commands": commands
        }
        
        # Load existing history
        history = []
        if self.command_history_file.exists():
            with open(self.command_history_file, 'r') as f:
                history = json.load(f)
        
        # Add new entry
        history.append(entry)
        
        # Keep only last 1000 entries to prevent file from growing too large
        if len(history) > 1000:
            history = history[-1000:]
        
        # Save updated history
        with open(self.command_history_file, 'w') as f:
            json.dump(history, f, indent=2)
    
    def parse_commands_to_entries(self, commands: Dict[str, Any]) -> List[CommandEntry]:
        """Parse raw command data into CommandEntry objects."""
        entries = []
        for cmd_key, cmd_info in commands.items():
            if isinstance(cmd_info, dict):
                entry = CommandEntry(
                    index=str(cmd_info.get('index', 'N/A')),
                    command=cmd_key,
                    timestamp=cmd_info.get('timestamp', 'N/A'),
                    status=cmd_info.get('status', ''),
                    client=cmd_info.get('client', '—'),
                    verify=cmd_info.get('verify'),
                    response=cmd_info.get('response')
                )
                entries.append(entry)
        return entries
    
    def analyze_commands(self, commands: Dict[str, Any]) -> CommandSummaryData:
        """Analyze command data and generate summary statistics."""
        entries = self.parse_commands_to_entries(commands)
        
        # Analyze failed commands
        failed_commands = [entry for entry in entries if entry.is_failed()]
        
        # Analyze client traffic
        client_traffic = {}
        for entry in entries:
            client = entry.client if entry.client != '—' else '(none recorded)'
            client_traffic[client] = client_traffic.get(client, 0) + 1
        
        # Analyze unverified commands
        unverified_commands = [entry for entry in entries if entry.is_unverified()]
        
        # Generate special attention items
        special_attention_items = self._generate_special_attention_items(
            failed_commands, unverified_commands, entries
        )
        
        # Update session statistics (not cumulative across runs)
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.cumulative_stats['summaries_count'] += 1
        self.cumulative_stats['total_failed_commands'] += len(failed_commands)
        self.cumulative_stats['total_commands_processed'] += len(entries)
        self.cumulative_stats['last_summary_time'] = current_time
        
        # Get previous summary data
        previous_summary = self.get_last_summary()
        
        return CommandSummaryData(
            failed_commands=failed_commands,
            client_traffic=client_traffic,
            unverified_commands=unverified_commands,
            special_attention_items=special_attention_items,
            first_summary_time=self.first_summary_time,
            current_summary_time=current_time,
            total_commands=len(entries),
            cumulative_summaries_count=self.cumulative_stats['summaries_count'],
            cumulative_failed_commands=self.cumulative_stats['total_failed_commands'],
            cumulative_total_commands=self.cumulative_stats['total_commands_processed'],
            previous_summary_data=previous_summary
        )
    
    def _save_first_summary_time(self):
        """Save the first summary time to persist across runs."""
        self._save_cumulative_stats()
    
    def _generate_special_attention_items(self, failed_commands: List[CommandEntry], 
                                        unverified_commands: List[CommandEntry],
                                        all_commands: List[CommandEntry]) -> List[str]:
        """Generate special attention items based on command analysis."""
        items = []
        
        # Group failed commands by error type
        error_groups = {}
        for cmd in failed_commands:
            error_type = cmd.status
            if error_type not in error_groups:
                error_groups[error_type] = []
            error_groups[error_type].append(cmd)
        
        # Add items for each error group
        for error_type, commands in error_groups.items():
            if len(commands) == 1:
                cmd = commands[0]
                items.append(f"**Command failure** – `{cmd.command}` failed with \"{error_type}\". "
                           f"Investigate and retry if necessary.")
            else:
                items.append(f"**Multiple {error_type} failures** – {len(commands)} commands failed "
                           f"with \"{error_type}\". Review system configuration.")
        
        # Add item for unverified commands
        if unverified_commands:
            if len(unverified_commands) == 1:
                cmd = unverified_commands[0]
                items.append(f"**Unverified command** – `{cmd.command}` has no verification status. "
                           f"Follow up to confirm execution.")
            else:
                items.append(f"**Multiple unverified commands** – {len(unverified_commands)} commands "
                           f"lack verification. Review execution status.")
        
        # Add item for blank status commands
        blank_status_commands = [cmd for cmd in all_commands if cmd.status == '']
        if blank_status_commands:
            items.append(f"**Blank status commands** – {len(blank_status_commands)} commands "
                       f"returned empty status. Investigate system response handling.")
        
        return items
    
    def prepare_llm_prompt(self, commands: Dict[str, Any], summary_data: CommandSummaryData) -> str:
        """Prepare the prompt for LLM to generate summary."""
        
        # Format command data for LLM
        commands_json = json.dumps(commands, indent=2, ensure_ascii=False)
        
        prompt = f"""Please generate a comprehensive command activity summary based on the following data:

**FETCHING DATA:**
```json
{commands_json}
```

**SUMMARY STATISTICS:**
- This is summary #{summary_data.cumulative_summaries_count} in current session
- Current period commands: {summary_data.total_commands}
- Current period failed commands: {len(summary_data.failed_commands)}
- Session total commands: {summary_data.cumulative_total_commands}
- Session failed commands: {summary_data.cumulative_failed_commands}
- Session start time: {summary_data.first_summary_time}
- This summary generated: {summary_data.current_summary_time}
"""

        # Add previous summary if available (for context, not cumulative)
        if summary_data.previous_summary_data:
            prompt += f"""

**PREVIOUS SUMMARY (for context only):**
```
{summary_data.previous_summary_data}
```

Please generate a new summary focusing on the current period data. Use the previous summary only for context and comparison, not for cumulative reporting.
"""
        else:
            # First summary - provide template
            prompt += f"""

**TEMPLATE FOR SUMMARY:**
```
{INITIAL_SUMMARY_TEMPLATE}
```

This is the first summary in this session. Please use the above template structure and fill in the real data from the fetching data provided.
"""

        prompt += """

Generate a comprehensive summary following the system prompt guidelines. Ensure the summary is clear, actionable, and focuses on the current session data."""

        return prompt
    
    def display_generation_details(self, commands: Dict[str, Any], summary_data: CommandSummaryData):
        """Display detailed information about summary generation process."""
        print("\n" + "🤖" * 80)
        print("GENERATING SUMMARY WITH LLM")
        print("🤖" * 80)
        
        # Show system prompt info
        print("📋 SYSTEM PROMPT:")
        system_prompt_preview = SYSTEM_PROMPT[:100] + "..." if len(SYSTEM_PROMPT) > 100 else SYSTEM_PROMPT
        print(f"  Length: {len(SYSTEM_PROMPT)} characters")
        print(f"  Preview: {system_prompt_preview}")
        print()
        
        # Show fetching data info
        commands_json = json.dumps(commands, indent=2, ensure_ascii=False)
        print("📊 FETCHING DATA:")
        print(f"  Total commands: {len(commands)}")
        print(f"  JSON length: {len(commands_json)} characters")
        data_preview = commands_json[:100] + "..." if len(commands_json) > 100 else commands_json
        print(f"  Preview: {data_preview}")
        print()
        
        # Show previous summary info
        if summary_data.previous_summary_data:
            print("📄 PREVIOUS SUMMARY (for context):")
            print(f"  Length: {len(summary_data.previous_summary_data)} characters")
            prev_preview = summary_data.previous_summary_data[:100] + "..." if len(summary_data.previous_summary_data) > 100 else summary_data.previous_summary_data
            print(f"  Head 100 chars: {prev_preview}")
        else:
            print("📄 PREVIOUS SUMMARY: None (first summary)")
        
        print("\n🎯 LLM Generation Parameters:")
        print(f"  Model: {OPENAI_MODEL}")
        print(f"  Session summary #: {summary_data.cumulative_summaries_count}")
        
        print("\n⏳ Calling OpenAI API...")
        print("🤖" * 80)
    
    def generate_summary_with_llm(self, commands: Dict[str, Any], summary_data: CommandSummaryData) -> Optional[str]:
        """Generate summary using OpenAI LLM."""
        if not self.openai_client:
            logger.error("❌ OpenAI client not initialized. Cannot generate summary.")
            return None
        
        try:
            # Display generation details
            self.display_generation_details(commands, summary_data)
            
            prompt = self.prepare_llm_prompt(commands, summary_data)
            
            logger.info("🤖 Generating summary with OpenAI...")
            
            response = self.openai_client.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": prompt}
                ]
            )
            
            summary = response.choices[0].message.content
            logger.info("✅ Summary generated successfully")
            
            print("\n" + "✅" * 80)
            print("SUMMARY GENERATION COMPLETED")
            print("✅" * 80)
            
            return summary
            
        except Exception as exc:
            logger.error("❌ Failed to generate summary with LLM: %s", exc)
            return None
    
    def save_summary_report(self, report: str):
        """Save the summary report to file."""
        if report is None:
            logger.error("❌ Cannot save None report")
            return None
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"command_summary_{timestamp}.md"
        filepath = self.storage_path / filename
        
        # Save timestamped version
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # Save as last summary
        with open(self.last_summary_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # Save cumulative statistics
        self._save_cumulative_stats()
        
        logger.info(f"📊 Summary report saved to: {filepath}")
        return filepath

# ---------------------------------------------------------------------------
# System prompt for AI analysis
# ---------------------------------------------------------------------------

SYSTEM_PROMPT = """# Command Data Summary System Prompt

You are a command monitoring and reporting system. Your task is to analyze command execution data and generate comprehensive English summaries following these specific requirements:

## Summary Structure Requirements

### 1. Summary Header Information
- **Initial Summary Start Time**: Include the timestamp of the very first summary (only required for the first summary report)
- **Current Summary Timestamp**: Always include the timestamp when this current summary is generated
- **Reporting Period**: Specify the time range covered by this summary

### 2. Failed Command Analysis
- **Categorize Non-OK Status Commands**: Group and list all commands where status ≠ "ok"
  - Group by error type/category
  - Include command details, timestamps, and failure reasons
  - Provide count of failures per category
  - Highlight recurring failure patterns
  - Empty status commands should be tag pending and be included in the summary

### 3. Client Traffic Analysis
- **Per-Client Traffic Summary**: Generate traffic statistics for each client
  - Total commands executed per client
  - Success rate per client
  - Peak usage periods per client
  - Bandwidth/resource usage metrics if available

### 4. Unverified Command Tracking
- **Pending Verification Status**: Track and list commands that haven't been verified yet
  - Command IDs and timestamps
  - Duration since execution
  - Priority level for verification
  - Escalation recommendations for long-pending verifications

### 5. Cumulative Reporting
- **Historical Integration**: Each summary must include data from the previous summary period
  - Aggregate trends and patterns
  - Compare current period with previous periods
  - Identify improving or deteriorating metrics
  - Maintain continuity of critical issues across reporting periods

### 6. Special Attention Items
- **Critical Alerts and Notices**: Highlight items requiring immediate attention
  - Security concerns or anomalies
  - Performance degradation indicators
  - System health warnings
  - Resource threshold breaches
  - Compliance or operational risks

## Output Format Guidelines

- Use clear, professional English
- Organize information hierarchically with proper headings
- Include quantitative data with percentages and totals
- Use bullet points for lists and detailed breakdowns
- Highlight critical information with appropriate formatting
- Provide actionable recommendations where applicable
- Maintain consistent terminology and formatting across all summaries

## Data Processing Instructions

1. Parse all command data systematically
2. Validate timestamp formats and ensure chronological accuracy
3. Cross-reference command statuses with client information
4. Calculate metrics and trends accurately
5. Identify anomalies and patterns in the data
6. Preserve historical context from previous summaries
7. Flag any data inconsistencies or gaps

## Reporting Priorities

1. **Critical Issues**: System failures, security breaches, or operational disruptions
2. **Performance Metrics**: Efficiency trends, response times, and throughput analysis
3. **Client Management**: Individual client performance and usage patterns
4. **Operational Health**: Overall system status and maintenance requirements
5. **Trend Analysis**: Long-term patterns and predictive insights

Generate each summary as a comprehensive, standalone document that provides both immediate operational insights and strategic overview for stakeholders."""

# ---------------------------------------------------------------------------
# Summary Template for first-time generation
# ---------------------------------------------------------------------------

INITIAL_SUMMARY_TEMPLATE = """### Command Activity Summary

**Log start time (first summary):** {log_start_time}
**This summary generated:** {summary_time}

---

#### 1. Commands with *status ≠ `ok`*

| Index                              | Timestamp     | Command     | Status           | Client     |
| ---------------------------------- | ------------- | ----------- | ---------------- | ---------- |
| `{index}`                          | `{timestamp}` | `{command}` | `{error_status}` | `{client}` |
| *(add one row per non-OK command)* |               |             |                  |            |

---

#### 2. Command traffic by client

| Client                     | Commands issued   |
| -------------------------- | ----------------- |
| `{client}`                 | `{command_count}` |
| *(add one row per client)* |                   |

---

#### 3. Un-verified commands (`verify` field not `ok`)

| Index                                   | Timestamp     | Command     | Client     |
| --------------------------------------- | ------------- | ----------- | ---------- |
| `{index}`                               | `{timestamp}` | `{command}` | `{client}` |
| *(add one row per un-verified command)* |               |             |            |

---

#### 4. Items requiring special attention

1. **{issue_title_1}** – {brief_description_and_recommendation_1}
2. **{issue_title_2}** – {brief_description_and_recommendation_2}
3. *(add additional bullet points as needed)*

---

> *Note: Use the placeholders above to insert real data for each new summary. Each future summary should append new findings while preserving previous entries to maintain a complete history.*
"""

# ---------------------------------------------------------------------------
# Logging setup
# ---------------------------------------------------------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# Initialize command monitor
command_monitor = CommandMonitor()

# ---------------------------------------------------------------------------
# Keyboard input handling (Windows optimized)
# ---------------------------------------------------------------------------

class KeyboardListener:
    """Windows-optimized keyboard input listener."""
    
    def __init__(self):
        self.interrupted = False
        self.skip_wait = False
    
    def is_key_pressed(self) -> Optional[str]:
        """Check if a key is pressed and return it (Windows optimized)."""
        if os.name == 'nt':  # Windows
            if msvcrt.kbhit():
                try:
                    key = msvcrt.getch()
                    # Handle both bytes and string
                    if isinstance(key, bytes):
                        key = key.decode('utf-8', errors='ignore')
                    return key.lower()
                except:
                    return None
        else:  # Unix/Linux/Mac (fallback)
            if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
                key = sys.stdin.read(1).lower()
                return key
        return None
    
    def clear_input_buffer(self):
        """Clear any pending keyboard input (Windows)."""
        if os.name == 'nt':
            while msvcrt.kbhit():
                msvcrt.getch()
    
    def wait_with_countdown(self, seconds: int) -> bool:
        """Wait with countdown display and keyboard monitoring.
        Returns True if wait completed normally, False if interrupted by 's' key."""
        
        # Clear any pending input first
        self.clear_input_buffer()
        
        print(f"\n⏰ Next fetch in {seconds} seconds...")
        print(f"🔥 Press 's' key anytime to fetch immediately!")
        print("=" * 60)
        
        for remaining in range(seconds, 0, -1):
            # Create a more visual countdown display
            progress_bar = "█" * (30 - int(30 * remaining / seconds)) + "░" * int(30 * remaining / seconds)
            
            # Display countdown with progress bar
            print(f"\r⏰ [{progress_bar}] {remaining:3d}s | Press 's' to skip", end='', flush=True)
            
            # Check for key press multiple times per second for better responsiveness
            for _ in range(20):  # Check 20 times per second
                key = self.is_key_pressed()
                if key == 's':
                    print(f"\n\n🚀 Key 's' pressed! Fetching immediately...")
                    print("=" * 60)
                    return False  # Interrupted
                elif key and key != 's':
                    # Clear any other keys pressed
                    continue
                
                time.sleep(0.05)  # Sleep 50ms (20 checks per second)
        
        print(f"\n\n✅ Countdown complete! Starting next fetch...")
        print("=" * 60)
        return True  # Completed normally

def countdown_wait_with_skip(seconds: int) -> bool:
    """Wait with countdown and ability to skip by pressing 's'.
    Returns True if wait completed, False if skipped."""
    
    listener = KeyboardListener()
    return listener.wait_with_countdown(seconds)

# ---------------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------------

def login(username: str, password: str) -> Optional[str]:
    """Authenticate and return JWT token. Returns None on failure."""
    try:
        url = f"{BASE_URL}/api/v1/login"
        resp = requests.post(url, json={"user": username, "password": password}, timeout=10)
        resp.raise_for_status()
        data: Dict[str, Any] = resp.json()
        token = data.get("token")
        if token:
            logger.debug("Obtained token: %s", token)
            return token
        logger.error("Login succeeded but no token found in response: %s", data)
    except Exception as exc:
        logger.error("Login request failed: %s", exc)
    return None

def fetch_all_commands(token: str) -> Optional[Dict[str, Any]]:
    """Retrieve all commands (cmd=all) using the provided JWT token."""
    try:
        print("\n" + "🔄" * 60)
        print("FETCHING COMMANDS FROM API")
        print("🔄" * 60)
        print("⏳ Fetching data from server...")
        
        url = f"{BASE_URL}/api/v1/commands"
        headers = {"Authorization": f"Bearer {token}"}
        params = {"cmd": "all"}
        resp = requests.get(url, headers=headers, params=params, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        
        # Display fetching results
        data_json = json.dumps(data, indent=2, ensure_ascii=False)
        print(f"✅ Data fetched successfully!")
        print(f"📊 Total items: {len(data) if isinstance(data, dict) else 'N/A'}")
        print(f"📄 JSON length: {len(data_json)} characters")
        print(f"📋 JSON preview (first 100 chars):")
        preview = data_json[:100] + "..." if len(data_json) > 100 else data_json
        print(f"   {preview}")
        print("🔄" * 60)
        
        return data
    except Exception as exc:
        logger.error("Fetching commands failed: %s", exc)
        print(f"❌ Fetching failed: {exc}")
        print("🔄" * 60)
    return None

def filter_recent_commands(commands: Dict[str, Any], is_first_run: bool = False) -> Dict[str, Any]:
    """Filter commands to only include those newer than POLL_INTERVAL_SEC.
    Skip filtering on first run to show all existing data."""
    if is_first_run:
        logger.info("First run - showing all existing commands")
        return commands
    
    cutoff_time = datetime.now() - timedelta(seconds=POLL_INTERVAL_SEC)
    filtered_commands = {}
    
    for cmd_key, cmd_info in commands.items():
        if isinstance(cmd_info, dict) and "timestamp" in cmd_info:
            try:
                # Parse ISO timestamp
                cmd_timestamp = datetime.fromisoformat(cmd_info["timestamp"].replace('+08:00', ''))
                if cmd_timestamp >= cutoff_time:
                    filtered_commands[cmd_key] = cmd_info
            except Exception as exc:
                logger.warning("Failed to parse timestamp for command '%s': %s", cmd_key, exc)
                # Include commands with unparseable timestamps to be safe
                filtered_commands[cmd_key] = cmd_info
        else:
            # Include commands without timestamp field
            filtered_commands[cmd_key] = cmd_info
    
    logger.info("Filtered %d recent commands from %d total", len(filtered_commands), len(commands))
    return filtered_commands

def generate_and_save_summary(commands: Dict[str, Any]):
    """Generate and save a summary report for the given commands."""
    try:
        # Display previous summary info before generating new one
        print("\n" + "="*80)
        print("🔄 PREPARING TO GENERATE NEW SUMMARY...")
        print("="*80)
        command_monitor.display_last_summary_info()
        
        summary_data = command_monitor.analyze_commands(commands)
        report = command_monitor.generate_summary_with_llm(commands, summary_data)
        
        if report is None:
            logger.error("❌ Failed to generate summary with LLM")
            return None
            
        filepath = command_monitor.save_summary_report(report)
        
        # Display final summary and keep it on screen
        display_final_summary(report)
        
        return filepath
    except Exception as exc:
        logger.error("Failed to generate summary report: %s", exc)
        return None

def display_final_summary(summary: str):
    """Display the final summary prominently and keep it on screen."""
    print("\n" + "🎉" * 80)
    print("FINAL SUMMARY GENERATED - KEEPING ON SCREEN")
    print("🎉" * 80)
    print()
    print(summary)
    print()
    print("🎉" * 80)
    print("SUMMARY COMPLETE - This summary will remain visible")
    print("🎉" * 80)
    print(f"\n💡 Next summary after next fetch (countdown will start shortly)")
    print(f"🔥 Press 's' during countdown to fetch immediately!")

# ---------------------------------------------------------------------------
# Main poll loop (modified)
# ---------------------------------------------------------------------------

# Global variables to track state
_is_first_run = True
_last_summary_time = datetime.now()
_startup_info_displayed = False

def poll_once():
    """One polling cycle: login then fetch commands."""
    global _is_first_run, _last_summary_time, _startup_info_displayed
    
    # Display startup information on first run
    if not _startup_info_displayed:
        print("\n" + "="*80)
        print("🚀 COMMAND POLLER STARTED - FRESH SESSION")
        print("="*80)
        print(f"📡 Polling interval: {POLL_INTERVAL_SEC} seconds")
        print(f"📊 Summary generation: After each fetch")
        print(f"💾 Data storage: {command_monitor.storage_path}")
        print(f"🔄 Each run starts fresh (not cumulative)")
        print("="*80)
        
        # Display last summary information if available
        command_monitor.display_last_summary_info()
        _startup_info_displayed = True
    
    logger.info("Starting polling cycle…")
    token = login(USERNAME, PASSWORD)
    if not token:
        logger.error("Skipping commands fetch because login failed.")
        return

    commands = fetch_all_commands(token)
    if commands is not None:
        # Store all commands for monitoring
        command_monitor.store_commands(commands)
        
        # For fresh session approach, treat all commands as "recent"
        filtered_commands = commands  # Don't filter by time for fresh sessions
        _is_first_run = False  # Mark first run as complete
        
        logger.info("✅ Successfully fetched command data")
        print("\n" + "📈" * 60)
        print("COMMAND DATA ANALYSIS")
        print("📈" * 60)
        
        if isinstance(filtered_commands, dict):
            if filtered_commands:
                print(f"📈 Commands found: {len(filtered_commands)}")
                
                # Show quick statistics
                entries = command_monitor.parse_commands_to_entries(filtered_commands)
                failed_count = len([e for e in entries if e.is_failed()])
                unverified_count = len([e for e in entries if e.is_unverified()])
                
                print(f"❌ Failed commands: {failed_count}")
                print(f"❓ Unverified commands: {unverified_count}")
                print(f"✅ Success rate: {((len(entries) - failed_count) / len(entries) * 100):.1f}%" if entries else "N/A")
                
                print("\n🔍 Command Details (sample):")
                # Show only first 5 commands for brevity
                sample_commands = list(filtered_commands.items())[:5]
                for cmd_key, cmd_info in sample_commands:
                    timestamp = cmd_info.get('timestamp', 'N/A') if isinstance(cmd_info, dict) else 'N/A'
                    status = cmd_info.get('status', 'N/A') if isinstance(cmd_info, dict) else 'N/A'
                    client = cmd_info.get('client', '—') if isinstance(cmd_info, dict) else '—'
                    
                    # Add status indicator
                    status_icon = "✅" if status == "ok" else "❌" if status else "❓"
                    
                    print(f"  {status_icon} {cmd_key}")
                    print(f"    ⏰ {timestamp}")
                    print(f"    📊 Status: {status}")
                    print(f"    👤 Client: {client}")
                    print()
                
                if len(filtered_commands) > 5:
                    print(f"  ... and {len(filtered_commands) - 5} more commands")
            else:
                print("📭 No commands found")
        else:
            print(f"📄 Raw response: {filtered_commands}")
        
        print("📈" * 60)
        print()
        
        # Generate summary immediately after each successful fetch
        logger.info("🎯 Generating summary after successful fetch...")
        generate_and_save_summary(commands)
        _last_summary_time = datetime.now()
        
    else:
        logger.error("Failed to retrieve commands data.")


def main():
    logger.info("Command poller with monitoring system started – polling every %s seconds", POLL_INTERVAL_SEC)
    print("\n" + "🎯" * 40)
    print("Command Monitoring & Reporting System")
    print("Fresh Session Mode - Summary After Each Fetch")
    print("🎯" * 40)
    print("💡 Tip: Press 's' during wait to fetch immediately")
    print("🎯" * 40)
    
    # Initialize last summary display
    last_displayed_summary = None
    
    while True:
        try:
            poll_once()
            
            # Show current final summary if available
            current_summary = command_monitor.get_last_summary()
            if current_summary and current_summary != last_displayed_summary:
                print("\n" + "📋" * 80)
                print("CURRENT FINAL SUMMARY (persistent display)")
                print("📋" * 80)
                print(current_summary)
                print("📋" * 80)
                last_displayed_summary = current_summary
            
            # Wait with countdown and skip option
            wait_completed = countdown_wait_with_skip(POLL_INTERVAL_SEC)
            if not wait_completed:
                print("🚀 Immediate fetch triggered by user!")
            
        except KeyboardInterrupt:
            print("\n\n" + "="*80)
            print("⏹️  SHUTDOWN SIGNAL RECEIVED")
            print("="*80)
            
            # Display final statistics
            command_monitor.display_last_summary_info()
            
            print("\n📊 Final Session Statistics:")
            print(f"  • Session summaries: {command_monitor.cumulative_stats['summaries_count']}")
            print(f"  • Session commands: {command_monitor.cumulative_stats['total_commands_processed']}")
            print(f"  • Session failures: {command_monitor.cumulative_stats['total_failed_commands']}")
            
            # Show final summary one more time
            final_summary = command_monitor.get_last_summary()
            if final_summary:
                print("\n" + "📋" * 80)
                print("FINAL SUMMARY AT SHUTDOWN")
                print("📋" * 80)
                print(final_summary)
                print("📋" * 80)
            
            print("\n👋 Command poller stopped gracefully.")
            print("="*80)
            break
        except Exception as exc:
            logger.error("Unexpected error in main loop: %s", exc)
            print("❌ Error occurred, waiting 10 seconds before retry...")
            countdown_wait_with_skip(10)  # Use countdown for error retry too


if __name__ == "__main__":
    main() 