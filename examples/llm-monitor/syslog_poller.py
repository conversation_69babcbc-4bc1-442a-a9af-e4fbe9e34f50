import time
import logging
import json
import os
import sys
import threading
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from pathlib import Path

# Platform-specific keyboard input handling
if os.name == 'nt':  # Windows
    import msvcrt
else:  # Unix/Linux/Mac
    import select
    import termios
    import tty

try:
    import requests
    from openai import OpenAI
except ImportError as e:
    missing = str(e).split("'")[1]
    print(f"❌ Missing dependency {missing}. Install with:")
    print("   pip install requests openai")
    sys.exit(1)

# ---------------------------------------------------------------------------
# Configuration – adjust these values for your environment
# ---------------------------------------------------------------------------
BASE_URL = "http://localhost:27182"  # API server base URL
USERNAME = "admin"                   # Login username
PASSWORD = "default"                 # Login password
POLL_INTERVAL_SEC = 300              # 5 minutes
DATA_STORAGE_PATH = ".syslog_data"   # Directory to store syslog data and summaries

# OpenAI Configuration
OPENAI_MODEL = "o4-mini"  # Using o4-mini as requested
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")  # Set via environment variable

# ---------------------------------------------------------------------------
# Data structures for syslog monitoring
# ---------------------------------------------------------------------------

@dataclass
class SyslogEntry:
    """Represents a single syslog entry with all its metadata."""
    timestamp: str
    facility: str
    severity: str
    hostname: str
    message: str
    source: str
    
    def is_anomaly(self) -> bool:
        """Check if syslog entry indicates an anomaly."""
        # Define severity levels that indicate anomalies
        anomaly_severities = ['emerg', 'alert', 'crit', 'err', 'warning']
        return self.severity.lower() in anomaly_severities

@dataclass
class SyslogSummaryData:
    """Container for syslog summary analysis data."""
    anomaly_logs: List[SyslogEntry]
    facility_stats: Dict[str, int]
    severity_stats: Dict[str, int]
    host_stats: Dict[str, int]
    special_attention_items: List[str]
    first_summary_time: Optional[str]
    current_summary_time: str
    total_logs: int
    cumulative_summaries_count: int = 0
    cumulative_anomalies: int = 0
    cumulative_total_logs: int = 0
    previous_summary_data: Optional[str] = None

class SyslogMonitor:
    """Syslog monitoring and reporting system."""
    
    def __init__(self, storage_path: str = DATA_STORAGE_PATH):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        self.syslog_history_file = self.storage_path / "syslog_history.json"
        self.summary_history_file = self.storage_path / "summary_history.json"
        self.last_summary_file = self.storage_path / "last_summary.md"
        # Reset to first time on each run
        self.first_summary_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.cumulative_stats = {
            "summaries_count": 0,
            "total_anomalies": 0,
            "total_logs_processed": 0,
            "last_summary_time": None
        }
        # Only load previous summary content, not cumulative stats
        self._load_previous_summary_only()
        
        # Initialize OpenAI client
        if OPENAI_API_KEY:
            self.openai_client = OpenAI(api_key=OPENAI_API_KEY)
        else:
            self.openai_client = None
            logger.warning("⚠️ OPENAI_API_KEY not set. Summary generation will be disabled.")
    
    def _load_previous_summary_only(self):
        """Load only the previous summary content, reset stats."""
        # Don't load cumulative stats - start fresh each time
        pass
    
    def _save_cumulative_stats(self):
        """Save cumulative statistics to persist across runs."""
        summary_data = {
            "first_summary_time": self.first_summary_time,
            "cumulative_stats": self.cumulative_stats
        }
        with open(self.summary_history_file, 'w') as f:
            json.dump(summary_data, f, indent=2)
    
    def get_last_summary(self) -> Optional[str]:
        """Get the content of the last generated summary."""
        if self.last_summary_file.exists():
            with open(self.last_summary_file, 'r', encoding='utf-8') as f:
                return f.read()
        return None
    
    def display_last_summary_info(self):
        """Display information about the last summary and cumulative statistics."""
        print("\n" + "="*80)
        print("📋 SESSION SUMMARY INFORMATION")
        print("="*80)
        
        print(f"📊 Current Session Statistics:")
        print(f"  • Session summaries generated: {self.cumulative_stats['summaries_count']}")
        print(f"  • Session logs processed: {self.cumulative_stats['total_logs_processed']}")
        print(f"  • Session anomalies detected: {self.cumulative_stats['total_anomalies']}")
        print(f"  • Session start time: {self.first_summary_time}")
        if self.cumulative_stats['last_summary_time']:
            print(f"  • Last summary generated: {self.cumulative_stats['last_summary_time']}")
        print()
        
        last_summary = self.get_last_summary()
        if last_summary:
            print("📄 Previous Summary (kept for context):")
            print("-" * 60)
            lines = last_summary.split('\n')
            for i, line in enumerate(lines[:8]):
                print(line)
            if len(lines) > 8:
                print(f"... ({len(lines) - 8} more lines)")
            print("-" * 60)
        else:
            print("📭 No previous summary found - starting fresh.")
        
        print("="*80)

    def store_syslogs(self, syslogs: List[Dict[str, Any]]):
        """Store syslog data with timestamp for analysis."""
        timestamp = datetime.now().isoformat()
        entry = {
            "timestamp": timestamp,
            "syslogs": syslogs
        }
        
        # Load existing history
        history = []
        if self.syslog_history_file.exists():
            with open(self.syslog_history_file, 'r') as f:
                history = json.load(f)
        
        # Add new entry
        history.append(entry)
        
        # Keep only last 1000 entries to prevent file from growing too large
        if len(history) > 1000:
            history = history[-1000:]
        
        # Save updated history
        with open(self.syslog_history_file, 'w') as f:
            json.dump(history, f, indent=2)
    
    def parse_syslogs_to_entries(self, syslogs: List[Dict[str, Any]]) -> List[SyslogEntry]:
        """Parse raw syslog data into SyslogEntry objects."""
        entries = []
        for log in syslogs:
            if isinstance(log, dict):
                entry = SyslogEntry(
                    timestamp=log.get('timestamp', 'N/A'),
                    facility=log.get('facility', 'N/A'),
                    severity=log.get('severity', 'N/A'),
                    hostname=log.get('hostname', 'N/A'),
                    message=log.get('message', 'N/A'),
                    source=log.get('source', 'N/A')
                )
                entries.append(entry)
        return entries
    
    def analyze_syslogs(self, syslogs: List[Dict[str, Any]]) -> SyslogSummaryData:
        """Analyze syslog data and generate summary statistics."""
        entries = self.parse_syslogs_to_entries(syslogs)
        
        # Analyze anomaly logs
        anomaly_logs = [entry for entry in entries if entry.is_anomaly()]
        
        # Analyze facility statistics
        facility_stats = {}
        for entry in entries:
            facility_stats[entry.facility] = facility_stats.get(entry.facility, 0) + 1
        
        # Analyze severity statistics
        severity_stats = {}
        for entry in entries:
            severity_stats[entry.severity] = severity_stats.get(entry.severity, 0) + 1
        
        # Analyze host statistics
        host_stats = {}
        for entry in entries:
            host_stats[entry.hostname] = host_stats.get(entry.hostname, 0) + 1
        
        # Generate special attention items
        special_attention_items = self._generate_special_attention_items(
            anomaly_logs, entries
        )
        
        # Update session statistics
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.cumulative_stats['summaries_count'] += 1
        self.cumulative_stats['total_anomalies'] += len(anomaly_logs)
        self.cumulative_stats['total_logs_processed'] += len(entries)
        self.cumulative_stats['last_summary_time'] = current_time
        
        # Get previous summary data
        previous_summary = self.get_last_summary()
        
        return SyslogSummaryData(
            anomaly_logs=anomaly_logs,
            facility_stats=facility_stats,
            severity_stats=severity_stats,
            host_stats=host_stats,
            special_attention_items=special_attention_items,
            first_summary_time=self.first_summary_time,
            current_summary_time=current_time,
            total_logs=len(entries),
            cumulative_summaries_count=self.cumulative_stats['summaries_count'],
            cumulative_anomalies=self.cumulative_stats['total_anomalies'],
            cumulative_total_logs=self.cumulative_stats['total_logs_processed'],
            previous_summary_data=previous_summary
        )
    
    def _generate_special_attention_items(self, anomaly_logs: List[SyslogEntry],
                                        all_logs: List[SyslogEntry]) -> List[str]:
        """Generate special attention items based on syslog analysis."""
        items = []
        
        # Group anomalies by severity
        severity_groups = {}
        for log in anomaly_logs:
            severity = log.severity.lower()
            if severity not in severity_groups:
                severity_groups[severity] = []
            severity_groups[severity].append(log)
        
        # Add items for each severity group
        for severity, logs in severity_groups.items():
            if len(logs) == 1:
                log = logs[0]
                items.append(f"**{severity.upper()} severity log** – From {log.hostname}: {log.message}")
            else:
                items.append(f"**Multiple {severity} severity logs** – {len(logs)} logs with {severity} severity. "
                           f"Review system health and configuration.")
        
        # Add item for high frequency hosts
        host_frequencies = {}
        for log in all_logs:
            host_frequencies[log.hostname] = host_frequencies.get(log.hostname, 0) + 1
        
        high_frequency_threshold = len(all_logs) * 0.3  # 30% of total logs
        for host, count in host_frequencies.items():
            if count > high_frequency_threshold:
                items.append(f"**High log frequency** – Host {host} generated {count} logs "
                           f"({(count/len(all_logs)*100):.1f}% of total). Investigate logging configuration.")
        
        return items
    
    def prepare_llm_prompt(self, syslogs: List[Dict[str, Any]], summary_data: SyslogSummaryData) -> str:
        """Prepare the prompt for LLM to generate summary."""
        
        # Format syslog data for LLM
        syslogs_json = json.dumps(syslogs, indent=2, ensure_ascii=False)
        
        prompt = f"""Please generate a comprehensive syslog activity summary based on the following data:

**FETCHING DATA:**
```json
{syslogs_json}
```

**SUMMARY STATISTICS:**
- This is summary #{summary_data.cumulative_summaries_count} in current session
- Current period logs: {summary_data.total_logs}
- Current period anomalies: {len(summary_data.anomaly_logs)}
- Session total logs: {summary_data.cumulative_total_logs}
- Session anomalies: {summary_data.cumulative_anomalies}
- Session start time: {summary_data.first_summary_time}
- This summary generated: {summary_data.current_summary_time}
"""

        # Add previous summary if available
        if summary_data.previous_summary_data:
            prompt += f"""

**PREVIOUS SUMMARY (for context only):**
```
{summary_data.previous_summary_data}
```

Please generate a new summary focusing on the current period data. Use the previous summary only for context and comparison, not for cumulative reporting.
"""
        else:
            # First summary - provide template
            prompt += f"""

**TEMPLATE FOR SUMMARY:**
```
{INITIAL_SUMMARY_TEMPLATE}
```

This is the first summary in this session. Please use the above template structure and fill in the real data from the fetching data provided.
"""

        prompt += """

Generate a comprehensive summary following the system prompt guidelines. Ensure the summary is clear, actionable, and focuses on the current session data."""

        return prompt
    
    def display_generation_details(self, syslogs: List[Dict[str, Any]], summary_data: SyslogSummaryData):
        """Display detailed information about summary generation process."""
        print("\n" + "🤖" * 80)
        print("GENERATING SUMMARY WITH LLM")
        print("🤖" * 80)
        
        # Show system prompt info
        print("📋 SYSTEM PROMPT:")
        system_prompt_preview = SYSTEM_PROMPT[:100] + "..." if len(SYSTEM_PROMPT) > 100 else SYSTEM_PROMPT
        print(f"  Length: {len(SYSTEM_PROMPT)} characters")
        print(f"  Preview: {system_prompt_preview}")
        print()
        
        # Show fetching data info
        syslogs_json = json.dumps(syslogs, indent=2, ensure_ascii=False)
        print("📊 FETCHING DATA:")
        print(f"  Total logs: {len(syslogs)}")
        print(f"  JSON length: {len(syslogs_json)} characters")
        data_preview = syslogs_json[:100] + "..." if len(syslogs_json) > 100 else syslogs_json
        print(f"  Preview: {data_preview}")
        print()
        
        # Show previous summary info
        if summary_data.previous_summary_data:
            print("📄 PREVIOUS SUMMARY (for context):")
            print(f"  Length: {len(summary_data.previous_summary_data)} characters")
            prev_preview = summary_data.previous_summary_data[:100] + "..." if len(summary_data.previous_summary_data) > 100 else summary_data.previous_summary_data
            print(f"  Head 100 chars: {prev_preview}")
        else:
            print("📄 PREVIOUS SUMMARY: None (first summary)")
        
        print("\n🎯 LLM Generation Parameters:")
        print(f"  Model: {OPENAI_MODEL}")
        print(f"  Session summary #: {summary_data.cumulative_summaries_count}")
        
        print("\n⏳ Calling OpenAI API...")
        print("🤖" * 80)
    
    def generate_summary_with_llm(self, syslogs: List[Dict[str, Any]], summary_data: SyslogSummaryData) -> Optional[str]:
        """Generate summary using OpenAI LLM."""
        if not self.openai_client:
            logger.error("❌ OpenAI client not initialized. Cannot generate summary.")
            return None
        
        try:
            # Display generation details
            self.display_generation_details(syslogs, summary_data)
            
            prompt = self.prepare_llm_prompt(syslogs, summary_data)
            
            logger.info("🤖 Generating summary with OpenAI...")
            
            response = self.openai_client.chat.completions.create(
                model=OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": SYSTEM_PROMPT},
                    {"role": "user", "content": prompt}
                ]
            )
            
            summary = response.choices[0].message.content
            logger.info("✅ Summary generated successfully")
            
            print("\n" + "✅" * 80)
            print("SUMMARY GENERATION COMPLETED")
            print("✅" * 80)
            
            return summary
            
        except Exception as exc:
            logger.error("❌ Failed to generate summary with LLM: %s", exc)
            return None
    
    def save_summary_report(self, report: str):
        """Save the summary report to file."""
        if report is None:
            logger.error("❌ Cannot save None report")
            return None
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"syslog_summary_{timestamp}.md"
        filepath = self.storage_path / filename
        
        # Save timestamped version
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # Save as last summary
        with open(self.last_summary_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # Save cumulative statistics
        self._save_cumulative_stats()
        
        logger.info(f"📊 Summary report saved to: {filepath}")
        return filepath

# ---------------------------------------------------------------------------
# System prompt for AI analysis
# ---------------------------------------------------------------------------

SYSTEM_PROMPT = """# Syslog Analysis System Prompt

You are a syslog monitoring and reporting system. Your task is to analyze syslog data and generate comprehensive English summaries following these specific requirements:

## Summary Structure Requirements

### 1. Summary Header Information
- **Initial Summary Start Time**: Include the timestamp of the very first summary
- **Current Summary Timestamp**: Always include the timestamp when this current summary is generated
- **Reporting Period**: Specify the time range covered by this summary

### 2. Anomaly Analysis
- **Categorize Anomalous Logs**: Group and list all logs with severity levels indicating anomalies
  - Group by severity level (emerg, alert, crit, err, warning)
  - Include log details, timestamps, and hostnames
  - Provide count of anomalies per severity level
  - Highlight recurring patterns or related anomalies
  - Focus on security-related and system health issues

### 3. Facility Analysis
- **Per-Facility Statistics**: Generate statistics for each logging facility
  - Total logs per facility
  - Anomaly rate per facility
  - Critical facilities requiring attention
  - Facility-specific trends and patterns

### 4. Host Analysis
- **Per-Host Statistics**: Track and analyze logs by host
  - Log volume per host
  - Anomaly distribution across hosts
  - Hosts with unusual logging patterns
  - Hosts requiring immediate attention

### 5. Special Attention Items
- **Critical Alerts and Notices**: Highlight items requiring immediate attention
  - Security-related anomalies
  - System health warnings
  - Performance degradation indicators
  - Resource threshold breaches
  - Compliance or operational risks

## Output Format Guidelines

- Use clear, professional English
- Organize information hierarchically with proper headings
- Include quantitative data with percentages and totals
- Use bullet points for lists and detailed breakdowns
- Highlight critical information with appropriate formatting
- Provide actionable recommendations where applicable
- Maintain consistent terminology and formatting across all summaries

## Data Processing Instructions

1. Parse all syslog data systematically
2. Validate timestamp formats and ensure chronological accuracy
3. Cross-reference severity levels with facility information
4. Calculate metrics and trends accurately
5. Identify anomalies and patterns in the data
6. Preserve historical context from previous summaries
7. Flag any data inconsistencies or gaps

## Reporting Priorities

1. **Critical Issues**: System failures, security breaches, or operational disruptions
2. **Performance Metrics**: System health trends and resource utilization
3. **Host Management**: Individual host performance and logging patterns
4. **Operational Health**: Overall system status and maintenance requirements
5. **Trend Analysis**: Long-term patterns and predictive insights

Generate each summary as a comprehensive, standalone document that provides both immediate operational insights and strategic overview for stakeholders."""

# ---------------------------------------------------------------------------
# Summary Template for first-time generation
# ---------------------------------------------------------------------------

INITIAL_SUMMARY_TEMPLATE = """### Syslog Activity Summary

**Log start time (first summary):** {log_start_time}
**This summary generated:** {summary_time}

---

#### 1. Anomalous Logs (severity: emerg, alert, crit, err, warning)

| Timestamp     | Severity | Hostname | Facility | Message |
| ------------- | -------- | -------- | -------- | ------- |
| `{timestamp}` | `{severity}` | `{hostname}` | `{facility}` | `{message}` |
| *(add one row per anomalous log)* | | | | |

---

#### 2. Log Statistics by Facility

| Facility | Total Logs | Anomalies | Anomaly Rate |
| -------- | ---------- | --------- | ------------ |
| `{facility}` | `{total}` | `{anomalies}` | `{rate}%` |
| *(add one row per facility)* | | | |

---

#### 3. Log Statistics by Host

| Hostname | Total Logs | Anomalies | Anomaly Rate |
| -------- | ---------- | --------- | ------------ |
| `{hostname}` | `{total}` | `{anomalies}` | `{rate}%` |
| *(add one row per host)* | | | |

---

#### 4. Items Requiring Special Attention

1. **{issue_title_1}** – {brief_description_and_recommendation_1}
2. **{issue_title_2}** – {brief_description_and_recommendation_2}
3. *(add additional bullet points as needed)*

---

> *Note: Use the placeholders above to insert real data for each new summary. Each future summary should append new findings while preserving previous entries to maintain a complete history.*
"""

# ---------------------------------------------------------------------------
# Logging setup
# ---------------------------------------------------------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# Initialize syslog monitor
syslog_monitor = SyslogMonitor()

# ---------------------------------------------------------------------------
# Keyboard input handling (Windows optimized)
# ---------------------------------------------------------------------------

class KeyboardListener:
    """Windows-optimized keyboard input listener."""
    
    def __init__(self):
        self.interrupted = False
        self.skip_wait = False
    
    def is_key_pressed(self) -> Optional[str]:
        """Check if a key is pressed and return it (Windows optimized)."""
        if os.name == 'nt':  # Windows
            if msvcrt.kbhit():
                try:
                    key = msvcrt.getch()
                    # Handle both bytes and string
                    if isinstance(key, bytes):
                        key = key.decode('utf-8', errors='ignore')
                    return key.lower()
                except:
                    return None
        else:  # Unix/Linux/Mac (fallback)
            if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
                key = sys.stdin.read(1).lower()
                return key
        return None
    
    def clear_input_buffer(self):
        """Clear any pending keyboard input (Windows)."""
        if os.name == 'nt':
            while msvcrt.kbhit():
                msvcrt.getch()
    
    def wait_with_countdown(self, seconds: int) -> bool:
        """Wait with countdown display and keyboard monitoring.
        Returns True if wait completed normally, False if interrupted by 's' key."""
        
        # Clear any pending input first
        self.clear_input_buffer()
        
        print(f"\n⏰ Next fetch in {seconds} seconds...")
        print(f"🔥 Press 's' key anytime to fetch immediately!")
        print("=" * 60)
        
        for remaining in range(seconds, 0, -1):
            # Create a more visual countdown display
            progress_bar = "█" * (30 - int(30 * remaining / seconds)) + "░" * int(30 * remaining / seconds)
            
            # Display countdown with progress bar
            print(f"\r⏰ [{progress_bar}] {remaining:3d}s | Press 's' to skip", end='', flush=True)
            
            # Check for key press multiple times per second for better responsiveness
            for _ in range(20):  # Check 20 times per second
                key = self.is_key_pressed()
                if key == 's':
                    print(f"\n\n🚀 Key 's' pressed! Fetching immediately...")
                    print("=" * 60)
                    return False  # Interrupted
                elif key and key != 's':
                    # Clear any other keys pressed
                    continue
                
                time.sleep(0.05)  # Sleep 50ms (20 checks per second)
        
        print(f"\n\n✅ Countdown complete! Starting next fetch...")
        print("=" * 60)
        return True  # Completed normally

def countdown_wait_with_skip(seconds: int) -> bool:
    """Wait with countdown and ability to skip by pressing 's'.
    Returns True if wait completed, False if skipped."""
    
    listener = KeyboardListener()
    return listener.wait_with_countdown(seconds)

# ---------------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------------

def login(username: str, password: str) -> Optional[str]:
    """Authenticate and return JWT token. Returns None on failure."""
    try:
        url = f"{BASE_URL}/api/v1/login"
        resp = requests.post(url, json={"user": username, "password": password}, timeout=10)
        resp.raise_for_status()
        data: Dict[str, Any] = resp.json()
        token = data.get("token")
        if token:
            logger.debug("Obtained token: %s", token)
            return token
        logger.error("Login succeeded but no token found in response: %s", data)
    except Exception as exc:
        logger.error("Login request failed: %s", exc)
    return None

def fetch_all_syslogs(token: str) -> Optional[List[Dict[str, Any]]]:
    """Retrieve all syslogs using the provided JWT token."""
    try:
        print("\n" + "🔄" * 60)
        print("FETCHING SYSLOGS FROM API")
        print("🔄" * 60)
        print("⏳ Fetching data from server...")
        
        url = f"{BASE_URL}/api/v1/syslogs"
        headers = {"Authorization": f"Bearer {token}"}
        resp = requests.get(url, headers=headers, timeout=10)
        resp.raise_for_status()
        data = resp.json()
        
        # Display fetching results
        data_json = json.dumps(data, indent=2, ensure_ascii=False)
        print(f"✅ Data fetched successfully!")
        print(f"📊 Total items: {len(data) if isinstance(data, list) else 'N/A'}")
        print(f"📄 JSON length: {len(data_json)} characters")
        print(f"📋 JSON preview (first 100 chars):")
        preview = data_json[:100] + "..." if len(data_json) > 100 else data_json
        print(f"   {preview}")
        print("🔄" * 60)
        
        return data
    except Exception as exc:
        logger.error("Fetching syslogs failed: %s", exc)
        print(f"❌ Fetching failed: {exc}")
        print("🔄" * 60)
    return None

def filter_recent_syslogs(syslogs: List[Dict[str, Any]], is_first_run: bool = False) -> List[Dict[str, Any]]:
    """Filter syslogs to only include those newer than POLL_INTERVAL_SEC.
    Skip filtering on first run to show all existing data."""
    if is_first_run:
        logger.info("First run - showing all existing syslogs")
        return syslogs
    
    cutoff_time = datetime.now() - timedelta(seconds=POLL_INTERVAL_SEC)
    filtered_syslogs = []
    
    for log in syslogs:
        if isinstance(log, dict) and "timestamp" in log:
            try:
                # Parse ISO timestamp
                log_timestamp = datetime.fromisoformat(log["timestamp"].replace('+08:00', ''))
                if log_timestamp >= cutoff_time:
                    filtered_syslogs.append(log)
            except Exception as exc:
                logger.warning("Failed to parse timestamp for log: %s", exc)
                # Include logs with unparseable timestamps to be safe
                filtered_syslogs.append(log)
        else:
            # Include logs without timestamp field
            filtered_syslogs.append(log)
    
    logger.info("Filtered %d recent syslogs from %d total", len(filtered_syslogs), len(syslogs))
    return filtered_syslogs

def generate_and_save_summary(syslogs: List[Dict[str, Any]]):
    """Generate and save a summary report for the given syslogs."""
    try:
        # Display previous summary info before generating new one
        print("\n" + "="*80)
        print("🔄 PREPARING TO GENERATE NEW SUMMARY...")
        print("="*80)
        syslog_monitor.display_last_summary_info()
        
        summary_data = syslog_monitor.analyze_syslogs(syslogs)
        report = syslog_monitor.generate_summary_with_llm(syslogs, summary_data)
        
        if report is None:
            logger.error("❌ Failed to generate summary with LLM")
            return None
            
        filepath = syslog_monitor.save_summary_report(report)
        
        # Display final summary and keep it on screen
        display_final_summary(report)
        
        return filepath
    except Exception as exc:
        logger.error("Failed to generate summary report: %s", exc)
        return None

def display_final_summary(summary: str):
    """Display the final summary prominently and keep it on screen."""
    print("\n" + "🎉" * 80)
    print("FINAL SUMMARY GENERATED - KEEPING ON SCREEN")
    print("🎉" * 80)
    print()
    print(summary)
    print()
    print("🎉" * 80)
    print("SUMMARY COMPLETE - This summary will remain visible")
    print("🎉" * 80)
    print(f"\n💡 Next summary after next fetch (countdown will start shortly)")
    print(f"🔥 Press 's' during countdown to fetch immediately!")

# ---------------------------------------------------------------------------
# Main poll loop
# ---------------------------------------------------------------------------

# Global variables to track state
_is_first_run = True
_last_summary_time = datetime.now()
_startup_info_displayed = False

def poll_once():
    """One polling cycle: login then fetch syslogs."""
    global _is_first_run, _last_summary_time, _startup_info_displayed
    
    # Display startup information on first run
    if not _startup_info_displayed:
        print("\n" + "="*80)
        print("🚀 SYSLOG POLLER STARTED - FRESH SESSION")
        print("="*80)
        print(f"📡 Polling interval: {POLL_INTERVAL_SEC} seconds")
        print(f"📊 Summary generation: After each fetch")
        print(f"💾 Data storage: {syslog_monitor.storage_path}")
        print(f"🔄 Each run starts fresh (not cumulative)")
        print("="*80)
        
        # Display last summary information if available
        syslog_monitor.display_last_summary_info()
        _startup_info_displayed = True
    
    logger.info("Starting polling cycle…")
    token = login(USERNAME, PASSWORD)
    if not token:
        logger.error("Skipping syslogs fetch because login failed.")
        return

    syslogs = fetch_all_syslogs(token)
    if syslogs is not None:
        # Store all syslogs for monitoring
        syslog_monitor.store_syslogs(syslogs)
        
        # Filter recent syslogs
        filtered_syslogs = filter_recent_syslogs(syslogs, _is_first_run)
        _is_first_run = False  # Mark first run as complete
        
        logger.info("✅ Successfully fetched syslog data")
        print("\n" + "📈" * 60)
        print("SYSLOG DATA ANALYSIS")
        print("📈" * 60)
        
        if isinstance(filtered_syslogs, list):
            if filtered_syslogs:
                print(f"📈 Logs found: {len(filtered_syslogs)}")
                
                # Show quick statistics
                entries = syslog_monitor.parse_syslogs_to_entries(filtered_syslogs)
                anomaly_count = len([e for e in entries if e.is_anomaly()])
                
                print(f"⚠️ Anomaly logs: {anomaly_count}")
                print(f"✅ Normal logs: {len(entries) - anomaly_count}")
                print(f"📊 Anomaly rate: {(anomaly_count / len(entries) * 100):.1f}%" if entries else "N/A")
                
                print("\n🔍 Log Details (sample):")
                # Show only first 5 logs for brevity
                sample_logs = filtered_syslogs[:5]
                for log in sample_logs:
                    timestamp = log.get('timestamp', 'N/A')
                    severity = log.get('severity', 'N/A')
                    hostname = log.get('hostname', 'N/A')
                    facility = log.get('facility', 'N/A')
                    message = log.get('message', 'N/A')
                    
                    # Add severity indicator
                    severity_icon = "⚠️" if severity.lower() in ['emerg', 'alert', 'crit', 'err', 'warning'] else "✅"
                    
                    print(f"  {severity_icon} {message}")
                    print(f"    ⏰ {timestamp}")
                    print(f"    📊 Severity: {severity}")
                    print(f"    🏢 Facility: {facility}")
                    print(f"    👤 Host: {hostname}")
                    print()
                
                if len(filtered_syslogs) > 5:
                    print(f"  ... and {len(filtered_syslogs) - 5} more logs")
            else:
                print("📭 No logs found")
        else:
            print(f"📄 Raw response: {filtered_syslogs}")
        
        print("📈" * 60)
        print()
        
        # Generate summary immediately after each successful fetch
        logger.info("🎯 Generating summary after successful fetch...")
        generate_and_save_summary(filtered_syslogs)
        _last_summary_time = datetime.now()
        
    else:
        logger.error("Failed to retrieve syslogs data.")


def main():
    logger.info("Syslog poller with monitoring system started – polling every %s seconds", POLL_INTERVAL_SEC)
    print("\n" + "🎯" * 40)
    print("Syslog Monitoring & Reporting System")
    print("Fresh Session Mode - Summary After Each Fetch")
    print("🎯" * 40)
    print("💡 Tip: Press 's' during wait to fetch immediately")
    print("🎯" * 40)
    
    # Initialize last summary display
    last_displayed_summary = None
    
    while True:
        try:
            poll_once()
            
            # Show current final summary if available
            current_summary = syslog_monitor.get_last_summary()
            if current_summary and current_summary != last_displayed_summary:
                print("\n" + "📋" * 80)
                print("CURRENT FINAL SUMMARY (persistent display)")
                print("📋" * 80)
                print(current_summary)
                print("📋" * 80)
                last_displayed_summary = current_summary
            
            # Wait with countdown and skip option
            wait_completed = countdown_wait_with_skip(POLL_INTERVAL_SEC)
            if not wait_completed:
                print("🚀 Immediate fetch triggered by user!")
            
        except KeyboardInterrupt:
            print("\n\n" + "="*80)
            print("⏹️  SHUTDOWN SIGNAL RECEIVED")
            print("="*80)
            
            # Display final statistics
            syslog_monitor.display_last_summary_info()
            
            print("\n📊 Final Session Statistics:")
            print(f"  • Session summaries: {syslog_monitor.cumulative_stats['summaries_count']}")
            print(f"  • Session logs: {syslog_monitor.cumulative_stats['total_logs_processed']}")
            print(f"  • Session anomalies: {syslog_monitor.cumulative_stats['total_anomalies']}")
            
            # Show final summary one more time
            final_summary = syslog_monitor.get_last_summary()
            if final_summary:
                print("\n" + "📋" * 80)
                print("FINAL SUMMARY AT SHUTDOWN")
                print("📋" * 80)
                print(final_summary)
                print("📋" * 80)
            
            print("\n👋 Syslog poller stopped gracefully.")
            print("="*80)
            break
        except Exception as exc:
            logger.error("Unexpected error in main loop: %s", exc)
            print("❌ Error occurred, waiting 10 seconds before retry...")
            countdown_wait_with_skip(10)  # Use countdown for error retry too


if __name__ == "__main__":
    main() 