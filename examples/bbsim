#!/usr/bin/env bash
# bbsim-lite - tiny network namespace helper for NIMBL testing
# Goals: minimal, predictable, no iptables, default background runs
# Commands:
#   up                         ensure bridge exists (bb-bridge @ *********/16)
#   ns <name>                  create namespace (bb-ns-<name>) and attach to bridge
#   run <name> <cmd...>        run command in namespace (default: background)
#   run --fg <name> <cmd...>   run in foreground
#   term <name>                interactive bash in namespace
#   list                       list managed namespaces
#   kill <name>                stop processes in ns and delete it
#   kill-all                   remove all managed namespaces
#   clean                      kill-all + delete bridge

set -Eeuo pipefail
BRIDGE_NAME="bb-bridge"
BRIDGE_CIDR="*********/16"
BRIDGE_IP=${BRIDGE_CIDR%/*}
NS_PREFIX="bb-ns-"

msg(){ printf "[bbsim-lite] %s\n" "$*"; }
need_root(){ [[ $EUID -eq 0 ]] || { msg "Run as root (sudo)"; exit 1; }; }

mac_from_host(){
  local h; h=$(printf "%s" "$(hostname)-${BRIDGE_NAME}" | md5sum | head -c 12)
  printf "02:%s:%s:%s:%s:%s\n" "${h:0:2}" "${h:2:2}" "${h:4:2}" "${h:6:2}" "${h:8:2}"
}

ensure_bridge(){
  if ! ip link show "${BRIDGE_NAME}" >/dev/null 2>&1; then
    msg "create bridge ${BRIDGE_NAME} (${BRIDGE_IP})"
    ip link add "${BRIDGE_NAME}" type bridge
    ip link set "${BRIDGE_NAME}" down
    ip link set "${BRIDGE_NAME}" address "$(mac_from_host)"
    ip addr add "${BRIDGE_CIDR}" dev "${BRIDGE_NAME}"
    ip link set "${BRIDGE_NAME}" up
  else
    ip addr show "${BRIDGE_NAME}" | grep -q "${BRIDGE_CIDR}" || ip addr add "${BRIDGE_CIDR}" dev "${BRIDGE_NAME}"
    local mac; mac=$(ip -o link show "${BRIDGE_NAME}" | awk '/link\/ether/{print $17}')
    if [[ -z "$mac" || "$mac" == "00:00:00:00:00:00" ]]; then
      ip link set "${BRIDGE_NAME}" down
      ip link set "${BRIDGE_NAME}" address "$(mac_from_host)"
      ip link set "${BRIDGE_NAME}" up
    fi
  fi
}

ns_name(){ printf "%s%s\n" "$NS_PREFIX" "$1"; }
ns_exists(){ ip netns list | grep -q "^$(ns_name "$1")\b"; }

ns_ip(){
  local n=$1; local hash dec o3 o4
  hash=$(echo -n "$n" | md5sum | head -c 8)
  dec=$(echo "ibase=16; ${hash^^}" | bc)
  o3=$(( (dec % 200) + 1 ))
  o4=$(( (dec / 200 % 253) + 2 ))
  printf "10.20.%d.%d/16\n" "$o3" "$o4"
}

ns_create(){
  local short=$1 ns veth peer cidr
  ns=$(ns_name "$short"); veth="veth-$short"; peer="vpeer-$short"; cidr=$(ns_ip "$short")
  ensure_bridge
  ip link del "$veth" 2>/dev/null || true
  ip netns add "$ns" 2>/dev/null || true
  ip link add "$veth" type veth peer name "$peer"
  ip link set "$veth" master "${BRIDGE_NAME}"; ip link set "$veth" up
  ip link set "$peer" netns "$ns"
  ip netns exec "$ns" ip link set lo up
  ip netns exec "$ns" ip addr add "$cidr" dev "$peer"
  ip netns exec "$ns" ip link set "$peer" up
  ip netns exec "$ns" ip route replace default via "${BRIDGE_IP}"
  msg "ns $ns ready (IP $cidr)"
}

ns_term(){ local ns=$(ns_name "$1"); ns_exists "$1" || ns_create "$1"; ip netns exec "$ns" bash -i; }

ns_run_bg(){
  local short=$1; shift
  local ns=$(ns_name "$short")
  ns_exists "$short" || ns_create "$short"
  # Start new session so it survives this shell
  ip netns exec "$ns" setsid bash -lc "$*" &
  local pid=$!
  msg "started [$short] pid=$pid : $*"
}

ns_run_fg(){ local short=$1; shift; local ns=$(ns_name "$short"); ns_exists "$short" || ns_create "$short"; ip netns exec "$ns" bash -lc "$*"; }

ns_list(){
  msg "namespaces:"; local any=false
  while read -r ns _; do
    [[ -z "$ns" ]] && continue
    if [[ $ns == ${NS_PREFIX}* ]]; then
      any=true; local short=${ns#${NS_PREFIX}}; local cidr=$(ns_ip "$short"); local p=$(ip netns pids "$ns" | wc -l)
      printf "  %-20s %-18s (%s procs)\n" "$ns" "$cidr" "$p"
    fi
  done < <(ip netns list)
  $any || msg "(none)"
}

ns_kill(){
  local ns=$(ns_name "$1"); local veth="veth-$1"
  if ns_exists "$1"; then
    msg "kill $ns"
    local pids; pids=$(ip netns pids "$ns" 2>/dev/null || true)
    [[ -n "$pids" ]] && { echo "$pids" | xargs -r kill -TERM 2>/dev/null || true; sleep 1; echo "$pids" | xargs -r kill -KILL 2>/dev/null || true; }
    ip link del "$veth" 2>/dev/null || true
    ip netns del "$ns" 2>/dev/null || true
  fi
}

ns_kill_all(){ while read -r ns _; do [[ $ns == ${NS_PREFIX}* ]] && ns_kill "${ns#${NS_PREFIX}}"; done < <(ip netns list); }

bridge_del(){ ip link show "${BRIDGE_NAME}" >/dev/null 2>&1 && { msg "delete bridge ${BRIDGE_NAME}"; ip link del "${BRIDGE_NAME}"; }; }

usage(){ cat <<USG
Usage:
  $0 up
  $0 ns <name>
  $0 run <name> <cmd...>        # default background
  $0 run --fg <name> <cmd...>   # foreground
  $0 term <name>
  $0 list
  $0 kill <name>
  $0 kill-all
  $0 clean
USG
}

main(){
  need_root
  local cmd=${1:-}; shift || true
  case "${cmd}" in
    up) ensure_bridge ;;
    ns) [[ $# -ge 1 ]] || { usage; exit 1; }; ns_create "$1" ;;
    term) [[ $# -ge 1 ]] || { usage; exit 1; }; ns_term "$1" ;;
    run)
      [[ $# -ge 2 ]] || { usage; exit 1; }
      if [[ "$1" == "--fg" ]]; then shift; local name=$1; shift; ns_run_fg "$name" "$*"; else local name=$1; shift; ns_run_bg "$name" "$*"; fi ;;
    list) ns_list ;;
    kill) [[ $# -ge 1 ]] || { usage; exit 1; }; ns_kill "$1" ;;
    kill-all) ns_kill_all ;;
    clean) ns_kill_all; bridge_del ;;
    *) usage; exit 1 ;;
  esac
}

main "$@"
