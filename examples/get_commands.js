const http = require('http');

const get_commands = function(token){
	const options = {
		hostname: 'localhost',
		port: 27182,
		path: '/api/v1/commands?cmd=all',
		method: 'GET',
		headers: {
			'Authorization': 'Bearer '+token
		},
	};
	const req=http.request(options,(res)=>{
		let data = '';
		res.on('data',(chunk)=>{
			data += chunk;
		});
		res.on('end', ()=>{
			let obj= JSON.parse(data);
			Object.entries(obj).forEach((cmd)=> {
				console.log(cmd);
			});
		});
	}).on('error',(err)=>{
		console.log("error:" + err.message);
	});
	req.end();
};

const post_data = JSON.stringify({
	'user': 'admin',
	'password':'default'
});


const options = {
	hostname: 'localhost',
	port: 27182,
	path: '/api/v1/login',
	method: 'POST',
	headers: {
		'Content-Type': 'application/json',
		'Content-Length': Buffer.byteLength(post_data),
	},
};

const req= http.request(options, (res) =>{
	let data = '';
	res.on('data',(chunk)=>{
		data += chunk;
	});
	res.on('end',()=>{
		let obj = JSON.parse(data);
		get_commands(obj.token);
	});
}).on('error',(e)=>{
	console.error(`problem with request: ${e.message}`);
});
req.write(post_data);
req.end();

