import os
import shutil
import stat
import subprocess
from datetime import datetime
import openai
from dotenv import load_dotenv
import json
import requests

# --- Load Environment Variables ---
load_dotenv()

# --- Configuration ---
GIT_REPO_URL = "**************:bbtechhive/userguide.git"
REPO_LOCAL_DIR = "./userguide_repo"
MANUAL_DIRECTORY_IN_REPO = "user_guide_chapters"
TARGET_MANUAL_FILE = "08-bbrootsvc-HTTP-endpoint-API-Documentation.md"
REPORT_FILENAME_TEMPLATE = "command_validation_report_{timestamp}.md"
SYSLOG_REPORT_LIMIT = 10  # Max number of syslog entries to show in the report

# --- API Server Configuration ---
BASE_URL = "http://localhost:27182"
LOGIN_CREDENTIALS = {"user": "admin", "password": "default"}

# --- OpenAI Configuration ---
try:
    openai.api_key = os.getenv("OPENAI_API_KEY")
    if not openai.api_key:
        raise ValueError("OPENAI_API_KEY not found in environment or .env file.")
    client = openai.OpenAI()
    OPENAI_MODEL = "gpt-4o"
except (ValueError, ImportError) as e:
    print(f"ERROR: OpenAI configuration failed. {e}")
    client = None

# --- Utility Functions ---

def handle_remove_readonly(func, path, exc):
    """Error handler for shutil.rmtree to handle read-only files."""
    excvalue = exc[1]
    if func in (os.rmdir, os.remove, os.unlink) and excvalue.errno == 13:
        os.chmod(path, stat.S_IWUSR)
        func(path)
    else:
        raise exc

def setup_git_repository(repo_url, local_dir):
    """Clones a Git repository, ensuring any previous local copy is removed."""
    print(f"Ensuring fresh copy of Git repository from {repo_url}...")
    if os.path.exists(local_dir):
        try:
            shutil.rmtree(local_dir, onerror=handle_remove_readonly)
            print(f"Successfully removed old directory: {local_dir}")
        except OSError as e:
            print(f"ERROR: Failed to remove directory '{local_dir}'. Error: {e}")
            return False
    try:
        subprocess.run(['git', 'clone', repo_url, local_dir], check=True, capture_output=True, text=True, timeout=120)
        print("Successfully cloned repository.")
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Failed to clone Git repository. Git Error: {e.stderr}")
        return False
    except subprocess.TimeoutExpired:
        print(f"ERROR: Git clone timed out after 120 seconds.")
        return False
    return True

def get_auth_token(base_url, credentials):
    """Authenticates with the API and returns the auth token."""
    print("\nAttempting to authenticate to get API token...")
    login_url = f"{base_url}/api/v1/login"
    try:
        response = requests.post(login_url, json=credentials, timeout=10)
        response.raise_for_status()
        data = response.json()
        token = data.get("token")
        if token:
            print("Successfully authenticated. Token received.")
            return token
        else:
            print(f"ERROR: Authentication successful, but no token found in response: {data}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"ERROR: Failed to connect to API for authentication at {login_url}. Is the server running?")
        print(f"Details: {e}")
        return None

def parse_api_documentation_with_openai(markdown_content):
    """Uses OpenAI to parse markdown and extract API call details."""
    if not client:
        print("ERROR: OpenAI client not initialized. Cannot parse documentation.")
        return None

    print("\nParsing API documentation with OpenAI...")
    system_prompt = """
You are an expert API documentation parser. Your task is to analyze the provided Markdown content and extract all HTTP API call examples.
You must return the result as a single, valid JSON object.
The JSON object should have a single key "api_calls" which contains a list of objects.
Each object in the list represents one API call and must have the following keys:
- "description": A brief, one-sentence description of what the API endpoint does.
- "method": The HTTP method (e.g., "GET", "POST").
- "path": The API path (e.g., "/api/v1/info").
- "body": The JSON request body as a string if it exists, otherwise `null`.
- "expected_output": The expected JSON response body or plain text as a string.

Example of a single object in the list:
{
  "description": "Retrieves system information.",
  "method": "GET",
  "path": "/api/v1/info",
  "body": null,
  "expected_output": "{\\"version\\": \\"1.0.0\\", \\"status\\": \\"running\\"}"
}
"""
    try:
        response = client.chat.completions.create(
            model=OPENAI_MODEL,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": markdown_content}
            ],
            response_format={"type": "json_object"},
            temperature=0.1,
        )
        parsed_json = json.loads(response.choices[0].message.content)
        api_calls = parsed_json.get("api_calls")
        if not api_calls or not isinstance(api_calls, list):
            print("ERROR: OpenAI response did not contain a valid 'api_calls' list.")
            return None
        print(f"Successfully parsed {len(api_calls)} API calls from the documentation.")
        return api_calls
    except Exception as e:
        print(f"ERROR: An error occurred while calling OpenAI API: {e}")
        return None

def is_dynamic_key_dict(d):
    """
    Heuristically checks if a dictionary uses dynamic keys (e.g., MACs, IDs).
    It assumes so if it's not empty and all its values are dictionaries.
    """
    if not isinstance(d, dict) or not d:
        return False
    return all(isinstance(v, dict) for v in d.values())

def compare_json_schemas(actual, expected, ignore_values=False, strict_keys=True):
    """
    Recursively compares two JSON structures with enhanced flexibility.
    - ignore_values: If True, only checks keys and types, not values.
    - strict_keys: If False, ignores keys present in 'expected' but missing in 'actual'.
    - Handles dynamic-key dictionaries and optional null values.
    """
    if actual is None and expected is not None:
        return True, "Actual value is null, considered acceptable for an optional field."

    if type(actual) is not type(expected):
        if isinstance(expected, str) and actual is None:
            return True, "Type mismatch (str vs None) is considered acceptable."
        return False, f"Type mismatch: expected {type(expected).__name__}, got {type(actual).__name__}"

    if isinstance(expected, dict):
        if is_dynamic_key_dict(expected):
            if not actual:
                return not expected, "Actual dictionary is empty, but expected has dynamic keys."
            
            template_schema = next(iter(expected.values()))
            for key, actual_value in actual.items():
                is_match, reason = compare_json_schemas(actual_value, template_schema, ignore_values, strict_keys)
                if not is_match:
                    return False, f"Mismatch in dynamic-key item '{key}': {reason}"
            return True, "All items in dynamic-key dictionary match the template schema."

        keys_to_check = expected.keys() if strict_keys else actual.keys() & expected.keys()
        
        if strict_keys and (missing_keys := set(expected.keys()) - set(actual.keys())):
            return False, f"Missing key(s): {', '.join(missing_keys)} in actual response"
            
        for key in keys_to_check:
            is_match, reason = compare_json_schemas(actual.get(key), expected.get(key), ignore_values, strict_keys)
            if not is_match:
                return False, f"Mismatch in key '{key}': {reason}"
        return True, "Structure matches"

    elif isinstance(expected, list):
        if not isinstance(actual, list):
            return False, "Actual response is not a list, but expected a list."
        if not expected:
            return True, "Structure matches"
        if not actual:
            return False, "Actual response is an empty list, but expected a list with items"
        
        template = expected[0]
        for i, item in enumerate(actual):
            is_match, reason = compare_json_schemas(item, template, ignore_values, strict_keys)
            if not is_match:
                return False, f"Mismatch in list item at index {i}: {reason}"
        return True, "Structure matches"

    else:
        if ignore_values: return True, "Type matches"
        if actual != expected: return False, f"Value mismatch: expected '{expected}', got '{actual}'"
        return True, "Value matches"

def validate_apis(api_calls, base_url, auth_token):
    """Iterates through parsed API calls, executes them, and validates the responses."""
    if not api_calls: return []

    print(f"\nStarting API validation for {len(api_calls)} endpoints...")
    results = []
    headers = {"Authorization": f"Bearer {auth_token}", "Content-Type": "application/json"}

    for i, api_call in enumerate(api_calls):
        method = api_call.get("method", "UNKNOWN").upper()
        path = api_call.get("path")
        
        print(f"[{i+1}/{len(api_calls)}] Testing: {method} {path}")

        result_details = {
            "api_call": api_call, "status": "FAIL", "reason": "",
            "actual_status_code": None, "actual_response_body": None,
        }

        if not path:
            result_details["reason"] = "API path not found in parsed data."
            results.append(result_details); continue
        
        url = f"{base_url}{path}"
        body_data = None
        if api_call.get("body"):
            try: body_data = json.loads(api_call["body"])
            except json.JSONDecodeError:
                result_details["reason"] = "Invalid JSON in the documented request body."
                results.append(result_details); continue
        
        try:
            response = requests.request(method, url, headers=headers, json=body_data, timeout=15)
            result_details["actual_status_code"] = response.status_code

            if not response.ok:
                result_details["reason"] = f"Request failed with status code {response.status_code}."
                try: result_details["actual_response_body"] = response.json()
                except json.JSONDecodeError: result_details["actual_response_body"] = response.text
                results.append(result_details); continue

            expected_output_str = api_call.get("expected_output", "")
            
            try:
                expected_json = json.loads(expected_output_str)
                actual_json = response.json()
                result_details["actual_response_body"] = actual_json
                
                is_get_request = (method == "GET")
                ignore_values = is_get_request
                strict_keys = not is_get_request

                if path == "/api/v1/login":
                    keys_ok = all(k in actual_json for k in ["role", "token", "user"])
                    token_ok = isinstance(actual_json.get("token"), str) and actual_json.get("token")
                    is_match, reason = (True, "Response structure is correct and token is present.") if keys_ok and token_ok else (False, "Login response is missing required keys or token.")
                elif path == "/api/v1/register" and isinstance(actual_json, list) and isinstance(expected_json, dict):
                    is_match, reason = False, "Structural mismatch: Expected a dictionary but got a list."
                else:
                    is_match, reason = compare_json_schemas(actual_json, expected_json, ignore_values=ignore_values, strict_keys=strict_keys)

                if is_match:
                    result_details["status"] = "PASS"
                    result_details["reason"] = "Response validation passed."
                else:
                    result_details["reason"] = f"Response schema mismatch: {reason}"

            except (json.JSONDecodeError, requests.exceptions.JSONDecodeError):
                actual_text = response.text.strip()
                result_details["actual_response_body"] = actual_text
                if actual_text == expected_output_str.strip():
                    result_details["status"] = "PASS"
                    result_details["reason"] = "Actual text response matches expected text."
                else:
                    result_details["reason"] = f"Text response mismatch: Expected '{expected_output_str}', got '{actual_text}'."
        
        except requests.exceptions.RequestException as e:
            result_details["reason"] = f"Request failed: {e}"
        
        results.append(result_details)
    
    print("API validation complete.")
    return results

def generate_report(validation_results, timestamp):
    """
    Generates a markdown report from the validation results.
    Only shows full details for failed tests.
    """
    if not validation_results:
        return "No validation results to report."

    passed_count = sum(1 for r in validation_results if r["status"] == "PASS")
    failed_count = len(validation_results) - passed_count
    
    report_lines = [
        f"# API Validation Report - {timestamp}",
        f"**Target File:** `{TARGET_MANUAL_FILE}`",
        f"**Target Server:** `{BASE_URL}`",
        "---",
        "## Summary",
        f"- **Total Tests:** {len(validation_results)}",
        f"- **Passed:** {passed_count}",
        f"- **Failed:** {failed_count}",
        "---",
        "## Detailed Results"
    ]

    # Separate passed and failed to group them
    passed_results = [r for r in validation_results if r['status'] == 'PASS']
    failed_results = [r for r in validation_results if r['status'] == 'FAIL']

    # List all failed tests with full details first
    if failed_results:
        report_lines.append("\n### Failed Tests\n")
        for result in failed_results:
            api_call = result["api_call"]
            method = api_call.get("method", "N/A")
            path = api_call.get("path", "N/A")
            
            report_lines.append(f"#### `[FAIL]` {method} {path}")
            report_lines.append(f"**Description:** {api_call.get('description', 'N/A')}")
            report_lines.append(f"**Result:** {result['reason']}")
            
            if api_call.get("body"):
                report_lines.append("\n**Request Body:**")
                report_lines.append(f"```json\n{json.dumps(json.loads(api_call['body']), indent=2)}\n```")
            
            report_lines.append("\n**Expected Output (from docs):**")
            try:
                expected_json = json.dumps(json.loads(api_call['expected_output']), indent=2)
                report_lines.append(f"```json\n{expected_json}\n```")
            except (json.JSONDecodeError, TypeError):
                report_lines.append(f"```\n{api_call.get('expected_output', 'Invalid format')}\n```")

            actual_body = result['actual_response_body']
            actual_status_code = result['actual_status_code']
            report_lines.append(f"\n**Actual Response (Status: {actual_status_code}):**")
            
            if path == "/api/v1/syslogs" and isinstance(actual_body, list):
                total_logs = len(actual_body)
                if total_logs > SYSLOG_REPORT_LIMIT:
                    report_lines.append(f"*Showing first {SYSLOG_REPORT_LIMIT} of {total_logs} entries:*")
                    actual_body = actual_body[:SYSLOG_REPORT_LIMIT]

            if isinstance(actual_body, (dict, list)):
                actual_json = json.dumps(actual_body, indent=2)
                report_lines.append(f"```json\n{actual_json}\n```")
            else:
                report_lines.append(f"```\n{str(actual_body)}\n```")
            report_lines.append("\n---\n")
    
    # List a summary of passed tests
    if passed_results:
        report_lines.append("\n### Passed Tests\n")
        for result in passed_results:
            api_call = result["api_call"]
            method = api_call.get("method", "N/A")
            path = api_call.get("path", "N/A")
            report_lines.append(f"- `[PASS]` {method} {path}")

    return "\n".join(report_lines)

# --- Main Execution ---

if __name__ == "__main__":
    if not client: exit(1)
        
    if not setup_git_repository(GIT_REPO_URL, REPO_LOCAL_DIR): exit(1)

    manual_file_path = os.path.join(REPO_LOCAL_DIR, MANUAL_DIRECTORY_IN_REPO, TARGET_MANUAL_FILE)
    if not os.path.exists(manual_file_path):
        print(f"ERROR: Target manual file not found at: {manual_file_path}"); exit(1)
        
    print(f"\nReading target file: {manual_file_path}")
    with open(manual_file_path, 'r', encoding='utf-8') as f: markdown_content = f.read()

    auth_token = get_auth_token(BASE_URL, LOGIN_CREDENTIALS)
    if not auth_token: print("Halting execution due to authentication failure."); exit(1)

    parsed_api_calls = parse_api_documentation_with_openai(markdown_content)
    if not parsed_api_calls: print("Halting execution due to parsing failure."); exit(1)

    validation_results = validate_apis(parsed_api_calls, BASE_URL, auth_token)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_content = generate_report(validation_results, timestamp)
    
    report_filename = REPORT_FILENAME_TEMPLATE.format(timestamp=timestamp)
    with open(report_filename, 'w', encoding='utf-8') as f: f.write(report_content)
        
    print(f"\nReport has been saved to: {report_filename}")
    passed_count = sum(1 for r in validation_results if r["status"] == "PASS")
    failed_count = len(validation_results) - passed_count
    print(f"\n--- REPORT SUMMARY ---")
    print(f"Total Tests: {len(validation_results)}, Passed: {passed_count}, Failed: {failed_count}")
    print("----------------------")