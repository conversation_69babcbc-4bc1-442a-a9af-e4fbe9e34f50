# Validate Manual

This function has two verification procedures
- Validate Commands(`validateCommands.py`)
- Validate Http APIs(`validateHttpAPIs.py`)

## Validate Commands

This script automates the validation of command-line examples found in technical documentation (user manuals). It compares the commands from your documentation against the *actual* help text generated by your application's executables, using the power of OpenAI's GPT models to intelligently identify discrepancies.

The primary goal is to solve a common problem in technical writing: documentation becoming outdated as the software evolves. This tool helps ensure that the command examples provided to users are accurate and functional.

### How It Works

The script follows a clear, automated workflow:

1.  **Generate CLI Help**: It runs your specified CLI executables with their respective help arguments to build a single, comprehensive `cli_help_output.txt` file. This file serves as the ground truth.
2.  **Clone Documentation Repo**: It performs a fresh `git clone` of your user guide repository to ensure it's working with the latest version.
3.  **Extract Command Examples**: It parses all Markdown files in the specified directory, using regex to extract any command examples found within code blocks (```).
4.  **Analyze in Chunks**: The extracted commands are sent in chunks to the OpenAI API, along with the ground truth help text.
5.  **AI Review**: The AI, guided by a detailed system prompt, reviews each command for errors like invalid syntax, incorrect flags, or non-existent sub-commands.
6.  **Generate Report**: The script collects all discrepancies found by the AI and compiles them into a final, easy-to-read Markdown report.

### Prepare
1.  Install python 3.11
2.  Install dependency library
    ```python
    pip install openai python-dotenv
    ```
3.  Git installed and configured on your system.
4.  Add your OpenAI API key in `.env`:
    ```ini
    OPENAI_API_KEY="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    ```

### Example

Run ValidateManual.
```shell
$ python validateManual.py
🔍 Auto-discovering commands from multiple executables...

--- Processing special executable: bbctl ---
✅ Successfully fetched main help for 'bbctl'.
✅ Found 21 top-level sub-commands: agent, beep, config, debug, firewall, firmware, gwd, idps, mqtt, msg, mtderase, opcua, rese
t, scan, service, snmp, ssh, switch, syslog, util, wg
   -> Fetching help for 'bbctl help agent'...
   -> Fetching help for 'bbctl help beep'...
   -> Fetching help for 'bbctl help config'...
   -> Fetching help for 'bbctl help debug'...
   -> Fetching help for 'bbctl help firewall'...
   -> Fetching help for 'bbctl help firmware'...
   -> Fetching help for 'bbctl help gwd'...
   -> Fetching help for 'bbctl help idps'...
   -> Fetching help for 'bbctl help mqtt'...
   -> Fetching help for 'bbctl help msg'...
   -> Fetching help for 'bbctl help mtderase'...
   -> Fetching help for 'bbctl help opcua'...
   -> Fetching help for 'bbctl help reset'...
   -> Fetching help for 'bbctl help scan'...
   -> Fetching help for 'bbctl help service'...
   -> Fetching help for 'bbctl help snmp'...
   -> Fetching help for 'bbctl help ssh'...
   -> Fetching help for 'bbctl help switch'...
   -> Fetching help for 'bbctl help syslog'...
   -> Fetching help for 'bbctl help util'...
   -> ✅ Found nested commands under 'util': genpair, encrypt, decrypt, export, import
   -> Fetching help for 'bbctl help util genpair'...
   -> Fetching help for 'bbctl help util encrypt'...
   -> Fetching help for 'bbctl help util decrypt'...
   -> Fetching help for 'bbctl help util export'...
   -> Fetching help for 'bbctl help util import'...
   -> Fetching help for 'bbctl help wg'...

--- Processing regular executable: bbrootsvc ---
✅ Successfully fetched help for 'bbrootsvc'.

--- Processing regular executable: bbnmssvc ---
✅ Successfully fetched help for 'bbnmssvc'.

--- Processing regular executable: bblogsvc ---
✅ Successfully fetched help for 'bblogsvc'.

--- Processing regular executable: bbidpsvc ---
✅ Successfully fetched help for 'bbidpsvc'.

✅ Successfully generated a comprehensive help file at './cli_help_output.txt'
🔄 Ensuring fresh copy of Git <NAME_EMAIL>:bbtechhive/userguide.git...
✅ Successfully cloned repository.
📖 Searching for Markdown (.md) files in './userguide_repo\user_guide_chapters' and extracting commands with context...
✅ Found and extracted 524 command examples with context from 22 Markdown files.
📖 Reading definitive CLI help text from './cli_help_output.txt'...

🤖 Splitting 524 examples into 27 chunks of size ~20.
✅ OpenAI Client Initialized. Using model: gpt-4o
--- Analyzing chunk 1/27 ---
    -> ✅ Chunk 1 processed successfully, found 0 errors.
--- Analyzing chunk 2/27 ---
    -> ✅ Chunk 2 processed successfully, found 0 errors.
--- Analyzing chunk 3/27 ---
    -> ✅ Chunk 3 processed successfully, found 0 errors.
--- Analyzing chunk 4/27 ---
    -> ✅ Chunk 4 processed successfully, found 0 errors.
--- Analyzing chunk 5/27 ---
    -> ✅ Chunk 5 processed successfully, found 0 errors.
--- Analyzing chunk 6/27 ---
    -> ✅ Chunk 6 processed successfully, found 0 errors.
--- Analyzing chunk 7/27 ---
    -> ✅ Chunk 7 processed successfully, found 4 errors.
--- Analyzing chunk 8/27 ---
    -> ✅ Chunk 8 processed successfully, found 0 errors.
--- Analyzing chunk 9/27 ---
    -> ✅ Chunk 9 processed successfully, found 0 errors.
--- Analyzing chunk 10/27 ---
    -> ✅ Chunk 10 processed successfully, found 0 errors.
--- Analyzing chunk 11/27 ---
    -> ✅ Chunk 11 processed successfully, found 0 errors.
--- Analyzing chunk 12/27 ---
    -> ✅ Chunk 12 processed successfully, found 0 errors.
--- Analyzing chunk 13/27 ---
    -> ✅ Chunk 13 processed successfully, found 0 errors.
--- Analyzing chunk 14/27 ---
    -> ✅ Chunk 14 processed successfully, found 0 errors.
--- Analyzing chunk 15/27 ---
    -> ✅ Chunk 15 processed successfully, found 0 errors.
--- Analyzing chunk 16/27 ---
    -> ✅ Chunk 16 processed successfully, found 0 errors.
--- Analyzing chunk 17/27 ---
    -> ✅ Chunk 17 processed successfully, found 0 errors.
--- Analyzing chunk 18/27 ---
    -> ✅ Chunk 18 processed successfully, found 0 errors.
--- Analyzing chunk 19/27 ---
    -> ✅ Chunk 19 processed successfully, found 1 errors.
--- Analyzing chunk 20/27 ---
    -> ✅ Chunk 20 processed successfully, found 0 errors.
--- Analyzing chunk 21/27 ---
    -> ✅ Chunk 21 processed successfully, found 0 errors.
--- Analyzing chunk 22/27 ---
    -> ✅ Chunk 22 processed successfully, found 8 errors.
--- Analyzing chunk 23/27 ---
    -> ✅ Chunk 23 processed successfully, found 0 errors.
--- Analyzing chunk 24/27 ---
    -> ✅ Chunk 24 processed successfully, found 1 errors.
--- Analyzing chunk 25/27 ---
    -> ✅ Chunk 25 processed successfully, found 0 errors.
--- Analyzing chunk 26/27 ---
    -> ✅ Chunk 26 processed successfully, found 0 errors.
--- Analyzing chunk 27/27 ---
    -> ✅ Chunk 27 processed successfully, found 0 errors.

💾 Saving command validation report to command_validation_report_2025-07-07_162540.md...
✅ Report saved successfully.

========================================
🔬 FINAL REPORT SUMMARY (also saved to file)
========================================
Total errors found: 14
See command_validation_report_2025-07-07_162540.md for full details.
```
And you can check report `command_validation_report_2025-07-07_162540.md`.

## Validate HTTP APIs

This script provides automatically validates HTTP API endpoints against their documentation written in a Markdown file. It leverages the OpenAI GPT-4o model to parse unstructured documentation, runs the described API calls against a live server, and generates a concise validation report highlighting discrepancies.

This tool is designed to bridge the gap between human-readable documentation and machine-executable tests, ensuring that your API behaves exactly as documented.

### How It Works

The script follows a multi-step process to perform the validation:

1.  **Clone Repository**: It starts by cloning a fresh copy of the specified Git repository (`**************:bbtechhive/userguide.git`) to ensure it's working with the latest version of the API documentation.
2.  **Parse Documentation with AI**: The content of the target Markdown file (`08-bbrootsvc-HTTP-endpoint-API-Documentation.md`) is sent to the OpenAI GPT-4o API. A specialized prompt instructs the model to extract all API examples and return them in a structured JSON format, including the HTTP method, path, request body, and expected output.
3.  **Authenticate**: The script performs an initial `POST` request to `/api/v1/login` to obtain a bearer token, which is required for all subsequent API calls.
4.  **Execute & Validate API Calls**: For each API call extracted by the AI, the script:
    - Sends an HTTP request to the target server (`http://localhost:27182`).
    - Receives the actual response.
5.  **Perform Smart Comparison**: A sophisticated comparison logic is used to validate the actual response against the expected output from the documentation. This logic is "smart" because it understands common API patterns:
    - **Dynamic Values**: It ignores values that are naturally dynamic, such as authentication tokens, timestamps, and dynamically generated IDs.
    - **Flexible Schema for GET**: For `GET` requests, it performs a schema-based comparison. It checks for the presence and correct data type of keys rather than exact values. It also allows for optional keys (keys present in the documentation but not in the response) to support API evolution.
    - **Dynamic-Key Dictionaries**: It can validate responses where keys are dynamic (e.g., MAC addresses or other identifiers). It uses the first item in the documentation's example as a structural template to validate all items in the actual response.
    - **Non-JSON Responses**: It correctly handles and compares plain text responses (e.g., `ok`).
6.  **Generate Report**: Finally, it creates a clean and focused Markdown report (`command_validation_report_{timestamp}.md`).
    - **Failures First**: All failed tests are listed at the top with full details, including the request, expected output, and actual response, making it easy to identify and debug issues.
    - **Successes Summarized**: All passed tests are listed in a concise summary at the end, keeping the report uncluttered.

### Prepare
1.  Install python 3.11
2.  Install dependency library
    ```python
    pip install openai python-dotenv requests
    ```
3.  Git installed and configured on your system.
4.  Add your OpenAI API key in `.env`:
    ```ini
    OPENAI_API_KEY="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    ```
5. Running `bbrootsvc`(named `root`) and `bbnmssvc`(named `client1`) and accessible at `http://localhost:27182`. All tests will fail if the server is not available.

### Example
```shell
$ python validateHttpApi.py
Ensuring fresh copy of Git <NAME_EMAIL>:bbtechhive/userguide.git...
Successfully removed old directory: ./userguide_repo
Successfully cloned repository.

Reading target file: ./userguide_repo\user_guide_chapters\08-bbrootsvc-HTTP-endpoint-API-Documentation.md

Attempting to authenticate to get API token...
Successfully authenticated. Token received.

Parsing API documentation with OpenAI...
Successfully parsed 23 API calls from the documentation.

Starting API validation for 23 endpoints...
[1/23] Testing: POST /api/v1/login
[2/23] Testing: POST /api/v1/users
[3/23] Testing: GET /api/v1/users
[4/23] Testing: PUT /api/v1/users
[5/23] Testing: DELETE /api/v1/users
[6/23] Testing: POST /api/v1/2fa/secret
[7/23] Testing: POST /api/v1/2fa/validate
[8/23] Testing: GET /api/v1/2fa/secret
[9/23] Testing: PUT /api/v1/2fa/secret
[10/23] Testing: DELETE /api/v1/2fa/secret
[11/23] Testing: GET /api/v1/register
[12/23] Testing: GET /api/v1/info
[13/23] Testing: GET /api/v1/syslogs
[14/23] Testing: POST /api/v1/commands
[15/23] Testing: GET /api/v1/commands
[16/23] Testing: GET /api/v1/devices
[17/23] Testing: GET /api/v1/topology
[18/23] Testing: GET /api/v1/wg
[19/23] Testing: GET /api/v1/clients
[20/23] Testing: POST /api/v1/clients
[21/23] Testing: GET /api/v1/license-info
[22/23] Testing: GET /api/v1/ssh/tunnels
[23/23] Testing: GET /api/v1/agent/ports
API validation complete.

Report has been saved to: command_validation_report_20250710_170943.md

--- REPORT SUMMARY ---
Total Tests: 23, Passed: 11, Failed: 12
----------------------
```
And you can check report `command_validation_report_20250710_170943.md`.
