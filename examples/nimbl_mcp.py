"""
FastMCP REST API Wrapper for NIMBL API

This python example shows how to use FastMCP to wrap a REST API server,
making NIMBL REST endpoints available as MCP tools that can be called
by AI assistants or other MCP clients. Only a few APIs are wrapped
for demonstration purposes, but you can extend this to cover more endpoints as needed.

You can use the configuration like this in your MCP client:
```json
{
  "mcpServers": {
    "nimble-api-wrapper": {
      "command": "/$HOME/venv-3.11/bin/python",
      "args": ["$HOME/mnms/examples/nimbl_mcp.py"]
    }
  }
}
```

"""

import asyncio
import json
from typing import Any, Dict, List, Optional
import httpx
import requests
from fastmcp import FastMCP

mcp = FastMCP("NIMBL REST API Wrapper")

BASE_URL = "http://localhost:27182/api/v1"

client = httpx.AsyncClient()
headers = {
        'Content-Type': 'application/json'
}

post_data = {
        'user': 'admin',  # Replace with your Nimbl username
        'password':'default' # Replace with your Nimbl password
}

json_payload = json.dumps(post_data)

url = f"{BASE_URL}/login"
response=requests.post(url,headers=headers, data=json_payload)
json_res= json.loads(response.text)
token=json_res['token']

@mcp.tool()
async def get_devices() -> List[Dict[str, Any]]:
    """
    Get devices information from NIMBL.

    Returns:
        List of devices
    """
    url = f"{BASE_URL}/devices"
    try:
        response = await client.get(url, headers={'Authorization': f'Bearer {token}'})
        response.raise_for_status()
        return response.json()
    except httpx.RequestError as e:
        return [{"error": f"Request failed: {str(e)}"}]
    except httpx.HTTPStatusError as e:
        return [{"error": f"HTTP error {e.response.status_code}: {e.response.text}"}]


@mcp.tool()
async def get_commands() -> List[Dict[str, Any]]:
    """
    Get commands information from NIMBL.

    Returns:
        List of commands
    """
    url = f"{BASE_URL}/commands?cmd=all"
    try:
        response = await client.get(url, headers={'Authorization': f'Bearer {token}'})
        response.raise_for_status()
        return response.json()
    except httpx.RequestError as e:
        return [{"error": f"Request failed: {str(e)}"}]
    except httpx.HTTPStatusError as e:
        return [{"error": f"HTTP error {e.response.status_code}: {e.response.text}"}]

@mcp.tool()
async def post_command(cmd: str) -> List[Dict[str, Any]]:
    """
    Create a new command in NIMBL.

    Args:
        cmd: json string containing one command to be created.
        
    Returns:
        Created commands data or error message
    """
    url = f"{BASE_URL}/commands"
    data=f'[{"kind":"usercommand", "command":"{cmd}"}]'

    try:
        response = await client.post(url, json=data, headers={'Authorization': f'Bearer {token}'})
        response.raise_for_status()
        return response.json()
    except httpx.RequestError as e:
        return {"error": f"Request failed: {str(e)}"}
    except httpx.HTTPStatusError as e:
        return {"error": f"HTTP error {e.response.status_code}: {e.response.text}"}

# Resource for API information
@mcp.resource("api://info")
async def get_api_info() -> str:
    """Provide information about the wrapped REST API."""
    return json.dumps({
        "name": "NIMBL API Wrapper",
        "description": "MCP wrapper for NIMBL REST API",
        "base_url": BASE_URL,
        "available_endpoints": [
            "GET /devices - Get all devices",
            "POST /commands - Create new commands",
            "GET /commands?cmd=all - Get all commands",
        ],
        "tools": [
            "get_devices", "get_commands", 
            "post_commands"
        ]
    }, indent=2)

async def cleanup():
    """Clean up resources when shutting down."""
    await client.aclose()

async def main():
    """Run the MCP server."""
    try:
        # Run the MCP server
        await mcp.run_async()
    finally:
        # Clean up HTTP client
        await cleanup()

if __name__ == "__main__":
    # Run the server
    asyncio.run(main())
