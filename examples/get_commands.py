import requests
import json

headers = {
        'Content-Type': 'application/json'
}

post_data = {
        'user': 'admin',
        'password':'default'
}

json_payload = json.dumps(post_data)

url = 'http://localhost:27182/api/v1/login'
response=requests.post(url,headers=headers, data=json_payload)
json_res= json.loads(response.text)
token=json_res['token']

headers = {
        'Authorization': 'Bearer ' + token
}

url = 'http://localhost:27182/api/v1/commands?cmd=all'
response=requests.get(url,headers=headers)
print(response.text)
