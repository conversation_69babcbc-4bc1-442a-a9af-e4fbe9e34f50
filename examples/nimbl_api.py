# This python example script demonstrates how to authenticate 
# via POST to Nimbl API endpoint api/v1/login 
# and use the token received from the authentication to use in
# Nimbl HTTP endpoint API calls to accomplish variety of tasks such as:
#   - GET the list of devices via api/v1/devices endpoint
#   - POST Nimbl script commands via api/v1/commands endpoint
#   - GET the commands sent via api/v1/commands?cmd=all endpoint
#   - GET the status of the commands sent via api/v1/commands?cmd=status endpoint
#
# The complete Nimbl script command API documentation can be found at:
# https://www.blackbeartechhive.com/files/docs/nimbl_api.md

import requests
import json

headers = {
        'Content-Type': 'application/json'
}

post_data = {
        'user': 'admin',  # Replace with your Nimbl username
        'password':'default' # Replace with your Nimbl password
}

json_payload = json.dumps(post_data)

# Assume Nimbl API is running on localhost:27182
url = 'http://localhost:27182/api/v1/login'

# POST login data to the url to Authenticate and get the token
response=requests.post(url,headers=headers, data=json_payload)
json_res= json.loads(response.text)
token=json_res['token']

# Set the Authorization header with the token received
auth_header = {
        'Authorization': 'Bearer ' + token
}

url= 'http://localhost:27182/api/v1/devices'
# Get the list of devices
response=requests.get(url,headers=auth_header)
# response.text contains a JSON object with the map of devices keyed
# by their MAC address, each device has a dictionary with its details
print("list of devices:", response.text)

# prepare a config network  set command
# which takes the following parameters:
#   - mac_address: MAC address of the device
#   - ip_address_old: old IP address of the device
#   - ip_address_new: new IP address of the device
#   - netmask: subnet mask
#   - gateway: gateway IP address
#   - hostname: hostname of the device  
#   - dhcp_setting: 0 for static IP, 1 for DHCP
#

# Note: Replace the values below with actual values for your device
mac_address="00-07-17-03-22-22"
ip_address_old="*********"
ip_address_new="*********"
netmask="***********"
gateway="**********"
hostname="test7654"
dhcp_setting="0"  # 0 for static, 1 for DHCP

config_command=f"config network set {mac_address} {ip_address_old} {ip_address_new} {netmask} {gateway} {hostname} {dhcp_setting}"

# one more Nimbl script commands can be sent via POST to api/v1/commands
commands=f'[{"kind":"usercommand", "command":"{config_command}"}]'

url='http://localhost:27182/api/v1/commands'
response=requests.post(url,headers=auth_header,data=commands)
# response.text contains the result of the command posted
print("Nimbl script command posted:", response.text)

# after some time we can check the status of all ofthe commands sent
url = 'http://localhost:27182/api/v1/commands?cmd=all'
response=requests.get(url,headers=auth_header)
# response.text contains a JSON object with the map of commands keyed
# by their ID, each command has a dictionary with its details.
# the ID is the full command string itself.
print("status of all the commands:", response.text)