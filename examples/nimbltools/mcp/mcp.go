package mcp

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mnms"
	"strings"
	"time"

	"github.com/mark3labs/mcp-go/server"
)

type events struct {
	Post      post      // post cmd to root
	GetDevice getDevice // get device from root
}
type getDevice func() (data []byte, err error)
type post func(cmd mnms.CmdInfo, waitResult bool) (result, status []byte, err error) // post cmd to root

func initMcpTool(s *server.MCPServer, es events) {
	registerDevice(s, es)
	registerIdps(s, es)
}

func NewMcpTool(s *server.MCPServer, domain string) {
	tool := &McpTool{
		domain:      domain,
		commanduURL: fmt.Sprintf("%v/api/v1/commands", domain),
		deviceURL:   fmt.Sprintf("%v/api/v1/devices", domain),
	}
	es := events{
		Post:      tool.postCmd,
		GetDevice: tool.getDevice,
	}
	initMcpTool(s, es)
}

type McpTool struct {
	domain      string
	commanduURL string
	deviceURL   string
}

func (m *McpTool) postCmd(cmd mnms.CmdInfo, waitResult bool) (status, result []byte, err error) {
	cmdList := []mnms.CmdInfo{cmd}
	jsonBytes, err := json.Marshal(cmdList)
	if err != nil {
		return nil, nil, err
	}
	mnms.QC.AdminToken, err = mnms.GetToken("admin")
	if err != nil {
		return nil, nil, err
	}
	resp, err := mnms.PostWithToken(m.commanduURL, mnms.QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return nil, nil, err
	}
	defer resp.Body.Close()
	status, err = io.ReadAll(resp.Body)
	if err != nil {
		return nil, nil, err
	}
	if waitResult {
		ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
		defer cancel()
		cmdResult, err := mnms.QueryCmdTilFinished(ctx, m.domain, time.Second, cmd.Command)
		if err != nil {
			return nil, nil, err
		}
		if strings.HasPrefix(cmdResult.Status, "err") {
			return nil, nil, fmt.Errorf("%v", cmdResult.Status)
		}
		return []byte(cmdResult.Status), []byte(cmdResult.Result), nil
	}

	return status, nil, err
}

func (m *McpTool) getDevice() ([]byte, error) {
	token, err := mnms.GetToken("admin")
	if err != nil {
		return nil, err
	}
	resp, err := mnms.GetWithToken(m.deviceURL, token)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("error StatusCode:%v", resp.StatusCode)
	}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return body, err
}
