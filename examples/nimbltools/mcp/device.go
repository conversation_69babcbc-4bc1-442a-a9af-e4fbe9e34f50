package mcp

import (
	"context"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

func registerDevice(s *server.MCPServer, es events) {
	addDeviceTool(s, es)
}

func addDeviceTool(s *server.MCPServer, es events) {
	add := mcp.NewTool("get_device",
		mcp.WithDescription("get all devices from nimble"),
	)
	s.AddTool(add, func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		b, err := es.GetDevice()
		if err != nil {
			return nil, err
		}
		return mcp.NewToolResultText(string(b)), nil
	})
}
