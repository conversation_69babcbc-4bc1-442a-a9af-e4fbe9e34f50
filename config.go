package mnms

import (
	"errors"
	"fmt"
	"strings"

	"github.com/qeof/q"
)

var (
	ErrorDeviceExisted = errors.New("device ip exist")
	ErrorSnmpfunction  = errors.New("please ensure SNMP function is enabled and community string is correct.")
)

// There are three types of config: basic, snmp and switch cli
//
// this file implements basic config.
//
// The basic config are :
//    host, ip,  trap server, syslog server, enable ip,
//    username, password, etc.
//
// reset, mtderase and beep are not here. They are basic commands in cmd.go
//
// snmp stuff is in snmp.go
//
// switch cli stuff is in switch.go

func getGwdNetConfig(dev *DevInfo) GwdNetworkConfig {
	return GwdNetworkConfig{
		IPAddress:    dev.IPAddress,
		MACAddress:   dev.Mac,
		NewIPAddress: dev.IPAddress,
		Netmask:      dev.Netmask,
		Gateway:      dev.Gateway,
		Hostname:     dev.Hostname,
		Username:     "admin",   // XXX
		Password:     "default", // XXX
	}
}

// setting some config (like IP ) may have to be device specific.
// some devices can be set via gwd, some may not have gwd.
//  some devices may have snmp working.
//  some may or may not have switch cli.
// start with basic -- GWD.  gradually add support for devices
// that require special support (e.g. device has no gwd, only snmp).

type snmpBasicSetting struct {
	Oid  string
	Type string
}

func getSnmpBasicSetting(targetIp, cate, kind string) (*snmpBasicSetting, error) {
	syslogFields := map[string]string{
		"status":       ".********.0:Integer",
		"server-ip":    ".********.0:OctetString",
		"server-port":  ".********.0:Integer",
		"log-level": ".********.0:Integer",
		"LogToFlash":   ".********.0:Integer",
	}

	trapServerFields := map[string]string{
		"status":      ".*******.0:Integer",
		"server-ip":   ".*******.0:OctetString",
		"server-port": ".*******.0:Integer",
		"community":   ".*******.0:OctetString",
	}
	pointToDefined := map[string]string{}

	switch cate {
	case "syslog":
		pointToDefined = syslogFields
	case "snmp-trap":
		pointToDefined = trapServerFields
	}

	objID, err := SnmpGetObjectID(targetIp)
	if err != nil {
		q.Q(err)
		return nil, fmt.Errorf("dev not support snmp: %v", err)
	}
	q.Q("objID", objID)

	t, ok := pointToDefined[kind]
	if !ok {
		q.Q("error", kind)
		return nil, fmt.Errorf("invalid syslog field %v", kind)
	}
	tt := strings.Split(t, ":")
	oid := objID + tt[0]
	datatype := tt[1]
	return &snmpBasicSetting{Oid: oid, Type: datatype}, nil
}

func SetSnmpOneCommand(IPAddress string, cate string, value string, kind string) error {
	setting, err := getSnmpBasicSetting(IPAddress, cate, kind)
	if err != nil {
		return err
	}
	pkt, err := SnmpSet(IPAddress, setting.Oid, value, setting.Type)
	if err != nil {
		return err
	}
	if uint8(pkt.Error) > 0 {
		return fmt.Errorf("%v", pkt.Error.String())
	}

	return nil
}

type SyslogStatus struct {
	Status     string `json:"status"`
	Serverip   string `json:"server_ip"`
	ServerPort string `json:"server_port"`
	Level      string `json:"log_level"`
	LogToFlash string `json:"logToflash"`
}

func GetSnmpSyslogStatus(IPAddress string) (SyslogStatus, error) {
	cate := "syslog"
	result := SyslogStatus{}
	st, err := getSnmpBasicSetting(IPAddress, cate, "status")
	if err != nil {
		return result, err
	}
	ip, err := getSnmpBasicSetting(IPAddress, cate, "server-ip")
	if err != nil {
		return result, err
	}
	port, err := getSnmpBasicSetting(IPAddress, cate, "server-port")
	if err != nil {
		return result, err
	}
	lev, err := getSnmpBasicSetting(IPAddress, cate, "log-level")
	if err != nil {
		return result, err
	}
	flash, err := getSnmpBasicSetting(IPAddress, cate, "LogToFlash")
	if err != nil {
		return result, err
	}
	res, err := SnmpGet(IPAddress, []string{st.Oid, ip.Oid, port.Oid, lev.Oid, flash.Oid})
	if err != nil {
		return result, err
	}
	for _, v := range res.Variables {
		switch v.Name {
		case st.Oid:
			result.Status = PDUToString(v)
		case ip.Oid:
			result.Serverip = PDUToString(v)
		case port.Oid:
			result.ServerPort = PDUToString(v)
		case lev.Oid:
			result.Level = PDUToString(v)
		case flash.Oid:
			result.LogToFlash = PDUToString(v)
		}
	}

	return result, nil
}

func GwdConfig(conf GwdNetworkConfig) error {
	b, err := gwdSetConfigPacket(conf)
	if err != nil {
		return err
	}
	return GwdBroadcast(b)
}

func ConfigCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	if strings.HasPrefix(cmd, "config user") {
		return ConfigUserAuthCmd(cmdinfo)
	}

	if strings.HasPrefix(cmd, "config local syslog ") && QC.IsRoot {
		return RootConfigSyslogCmd(cmdinfo)
	}

	q.Q("unrecognized", cmd, len(cmd))
	cmdinfo.Status = "error: unknown command"
	return cmdinfo
}

// run gwd command
func GwdCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	q.Q("cmd = ", cmd)

	// gwd beep
	if strings.HasPrefix(cmd, "gwd beep") {
		return GwdBeepCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "gwd reset") {
		return GwdResetCmd(cmdinfo)
	}
	// gwd config network
	if strings.HasPrefix(cmd, "gwd config") {
		return GwdConfigNetworkCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "gwd firmware update") {
		return GwdFirmwareCmd(cmdinfo)
	}
	if strings.HasPrefix(cmd, "gwd mtderase") {
		return GwdMtdEraseCmd(cmdinfo)
	}
	q.Q("unrecognized", cmd, len(cmd))
	cmdinfo.Status = "error: unknown command"
	return cmdinfo
}

// Usage :config user [mac address] [username] [password]
//
//	[mac address] : target device mac address
//	[username]    : target device login user name
//	[password]    : target device login passwaord
//
// Example :
//
//	config user AA-BB-CC-DD-EE-FF admin default
func ConfigUserAuthCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Split(cmd, " ")
	standardMac := cmdinfo.DevId
	username := ws[3]
	password := ws[4]
	dev, err := FindDev(standardMac)
	if err != nil || dev == nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo
	}
	dev.UserName = username
	dev.PassWord = password
	// Insert and publish device
	InsertAndPublishDevice(*dev)
	cmdinfo.Status = "ok"
	return cmdinfo
}
