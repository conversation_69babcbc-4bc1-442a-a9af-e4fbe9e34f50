//go:build !ci
// +build !ci

package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"slices"
	"strings"
	"testing"
	"time"

	"github.com/influxdata/go-syslog/v3"
)

var rootsvcPath = "bbrootsvc"
var nmssvcPath = "bbnmssvc"
var logsvcPath = "bblogsvc"
var ctlPath = "bbctl"

var passedItems = []string{}
var failedItems = []string{}

func build() {
	fmt.Println("Building bbnim")
	cmd := "make"
	exec.Command("bash", "-c", cmd).Run()

	var exe string
	if runtime.GOOS == "windows" {
		exe = ".exe"
	}

	rootsvcPath = filepath.Join("bbrootsvc", "bbrootsvc"+exe)
	nmssvcPath = filepath.Join("bbnmssvc", "bbnmssvc"+exe)
	logsvcPath = filepath.Join("bblogsvc", "bblogsvc"+exe)
	ctlPath = filepath.Join("bbctl", "bbctl"+exe)

	files := []string{rootsvcPath, nmssvcPath, logsvcPath, ctlPath}
	for _, file := range files {
		if _, err := os.Stat(file); os.IsNotExist(err) {
			fmt.Printf("%s not found\n", file)
			os.Exit(1)
		}
	}
}

var regressionRootUrl = "http://localhost:27182"
var regressionNMS1Url = "http://localhost:27183"
var regressionNMS2Url = "http://localhost:27184"

func runProcess() {
	// run
	fmt.Println("Starting bbnim: bbrootsvc, bbnmssvc, bblogsvc")
	p, err := randomOpenPort()
	if err != nil {
		fmt.Println("Failed to get random open port")
		os.Exit(1)
	}
	fmt.Println("Starting bbrootsvc on port: ", p)
	cmd := exec.Command(rootsvcPath, "-n", "root", "-p", fmt.Sprintf("%d", p))
	err = cmd.Start()
	if err != nil {
		fmt.Printf("Failed starting bbrootsvc: %s\n", err)
		os.Exit(1)
	}
	time.Sleep(1 * time.Second)

	type SvcInfo struct {
		Name string   `json:"name"`
		Path string   `json:"path"`
		Ars  []string `json:"args"`
	}

	regressionRootUrl = "http://localhost:" + fmt.Sprintf("%d", p)
	fmt.Println("Root url: ", regressionRootUrl)

	nmsPort, err := randomOpenPort()
	if err != nil {
		fmt.Println("Failed to get random open port")
		os.Exit(1)
	}
	regressionNMS1Url = "http://localhost:" + fmt.Sprintf("%d", nmsPort)
	fmt.Println("NMS1 url: ", regressionNMS1Url)
	svcs := []SvcInfo{
		{"bbnmssvc", nmssvcPath, []string{"-n", "nms", "-r", regressionRootUrl, "-mb", ":11883", "-rs", ":5514", "-p", fmt.Sprintf("%d", nmsPort), "-ir", "5"}},
		{"bblogsvc", logsvcPath, []string{"-n", "log", "-r", regressionRootUrl}},
	}

	for _, svc := range svcs {
		cmd := exec.Command(svc.Path, svc.Ars...)
		err := cmd.Start()
		if err != nil {
			fmt.Printf("Failed starting %s: %s\n", svc.Name, err)
			os.Exit(1)
		}
	}

	time.Sleep(10 * time.Second)
}

func login() (string, error) {
	body := `{"user":"admin","password":"default"}`
	resp, err := http.Post(regressionRootUrl+"/api/v1/login", "application/json", strings.NewReader(body))
	if err != nil {
		fmt.Printf("Failed making POST request: %s\n", err)
		return "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("expected status OK but got: %v", resp.StatusCode)
	}
	fmt.Println("Login success")
	var loginResp struct {
		Token string `json:"token"`
		Role  string `json:"role"`
		User  string `json:"user"`
	}
	err = json.NewDecoder(resp.Body).Decode(&loginResp)
	if err != nil {
		fmt.Printf("Failed unmarshaling response: %s\n", err)
		return "", err
	}
	return loginResp.Token, nil
}

func getDeviceList(token string) (map[string]DevInfo, error) {
	// GET /api/v1/devices
	url := regressionRootUrl + "/api/v1/devices"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	var devices map[string]DevInfo
	err = json.NewDecoder(resp.Body).Decode(&devices)
	if err != nil {
		fmt.Printf("Failed unmarshaling response: %s\n", err)
		return nil, err
	}
	_, ok := devices["11-22-33-44-55-66"]
	if ok {
		delete(devices, "11-22-33-44-55-66")
	}
	return devices, nil
}

// better use for util commands only
func ctlCommand(commands [][]string) error {
	for _, cmd := range commands {
		// fmt.Println(ctl, cmd)
		if cmd[0] != "util" {
			cmd = append([]string{"-r", regressionRootUrl}, cmd...)
		}
		exec.Command(ctlPath, cmd...).Run()
		time.Sleep(100 * time.Millisecond)
	}
	return nil
}

// bbctl or ui ultimately will call this api and return commands created
func sendCommands(commands []CmdInfo, token string) ([]CmdInfo, error) {
	url := regressionRootUrl + "/api/v1/commands"
	jsonBytes, err := json.Marshal(commands)
	if err != nil {
		return nil, err
	}
	resp, err := PostWithToken(url, token, bytes.NewReader(jsonBytes))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		data, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("expected status OK but got: %v, %s", resp.StatusCode, string(data))
	}

	var respData []CmdInfo
	err = json.NewDecoder(resp.Body).Decode(&respData)
	if err != nil {
		return nil, err
	}
	return respData, nil
}

func queryCmdData(token string) (map[string]CmdInfo, error) {
	url := regressionRootUrl + "/api/v1/commands?cmd=all"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	cmddata := make(map[string]CmdInfo)
	err = json.NewDecoder(resp.Body).Decode(&cmddata)
	if err != nil {
		return nil, err
	}
	return cmddata, nil
}

func validateCommandsStatus(cmds []CmdInfo, token string) error {
	cmddata, err := queryCmdData(token)
	if err != nil {
		return err
	}

	for _, cmd := range cmds {
		cmdinfo, ok := cmddata[cmd.Command]
		if !ok {
			failedItems = append(failedItems, cmd.Command+": command not found")
			continue
		}

		// simple retry logic
		for i := 0; i < 3; i++ {
			if len(cmdinfo.Status) > 0 {
				break
			}
			time.Sleep(10 * time.Second)
			cmddata, _ = queryCmdData(token)
			cmdinfo = cmddata[cmd.Command]
		}

		if cmdinfo.Status != "ok" {
			failedItems = append(failedItems, cmd.Command+": "+cmdinfo.Status)
		} else {
			passedItems = append(passedItems, cmd.Command)
		}
	}
	return nil

}

func devTest(token string) error {
	fmt.Println("Running dev test")

	devices, err := getDeviceList(token)
	if err != nil {
		return err
	}
	fmt.Println("Devices number: ", len(devices))
	if len(devices) == 0 {
		return fmt.Errorf("no devices found")
	}

	commands := make([]CmdInfo, 0)
	for _, dev := range devices {
		commands = append(commands, CmdInfo{Command: fmt.Sprintf("snmp enable %s", dev.Mac)})
	}
	ret, err := sendCommands(commands, token)
	if err != nil {
		return err
	}
	time.Sleep(2 * time.Minute)
	// validate commands
	err = validateCommandsStatus(ret, token)
	if err != nil {
		return err
	}

	// random number 1000-9999
	randNum := 1000 + rand.Intn(9000)
	rand4char := fmt.Sprintf("%04d", randNum)
	randomhost := "host" + rand4char
	fmt.Println("Random hostname: ", randomhost)

	// test network
	for _, dev := range devices {
		commands = append(commands, CmdInfo{Command: fmt.Sprintf("config network set %s %s %s %s %s %s 0",
			dev.Mac, dev.IPAddress, dev.IPAddress, dev.Netmask, dev.Gateway, randomhost)})
	}
	_, err = sendCommands(commands, token)
	if err != nil {
		return err
	}
	// wait for device reboot
	time.Sleep(5 * time.Minute)
	// verify hostname is updated
	newDevices, err := getDeviceList(token)
	if err != nil {
		return err
	}
	for _, dev := range newDevices {
		if dev.Hostname != randomhost {
			failedItems = append(failedItems, "dev "+dev.Mac+" "+dev.IPAddress+": hostname not updated")
		} else {
			passedItems = append(passedItems, dev.Hostname)
		}
	}

	return nil
}

func mqttTest(token string) error {
	fmt.Println("Running mqtt test")
	commands := make([]CmdInfo, 0)
	commands = append(commands, CmdInfo{Command: "mqtt sub localhost:11883 topictest"})
	commands = append(commands, CmdInfo{Command: "mqtt pub localhost:11883 topictest hello this is a test message"})
	commands = append(commands, CmdInfo{Command: "mqtt unsub localhost:11883 topictest"})
	commands = append(commands, CmdInfo{Command: "mqtt list"})
	ret, err := sendCommands(commands, token)
	if err != nil {
		return err
	}
	time.Sleep(10 * time.Second)

	// validate commands
	err = validateCommandsStatus(ret, token)
	if err != nil {
		return err
	}

	// validate mqtt sub
	url := regressionRootUrl + "/api/v1/syslogs?number=10"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	logs := []syslog.Base{}
	err = json.NewDecoder(resp.Body).Decode(&logs)
	if err != nil {
		return err
	}
	found := false
	for _, log := range logs {
		if strings.Contains(*log.Message, "hello this is a test message") {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("expected message not found")
	}

	return nil
}

func wgTest(token string) error {
	fmt.Println("Running wg test")

	commands := make([]CmdInfo, 0)
	commands = append(commands, CmdInfo{Kind: "root", Command: "wg config interface set **********/24 55820"})
	commands = append(commands, CmdInfo{Kind: "root", Command: "wg config peer add Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg= 10.253.0.2/32"})
	commands = append(commands, CmdInfo{Command: "wg config interface set 10.253.0.2/32"})
	commands = append(commands, CmdInfo{Command: "wg config peer add 4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00= 10.253.0.0/24 122.147.151.234:55820 30"})
	ret, err := sendCommands(commands, token)
	if err != nil {
		return err
	}
	time.Sleep(10 * time.Second)
	// validate commands
	err = validateCommandsStatus(ret, token)
	if err != nil {
		return err
	}

	// wg api
	url := regressionRootUrl + "/api/v1/wg"
	resp, err := GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	wgData := make(map[string]WgInfo)
	err = json.NewDecoder(resp.Body).Decode(&wgData)
	if err != nil {
		return err
	}
	if len(wgData) != 2 {
		return fmt.Errorf("expected 2 wg interfaces but got: %v", len(wgData))
	}
	return nil
}

func logsvcTest(token string) error {
	fmt.Println("Running logsvc test")

	// create syslog_mnms.log file if not exist
	if _, err := os.Stat("syslog_mnms.log"); err != nil {
		fakeSyslog := "syslog_mnms.log"
		f, err := os.Create(fakeSyslog)
		if err != nil {
			return err
		}
		defer f.Close()
		defer os.Remove(fakeSyslog)
		for i := range 100 {
			fmt.Fprintf(f, "<134>%s mnms[1234]: This is a test log message %d\n", time.Now().Format("Jan 2 15:04:05"), i)
		}
		f.Sync()
	}

	commands := make([]CmdInfo, 0)
	commands = append(commands, CmdInfo{Client: "log", Command: "syslog config maxsize 500"})
	commands = append(commands, CmdInfo{Client: "log", Command: "syslog config compress true"})
	commands = append(commands, CmdInfo{Client: "log", Command: "syslog list"})
	commands = append(commands, CmdInfo{Client: "log", Command: "syslog export syslog_mnms.log 5"})
	ret, err := sendCommands(commands, token)
	if err != nil {
		return err
	}
	time.Sleep(10 * time.Second)

	// validate
	err = validateCommandsStatus(ret, token)
	if err != nil {
		return err
	}

	// check export result
	cmddata, err := queryCmdData(token)
	if err != nil {
		return err
	}

	if cmddata["syslog list"].Status == "ok" {
		type ListResult struct {
			Files      []string `json:"files"`
			ExportUrls []string `json:"export_urls,omitempty"`
		}
		var result ListResult
		err = json.Unmarshal([]byte(cmddata["syslog list"].Result), &result)
		if err != nil {
			return err
		}
		fmt.Println(result)
		if len(result.Files) == 0 {
			return fmt.Errorf("no syslog files found")
		}
		if !slices.Contains(result.Files, "syslog_mnms.log") {
			return fmt.Errorf("syslog_mnms.log not found in syslog files")
		}
	}

	if cmddata["syslog export syslog_mnms.log 5"].Status == "ok" {
		result := cmddata["syslog export syslog_mnms.log 5"].Result
		// should be a url can get content
		resp, err := GetWithToken(result, token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
	}

	return nil
}

func syslogTest(token string) error {
	fmt.Println("Running syslog test")
	resp, err := GetWithToken(regressionRootUrl+"/api/v1/syslog", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	return nil
}

/*
***Will restart nimbl, make sure this is the last test or understand what's going on***
 */
func authTest(token string) error {
	fmt.Println("Running auth test")
	commands := make([][]string, 0)
	commands = append(commands, []string{"util", "genpair", "-name", "newkey"})
	commands = append(commands, []string{"util", "export", "config", "-pubkey", "newkey.pub", "-out", "123"})
	commands = append(commands, []string{"util", "decrypt", "config", "-privkey", "newkey", "-in", "123", "-out", "456"})

	// run commands
	ctlCommand(commands)
	time.Sleep(10 * time.Second)

	// validate 123, 456 exist
	if _, err := os.Stat("123"); os.IsNotExist(err) {
		return err
	}
	if _, err := os.Stat("456"); os.IsNotExist(err) {
		return err
	}
	defer os.Remove("newkey")
	defer os.Remove("newkey.pub")
	defer os.Remove("123")
	defer os.Remove("456")

	// modify 456, which is a json file in UserConfig format
	// load 456, ex: {"users":[{"name":"admin","email":"","role":"admin","password":"default","enable2FA":false,"secret":""}]}
	var users MNMSConfig
	jsonBytes, err := os.ReadFile("456")
	if err != nil {
		return err
	}
	err = json.Unmarshal(jsonBytes, &users)
	if err != nil {
		return err
	}
	users.Users = append(users.Users, UserConfig{Name: "test", Email: "", Role: "user", Password: "default", Enable2FA: false, Secret: ""})
	jsonBytes, err = json.Marshal(users)
	if err != nil {
		return err
	}
	// overwrite 456
	err = os.WriteFile("456", jsonBytes, 0644)
	if err != nil {
		return err
	}

	// encrypt 456
	commands = make([][]string, 0)
	commands = append(commands, []string{"util", "encrypt", "config", "-pubkey", "newkey.pub", "-in", "456", "-out", "789"})
	ctlCommand(commands)

	if _, err := os.Stat("789"); os.IsNotExist(err) {
		return err
	}
	defer os.Remove("789")

	// run nimbl with newkey
	killNmsctlProcesses()
	time.Sleep(2 * time.Second)

	p, err := randomOpenPort()
	if err != nil {
		fmt.Println("Failed to get random open port")
		os.Exit(1)
	}
	cmd := exec.Command(rootsvcPath, "-n", "root", "-privkey", "newkey", "-p", fmt.Sprintf("%d", p))
	err = cmd.Start()
	if err != nil {
		fmt.Printf("Failed starting bbrootsvc: %s\n", err)
		os.Exit(1)
	}
	regressionRootUrl = "http://localhost:" + fmt.Sprintf("%d", p)
	time.Sleep(5 * time.Second)

	// update config
	commands = make([][]string, 0)
	commands = append(commands, []string{"util", "import", "config", "-in", "789", "-root", regressionRootUrl})
	ctlCommand(commands)

	// get users to see if test user is added
	resp, err := GetWithToken(regressionRootUrl+"/api/v1/users", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	var users2 []UserConfig
	err = json.NewDecoder(resp.Body).Decode(&users2)
	if err != nil {
		return err
	}
	found := false
	for _, user := range users2 {
		if user.Name == "test" {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("config not updated")
	}
	return nil
}

func putWithToken(url, token string, body io.Reader) (*http.Response, error) {
	bearer := "Bearer " + token
	req, err := http.NewRequest("PUT", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func deleteWithToken(url, token string, body io.Reader) (*http.Response, error) {
	bearer := "Bearer " + token
	req, err := http.NewRequest("DELETE", url, body)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Authorization", bearer)
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func userTest(token string) error {
	fmt.Println("Running user test")
	// add user : resp=$(curl -s -X POST -H "Authorization: Bearer $received_token" -d '{ "name": "abc1", "email": "<EMAIL>", "password": "Pas$Word1" , "role": "user"}' $URL/api/v1/users)
	url := regressionRootUrl + "/api/v1/users"
	body := `{"name": "abc1", "email": "<EMAIL>", "password": "Pas$Word1", "role": "user"}`
	resp, err := PostWithToken(url, token, strings.NewReader(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// get user list
	resp, err = GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// check if user is in the list
	var users []UserConfig
	err = json.NewDecoder(resp.Body).Decode(&users)
	if err != nil {
		return err
	}
	found := false
	for _, user := range users {
		if user.Name == "abc1" {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("user not found")
	}

	// update user, PUT /api/v1/users
	body = `{"name": "abc1", "email": "<EMAIL>", "password": "Pas$Word2", "role": "user"}`
	resp, err = putWithToken(url, token, strings.NewReader(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	// check if user is updated
	resp, err = GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	err = json.NewDecoder(resp.Body).Decode(&users)
	if err != nil {
		return err
	}
	found = false
	for _, user := range users {
		if user.Name == "abc1" && user.Email == "<EMAIL>" {
			found = true
			break
		}
	}
	if !found {
		return fmt.Errorf("user not found")
	}

	// delete user DELETE /api/v1/users
	body = `{"name": "abc1"}`
	resp, err = deleteWithToken(url, token, strings.NewReader(body))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	// check if user is deleted
	resp, err = GetWithToken(url, token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	err = json.NewDecoder(resp.Body).Decode(&users)
	if err != nil {
		return err
	}
	found = false
	for _, user := range users {
		if user.Name == "abc1" {
			found = true
			break
		}
	}
	if found {
		return fmt.Errorf("user not deleted")
	}
	return nil
}

func registerTest(token string) error {
	fmt.Println("Running register test")
	fakeClient := ClientInfo{
		Name:       "fake",
		NumDevices: 100,
		NumCmds:    100,
		Start:      int(time.Now().Unix()),
		Now:        int(time.Now().Unix()),
	}
	jsonBytes, err := json.Marshal(fakeClient)
	if err != nil {
		return err
	}
	resp, err := PostWithToken(regressionRootUrl+"/api/v1/register", token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("post register failed")
	}

	// check if the client is registered
	resp, err = GetWithToken(regressionRootUrl+"/api/v1/clients", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	clients := make(map[string]ClientInfo)
	err = json.NewDecoder(resp.Body).Decode(&clients)
	if err != nil {
		return err
	}
	found := false
	for name, _ := range clients {
		if name == "fake" {
			found = true
		}
	}
	if !found {
		return fmt.Errorf("client not found")
	}

	return nil
}

func clientsTest(token string) error {
	fmt.Println("Running clients test")
	// get clients
	resp, err := GetWithToken(regressionRootUrl+"/api/v1/clients", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	clients := make(map[string]ClientInfo)
	err = json.NewDecoder(resp.Body).Decode(&clients)
	if err != nil {
		return err
	}
	if len(clients) == 0 {
		return fmt.Errorf("no client found")
	}
	for name := range clients {
		fmt.Println("client: ", name)
	}

	// add client
	newClient := ClientInfo{
		Name:       "newclient",
		NumDevices: 100,
		NumCmds:    100,
	}
	clients["newclient"] = newClient
	jsonBytes, err := json.Marshal(clients)
	if err != nil {
		return err
	}
	resp, err = PostWithToken(regressionRootUrl+"/api/v1/clients", token, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// get clients
	resp, err = GetWithToken(regressionRootUrl+"/api/v1/clients", token)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	clients2 := make(map[string]ClientInfo)
	err = json.NewDecoder(resp.Body).Decode(&clients2)
	if err != nil {
		return err
	}
	if len(clients2) != len(clients) {
		return fmt.Errorf("expected %d clients but got: %v", len(clients), len(clients2))
	}

	return nil
}

func snmpTest(token string) error {
	fmt.Println("Running snmp test")

	// system oids
	sysDescr := ".*******.*******.0"
	sysContact := ".*******.*******.0"
	sysName := ".*******.*******.0"
	sysObject := ".*******.*******.0"

	// test basic snmp oids
	// if want to test full oids, use Parse_MIBs to get all oids in MIB directory

	devices, err := getDeviceList(token)
	if err != nil {
		return err
	}

	commands := make([]CmdInfo, 0)
	for _, dev := range devices {
		commands = append(commands, CmdInfo{Command: "snmp get " + dev.IPAddress + " " + sysDescr})
		commands = append(commands, CmdInfo{Command: "snmp get " + dev.IPAddress + " " + sysContact})
		commands = append(commands, CmdInfo{Command: "snmp get " + dev.IPAddress + " " + sysName})
		commands = append(commands, CmdInfo{Command: "snmp get " + dev.IPAddress + " " + sysObject})
	}
	// run commands
	ret, err := sendCommands(commands, token)
	if err != nil {
		return err
	}
	// about 40 devs * 4 oids = 160, wait for 2 minutes
	time.Sleep(2 * time.Minute)

	// validate
	err = validateCommandsStatus(ret, token)
	if err != nil {
		return err
	}
	return nil
}

func nestedTest(token string) error {
	// register nested NMS
	// {"bbnmssvc", nmssvcPath, []string{"-n", "nms/client1", "-r", fmt.Sprintf("http://localhost:%d", nmsPort), "-rs", ":5534", "-ss", ":5544", "-fake", "-p", fmt.Sprintf("%d", nms2Port), "-ir", "5"}},
	nms2Port, err := randomOpenPort()
	if err != nil {
		fmt.Println("Failed to get random open port")
		return err
	}
	regressionNMS2Url = "http://localhost:" + fmt.Sprintf("%d", nms2Port)
	cmd := exec.Command(nmssvcPath, "-n", "nms/client1", "-r", regressionNMS1Url, "-rs", ":5534", "-ss", ":5544", "-fake", "-p", fmt.Sprintf("%d", nms2Port), "-ir", "5")
	err = cmd.Start()
	if err != nil {
		fmt.Printf("Failed starting nms2: %s\n", err)
		return err
	}
	fmt.Println("NMS2 url: ", regressionNMS2Url)

	for i := range 3 {
		resp, err := GetWithToken(regressionRootUrl+"/api/v1/clients", token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		var clients map[string]ClientInfo
		err = json.NewDecoder(resp.Body).Decode(&clients)
		if err != nil {
			return err
		}
		if _, ok := clients["nms/client1"]; ok {
			break
		}

		if i == 2 {
			// dump all clients
			for name, client := range clients {
				fmt.Println(name, client)
			}
			return fmt.Errorf("nested NMS not found")
		}
		time.Sleep(10 * time.Second)
	}

	// post fake device to (AA-BB-CC-DD-EE-FF) FakeNMS2
	fakeDev := `
	{
		"AA-BB-CC-DD-EE-FF": {
			"mac": "AA-BB-CC-DD-EE-FF",
			"modelname": "Model-X",
			"timestamp": "1724830122",
			"scanproto": "agent",
			"ipaddress": "***********",
			"netmask": "*************",
			"gateway": "*************",
			"hostname": "",
			"kernel": "1",
			"ap": "123",
			"scannedby": "nms/client1"
		}
	}`
	resp, err := PostWithToken(regressionNMS2Url+"/api/v1/devices", token, strings.NewReader(fakeDev))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// get device list from root to see fake data (AA-BB-CC-DD-EE-FF) is added
	for i := range 3 {
		resp, err := GetWithToken(regressionRootUrl+"/api/v1/devices", token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		devices := make(map[string]DevInfo)
		err = json.NewDecoder(resp.Body).Decode(&devices)
		if err != nil {
			return err
		}

		if _, ok := devices["AA-BB-CC-DD-EE-FF"]; ok {
			break
		}

		if i == 2 {
			return fmt.Errorf("nested device not found")
		}
		time.Sleep(10 * time.Second)
	}

	// post fake port info and topology info to FakeNMS2
	url := regressionNMS2Url + "/api/v1/agent/ports"
	fakePortAndPowerInfo := `{
		"AA-BB-CC-DD-EE-FF": {
            "portStatus": [
                {
                    "portName": "Port 1",
                    "portStatus": true,
                    "speed": "1000",
                    "portMode": "copper",
                    "inOctets": "12345",
                    "inErrors": "0",
                    "inUcastPkts": "67890",
                    "inMulticastPkts": "1234",
                    "inBroadcastPkts": "5678",
                    "outOctets": "98765",
                    "outErrors": "0",
                    "outUcastPkts": "54321",
                    "outMulticastPkts": "4321",
                    "outBroadcastPkts": "8765",
                    "enableStatus": true
                },
                {
                    "portName": "Port 2",
                    "portStatus": false,
                    "speed": "100",
                    "portMode": "fiber",
                    "inOctets": "23456",
                    "inErrors": "1",
                    "inUcastPkts": "78901",
                    "inMulticastPkts": "2345",
                    "inBroadcastPkts": "6789",
                    "outOctets": "87654",
                    "outErrors": "1",
                    "outUcastPkts": "65432",
                    "outMulticastPkts": "5432",
                    "outBroadcastPkts": "9876",
                    "enableStatus": false
                }
            ],
            "powerStatus": [
                {
                    "powerId": "1",
                    "status": true
                },
                {
                    "powerId": "2",
                    "status": false
                }
            ]
        }
    }`
	resp, err = PostWithToken(url, token, strings.NewReader(fakePortAndPowerInfo))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	url = regressionNMS2Url + "/api/v1/topology"
	fakeTopologyInfo := `{
		"id": "AA-BB-CC-DD-EE-FF",
		"ipAddress": "***********",
		"modelname": "Model-X",
		"services": "agent",
		"lastUpdated": 1627847261,
		"linkData": []
	}`
	resp, err = PostWithToken(url, token, strings.NewReader(fakeTopologyInfo))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// get port info and topology info from root to see fake data is added
	for i := range 3 {
		resp, err := GetWithToken(regressionRootUrl+"/api/v1/agent/ports", token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		ports := make(map[string]PortAndPowerInfo)
		err = json.NewDecoder(resp.Body).Decode(&ports)
		if err != nil {
			return err
		}
		if _, ok := ports["AA-BB-CC-DD-EE-FF"]; ok {
			break
		}

		if i == 2 {
			return fmt.Errorf("port and power info not found")
		}
		time.Sleep(10 * time.Second)
	}

	for i := range 3 {
		resp, err := GetWithToken(regressionRootUrl+"/api/v1/topology", token)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		topology := make(map[string]Topology)
		err = json.NewDecoder(resp.Body).Decode(&topology)
		if err != nil {
			return err
		}
		fmt.Println(topology)
		if _, ok := topology["AA-BB-CC-DD-EE-FF"]; ok {
			break
		}

		if i == 2 {
			return fmt.Errorf("topology info not found")
		}
		time.Sleep(10 * time.Second)
	}

	// send command to nested client nms/client1
	commands := make([]CmdInfo, 0)
	commands = append(commands, CmdInfo{Client: "nms/client1", Command: "msg syslog send 0 1 Test test syslog"})
	ret, err := sendCommands(commands, token)
	if err != nil {
		return err
	}
	time.Sleep(20 * time.Second)
	// validate
	err = validateCommandsStatus(ret, token)
	if err != nil {
		return err
	}

	return nil
}

func TestNimBL(t *testing.T) {
	fmt.Println("Regression test ...")
	fmt.Println("This will restart nimbl, make sure this is the last test or understand what's going on and not running with time limit.")

	killNmsctlProcesses()
	defer killNmsctlProcesses()
	build()
	runProcess()

	//
	token, err := login()
	if err != nil {
		t.Fatal(err)
	}

	tests := []struct {
		name string
		fn   func(string) error
	}{
		{"clientsTest", clientsTest}, // client test should be the first test to make sure all services are running
		{"mqttTest", mqttTest},
		{"wgTest", wgTest},
		{"userTest", userTest},
		{"registerTest", registerTest},
		{"syslogTest", syslogTest},
		{"logsvcTest", logsvcTest},
		{"snmpTest", snmpTest},
		{"devTest", devTest},

		// syslog flow test can be added later
		// ssh tunnel test can be added later
		// firmware update test can be added later

		{"nestedTest", nestedTest},
		// authTest should be the last test due to nimbl restart with new key
		{"authTest", authTest},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := test.fn(token)
			if err == nil {
				passedItems = append(passedItems, test.name)
			} else {
				fmt.Println(err)
				failedItems = append(failedItems, test.name+": "+err.Error())
			}
		})
	}

	// write result to file with datetime
	filename := fmt.Sprintf("regression_%s.txt", time.Now().Format("20060102_150405"))
	file, err := os.Create(filename)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer file.Close()
	fmt.Fprintf(file, "Passed %d items\n", len(passedItems))
	fmt.Fprintf(file, "Failed %d items --------\n%s\n", len(failedItems), strings.Join(failedItems, "\n"))
	fmt.Println("Regression test done, please check the result in ", filename)

	if len(failedItems) > 0 {
		// t.Logf("Failed %d items --------\n%s\n", len(failedItems), strings.Join(failedItems, "\n"))
		t.Logf("Failed %d items --------\n", len(failedItems))
	} else {
		t.Log("All tests passed")
	}
}
