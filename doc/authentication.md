# NIMBL authentication guide

## Custom bbrootsvc private key

The `NIMBL`, is designed with security in mind and uses encryption to protect data. The system comes with an RSA private key and its corresponding public key for encryption and decryption. However, users have the option to configure their own private key when running `bbrootsvc`.

```bash
# run bbrootsvc with default private key
$ ./bbrootsvc -n root 
# run bbrootsvc with custom private key
$ ./bbrootsvc -n root  -privkey {private_key_file}
```

We suggest use custom private key since it is more secure.

Be aware that for any reason, if the user restart `bbrootsvc` with different private key, the `bbrootconfig.enc` file will not be correctly decrypted. Users who provide private key must keep it safe and use it consistently.

## Get bbrootsvc default public key
**Using the default public key is not secure. Users are encouraged to use custom private key instead.**

The use of the default public key is allowed by default to enable the initial use of the software for demo and testing purposes. Subsequent use of the software should be done using custom private keys.

If you didn't start `bbrootsvc` with custom private key, you can get the default public key by running the following command:

```shell
$ bbctl util export default-public-key -out {out_file}
```
You can specify the output file using the `-out` flag. If you run the `bbnimpubkey` flag without the `-out` flag, the output will be written to stdout. For example, you can obtain the `bbrootsvc` public key as follows:

```shell
$ bbctl util export default-public-key > ~/bbnimpub
```

## Default administrator password

The default administrator password is `default`. After logging in, you should change it.

```
account: admin
password: default
```

### Login

User can login with API or Web UI. bbnim will return a token after login. The token will be used for other API request.

POST /api/v1/login

```json
{
  "user": "admin",
  "password": "default"
}
```

## Change password

Should login with administrator permission first, then use the following API to change the password. Or you can use Web UI user management page to change the password also.

API need to set the `Authorization` header with the token returned by login API.

PUT /api/v1/users

```json
{
  "name": "admin",
  "password": "new_password",
  "email": "<EMAIL>",
  "role": "admin"
}
```

## Password rule

Over 8 characters, at least one uppercase letter, one lowercase letter, one number and one special character. Allowed special characters are `@$!%*#?&`

### How to Generate RSA key pair using bbctl util genpair

`bbctl` can help users to create an RSA key pair. RSA keys are mainly used to encrypt data exported by `NIMBL`. `NIMBL` does not want to export unencrypted data directly, so when exporting, it will require the user to provide a public key for encryption. Users can use the corresponding private key to decrypt the content.

**Example**: default output

Running bbctl util genpair without any flags will write a key pair to working directory: ./id_rsa and ./id_rsa.pub.

```shell
$ bbctl util genpair
```
**Example**: `-name` flag

To generate an RSA key pair with the name flag, use it as a prefix for the key pair file name. For example, if you use name `~/bbnimkey`, the private key will be stored in `~/bbnimkey`, and the public key will be stored in `~/bbnimkey.pub`.

```shell
$ bbctl util genpair -name ~/bbnimkey
```

### How to Encrypt data using bbctl util encrypt
The `bbctl` tool also provides an encryption function. Users can choose any RSA tool to encrypt data. The following sample describes how to encrypt a file with `bbctl`.
```shell
$ bbctl util encrypt -pubkey {pubkey_file} -in {input_file} -out {output_file}
```
If the -in or -out flag is not provided, stdin and stdout will be used instead.

### How to Decrypt data using bbctl util decrypt
```shell
$ bbctl util decrypt -privkey {private_key} -in {input_file} -out {output_file}
```
If the `-in` or `-out` flag is not provided, stdin and stdout will be used instead.

### How to export an encrypted version of bbrootconfig.enc
bbrootconfig.enc is the configuration file for NIMBL. You can export an encrypted version of this file using the command bbctl util export config -pubkey {pubkey_file}. The encryption is done using the public key that was set when running command.
```shell
$ bbctl util export config -pubkey ~/pubkey.pem  > ~/bbnimconfig
```
The above command will export the bbrootconfig.enc file to the ~/bbnimconfig file. The bbrootconfig.enc file is encrypted using the public key in the ~/.bbnim/pubkey.pem file. The private key in the ~/.bbnim/privkey file is used to decrypt the original bbrootconfig.enc file. Private key is same as the private key used to start the bbrootsvc. If bbrootsvc started without specify -privkey flag, the default private key will be used. In this case, you can omit the -privkey flag. -pubkey and -privkey do not need to be a pair. You can use any public key to encrypt the file, and use any private key to decrypt the output file.


### Update and import the `bbrootconfig.enc`
When a user has made changes to the `bbrootconfig.enc` file and wishes to update the Nimbl system, they must first encrypt the file using the public key. The default public key can be obtained using the command `bbctl util export default-public-key -out {filename}`. Or custom public key can be used. The following is an example of encrypting the modified `bbrootconfig.enc`

The use command `bbctl util import config -in {filename} -root {root_url}` to upload encrypted `bbrootconfig.enc` file

If `bbrootsvc` started without `-privkey` flag, you can omit the `-privkey` flag.


### Example usage of bbctl util encrypt and import
**Step 1**
Encrypt the modified `bbrootconfig.enc` file using the proper public key, the paired key corresponding to the private key used to start the `bbrootsvc`.
```shell
$ bbctl util encrypt -pubkey {pubkey_file} -in {modified_config} -out {encrypted_config}
```

**Step 2**
Upload the encrypted `bbrootconfig.enc` file to `bbrootsvc`
```shell
$ bbctl util import config -in {encrypted_config} -root {root_url}
```

# Two-Factor Authentication (2FA) Guide

## What is 2FA?

Two-Factor Authentication (2FA) is an additional layer of security that requires users to provide two forms of identification before accessing an account. The first form of identification is typically a password, and the second form of identification is usually a unique code that is generated by an app or sent to a user's smart phone.

## Why use 2FA?

2FA adds an extra layer of protection to user accounts and can prevent unauthorized access, even if a password is compromised. By requiring two forms of identification, 2FA helps to verify that a user is who they claim to be.

## Enabling 2FA

To enable 2FA for your account, follow these general steps:

1. Log in to your account and navigate to the user management settings.
2. Enable 2FA for your account.
3. A QR code will be displayed. Use an authenticator app, such as Google Authenticator or Authy, to scan the QR code and set up 2FA.
4. Verify that your 2FA is working by logging out and logging back in.

## Disabling 2FA

If you need to disable 2FA for your account, follow these steps:

1. Log in to your account and navigate to the user management settings.
2. Disable 2FA for your account.
3. Verify that 2FA has been disabled by logging out and logging back in.

Note: Disabling 2FA removes an important security feature from your account. Make sure to only disable 2FA if it's absolutely necessary and take other steps to secure your account.


## Conclusion

2FA is an important security feature that can help protect your accounts from unauthorized access. By following these best practices and enabling 2FA for your account, you can help ensure the security of your accounts and personal information.
