## <PERSON> and Cgo and mingw64 compiler on Windows

On Windows 64 bit machines you will need to install mingw64 gcc compilers
so that CGO can be used with your Go compiler.  CGO is used in agent support
code.

The mingw64 download is at https://winlibs.com/

Looking for a zip file that contains win64 gcc with POSIX threads without LLVM/Clang/LLD/LLDB.

For example:

https://github.com/brechtsanders/winlibs_mingw/releases/download/13.2.0posix-17.0.5-11.0.1-ucrt-r3/winlibs-x86_64-posix-seh-gcc-13.2.0-mingw-w64ucrt-11.0.1-r3.7z

Unzip this to C:\mingw64

And add C:\mingw64\bin to your PATH.

In PowerShell you can do so by,

```
$env:Path += ';C:\mingw64\bin'
```

You can put that in your $profile for powershell.

