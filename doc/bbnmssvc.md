# bbnmssvc
Network Service

Network service a distributed architecture service named `bbnmssvc`, the main goal is to scan the devices and send control commands to the devices.

## Quick Start
Step 1. Run root service
```shell
$ ./bbrootsvc -n root 
```

Step 2. Run network service
```shell
$ ./bbnmssvc -n nmssvc -r {root_service_url} -rs {log_service_ip} 
```
For example if you run the root service on the same machine:
```shell
$ ./bbnmssvc -n nmssvc -r https://localhost:27182 -rs localhost:5524
```

Step 3. Start UI
```shell
$ bbnimbl
```
You should be able to see the devices scanned by bbnmssvc.

## Example

We provide an example of running network service and send logs to log service, this example has root, network and log service on the same machine.

```shell
$ ./bbrootsvc -n root
$ ./bbnmssvc -n nmssvc -r http://localhost:27182 -rs localhost:5524
$ ./bblogsvc -n logsvc -r http://localhost:27182 -rs localhost:5514
```

The `-n` flag can specify service name.
The port `5524` stands for bblogsvc default syslog server port.
The `-rs` flag can specify where logs forward to. In this case we send network service logs to log service.


## Nested NMS Service Support

### Overview
The Nimbl NMS now supports nested NMS services, allowing for more complex network topologies. This enhancement enables multiple levels of NMS services to be defined, with each NMS capable of sending device information to and receiving commands from adjacent NMS services.

### Syslog Embedding
Additionally, the NMS now embeds a syslog feature, allowing child NMS services to send syslogs to their parent NMS services. This further enhances the monitoring and management capabilities of the network.

### Naming Rules for Nested NMS Service

In our current flat `ClientInfo` structure, the system does not enforce any hierarchy. Instead, it is the responsibility of the operator to assign unique names that indicate the intended relationships. Follow these guidelines when naming your NMS services:

1. **Unique Name Requirement**  
   Each NMS instance must have a unique name. This avoids conflicts and ambiguity in distributed network configurations.

2. **Manual Hierarchy Indication**  
Even though the structure is flat, you need to indicate a parent/child relationship by using a delimiter (for example, `/`) while registering to a parent NMS instance. For instance:  
- `NMS1` for a parent instance  
- `NMS1/NMS2` for a child of `NMS1`  
- `NMS1/NMS2/NMS3` for a further nested instance
  
If the naming does not follow the `ParentName/ChildName` rule, the registration will be rejected. Once registered, the service forwards its client information to its parent, ensuring that the root sees a complete network topology.   

The name provided through the command-line `-n` flag should accurately reflect its role in the network:
```shell
$ ./bbnmssvc -n "NMS1/NMS2" -r {parent_url} -rs {parent_syslog_address}
```

### Example
In the new system, you can define nested NMS services as follows:
- Root ↔ {(NMS1 ↔ NMS2 ↔ NMS3), (NMS4 ↔ NMS5), NMS6, ...}

In this example:
- NMS3 sends device information to NMS2 and receives commands from NMS2.
- NMS2 sends device information to NMS1 and receives commands from NMS1.

This configuration allows for arbitrarily complex network topologies, enhancing the flexibility and scalability of the Nimbl NMS.

For example, if there are two local networks, LAN1 and LAN2.
LAN1:
```shell
$ ./bbrootsvc -n root
$ ./bbnmssvc -n NMS1 -r http://localhost:27182 -rs :5514
```
LAN2:
```shell
$ ./bbnmssvc -n NMS1/NMS2 -r http://***********:27183 -rs ***********:5534
```
In this configuration:

- NMS1 is the parent instance.
- NMS1/NMS2 is a child of NMS1.
- Further nesting (e.g., NMS1/NMS2/NMS3) is supported similarly.