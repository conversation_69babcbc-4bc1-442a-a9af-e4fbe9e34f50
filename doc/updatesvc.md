# BBNIM Update service Guide

The `NIMBL` support users to manually run commands to update the service.

# Commands

## Update service 
The `NIMBL` provides command to update service and stop service.
```shell
# For root service
-ck root service update
# For other service
-cc [service name] service update
```
When using this command, remember to add `-cc` or `-ck`.

Example:
```shell
# For root service
$ ./bbctl -ck root service update
# For other service
$ ./bbctl -cc nms1 service update
```

## Stop service 
The `NIMBL` provides command to stop service.
```shell
# For root service
-ck root service stop
# For other service
-cc [service name] service stop
```
When using this command, remember to add `-cc` or `-ck`.

Example:
```shell
# For root service
$ ./bbctl -ck root service stop
# For other service
$ ./bbctl -cc nms1 service stop
```

# Test method

Since the release file on https://nms.blackbeartechhive.com does not include automatic updates, you may need to add some hardcode for testing.

## Test `bbrootsvc` : 

1. Run commands.
```shell
$ ./bbrootsvc/bbrootsvc.exe -n root -debuglog
```

2. If the version of `bbrootsvc`(v1.0.8) is lower than last version(v1.0.9 from https://nms.blackbeartechhive.com), we will get syslog.
```shell
<1>Jan 24 10:04:18 root AutoUpdateService: The root service root have new version, please update root service.
``` 

3. Run command
```shell
$  ./bbctl/bbctl.exe -ck root service update
```

4. Check folder and check new `bbrootsvc` version.
```shell
$ ls bbrootsvc
Makefile  bbnim_windows_amd64_v1.0.9/  bbrootsvc.exe*  bbrootsvc.old.exe*  main.go

$ ./bbrootsvc/bbrootsvc.old.exe -version
License:
......
Build information:
go1.22.0 {mnms (devel)  <nil>} [{-buildmode exe} {-compiler gc} {CGO_ENABLED 1} {CGO_CFLAGS } {CGO_CPPFLAGS } {CGO_CXXFLAGS } {CGO_LDFLAGS } {GOARCH amd64} {GOOS windows} {GOAMD64 v1} {vcs git} {vcs.revision 3d9ed2e5907b8176e5353e0e8a1f49576e7f21a8} {vcs.time 2024-12-18T02:50:00Z} {vcs.modified false}]

$ ./bbrootsvc/bbrootsvc.exe -version
License:
......
Build information:
go1.22.0 {   <nil>} [{-buildmode exe} {-compiler gc} {-ldflags -X main.Version=v1.0.9 -s -w} {CGO_ENABLED 1} {CGO_CFLAGS -I ../mnms_installation/bbnim_idps/windows/include/hs} {CGO_CPPFLAGS } {CGO_CXXFLAGS } {CGO_LDFLAGS -L ../mnms_installation/bbnim_idps/windows} {GOARCH amd64} {GOOS windows} {GOAMD64 v1}]
```

## Test other sevice : 
The `bbnmssvc` of example
1. Pretend to have run the above chapters.
Hardcode bbrootsvc version. In `bbrootsvc/main.go` line 90:
```golang
	// version
	if Version != "" {
		mnms.QC.Version = Version
	}
	mnms.QC.Version = "v1.0.9" // same with last version(v1.0.9 from https://nms.blackbeartechhive.com)
```
2. Run make.
3. Run commands.
```shell
$ ./bbrootsvc/bbrootsvc.exe -n root -debuglog

$ ./bbnmssvc/bbnmssvc.exe -n danielNms -r http://localhost:27182 -rs localhost:5514 -debuglog
```

4. If the version of `bbnmssvc`(v1.0.8) is lower than the version of `bbrootsvc`(v1.0.9), we will get syslog.
```shell
<1>Jan 22 13:49:47 danielNms AutoUpdateService: The network service danielNms have new version, please update network service.
``` 

5. Run command
```shell
$  ./bbctl/bbctl.exe -cc danielNms service update
```

6. Check folder and check new `bbrootsvc` version.
```shell
$ ls bbnmssvc
Makefile  bbnim_windows_amd64_v1.0.9/  bbnmssvc.exe*  bbnmssvc.old.exe*  main.go

$ ./bbnmssvc/bbnmssvc.exe -version
BlackBear NIMBL Version: v1.0.9
Build information:
go1.22.0 {   <nil>} [{-buildmode exe} {-compiler gc} {-ldflags -X main.Version=v1.0.9 -s -w} {CGO_ENABLED 1} {CGO_CFLAGS -I ../mnms_installation/bbnim_idps/windows/include/hs} {CGO_CPPFLAGS } {CGO_CXXFLAGS } {CGO_LDFLAGS -L ../mnms_installation/bbnim_idps/windows} {GOARCH amd64} {GOOS windows} {GOAMD64 v1}]

$ ./bbnmssvc/bbnmssvc.old.exe -version
BlackBear NIMBL Version: Development version
Build information:
go1.22.0 {mnms (devel)  <nil>} [{-buildmode exe} {-compiler gc} {CGO_ENABLED 1} {CGO_CFLAGS } {CGO_CPPFLAGS } {CGO_CXXFLAGS } {CGO_LDFLAGS } {GOARCH amd64} {GOOS windows} {GOAMD64 v1} {vcs git} {vcs.revision 5b4955b4382efe3af0a1906719eb807a7ea0b728} {vcs.time 2025-01-28T03:38:33Z} {vcs.modified true}]
```
