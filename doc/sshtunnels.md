# BBNIM SSH Tunnel Guide

`bbrootsvc` provides an SSH tunnel service using SSH reverse tunneling. 

The goal is to establish tunnels between the root and devices, where the root serves SSH server, and the devices act as clients using ssh or dropbear client.

Be sure to allow the ports through firewall on the root machine.

Once the tunnel is established, you can access the specified node.

## Quick Start
1. Build the agent (v1.0.6 or later) into your devices.
2. Run `bbrootsvc` as root or administrator.
3. Follow the commands below to establish an SSH tunnel. [Start Tunneling](#start-tunneling)


## Setting Up
### Requirements
- Devices
    - Agent built-in (v1.0.6 or later)
    - `ssh` or `dbclient` built-in
    - Must have network connectivity to the root machine
- Run `bbrootsvc` as root or administrator and make sure SSH feature is enabled
    - `nosshsrv`: false by default
    - `sp`: 6422 by default
- Make sure to allow the ports through the firewall


## Using SSH Tunnels
### Commands

- ssh tunnel close [port]: Close an SSH tunnel connection with the specified port ssh server is listening on.
- ssh tunnel fetch [port]: Send an HTTP request to the SSH tunnel.
- ssh tunnels list : List all SSH tunnels.

- agent ssh reverse start [mac] [server hostname] [listen port] [remote port] [ssh server port]: Tell the device to start SSH reversing where `server hostname` is ssh server, `listen port` is the port on the server for port forwarding, `remote port` is the port on the client for port forwarding.
- agent ssh reverse stop [mac] [server hostname] [listen port]: Tell the device to stop the specific reversing.
- agent ssh reverse websrv [mac]: Tell the device to reverse a tunnel between root machine and its web server.
- agent ssh reverse status [mac]: Get the latest status after running start/start/websrv commands.


Since ssh tunnel reversing are between root and device agents, ssh commands should always have `-ck root` which instructs root to run the commands.
Once a device successfully reverse from root to its web server, the agent will fill `TunneledUrl` in device info.


#### Start Tunneling

##### One Click Start
Start tunneling to the device with one click:

```shell
# one click start
bbctl agent ssh reverse websrv 11-22-33-44-55-66
```

* Agent will search information to run SSH automatically. Here is a breakdown
    * Agent sends an http request to `nmssvc`.
    * Then `nmssvc` will forward this request to `rootsvc`.
    * Agent receives json data and parse the content.
* Once the device's SSH reverse tunnel is successful, the agent will also fill the accessible endpoint  in the device information when updating.
* This command will also try to allow the port by adding the rules with ufw or netsh.
* Alternatively, users can click the "+" button on the WebUI in the device page after setting the port and base domain.

##### Full Functionality

```shell
# Start the SSH reverse tunnel.
bbctl agent ssh reverse start 11-22-33-44-55-66 *********** 12345 443 22
```

* ***********: suppose to be the root hostname
* 12345: should be a free port on the root machine
* 443: should be the device's web server port
* 22: root ssh server port


#### Close Tunnel

Issue command to root to close the tunnel
```shell
bbctl -ck root ssh tunnel close 12345
```

Or issue command to agent
```shell
bbctl agent ssh reverse stop 11-22-33-44-55-66 *********** 12345
```

Either way can close the ssh connection.


## API

- POST /api/v1/sh/tunnels

Device send register tunnel request to auto start tunneling, the request body is a json object with the following format:
request:
```json
{
    "mac": "mac address",
    "nms_url": "nms url",
}
```
response:
```json
{
    "status": "ok",
    "root_host": "hostname",
    "ssh_port": 22,
    "listen_port": 34567,
    "message": ""
}
```
or
```json
{
    "status": "error",
    "message": "base domain is not set"
}
```
```json
{
    "status": "error",
    "message": "tunnel for this device already exists"
}
```
```json
{
    "status": "ok",
    "root_host": "hostname",
    "ssh_port": 22,
    "listen_port": 34567,
    "message": "failed to allow port automatically, make sure the port is allowed"
}
```

- GET /api/v1/ssh/tunnels

retrieves ssh tunnel data from root machine

params:
    domain: domain name

response example:
```json
{
    "12345":{
        "dev_mac": "11-22-33-44-55-66",
        "remote_addr": "*************:53013",
        "listen_port": 12345,
    }
}
```

## Testing with testbed and CWR5805

### Requirement

* One CWR5805 with the latest agent


### Steps

Following steps can set up testing environment quickly:

1. Log into the testbed.
2. Pull the latest mnms code (v1.0.7 or later) and build it.
3. Run `rootsvc` on the testbed.
4. Run `nmssvc` locally and register it with the `rootsvc` on the testbed.
5. Boot `cwr5805` and connect it to the local `nmssvc`. If you enable debug log, you should see the agent connect to the NMS:
You shall see agent connect to the nms if enable debug log
```
agentclient-app_1cpu_32mb-1  | [getClientURL : 1250] success
agentclient-app_1cpu_32mb-1  | [runAgentClient : 1296] iface = eth0
agentclient-app_1cpu_32mb-1  | [runAgentClient : 1333] 11-22-33-44-55-66
agentclient-app_1cpu_32mb-1  | [runAgentClient : 1377] Success to get client URL
```
6. Return to the testbed.
7. Run `bbctl agent ssh reverse websrv 11-22-33-44-55-66`. Make sure to replace 11-22-33-44-55-66 with the correct MAC address.
8. If you can't access the web server, check the command result for any error messages.


## Trouble Shooting

### How to know the tunnel is established?

Ther are few ways can confirm
- Check tunnels data: Use command `ssh tunnels list` or api `GET /api/v1/ssh/tunnels` 
- Check client response: `ssh tunnel fetch [port]`


### I don't have CWR5805 or other device at hand, how should I test it?

I also setup a simulator in container to test with. Following steps can start it:
1. Run rootsvc at testbed
2. Run nmssvc locally or you can also run at testbed
3. Copy agentclient code from github
4. Go to agentclient root folder
5. Go to test/blackbearresp, make sure the "nms_service" point to the right place
6. Go back to agentclient root folder
7. Run command `docker compose up app_1cpu_32mb`
8. This container will start and connect to nmssvc
9. Run `bbctl agent ssh reverse websrv 11-22-33-44-55-66`
10. Access `http://***************:12345`, make sure the root ip and listen port is right.

