
# Nimbl support for ollama
This document explains how to use ollama as a local large language model for Nimbl. ollama is an open-source, locally installed natural language processing (NLP) model that can be used with various platforms. It's designed to work offline, without the need for an internet connection. ollama is based on the GPT-3 architecture and can be used for a wide range of NLP tasks, including text generation, question answering, and more.

## Install ollama
1. Download from official website: [ollama](https://ollama.com/download) we tested windows preview version.
2. Download CUDA and install afrom NVIDIA official website: [CUDA](https://developer.nvidia.com/cuda-downloads)

## Run ollama
In the terminal, first to check the ollama was installed successfully:  
```shell
❯ ollama --version
ollama version is 0.1.32
```

Run with specific model, we use the `llama3-7B` model:  
```shell
❯ ollama run llama3
```

You may see ollama start to download the model, if you are first time to run the model.

After start the model, you can see the following prompt:
```shell
>>>
```

Try to input some text, and see the response:
```shell
>>> hello
Hello! It's nice to meet you. Is there something I can help you with, or would you like to chat?
```

Check ollama server is running:
```shell
curl http://localhost:11434/api/generate -d '{
  "model": "llama3",
  "prompt": "Why is the sky blue?"
}'
```

## Configurate Nimbl to use ollama
1. Setup the ollama server
```shell
> bbctl -cc [client_name] anomaly config llm ollama {host} {port} {model}
# example, ollama in same machine use default port and llama3, bbanomsvc start with name an1
> bbctl -cc an1 anomaly config llm ollama localhost 11434 llama3
```
2. Switch to ollama !!! WARNING !!! Switch LLM server will clear all knowledge, we recommand to backup the knowledge before switch.
```shell
# backup the knowledge
> bbctl -cc [client_name] anomaly knowledge export [file_url] [filename]
# switch to ollama
> bbctl -cc [client_name] anomaly config llm active ollama
```


## Test system
We have tested ollama with llama3-7B, here is the testing machine:

### System
- Windows 10 Home Edition 64-bit
- CPU: 11th Gen Intel Core i7-11700K @ 3.60GHz (16 CPUs), ~3.6GHz
- Memory: 81918MB RAM
  
### Graphics card
- NVIDIA GeForce RTX 3060 Ti
- Memory: 8GB

