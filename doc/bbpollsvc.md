# BlackBear Polling Service (bbpollsvc)

## Overview

The BlackBear Polling Service (`bbpollsvc`) is a network monitoring service that performs continuous device polling, connectivity checks, and LLDP topology discovery. It operates as a distributed component of the NIMBL network management system, registering with a root MNMS server and providing real-time device state monitoring and network topology mapping.

## Architecture

### Core Components

1. **NMS Validation System** - Validates target NMS service exists, is active, and of correct type
2. **Device Polling Engine** - Monitors device connectivity via SNMP and ping
3. **LLDP Topology Discovery** - Discovers network topology using LLDP protocol
4. **State Tracking System** - Tracks device online/offline states with change detection
5. **Syslog Notification System** - Sends real-time alerts for device state changes
6. **Service Registration** - Registers with root MNMS server for distributed operation

### Service Flow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Root MNMS     │◄──►│   bbpollsvc      │◄──►│  Network        │
│   Server        │    │   (Polling)      │    │  Devices        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                       │
        ▼                        ▼                       ▼
   Device List              NMS Validation          SNMP/LLDP
   Service Registry         State Tracking          Connectivity
   Topology Data            Syslog Alerts
```

### Startup Validation Process

```
1. Service Start
2. NMS Validation ──► Validate NMS exists
                  ──► Check kind = "nms"
                  ──► Verify status = "active"
3. Validation Pass ──► Continue with polling
4. Validation Fail ──► Exit service (code 1)
```

## Installation & Usage

### Command Line Options

```bash
./bbpollsvc [OPTIONS]

Required Parameters:
  -n <name>           Service name (required)
  -r <root_url>       Root MNMS server URL (required)
  -nsn <nms_name>     NIMBL network service name for polling (required)
  -rs <address>       Remote syslog server address (required)

Optional Parameters:
  -p <port>           Service port (default: 27189)
  -pi <seconds>       Polling interval in seconds (default: 10)
  -ir <seconds>       Registration interval (default: 60)
  -ss <address>       Local syslog server address (default: :5554)
  -O <output>         Debug log output (default: stderr)
  -P <pattern>        Debug log pattern string
  -debuglog           Enable debug logging
  -ds                 Dump stack trace on exit
  -version            Print version information

Daemon Control:
  -daemon install     Install as system service
  -daemon start       Start service
  -daemon stop        Stop service
  -daemon remove      Remove service
```

### Example Usage

```bash
# Start polling service
./bbpollsvc -n "polling-node-1" \
           -r "https://root.example.com:27182" \
           -nsn "nms-east" \
           -rs "syslog.example.com:514" \
           -pi 15

# Install as Windows service
./bbpollsvc -daemon install -n "polling-svc" -r "https://root:27182" -nsn "nms1" -rs "syslog:514"

# Enable debug logging
./bbpollsvc -debuglog -n "debug-poller" -r "https://root:27182" -nsn "nms1" -rs "syslog:514"
```

## Core Functionality

### 1. NMS Validation System

The service performs mandatory validation of the target NMS service before starting polling operations:

#### Startup Validation Process

1. **Service Lookup** - Queries `/api/v1/clients` endpoint on root server
2. **Existence Check** - Verifies the specified NMS name exists in registered services
3. **Kind Validation** - Ensures the service kind is "nms" (not "polling", "agent", etc.)
4. **Status Verification** - Confirms the service status is "active"
5. **Exit on Failure** - Service exits with code 1 if any validation fails

#### Validation Scenarios

- **✅ Valid NMS**: Service continues with polling operations
- **❌ Missing NMS**: `NMS service 'nms-name' not found in registered services`
- **❌ Wrong Kind**: `Service 'name' exists but kind is 'polling' (expected 'nms')`
- **❌ Inactive**: `NMS service 'name' exists but status is 'inactive' (expected 'active')`

#### Continuous Monitoring

After startup validation, the service continues to validate the NMS every `RegisterInterval` seconds:

- **Valid State**: Continues normal operation
- **Invalid State**: Logs error and exits service immediately

### 2. Device Connectivity Monitoring

The service continuously monitors device connectivity using a multi-tier approach:

#### Connectivity Check Process

1. **SNMP Check** - Primary method using sysDescr OID (*******.*******.0)
2. **Ping Fallback** - Secondary method if SNMP fails
3. **State Tracking** - Maintains device online/offline states
4. **Change Detection** - Identifies state transitions for notifications

#### Device Identification

- **MAC Address** - Primary device identifier from MNMS
- **Chassis ID** - Secondary identifier from LLDP (for devices with different MAC/chassis)
- **IP Mapping** - Maintains IP-to-identifier relationships

### 3. LLDP Topology Discovery

Automated network topology discovery using Link Layer Discovery Protocol:

#### Discovery Process

1. **Device Filtering** - Only processes non-agent, online devices
2. **Local Device Info** - Retrieves chassis ID and model information
3. **Interface Mapping** - Maps LLDP port indexes to interface names
4. **Neighbor Discovery** - Identifies connected devices and ports
5. **Topology Publishing** - Sends discovered topology to root server

#### SNMP OIDs Used

- `1.0.8802.*******.3.2.0` - Local chassis ID
- `1.0.8802.*******.*******` - Local port descriptions
- `1.0.8802.*******.*******` - Remote chassis IDs
- `1.0.8802.*******.*******` - Remote port IDs
- `*******.*******.1.2` - Interface descriptions

#### Interface Name Normalization

Converts Cisco short-form interface names to full names:

- `Fa0/1` → `FastEthernet0/1`
- `Gi0/1` → `GigabitEthernet0/1`
- `Te0/1` → `TenGigabitEthernet0/1`
- `Port1` → `Port-1`

### 4. State Tracking & Notifications

#### State Management

- **Device States** - Tracks online/offline status for each device
- **IP Mappings** - Maintains relationships between IP, MAC, and chassis ID
- **Change Detection** - Identifies first discoveries and state transitions
- **Cleanup Process** - Removes stale device entries

#### Notification System

- **Syslog Alerts** - Real-time notifications via syslog protocol
- **First Discovery** - Alerts when devices are first discovered
- **State Changes** - Alerts when devices go online/offline
- **Batch Notifications** - Groups multiple device changes

#### Syslog Message Format

```
Devices online: MAC1,MAC2,MAC3
Devices offline: MAC4,MAC5
```

## Configuration

### Environment Requirements

- **Go Runtime** - Go 1.19+ for compilation
- **Network Access** - SNMP (UDP 161) and HTTP/HTTPS to root server
- **SNMP Community** - Read access to target devices (default: "public")
- **Syslog Server** - Remote syslog server for notifications

### Service Registration

The service automatically registers with the root MNMS server providing:

- Service name and type ("polling")
- Device count and status
- Service health metrics
- Registration renewal (default: 60 seconds)

### Device Filtering

Devices are filtered based on:

- **NMS Assignment** - Only devices assigned to specified NMS name
- **Agent Status** - Excludes devices managed by agents
- **IP Address** - Requires valid IP address for polling
- **Online Status** - LLDP discovery only for online devices

## Monitoring & Troubleshooting

### Debug Logging

Enable comprehensive logging with `-debuglog` flag:

```bash
./bbpollsvc -debuglog -n "debug-poller" -r "https://root:27182" -nsn "nms1" -rs "syslog:514"
```

### Log Categories

- **NMS Validation** - Target NMS service validation results
- **Device Polling** - Connectivity check results
- **LLDP Discovery** - Topology discovery process
- **State Changes** - Device state transitions
- **Service Registration** - Root server communication
- **Error Handling** - SNMP failures and network issues

### Common Issues

#### NMS Validation Failures

- **Missing NMS Service**: Verify the NMS name is correctly registered with root server
- **Wrong Service Kind**: Ensure the registered service is of type "nms", not "polling" or "agent"
- **Inactive NMS**: Check that the target NMS service is running and registered as "active"
- **Authentication Issues**: Verify admin token has access to `/api/v1/clients` endpoint
- **Network Connectivity**: Ensure polling service can reach the root server

#### SNMP Connectivity

- Verify SNMP community string (default: "public")
- Check firewall rules for UDP port 161
- Ensure SNMP service is enabled on target devices

#### LLDP Discovery

- Verify LLDP is enabled on network devices
- Check LLDP neighbor relationships
- Ensure proper SNMP access to LLDP MIB

#### Service Registration

- Verify root server URL and accessibility
- Check admin token authentication
- Monitor registration interval logs

### Performance Metrics

- **Polling Interval** - Configurable (default: 10 seconds)
- **Device Count** - Logged in state tracker statistics
- **Discovery Success Rate** - LLDP topology discovery results
- **State Change Frequency** - Device transition rates

## API Integration

### Root Server Endpoints

- `GET /api/v1/devices` - Retrieve device list
- `POST /api/v1/topology` - Publish topology data
- `POST /api/v1/register` - Service registration

### Data Structures

#### Device Information

```json
{
  "mac": "AA-BB-CC-DD-EE-FF",
  "ipAddress": "*************",
  "scannedBy": "nms-east",
  "scanproto": "snmp"
}
```

#### Topology Data

```json
{
  "id": "AA-BB-CC-DD-EE-FF",
  "ipAddress": "*************",
  "modelname": "Cisco Catalyst 2960",
  "services": "nms-east",
  "topoType": "snmp",
  "lastUpdated": 1640995200,
  "linkData": [
    {
      "source": "AA-BB-CC-DD-EE-FF",
      "target": "11-22-33-44-55-66",
      "sourcePort": "FastEthernet0/1",
      "targetPort": "GigabitEthernet0/24",
      "edge": "AA-BB-CC-DD-EE-FF_11-22-33-44-55-66",
      "blockedPort": "false",
      "linkType": "snmp"
    }
  ]
}
```

## Security Considerations

### Authentication

- **Admin Token** - Required for root server API access
- **SNMP Community** - Read-only access recommended
- **Service Registration** - Authenticated communication with root server

### Network Security

- **Firewall Rules** - Allow SNMP (UDP 161) and HTTP/HTTPS outbound
- **Access Control** - Restrict SNMP community access
- **Encryption** - Use HTTPS for root server communication

### Data Privacy

- **Device Information** - MAC addresses and IP addresses are logged
- **Topology Data** - Network structure information is transmitted
- **Syslog Messages** - Device state information sent to remote server

## Version Information

Use `-version` flag to display version and build information:

```bash
./bbpollsvc -version
```

Output includes:

- BlackBear NIMBL version
- Go version used for compilation
- Build information and settings

---

_This documentation covers bbpollsvc version 1.0.4 and later. For older versions, refer to legacy documentation._
