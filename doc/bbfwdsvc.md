# bbfwdsvc

Distributed Syslog Alert Forwarder Service

`bbfwdsvc` is a distributed service in the MNMS ecosystem designed to receive syslog messages, process and filter them, and forward critical alerts to external platforms such as WhatsApp, Telegram, and MQTT. It also registers itself to the root service and supports remote command execution, similar to other bb services.

## Overview

- **Syslog Server**: Listens for incoming syslog messages (UDP), the same as bbrootsvc and bblogsvc
- **Alert Forwarding**: Forwards filtered alerts to WhatsApp, Telegram, MQTT, etc., based on configurable rules
- **Distributed Registration**: Registers to the root service for management and command execution
- **Configurable**: Uses a JSON configuration file for flexible alerting and forwarding rules

## Architecture

1. **Syslog Listener**: Starts a syslog server on a configurable port, receives logs from network devices and other services
2. **Log Processing**: Parses incoming logs, applies severity, keyword, and rate-limiting filters
3. **Alert Forwarding**: Sends matching alerts to configured platforms (WhatsA<PERSON>, Telegram, MQTT)
4. **Service Registration**: Registers to the root service for health monitoring and command dispatch
5. **Command Handling**: Supports remote commands for configuration and control
6. **Configuration Upload**: Periodically posts its current configuration to the root service for UI display and management

## Features

- Real-time syslog monitoring (UDP)
- Multi-platform alert forwarding (WhatsApp, Telegram, MQTT)
- Severity and keyword filtering
- Rate limiting per platform
- Configurable via remote commands
- Service registration and command execution via root service
- Backup and compression options for syslog files if need

## Quick Start

### 1. Run the Root Service
```bash
$ bbrootsvc -n root
```

### 2. Run bbfwdsvc
```bash
$ bbfwdsvc -n forwarder -r http://localhost:27182 -ss :5544
```

- `-n` : Service name
- `-r` : Root service URL
- `-ss`: bbfwdsvc's syslog server address, default to `:5544`

### 3. Configure Forwarding

You can configure forwarding rules for bbfwdsvc in two main ways:

#### A. Using a JSON Configuration File (at startup, Optional)

Create a config file (see `syslog_forwarder.json` for a full example):
```json
{
  "whatsapp": {
    "enabled": true,
    "account_sid": "...",
    "auth_token": "...",
    "from_number": "+*********0",
    "to_numbers": ["+**********"],
    "alert_config": { ... }
  },
  "telegram": {
    "enabled": true,
    "bot_token": "...",
    "chat_ids": ["*********"],
    "alert_config": { ... }
  },
  "mqtt": {
    "enabled": false,
    "broker_host": "localhost",
    "broker_port": 1883,
    "topic": "mnms/alerts",
    "alert_config": { ... }
  }
}
```

Start the service with your config file:
```bash
$ bbfwdsvc -n forwarder -r http://localhost:27182 -fc syslog_forwarder.json
```

#### B. Changing Forwarding Rules at Runtime (Remote Commands)

You can update the forwarding configuration while the service is running, without restarting, using remote commands. These can be issued from the root service UI (Script page) or via the `bbctl` CLI.

**1. Import entire configuration from URL**
- **UI Script page** (with service name `-cc`):
  ```
  forward import http://yourserver/config.json
  ```
- **CLI (`bbctl`)**:
  ```bash
  bbctl -cc forwardsvc1 forward import http://yourserver/config.json
  ```


**2. Set specific configuration via flags**
- **UI Script page**:
  ```
  forward config -whatsapp=true -whatsapp_to_numbers=+*********0,+********** -telegram=true -telegram_chat_ids=12345,67890 -mqtt=true -mqtt_broker_host=broker.example.com -mqtt_topic=alerts
  ```
- **CLI (`bbctl`)**:
  ```bash
  bbctl -cc forwardsvc1 forward config -whatsapp=true -whatsapp_to_numbers=+*********0,+********** -telegram=true -telegram_chat_ids=12345,67890 -mqtt=true -mqtt_broker_host=broker.example.com -mqtt_topic=alerts
  ```

**3. Set entire configuration via raw JSON**
- **UI Script page**:
  ```
  forward raw-json config {\"whatsapp\":{...},\"telegram\":{...},\"mqtt\":{...}}
  ```
- **CLI (`bbctl`)**:
  ```bash
  bbctl -cc forwardsvc1 forward raw-json config {\"whatsapp\":{...},\"telegram\":{...},\"mqtt\":{...}}
  ```
Make sure to use single quotes for the entire string.

These commands allow you to update alerting rules, credentials, and forwarding targets without restarting the service.

See the full config file and command API documentation for all available options and flags.

#### C. Changing Forwarding Rules at Runtime with UI

Alternatively, after starting `bbrootsvc`, you can access the root service address in your browser. Log in and navigate to the **Cluster Info** page from the left menu.

You will see all registered services in the Cluster Info table. For any forward service, click the **EDIT** button in the "Action" column at the end of the row.

A forwarder panel will appear, allowing you to edit the configuration and save changes. These changes are applied immediately—no service restart required.

### 4. Forward Syslogs to bbfwdsvc
Depends on the use case, we can have a flow like `bbnmssvc` -> `bbfwdsvc(:5544)` -> `bbrootsvc(:5514)`
```bash
$ bbfwdsvc -n forwarder -r http://localhost:27182 -ss :5544 -rs :5514
$ bbnmsvc -n nms -r http://localhost:27182 -rs :5544
```

Or configure the device syslog settings either by UI, command or device web server. 
We can have a flow like `device` -> `bbnmssvc(bbnmssvc_ipaddr:5534)` -> `bbfwdsvc(:5544)` -> `bbrootsvc(:5514)` to forward the logs.


### Example Usage

#### Forward logs from a service to bbfwdsvc
- Configure your device/service to send syslog to the bbfwdsvc server address (default UDP port: 5544)

#### Forwarding Alerts
- Alerts matching your configuration will be forwarded to WhatsApp, Telegram, or MQTT

#### Registering and Command Handling
- bbfwdsvc will register itself to the root service and periodically check for commands
- You can issue commands from the root service to update configuration or control the forwarder

## Configuration Reference

See `syslog_forwarder.json` for a full example. Key sections:
- `whatsapp`: Twilio credentials, phone numbers, alert rules
- `telegram`: Bot token, chat IDs, alert rules
- `mqtt`: Broker settings, topic, alert rules
- `alert_config`: Severity, rate limiting, keywords, exclusions

### WhatsApp Configuration (via Twilio)

```json
{
  "whatsapp": {
    "enabled": true,
    "account_sid": "YOUR_TWILIO_ACCOUNT_SID",
    "auth_token": "YOUR_TWILIO_AUTH_TOKEN", 
    "from_number": "+***********",
    "to_numbers": ["+*********0"],
    "alert_config": {
      "min_severity": 0,
      "max_severity": 3,
      "rate_limit_seconds": 300,
      "max_alerts_per_minute": 5,
      "keywords": ["error", "fail", "critical"],
      "exclude_keywords": ["test", "debug"]
    }
  }
}
```

#### Setting up Twilio WhatsApp:
1. Create a Twilio account at https://www.twilio.com
2. Get your Account SID and Auth Token from the console
3. Enable WhatsApp sandbox or get approved WhatsApp number
4. Use the sandbox number as `from_number`
5. Send "join &lt;your-sandbox-name&gt;" to the sandbox number from recipient phones

### Telegram Configuration

```json
{
  "telegram": {
    "enabled": true,
    "bot_token": "YOUR_BOT_TOKEN",
    "chat_ids": ["@your_channel", "*********"],
    "alert_config": {
      "min_severity": 0,
      "max_severity": 4,
      "rate_limit_seconds": 180,
      "max_alerts_per_minute": 10,
      "keywords": ["error", "fail", "warning"],
      "exclude_keywords": ["test"]
    }
  }
}
```

#### Setting up Telegram Bot:

**Step 1: Create the Bot**
1. Message @BotFather on Telegram
2. Send `/newbot` command
3. Choose a name and username for your bot
4. Copy the bot token (like `**********:AAFVh90KZ0MpbQg5xaONGOvej9E1V1KsSMY`)

**Step 2: Get Your Chat ID (for personal messages)**
1. Message @userinfobot on Telegram
2. It will reply with your user ID (like `7534357522`)
3. Use this number in the `chat_ids` array

**Step 3: Optional - Set up Channel/Group Alerts**
For channel or group alerts, you have two options:

**Option A: Public Channel**
- Create a public channel (e.g., `@mynms_alerts`)
- Add your bot as an administrator
- Use the channel name: `"@mynms_alerts"`

**Option B: Private Channel/Group**
- Create a private channel or group
- Add your bot as administrator
- Send a message in the channel, then forward it to @userinfobot to get the chat ID
- Use the negative number ID (e.g., `"-100*********0"`)

**Current Setup:**
Your config shows:
- Personal alerts: `"7534357522"` ✅ (your user ID)  
- Channel placeholder: `"@your_channel"` ❌ (replace with actual channel name or remove)

### MQTT Configuration

```json
{
  "mqtt": {
    "enabled": true,
    "broker_host": "localhost",
    "broker_port": 1883,
    "username": "",
    "password": "",
    "topic": "mnms/alerts",
    "qos": 1,
    "retain": false,
    "alert_config": {
      "min_severity": 0,
      "max_severity": 7,
      "rate_limit_seconds": 60,
      "max_alerts_per_minute": 20
    }
  }
}
```

### Syslog Severity Levels

The script recognizes standard syslog severity levels:

| Level | Name          | Description                      |
| ----- | ------------- | -------------------------------- |
| 0     | Emergency     | System is unusable               |
| 1     | Alert         | Action must be taken immediately |
| 2     | Critical      | Critical conditions              |
| 3     | Error         | Error conditions                 |
| 4     | Warning       | Warning conditions               |
| 5     | Notice        | Normal but significant condition |
| 6     | Informational | Informational messages           |
| 7     | Debug         | Debug-level messages             |

### Alert Configuration Options

#### Severity Filtering
- `min_severity`: Minimum severity level to forward (0-7)
- `max_severity`: Maximum severity level to forward (0-7)

#### Rate Limiting
- `rate_limit_seconds`: Minimum time between identical messages
- `max_alerts_per_minute`: Maximum alerts per minute per platform

#### Keyword Filtering
- `keywords`: Only forward messages containing these keywords (empty = all)
- `exclude_keywords`: Never forward messages containing these keywords


### Sync with Rootsvc

`bbfwdsvc` will POST to `bbrootsvc /api/v1/forwards ` to update its configuration for displaying on UI every 10 seconds.


## Message Formats

### WhatsApp Message
```
🚨 MNMS Alert 🚨
Time: 2025-02-06 12:04:06
Host: root
Severity: ALERT (1)
Tag: AutoUpdateService
Message: The root service root have new version, please update root service.
```

### Telegram Message
```
🚨 *MNMS Alert* 🚨
*Time:* 2025-02-06 12:04:06
*Host:* `root`
*Severity:* 🟠 ALERT
*Tag:* `AutoUpdateService`
*Message:* The root service root have new version, please update root service.
```

### MQTT Message (JSON)
```json
{
  "timestamp": "2025-02-06T12:04:06",
  "hostname": "root",
  "facility": 0,
  "severity": 1,
  "priority": 1,
  "tag": "AutoUpdateService",
  "message": "The root service root have new version, please update root service.",
  "raw_line": "<1>Feb  6 12:04:06 root AutoUpdateService: The root service root have new version, please update root service."
}
```

## Command Line Flags

- `-O string` : debug log output (default "stderr")
- `-P string` : debug log pattern string
- `-daemon string` : run | start | stop | restart | install | uninstall | status
- `-debuglog` : enable debug log, this will override -P to .*
- `-ds` : dump stack trace when exiting with non zero code
- `-fc string` : forward config file
- `-ic int` : command processing interval (default 5)
- `-ir int` : service registration interval (default 60)
- `-n string` : name
- `-nohttp` : no http service (default true)
- `-p int` : port (default 27196)
- `-r string` : root URL
- `-rs string` : remote syslog server address
- `-sbk` : backup syslog after forwarding
- `-sc` : enable compress file of backup syslog
- `-sf uint` : file size(megabytes) of syslog (default 100)
- `-so string` : local path of syslog (default "syslog_mnms.log")
- `-ss string` : syslog server address (default ":5544")
- `-version` : print version

## Command API

bbfwdsvc supports remote commands for configuration and control. These commands can be executed via the root service UI (Script page) or using the `bbctl` CLI tool.

### Overview

All forward commands are only available on forwarder services (services with `-kind=forward`). Commands return status responses in the format:
- Success: `status: "ok"`
- Error: `status: "error: <error message>"`

### Supported Commands

#### 1. Import Forward Configuration from URL
```
forward import [url]
```

Imports the entire forward configuration from a remote HTTP URL.

**Examples:**
- **With bbctl:**
  ```bash
  bbctl -cc service-name forward import http://example.com/forward-config.json
  // or bbctl -cc service-name forward import file://path/to/local/forward_config.json
  ```
- **With Script UI** (fill service name in the client field):
  ```
  forward import http://example.com/forward-config.json
  // or forward import file://path/to/local/forward_config.json
  ```

**Response:**
```
status: "ok" or "error: <error message>"
```

**Note:** This command does not support any Auth Token.

**Example JSON Configuration Structure:**
```json
{
  "whatsapp": {
    "enabled": true,
    "account_sid": "YOUR_ACCOUNT_SID",
    "auth_token": "YOUR_AUTH_TOKEN",
    "from_number": "+*********0",
    "to_numbers": [
      "+**********",
      "+1*********0"
    ],
    "alert_config": {
      "min_severity": 0,
      "max_severity": 3,
      "rate_limit_seconds": 300,
      "max_alerts_per_minute": 5,
      "keywords": [
        "error",
        "fail",
        "critical",
        "emergency",
        "alert"
      ],
      "exclude_keywords": [
        "debug"
      ]
    }
  },
  "telegram": {
    "enabled": true,
    "bot_token": "YOUR_BOT_TOKEN",
    "chat_ids": [
      "*********",
      "@your_channel"
    ],
    "alert_config": {
      "min_severity": 0,
      "max_severity": 4,
      "rate_limit_seconds": 180,
      "max_alerts_per_minute": 10,
      "keywords": [],
      "exclude_keywords": []
    }
  },
  "mqtt": {
    "enabled": false,
    "broker_host": "localhost",
    "broker_port": 1883,
    "username": "",
    "password": "",
    "topic": "mnms/alerts",
    "qos": 1,
    "retain": false,
    "alert_config": {
      "min_severity": 0,
      "max_severity": 7,
      "rate_limit_seconds": 60,
      "max_alerts_per_minute": 20,
      "keywords": [],
      "exclude_keywords": []
    }
  }
}
```

#### 2. Configure Forward Settings via Flags
```
forward config [-option] [value]
```

Set specific configuration options for WhatsApp, Telegram, and MQTT forwarding using command-line style flags.

**Examples:**
- **With bbctl:**
  ```bash
  bbctl -cc service-name forward config -whatsapp=true -whatsapp_to_numbers=+*********0,+********** -whatsapp_keywords=error,critical -telegram=true -telegram_chat_ids=12345,67890 -mqtt=true -mqtt_broker_host=broker.example.com -mqtt_topic=alerts
  ```
- **With Script UI:**
  ```
  forward config -whatsapp=true -whatsapp_to_numbers=+*********0,+********** -whatsapp_keywords=error,critical -telegram=true -telegram_chat_ids=12345,67890 -mqtt=true -mqtt_broker_host=broker.example.com -mqtt_topic=alerts
  ```

**Response:**
```
status: "ok" or "error: <error message>"
```

**Available Options:**

**WhatsApp Options:**
- `whatsapp=true|false` - Enable/disable WhatsApp forwarding
- `whatsapp_account_sid=your_account_sid` - Twilio Account SID
- `whatsapp_auth_token=your_auth_token` - Twilio Auth Token
- `whatsapp_from_number=your_whatsapp_from_number` - WhatsApp sender number
- `whatsapp_to_numbers=comma,separated,phone,numbers` - Recipient phone numbers
- `whatsapp_min_severity=0-7` (default: 0) - Minimum severity level
- `whatsapp_max_severity=0-7` (default: 7) - Maximum severity level
- `whatsapp_rate_limit_seconds=seconds` (default: 300) - Rate limit between identical messages
- `whatsapp_max_alerts_per_minute=number` (default: 5) - Max alerts per minute
- `whatsapp_keywords=comma,separated,keywords` - Keywords whitelist (empty = all)
- `whatsapp_exclude_keywords=comma,separated,keywords` - Keywords blacklist

**Telegram Options:**
- `telegram=true|false` - Enable/disable Telegram forwarding
- `telegram_bot_token=your_bot_token` - Telegram bot token
- `telegram_chat_ids=comma,separated,chat_ids` - Chat IDs or channel names
- `telegram_min_severity=0-7` (default: 0) - Minimum severity level
- `telegram_max_severity=0-7` (default: 7) - Maximum severity level
- `telegram_rate_limit_seconds=seconds` (default: 180) - Rate limit between identical messages
- `telegram_max_alerts_per_minute=number` (default: 10) - Max alerts per minute
- `telegram_keywords=comma,separated,keywords` - Keywords whitelist (empty = all)
- `telegram_exclude_keywords=comma,separated,keywords` - Keywords blacklist

**MQTT Options:**
- `mqtt=true|false` - Enable/disable MQTT forwarding
- `mqtt_broker_host=your_mqtt_broker_host` (default: localhost) - MQTT broker hostname
- `mqtt_broker_port=your_mqtt_broker_port` (default: 1883) - MQTT broker port
- `mqtt_username=your_mqtt_username` - MQTT username (optional)
- `mqtt_password=your_mqtt_password` - MQTT password (optional)
- `mqtt_topic=your_mqtt_topic` (default: mnms/alerts) - MQTT topic for alerts
- `mqtt_qos=0|1|2` (default: 1) - MQTT Quality of Service level
- `mqtt_retain=true|false` (default: false) - MQTT retain flag
- `mqtt_min_severity=0-7` (default: 0) - Minimum severity level
- `mqtt_max_severity=0-7` (default: 7) - Maximum severity level
- `mqtt_rate_limit_seconds=seconds` (default: 60) - Rate limit between identical messages
- `mqtt_max_alerts_per_minute=number` (default: 20) - Max alerts per minute
- `mqtt_keywords=comma,separated,keywords` - Keywords whitelist (empty = all)
- `mqtt_exclude_keywords=comma,separated,keywords` - Keywords blacklist

#### 3. Configure Forward Settings via Raw JSON
```
forward raw-json config [json_string]
```

Set the entire configuration using a raw JSON string. Use single quotes when using bbctl to wrap the JSON string.

**Examples:**
- **With bbctl:**
  ```bash
  bbctl -cc service-name forward raw-json config '{"whatsapp": {"enabled": true, "account_sid": "your_account_sid", "auth_token": "your_auth_token", "from_number": "your_whatsapp_from_number", "to_numbers": ["+*********0"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 300, "max_alerts_per_minute": 5, "keywords": ["error", "critical"], "exclude_keywords": []}}, "telegram": {"enabled": true, "bot_token": "your_bot_token", "chat_ids": ["12345"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 180, "max_alerts_per_minute": 10, "keywords": [], "exclude_keywords": []}}, "mqtt": {"enabled": true, "broker_host": "localhost", "broker_port": 1883, "username": "", "password": "", "topic": "mnms/alerts", "qos": 1, "retain": false, "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 60, "max_alerts_per_minute": 20, "keywords": [], "exclude_keywords":[]}}}'
  ```
Make sure to use single quotes `'` for the entire string with `bbctl`.

- **With Script UI:**
  ```
  forward raw-json config {"whatsapp": {"enabled": true, "account_sid": "your_account_sid", "auth_token": "your_auth_token", "from_number": "your_whatsapp_from_number", "to_numbers": ["+*********0"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 300, "max_alerts_per_minute": 5, "keywords": ["error", "critical"], "exclude_keywords": []}}, "telegram": {"enabled": true, "bot_token": "your_bot_token", "chat_ids": ["12345"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 180, "max_alerts_per_minute": 10, "keywords": [], "exclude_keywords": []}}, "mqtt": {"enabled": true, "broker_host": "localhost", "broker_port": 1883, "username": "", "password": "", "topic": "mnms/alerts", "qos": 1, "retain": false, "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 60, "max_alerts_per_minute": 20, "keywords": [], "exclude_keywords":[]}}}
  ```

**Response:**
```
status: "ok" or "error: <error message>"
```

#### 4. Upload Current Configuration to Root Service
```
forward upload
```

Uploads the current forward configuration to the Root Service immediately. This is useful for syncing configuration changes to the UI.

**Examples:**
- **With bbctl:**
  ```bash
  bbctl -cc service-name forward upload
  ```
- **With Script UI:**
  ```
  forward upload
  ```

**Response:**
```
status: "ok" or "error: <error message>"
```

#### 5. Send Test Syslog Message via Forwarder
```
forward send [facility] [severity] [tag] [message...]
```

Sends a test syslog message using the current forwarder configuration. This is convenient for testing the forwarder without waiting for actual syslog messages.

**Parameters:**
- `[facility]` - Syslog facility (0-23), RFC3164 standard
- `[severity]` - Syslog severity (0-7), RFC3164 standard  
- `[tag]` - Syslog tag
- `[message...]` - Syslog message, can be multiple words

**Examples:**
- **With bbctl:**
  ```bash
  bbctl -cc service-name forward send 0 1 test alert test
  ```
- **With Script UI:**
  ```
  forward send 0 1 test alert test
  ```

**Response:**
```
status: "ok" or "error: <error message>"
result: {
  "whatsapp_result": "WhatsApp alert sent." | "WhatsApp alert not sent by config." | "WhatsApp alert disabled."
  "telegram_result": "Telegram alert sent." | "Telegram alert not sent by config." | "Telegram alert disabled."
  "mqtt_result": "MQTT alert sent." | "MQTT alert not sent by config." | "MQTT alert disabled."
  "raw_line": "<syslog message from the command line>"
}
```

#### 6. Send Test Syslog Message with Custom Configuration
```
forward custom send [json_string] [facility] [severity] [tag] [message...]
```

Sends a test syslog message using a custom configuration. This allows testing different configurations without changing the current forwarder settings.

**Parameters:**
- `[json_string]` - Valid JSON string representing the ForwardConfig structure (use single quotes with bbctl)
- `[facility]` - Syslog facility (0-23), RFC3164 standard
- `[severity]` - Syslog severity (0-7), RFC3164 standard
- `[tag]` - Syslog tag
- `[message...]` - Syslog message, can be multiple words

**Examples:**
- **With bbctl:**
  ```bash
  bbctl -cc service-name forward custom send '{"whatsapp": {...}, "telegram": {...}, "mqtt": {...}}' 1 5 mytag This is a test message
  ```
Make sure to use single quotes for the entire string.

- **With Script UI:**
  ```
  forward custom send {"whatsapp": {...}, "telegram": {...}, "mqtt": {...}} 1 5 mytag This is a test message
  ```

**Response:**
```
status: "ok" or "error: <error message>"
result: {
  "whatsapp_result": "WhatsApp alert sent." | "WhatsApp alert not sent by config." | "WhatsApp alert disabled."
  "telegram_result": "Telegram alert sent." | "Telegram alert not sent by config." | "Telegram alert disabled."
  "mqtt_result": "MQTT alert sent." | "MQTT alert not sent by config." | "MQTT alert disabled."
  "raw_line": "<syslog message from the command line>"
}
```

### Command Help

Type `help forward [cmd]` for detailed information on each command:
- `help forward import` - Import configuration help
- `help forward config` - Flag-based configuration help  
- `help forward raw-json config` - Raw JSON configuration help
- `help forward upload` - Upload configuration help
- `help forward send` - Send test message help
- `help forward custom send` - Custom send test message help

### Syslog Facility and Severity Reference

**Facilities (0-23):**
- 0: Kernel messages
- 1: User-level messages  
- 2: Mail system
- 3: System daemons
- 4: Security/authorization messages
- 5: Messages generated internally by syslogd
- 6: Line printer subsystem
- 7: Network news subsystem
- 8: UUCP subsystem
- 9: Clock daemon
- 10: Security/authorization messages
- 11: FTP daemon
- 12: NTP subsystem
- 13: Log audit
- 14: Log alert
- 15: Clock daemon
- 16-23: Local facilities (local0-local7)

**Severities (0-7):**
- 0: Emergency - System is unusable
- 1: Alert - Action must be taken immediately
- 2: Critical - Critical conditions
- 3: Error - Error conditions  
- 4: Warning - Warning conditions
- 5: Notice - Normal but significant condition
- 6: Informational - Informational messages
- 7: Debug - Debug-level messages


## Monitoring and Troubleshooting

- Check logs for service status and alert forwarding
- Use the root service UI or CLI to issue commands and view service health
- Adjust configuration and command flags as needed for your environment

## See Also
- [bblogsvc](bblogsvc.md): Log service documentation
- [syslog_forwarder.json.template](../examples/syslog-alert-forwarder/syslog_forwarder.json.template): Example configuration

