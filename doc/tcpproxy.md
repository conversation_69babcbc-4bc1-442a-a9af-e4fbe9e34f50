# BBNIM TCP Proxy Guide

`bbnim` can proxy **TCP** connection to the address.


## Commands

Usage
- tcpproxy
    - start [from] [to]
    - stop [from]


`tcpproxy start` start to proxy TCP packets.
```shell
$ ./bbctl tcpproxy start :55821 **********:443
```

Note that the port should not be used by other process or unsafe port (0-1024)

`proxy stop` stop the proxy connection.
```shell
$ ./bbctl tcpproxy stop :55821
```


## API

- Get /api/v1/tcpproxy, retrieve 'mnms' all tcp proxying
    - query parameters: name

Example:

```json
{
    "client1": {
        ":1234": {
            "from": ":1234",
            "to": "***********:443"
        }
    },
    "client2": {
        ":5361": {
            "from": ":5361",
            "to": "***********:443"
        }
    },
    "root": {
        ":9001": {
            "from": ":9001",
            "to": "**********:1234"
        },
        ":9002": {
            "from": ":9002",
            "to": "**********:5361"
        }
    }
}
```
