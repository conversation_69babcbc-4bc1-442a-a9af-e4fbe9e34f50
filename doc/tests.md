# Importance of testing 

- Testing is the most important work we must do consistently and correctly 

- Testing is our primary job

- Tests are absolutely required so that we can [release](https://github.com/bbtechhive/mnms/blob/main/doc/release_procedure.md) the product 


# How to test

- Test negative cases not just positive -- verify a feature works as intended but also test if it handles cases where errors happen, users provide wrong input or other random cases

- Automate the tests as much as possible -- use scripts or code or other automation tools

- Make notes to enable consistent testing if automation is not available

- Write down test results -- dates, tester's name, what test was performed, what was the result?

- if tests fail, create github issue and assign a team member to work on the issue

- after every code modification you must run as many tests as you can. To enable this, tests should be automated. At minimum you should run unit and regression tests after merge of a PR. 

# List of tests

- Run all tests "sudo go test -v -p 1 -timeout 20m" on testbed. The tests are include unit test and regression test.

- unit tests must be run before and after any code modification 

- unit tests must be kept updated 

- as you code, unit tests must be created for code you add or modify 

- Perform all [tests listed in github issues tagged testing](https://github.com/bbtechhive/mnms/issues?q=+label%3Atesting+) 

- Read and verify contents of User manual

- Revise the changelog.md thoroughly 

- Installation process, installation package content verification

- Version verification, version display in commands and UI must match

- Run all examples   

- Run the root service 

- Run a client node service on another machine
  -  Austin tested with root runs on windows PC and 1st client runs on macbook, 2nd clent runs on the windows PC. worked (/register API return two client's information). In the same subnet, 2nd client could find devices but 1st client can't it's may gwd cross-platform issue (in Darwin) .

- Run another client node service on another machine
  -  Worked, we run two client in Alan's windows PC, and terminate one. We can see /register API returns four clients now. After a while terminted client's status changed to inactive.

- Make sure root and client services (network services) are communicating
  - In the run client on another machine testing case, we can see root updated devices information (/devices) and if clinet disconnet (/register) root update status as expect. 
  - Run beep {MAC} work, that means client can get command correctly.

- Run a web UI and make sure it is communicating properly with Root
  - Worked, Daniel runs root on windows notebook and client on windows PC. Run the UI startup command `npm start -- --host` on the windows notebook, and you can use the root ip to login to the UI on the windows PC(client side).

- Getting a list of devices on testbed -- when services start the devices should be discovered and available in the list of devices from the UI and SDK -- there should be about 100 devices

- Test IDPS with example rules sets 

- Test Anomaly detection with real time detection and example log files. 

- Getting help -- various modes of help for CLI and SDK should be printable from user interface

- APIs and SDK -- write scripts and code to test the SDK features
  - regression test will cover it. 

- Use of tokens in API and SDK
  - Austin & Alan try to port NMS regression test, we add the sub-folder /regression_test. Our target is cover all safe API and command of testbed. Keep each test results let us can trace different.
  - Please pull add-regression-test branch and go to /regression_test run `regression_test.sh`

- Running TLS -- use caddy to allow TLS features

- Test for any security leaks

- Cluster high availability -- detection of network service going down, alert messages generated, restart of network services using feature -M flag of the bbnmssvc

- SNMP MIB Browser -- use UI to browse atop and third party MIBs

| Date  | member |                                          how to test                                           |
| :---: | :----: | :--------------------------------------------------------------------------------------------: |
| 06/20 | yanlin | 1. use simluator and run command  2. input command ./simulator.exe run -P ".*" (admin or root) |
       
|   Item    | Result |
| :-------: | :----: |
| snmp walk |  Pass  |
| snmp get  |  Pass  |
| snmp set  |  Pass  |
| snmp bulk |  Pass  |



- Syslog forwarding, aggregation and display -- be sure to have syslogs being forwarded from the devices to the network service and to the root
  - 0620 daniel, add new syslog test script to test syslog forwarded from the devices to the network service and to the root. The test is normal on the testbed.

- Alerts and events -- make sure they are being generated and displayed
  - In syslog test script will cover it.

- MQTT message service -- use basic features MQTT
  - 0621 daniel, add new mqtt test script to test mqtt client communicate other mqtt server. The test is normal.

- OPC UA -- use basic OPC UA features

| Date  | member |                 how to test                  |
| :---: | :----: | :------------------------------------------: |
| 06/20 | yanlin | use test code, input go test -v -run TestOpc |


|              Item              | Result |
| :----------------------------: | :----: |
|       TestOpcFindServers       |  Pass  |
|      TestOpcGetEndpoints       |  Pass  |
|          TestOpcWrite          |  Pass  |
|      TestOpcNegativeWrite      |  Pass  |
|         TestOpcBrowse          |  Pass  |
|     TestOpcNegativeBrowse      |  Pass  |
|    TestOpcReadServerStatus     |  Pass  |
|          TestOpcRead           |  Pass  |
|      TestOpcNegativeRead       |  Pass  |
|     TestOpcReadAttributes      |  Pass  |
| TestOpcNegativeReadAttributes  |  Pass  |
| TestOpcReadVariableAttributes  |  Pass  |
|     TestOpcBrowseReference     |  Pass  |
| TestOpcNegativeBrowseReference |  Pass  |
|       TestOpcConnectCmd        |  Pass  |
|         TestOpcReadCmd         |  Pass  |
|        TestOpcBrowseCmd        |  Pass  |
|      TestOpcSubscribeCmd       |  Pass  |
|   TestOpcDeleteSubscribeCmd    |  Pass  |
|        TestOpcCloseCmd         |  Pass  |

- Test agents

- Authentication
  - We integrate authentication test in to regression test, regression_test.go
- Changing bbrootconfig.enc using encryption -- see authentication.md

- License key and alerts -- test negative and positive cases -- limited network services and devices should generate alerts

- Topology discovery and display

- Regression
  - in regression_test.go will run all services at once and test  