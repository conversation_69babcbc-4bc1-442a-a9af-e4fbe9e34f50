## Prerequisites

- The device must have the `agentclient`.

# How to test agentclient regression test

Before running the regression test, replace the IP address ********** with the actual Host IP address in the following command:

`./$BINDIR/$PROGRAM -r $URL -n client1 -s -r http://localhost:27182 -rs localhost:27182 -ns http://**********:27183 -p 27183 -P "agent" `


# Running Regression Test on Windows

Run regression test, the `agent_reg_test` should be in `regression_test` folder

```
$ ./agent_reg_test.sh 

```
when user run regression test it will generate  `agent_reg_test.txt` file under  `regression_test` folder,so that user can view which tests are pass or fail.

# Running Regression Test on Linux

we should give permission to run regression_test for all tests in linux
command: chmod +x test_filename.sh

Note : `agent topologyinfo send AA-BB-CC-DD-EE-FF` this command only tested on EHG75xx and EHG76xx series switches.

Note :If you're updating network settings using a DHCP server, make sure the device is connected to a DHCP server. This is necessary for the device to obtain an New IP address dynamically.
`agent config network set $mac_address ********** $netmask $gateway $hostname 1`
