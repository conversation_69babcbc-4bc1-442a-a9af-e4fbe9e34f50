# NIMBL mqtt guide

`NIMBL` provide basic support for mqtt publish and subscribe messaging. Both Mqtt broker and client run on the client node. When a message comes in, the message will be sent to the server node by syslog. And `NIMBL` does not have its own subscription item, we only provide basic subscription and publishing.

## Mqtt broker ##

`NIMBL` contains an mqtt broker that can accept subscriptions and publish messages. The default port is :11883. If you want to change port, you can add commmand `mb`:
```shell
$ ./bbnmssvc -n client -mb ":1883" -r http://************:27182 -rs ************:5514
```
And when mqtt broker receive the publish messages, it would send the publish messages to server node by syslog.

## Mqtt client ##
And, when the remote mqtt broker does not exist, it will not work to subscribe and publish messages. User must carefully check remote mqtt broker status.

### subscribe ###
`NIMBL` supports subscribing and publishing messages to other remote mqtt brokers. If you need to subscribe messages to the remote mqtt broker, you can write the command:
```
	mqtt sub [tcp address] [topic] 
```

Example:
```shell
# In command line,
$ ./bbctl mqtt sub ************:1883 topictest

#In UI script,
$ mqtt sub ************:1883 topictest
```

### publish ###
If you need to publish messages to the remote mqtt broker, you can write the command:
```
	mqtt pub [tcp address] [topic] [message]
```

Example:
```shell
# In command line,
$ ./bbctl mqtt pub ************:1883 topictest "this is message."

#In UI script,
$ mqtt pub ************:1883 topictest "this is message."
```

### unsubscribe ###
If you don't need to subscribe messages to the remote mqtt broker, you can write the command:
```
mqtt unsub [tcp address] [topic]
```

Example:
```shell
# In command line,
$ ./bbctl mqtt unsub ************:1883 topictest

# In UI script,
$ mqtt unsub ************:1883 topictest
```

## Not yet implemented mqtt feature ##

1. User can get more information about the machine by subscribing to the `network`, `config` and `device` topic of `bbnim`.

2. User can use the UI to observe all topics, and easily add/remove subscribed topics without commands.

3. The message published/subscribed by the user will be displayed on the UI, and the user can easily get the message.
