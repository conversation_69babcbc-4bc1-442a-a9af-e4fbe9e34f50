# BBNIM WireGuard(VPN) Guide

`bbnim` provides VPN service by WireGuard. 

The goal is to build tunnels between root and clients, so we'll treat mnms root as the VPN server, and mnms clients as the VPN clients.

Once tunnel is built, we can access every node we want.

## Required

- WireGuard installed
    - Windows: https://www.wireguard.com/install/
    - Linux: sudo apt install wireguard
- Run `bbnim` as root or administrator
- `bbnim` vpn enabled
- WireGuard port enabled on system

## Flags

The following flags can be configured with `bbnim` run

- wgname: WireGuard interface name, default name is `nms-wg0`, once nmns starts it'll load [wgname].conf is exist
- nowg: VPN disabled, default is `false`

## Commands

`mnms` provides command to operate WireGuard config

Usage:
- [-ck root] wg 
    - config
        - interface
            - addresses set [addr1] [addr2] [addr3] ...
            - listenport set [port]
            - mtu set [mtu]
            - dns set [dns1] [dns2] [dns3] ...
            - set [address] [...listenport] [...mtu] [...dns]
            - preup add [command]
            - preup delete [index]
            - postup add [command]
            - postup delete [index]
            - predown add [command]
            - predown delete [index]
            - postdown add [command]
            - postdown delete [index]
        - peer
            - pubkey set [index] [pubkey]
            - allowedips set [index] [addr1] [addr2] [addr3] ...
            - endpoint set [index] [endpoint]
            - persistentkeepalive set [index] [seconds]
            - presharedkey set [index] [presharedkey]
            - add [pubkey] [allowedips] [...endpoint] [...persistentkeepalive] [...presharedkey]
            - delete [index]
        - generate
        - show
    - start
    - stop
    - status

`-ck root ` means root command, command will be executed at root, for example `-ck root wg start`

`wg config interface` and `wg config peer` change the configuration directly.
Please refer to Wireguard official website for details.

`wg config interface set [address] [...listenport] [...mtu] [...dns]` is a convient command for set arguments at once. For example `wg config interface set **********/24 55820 1400 *******`

`wg config interface add|delete [preup|postup|predown|postdown]` are commands to add/delete commands while wg up or down, for example: `wg config interface add preup echo hello world`; Note that delete commands must given an index, index starts from 0.

`wg config peer` needs argument index to operate, index starts from 0.

`wg config generate` save configuration in current path as [wgname].conf
`wg config show` return configuration in command result, normally you don't have to use this.

`wg start` start WireGuard tunnel with [wgname].conf, make sure WireGuard configuration file exists
`wg stop` stop WireGuard tunnel with [wgname], make sure WireGuard configuration file exists
`wg status` return WireGuard interface [wgname] status in command result, normally you don't have to use this


## API

- Get /api/v1/wg

Note that the `WgStatus` is collected by Wireguard tool `wg`, and if the data is empty means tunnel is not running or mnms isn't running as root.


## Example

Here is an example for using wg commands in root.
- check wg data
- set root wg and run
- set client wg and run
- check wg data again
- stop wg

### Check Wg Data

Use API /api/v1/wg to check wg data, if mnms starts with no [wgname].conf, the data will look like following
```json
{
    "client": {
        "enabled": true,
        "name": "nms-wg0",
        "config": {
            "interface": {
                "public_key": "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=",
                "addresses": null,
                "listen_port": 0,
                "mtu": 0,
                "dns": null,
                "pre_up": nil,
                "post_up": nil,
                "pre_down": nil,
                "post_down": nil
            },
            "peers": null
        },
        "status": {
            "interface": "",
            "public_key": "",
            "peers": null
        }
    },
    "root": {
        "enabled": true,
        "name": "nms-wg0",
        "config": {
            "interface": {
                "public_key": "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=",
                "addresses": null,
                "listen_port": 0,
                "mtu": 0,
                "dns": null,
                "pre_up": nil,
                "post_up": nil,
                "pre_down": nil,
                "post_down": nil
            },
            "peers": null
        },
        "status": {
            "interface": "",
            "public_key": "",
            "peers": null
        }
    }
}
```

### Set Root Wg

```shell
# set interface with server ip and port
$ ./bbctl -ck root wg config interface set **********/24 55820
# set peer with client public key and assign client ip
$ ./bbctl -ck root wg config peer add Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg= **********/32
# create config file
$ ./bbctl -ck root wg config generate
```

**Note that make sure port is able to through firewall.**

Now there is a file generated as following
```
[Interface]
PrivateKey = SM343uawQ5NM5HiQS0vrrAfnYrZLsCIpNqii6bEuLWU=
Address = **********/24
ListenPort = 55820

[Peer]
PublicKey = Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=
AllowedIPs = **********/32
```

Or you can use /api/v1/wg to check the configuration

Now let's run the WireGuard with command
```shell
$ ./bbctl -ck root wg start
```

Check the status with api, it'll be like
```json
{
    "client": {
        "enabled": true,
        "name": "nms-wg0",
        "config": {
            "interface": {
                "public_key": "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=",
                "addresses": null,
                "listen_port": 0,
                "mtu": 0,
                "dns": null,
                "pre_up": "",
                "post_up": "",
                "pre_down": "",
                "post_down": ""
            },
            "peers": null
        },
        "status": {
            "interface": "",
            "public_key": "",
            "peers": null
        }
    },
    "root": {
        "enabled": true,
        "name": "nms-wg0",
        "config": {
            "interface": {
                "public_key": "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=",
                "addresses": [
                    "**********/24"
                ],
                "listen_port": 55820,
                "mtu": 0,
                "dns": [],
                "pre_up": "",
                "post_up": "",
                "pre_down": "",
                "post_down": ""
            },
            "peers": [
                {
                    "public_key": "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=",
                    "preshared_key": "",
                    "allowed_ips": [
                        "**********/32"
                    ],
                    "endpoint": "",
                    "persistent_keepalive": 0
                }
            ]
        },
        "status": {
            "interface": "nms-wg0",
            "public_key": "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=",
            "peers": {
                "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=": {
                    "allowed_ips": "**********/32",
                    "latest_handshake": "",
                    "transfer": "",
                    "persistent_keepalive": ""
                }
            }
        }
    }
}
```

### Set Network Service(Client) Wg

Now we can configure client's Wireguard with command
```shell
# set interface with ip just assigned
$ ./bbctl wg config interface set **********/32
# add root peer
$ ./bbctl wg config peer add 4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00= **********/24 ***************:55820 30
$ ./bbctl wg config generate
```

**Note that if we want specific network service (client) to run the wg, please run command with `-cc` flag**

Then there will be a [wgname].conf generated at client
```
[Interface]
PrivateKey = mDstwp1P6b+7GAtSuDahXpOZwFPuPysx1VrBtMfunlI=
Address = **********/32

[Peer]
PublicKey = 4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=
AllowedIPs = **********/24
Endpoint = ***************:55820
PersistentKeepalive = 30
```

Start client Wireguard
```shell
$ ./bbctl wg start
```

### Check Wg Again

Get /api/v1/wg

```json
{
    "client": {
        "enabled": true,
        "name": "nms-wg0",
        "config": {
            "interface": {
                "public_key": "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=",
                "addresses": [
                    "**********/32"
                ],
                "listen_port": 0,
                "mtu": 0,
                "dns": [],
                "pre_up": nil,
                "post_up": nil,
                "pre_down": nil,
                "post_down": nil
            },
            "peers": [
                {
                    "public_key": "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=",
                    "preshared_key": "",
                    "allowed_ips": [
                        "**********/24"
                    ],
                    "endpoint": "***************:55820",
                    "persistent_keepalive": 30
                }
            ]
        },
        "status": {
            "interface": "nms-wg0",
            "public_key": "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=",
            "peers": {
                "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=": {
                    "allowed_ips": "**********/24",
                    "latest_handshake": "18 seconds ago",
                    "transfer": "92 B received, 15.79 KiB sent",
                    "persistent_keepalive": "every 30 seconds"
                }
            }
        }
    },
    "root": {
        "enabled": true,
        "name": "nms-wg0",
        "config": {
            "interface": {
                "public_key": "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=",
                "addresses": [
                    "**********/24"
                ],
                "listen_port": 55820,
                "mtu": 0,
                "dns": [],
                "pre_up": nil,
                "post_up": nil,
                "pre_down": nil,
                "post_down": nil
            },
            "peers": [
                {
                    "public_key": "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=",
                    "preshared_key": "",
                    "allowed_ips": [
                        "**********/32"
                    ],
                    "endpoint": "",
                    "persistent_keepalive": 0
                }
            ]
        },
        "status": {
            "interface": "nms-wg0",
            "public_key": "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=",
            "peers": {
                "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=": {
                    "allowed_ips": "**********/32",
                    "latest_handshake": "26 seconds ago",
                    "transfer": "180 B received, 92 B sent",
                    "persistent_keepalive": ""
                }
            }
        }
    }
}
```


### Stop Wg

If we are going to shutdown Wireguard, use commands

```shell
$ ./bbctl -ck root wg stop
$ ./bbctl wg stop
```

```json
{
    "client": {
        "enabled": true,
        "name": "nms-wg0",
        "config": {
            "interface": {
                "public_key": "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=",
                "addresses": [
                    "**********/32"
                ],
                "listen_port": 0,
                "mtu": 0,
                "dns": [],
                "pre_up": nil,
                "post_up": nil,
                "pre_down": nil,
                "post_down": nil
            },
            "peers": [
                {
                    "public_key": "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=",
                    "preshared_key": "",
                    "allowed_ips": [
                        "**********/24"
                    ],
                    "endpoint": "***************:55820",
                    "persistent_keepalive": 30
                }
            ]
        },
        "status": {
            "interface": "",
            "public_key": "",
            "peers": null
        }
    },
    "root": {
        "enabled": true,
        "name": "nms-wg0",
        "config": {
            "interface": {
                "public_key": "4UobIzaAODI1XA5aLHy0TJmaM7JtiBwuOFz2I8TLB00=",
                "addresses": [
                    "**********/24"
                ],
                "listen_port": 55820,
                "mtu": 0,
                "dns": [],
                "pre_up": nil,
                "post_up": nil,
                "pre_down": nil,
                "post_down": nil
            },
            "peers": [
                {
                    "public_key": "Z6tBqHNZEr3+gp1JuEAJXLp+dmE5zeZLVJZ0vSX41Eg=",
                    "preshared_key": "",
                    "allowed_ips": [
                        "**********/32"
                    ],
                    "endpoint": "",
                    "persistent_keepalive": 0
                }
            ]
        },
        "status": {
            "interface": "",
            "public_key": "",
            "peers": null
        }
    }
}
```