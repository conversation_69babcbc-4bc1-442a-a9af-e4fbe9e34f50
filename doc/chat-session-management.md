# Chat Session Management

## Overview

To prevent unlimited memory growth in the Chat functionality, we have implemented an intelligent session management mechanism that includes:

1. **Smart Cleanup Triggering**: Automatically cleans up expired sessions when the session count reaches 90% of the maximum value
2. **Manual Session Management**: Provides API endpoints for manual session management
3. **Memory Control**: Limits the number of messages per session and total session count

## Smart Cleanup Mechanism

### Configuration Parameters

- **Session TTL**: 6 hours (adjustable in `NewChat()`)
- **Cleanup Trigger Threshold**: 90% of maximum session count
- **Maximum Sessions**: 1000 sessions
- **Maximum Messages per Session**: 20 messages

### Cleanup Logic

1. Check current session count every time a new session is created
2. If session count reaches 90% of maximum (900 sessions), trigger cleanup
3. Clean up sessions that haven't been accessed for more than 6 hours
4. Log cleanup statistics

### Trigger Conditionss

- **Auto Trigger**: When active sessions ≥ 900 (90% × 1000)
- **Cleanup Target**: Remove sessions unused for more than 6 hours
- **Cleanup Timing**: Check before creating new sessions

## API Endpoints

### 1. Get Session Statistics

```http
GET /api/v1/llm/session/stats
```

**Response:**
```json
{
  "active_sessions": 850,
  "max_sessions": 1000,
  "cleanup_threshold": 900,
  "cleanup_trigger_count": 900
}
```

### 2. Manual Cleanup of Expired Sessions

```http
POST /api/v1/llm/session/cleanup
```

**Response:**
```json
{
  "message": "Cleaned up 5 expired sessions",
  "cleaned_count": 5,
  "active_sessions": 10
}
```

### 3. Remove Specific Session

```http
DELETE /api/v1/llm/session/remove
Content-Type: application/json

{
  "session_id": "your-session-id"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Session removed successfully"
}
```

## Code Examples

### Check Session Statistics

```bash
curl -X GET http://localhost:27182/api/v1/llm/session/stats
```

### Manual Cleanup of Expired Sessions

```bash
curl -X POST http://localhost:27182/api/v1/llm/session/cleanup
```

### Remove Specific Session

```bash
curl -X DELETE http://localhost:27182/api/v1/llm/session/remove \
  -H "Content-Type: application/json" \
  -d '{"session_id": "your-session-id"}'
```

## Memory Optimization Features

### 1. Message History Truncation

When the number of messages in a single session exceeds `MaxHistoryPerSession` (default 20):
- Automatically retain the most recent half of messages
- Remove older messages to control memory usage

### 2. Session Expiration Check

Each session has:
- `LastAccessed`: Last access time
- `SessionTTL`: Session time-to-live (default 24 hours)
- `IsExpired()`: Check if expired

### 3. Concurrency Safety

All session operations are protected by `sync.RWMutex` to ensure thread safety.

## Monitoring and Debugging

### Log Output

The cleanup process outputs logs:
```
Cleaned up expired chat sessions count=5 remaining=15
```

### Testing

Run tests to verify functionality:
```bash
go test -v -run TestChatSession
```

## Configuration Adjustment

To adjust configuration, modify the following parameters:

```go
// In NewChat()
SessionTTL: 6 * time.Hour,   // Session time-to-live (6 hours)
MaxHistoryPerSession: 20,    // Maximum messages per session

// In NewChatManager()
maxTotalSessions: 1000,      // Maximum total sessions
cleanupThreshold: 0.9,       // Cleanup trigger threshold (90%)
```

## Important Notes

1. **Smart Cleanup Triggering**: Cleanup is only triggered when session count reaches threshold, avoiding unnecessary resource consumption
2. **Session Removal is Permanent**: Once a session is cleaned up or manually removed, it cannot be recovered
3. **Concurrency Safety**: All operations are thread-safe
4. **Memory Efficiency**: On-demand cleanup mechanism effectively prevents memory leaks
5. **Performance Optimization**: Only checks when creating new sessions, doesn't affect existing session performance 