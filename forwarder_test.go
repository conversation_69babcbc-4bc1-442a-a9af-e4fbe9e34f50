package mnms

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	MQTTBroker "github.com/mochi-co/mqtt/server"
	"github.com/mochi-co/mqtt/server/listeners"
)

func TestForwardCmd(t *testing.T) {
	QC.Name = "testForwarder"
	QC.Kind = "forward"

	ForwardInit("")

	// General/Dispatch tests
	t.Run("DispatchLogic", func(t *testing.T) {
		// Unknown command
		cmdinfo := CmdInfo{Command: "forward unknown"}
		result := ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "unknown forward command") {
			t.Errorf("Expected unknown command error, got: %v", result.Status)
		}
	})

	// Tests for "forward import"
	t.Run("ForwardImportCmd", func(t *testing.T) {
		// create a test forward configuration json file at [pwd]/test_forward_config.json
		configJson := `{"whatsapp": {"enabled": true, "account_sid": "test_sid", "auth_token": "test_token", "from_number": "+**********", "to_numbers": ["+**********"]}}`
		testFile := "test_forward_config.json"
		err := os.WriteFile(testFile, []byte(configJson), 0644)
		if err != nil {
			t.Fatalf("Failed to create test config file: %v", err)
		}
		defer os.Remove(testFile) // Clean up immediately after creation

		// Create a test HTTP server to serve the file
		testServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.URL.Path == "/"+testFile {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(configJson))
			} else {
				w.WriteHeader(http.StatusNotFound)
			}
		}))
		defer testServer.Close()

		// Use the test server URL
		testUrl := testServer.URL + "/" + testFile
		cmdinfo := CmdInfo{Command: "forward import " + testUrl}
		cmdinfo = *RunCmd(&cmdinfo)

		// check if the command was successful and QC.ForwardSvcData[QC.Name] is not nil and has the expected data
		if cmdinfo.Status != "ok" {
			t.Errorf("Expected command to succeed, got: %v", cmdinfo.Status)
		}
		if f, ok := QC.ForwardSvcData[QC.Name]; !ok {
			t.Errorf("Expected forward service data to be set, got: %v", f)
		} else {
			if f.WhatsApp.Enabled != true {
				t.Errorf("Expected WhatsApp to be enabled, got: %v", f.WhatsApp.Enabled)
			}
			if f.WhatsApp.AccountSID != "test_sid" {
				t.Errorf("Expected WhatsApp AccountSID to be 'test_sid', got: %v", f.WhatsApp.AccountSID)
			}
			if f.WhatsApp.AuthToken != "test_token" {
				t.Errorf("Expected WhatsApp AuthToken to be 'test_token', got: %v", f.WhatsApp.AuthToken)
			}
			if f.WhatsApp.FromNumber != "+**********" {
				t.Errorf("Expected WhatsApp From to be '+**********', got: %v", f.WhatsApp.FromNumber)
			}
			if len(f.WhatsApp.ToNumbers) != 1 {
				t.Errorf("Expected WhatsApp ToNumbers to have 1 number, got: %v", len(f.WhatsApp.ToNumbers))
			}
			if f.WhatsApp.ToNumbers[0] != "+**********" {
				t.Errorf("Expected WhatsApp To to be '+**********', got: %v", f.WhatsApp.ToNumbers[0])
			}
		}

		// negative test for invalid URL
		cmdinfo = CmdInfo{Command: "forward import http://invalid.example.com/nonexistent.json"}
		cmdinfo = *RunCmd(&cmdinfo)
		if !strings.HasPrefix(cmdinfo.Status, "error: failed to read forward config") {
			t.Errorf("Expected error for invalid URL, got: %v", cmdinfo.Status)
		}
	})

	// Tests for "forward config"
	t.Run("ForwardConfigCmd", func(t *testing.T) {
		// Initialize with default config
		config := DefaultForwardConfig()
		QC.ForwardSvcData[QC.Name] = *config

		// Test valid config command with WhatsApp settings
		cmdinfo := CmdInfo{Command: "forward config -whatsapp=true -whatsapp_account_sid=test_sid -whatsapp_to_numbers=+**********,+**********"}
		result := ForwardCmd(&cmdinfo)
		if result.Status != "ok" {
			t.Errorf("Expected config command to succeed, got: %v", result.Status)
		}

		// Verify config was updated
		updatedConfig, ok := QC.ForwardSvcData[QC.Name]
		if !ok {
			t.Errorf("Expected config to exist after update")
		} else {
			if !updatedConfig.WhatsApp.Enabled {
				t.Errorf("Expected WhatsApp to be enabled")
			}
			if updatedConfig.WhatsApp.AccountSID != "test_sid" {
				t.Errorf("Expected AccountSID to be 'test_sid', got: %v", updatedConfig.WhatsApp.AccountSID)
			}
			if len(updatedConfig.WhatsApp.ToNumbers) != 2 {
				t.Errorf("Expected 2 phone numbers, got: %v", len(updatedConfig.WhatsApp.ToNumbers))
			}
			if updatedConfig.WhatsApp.ToNumbers[0] != "+**********" {
				t.Errorf("Expected first phone number to be '+**********', got: %v", updatedConfig.WhatsApp.ToNumbers[0])
			}
		}

		// Test Telegram settings
		cmdinfo = CmdInfo{Command: "forward config -telegram=true -telegram_bot_token=test_token -telegram_chat_ids=12345,67890"}
		result = ForwardCmd(&cmdinfo)
		if result.Status != "ok" {
			t.Errorf("Expected telegram config command to succeed, got: %v", result.Status)
		}

		updatedConfig = QC.ForwardSvcData[QC.Name]
		if !updatedConfig.Telegram.Enabled {
			t.Errorf("Expected Telegram to be enabled")
		}
		if updatedConfig.Telegram.BotToken != "test_token" {
			t.Errorf("Expected BotToken to be 'test_token', got: %v", updatedConfig.Telegram.BotToken)
		}

		// Test MQTT settings
		cmdinfo = CmdInfo{Command: "forward config -mqtt=true -mqtt_broker_host=test.broker.com -mqtt_topic=test/alerts"}
		_ = ForwardCmd(&cmdinfo)
		// since mqtt client might fail to connect in test environment, we just check if the command was successful

		updatedConfig = QC.ForwardSvcData[QC.Name]
		if !updatedConfig.MQTT.Enabled {
			t.Errorf("Expected MQTT to be enabled")
		}
		if updatedConfig.MQTT.BrokerHost != "test.broker.com" {
			t.Errorf("Expected BrokerHost to be 'test.broker.com', got: %v", updatedConfig.MQTT.BrokerHost)
		}
		if updatedConfig.MQTT.Topic != "test/alerts" {
			t.Errorf("Expected Topic to be 'test/alerts', got: %v", updatedConfig.MQTT.Topic)
		}

		// Test invalid command format
		cmdinfo = CmdInfo{Command: "forward config"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "usage: forward config") {
			t.Errorf("Expected usage error, got: %v", result.Status)
		}

		// Test invalid flag
		cmdinfo = CmdInfo{Command: "forward config -invalid_flag=value"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error:") {
			t.Errorf("Expected error for invalid flag, got: %v", result.Status)
		}

		// Test severity validation errors
		// Test WhatsApp invalid min severity
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_min_severity=-1"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid severity configuration") || !strings.Contains(result.Status, "WhatsApp severity invalid") {
			t.Errorf("Expected WhatsApp severity validation error, got: %v", result.Status)
		}

		// Test WhatsApp invalid max severity
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_max_severity=8"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid severity configuration") || !strings.Contains(result.Status, "WhatsApp severity invalid") {
			t.Errorf("Expected WhatsApp max severity validation error, got: %v", result.Status)
		}

		// Test WhatsApp min > max severity
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_min_severity=5 -whatsapp_max_severity=3"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid severity configuration") || !strings.Contains(result.Status, "WhatsApp severity invalid") {
			t.Errorf("Expected WhatsApp min>max severity validation error, got: %v", result.Status)
		}

		// Test Telegram severity validation
		cmdinfo = CmdInfo{Command: "forward config -telegram_min_severity=-2 -telegram_max_severity=10"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid severity configuration") || !strings.Contains(result.Status, "Telegram severity invalid") {
			t.Errorf("Expected Telegram severity validation error, got: %v", result.Status)
		}

		// Test MQTT severity validation
		cmdinfo = CmdInfo{Command: "forward config -mqtt_min_severity=8 -mqtt_max_severity=-1"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid severity configuration") || !strings.Contains(result.Status, "MQTT severity invalid") {
			t.Errorf("Expected MQTT severity validation error, got: %v", result.Status)
		}

		// Test multiple platform severity errors
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_min_severity=-1 -telegram_max_severity=8 -mqtt_min_severity=5 -mqtt_max_severity=2"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid severity configuration") {
			t.Errorf("Expected multiple platform severity validation error, got: %v", result.Status)
		}
		// Should contain all three platform errors
		if !strings.Contains(result.Status, "WhatsApp severity invalid") ||
			!strings.Contains(result.Status, "Telegram severity invalid") ||
			!strings.Contains(result.Status, "MQTT severity invalid") {
			t.Errorf("Expected all three platform severity errors, got: %v", result.Status)
		}

		// Test rate limit validation errors
		// Test WhatsApp negative rate limit seconds
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_rate_limit_seconds=-1"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid rate limit configuration") || !strings.Contains(result.Status, "WhatsApp rate limit seconds invalid") {
			t.Errorf("Expected WhatsApp rate limit seconds validation error, got: %v", result.Status)
		}

		// Test WhatsApp negative max alerts per minute
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_max_alerts_per_minute=-5"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid rate limit configuration") || !strings.Contains(result.Status, "WhatsApp max alerts per minute invalid") {
			t.Errorf("Expected WhatsApp max alerts per minute validation error, got: %v", result.Status)
		}

		// Test Telegram rate limit validation
		cmdinfo = CmdInfo{Command: "forward config -telegram_rate_limit_seconds=-10 -telegram_max_alerts_per_minute=-3"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid rate limit configuration") {
			t.Errorf("Expected Telegram rate limit validation error, got: %v", result.Status)
		}
		if !strings.Contains(result.Status, "Telegram rate limit seconds invalid") || !strings.Contains(result.Status, "Telegram max alerts per minute invalid") {
			t.Errorf("Expected both Telegram rate limit errors, got: %v", result.Status)
		}

		// Test MQTT rate limit validation
		cmdinfo = CmdInfo{Command: "forward config -mqtt_rate_limit_seconds=-60 -mqtt_max_alerts_per_minute=-20"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid rate limit configuration") {
			t.Errorf("Expected MQTT rate limit validation error, got: %v", result.Status)
		}
		if !strings.Contains(result.Status, "MQTT rate limit seconds invalid") || !strings.Contains(result.Status, "MQTT max alerts per minute invalid") {
			t.Errorf("Expected both MQTT rate limit errors, got: %v", result.Status)
		}

		// Test multiple platform rate limit errors
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_rate_limit_seconds=-1 -telegram_max_alerts_per_minute=-5 -mqtt_rate_limit_seconds=-60"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "invalid rate limit configuration") {
			t.Errorf("Expected multiple platform rate limit validation error, got: %v", result.Status)
		}
		// Should contain all platform-specific errors
		if !strings.Contains(result.Status, "WhatsApp rate limit seconds invalid") ||
			!strings.Contains(result.Status, "Telegram max alerts per minute invalid") ||
			!strings.Contains(result.Status, "MQTT rate limit seconds invalid") {
			t.Errorf("Expected all specified rate limit errors, got: %v", result.Status)
		}

		// Test valid edge cases (0 values should be allowed)
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_rate_limit_seconds=0 -telegram_max_alerts_per_minute=0 -mqtt_rate_limit_seconds=0 -mqtt_max_alerts_per_minute=0"}
		result = ForwardCmd(&cmdinfo)
		if result.Status != "ok" {
			t.Errorf("Expected zero values to be valid, got: %v", result.Status)
		}

		// Test valid boundary values (min and max severity boundaries)
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_min_severity=0 -whatsapp_max_severity=7 -telegram_min_severity=0 -telegram_max_severity=7 -mqtt_min_severity=0 -mqtt_max_severity=7"}
		result = ForwardCmd(&cmdinfo)
		if result.Status != "ok" {
			t.Errorf("Expected boundary severity values to be valid, got: %v", result.Status)
		}

		// Test combination of severity and rate limit errors
		cmdinfo = CmdInfo{Command: "forward config -whatsapp_min_severity=-1 -whatsapp_rate_limit_seconds=-300 -telegram_max_severity=8 -telegram_max_alerts_per_minute=-10"}
		result = ForwardCmd(&cmdinfo)
		// Should fail on severity validation first (since it's checked before rate limits)
		if !strings.Contains(result.Status, "invalid severity configuration") {
			t.Errorf("Expected severity validation to be checked first, got: %v", result.Status)
		}
	})

	// Tests for "forward raw-json config"
	t.Run("ForwardRawJsonConfigCmd", func(t *testing.T) {
		// Test valid JSON config
		jsonConfig := `{"whatsapp":{"enabled":true,"account_sid":"test_sid","auth_token":"test_token","from_number":"+**********","to_numbers":["+**********"],"alert_config":{"min_severity":0,"max_severity":7,"rate_limit_seconds":300,"max_alerts_per_minute":5,"keywords":[],"exclude_keywords":[]}},"telegram":{"enabled":false,"bot_token":"","chat_ids":[],"alert_config":{"min_severity":0,"max_severity":7,"rate_limit_seconds":180,"max_alerts_per_minute":10,"keywords":[],"exclude_keywords":[]}},"mqtt":{"enabled":false,"broker_host":"localhost","broker_port":1883,"username":"","password":"","topic":"mnms/alerts","qos":1,"retain":false,"alert_config":{"min_severity":0,"max_severity":7,"rate_limit_seconds":60,"max_alerts_per_minute":20,"keywords":[],"exclude_keywords":[]}}}`

		cmdinfo := CmdInfo{Command: "forward raw-json config " + jsonConfig}
		result := ForwardCmd(&cmdinfo)
		if result.Status != "ok" {
			t.Errorf("Expected raw-json config command to succeed, got: %v", result.Status)
		}

		// Verify config was applied
		config, ok := QC.ForwardSvcData[QC.Name]
		if !ok {
			t.Errorf("Expected config to exist after raw-json config")
		} else {
			if !config.WhatsApp.Enabled {
				t.Errorf("Expected WhatsApp to be enabled")
			}
			if config.WhatsApp.AccountSID != "test_sid" {
				t.Errorf("Expected AccountSID to be 'test_sid', got: %v", config.WhatsApp.AccountSID)
			}
		}

		// Test invalid JSON
		cmdinfo = CmdInfo{Command: "forward raw-json config {invalid json}"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: failed to parse raw config") {
			t.Errorf("Expected JSON parse error, got: %v", result.Status)
		}

		// Test missing parameters
		cmdinfo = CmdInfo{Command: "forward raw-json config"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "usage: forward raw-json config") {
			t.Errorf("Expected usage error, got: %v", result.Status)
		}

		// Test wrong command format
		cmdinfo = CmdInfo{Command: "forward raw-json invalid"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "unknown forward command") {
			t.Errorf("Expected unknown forward command, got: %v", result.Status)
		}
	})

	// Tests for "forward upload"
	t.Run("ForwardUploadConfigCmd", func(t *testing.T) {
		// Set up a config to upload
		config := DefaultForwardConfig()
		config.WhatsApp.Enabled = true
		config.WhatsApp.AccountSID = "test_upload_sid"
		QC.ForwardSvcData[QC.Name] = *config

		// Note: This test will fail because it tries to actually upload to root service
		// In a real environment, you might want to mock the HTTP client or set up test server
		cmdinfo := CmdInfo{Command: "forward upload"}
		result := ForwardCmd(&cmdinfo)

		// We expect this to fail in test environment since no root service is running
		// But we can verify the command parsing works
		if !strings.Contains(result.Status, "error:") && result.Status != "ok" {
			t.Errorf("Expected either error (no root service) or success, got: %v", result.Status)
		}

		// Test with no config
		delete(QC.ForwardSvcData, QC.Name)
		cmdinfo = CmdInfo{Command: "forward upload"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: no forward configuration found") {
			t.Errorf("Expected no config error, got: %v", result.Status)
		}
	})

	// Tests for "forward send"
	t.Run("ForwardSendCmd", func(t *testing.T) {
		// Set up a test config
		config := DefaultForwardConfig()
		config.WhatsApp.Enabled = true
		QC.ForwardSvcData[QC.Name] = *config

		// Initialize alert forwarder
		forwarder, err := NewAlertForwarder(config)
		if err != nil {
			t.Fatalf("Failed to create alert forwarder: %v", err)
		}
		QC.AlertForwarder = forwarder

		// Test valid send command
		cmdinfo := CmdInfo{Command: "forward send 1 5 TestTag This is a test message"}
		result := ForwardCmd(&cmdinfo)
		if result.Status != "ok" {
			t.Errorf("Expected send command to succeed, got: %v", result.Status)
		}

		// Verify result contains expected data
		if result.Result == "" {
			t.Errorf("Expected result to contain forwarding data")
		}

		// Test invalid facility
		cmdinfo = CmdInfo{Command: "forward send invalid 5 TestTag message"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: invalid facility") {
			t.Errorf("Expected invalid facility error, got: %v", result.Status)
		}

		// Test invalid severity
		cmdinfo = CmdInfo{Command: "forward send 1 invalid TestTag message"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: invalid severity") {
			t.Errorf("Expected invalid severity error, got: %v", result.Status)
		}

		// Test severity out of range
		cmdinfo = CmdInfo{Command: "forward send 1 8 TestTag message"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: severity must be between 0-7") {
			t.Errorf("Expected severity range error, got: %v", result.Status)
		}

		// Test missing parameters
		cmdinfo = CmdInfo{Command: "forward send 1 5"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "usage: forward send") {
			t.Errorf("Expected usage error, got: %v", result.Status)
		}

		// Test empty message
		cmdinfo = CmdInfo{Command: "forward send 1 5 TestTag"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "usage: forward send") {
			t.Errorf("Expected usage error for missing message, got: %v", result.Status)
		}

		// Test with no config
		delete(QC.ForwardSvcData, QC.Name)
		cmdinfo = CmdInfo{Command: "forward send 1 5 TestTag message"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: no forward configuration found") {
			t.Errorf("Expected no config error, got: %v", result.Status)
		}

		// Cleanup
		forwarder.Stop()
	})

	// Tests for "forward custom send"
	t.Run("ForwardCustomSendCmd", func(t *testing.T) {
		// Test valid custom send command
		customConfig := `{"whatsapp":{"enabled":true,"account_sid":"custom_sid","auth_token":"custom_token","from_number":"+**********","to_numbers":["+**********"],"alert_config":{"min_severity":0,"max_severity":7,"rate_limit_seconds":300,"max_alerts_per_minute":5,"keywords":[],"exclude_keywords":[]}},"telegram":{"enabled":false},"mqtt":{"enabled":false}}`

		cmdinfo := CmdInfo{Command: "forward custom send " + customConfig + " 1 5 CustomTag This is a custom test message"}
		result := ForwardCmd(&cmdinfo)
		if result.Status != "ok" {
			t.Errorf("Expected custom send command to succeed, got: %v", result.Status)
		}

		// Verify result contains expected data
		if result.Result == "" {
			t.Errorf("Expected result to contain forwarding data")
		}

		// Test invalid JSON
		cmdinfo = CmdInfo{Command: "forward custom send {invalid json} 1 5 TestTag message"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: failed to parse custom config") {
			t.Errorf("Expected JSON parse error, got: %v", result.Status)
		}

		// Test missing JSON closing brace
		cmdinfo = CmdInfo{Command: "forward custom send {\"test\":true 1 5 TestTag message"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: invalid JSON config format") {
			t.Errorf("Expected JSON format error, got: %v", result.Status)
		}

		// Test missing parameters
		cmdinfo = CmdInfo{Command: "forward custom send"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "usage: forward custom send") {
			t.Errorf("Expected usage error, got: %v", result.Status)
		}

		// Test wrong command format
		cmdinfo = CmdInfo{Command: "forward custom invalid"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "unknown forward command") {
			t.Errorf("Expected unknown forward command, got: %v", result.Status)
		}

		// Test missing syslog parameters after JSON
		cmdinfo = CmdInfo{Command: "forward custom send " + customConfig}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: usage: forward custom send") {
			t.Errorf("Expected usage error, got: %v", result.Status)
		}

		// Test insufficient syslog parameters
		cmdinfo = CmdInfo{Command: "forward custom send " + customConfig + " 1 5"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "usage: forward custom send") {
			t.Errorf("Expected usage error for insufficient parameters, got: %v", result.Status)
		}

		// Test invalid facility in custom send
		cmdinfo = CmdInfo{Command: "forward custom send " + customConfig + " invalid 5 TestTag message"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: invalid facility") {
			t.Errorf("Expected invalid facility error, got: %v", result.Status)
		}

		// Test invalid severity in custom send
		cmdinfo = CmdInfo{Command: "forward custom send " + customConfig + " 1 invalid TestTag message"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: invalid severity") {
			t.Errorf("Expected invalid severity error, got: %v", result.Status)
		}

		// Test severity out of range in custom send
		cmdinfo = CmdInfo{Command: "forward custom send " + customConfig + " 1 8 TestTag message"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "error: severity must be between 0-7") {
			t.Errorf("Expected severity range error, got: %v", result.Status)
		}

		// Test empty message in custom send
		cmdinfo = CmdInfo{Command: "forward custom send " + customConfig + " 1 5 TestTag"}
		result = ForwardCmd(&cmdinfo)
		if !strings.Contains(result.Status, "usage: forward custom send") {
			t.Errorf("Expected usage error for missing message, got: %v", result.Status)
		}
	})

	t.Run("WhatsAppConnection", func(t *testing.T) {
		// Create a mock Twilio API server
		mockTwilioServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Check that it's the correct endpoint
			if !strings.Contains(r.URL.Path, "/Messages.json") {
				w.WriteHeader(http.StatusNotFound)
				return
			}

			// Check authentication
			username, password, ok := r.BasicAuth()
			if !ok || username != "test_sid" || password != "test_token" {
				w.WriteHeader(http.StatusUnauthorized)
				return
			}

			// Check content type
			if r.Header.Get("Content-Type") != "application/x-www-form-urlencoded" {
				w.WriteHeader(http.StatusBadRequest)
				return
			}

			// Parse form data
			err := r.ParseForm()
			if err != nil {
				w.WriteHeader(http.StatusBadRequest)
				return
			}

			// Validate required fields
			if r.FormValue("From") == "" || r.FormValue("To") == "" || r.FormValue("Body") == "" {
				w.WriteHeader(http.StatusBadRequest)
				return
			}

			// Return success response (Twilio returns 201 for successful message creation)
			w.WriteHeader(http.StatusCreated)
			w.Write([]byte(`{"sid": "test_message_sid", "status": "queued"}`))
		}))
		defer mockTwilioServer.Close()

		// Set up config with test credentials
		config := DefaultForwardConfig()
		config.WhatsApp.Enabled = true
		config.WhatsApp.AccountSID = "test_sid"
		config.WhatsApp.AuthToken = "test_token"
		config.WhatsApp.FromNumber = "+**********"
		config.WhatsApp.ToNumbers = []string{"+**********"}

		// Create alert forwarder
		forwarder, err := NewAlertForwarder(config)
		if err != nil {
			t.Fatalf("Failed to create alert forwarder: %v", err)
		}
		defer forwarder.Stop()

		// Replace the Twilio URL in the forwarder's sendWhatsAppMessage method
		// This is a bit tricky since we need to modify the URL being called
		// For now, we'll test the message formatting instead

		// Test message formatting
		entry := &SyslogEntry{
			Facility: 16,
			Severity: 3,
			Tag:      "TestTag",
			Message:  "Test WhatsApp message",
			Hostname: "testhost",
		}

		message := forwarder.formatWhatsAppMessage(entry)
		if !strings.Contains(message, "Test WhatsApp message") {
			t.Errorf("Expected message to contain test content, got: %v", message)
		}
		if !strings.Contains(message, "TestTag") {
			t.Errorf("Expected message to contain tag, got: %v", message)
		}

		// Note: To properly test the HTTP call, we would need to inject the mock server URL
		// or use dependency injection for the HTTP client
		t.Log("WhatsApp message formatting test passed")
	})

	t.Run("TelegramConnection", func(t *testing.T) {
		// Create a mock Telegram API server
		mockTelegramServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Check that it's the correct endpoint
			if !strings.Contains(r.URL.Path, "/sendMessage") {
				w.WriteHeader(http.StatusNotFound)
				return
			}

			// Check content type
			if r.Header.Get("Content-Type") != "application/json" {
				w.WriteHeader(http.StatusBadRequest)
				return
			}

			// Read and parse JSON body
			body, err := io.ReadAll(r.Body)
			if err != nil {
				w.WriteHeader(http.StatusBadRequest)
				return
			}

			var payload map[string]interface{}
			if err := json.Unmarshal(body, &payload); err != nil {
				w.WriteHeader(http.StatusBadRequest)
				return
			}

			// Validate required fields
			if payload["chat_id"] == "" || payload["text"] == "" {
				w.WriteHeader(http.StatusBadRequest)
				return
			}

			// Return success response
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"ok": true, "result": {"message_id": 123}}`))
		}))
		defer mockTelegramServer.Close()

		// Set up config with test credentials
		config := DefaultForwardConfig()
		config.Telegram.Enabled = true
		config.Telegram.BotToken = "123456789:TEST_BOT_TOKEN"
		config.Telegram.ChatIDs = []string{"12345", "67890"}

		// Create alert forwarder
		forwarder, err := NewAlertForwarder(config)
		if err != nil {
			t.Fatalf("Failed to create alert forwarder: %v", err)
		}
		defer forwarder.Stop()

		// Test message formatting
		entry := &SyslogEntry{
			Facility: 16,
			Severity: 3,
			Tag:      "TestTag",
			Message:  "Test Telegram message",
			Hostname: "testhost",
		}

		message := forwarder.formatTelegramMessage(entry)
		if !strings.Contains(message, "Test Telegram message") {
			t.Errorf("Expected message to contain test content, got: %v", message)
		}
		if !strings.Contains(message, "TestTag") {
			t.Errorf("Expected message to contain tag, got: %v", message)
		}

		// Note: To properly test the HTTP call, we would need to inject the mock server URL
		// or use dependency injection for the HTTP client
		t.Log("Telegram message formatting test passed")
	})

	t.Run("MQTTConnection", func(t *testing.T) {
		// Start an embedded MQTT broker for testing
		server := MQTTBroker.New()
		port, err := randomOpenPort()
		if err != nil {
			t.Fatalf("Failed to find an open port for MQTT broker: %v", err)
		}
		tcp := listeners.NewTCP("tcp", fmt.Sprintf(":%d", port))
		if tcp == nil {
			t.Fatalf("Failed to create TCP listener for MQTT broker")
		}
		err = server.AddListener(tcp, nil)
		if err != nil {
			t.Fatalf("Failed to add TCP listener: %v", err)
		}

		go func() {
			err := server.Serve()
			if err != nil {
				t.Logf("MQTT server error: %v", err)
			}
		}()

		// Wait a moment for the server to start
		time.Sleep(100 * time.Millisecond)
		// Get the actual port the server is listening on using reflection to access the unexported field 'l'
		brokerAddr := fmt.Sprintf("localhost:%d", port)
		t.Logf("MQTT broker started on %s", brokerAddr)

		// Set up a subscriber to receive messages
		var receivedMessages []string
		subscriber := mqtt.NewClient(mqtt.NewClientOptions().
			AddBroker("tcp://" + brokerAddr).
			SetClientID("test-subscriber"))

		if token := subscriber.Connect(); token.Wait() && token.Error() != nil {
			t.Fatalf("Failed to connect subscriber: %v", token.Error())
		}
		defer subscriber.Disconnect(250)

		// Subscribe to the test topic
		if token := subscriber.Subscribe("test/alerts", 0, func(client mqtt.Client, msg mqtt.Message) {
			receivedMessages = append(receivedMessages, string(msg.Payload()))
			t.Logf("Received MQTT message: %s", string(msg.Payload()))
		}); token.Wait() && token.Error() != nil {
			t.Fatalf("Failed to subscribe: %v", token.Error())
		}

		// Set up forwarder config to use our test broker
		cmdinfo := CmdInfo{Command: "forward config -mqtt=true -mqtt_broker_host=localhost -mqtt_broker_port=" + fmt.Sprintf("%d", port) + " -mqtt_topic=test/alerts"}
		cmdinfo = *RunCmd(&cmdinfo)
		if cmdinfo.Status != "ok" {
			t.Errorf("Expected command to succeed, got: %v", cmdinfo.Status)
		}

		// Test sending a message through the forwarder
		entry := &SyslogEntry{
			Facility:  16,
			Severity:  3,
			Tag:       "TestTag",
			Message:   "Test MQTT alert message",
			Hostname:  "testhost",
			Timestamp: time.Now(),
		}

		// Process the alert (this should send it to MQTT)
		result := QC.AlertForwarder.processLogEntry(entry)

		// Give some time for the message to be published and received
		time.Sleep(200 * time.Millisecond)

		// Verify the message was sent
		if len(receivedMessages) == 0 {
			t.Errorf("Expected to receive MQTT message, but got none")
		} else {
			message := receivedMessages[0]
			if !strings.Contains(message, "Test MQTT alert message") {
				t.Errorf("Expected message to contain test content, got: %v", message)
			}
			if !strings.Contains(message, "TestTag") {
				t.Errorf("Expected message to contain tag, got: %v", message)
			}
			t.Logf("Successfully sent and received MQTT message: %s", message)
		}

		// Verify the result contains MQTT data
		if !strings.Contains(result.MQTTResult, "alert sent") {
			t.Logf("MQTT result: %s", result.MQTTResult) // Log for debugging
		}

		// Test that MQTT config is properly parsed
		config := QC.ForwardSvcData[QC.Name]
		if !strings.Contains(config.MQTT.BrokerHost, "localhost") {
			t.Errorf("Expected BrokerHost to contain %s, got: %v", brokerAddr, config.MQTT.BrokerHost)
		}
		if config.MQTT.Topic != "test/alerts" {
			t.Errorf("Expected Topic to be 'test/alerts', got: %v", config.MQTT.Topic)
		}

		// Reeset forwarder to ensure mqtt connection is closed properly and do not receive messages after test
		// use ForwardConfig to reset the forwarder
		cmdinfo = CmdInfo{Command: "forward config -mqtt=false"}
		cmdinfo = *RunCmd(&cmdinfo)
		if cmdinfo.Status != "ok" {
			t.Errorf("Expected command to succeed, got: %v", cmdinfo.Status)
		}
		// send a message after resetting the forwarder
		entry = &SyslogEntry{
			Facility:  16,
			Severity:  3,
			Tag:       "TestTag",
			Message:   "Test MQTT alert message after reset",
			Hostname:  "testhost",
			Timestamp: time.Now(),
		}
		receivedMessages = []string{} // Reset received messages
		result = QC.AlertForwarder.processLogEntry(entry)
		// Give some time for the message to be published and received
		time.Sleep(200 * time.Millisecond)
		// Verify no messages were received after reset
		if len(receivedMessages) > 0 {
			t.Errorf("Expected no MQTT messages after reset, but got: %v", len(receivedMessages))
		}
		if !strings.Contains(result.MQTTResult, "disabled") {
			t.Errorf("Expected MQTT result to indicate disabled, got: %v", result.MQTTResult)
		} else {
			t.Logf("MQTT connection successfully reset: %s", result.MQTTResult)

		}

		// Stop the MQTT server
		server.Close()
		t.Log("MQTT full integration test passed")
	})

}

// TestSyslogParsingFormats tests the core syslog parsing functionality
// that was failing with RFC5424 and Cisco format logs
func TestSyslogParsingFormats(t *testing.T) {
	config := DefaultForwardConfig()
	af, err := NewAlertForwarder(config)
	if err != nil {
		t.Fatalf("Failed to create AlertForwarder: %v", err)
	}
	defer af.Stop()

	// Test cases based on real-world logs that were failing
	tests := []struct {
		name     string
		line     string
		wantNil  bool
		facility int
		severity int
		hostname string
		tag      string
	}{
		{
			name:     "RFC5424_ISO8601_timestamp",
			line:     "<185>2017-01-19T06:13:21.690Z *********** syslog: Link Status: Port1 link is down.",
			facility: 23,
			severity: 1,
			hostname: "***********",
			tag:      "syslog",
		},
		{
			name:     "Cisco_format",
			line:     "<187>82: *Mar  1 08:13:42.296 UTC: %LINK-3-UPDOWN: Interface FastEthernet0/1, changed state to down",
			facility: 23,
			severity: 3,
			hostname: "*************",
			tag:      "LINK-3-UPDOWN",
		},
		{
			name:     "RFC3164_classic",
			line:     "<134>Dec 10 12:30:45 myhost myapp: This is a test message",
			facility: 16,
			severity: 6,
			hostname: "myhost",
			tag:      "myapp",
		},
		{
			name:    "Invalid_empty",
			line:    "",
			wantNil: true,
		},
		{
			name:    "Invalid_no_priority",
			line:    "Just a plain message without priority",
			wantNil: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// For tests, use a mock client address
			mockClientAddr := ""
			if tt.name == "Cisco_format" {
				mockClientAddr = "*************" // Mock Cisco device IP
			}

			entry := af.parseSyslogLine(tt.line, mockClientAddr)

			if tt.wantNil {
				if entry != nil {
					t.Errorf("Expected nil for invalid input, got: %+v", entry)
				}
				return
			}

			if entry == nil {
				t.Fatalf("Expected successful parsing, got nil for line: %s", tt.line)
			}

			if entry.Facility != tt.facility {
				t.Errorf("Facility: expected %d, got %d", tt.facility, entry.Facility)
			}
			if entry.Severity != tt.severity {
				t.Errorf("Severity: expected %d, got %d", tt.severity, entry.Severity)
			}
			if entry.Hostname != tt.hostname {
				t.Errorf("Hostname: expected %s, got %s", tt.hostname, entry.Hostname)
			}
			if entry.Tag != tt.tag {
				t.Errorf("Tag: expected %s, got %s", tt.tag, entry.Tag)
			}
			if entry.RawLine != tt.line {
				t.Errorf("RawLine not preserved correctly")
			}
		})
	}
}
