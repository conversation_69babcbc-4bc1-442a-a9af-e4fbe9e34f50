# Repository Guidelines

## Project Structure & Module Organization
The Go backend sits at the repository root; each service (such as `bbnmssvc`, `bbrootsvc`, `bbctl`) keeps domain code inside its directory with a local `Makefile`. Shared packages and entrypoints (`agent.go`, `config.go`, `daemon.go`) live beside them, and tests mirror production files as `*_test.go`. The React client is under `frontend/`, long-form docs live in `doc/`, build outputs land in `dist/`, and integration tooling lives in `testing/cluster-testing/`.

## Build, Test, and Development Commands
- `make dev` — build the Go services listed in `DEVCMDS` into `dist/` for local runs.
- `make lint` — aggregate `go vet`, `revive`, `golangci-lint`, and `staticcheck`.
- `go test ./...` — run all Go tests; add `-race` or `-cover` as needed.
- `npm install && npm run dev` (inside `frontend/`) — serve the UI on port 3000.
- `npm run build`, `npm run test`, `npm run test:cy` — build, unit-test, and exercise Cypress flows for the frontend.

## Coding Style & Naming Conventions
Format Go changes with `gofmt` (tabs) and keep packages focused; exported names use `CamelCase`, locals use `lowerCamel`, and wrap errors with `fmt.Errorf("...: %w", err)`. The frontend follows ESLint defaults: 2-space indents, `PascalCase` components, `useThing.ts` hooks, and filenames that reflect the primary export.

## Testing Guidelines
Keep Go tests in-package using the `TestXxx` naming pattern and favour table-driven cases. Track coverage with `go test -cover ./...` and prioritise stability in high-risk packages like `agent/` and `network`. Frontend specs live alongside components as `Component.test.tsx`, while Cypress suites belong in `frontend/cypress/e2e/`.

## Commit & Pull Request Guidelines
Write imperative, ≤72-character commit subjects (e.g., `feat: add syslog throttling`) and reference tickets with `Fixes #123` or `Refs #123`. PR descriptions should outline the problem, solution, and verification commands (`make lint`, `go test ./...`, `npm run test`), plus screenshots or logs for UI updates. Request reviews from owners listed in CODEOWNERS and merge only after CI passes.

## Security & Configuration Tips
Do not commit secrets; store examples in `doc/` or `examples/` and load real values through the environment as read by `config.go`. Frontend `.env` files stay local (see `frontend/README.md`), and clear stale bundles with `rm -rf dist && make release_build` before packaging.
