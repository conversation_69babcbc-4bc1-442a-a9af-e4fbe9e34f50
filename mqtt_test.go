package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os/exec"
	"runtime"
	"strings"
	"testing"
	"time"

	MQTTClient "github.com/eclipse/paho.mqtt.golang"
	"github.com/influxdata/go-syslog/v3"
	MQTTBroker "github.com/mochi-co/mqtt/server"
	"github.com/mochi-co/mqtt/server/listeners"
	"github.com/qeof/q"
)

func TestRunMqttBroker(t *testing.T) {
	broker := MQTTBroker.NewServer(nil)
	tcp := listeners.NewTCP("t1", QC.MqttBrokerAddr)
	err := broker.AddListener(tcp, nil)
	if err != nil {
		t.Error(err)
	}
	err = broker.Serve()
	if err != nil {
		t.Error(err)
		return
	}
	defer broker.Close()
}

func TestRunMqttClient(t *testing.T) {
	go func() {
		_ = RunMqttBroker("TestRunMqttClient")
	}()
	time.Sleep(500 * time.Millisecond)

	opts := MQTTClient.NewClientOptions().AddBroker(QC.MqttBrokerAddr).SetClientID("testmqtt")
	opts.SetKeepAlive(keepAlive * time.Second)
	opts.SetPingTimeout(pingTimeout * time.Second)
	client := MQTTClient.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		t.Error(token.Error())
	} else {
		t.Log("subscribe")

		err := RunMqttSubscribe(":11883", "testtopic1")
		if err != nil {
			t.Error(err)
		}
		err = RunMqttSubscribe(":11883", "testtopic2")
		if err != nil {
			t.Error(err)
		}
		err = RunMqttPublish(":11883", "testtopic1", "test messages1")
		if err != nil {
			t.Error(err)
		}
		err = RunMqttPublish(":11883", "testtopic2", "test messages2")
		if err != nil {
			t.Error(err)
		}
		time.Sleep(250 * time.Millisecond)
		err = RunMqttUnSubscribe(":11883", "testtopic1")
		if err != nil {
			t.Error(err)
		}
		err = RunMqttUnSubscribe(":11883", "testtopic2")
		if err != nil {
			t.Error(err)
		}
		err = RunMqttPublish(":11883", "testtopic1", "test messages1")
		if err != nil {
			t.Error(err)
		}
		err = RunMqttPublish(":11883", "testtopic2", "test messages2")
		if err != nil {
			t.Error(err)
		}
		time.Sleep(250 * time.Millisecond)

	}

}

func PostCmd(RootURL, token string, cmd string) error {
	cmdURL := RootURL + "/api/v1/commands"
	cmdJson := `[{"command":"` + cmd + `"}]`
	resp, err := PostWithToken(cmdURL, token, bytes.NewBuffer([]byte(cmdJson)))
	if err != nil {
		return err
	}
	if resp != nil {
		_, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("reading resp body %v", err)
		}
		//save close
		resp.Body.Close()
		if resp.StatusCode != 200 {
			return fmt.Errorf("post bad status %v", resp)
		}
	}
	return nil
}

func TestMqttClientWithRemoteServer(t *testing.T) {
	defer func() {
		killNmsctlProcesses()
	}()
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbrootsvc/bbrootsvc.exe", "-n", "root")
		} else {
			cmd = exec.Command("./bbrootsvc/bbrootsvc", "-n", "root")
		}
		err := cmd.Run()
		if err != nil {
			q.Q(err)
		}
	}()
	// wait root
	time.Sleep(3 * time.Second)
	// network service
	go func() {
		var cmd *exec.Cmd
		if runtime.GOOS == "windows" {
			cmd = exec.Command("./bbnmssvc/bbnmssvc.exe", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		} else {
			cmd = exec.Command("./bbnmssvc/bbnmssvc", "-n", "client", "-r", "http://localhost:27182", "-rs", "localhost:5514")
		}
		err := cmd.Run()
		if err != nil {
			q.Q(err)
		}
	}()
	// wait root
	time.Sleep(10 * time.Second)

	// start
	RootURL := "http://localhost:27182"

	//login
	loginurl := RootURL + "/api/v1/login"
	jsonBytes, err := json.Marshal(map[string]string{
		"user":     "admin",
		"password": "default",
	})
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	resp, err := http.Post(loginurl, "application/json", bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	if resp.StatusCode != http.StatusOK {
		resText, _ := io.ReadAll(resp.Body)
		t.Fatalf("error: %v\n", string(resText))
		return
	}
	if resp == nil {
		t.Fatalf("error: %v\n", "response should not be nil")
		return
	}
	defer resp.Body.Close()

	//get token
	var respBody map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&respBody)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	token, ok := respBody["token"].(string)
	if !ok {
		t.Fatalf("error: %v\n", "token is not string")
		return
	}

	// subscribe
	PostCmd(RootURL, token, "mqtt sub localhost:11883 topictest")
	time.Sleep(3 * time.Second)

	// publish
	PostCmd(RootURL, token, "mqtt pub localhost:11883 topictest teststring")

	time.Sleep(10 * time.Second)

	// get syslog
	syslogURL := RootURL + "/api/v1/syslogs?number=-1"
	syslogResp, err := GetWithToken(syslogURL, token)
	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	if syslogResp.StatusCode != 200 {
		syslogRespText, _ := io.ReadAll(syslogResp.Body)
		t.Fatalf("error: %v\n", string(syslogRespText))
		return
	}
	if syslogResp == nil {
		t.Fatalf("error: %v\n", "response should not be nil")
		return
	}
	defer syslogResp.Body.Close()

	var syslogRespBody []syslog.Base
	err = json.NewDecoder(syslogResp.Body).Decode(&syslogRespBody)

	if err != nil {
		t.Fatalf("error: %v\n", err)
		return
	}
	success := false
	for _, v := range syslogRespBody {
		if strings.Contains(*v.Message, "topictest") && strings.Contains(*v.Message, ":11883") {
			t.Logf("Message : %v\n", *v.Message)
			success = true
			break
		}
	}
	if success {
		t.Logf("Result : Success subscribe to localhost:11883\n")
	} else {
		t.Logf("Result : Fail subscribe to localhost:11883\n")
	}

	// unsubscribe
	PostCmd(RootURL, token, "mqtt unsub localhost:11883 topictest")
	time.Sleep(3 * time.Second)
}
