package mnms

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"fmt"

	"github.com/qeof/q"
)

func SetPrivateKey(key string) {
	mnmsOwnPrivateKeyPEM = key
}

func GetDefaultPrivateKey() string {
	return mnmsOwnPrivateKeyPEM
}

// hardcode rsa private key
var mnmsOwnPrivateKeyPEM = `
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

/*
  This file consis of bunch of functions related to crypto stuff.
*/

// GenerateOwnPublickey generate own public key
func GenerateOwnPublickey() ([]byte, error) {
	pri, err := GetPrivateKeyFromPEM([]byte(mnmsOwnPrivateKeyPEM))
	if err != nil {
		q.Q(err)
		return nil, err
	}
	pub, err := GenerateRSAPublickey(pri)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	return pub, nil
}

func GenerateRSAKeyPair(bits int) (*rsa.PrivateKey, error) {

	return rsa.GenerateKey(rand.Reader, bits)
}

// EndcodePrivateKeyToPEM encode private key to bytes
func EndcodePrivateKeyToPEM(privateKey *rsa.PrivateKey) ([]byte, error) {
	// privatekey to pkcs8 pem

	privateKeyBytes, err := x509.MarshalPKCS8PrivateKey(privateKey)
	if err != nil {
		return nil, err
	}
	b := pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	}
	return pem.EncodeToMemory(&b), nil
}

// GetPrivateKeyFromPEM get private key from bytes
func GetPrivateKeyFromPEM(privateKeyBytes []byte) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode(privateKeyBytes)
	if block == nil {
		return nil, errors.New("failed to parse PEM block containing the key")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return nil, errors.New("failed to parse RSA private key")
	}
	return rsaPrivateKey, nil
}

// GenerateRSAPublickey generate public key from private key
func GenerateRSAPublickey(privateKey *rsa.PrivateKey) ([]byte, error) {
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	b := pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	}
	return pem.EncodeToMemory(&b), nil
}

// EncryptWithPublicKey encrypt data with private key PEM
func EncryptWithPublicKey(data []byte, publicKey []byte) ([]byte, error) {

	block, _ := pem.Decode(publicKey)
	if block == nil {
		q.Q("public key error")
		return nil, fmt.Errorf("public key error")
	}

	pubInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		q.Q(err)
		return nil, err
	}
	pub := pubInterface.(*rsa.PublicKey)
	hash := sha256.New()
	setp := pub.Size() - 2*hash.Size() - 2
	mlen := len(data)
	var ciphertext []byte
	for i := 0; i < mlen; i += setp {
		if i+setp > mlen {
			chunk, err := rsa.EncryptOAEP(hash, rand.Reader, pub, data[i:], nil)
			if err != nil {
				q.Q(err)
				return nil, err
			}
			ciphertext = append(ciphertext, chunk...)
		} else {
			chunk, err := rsa.EncryptOAEP(hash, rand.Reader, pub, data[i:i+setp], nil)
			if err != nil {
				q.Q(err)
				return nil, err
			}
			ciphertext = append(ciphertext, chunk...)
		}
	}
	base64Text := base64.StdEncoding.EncodeToString(ciphertext)
	return []byte(base64Text), err

}

// DecryptWithOwnPrivateKey decrypt data with own private key
func DecryptWithOwnPrivateKey(data []byte) ([]byte, error) {
	// get private key
	return DecryptWithPrivateKeyPEM(data, []byte(mnmsOwnPrivateKeyPEM))
}

// DecryptWithPrivateKeyPEM decrypt data with private key PEM
func DecryptWithPrivateKeyPEM(txtdata []byte, privateKey []byte) ([]byte, error) {
	data, err := base64.StdEncoding.DecodeString(string(txtdata))
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(privateKey)
	if block == nil {
		q.Q("private key error")
		return nil, fmt.Errorf("private key error")
	}
	priv, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		q.Q(err)
		return nil, err
	}

	rsaPrivateKey, ok := priv.(*rsa.PrivateKey)
	if !ok {
		return nil, errors.New("failed to parse RSA private key")
	}

	mlen := len(data)
	hash := sha256.New()
	setp := rsaPrivateKey.PublicKey.Size()
	var plaintext []byte
	for i := 0; i < mlen; i += setp {
		if i+setp > mlen {
			chunk, err := rsa.DecryptOAEP(hash, rand.Reader, rsaPrivateKey, data[i:], nil)
			if err != nil {
				q.Q(err)
				return nil, err
			}
			plaintext = append(plaintext, chunk...)
		} else {
			chunk, err := rsa.DecryptOAEP(hash, rand.Reader, rsaPrivateKey, data[i:i+setp], nil)
			if err != nil {
				q.Q(err)
				return nil, err
			}
			plaintext = append(plaintext, chunk...)
		}
	}

	return plaintext, err
}
