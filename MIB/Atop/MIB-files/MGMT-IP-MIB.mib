-- *****************************************************************
-- IP-MIB:  
-- ****************************************************************

MGMT-IP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Counter64 FROM SNMPv2-SMI
    Integer32 FROM SNMPv2-<PERSON>I
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned64 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtIpMib MODULE-IDENTITY
    LAST-UPDATED "202011060000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private IP MIB."
    REVISION    "202011060000Z"
    DESCRIPTION
        "Added global status trap to indicate H/W routing table depletion."
    REVISION    "202006250000Z"
    DESCRIPTION
        "Non-backwards compatible change in ipStatusRoutesIpv4 and
         ipStatusRoutesIpv6 to make snmp-walk work for multiple link-local IPv6
         addresses."
    REVISION    "202003050000Z"
    DESCRIPTION
        "Non-backwards compatible changes due to (re-)introduction of dynamic
         routing."
    REVISION    "201805290000Z"
    DESCRIPTION
        "Replace 'protoConneted(1)' with 'protoConnected(1)'."
    REVISION    "201801300000Z"
    DESCRIPTION
        "Add IPv4 route distance configuration and routing information base
         table."
    REVISION    "201705240000Z"
    DESCRIPTION
        "Add DHCP client hostname and client identifier configuration."
    REVISION    "201607280000Z"
    DESCRIPTION
        "Add ability to clear address conflict detection table."
    REVISION    "201508240000Z"
    DESCRIPTION
        "Add capability for IPv4/IPv6 statistics availability."
    REVISION    "201410290000Z"
    DESCRIPTION
        "Removed the fields arpRetransmitTime and reasmMaxSize."
    REVISION    "201410210000Z"
    DESCRIPTION
        "Added arpCheck in the DhcpClientState enum"
    REVISION    "201409110000Z"
    DESCRIPTION
        "Revise definition of IPv4/IPv6 interface status table index."
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version."
    ::= { mgmtSwitch 102 }


MGMTIpDhcpClientState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The state of the DHCP client"
    SYNTAX      INTEGER { stopped(0), init(1), selecting(2),
                          requesting(3), rebinding(4), bound(5),
                          renewing(6), fallback(7), arpCheck(8) }

MGMTIpDhcpClientType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The type of the DHCP client identifier."
    SYNTAX      INTEGER { auto(0), ifmac(1), ascii(2), hex(3) }

MGMTIpRouteProtocol ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The protocol of the route."
    SYNTAX      INTEGER { protoKernel(0), protoDhcp(1),
                          protoConnected(2), protoStatic(3),
                          protoOspf(4), protoRip(5), protoUnknown(6) }

mgmtIpMibObjects OBJECT IDENTIFIER
    ::= { mgmtIpMib 1 }

mgmtIpCapabilities OBJECT IDENTIFIER
    ::= { mgmtIpMibObjects 1 }

mgmtIpCapabilitiesHasIpv4HostCapabilities OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The device has IPv4 host capabilities for management."
    ::= { mgmtIpCapabilities 1 }

mgmtIpCapabilitiesHasIpv6HostCapabilities OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The device has IPv6 host capabilities for management."
    ::= { mgmtIpCapabilities 2 }

mgmtIpCapabilitiesHasIpv4UnicastRoutingCapabilities OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The device has IPv4 unicast routing capabilities."
    ::= { mgmtIpCapabilities 3 }

mgmtIpCapabilitiesHasIpv4UnicastHwRoutingCapabilities OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The device has IPv4 unicast hardware accelerated routing capabilities."
    ::= { mgmtIpCapabilities 4 }

mgmtIpCapabilitiesHasIpv6UnicastRoutingCapabilities OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The device has IPv6 unicast routing capabilities."
    ::= { mgmtIpCapabilities 5 }

mgmtIpCapabilitiesHasIpv6UnicastHwRoutingCapabilities OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The device has IPv6 unicast hardware accelerated routing capabilities."
    ::= { mgmtIpCapabilities 6 }

mgmtIpCapabilitiesMaxNumberOfIpInterfaces OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of IP interfaces supported by the device."
    ::= { mgmtIpCapabilities 7 }

mgmtIpCapabilitiesMaxNumberOfStaticRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum number of static configured IP routes (shared by IPv4 and
         IPv6)."
    ::= { mgmtIpCapabilities 8 }

mgmtIpCapabilitiesNumberOfLpmHardwareEntries OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of hardware LPM (longest prefix match) entries."
    ::= { mgmtIpCapabilities 9 }

mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports detailed IPv4 statistics per interface."
    ::= { mgmtIpCapabilities 10 }

mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports detailed IPv6 statistics per interface."
    ::= { mgmtIpCapabilities 11 }

mgmtIpConfig OBJECT IDENTIFIER
    ::= { mgmtIpMibObjects 2 }

mgmtIpConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtIpConfig 1 }

mgmtIpConfigGlobalsMain OBJECT IDENTIFIER
    ::= { mgmtIpConfigGlobals 1 }

mgmtIpConfigGlobalsMainEnableRouting OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable routing."
    ::= { mgmtIpConfigGlobalsMain 1 }

mgmtIpConfigInterfaces OBJECT IDENTIFIER
    ::= { mgmtIpConfig 2 }

mgmtIpConfigInterfacesTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpConfigInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the IP interface table. When an IP interface is created it can
         be configured in the other tables found in this MIB."
    ::= { mgmtIpConfigInterfaces 1 }

mgmtIpConfigInterfacesEntry OBJECT-TYPE
    SYNTAX      MGMTIpConfigInterfacesEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Entries in this table represent IP interfaces created on the system"
    INDEX       { mgmtIpConfigInterfacesIfIndex }
    ::= { mgmtIpConfigInterfacesTable 1 }

MGMTIpConfigInterfacesEntry ::= SEQUENCE {
    mgmtIpConfigInterfacesIfIndex  MGMTInterfaceIndex,
    mgmtIpConfigInterfacesMtu      Unsigned32,
    mgmtIpConfigInterfacesAction   MGMTRowEditorState
}

mgmtIpConfigInterfacesIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpConfigInterfacesEntry 1 }

mgmtIpConfigInterfacesMtu OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MTU for this IP interface."
    ::= { mgmtIpConfigInterfacesEntry 2 }

mgmtIpConfigInterfacesAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpConfigInterfacesEntry 100 }

mgmtIpConfigInterfacesTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtIpConfigInterfaces 2 }

mgmtIpConfigInterfacesTableRowEditorIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpConfigInterfacesTableRowEditor 1 }

mgmtIpConfigInterfacesTableRowEditorMtu OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MTU for this IP interface."
    ::= { mgmtIpConfigInterfacesTableRowEditor 2 }

mgmtIpConfigInterfacesTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpConfigInterfacesTableRowEditor 100 }

mgmtIpConfigInterfacesIpv4Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpConfigInterfacesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "IPv4 interface configuration table. This table enables IPv4 related
         configuration of the corresponding IP interface."
    ::= { mgmtIpConfigInterfaces 3 }

mgmtIpConfigInterfacesIpv4Entry OBJECT-TYPE
    SYNTAX      MGMTIpConfigInterfacesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IP interface."
    INDEX       { mgmtIpConfigInterfacesIpv4IfIndex }
    ::= { mgmtIpConfigInterfacesIpv4Table 1 }

MGMTIpConfigInterfacesIpv4Entry ::= SEQUENCE {
    mgmtIpConfigInterfacesIpv4IfIndex                    MGMTInterfaceIndex,
    mgmtIpConfigInterfacesIpv4Active                     TruthValue,
    mgmtIpConfigInterfacesIpv4EnableDhcpClient           TruthValue,
    mgmtIpConfigInterfacesIpv4Ipv4Address                IpAddress,
    mgmtIpConfigInterfacesIpv4PrefixSize                 Unsigned32,
    mgmtIpConfigInterfacesIpv4DhcpClientFallbackTimeout  Unsigned32,
    mgmtIpConfigInterfacesIpv4DhcpClientHostname         MGMTDisplayString,
    mgmtIpConfigInterfacesIpv4DhcpClientIdType           MGMTIpDhcpClientType,
    mgmtIpConfigInterfacesIpv4DhcpClientIdIfMac          MGMTInterfaceIndex,
    mgmtIpConfigInterfacesIpv4DhcpClientIdAscii          MGMTDisplayString,
    mgmtIpConfigInterfacesIpv4DhcpClientIdHex            MGMTDisplayString
}

mgmtIpConfigInterfacesIpv4IfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpConfigInterfacesIpv4Entry 1 }

mgmtIpConfigInterfacesIpv4Active OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable IPv4.
         
         IPv4 can only be enabled if either the DHCP client is enabled, or a
         valid address has been configured."
    ::= { mgmtIpConfigInterfacesIpv4Entry 2 }

mgmtIpConfigInterfacesIpv4EnableDhcpClient OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable IPv4 DHCP client.
         
         Note: the DHCP client can only be enabled if there is no conflict in
         the values of: ipv4Address, prefixSize, and dhcpClientFallbackTimeout."
    ::= { mgmtIpConfigInterfacesIpv4Entry 3 }

mgmtIpConfigInterfacesIpv4Ipv4Address OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 address.
         
         Note: Multiple interfaces may not have overlapping networks."
    ::= { mgmtIpConfigInterfacesIpv4Entry 4 }

mgmtIpConfigInterfacesIpv4PrefixSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Prefix size of the network.
         
         Note: Multiple interfaces may not have overlapping networks."
    ::= { mgmtIpConfigInterfacesIpv4Entry 5 }

mgmtIpConfigInterfacesIpv4DhcpClientFallbackTimeout OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "DHCP client fallback timer.
         
         If DHCP is disabled then this object has no effect. If DHCP is enabled
         and the fallback timeout value is different from zero, then this timer
         will stop the DHCP process and assign the ipv4Address to the interface
         instead."
    ::= { mgmtIpConfigInterfacesIpv4Entry 6 }

mgmtIpConfigInterfacesIpv4DhcpClientHostname OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..63))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The hostname of DHCP client. If DHCPv4 client is enabled, the
         configured hostname will be used in the DHCP option 12 field. When this
         value is empty string, the field use the configured system name plus
         the latest three bytes of system MAC addresses as the hostname."
    ::= { mgmtIpConfigInterfacesIpv4Entry 7 }

mgmtIpConfigInterfacesIpv4DhcpClientIdType OBJECT-TYPE
    SYNTAX      MGMTIpDhcpClientType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The type of the DHCP client identifier."
    ::= { mgmtIpConfigInterfacesIpv4Entry 8 }

mgmtIpConfigInterfacesIpv4DhcpClientIdIfMac OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The interface name of DHCP client identifier. When DHCPv4 client is
         enabled and the client identifier type is 'ifmac', the configured
         interface's hardware MAC address will be used in the DHCP option 61
         field."
    ::= { mgmtIpConfigInterfacesIpv4Entry 9 }

mgmtIpConfigInterfacesIpv4DhcpClientIdAscii OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ASCII string of DHCP client identifier. When DHCPv4 client is
         enabled and the client identifier type is 'ascii', the ASCII string
         will be used in the DHCP option 61 field."
    ::= { mgmtIpConfigInterfacesIpv4Entry 10 }

mgmtIpConfigInterfacesIpv4DhcpClientIdHex OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..64))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The hexadecimal string of DHCP client identifier. When DHCPv4 client is
         enabled and the client identifier type 'hex', the hexadecimal value
         will be used in the DHCP option 61 field."
    ::= { mgmtIpConfigInterfacesIpv4Entry 11 }

mgmtIpConfigInterfacesIpv6Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpConfigInterfacesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "IPv6 interface configuration table. This table enables IPv6 related
         configuration of the corresponding IP interface."
    ::= { mgmtIpConfigInterfaces 4 }

mgmtIpConfigInterfacesIpv6Entry OBJECT-TYPE
    SYNTAX      MGMTIpConfigInterfacesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IP interface."
    INDEX       { mgmtIpConfigInterfacesIpv6IfIndex }
    ::= { mgmtIpConfigInterfacesIpv6Table 1 }

MGMTIpConfigInterfacesIpv6Entry ::= SEQUENCE {
    mgmtIpConfigInterfacesIpv6IfIndex      MGMTInterfaceIndex,
    mgmtIpConfigInterfacesIpv6Active       TruthValue,
    mgmtIpConfigInterfacesIpv6Ipv6Address  InetAddressIPv6,
    mgmtIpConfigInterfacesIpv6PrefixSize   Unsigned32
}

mgmtIpConfigInterfacesIpv6IfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpConfigInterfacesIpv6Entry 1 }

mgmtIpConfigInterfacesIpv6Active OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable the static configured IPv6 address.
         
         The static configured IPv6 address can only be configured if a valid
         address has been written into 'ipv6Address' and 'prefixSize'."
    ::= { mgmtIpConfigInterfacesIpv6Entry 2 }

mgmtIpConfigInterfacesIpv6Ipv6Address OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Static configured IPv6 address."
    ::= { mgmtIpConfigInterfacesIpv6Entry 3 }

mgmtIpConfigInterfacesIpv6PrefixSize OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Prefix size of the network."
    ::= { mgmtIpConfigInterfacesIpv6Entry 4 }

mgmtIpConfigRoutes OBJECT IDENTIFIER
    ::= { mgmtIpConfig 3 }

mgmtIpConfigRoutesIpv4Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpConfigRoutesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the IPv4 route configuration table."
    ::= { mgmtIpConfigRoutes 1 }

mgmtIpConfigRoutesIpv4Entry OBJECT-TYPE
    SYNTAX      MGMTIpConfigRoutesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents a configured route.
         
         Note: A route may be configured without being active."
    INDEX       { mgmtIpConfigRoutesIpv4NetworkAddress,
                  mgmtIpConfigRoutesIpv4NetworkPrefixSize,
                  mgmtIpConfigRoutesIpv4NextHop }
    ::= { mgmtIpConfigRoutesIpv4Table 1 }

MGMTIpConfigRoutesIpv4Entry ::= SEQUENCE {
    mgmtIpConfigRoutesIpv4NetworkAddress     IpAddress,
    mgmtIpConfigRoutesIpv4NetworkPrefixSize  Integer32,
    mgmtIpConfigRoutesIpv4NextHop            IpAddress,
    mgmtIpConfigRoutesIpv4Distance           MGMTUnsigned8,
    mgmtIpConfigRoutesIpv4Action             MGMTRowEditorState
}

mgmtIpConfigRoutesIpv4NetworkAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Network address."
    ::= { mgmtIpConfigRoutesIpv4Entry 1 }

mgmtIpConfigRoutesIpv4NetworkPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Network prefix size."
    ::= { mgmtIpConfigRoutesIpv4Entry 2 }

mgmtIpConfigRoutesIpv4NextHop OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Next-hop address."
    ::= { mgmtIpConfigRoutesIpv4Entry 3 }

mgmtIpConfigRoutesIpv4Distance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "distance value for this route "
    ::= { mgmtIpConfigRoutesIpv4Entry 5 }

mgmtIpConfigRoutesIpv4Action OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpConfigRoutesIpv4Entry 100 }

mgmtIpConfigRoutesIpv4RowEditor OBJECT IDENTIFIER
    ::= { mgmtIpConfigRoutes 2 }

mgmtIpConfigRoutesIpv4RowEditorNetworkAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Network address."
    ::= { mgmtIpConfigRoutesIpv4RowEditor 1 }

mgmtIpConfigRoutesIpv4RowEditorNetworkPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Network prefix size."
    ::= { mgmtIpConfigRoutesIpv4RowEditor 2 }

mgmtIpConfigRoutesIpv4RowEditorNextHop OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Next-hop address."
    ::= { mgmtIpConfigRoutesIpv4RowEditor 3 }

mgmtIpConfigRoutesIpv4RowEditorDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "distance value for this route "
    ::= { mgmtIpConfigRoutesIpv4RowEditor 5 }

mgmtIpConfigRoutesIpv4RowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpConfigRoutesIpv4RowEditor 100 }

mgmtIpConfigRoutesIpv6Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpConfigRoutesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the IPv6 route configuration table."
    ::= { mgmtIpConfigRoutes 3 }

mgmtIpConfigRoutesIpv6Entry OBJECT-TYPE
    SYNTAX      MGMTIpConfigRoutesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents a configured route.
         
         Note: a route may be configured without being active."
    INDEX       { mgmtIpConfigRoutesIpv6NetworkAddress,
                  mgmtIpConfigRoutesIpv6NetworkPrefixSize,
                  mgmtIpConfigRoutesIpv6NextHop,
                  mgmtIpConfigRoutesIpv6NextHopInterface }
    ::= { mgmtIpConfigRoutesIpv6Table 1 }

MGMTIpConfigRoutesIpv6Entry ::= SEQUENCE {
    mgmtIpConfigRoutesIpv6NetworkAddress     InetAddressIPv6,
    mgmtIpConfigRoutesIpv6NetworkPrefixSize  Integer32,
    mgmtIpConfigRoutesIpv6NextHop            InetAddressIPv6,
    mgmtIpConfigRoutesIpv6NextHopInterface   MGMTInterfaceIndex,
    mgmtIpConfigRoutesIpv6Distance           MGMTUnsigned8,
    mgmtIpConfigRoutesIpv6Action             MGMTRowEditorState
}

mgmtIpConfigRoutesIpv6NetworkAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Network address."
    ::= { mgmtIpConfigRoutesIpv6Entry 1 }

mgmtIpConfigRoutesIpv6NetworkPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Network prefix size."
    ::= { mgmtIpConfigRoutesIpv6Entry 2 }

mgmtIpConfigRoutesIpv6NextHop OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Next-hop address."
    ::= { mgmtIpConfigRoutesIpv6Entry 3 }

mgmtIpConfigRoutesIpv6NextHopInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "If the next-hop address is a link-local address, then the VLAN
         interface of the link-local address must be specified here. Otherwise
         this value is not used."
    ::= { mgmtIpConfigRoutesIpv6Entry 4 }

mgmtIpConfigRoutesIpv6Distance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "distance value for this route "
    ::= { mgmtIpConfigRoutesIpv6Entry 5 }

mgmtIpConfigRoutesIpv6Action OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpConfigRoutesIpv6Entry 100 }

mgmtIpConfigRoutesIpv6RowEditor OBJECT IDENTIFIER
    ::= { mgmtIpConfigRoutes 4 }

mgmtIpConfigRoutesIpv6RowEditorNetworkAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Network address."
    ::= { mgmtIpConfigRoutesIpv6RowEditor 1 }

mgmtIpConfigRoutesIpv6RowEditorNetworkPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Network prefix size."
    ::= { mgmtIpConfigRoutesIpv6RowEditor 2 }

mgmtIpConfigRoutesIpv6RowEditorNextHop OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Next-hop address."
    ::= { mgmtIpConfigRoutesIpv6RowEditor 3 }

mgmtIpConfigRoutesIpv6RowEditorNextHopInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If the next-hop address is a link-local address, then the VLAN
         interface of the link-local address must be specified here. Otherwise
         this value is not used."
    ::= { mgmtIpConfigRoutesIpv6RowEditor 4 }

mgmtIpConfigRoutesIpv6RowEditorDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "distance value for this route "
    ::= { mgmtIpConfigRoutesIpv6RowEditor 5 }

mgmtIpConfigRoutesIpv6RowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpConfigRoutesIpv6RowEditor 100 }

mgmtIpStatus OBJECT IDENTIFIER
    ::= { mgmtIpMibObjects 3 }

mgmtIpStatusGlobals OBJECT IDENTIFIER
    ::= { mgmtIpStatus 1 }

mgmtIpStatusGlobalsIpv4NeighborTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatusGlobalsIpv4NeighborEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the IPv4 neighbor (ARP) table."
    ::= { mgmtIpStatusGlobals 1 }

mgmtIpStatusGlobalsIpv4NeighborEntry OBJECT-TYPE
    SYNTAX      MGMTIpStatusGlobalsIpv4NeighborEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an entry in the underlying
         operating system's ARP table."
    INDEX       { mgmtIpStatusGlobalsIpv4NeighborIpv4 }
    ::= { mgmtIpStatusGlobalsIpv4NeighborTable 1 }

MGMTIpStatusGlobalsIpv4NeighborEntry ::= SEQUENCE {
    mgmtIpStatusGlobalsIpv4NeighborIpv4        IpAddress,
    mgmtIpStatusGlobalsIpv4NeighborMacAddress  MacAddress,
    mgmtIpStatusGlobalsIpv4NeighborInterface   MGMTInterfaceIndex
}

mgmtIpStatusGlobalsIpv4NeighborIpv4 OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 address."
    ::= { mgmtIpStatusGlobalsIpv4NeighborEntry 1 }

mgmtIpStatusGlobalsIpv4NeighborMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "MAC address associated with the IP address"
    ::= { mgmtIpStatusGlobalsIpv4NeighborEntry 2 }

mgmtIpStatusGlobalsIpv4NeighborInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Interface the neighbor can be reached on."
    ::= { mgmtIpStatusGlobalsIpv4NeighborEntry 3 }

mgmtIpStatusGlobalsIpv6NeighborTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatusGlobalsIpv6NeighborEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the IPv6 neighbor table."
    ::= { mgmtIpStatusGlobals 2 }

mgmtIpStatusGlobalsIpv6NeighborEntry OBJECT-TYPE
    SYNTAX      MGMTIpStatusGlobalsIpv6NeighborEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an entry in the underlying
         operating system's neighbor table."
    INDEX       { mgmtIpStatusGlobalsIpv6NeighborIpv6,
                  mgmtIpStatusGlobalsIpv6NeighborIfindex }
    ::= { mgmtIpStatusGlobalsIpv6NeighborTable 1 }

MGMTIpStatusGlobalsIpv6NeighborEntry ::= SEQUENCE {
    mgmtIpStatusGlobalsIpv6NeighborIpv6        InetAddressIPv6,
    mgmtIpStatusGlobalsIpv6NeighborIfindex     MGMTInterfaceIndex,
    mgmtIpStatusGlobalsIpv6NeighborMacAddress  MacAddress,
    mgmtIpStatusGlobalsIpv6NeighborInterface   MGMTInterfaceIndex
}

mgmtIpStatusGlobalsIpv6NeighborIpv6 OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv6 address."
    ::= { mgmtIpStatusGlobalsIpv6NeighborEntry 1 }

mgmtIpStatusGlobalsIpv6NeighborIfindex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "If 'ipAddress' is a link-local address, then the interface index where
         the host can be reached must be specified here, otherwise set this to
         zero."
    ::= { mgmtIpStatusGlobalsIpv6NeighborEntry 2 }

mgmtIpStatusGlobalsIpv6NeighborMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "MAC address associated with the IP address"
    ::= { mgmtIpStatusGlobalsIpv6NeighborEntry 3 }

mgmtIpStatusGlobalsIpv6NeighborInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Interface the neighbor can be reached on."
    ::= { mgmtIpStatusGlobalsIpv6NeighborEntry 4 }

mgmtIpStatusGlobalsNotification OBJECT IDENTIFIER
    ::= { mgmtIpStatusGlobals 3 }

mgmtIpStatusGlobalsNotificationHwRoutingTableDepleted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TRUE if the H/W routing table is full and yet another route is
         attempted added, causing the kernel's FIB database to become out of
         sync with H/W. This in turn means that proper routing can no longer be
         guaranteed.
         
         FALSE as long as everything is fine or the platform does not support
         H/W routing."
    ::= { mgmtIpStatusGlobalsNotification 1 }

mgmtIpStatusInterfaces OBJECT IDENTIFIER
    ::= { mgmtIpStatus 2 }

mgmtIpStatusInterfacesLinkTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatusInterfacesLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides link-layer status information for IP interfaces."
    ::= { mgmtIpStatusInterfaces 1 }

mgmtIpStatusInterfacesLinkEntry OBJECT-TYPE
    SYNTAX      MGMTIpStatusInterfacesLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IP interface."
    INDEX       { mgmtIpStatusInterfacesLinkIfIndex }
    ::= { mgmtIpStatusInterfacesLinkTable 1 }

MGMTIpStatusInterfacesLinkEntry ::= SEQUENCE {
    mgmtIpStatusInterfacesLinkIfIndex           MGMTInterfaceIndex,
    mgmtIpStatusInterfacesLinkOsInterfaceIndex  Unsigned32,
    mgmtIpStatusInterfacesLinkMtu               Unsigned32,
    mgmtIpStatusInterfacesLinkMacAddress        MacAddress,
    mgmtIpStatusInterfacesLinkUp                TruthValue,
    mgmtIpStatusInterfacesLinkBroadcast         TruthValue,
    mgmtIpStatusInterfacesLinkLoopback          TruthValue,
    mgmtIpStatusInterfacesLinkRunning           TruthValue,
    mgmtIpStatusInterfacesLinkNoarp             TruthValue,
    mgmtIpStatusInterfacesLinkPromisc           TruthValue,
    mgmtIpStatusInterfacesLinkMulticast         TruthValue
}

mgmtIpStatusInterfacesLinkIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpStatusInterfacesLinkEntry 1 }

mgmtIpStatusInterfacesLinkOsInterfaceIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Interface index used by the operating system."
    ::= { mgmtIpStatusInterfacesLinkEntry 2 }

mgmtIpStatusInterfacesLinkMtu OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "MTU for the interface."
    ::= { mgmtIpStatusInterfacesLinkEntry 3 }

mgmtIpStatusInterfacesLinkMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "MAC-address of the interface."
    ::= { mgmtIpStatusInterfacesLinkEntry 4 }

mgmtIpStatusInterfacesLinkUp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if the interface is up."
    ::= { mgmtIpStatusInterfacesLinkEntry 5 }

mgmtIpStatusInterfacesLinkBroadcast OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if the interface is capable of transmitting broadcast
         traffic."
    ::= { mgmtIpStatusInterfacesLinkEntry 6 }

mgmtIpStatusInterfacesLinkLoopback OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if the interface is a loop-back interface."
    ::= { mgmtIpStatusInterfacesLinkEntry 7 }

mgmtIpStatusInterfacesLinkRunning OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Interface is running (according to the operating system)."
    ::= { mgmtIpStatusInterfacesLinkEntry 8 }

mgmtIpStatusInterfacesLinkNoarp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if the interface will answer to ARP requests."
    ::= { mgmtIpStatusInterfacesLinkEntry 9 }

mgmtIpStatusInterfacesLinkPromisc OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if the interface is in promisc mode."
    ::= { mgmtIpStatusInterfacesLinkEntry 10 }

mgmtIpStatusInterfacesLinkMulticast OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates if the interface supports multicast."
    ::= { mgmtIpStatusInterfacesLinkEntry 11 }

mgmtIpStatusInterfacesIpv4Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatusInterfacesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides IPv4 status information for IP interfaces. If an
         interface is configured to use a DHCP client, then the address can be
         found here."
    ::= { mgmtIpStatusInterfaces 2 }

mgmtIpStatusInterfacesIpv4Entry OBJECT-TYPE
    SYNTAX      MGMTIpStatusInterfacesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IP interface."
    INDEX       { mgmtIpStatusInterfacesIpv4IfIndex,
                  mgmtIpStatusInterfacesIpv4NetworkAddress,
                  mgmtIpStatusInterfacesIpv4NetworkMaskLength }
    ::= { mgmtIpStatusInterfacesIpv4Table 1 }

MGMTIpStatusInterfacesIpv4Entry ::= SEQUENCE {
    mgmtIpStatusInterfacesIpv4IfIndex            MGMTInterfaceIndex,
    mgmtIpStatusInterfacesIpv4NetworkAddress     IpAddress,
    mgmtIpStatusInterfacesIpv4NetworkMaskLength  Integer32,
    mgmtIpStatusInterfacesIpv4Broadcast          IpAddress
}

mgmtIpStatusInterfacesIpv4IfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpStatusInterfacesIpv4Entry 1 }

mgmtIpStatusInterfacesIpv4NetworkAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtIpStatusInterfacesIpv4Entry 2 }

mgmtIpStatusInterfacesIpv4NetworkMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtIpStatusInterfacesIpv4Entry 3 }

mgmtIpStatusInterfacesIpv4Broadcast OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Broadcast address."
    ::= { mgmtIpStatusInterfacesIpv4Entry 4 }

mgmtIpStatusInterfacesDhcpClientV4Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatusInterfacesDhcpClientV4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides status on the DHCP client running on a given
         interface."
    ::= { mgmtIpStatusInterfaces 3 }

mgmtIpStatusInterfacesDhcpClientV4Entry OBJECT-TYPE
    SYNTAX      MGMTIpStatusInterfacesDhcpClientV4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an instance of a DHCP client."
    INDEX       { mgmtIpStatusInterfacesDhcpClientV4IfIndex }
    ::= { mgmtIpStatusInterfacesDhcpClientV4Table 1 }

MGMTIpStatusInterfacesDhcpClientV4Entry ::= SEQUENCE {
    mgmtIpStatusInterfacesDhcpClientV4IfIndex   MGMTInterfaceIndex,
    mgmtIpStatusInterfacesDhcpClientV4State     MGMTIpDhcpClientState,
    mgmtIpStatusInterfacesDhcpClientV4ServerIp  IpAddress
}

mgmtIpStatusInterfacesDhcpClientV4IfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpStatusInterfacesDhcpClientV4Entry 1 }

mgmtIpStatusInterfacesDhcpClientV4State OBJECT-TYPE
    SYNTAX      MGMTIpDhcpClientState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "State of the DHCP client."
    ::= { mgmtIpStatusInterfacesDhcpClientV4Entry 2 }

mgmtIpStatusInterfacesDhcpClientV4ServerIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IP address of the DHCP server that has provided the DHCP offer."
    ::= { mgmtIpStatusInterfacesDhcpClientV4Entry 3 }

mgmtIpStatusInterfacesIpv6Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatusInterfacesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides IPv6 status information for IP interfaces. If an
         interface is configured to use a DHCP client, then the address can be
         found here."
    ::= { mgmtIpStatusInterfaces 4 }

mgmtIpStatusInterfacesIpv6Entry OBJECT-TYPE
    SYNTAX      MGMTIpStatusInterfacesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IP interface."
    INDEX       { mgmtIpStatusInterfacesIpv6IfIndex,
                  mgmtIpStatusInterfacesIpv6NetworkAddress,
                  mgmtIpStatusInterfacesIpv6NetworkMaskLength }
    ::= { mgmtIpStatusInterfacesIpv6Table 1 }

MGMTIpStatusInterfacesIpv6Entry ::= SEQUENCE {
    mgmtIpStatusInterfacesIpv6IfIndex            MGMTInterfaceIndex,
    mgmtIpStatusInterfacesIpv6NetworkAddress     InetAddressIPv6,
    mgmtIpStatusInterfacesIpv6NetworkMaskLength  Integer32,
    mgmtIpStatusInterfacesIpv6Tentative          TruthValue,
    mgmtIpStatusInterfacesIpv6Duplicated         TruthValue,
    mgmtIpStatusInterfacesIpv6Detached           TruthValue,
    mgmtIpStatusInterfacesIpv6Nodad              TruthValue,
    mgmtIpStatusInterfacesIpv6Autoconf           TruthValue
}

mgmtIpStatusInterfacesIpv6IfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpStatusInterfacesIpv6Entry 1 }

mgmtIpStatusInterfacesIpv6NetworkAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv6 network address."
    ::= { mgmtIpStatusInterfacesIpv6Entry 2 }

mgmtIpStatusInterfacesIpv6NetworkMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv6 network mask length."
    ::= { mgmtIpStatusInterfacesIpv6Entry 3 }

mgmtIpStatusInterfacesIpv6Tentative OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "An address whose uniqueness on a link is being verified, prior to its
         assignment to an interface. A tentative address is not considered
         assigned to an interface in the usual sense."
    ::= { mgmtIpStatusInterfacesIpv6Entry 4 }

mgmtIpStatusInterfacesIpv6Duplicated OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates the address duplication is detected by Duplicate Address
         Detection (DAD).
         
         If the address is a link-local address formed from an interface
         identifier based on the hardware address, which is supposed to be
         uniquely assigned (e.g., EUI-64 for an Ethernet interface), IP
         operation on the interface should be disabled."
    ::= { mgmtIpStatusInterfacesIpv6Entry 5 }

mgmtIpStatusInterfacesIpv6Detached OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates this address is ready to be detached from the link (IPv6
         network)."
    ::= { mgmtIpStatusInterfacesIpv6Entry 6 }

mgmtIpStatusInterfacesIpv6Nodad OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates this address does not perform Duplicate Address Detection
         (DAD)."
    ::= { mgmtIpStatusInterfacesIpv6Entry 7 }

mgmtIpStatusInterfacesIpv6Autoconf OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates this address is capable of being retrieved by stateless
         address autoconfiguration."
    ::= { mgmtIpStatusInterfacesIpv6Entry 8 }

mgmtIpStatusRoutes OBJECT IDENTIFIER
    ::= { mgmtIpStatus 3 }

mgmtIpStatusRoutesIpv4Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatusRoutesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides IPv4 routing status."
    ::= { mgmtIpStatusRoutes 1 }

mgmtIpStatusRoutesIpv4Entry OBJECT-TYPE
    SYNTAX      MGMTIpStatusRoutesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IPv4 route."
    INDEX       { mgmtIpStatusRoutesIpv4NetworkAddress,
                  mgmtIpStatusRoutesIpv4NetworkPrefixSize,
                  mgmtIpStatusRoutesIpv4Protocol,
                  mgmtIpStatusRoutesIpv4NextHop,
                  mgmtIpStatusRoutesIpv4NextHopInterface }
    ::= { mgmtIpStatusRoutesIpv4Table 1 }

MGMTIpStatusRoutesIpv4Entry ::= SEQUENCE {
    mgmtIpStatusRoutesIpv4NetworkAddress     IpAddress,
    mgmtIpStatusRoutesIpv4NetworkPrefixSize  Integer32,
    mgmtIpStatusRoutesIpv4Protocol           MGMTIpRouteProtocol,
    mgmtIpStatusRoutesIpv4NextHop            IpAddress,
    mgmtIpStatusRoutesIpv4NextHopInterface   MGMTInterfaceIndex,
    mgmtIpStatusRoutesIpv4Selected           TruthValue,
    mgmtIpStatusRoutesIpv4Active             TruthValue,
    mgmtIpStatusRoutesIpv4Fib                TruthValue,
    mgmtIpStatusRoutesIpv4DirectlyConnected  TruthValue,
    mgmtIpStatusRoutesIpv4Onlink             TruthValue,
    mgmtIpStatusRoutesIpv4Duplicate          TruthValue,
    mgmtIpStatusRoutesIpv4Recursive          TruthValue,
    mgmtIpStatusRoutesIpv4Unreachable        TruthValue,
    mgmtIpStatusRoutesIpv4Reject             TruthValue,
    mgmtIpStatusRoutesIpv4AdminProhib        TruthValue,
    mgmtIpStatusRoutesIpv4Blackhole          TruthValue,
    mgmtIpStatusRoutesIpv4Metric             Unsigned32,
    mgmtIpStatusRoutesIpv4Distance           MGMTUnsigned8,
    mgmtIpStatusRoutesIpv4Uptime             MGMTUnsigned64
}

mgmtIpStatusRoutesIpv4NetworkAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Network address."
    ::= { mgmtIpStatusRoutesIpv4Entry 1 }

mgmtIpStatusRoutesIpv4NetworkPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Network prefix size."
    ::= { mgmtIpStatusRoutesIpv4Entry 2 }

mgmtIpStatusRoutesIpv4Protocol OBJECT-TYPE
    SYNTAX      MGMTIpRouteProtocol
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The protocol that installed this route."
    ::= { mgmtIpStatusRoutesIpv4Entry 3 }

mgmtIpStatusRoutesIpv4NextHop OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Next-hop IP address. All-zeroes indicates the link is directly
         connected."
    ::= { mgmtIpStatusRoutesIpv4Entry 4 }

mgmtIpStatusRoutesIpv4NextHopInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Next-hop interface."
    ::= { mgmtIpStatusRoutesIpv4Entry 5 }

mgmtIpStatusRoutesIpv4Selected OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is the best."
    ::= { mgmtIpStatusRoutesIpv4Entry 6 }

mgmtIpStatusRoutesIpv4Active OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Destination is active."
    ::= { mgmtIpStatusRoutesIpv4Entry 7 }

mgmtIpStatusRoutesIpv4Fib OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is installed in system's forward information base."
    ::= { mgmtIpStatusRoutesIpv4Entry 8 }

mgmtIpStatusRoutesIpv4DirectlyConnected OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Destination is directly connected."
    ::= { mgmtIpStatusRoutesIpv4Entry 9 }

mgmtIpStatusRoutesIpv4Onlink OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Gateway is forced on interface."
    ::= { mgmtIpStatusRoutesIpv4Entry 10 }

mgmtIpStatusRoutesIpv4Duplicate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is a duplicate."
    ::= { mgmtIpStatusRoutesIpv4Entry 11 }

mgmtIpStatusRoutesIpv4Recursive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is recursive."
    ::= { mgmtIpStatusRoutesIpv4Entry 12 }

mgmtIpStatusRoutesIpv4Unreachable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Nexthop is unreachable."
    ::= { mgmtIpStatusRoutesIpv4Entry 13 }

mgmtIpStatusRoutesIpv4Reject OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is rejected."
    ::= { mgmtIpStatusRoutesIpv4Entry 14 }

mgmtIpStatusRoutesIpv4AdminProhib OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is prohibited by admin."
    ::= { mgmtIpStatusRoutesIpv4Entry 15 }

mgmtIpStatusRoutesIpv4Blackhole OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Destination is a black hole."
    ::= { mgmtIpStatusRoutesIpv4Entry 16 }

mgmtIpStatusRoutesIpv4Metric OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Metric of the route."
    ::= { mgmtIpStatusRoutesIpv4Entry 17 }

mgmtIpStatusRoutesIpv4Distance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Distance of the route."
    ::= { mgmtIpStatusRoutesIpv4Entry 18 }

mgmtIpStatusRoutesIpv4Uptime OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time (in seconds) since this route was created"
    ::= { mgmtIpStatusRoutesIpv4Entry 19 }

mgmtIpStatusRoutesIpv6Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatusRoutesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides IPv6 routing status."
    ::= { mgmtIpStatusRoutes 2 }

mgmtIpStatusRoutesIpv6Entry OBJECT-TYPE
    SYNTAX      MGMTIpStatusRoutesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IPv6 route."
    INDEX       { mgmtIpStatusRoutesIpv6NetworkAddress,
                  mgmtIpStatusRoutesIpv6NetworkPrefixSize,
                  mgmtIpStatusRoutesIpv6Protocol,
                  mgmtIpStatusRoutesIpv6NextHop,
                  mgmtIpStatusRoutesIpv6NextHopInterface }
    ::= { mgmtIpStatusRoutesIpv6Table 1 }

MGMTIpStatusRoutesIpv6Entry ::= SEQUENCE {
    mgmtIpStatusRoutesIpv6NetworkAddress     InetAddressIPv6,
    mgmtIpStatusRoutesIpv6NetworkPrefixSize  Integer32,
    mgmtIpStatusRoutesIpv6Protocol           MGMTIpRouteProtocol,
    mgmtIpStatusRoutesIpv6NextHop            InetAddressIPv6,
    mgmtIpStatusRoutesIpv6NextHopInterface   MGMTInterfaceIndex,
    mgmtIpStatusRoutesIpv6Selected           TruthValue,
    mgmtIpStatusRoutesIpv6Active             TruthValue,
    mgmtIpStatusRoutesIpv6Fib                TruthValue,
    mgmtIpStatusRoutesIpv6DirectlyConnected  TruthValue,
    mgmtIpStatusRoutesIpv6Onlink             TruthValue,
    mgmtIpStatusRoutesIpv6Duplicate          TruthValue,
    mgmtIpStatusRoutesIpv6Recursive          TruthValue,
    mgmtIpStatusRoutesIpv6Unreachable        TruthValue,
    mgmtIpStatusRoutesIpv6Reject             TruthValue,
    mgmtIpStatusRoutesIpv6AdminProhib        TruthValue,
    mgmtIpStatusRoutesIpv6Blackhole          TruthValue,
    mgmtIpStatusRoutesIpv6Metric             Unsigned32,
    mgmtIpStatusRoutesIpv6Distance           MGMTUnsigned8,
    mgmtIpStatusRoutesIpv6Uptime             MGMTUnsigned64
}

mgmtIpStatusRoutesIpv6NetworkAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Network address."
    ::= { mgmtIpStatusRoutesIpv6Entry 1 }

mgmtIpStatusRoutesIpv6NetworkPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Network prefix size."
    ::= { mgmtIpStatusRoutesIpv6Entry 2 }

mgmtIpStatusRoutesIpv6Protocol OBJECT-TYPE
    SYNTAX      MGMTIpRouteProtocol
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The protocol that installed this route."
    ::= { mgmtIpStatusRoutesIpv6Entry 3 }

mgmtIpStatusRoutesIpv6NextHop OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Next-hop address. All-zeroes indicates the link is directly connected."
    ::= { mgmtIpStatusRoutesIpv6Entry 4 }

mgmtIpStatusRoutesIpv6NextHopInterface OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "If the next-hop address is a link-local address, then this is the VLAN
         interface of the link-local address. Otherwise this value is not used"
    ::= { mgmtIpStatusRoutesIpv6Entry 5 }

mgmtIpStatusRoutesIpv6Selected OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is the best."
    ::= { mgmtIpStatusRoutesIpv6Entry 6 }

mgmtIpStatusRoutesIpv6Active OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Destination is active."
    ::= { mgmtIpStatusRoutesIpv6Entry 7 }

mgmtIpStatusRoutesIpv6Fib OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is installed in system's forward information base."
    ::= { mgmtIpStatusRoutesIpv6Entry 8 }

mgmtIpStatusRoutesIpv6DirectlyConnected OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Destination is directly connected."
    ::= { mgmtIpStatusRoutesIpv6Entry 9 }

mgmtIpStatusRoutesIpv6Onlink OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Gateway is forced on interface."
    ::= { mgmtIpStatusRoutesIpv6Entry 10 }

mgmtIpStatusRoutesIpv6Duplicate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is a duplicate."
    ::= { mgmtIpStatusRoutesIpv6Entry 11 }

mgmtIpStatusRoutesIpv6Recursive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is recursive."
    ::= { mgmtIpStatusRoutesIpv6Entry 12 }

mgmtIpStatusRoutesIpv6Unreachable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Nexthop is unreachable."
    ::= { mgmtIpStatusRoutesIpv6Entry 13 }

mgmtIpStatusRoutesIpv6Reject OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is rejected."
    ::= { mgmtIpStatusRoutesIpv6Entry 14 }

mgmtIpStatusRoutesIpv6AdminProhib OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Route is prohibited by admin."
    ::= { mgmtIpStatusRoutesIpv6Entry 15 }

mgmtIpStatusRoutesIpv6Blackhole OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Destination is a black hole."
    ::= { mgmtIpStatusRoutesIpv6Entry 16 }

mgmtIpStatusRoutesIpv6Metric OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Metric of the route."
    ::= { mgmtIpStatusRoutesIpv6Entry 17 }

mgmtIpStatusRoutesIpv6Distance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Distance of the route."
    ::= { mgmtIpStatusRoutesIpv6Entry 18 }

mgmtIpStatusRoutesIpv6Uptime OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time (in seconds) since this route was created"
    ::= { mgmtIpStatusRoutesIpv6Entry 19 }

mgmtIpControl OBJECT IDENTIFIER
    ::= { mgmtIpMibObjects 4 }

mgmtIpControlGlobals OBJECT IDENTIFIER
    ::= { mgmtIpControl 1 }

mgmtIpControlGlobalsIpv4NeighborTableClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Flush the entries in IPv4 ARP cache except for the permanent ones."
    ::= { mgmtIpControlGlobals 1 }

mgmtIpControlGlobalsIpv6NeighborTableClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Erase all the NDP (Neighbor Discovery Protocol) entries registered in
         IPv6 neighbor cache."
    ::= { mgmtIpControlGlobals 2 }

mgmtIpControlGlobalsIpv4SystemStatisticsClear OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clear the system-wide IPv4 traffic statistics."
    ::= { mgmtIpControlGlobals 3 }

mgmtIpControlGlobalsIpv6SystemStatisticsClear OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clear the system-wide IPv6 traffic statistics."
    ::= { mgmtIpControlGlobals 4 }

mgmtIpControlGlobalsIpv4AcdStatusClear OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Clear the Address Conflict Detection table."
    ::= { mgmtIpControlGlobals 5 }

mgmtIpControlInterface OBJECT IDENTIFIER
    ::= { mgmtIpControl 2 }

mgmtIpControlInterfaceDhcpClientTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpControlInterfaceDhcpClientEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides control facilities to control an instance of the
         DHCP client."
    ::= { mgmtIpControlInterface 1 }

mgmtIpControlInterfaceDhcpClientEntry OBJECT-TYPE
    SYNTAX      MGMTIpControlInterfaceDhcpClientEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an instance of the DHCP client."
    INDEX       { mgmtIpControlInterfaceDhcpClientIfIndex }
    ::= { mgmtIpControlInterfaceDhcpClientTable 1 }

MGMTIpControlInterfaceDhcpClientEntry ::= SEQUENCE {
    mgmtIpControlInterfaceDhcpClientIfIndex  MGMTInterfaceIndex,
    mgmtIpControlInterfaceDhcpClientRestart  TruthValue
}

mgmtIpControlInterfaceDhcpClientIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpControlInterfaceDhcpClientEntry 1 }

mgmtIpControlInterfaceDhcpClientRestart OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Restart the DHCP client."
    ::= { mgmtIpControlInterfaceDhcpClientEntry 2 }

mgmtIpStatistics OBJECT IDENTIFIER
    ::= { mgmtIpMibObjects 5 }

mgmtIpStatisticsGlobals OBJECT IDENTIFIER
    ::= { mgmtIpStatistics 1 }

mgmtIpStatisticsGlobalsIpv4 OBJECT IDENTIFIER
    ::= { mgmtIpStatisticsGlobals 1 }

mgmtIpStatisticsGlobalsIpv4InReceives OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams received, including those received in
         error.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 1 }

mgmtIpStatisticsGlobalsIpv4HCInReceives OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams received, including those received in
         error. This object counts the same datagrams as InReceives, but allows
         for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 2 }

mgmtIpStatisticsGlobalsIpv4InOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in input IP datagrams, including those
         received in error. Octets from datagrams counted in InReceives must be
         counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 3 }

mgmtIpStatisticsGlobalsIpv4HCInOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in input IP datagrams, including those
         received in error. This object counts the same octets as InOctets, but
         allows for a larger value.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 4 }

mgmtIpStatisticsGlobalsIpv4InHdrErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded due to errors in their IP
         headers, including version number mismatch, other format errors, hop
         count exceeded, and errors discovered in processing their IP options.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 5 }

mgmtIpStatisticsGlobalsIpv4InNoRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because no route could be found
         to transmit them to their destination.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 6 }

mgmtIpStatisticsGlobalsIpv4InAddrErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because the IP address in their
         IP header's destination field was not a valid address to be received at
         this entity. This count includes invalid addresses (e.g., ::0). For
         entities that are not IP routers and therefore do not forward
         datagrams, this counter includes datagrams discarded because the
         destination address was not a local address.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 7 }

mgmtIpStatisticsGlobalsIpv4InUnknownProtos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of locally-addressed IP datagrams received successfully but
         discarded because of an unknown or unsupported protocol.
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 8 }

mgmtIpStatisticsGlobalsIpv4InTruncatedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because the datagram frame
         didn't carry enough data.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 9 }

mgmtIpStatisticsGlobalsIpv4InForwDatagrams OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input datagrams for which this entity was not their final IP
         destination and for which this entity attempted to find a route to
         forward them to their final destination.
         
         When tracking interface statistics, the counter of the incoming
         interface is incremented for each datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 10 }

mgmtIpStatisticsGlobalsIpv4HCInForwDatagrams OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input datagrams for which this entity was not their final IP
         destination and for which this entity attempted to find a route to
         forward them to their final destination. This object counts the same
         packets as InForwDatagrams, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 11 }

mgmtIpStatisticsGlobalsIpv4ReasmReqds OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP fragments received that needed to be reassembled at this
         interface.
         
         When tracking interface statistics, the counter of the interface to
         which these fragments were addressed is incremented. This interface
         might not be the same as the input interface for some of the fragments.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 12 }

mgmtIpStatisticsGlobalsIpv4ReasmOKs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams successfully reassembled.
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 13 }

mgmtIpStatisticsGlobalsIpv4ReasmFails OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of failures detected by the IP re-assembly algorithm.
         
         Note: this is not necessarily a count of discarded IP fragments because
         some algorithms (notably the algorithm in RFC 815) can lose track of
         the number of fragments by combining them as they are received.
         
         When tracking interface statistics, the counter of the interface to
         which these fragments were addressed is incremented. This interface
         might not be the same as the input interface for some of the fragments.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 14 }

mgmtIpStatisticsGlobalsIpv4InDiscards OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams for which no problems were encountered to
         prevent their continued processing, but were discarded.
         
         Note: this counter does not include any datagrams discarded while
         awaiting re-assembly.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 15 }

mgmtIpStatisticsGlobalsIpv4InDelivers OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams successfully delivered to IP user-protocols
         (including ICMP).
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 16 }

mgmtIpStatisticsGlobalsIpv4HCInDelivers OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams successfully delivered to IP user-protocols
         (including ICMP). This object counts the same packets as
         ipSystemStatsInDelivers, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 17 }

mgmtIpStatisticsGlobalsIpv4OutRequests OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that local IP user-protocols (including ICMP)
         supplied to IP in requests for transmission.
         
         Note: this counter does not include any datagrams counted in
         OutForwDatagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 18 }

mgmtIpStatisticsGlobalsIpv4HCOutRequests OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that local IP user-protocols (including ICMP)
         supplied to IP in requests for transmission. This object counts the
         same packets as OutRequests, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 19 }

mgmtIpStatisticsGlobalsIpv4OutNoRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of locally generated IP datagrams discarded because no route
         could be found to transmit them to their destination.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 20 }

mgmtIpStatisticsGlobalsIpv4OutForwDatagrams OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams for which this entity was not their final IP
         destination and for which it was successful in finding a path to their
         final destination.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully forwarded datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 21 }

mgmtIpStatisticsGlobalsIpv4HCOutForwDatagrams OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams for which this entity was not their final IP
         destination and for which it was successful in finding a path to their
         final destination. This object counts the same packets as
         OutForwDatagrams, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 22 }

mgmtIpStatisticsGlobalsIpv4OutDiscards OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of output IP datagrams for which no problem was encountered to
         prevent their transmission to their destination, but were discarded.
         
         Note: this counter includes datagrams counted in OutForwDatagrams if
         any such datagrams met this (discretionary) discard criterion.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 23 }

mgmtIpStatisticsGlobalsIpv4OutFragReqds OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that require fragmentation in order to be
         transmitted.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 24 }

mgmtIpStatisticsGlobalsIpv4OutFragOKs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that have been successfully fragmented.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 25 }

mgmtIpStatisticsGlobalsIpv4OutFragFails OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that have been discarded because they needed to
         be fragmented but could not be. This includes IPv4 packets that have
         the DF bit set and IPv6 packets that are being forwarded and exceed the
         outgoing link MTU.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for an unsuccessfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 26 }

mgmtIpStatisticsGlobalsIpv4OutFragCreates OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of output datagram fragments that have been generated as a
         result of IP fragmentation.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 27 }

mgmtIpStatisticsGlobalsIpv4OutTransmits OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that this entity supplied to the lower layers
         for transmission. This includes datagrams generated locally and those
         forwarded by this entity.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 28 }

mgmtIpStatisticsGlobalsIpv4HCOutTransmits OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that this entity supplied to the lower layers
         for transmission. This object counts the same datagrams as
         OutTransmits, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 29 }

mgmtIpStatisticsGlobalsIpv4OutOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets in IP datagrams delivered to the lower layers for
         transmission. Octets from datagrams counted in OutTransmits must be
         counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 30 }

mgmtIpStatisticsGlobalsIpv4HCOutOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets in IP datagrams delivered to the lower layers for
         transmission. This objects counts the same octets as OutOctets, but
         allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 31 }

mgmtIpStatisticsGlobalsIpv4InMcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams received.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 32 }

mgmtIpStatisticsGlobalsIpv4HCInMcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams received. This object counts the same
         datagrams as InMcastPkts but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 33 }

mgmtIpStatisticsGlobalsIpv4InMcastOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in IP multicast datagrams. Octets from
         datagrams counted in InMcastPkts MUST be counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 34 }

mgmtIpStatisticsGlobalsIpv4HCInMcastOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in IP multicast datagrams. This object counts
         the same octets as InMcastOctets, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 35 }

mgmtIpStatisticsGlobalsIpv4OutMcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams transmitted.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 36 }

mgmtIpStatisticsGlobalsIpv4HCOutMcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams transmitted. This object counts the
         same datagrams as OutMcastPkts, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 37 }

mgmtIpStatisticsGlobalsIpv4OutMcastOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets transmitted in IP multicast datagrams. Octets from
         datagrams counted in OutMcastPkts must be counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 38 }

mgmtIpStatisticsGlobalsIpv4HCOutMcastOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets transmitted in IP multicast datagrams. This object
         counts the same octets as OutMcastOctets, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 39 }

mgmtIpStatisticsGlobalsIpv4InBcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams received.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 40 }

mgmtIpStatisticsGlobalsIpv4HCInBcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams received. This object counts the same
         datagrams as InBcastPkts but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 41 }

mgmtIpStatisticsGlobalsIpv4OutBcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams transmitted.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 42 }

mgmtIpStatisticsGlobalsIpv4HCOutBcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams transmitted. This object counts the
         same datagrams as OutBcastPkts, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv4 43 }

mgmtIpStatisticsGlobalsIpv4DiscontinuityTime OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value of sysUpTime on the most recent occasion when any one or more of
         this entry's counters suffered a discontinuity.
         
         If no such discontinuities have occurred since the last
         re-initialization of the IP stack, then this object contains a zero
         value."
    ::= { mgmtIpStatisticsGlobalsIpv4 44 }

mgmtIpStatisticsGlobalsIpv4RefreshRate OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum reasonable polling interval for this entry. This object
         provides an indication of the minimum amount of time required to update
         the counters in this entry."
    ::= { mgmtIpStatisticsGlobalsIpv4 45 }

mgmtIpStatisticsGlobalsIpv6 OBJECT IDENTIFIER
    ::= { mgmtIpStatisticsGlobals 2 }

mgmtIpStatisticsGlobalsIpv6InReceives OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams received, including those received in
         error.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 1 }

mgmtIpStatisticsGlobalsIpv6HCInReceives OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams received, including those received in
         error. This object counts the same datagrams as InReceives, but allows
         for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 2 }

mgmtIpStatisticsGlobalsIpv6InOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in input IP datagrams, including those
         received in error. Octets from datagrams counted in InReceives must be
         counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 3 }

mgmtIpStatisticsGlobalsIpv6HCInOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in input IP datagrams, including those
         received in error. This object counts the same octets as InOctets, but
         allows for a larger value.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 4 }

mgmtIpStatisticsGlobalsIpv6InHdrErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded due to errors in their IP
         headers, including version number mismatch, other format errors, hop
         count exceeded, and errors discovered in processing their IP options.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 5 }

mgmtIpStatisticsGlobalsIpv6InNoRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because no route could be found
         to transmit them to their destination.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 6 }

mgmtIpStatisticsGlobalsIpv6InAddrErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because the IP address in their
         IP header's destination field was not a valid address to be received at
         this entity. This count includes invalid addresses (e.g., ::0). For
         entities that are not IP routers and therefore do not forward
         datagrams, this counter includes datagrams discarded because the
         destination address was not a local address.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 7 }

mgmtIpStatisticsGlobalsIpv6InUnknownProtos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of locally-addressed IP datagrams received successfully but
         discarded because of an unknown or unsupported protocol.
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 8 }

mgmtIpStatisticsGlobalsIpv6InTruncatedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because the datagram frame
         didn't carry enough data.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 9 }

mgmtIpStatisticsGlobalsIpv6InForwDatagrams OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input datagrams for which this entity was not their final IP
         destination and for which this entity attempted to find a route to
         forward them to their final destination.
         
         When tracking interface statistics, the counter of the incoming
         interface is incremented for each datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 10 }

mgmtIpStatisticsGlobalsIpv6HCInForwDatagrams OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input datagrams for which this entity was not their final IP
         destination and for which this entity attempted to find a route to
         forward them to their final destination. This object counts the same
         packets as InForwDatagrams, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 11 }

mgmtIpStatisticsGlobalsIpv6ReasmReqds OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP fragments received that needed to be reassembled at this
         interface.
         
         When tracking interface statistics, the counter of the interface to
         which these fragments were addressed is incremented. This interface
         might not be the same as the input interface for some of the fragments.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 12 }

mgmtIpStatisticsGlobalsIpv6ReasmOKs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams successfully reassembled.
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 13 }

mgmtIpStatisticsGlobalsIpv6ReasmFails OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of failures detected by the IP re-assembly algorithm.
         
         Note: this is not necessarily a count of discarded IP fragments because
         some algorithms (notably the algorithm in RFC 815) can lose track of
         the number of fragments by combining them as they are received.
         
         When tracking interface statistics, the counter of the interface to
         which these fragments were addressed is incremented. This interface
         might not be the same as the input interface for some of the fragments.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 14 }

mgmtIpStatisticsGlobalsIpv6InDiscards OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams for which no problems were encountered to
         prevent their continued processing, but were discarded.
         
         Note: this counter does not include any datagrams discarded while
         awaiting re-assembly.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 15 }

mgmtIpStatisticsGlobalsIpv6InDelivers OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams successfully delivered to IP user-protocols
         (including ICMP).
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 16 }

mgmtIpStatisticsGlobalsIpv6HCInDelivers OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams successfully delivered to IP user-protocols
         (including ICMP). This object counts the same packets as
         ipSystemStatsInDelivers, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 17 }

mgmtIpStatisticsGlobalsIpv6OutRequests OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that local IP user-protocols (including ICMP)
         supplied to IP in requests for transmission.
         
         Note: this counter does not include any datagrams counted in
         OutForwDatagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 18 }

mgmtIpStatisticsGlobalsIpv6HCOutRequests OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that local IP user-protocols (including ICMP)
         supplied to IP in requests for transmission. This object counts the
         same packets as OutRequests, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 19 }

mgmtIpStatisticsGlobalsIpv6OutNoRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of locally generated IP datagrams discarded because no route
         could be found to transmit them to their destination.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 20 }

mgmtIpStatisticsGlobalsIpv6OutForwDatagrams OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams for which this entity was not their final IP
         destination and for which it was successful in finding a path to their
         final destination.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully forwarded datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 21 }

mgmtIpStatisticsGlobalsIpv6HCOutForwDatagrams OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams for which this entity was not their final IP
         destination and for which it was successful in finding a path to their
         final destination. This object counts the same packets as
         OutForwDatagrams, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 22 }

mgmtIpStatisticsGlobalsIpv6OutDiscards OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of output IP datagrams for which no problem was encountered to
         prevent their transmission to their destination, but were discarded.
         
         Note: this counter includes datagrams counted in OutForwDatagrams if
         any such datagrams met this (discretionary) discard criterion.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 23 }

mgmtIpStatisticsGlobalsIpv6OutFragReqds OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that require fragmentation in order to be
         transmitted.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 24 }

mgmtIpStatisticsGlobalsIpv6OutFragOKs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that have been successfully fragmented.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 25 }

mgmtIpStatisticsGlobalsIpv6OutFragFails OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that have been discarded because they needed to
         be fragmented but could not be. This includes IPv4 packets that have
         the DF bit set and IPv6 packets that are being forwarded and exceed the
         outgoing link MTU.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for an unsuccessfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 26 }

mgmtIpStatisticsGlobalsIpv6OutFragCreates OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of output datagram fragments that have been generated as a
         result of IP fragmentation.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 27 }

mgmtIpStatisticsGlobalsIpv6OutTransmits OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that this entity supplied to the lower layers
         for transmission. This includes datagrams generated locally and those
         forwarded by this entity.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 28 }

mgmtIpStatisticsGlobalsIpv6HCOutTransmits OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that this entity supplied to the lower layers
         for transmission. This object counts the same datagrams as
         OutTransmits, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 29 }

mgmtIpStatisticsGlobalsIpv6OutOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets in IP datagrams delivered to the lower layers for
         transmission. Octets from datagrams counted in OutTransmits must be
         counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 30 }

mgmtIpStatisticsGlobalsIpv6HCOutOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets in IP datagrams delivered to the lower layers for
         transmission. This objects counts the same octets as OutOctets, but
         allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 31 }

mgmtIpStatisticsGlobalsIpv6InMcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams received.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 32 }

mgmtIpStatisticsGlobalsIpv6HCInMcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams received. This object counts the same
         datagrams as InMcastPkts but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 33 }

mgmtIpStatisticsGlobalsIpv6InMcastOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in IP multicast datagrams. Octets from
         datagrams counted in InMcastPkts MUST be counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 34 }

mgmtIpStatisticsGlobalsIpv6HCInMcastOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in IP multicast datagrams. This object counts
         the same octets as InMcastOctets, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 35 }

mgmtIpStatisticsGlobalsIpv6OutMcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams transmitted.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 36 }

mgmtIpStatisticsGlobalsIpv6HCOutMcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams transmitted. This object counts the
         same datagrams as OutMcastPkts, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 37 }

mgmtIpStatisticsGlobalsIpv6OutMcastOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets transmitted in IP multicast datagrams. Octets from
         datagrams counted in OutMcastPkts must be counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 38 }

mgmtIpStatisticsGlobalsIpv6HCOutMcastOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets transmitted in IP multicast datagrams. This object
         counts the same octets as OutMcastOctets, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 39 }

mgmtIpStatisticsGlobalsIpv6InBcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams received.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 40 }

mgmtIpStatisticsGlobalsIpv6HCInBcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams received. This object counts the same
         datagrams as InBcastPkts but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 41 }

mgmtIpStatisticsGlobalsIpv6OutBcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams transmitted.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 42 }

mgmtIpStatisticsGlobalsIpv6HCOutBcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams transmitted. This object counts the
         same datagrams as OutBcastPkts, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime."
    ::= { mgmtIpStatisticsGlobalsIpv6 43 }

mgmtIpStatisticsGlobalsIpv6DiscontinuityTime OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value of sysUpTime on the most recent occasion when any one or more of
         this entry's counters suffered a discontinuity.
         
         If no such discontinuities have occurred since the last
         re-initialization of the IP stack, then this object contains a zero
         value."
    ::= { mgmtIpStatisticsGlobalsIpv6 44 }

mgmtIpStatisticsGlobalsIpv6RefreshRate OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum reasonable polling interval for this entry. This object
         provides an indication of the minimum amount of time required to update
         the counters in this entry."
    ::= { mgmtIpStatisticsGlobalsIpv6 45 }

mgmtIpStatisticsInterfaces OBJECT IDENTIFIER
    ::= { mgmtIpStatistics 2 }

mgmtIpStatisticsInterfacesLinkTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatisticsInterfacesLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides interface link statistics for a given IP interface."
    ::= { mgmtIpStatisticsInterfaces 1 }

mgmtIpStatisticsInterfacesLinkEntry OBJECT-TYPE
    SYNTAX      MGMTIpStatisticsInterfacesLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IP interface."
    INDEX       { mgmtIpStatisticsInterfacesLinkIfIndex }
    ::= { mgmtIpStatisticsInterfacesLinkTable 1 }

MGMTIpStatisticsInterfacesLinkEntry ::= SEQUENCE {
    mgmtIpStatisticsInterfacesLinkIfIndex        MGMTInterfaceIndex,
    mgmtIpStatisticsInterfacesLinkInPackets      Counter64,
    mgmtIpStatisticsInterfacesLinkOutPackets     Counter64,
    mgmtIpStatisticsInterfacesLinkInBytes        Counter64,
    mgmtIpStatisticsInterfacesLinkOutBytes       Counter64,
    mgmtIpStatisticsInterfacesLinkInMulticasts   Counter64,
    mgmtIpStatisticsInterfacesLinkOutMulticasts  Counter64,
    mgmtIpStatisticsInterfacesLinkInBroadcasts   Counter64,
    mgmtIpStatisticsInterfacesLinkOutBroadcasts  Counter64
}

mgmtIpStatisticsInterfacesLinkIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtIpStatisticsInterfacesLinkEntry 1 }

mgmtIpStatisticsInterfacesLinkInPackets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of packets delivered by MAC layer to IP layer."
    ::= { mgmtIpStatisticsInterfacesLinkEntry 2 }

mgmtIpStatisticsInterfacesLinkOutPackets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of packets IP protocols requested be transmitted, including
         those that were discarded or not sent."
    ::= { mgmtIpStatisticsInterfacesLinkEntry 3 }

mgmtIpStatisticsInterfacesLinkInBytes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received on the interface, including framing
         characters."
    ::= { mgmtIpStatisticsInterfacesLinkEntry 4 }

mgmtIpStatisticsInterfacesLinkOutBytes OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets transmitted out of the interface, including framing
         characters."
    ::= { mgmtIpStatisticsInterfacesLinkEntry 5 }

mgmtIpStatisticsInterfacesLinkInMulticasts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of packets delivered by MAC layer to IP layer that were
         addressed to a MAC multicast address.
         
         For a MAC layer protocol, this includes both Group and Functional
         addresses."
    ::= { mgmtIpStatisticsInterfacesLinkEntry 6 }

mgmtIpStatisticsInterfacesLinkOutMulticasts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of packets addressed to a multicast MAC address that IP
         protocols requested be transmitted, including those that were discarded
         or not sent.
         
         For a MAC layer protocol, this includes both Group and Functional
         addresses."
    ::= { mgmtIpStatisticsInterfacesLinkEntry 7 }

mgmtIpStatisticsInterfacesLinkInBroadcasts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of packets delivered by MAC layer to IP layer that were
         addressed to a MAC broadcast address."
    ::= { mgmtIpStatisticsInterfacesLinkEntry 8 }

mgmtIpStatisticsInterfacesLinkOutBroadcasts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of packets addressed to a broadcast MAC address that IP
         protocols requested be transmitted, including those that were discarded
         or not sent."
    ::= { mgmtIpStatisticsInterfacesLinkEntry 9 }

mgmtIpStatisticsInterfacesIpv4Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatisticsInterfacesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides IPv4 related statitics for a given IP interface."
    ::= { mgmtIpStatisticsInterfaces 2 }

mgmtIpStatisticsInterfacesIpv4Entry OBJECT-TYPE
    SYNTAX      MGMTIpStatisticsInterfacesIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IP interface."
    INDEX       { mgmtIpStatisticsInterfacesIpv4IfIndex }
    ::= { mgmtIpStatisticsInterfacesIpv4Table 1 }

MGMTIpStatisticsInterfacesIpv4Entry ::= SEQUENCE {
    mgmtIpStatisticsInterfacesIpv4IfIndex             MGMTInterfaceIndex,
    mgmtIpStatisticsInterfacesIpv4InReceives          Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCInReceives        Counter64,
    mgmtIpStatisticsInterfacesIpv4InOctets            Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCInOctets          Counter64,
    mgmtIpStatisticsInterfacesIpv4InHdrErrors         Unsigned32,
    mgmtIpStatisticsInterfacesIpv4InNoRoutes          Unsigned32,
    mgmtIpStatisticsInterfacesIpv4InAddrErrors        Unsigned32,
    mgmtIpStatisticsInterfacesIpv4InUnknownProtos     Unsigned32,
    mgmtIpStatisticsInterfacesIpv4InTruncatedPkts     Unsigned32,
    mgmtIpStatisticsInterfacesIpv4InForwDatagrams     Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCInForwDatagrams   Counter64,
    mgmtIpStatisticsInterfacesIpv4ReasmReqds          Unsigned32,
    mgmtIpStatisticsInterfacesIpv4ReasmOKs            Unsigned32,
    mgmtIpStatisticsInterfacesIpv4ReasmFails          Unsigned32,
    mgmtIpStatisticsInterfacesIpv4InDiscards          Unsigned32,
    mgmtIpStatisticsInterfacesIpv4InDelivers          Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCInDelivers        Counter64,
    mgmtIpStatisticsInterfacesIpv4OutRequests         Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCOutRequests       Counter64,
    mgmtIpStatisticsInterfacesIpv4OutNoRoutes         Unsigned32,
    mgmtIpStatisticsInterfacesIpv4OutForwDatagrams    Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCOutForwDatagrams  Counter64,
    mgmtIpStatisticsInterfacesIpv4OutDiscards         Unsigned32,
    mgmtIpStatisticsInterfacesIpv4OutFragReqds        Unsigned32,
    mgmtIpStatisticsInterfacesIpv4OutFragOKs          Unsigned32,
    mgmtIpStatisticsInterfacesIpv4OutFragFails        Unsigned32,
    mgmtIpStatisticsInterfacesIpv4OutFragCreates      Unsigned32,
    mgmtIpStatisticsInterfacesIpv4OutTransmits        Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCOutTransmits      Counter64,
    mgmtIpStatisticsInterfacesIpv4OutOctets           Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCOutOctets         Counter64,
    mgmtIpStatisticsInterfacesIpv4InMcastPkts         Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCInMcastPkts       Counter64,
    mgmtIpStatisticsInterfacesIpv4InMcastOctets       Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCInMcastOctets     Counter64,
    mgmtIpStatisticsInterfacesIpv4OutMcastPkts        Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCOutMcastPkts      Counter64,
    mgmtIpStatisticsInterfacesIpv4OutMcastOctets      Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCOutMcastOctets    Counter64,
    mgmtIpStatisticsInterfacesIpv4InBcastPkts         Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCInBcastPkts       Counter64,
    mgmtIpStatisticsInterfacesIpv4OutBcastPkts        Unsigned32,
    mgmtIpStatisticsInterfacesIpv4HCOutBcastPkts      Counter64,
    mgmtIpStatisticsInterfacesIpv4DiscontinuityTime   Counter64,
    mgmtIpStatisticsInterfacesIpv4RefreshRate         Unsigned32
}

mgmtIpStatisticsInterfacesIpv4IfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 1 }

mgmtIpStatisticsInterfacesIpv4InReceives OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams received, including those received in
         error.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 2 }

mgmtIpStatisticsInterfacesIpv4HCInReceives OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams received, including those received in
         error. This object counts the same datagrams as InReceives, but allows
         for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 3 }

mgmtIpStatisticsInterfacesIpv4InOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in input IP datagrams, including those
         received in error. Octets from datagrams counted in InReceives must be
         counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 4 }

mgmtIpStatisticsInterfacesIpv4HCInOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in input IP datagrams, including those
         received in error. This object counts the same octets as InOctets, but
         allows for a larger value.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 5 }

mgmtIpStatisticsInterfacesIpv4InHdrErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded due to errors in their IP
         headers, including version number mismatch, other format errors, hop
         count exceeded, and errors discovered in processing their IP options.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 6 }

mgmtIpStatisticsInterfacesIpv4InNoRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because no route could be found
         to transmit them to their destination.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 7 }

mgmtIpStatisticsInterfacesIpv4InAddrErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because the IP address in their
         IP header's destination field was not a valid address to be received at
         this entity. This count includes invalid addresses (e.g., ::0). For
         entities that are not IP routers and therefore do not forward
         datagrams, this counter includes datagrams discarded because the
         destination address was not a local address.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 8 }

mgmtIpStatisticsInterfacesIpv4InUnknownProtos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of locally-addressed IP datagrams received successfully but
         discarded because of an unknown or unsupported protocol.
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 9 }

mgmtIpStatisticsInterfacesIpv4InTruncatedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because the datagram frame
         didn't carry enough data.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 10 }

mgmtIpStatisticsInterfacesIpv4InForwDatagrams OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input datagrams for which this entity was not their final IP
         destination and for which this entity attempted to find a route to
         forward them to their final destination.
         
         When tracking interface statistics, the counter of the incoming
         interface is incremented for each datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 11 }

mgmtIpStatisticsInterfacesIpv4HCInForwDatagrams OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input datagrams for which this entity was not their final IP
         destination and for which this entity attempted to find a route to
         forward them to their final destination. This object counts the same
         packets as InForwDatagrams, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 12 }

mgmtIpStatisticsInterfacesIpv4ReasmReqds OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP fragments received that needed to be reassembled at this
         interface.
         
         When tracking interface statistics, the counter of the interface to
         which these fragments were addressed is incremented. This interface
         might not be the same as the input interface for some of the fragments.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 13 }

mgmtIpStatisticsInterfacesIpv4ReasmOKs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams successfully reassembled.
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 14 }

mgmtIpStatisticsInterfacesIpv4ReasmFails OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of failures detected by the IP re-assembly algorithm.
         
         Note: this is not necessarily a count of discarded IP fragments because
         some algorithms (notably the algorithm in RFC 815) can lose track of
         the number of fragments by combining them as they are received.
         
         When tracking interface statistics, the counter of the interface to
         which these fragments were addressed is incremented. This interface
         might not be the same as the input interface for some of the fragments.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 15 }

mgmtIpStatisticsInterfacesIpv4InDiscards OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams for which no problems were encountered to
         prevent their continued processing, but were discarded.
         
         Note: this counter does not include any datagrams discarded while
         awaiting re-assembly.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 16 }

mgmtIpStatisticsInterfacesIpv4InDelivers OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams successfully delivered to IP user-protocols
         (including ICMP).
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 17 }

mgmtIpStatisticsInterfacesIpv4HCInDelivers OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams successfully delivered to IP user-protocols
         (including ICMP). This object counts the same packets as
         ipSystemStatsInDelivers, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 18 }

mgmtIpStatisticsInterfacesIpv4OutRequests OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that local IP user-protocols (including ICMP)
         supplied to IP in requests for transmission.
         
         Note: this counter does not include any datagrams counted in
         OutForwDatagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 19 }

mgmtIpStatisticsInterfacesIpv4HCOutRequests OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that local IP user-protocols (including ICMP)
         supplied to IP in requests for transmission. This object counts the
         same packets as OutRequests, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 20 }

mgmtIpStatisticsInterfacesIpv4OutNoRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of locally generated IP datagrams discarded because no route
         could be found to transmit them to their destination.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 21 }

mgmtIpStatisticsInterfacesIpv4OutForwDatagrams OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams for which this entity was not their final IP
         destination and for which it was successful in finding a path to their
         final destination.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully forwarded datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 22 }

mgmtIpStatisticsInterfacesIpv4HCOutForwDatagrams OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams for which this entity was not their final IP
         destination and for which it was successful in finding a path to their
         final destination. This object counts the same packets as
         OutForwDatagrams, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 23 }

mgmtIpStatisticsInterfacesIpv4OutDiscards OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of output IP datagrams for which no problem was encountered to
         prevent their transmission to their destination, but were discarded.
         
         Note: this counter includes datagrams counted in OutForwDatagrams if
         any such datagrams met this (discretionary) discard criterion.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 24 }

mgmtIpStatisticsInterfacesIpv4OutFragReqds OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that require fragmentation in order to be
         transmitted.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 25 }

mgmtIpStatisticsInterfacesIpv4OutFragOKs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that have been successfully fragmented.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 26 }

mgmtIpStatisticsInterfacesIpv4OutFragFails OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that have been discarded because they needed to
         be fragmented but could not be. This includes IPv4 packets that have
         the DF bit set and IPv6 packets that are being forwarded and exceed the
         outgoing link MTU.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for an unsuccessfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 27 }

mgmtIpStatisticsInterfacesIpv4OutFragCreates OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of output datagram fragments that have been generated as a
         result of IP fragmentation.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 28 }

mgmtIpStatisticsInterfacesIpv4OutTransmits OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that this entity supplied to the lower layers
         for transmission. This includes datagrams generated locally and those
         forwarded by this entity.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 29 }

mgmtIpStatisticsInterfacesIpv4HCOutTransmits OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that this entity supplied to the lower layers
         for transmission. This object counts the same datagrams as
         OutTransmits, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 30 }

mgmtIpStatisticsInterfacesIpv4OutOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets in IP datagrams delivered to the lower layers for
         transmission. Octets from datagrams counted in OutTransmits must be
         counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 31 }

mgmtIpStatisticsInterfacesIpv4HCOutOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets in IP datagrams delivered to the lower layers for
         transmission. This objects counts the same octets as OutOctets, but
         allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 32 }

mgmtIpStatisticsInterfacesIpv4InMcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams received.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 33 }

mgmtIpStatisticsInterfacesIpv4HCInMcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams received. This object counts the same
         datagrams as InMcastPkts but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 34 }

mgmtIpStatisticsInterfacesIpv4InMcastOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in IP multicast datagrams. Octets from
         datagrams counted in InMcastPkts MUST be counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 35 }

mgmtIpStatisticsInterfacesIpv4HCInMcastOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in IP multicast datagrams. This object counts
         the same octets as InMcastOctets, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 36 }

mgmtIpStatisticsInterfacesIpv4OutMcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams transmitted.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 37 }

mgmtIpStatisticsInterfacesIpv4HCOutMcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams transmitted. This object counts the
         same datagrams as OutMcastPkts, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 38 }

mgmtIpStatisticsInterfacesIpv4OutMcastOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets transmitted in IP multicast datagrams. Octets from
         datagrams counted in OutMcastPkts must be counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 39 }

mgmtIpStatisticsInterfacesIpv4HCOutMcastOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets transmitted in IP multicast datagrams. This object
         counts the same octets as OutMcastOctets, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 40 }

mgmtIpStatisticsInterfacesIpv4InBcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams received.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 41 }

mgmtIpStatisticsInterfacesIpv4HCInBcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams received. This object counts the same
         datagrams as InBcastPkts but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 42 }

mgmtIpStatisticsInterfacesIpv4OutBcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams transmitted.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 43 }

mgmtIpStatisticsInterfacesIpv4HCOutBcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams transmitted. This object counts the
         same datagrams as OutBcastPkts, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 44 }

mgmtIpStatisticsInterfacesIpv4DiscontinuityTime OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value of sysUpTime on the most recent occasion when any one or more of
         this entry's counters suffered a discontinuity.
         
         If no such discontinuities have occurred since the last
         re-initialization of the IP stack, then this object contains a zero
         value.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 45 }

mgmtIpStatisticsInterfacesIpv4RefreshRate OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum reasonable polling interval for this entry. This object
         provides an indication of the minimum amount of time required to update
         the counters in this entry.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv4Entry 46 }

mgmtIpStatisticsInterfacesIpv6Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpStatisticsInterfacesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides IPv6 related statitics for a given IP interface."
    ::= { mgmtIpStatisticsInterfaces 3 }

mgmtIpStatisticsInterfacesIpv6Entry OBJECT-TYPE
    SYNTAX      MGMTIpStatisticsInterfacesIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents an IP interface."
    INDEX       { mgmtIpStatisticsInterfacesIpv6IfIndex }
    ::= { mgmtIpStatisticsInterfacesIpv6Table 1 }

MGMTIpStatisticsInterfacesIpv6Entry ::= SEQUENCE {
    mgmtIpStatisticsInterfacesIpv6IfIndex             MGMTInterfaceIndex,
    mgmtIpStatisticsInterfacesIpv6InReceives          Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCInReceives        Counter64,
    mgmtIpStatisticsInterfacesIpv6InOctets            Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCInOctets          Counter64,
    mgmtIpStatisticsInterfacesIpv6InHdrErrors         Unsigned32,
    mgmtIpStatisticsInterfacesIpv6InNoRoutes          Unsigned32,
    mgmtIpStatisticsInterfacesIpv6InAddrErrors        Unsigned32,
    mgmtIpStatisticsInterfacesIpv6InUnknownProtos     Unsigned32,
    mgmtIpStatisticsInterfacesIpv6InTruncatedPkts     Unsigned32,
    mgmtIpStatisticsInterfacesIpv6InForwDatagrams     Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCInForwDatagrams   Counter64,
    mgmtIpStatisticsInterfacesIpv6ReasmReqds          Unsigned32,
    mgmtIpStatisticsInterfacesIpv6ReasmOKs            Unsigned32,
    mgmtIpStatisticsInterfacesIpv6ReasmFails          Unsigned32,
    mgmtIpStatisticsInterfacesIpv6InDiscards          Unsigned32,
    mgmtIpStatisticsInterfacesIpv6InDelivers          Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCInDelivers        Counter64,
    mgmtIpStatisticsInterfacesIpv6OutRequests         Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCOutRequests       Counter64,
    mgmtIpStatisticsInterfacesIpv6OutNoRoutes         Unsigned32,
    mgmtIpStatisticsInterfacesIpv6OutForwDatagrams    Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCOutForwDatagrams  Counter64,
    mgmtIpStatisticsInterfacesIpv6OutDiscards         Unsigned32,
    mgmtIpStatisticsInterfacesIpv6OutFragReqds        Unsigned32,
    mgmtIpStatisticsInterfacesIpv6OutFragOKs          Unsigned32,
    mgmtIpStatisticsInterfacesIpv6OutFragFails        Unsigned32,
    mgmtIpStatisticsInterfacesIpv6OutFragCreates      Unsigned32,
    mgmtIpStatisticsInterfacesIpv6OutTransmits        Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCOutTransmits      Counter64,
    mgmtIpStatisticsInterfacesIpv6OutOctets           Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCOutOctets         Counter64,
    mgmtIpStatisticsInterfacesIpv6InMcastPkts         Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCInMcastPkts       Counter64,
    mgmtIpStatisticsInterfacesIpv6InMcastOctets       Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCInMcastOctets     Counter64,
    mgmtIpStatisticsInterfacesIpv6OutMcastPkts        Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCOutMcastPkts      Counter64,
    mgmtIpStatisticsInterfacesIpv6OutMcastOctets      Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCOutMcastOctets    Counter64,
    mgmtIpStatisticsInterfacesIpv6InBcastPkts         Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCInBcastPkts       Counter64,
    mgmtIpStatisticsInterfacesIpv6OutBcastPkts        Unsigned32,
    mgmtIpStatisticsInterfacesIpv6HCOutBcastPkts      Counter64,
    mgmtIpStatisticsInterfacesIpv6DiscontinuityTime   Counter64,
    mgmtIpStatisticsInterfacesIpv6RefreshRate         Unsigned32
}

mgmtIpStatisticsInterfacesIpv6IfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 1 }

mgmtIpStatisticsInterfacesIpv6InReceives OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams received, including those received in
         error.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 2 }

mgmtIpStatisticsInterfacesIpv6HCInReceives OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams received, including those received in
         error. This object counts the same datagrams as InReceives, but allows
         for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 3 }

mgmtIpStatisticsInterfacesIpv6InOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in input IP datagrams, including those
         received in error. Octets from datagrams counted in InReceives must be
         counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 4 }

mgmtIpStatisticsInterfacesIpv6HCInOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in input IP datagrams, including those
         received in error. This object counts the same octets as InOctets, but
         allows for a larger value.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 5 }

mgmtIpStatisticsInterfacesIpv6InHdrErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded due to errors in their IP
         headers, including version number mismatch, other format errors, hop
         count exceeded, and errors discovered in processing their IP options.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 6 }

mgmtIpStatisticsInterfacesIpv6InNoRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because no route could be found
         to transmit them to their destination.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 7 }

mgmtIpStatisticsInterfacesIpv6InAddrErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because the IP address in their
         IP header's destination field was not a valid address to be received at
         this entity. This count includes invalid addresses (e.g., ::0). For
         entities that are not IP routers and therefore do not forward
         datagrams, this counter includes datagrams discarded because the
         destination address was not a local address.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 8 }

mgmtIpStatisticsInterfacesIpv6InUnknownProtos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of locally-addressed IP datagrams received successfully but
         discarded because of an unknown or unsupported protocol.
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 9 }

mgmtIpStatisticsInterfacesIpv6InTruncatedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams discarded because the datagram frame
         didn't carry enough data.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 10 }

mgmtIpStatisticsInterfacesIpv6InForwDatagrams OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input datagrams for which this entity was not their final IP
         destination and for which this entity attempted to find a route to
         forward them to their final destination.
         
         When tracking interface statistics, the counter of the incoming
         interface is incremented for each datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 11 }

mgmtIpStatisticsInterfacesIpv6HCInForwDatagrams OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input datagrams for which this entity was not their final IP
         destination and for which this entity attempted to find a route to
         forward them to their final destination. This object counts the same
         packets as InForwDatagrams, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 12 }

mgmtIpStatisticsInterfacesIpv6ReasmReqds OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP fragments received that needed to be reassembled at this
         interface.
         
         When tracking interface statistics, the counter of the interface to
         which these fragments were addressed is incremented. This interface
         might not be the same as the input interface for some of the fragments.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 13 }

mgmtIpStatisticsInterfacesIpv6ReasmOKs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams successfully reassembled.
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 14 }

mgmtIpStatisticsInterfacesIpv6ReasmFails OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of failures detected by the IP re-assembly algorithm.
         
         Note: this is not necessarily a count of discarded IP fragments because
         some algorithms (notably the algorithm in RFC 815) can lose track of
         the number of fragments by combining them as they are received.
         
         When tracking interface statistics, the counter of the interface to
         which these fragments were addressed is incremented. This interface
         might not be the same as the input interface for some of the fragments.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 15 }

mgmtIpStatisticsInterfacesIpv6InDiscards OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of input IP datagrams for which no problems were encountered to
         prevent their continued processing, but were discarded.
         
         Note: this counter does not include any datagrams discarded while
         awaiting re-assembly.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 16 }

mgmtIpStatisticsInterfacesIpv6InDelivers OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams successfully delivered to IP user-protocols
         (including ICMP).
         
         When tracking interface statistics, the counter of the interface to
         which these datagrams were addressed is incremented. This interface
         might not be the same as the input interface for some of the datagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 17 }

mgmtIpStatisticsInterfacesIpv6HCInDelivers OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams successfully delivered to IP user-protocols
         (including ICMP). This object counts the same packets as
         ipSystemStatsInDelivers, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 18 }

mgmtIpStatisticsInterfacesIpv6OutRequests OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that local IP user-protocols (including ICMP)
         supplied to IP in requests for transmission.
         
         Note: this counter does not include any datagrams counted in
         OutForwDatagrams.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 19 }

mgmtIpStatisticsInterfacesIpv6HCOutRequests OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that local IP user-protocols (including ICMP)
         supplied to IP in requests for transmission. This object counts the
         same packets as OutRequests, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 20 }

mgmtIpStatisticsInterfacesIpv6OutNoRoutes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of locally generated IP datagrams discarded because no route
         could be found to transmit them to their destination.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 21 }

mgmtIpStatisticsInterfacesIpv6OutForwDatagrams OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams for which this entity was not their final IP
         destination and for which it was successful in finding a path to their
         final destination.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully forwarded datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 22 }

mgmtIpStatisticsInterfacesIpv6HCOutForwDatagrams OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of datagrams for which this entity was not their final IP
         destination and for which it was successful in finding a path to their
         final destination. This object counts the same packets as
         OutForwDatagrams, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 23 }

mgmtIpStatisticsInterfacesIpv6OutDiscards OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of output IP datagrams for which no problem was encountered to
         prevent their transmission to their destination, but were discarded.
         
         Note: this counter includes datagrams counted in OutForwDatagrams if
         any such datagrams met this (discretionary) discard criterion.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 24 }

mgmtIpStatisticsInterfacesIpv6OutFragReqds OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that require fragmentation in order to be
         transmitted.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 25 }

mgmtIpStatisticsInterfacesIpv6OutFragOKs OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that have been successfully fragmented.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 26 }

mgmtIpStatisticsInterfacesIpv6OutFragFails OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that have been discarded because they needed to
         be fragmented but could not be. This includes IPv4 packets that have
         the DF bit set and IPv6 packets that are being forwarded and exceed the
         outgoing link MTU.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for an unsuccessfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 27 }

mgmtIpStatisticsInterfacesIpv6OutFragCreates OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of output datagram fragments that have been generated as a
         result of IP fragmentation.
         
         When tracking interface statistics, the counter of the outgoing
         interface is incremented for a successfully fragmented datagram.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 28 }

mgmtIpStatisticsInterfacesIpv6OutTransmits OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that this entity supplied to the lower layers
         for transmission. This includes datagrams generated locally and those
         forwarded by this entity.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 29 }

mgmtIpStatisticsInterfacesIpv6HCOutTransmits OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP datagrams that this entity supplied to the lower layers
         for transmission. This object counts the same datagrams as
         OutTransmits, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 30 }

mgmtIpStatisticsInterfacesIpv6OutOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets in IP datagrams delivered to the lower layers for
         transmission. Octets from datagrams counted in OutTransmits must be
         counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 31 }

mgmtIpStatisticsInterfacesIpv6HCOutOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets in IP datagrams delivered to the lower layers for
         transmission. This objects counts the same octets as OutOctets, but
         allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 32 }

mgmtIpStatisticsInterfacesIpv6InMcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams received.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 33 }

mgmtIpStatisticsInterfacesIpv6HCInMcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams received. This object counts the same
         datagrams as InMcastPkts but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 34 }

mgmtIpStatisticsInterfacesIpv6InMcastOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in IP multicast datagrams. Octets from
         datagrams counted in InMcastPkts MUST be counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 35 }

mgmtIpStatisticsInterfacesIpv6HCInMcastOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets received in IP multicast datagrams. This object counts
         the same octets as InMcastOctets, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 36 }

mgmtIpStatisticsInterfacesIpv6OutMcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams transmitted.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 37 }

mgmtIpStatisticsInterfacesIpv6HCOutMcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP multicast datagrams transmitted. This object counts the
         same datagrams as OutMcastPkts, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 38 }

mgmtIpStatisticsInterfacesIpv6OutMcastOctets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets transmitted in IP multicast datagrams. Octets from
         datagrams counted in OutMcastPkts must be counted here.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 39 }

mgmtIpStatisticsInterfacesIpv6HCOutMcastOctets OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of octets transmitted in IP multicast datagrams. This object
         counts the same octets as OutMcastOctets, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 40 }

mgmtIpStatisticsInterfacesIpv6InBcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams received.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 41 }

mgmtIpStatisticsInterfacesIpv6HCInBcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams received. This object counts the same
         datagrams as InBcastPkts but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 42 }

mgmtIpStatisticsInterfacesIpv6OutBcastPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams transmitted.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 43 }

mgmtIpStatisticsInterfacesIpv6HCOutBcastPkts OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of IP broadcast datagrams transmitted. This object counts the
         same datagrams as OutBcastPkts, but allows for larger values.
         
         Discontinuities in the value of this counter may occur at
         re-initialization of the system, and at other times as indicated by the
         value of DiscontinuityTime.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 44 }

mgmtIpStatisticsInterfacesIpv6DiscontinuityTime OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value of sysUpTime on the most recent occasion when any one or more of
         this entry's counters suffered a discontinuity.
         
         If no such discontinuities have occurred since the last
         re-initialization of the IP stack, then this object contains a zero
         value.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 45 }

mgmtIpStatisticsInterfacesIpv6RefreshRate OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum reasonable polling interval for this entry. This object
         provides an indication of the minimum amount of time required to update
         the counters in this entry.
         
         This object is only available if the capability object
         'mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics' is True."
    ::= { mgmtIpStatisticsInterfacesIpv6Entry 46 }

mgmtIpTrap OBJECT IDENTIFIER
    ::= { mgmtIpMibObjects 6 }

mgmtIpTrapInterfacesLinkAdd NOTIFICATION-TYPE
    OBJECTS     { mgmtIpStatusInterfacesLinkIfIndex,
                  mgmtIpStatusInterfacesLinkOsInterfaceIndex,
                  mgmtIpStatusInterfacesLinkMtu,
                  mgmtIpStatusInterfacesLinkMacAddress,
                  mgmtIpStatusInterfacesLinkUp,
                  mgmtIpStatusInterfacesLinkBroadcast,
                  mgmtIpStatusInterfacesLinkLoopback,
                  mgmtIpStatusInterfacesLinkRunning,
                  mgmtIpStatusInterfacesLinkNoarp,
                  mgmtIpStatusInterfacesLinkPromisc,
                  mgmtIpStatusInterfacesLinkMulticast }
    STATUS      current
    DESCRIPTION
        "This trap signals that a row has been added. The index(es) and value(s)
         of the row is included in the trap."

    ::= { mgmtIpTrap 1 }

mgmtIpTrapInterfacesLinkMod NOTIFICATION-TYPE
    OBJECTS     { mgmtIpStatusInterfacesLinkIfIndex,
                  mgmtIpStatusInterfacesLinkOsInterfaceIndex,
                  mgmtIpStatusInterfacesLinkMtu,
                  mgmtIpStatusInterfacesLinkMacAddress,
                  mgmtIpStatusInterfacesLinkUp,
                  mgmtIpStatusInterfacesLinkBroadcast,
                  mgmtIpStatusInterfacesLinkLoopback,
                  mgmtIpStatusInterfacesLinkRunning,
                  mgmtIpStatusInterfacesLinkNoarp,
                  mgmtIpStatusInterfacesLinkPromisc,
                  mgmtIpStatusInterfacesLinkMulticast }
    STATUS      current
    DESCRIPTION
        "This trap signals that one or more of the objects included in the trap
          has been updated."

    ::= { mgmtIpTrap 2 }

mgmtIpTrapInterfacesLinkDel NOTIFICATION-TYPE
    OBJECTS     { mgmtIpStatusInterfacesLinkIfIndex }
    STATUS      current
    DESCRIPTION
        "This trap signals that a row has been deleted. The index(es) of the
         row is included in the trap."

    ::= { mgmtIpTrap 3 }

mgmtIpTrapGlobalsMain NOTIFICATION-TYPE
    OBJECTS     {                   mgmtIpStatusGlobalsNotificationHwRoutingTableDepleted }
    STATUS      current
    DESCRIPTION
        "This trap signals that one or more of the objects included in the trap
         has been updated."

    ::= { mgmtIpTrap 4 }

mgmtIpMibConformance OBJECT IDENTIFIER
    ::= { mgmtIpMib 2 }

mgmtIpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtIpMibConformance 1 }

mgmtIpMibGroups OBJECT IDENTIFIER
    ::= { mgmtIpMibConformance 2 }

mgmtIpCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpCapabilitiesHasIpv4HostCapabilities,
                  mgmtIpCapabilitiesHasIpv6HostCapabilities,
                  mgmtIpCapabilitiesHasIpv4UnicastRoutingCapabilities,
                  mgmtIpCapabilitiesHasIpv4UnicastHwRoutingCapabilities,
                  mgmtIpCapabilitiesHasIpv6UnicastRoutingCapabilities,
                  mgmtIpCapabilitiesHasIpv6UnicastHwRoutingCapabilities,
                  mgmtIpCapabilitiesMaxNumberOfIpInterfaces,
                  mgmtIpCapabilitiesMaxNumberOfStaticRoutes,
                  mgmtIpCapabilitiesNumberOfLpmHardwareEntries,
                  mgmtIpCapabilitiesHasPerInterfaceIpv4Statistics,
                  mgmtIpCapabilitiesHasPerInterfaceIpv6Statistics }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 1 }

mgmtIpConfigGlobalsMainInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpConfigGlobalsMainEnableRouting }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 2 }

mgmtIpConfigInterfacesTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpConfigInterfacesIfIndex,
                  mgmtIpConfigInterfacesMtu,
                  mgmtIpConfigInterfacesAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 3 }

mgmtIpConfigInterfacesTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpConfigInterfacesTableRowEditorIfIndex,
                  mgmtIpConfigInterfacesTableRowEditorMtu,
                  mgmtIpConfigInterfacesTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 4 }

mgmtIpConfigInterfacesIpv4TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpConfigInterfacesIpv4IfIndex,
                  mgmtIpConfigInterfacesIpv4Active,
                  mgmtIpConfigInterfacesIpv4EnableDhcpClient,
                  mgmtIpConfigInterfacesIpv4Ipv4Address,
                  mgmtIpConfigInterfacesIpv4PrefixSize,
                  mgmtIpConfigInterfacesIpv4DhcpClientFallbackTimeout,
                  mgmtIpConfigInterfacesIpv4DhcpClientHostname,
                  mgmtIpConfigInterfacesIpv4DhcpClientIdType,
                  mgmtIpConfigInterfacesIpv4DhcpClientIdIfMac,
                  mgmtIpConfigInterfacesIpv4DhcpClientIdAscii,
                  mgmtIpConfigInterfacesIpv4DhcpClientIdHex }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 5 }

mgmtIpConfigInterfacesIpv6TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpConfigInterfacesIpv6IfIndex,
                  mgmtIpConfigInterfacesIpv6Active,
                  mgmtIpConfigInterfacesIpv6Ipv6Address,
                  mgmtIpConfigInterfacesIpv6PrefixSize }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 6 }

mgmtIpConfigRoutesIpv4TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpConfigRoutesIpv4NetworkAddress,
                  mgmtIpConfigRoutesIpv4NetworkPrefixSize,
                  mgmtIpConfigRoutesIpv4NextHop,
                  mgmtIpConfigRoutesIpv4Distance,
                  mgmtIpConfigRoutesIpv4Action }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 7 }

mgmtIpConfigRoutesIpv4RowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpConfigRoutesIpv4RowEditorNetworkAddress,
                  mgmtIpConfigRoutesIpv4RowEditorNetworkPrefixSize,
                  mgmtIpConfigRoutesIpv4RowEditorNextHop,
                  mgmtIpConfigRoutesIpv4RowEditorDistance,
                  mgmtIpConfigRoutesIpv4RowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 8 }

mgmtIpConfigRoutesIpv6TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpConfigRoutesIpv6NetworkAddress,
                  mgmtIpConfigRoutesIpv6NetworkPrefixSize,
                  mgmtIpConfigRoutesIpv6NextHop,
                  mgmtIpConfigRoutesIpv6NextHopInterface,
                  mgmtIpConfigRoutesIpv6Distance,
                  mgmtIpConfigRoutesIpv6Action }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 9 }

mgmtIpConfigRoutesIpv6RowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpConfigRoutesIpv6RowEditorNetworkAddress,
                  mgmtIpConfigRoutesIpv6RowEditorNetworkPrefixSize,
                  mgmtIpConfigRoutesIpv6RowEditorNextHop,
                  mgmtIpConfigRoutesIpv6RowEditorNextHopInterface,
                  mgmtIpConfigRoutesIpv6RowEditorDistance,
                  mgmtIpConfigRoutesIpv6RowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 10 }

mgmtIpStatusGlobalsIpv4NeighborInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatusGlobalsIpv4NeighborIpv4,
                  mgmtIpStatusGlobalsIpv4NeighborMacAddress,
                  mgmtIpStatusGlobalsIpv4NeighborInterface }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 11 }

mgmtIpStatusGlobalsIpv6NeighborInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatusGlobalsIpv6NeighborIpv6,
                  mgmtIpStatusGlobalsIpv6NeighborIfindex,
                  mgmtIpStatusGlobalsIpv6NeighborMacAddress,
                  mgmtIpStatusGlobalsIpv6NeighborInterface }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 12 }

mgmtIpStatusGlobalsNotificationInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpStatusGlobalsNotificationHwRoutingTableDepleted }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 13 }

mgmtIpStatusInterfacesLinkInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatusInterfacesLinkIfIndex,
                  mgmtIpStatusInterfacesLinkOsInterfaceIndex,
                  mgmtIpStatusInterfacesLinkMtu,
                  mgmtIpStatusInterfacesLinkMacAddress,
                  mgmtIpStatusInterfacesLinkUp,
                  mgmtIpStatusInterfacesLinkBroadcast,
                  mgmtIpStatusInterfacesLinkLoopback,
                  mgmtIpStatusInterfacesLinkRunning,
                  mgmtIpStatusInterfacesLinkNoarp,
                  mgmtIpStatusInterfacesLinkPromisc,
                  mgmtIpStatusInterfacesLinkMulticast }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 14 }

mgmtIpStatusInterfacesIpv4InfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatusInterfacesIpv4IfIndex,
                  mgmtIpStatusInterfacesIpv4NetworkAddress,
                  mgmtIpStatusInterfacesIpv4NetworkMaskLength,
                  mgmtIpStatusInterfacesIpv4Broadcast }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 15 }

mgmtIpStatusInterfacesDhcpClientV4InfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatusInterfacesDhcpClientV4IfIndex,
                  mgmtIpStatusInterfacesDhcpClientV4State,
                  mgmtIpStatusInterfacesDhcpClientV4ServerIp }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 16 }

mgmtIpStatusInterfacesIpv6InfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatusInterfacesIpv6IfIndex,
                  mgmtIpStatusInterfacesIpv6NetworkAddress,
                  mgmtIpStatusInterfacesIpv6NetworkMaskLength,
                  mgmtIpStatusInterfacesIpv6Tentative,
                  mgmtIpStatusInterfacesIpv6Duplicated,
                  mgmtIpStatusInterfacesIpv6Detached,
                  mgmtIpStatusInterfacesIpv6Nodad,
                  mgmtIpStatusInterfacesIpv6Autoconf }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 17 }

mgmtIpStatusRoutesIpv4InfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatusRoutesIpv4NetworkAddress,
                  mgmtIpStatusRoutesIpv4NetworkPrefixSize,
                  mgmtIpStatusRoutesIpv4Protocol,
                  mgmtIpStatusRoutesIpv4NextHop,
                  mgmtIpStatusRoutesIpv4NextHopInterface,
                  mgmtIpStatusRoutesIpv4Selected,
                  mgmtIpStatusRoutesIpv4Active,
                  mgmtIpStatusRoutesIpv4Fib,
                  mgmtIpStatusRoutesIpv4DirectlyConnected,
                  mgmtIpStatusRoutesIpv4Onlink,
                  mgmtIpStatusRoutesIpv4Duplicate,
                  mgmtIpStatusRoutesIpv4Recursive,
                  mgmtIpStatusRoutesIpv4Unreachable,
                  mgmtIpStatusRoutesIpv4Reject,
                  mgmtIpStatusRoutesIpv4AdminProhib,
                  mgmtIpStatusRoutesIpv4Blackhole,
                  mgmtIpStatusRoutesIpv4Metric,
                  mgmtIpStatusRoutesIpv4Distance,
                  mgmtIpStatusRoutesIpv4Uptime }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 18 }

mgmtIpStatusRoutesIpv6InfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatusRoutesIpv6NetworkAddress,
                  mgmtIpStatusRoutesIpv6NetworkPrefixSize,
                  mgmtIpStatusRoutesIpv6Protocol,
                  mgmtIpStatusRoutesIpv6NextHop,
                  mgmtIpStatusRoutesIpv6NextHopInterface,
                  mgmtIpStatusRoutesIpv6Selected,
                  mgmtIpStatusRoutesIpv6Active,
                  mgmtIpStatusRoutesIpv6Fib,
                  mgmtIpStatusRoutesIpv6DirectlyConnected,
                  mgmtIpStatusRoutesIpv6Onlink,
                  mgmtIpStatusRoutesIpv6Duplicate,
                  mgmtIpStatusRoutesIpv6Recursive,
                  mgmtIpStatusRoutesIpv6Unreachable,
                  mgmtIpStatusRoutesIpv6Reject,
                  mgmtIpStatusRoutesIpv6AdminProhib,
                  mgmtIpStatusRoutesIpv6Blackhole,
                  mgmtIpStatusRoutesIpv6Metric,
                  mgmtIpStatusRoutesIpv6Distance,
                  mgmtIpStatusRoutesIpv6Uptime }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 19 }

mgmtIpControlGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpControlGlobalsIpv4NeighborTableClear,
                  mgmtIpControlGlobalsIpv6NeighborTableClear,
                  mgmtIpControlGlobalsIpv4SystemStatisticsClear,
                  mgmtIpControlGlobalsIpv6SystemStatisticsClear,
                  mgmtIpControlGlobalsIpv4AcdStatusClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 20 }

mgmtIpControlInterfaceDhcpClientInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpControlInterfaceDhcpClientIfIndex,
                  mgmtIpControlInterfaceDhcpClientRestart }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 21 }

mgmtIpStatisticsGlobalsIpv4InfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatisticsGlobalsIpv4InReceives,
                  mgmtIpStatisticsGlobalsIpv4HCInReceives,
                  mgmtIpStatisticsGlobalsIpv4InOctets,
                  mgmtIpStatisticsGlobalsIpv4HCInOctets,
                  mgmtIpStatisticsGlobalsIpv4InHdrErrors,
                  mgmtIpStatisticsGlobalsIpv4InNoRoutes,
                  mgmtIpStatisticsGlobalsIpv4InAddrErrors,
                  mgmtIpStatisticsGlobalsIpv4InUnknownProtos,
                  mgmtIpStatisticsGlobalsIpv4InTruncatedPkts,
                  mgmtIpStatisticsGlobalsIpv4InForwDatagrams,
                  mgmtIpStatisticsGlobalsIpv4HCInForwDatagrams,
                  mgmtIpStatisticsGlobalsIpv4ReasmReqds,
                  mgmtIpStatisticsGlobalsIpv4ReasmOKs,
                  mgmtIpStatisticsGlobalsIpv4ReasmFails,
                  mgmtIpStatisticsGlobalsIpv4InDiscards,
                  mgmtIpStatisticsGlobalsIpv4InDelivers,
                  mgmtIpStatisticsGlobalsIpv4HCInDelivers,
                  mgmtIpStatisticsGlobalsIpv4OutRequests,
                  mgmtIpStatisticsGlobalsIpv4HCOutRequests,
                  mgmtIpStatisticsGlobalsIpv4OutNoRoutes,
                  mgmtIpStatisticsGlobalsIpv4OutForwDatagrams,
                  mgmtIpStatisticsGlobalsIpv4HCOutForwDatagrams,
                  mgmtIpStatisticsGlobalsIpv4OutDiscards,
                  mgmtIpStatisticsGlobalsIpv4OutFragReqds,
                  mgmtIpStatisticsGlobalsIpv4OutFragOKs,
                  mgmtIpStatisticsGlobalsIpv4OutFragFails,
                  mgmtIpStatisticsGlobalsIpv4OutFragCreates,
                  mgmtIpStatisticsGlobalsIpv4OutTransmits,
                  mgmtIpStatisticsGlobalsIpv4HCOutTransmits,
                  mgmtIpStatisticsGlobalsIpv4OutOctets,
                  mgmtIpStatisticsGlobalsIpv4HCOutOctets,
                  mgmtIpStatisticsGlobalsIpv4InMcastPkts,
                  mgmtIpStatisticsGlobalsIpv4HCInMcastPkts,
                  mgmtIpStatisticsGlobalsIpv4InMcastOctets,
                  mgmtIpStatisticsGlobalsIpv4HCInMcastOctets,
                  mgmtIpStatisticsGlobalsIpv4OutMcastPkts,
                  mgmtIpStatisticsGlobalsIpv4HCOutMcastPkts,
                  mgmtIpStatisticsGlobalsIpv4OutMcastOctets,
                  mgmtIpStatisticsGlobalsIpv4HCOutMcastOctets,
                  mgmtIpStatisticsGlobalsIpv4InBcastPkts,
                  mgmtIpStatisticsGlobalsIpv4HCInBcastPkts,
                  mgmtIpStatisticsGlobalsIpv4OutBcastPkts,
                  mgmtIpStatisticsGlobalsIpv4HCOutBcastPkts,
                  mgmtIpStatisticsGlobalsIpv4DiscontinuityTime,
                  mgmtIpStatisticsGlobalsIpv4RefreshRate }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 22 }

mgmtIpStatisticsGlobalsIpv6InfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatisticsGlobalsIpv6InReceives,
                  mgmtIpStatisticsGlobalsIpv6HCInReceives,
                  mgmtIpStatisticsGlobalsIpv6InOctets,
                  mgmtIpStatisticsGlobalsIpv6HCInOctets,
                  mgmtIpStatisticsGlobalsIpv6InHdrErrors,
                  mgmtIpStatisticsGlobalsIpv6InNoRoutes,
                  mgmtIpStatisticsGlobalsIpv6InAddrErrors,
                  mgmtIpStatisticsGlobalsIpv6InUnknownProtos,
                  mgmtIpStatisticsGlobalsIpv6InTruncatedPkts,
                  mgmtIpStatisticsGlobalsIpv6InForwDatagrams,
                  mgmtIpStatisticsGlobalsIpv6HCInForwDatagrams,
                  mgmtIpStatisticsGlobalsIpv6ReasmReqds,
                  mgmtIpStatisticsGlobalsIpv6ReasmOKs,
                  mgmtIpStatisticsGlobalsIpv6ReasmFails,
                  mgmtIpStatisticsGlobalsIpv6InDiscards,
                  mgmtIpStatisticsGlobalsIpv6InDelivers,
                  mgmtIpStatisticsGlobalsIpv6HCInDelivers,
                  mgmtIpStatisticsGlobalsIpv6OutRequests,
                  mgmtIpStatisticsGlobalsIpv6HCOutRequests,
                  mgmtIpStatisticsGlobalsIpv6OutNoRoutes,
                  mgmtIpStatisticsGlobalsIpv6OutForwDatagrams,
                  mgmtIpStatisticsGlobalsIpv6HCOutForwDatagrams,
                  mgmtIpStatisticsGlobalsIpv6OutDiscards,
                  mgmtIpStatisticsGlobalsIpv6OutFragReqds,
                  mgmtIpStatisticsGlobalsIpv6OutFragOKs,
                  mgmtIpStatisticsGlobalsIpv6OutFragFails,
                  mgmtIpStatisticsGlobalsIpv6OutFragCreates,
                  mgmtIpStatisticsGlobalsIpv6OutTransmits,
                  mgmtIpStatisticsGlobalsIpv6HCOutTransmits,
                  mgmtIpStatisticsGlobalsIpv6OutOctets,
                  mgmtIpStatisticsGlobalsIpv6HCOutOctets,
                  mgmtIpStatisticsGlobalsIpv6InMcastPkts,
                  mgmtIpStatisticsGlobalsIpv6HCInMcastPkts,
                  mgmtIpStatisticsGlobalsIpv6InMcastOctets,
                  mgmtIpStatisticsGlobalsIpv6HCInMcastOctets,
                  mgmtIpStatisticsGlobalsIpv6OutMcastPkts,
                  mgmtIpStatisticsGlobalsIpv6HCOutMcastPkts,
                  mgmtIpStatisticsGlobalsIpv6OutMcastOctets,
                  mgmtIpStatisticsGlobalsIpv6HCOutMcastOctets,
                  mgmtIpStatisticsGlobalsIpv6InBcastPkts,
                  mgmtIpStatisticsGlobalsIpv6HCInBcastPkts,
                  mgmtIpStatisticsGlobalsIpv6OutBcastPkts,
                  mgmtIpStatisticsGlobalsIpv6HCOutBcastPkts,
                  mgmtIpStatisticsGlobalsIpv6DiscontinuityTime,
                  mgmtIpStatisticsGlobalsIpv6RefreshRate }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 23 }

mgmtIpStatisticsInterfacesLinkInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatisticsInterfacesLinkIfIndex,
                  mgmtIpStatisticsInterfacesLinkInPackets,
                  mgmtIpStatisticsInterfacesLinkOutPackets,
                  mgmtIpStatisticsInterfacesLinkInBytes,
                  mgmtIpStatisticsInterfacesLinkOutBytes,
                  mgmtIpStatisticsInterfacesLinkInMulticasts,
                  mgmtIpStatisticsInterfacesLinkOutMulticasts,
                  mgmtIpStatisticsInterfacesLinkInBroadcasts,
                  mgmtIpStatisticsInterfacesLinkOutBroadcasts }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 24 }

mgmtIpStatisticsInterfacesIpv4InfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatisticsInterfacesIpv4IfIndex,
                  mgmtIpStatisticsInterfacesIpv4InReceives,
                  mgmtIpStatisticsInterfacesIpv4HCInReceives,
                  mgmtIpStatisticsInterfacesIpv4InOctets,
                  mgmtIpStatisticsInterfacesIpv4HCInOctets,
                  mgmtIpStatisticsInterfacesIpv4InHdrErrors,
                  mgmtIpStatisticsInterfacesIpv4InNoRoutes,
                  mgmtIpStatisticsInterfacesIpv4InAddrErrors,
                  mgmtIpStatisticsInterfacesIpv4InUnknownProtos,
                  mgmtIpStatisticsInterfacesIpv4InTruncatedPkts,
                  mgmtIpStatisticsInterfacesIpv4InForwDatagrams,
                  mgmtIpStatisticsInterfacesIpv4HCInForwDatagrams,
                  mgmtIpStatisticsInterfacesIpv4ReasmReqds,
                  mgmtIpStatisticsInterfacesIpv4ReasmOKs,
                  mgmtIpStatisticsInterfacesIpv4ReasmFails,
                  mgmtIpStatisticsInterfacesIpv4InDiscards,
                  mgmtIpStatisticsInterfacesIpv4InDelivers,
                  mgmtIpStatisticsInterfacesIpv4HCInDelivers,
                  mgmtIpStatisticsInterfacesIpv4OutRequests,
                  mgmtIpStatisticsInterfacesIpv4HCOutRequests,
                  mgmtIpStatisticsInterfacesIpv4OutNoRoutes,
                  mgmtIpStatisticsInterfacesIpv4OutForwDatagrams,
                  mgmtIpStatisticsInterfacesIpv4HCOutForwDatagrams,
                  mgmtIpStatisticsInterfacesIpv4OutDiscards,
                  mgmtIpStatisticsInterfacesIpv4OutFragReqds,
                  mgmtIpStatisticsInterfacesIpv4OutFragOKs,
                  mgmtIpStatisticsInterfacesIpv4OutFragFails,
                  mgmtIpStatisticsInterfacesIpv4OutFragCreates,
                  mgmtIpStatisticsInterfacesIpv4OutTransmits,
                  mgmtIpStatisticsInterfacesIpv4HCOutTransmits,
                  mgmtIpStatisticsInterfacesIpv4OutOctets,
                  mgmtIpStatisticsInterfacesIpv4HCOutOctets,
                  mgmtIpStatisticsInterfacesIpv4InMcastPkts,
                  mgmtIpStatisticsInterfacesIpv4HCInMcastPkts,
                  mgmtIpStatisticsInterfacesIpv4InMcastOctets,
                  mgmtIpStatisticsInterfacesIpv4HCInMcastOctets,
                  mgmtIpStatisticsInterfacesIpv4OutMcastPkts,
                  mgmtIpStatisticsInterfacesIpv4HCOutMcastPkts,
                  mgmtIpStatisticsInterfacesIpv4OutMcastOctets,
                  mgmtIpStatisticsInterfacesIpv4HCOutMcastOctets,
                  mgmtIpStatisticsInterfacesIpv4InBcastPkts,
                  mgmtIpStatisticsInterfacesIpv4HCInBcastPkts,
                  mgmtIpStatisticsInterfacesIpv4OutBcastPkts,
                  mgmtIpStatisticsInterfacesIpv4HCOutBcastPkts,
                  mgmtIpStatisticsInterfacesIpv4DiscontinuityTime,
                  mgmtIpStatisticsInterfacesIpv4RefreshRate }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 25 }

mgmtIpStatisticsInterfacesIpv6InfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpStatisticsInterfacesIpv6IfIndex,
                  mgmtIpStatisticsInterfacesIpv6InReceives,
                  mgmtIpStatisticsInterfacesIpv6HCInReceives,
                  mgmtIpStatisticsInterfacesIpv6InOctets,
                  mgmtIpStatisticsInterfacesIpv6HCInOctets,
                  mgmtIpStatisticsInterfacesIpv6InHdrErrors,
                  mgmtIpStatisticsInterfacesIpv6InNoRoutes,
                  mgmtIpStatisticsInterfacesIpv6InAddrErrors,
                  mgmtIpStatisticsInterfacesIpv6InUnknownProtos,
                  mgmtIpStatisticsInterfacesIpv6InTruncatedPkts,
                  mgmtIpStatisticsInterfacesIpv6InForwDatagrams,
                  mgmtIpStatisticsInterfacesIpv6HCInForwDatagrams,
                  mgmtIpStatisticsInterfacesIpv6ReasmReqds,
                  mgmtIpStatisticsInterfacesIpv6ReasmOKs,
                  mgmtIpStatisticsInterfacesIpv6ReasmFails,
                  mgmtIpStatisticsInterfacesIpv6InDiscards,
                  mgmtIpStatisticsInterfacesIpv6InDelivers,
                  mgmtIpStatisticsInterfacesIpv6HCInDelivers,
                  mgmtIpStatisticsInterfacesIpv6OutRequests,
                  mgmtIpStatisticsInterfacesIpv6HCOutRequests,
                  mgmtIpStatisticsInterfacesIpv6OutNoRoutes,
                  mgmtIpStatisticsInterfacesIpv6OutForwDatagrams,
                  mgmtIpStatisticsInterfacesIpv6HCOutForwDatagrams,
                  mgmtIpStatisticsInterfacesIpv6OutDiscards,
                  mgmtIpStatisticsInterfacesIpv6OutFragReqds,
                  mgmtIpStatisticsInterfacesIpv6OutFragOKs,
                  mgmtIpStatisticsInterfacesIpv6OutFragFails,
                  mgmtIpStatisticsInterfacesIpv6OutFragCreates,
                  mgmtIpStatisticsInterfacesIpv6OutTransmits,
                  mgmtIpStatisticsInterfacesIpv6HCOutTransmits,
                  mgmtIpStatisticsInterfacesIpv6OutOctets,
                  mgmtIpStatisticsInterfacesIpv6HCOutOctets,
                  mgmtIpStatisticsInterfacesIpv6InMcastPkts,
                  mgmtIpStatisticsInterfacesIpv6HCInMcastPkts,
                  mgmtIpStatisticsInterfacesIpv6InMcastOctets,
                  mgmtIpStatisticsInterfacesIpv6HCInMcastOctets,
                  mgmtIpStatisticsInterfacesIpv6OutMcastPkts,
                  mgmtIpStatisticsInterfacesIpv6HCOutMcastPkts,
                  mgmtIpStatisticsInterfacesIpv6OutMcastOctets,
                  mgmtIpStatisticsInterfacesIpv6HCOutMcastOctets,
                  mgmtIpStatisticsInterfacesIpv6InBcastPkts,
                  mgmtIpStatisticsInterfacesIpv6HCInBcastPkts,
                  mgmtIpStatisticsInterfacesIpv6OutBcastPkts,
                  mgmtIpStatisticsInterfacesIpv6HCOutBcastPkts,
                  mgmtIpStatisticsInterfacesIpv6DiscontinuityTime,
                  mgmtIpStatisticsInterfacesIpv6RefreshRate }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpMibGroups 26 }

mgmtIpTrapInterfacesLinkAddInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtIpTrapInterfacesLinkAdd }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtIpMibGroups 27 }

mgmtIpTrapInterfacesLinkModInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtIpTrapInterfacesLinkMod }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtIpMibGroups 28 }

mgmtIpTrapInterfacesLinkDelInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtIpTrapInterfacesLinkDel }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtIpMibGroups 29 }

mgmtIpTrapGlobalsMainInfoGroup NOTIFICATION-GROUP
    NOTIFICATIONS { mgmtIpTrapGlobalsMain }
    STATUS      current
    DESCRIPTION
        "Information group containing a trap."
    ::= { mgmtIpMibGroups 30 }

mgmtIpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtIpCapabilitiesInfoGroup,
                       mgmtIpConfigGlobalsMainInfoGroup,
                       mgmtIpConfigInterfacesTableInfoGroup,
                       mgmtIpConfigInterfacesTableRowEditorInfoGroup,
                       mgmtIpConfigInterfacesIpv4TableInfoGroup,
                       mgmtIpConfigInterfacesIpv6TableInfoGroup,
                       mgmtIpConfigRoutesIpv4TableInfoGroup,
                       mgmtIpConfigRoutesIpv4RowEditorInfoGroup,
                       mgmtIpConfigRoutesIpv6TableInfoGroup,
                       mgmtIpConfigRoutesIpv6RowEditorInfoGroup,
                       mgmtIpStatusGlobalsIpv4NeighborInfoGroup,
                       mgmtIpStatusGlobalsIpv6NeighborInfoGroup,
                       mgmtIpStatusGlobalsNotificationInfoGroup,
                       mgmtIpStatusInterfacesLinkInfoGroup,
                       mgmtIpStatusInterfacesIpv4InfoGroup,
                       mgmtIpStatusInterfacesDhcpClientV4InfoGroup,
                       mgmtIpStatusInterfacesIpv6InfoGroup,
                       mgmtIpStatusRoutesIpv4InfoGroup,
                       mgmtIpStatusRoutesIpv6InfoGroup,
                       mgmtIpControlGlobalsInfoGroup,
                       mgmtIpControlInterfaceDhcpClientInfoGroup,
                       mgmtIpStatisticsGlobalsIpv4InfoGroup,
                       mgmtIpStatisticsGlobalsIpv6InfoGroup,
                       mgmtIpStatisticsInterfacesLinkInfoGroup,
                       mgmtIpStatisticsInterfacesIpv4InfoGroup,
                       mgmtIpStatisticsInterfacesIpv6InfoGroup,
                       mgmtIpTrapInterfacesLinkAddInfoGroup,
                       mgmtIpTrapInterfacesLinkModInfoGroup,
                       mgmtIpTrapInterfacesLinkDelInfoGroup,
                       mgmtIpTrapGlobalsMainInfoGroup }

    ::= { mgmtIpMibCompliances 1 }

END
