-- *****************************************************************
-- FIRMWARE-MIB:  
-- ****************************************************************

MGMT-FIRMWARE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    ;

mgmtFirmwareMib MODULE-IDENTITY
    LAST-UPDATED "201807060000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of Firmware"
    REVISION    "201807060000Z"
    DESCRIPTION
        "Added 'FtpActiveMode' object to 'ControlImageUpload' table and new
         'StatusEnum' error codes."
    REVISION    "201807020000Z"
    DESCRIPTION
        "Added 'SaveSshHostKeys' object to 'ControlImageUpload' table."
    REVISION    "201702030000Z"
    DESCRIPTION
        "Replaced firmware upload status message 'Incorrect image version' with
         'Incompatible target'"
    REVISION    "201605310000Z"
    DESCRIPTION
        "Enhanced firmware upload status messages"
    REVISION    "201412160000Z"
    DESCRIPTION
        "Add switch table"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 28 }


MGMTFirmwareStatusImageEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of image for status."
    SYNTAX      INTEGER { bootloader(0), activeFirmware(1),
                          alternativeFirmware(2) }

MGMTFirmwareUploadImageEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the type of image to upload."
    SYNTAX      INTEGER { bootloader(0), firmware(1) }

MGMTFirmwareUploadStatusEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the status of upload operation."
    SYNTAX      INTEGER { none(0), success(1), inProgress(2),
                          errIvalidIp(3), errTftpFailed(4),
                          errBusy(5), errMemoryInsufficient(6),
                          errInvalidImage(7), errWriteFlash(8),
                          errSameImageExisted(9), errUnknownImage(10),
                          errFlashImageNotFound(11),
                          errFlashEntryNotFound(12), errCrc(13),
                          errImageSize(14), errEraseFlash(15),
                          errIncompatibleTarget(16),
                          errDownloadUrl(17), errInvalidUrl(18),
                          errInvalidPath(19), errInvalidFilename(20),
                          errInvalidProtocol(21),
                          errNoUserNamePassword(22) }

mgmtFirmwareMibObjects OBJECT IDENTIFIER
    ::= { mgmtFirmwareMib 1 }

mgmtFirmwareStatus OBJECT IDENTIFIER
    ::= { mgmtFirmwareMibObjects 3 }

mgmtFirmwareStatusImageTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFirmwareStatusImageEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of status of images in flash."
    ::= { mgmtFirmwareStatus 1 }

mgmtFirmwareStatusImageEntry OBJECT-TYPE
    SYNTAX      MGMTFirmwareStatusImageEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of image status."
    INDEX       { mgmtFirmwareStatusImageNumber }
    ::= { mgmtFirmwareStatusImageTable 1 }

MGMTFirmwareStatusImageEntry ::= SEQUENCE {
    mgmtFirmwareStatusImageNumber        Integer32,
    mgmtFirmwareStatusImageType          MGMTFirmwareStatusImageEnum,
    mgmtFirmwareStatusImageName          MGMTDisplayString,
    mgmtFirmwareStatusImageVersion       MGMTDisplayString,
    mgmtFirmwareStatusImageBuiltDate     MGMTDisplayString,
    mgmtFirmwareStatusImageCodeRevision  MGMTDisplayString
}

mgmtFirmwareStatusImageNumber OBJECT-TYPE
    SYNTAX      Integer32 (0..2)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The number of image. The number starts from 0."
    ::= { mgmtFirmwareStatusImageEntry 1 }

mgmtFirmwareStatusImageType OBJECT-TYPE
    SYNTAX      MGMTFirmwareStatusImageEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Image type of the status. bootloader(0) is for boot loader.
         activeFirmware(1) is for active (primary) firmware.
         alternativeFirmware(2) is for alternative (backup) firmware."
    ::= { mgmtFirmwareStatusImageEntry 2 }

mgmtFirmwareStatusImageName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..127))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Image name."
    ::= { mgmtFirmwareStatusImageEntry 3 }

mgmtFirmwareStatusImageVersion OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Image version."
    ::= { mgmtFirmwareStatusImageEntry 4 }

mgmtFirmwareStatusImageBuiltDate OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The built date when the image is built."
    ::= { mgmtFirmwareStatusImageEntry 5 }

mgmtFirmwareStatusImageCodeRevision OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..127))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The code revesion which the image is built."
    ::= { mgmtFirmwareStatusImageEntry 6 }

mgmtFirmwareStatusImageUpload OBJECT IDENTIFIER
    ::= { mgmtFirmwareStatus 2 }

mgmtFirmwareStatusImageUploadStatus OBJECT-TYPE
    SYNTAX      MGMTFirmwareUploadStatusEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The status indicates the status of current upload operation. It is
         updated automatically. Modifying this flag does not take any effect.
         none(0) means no upload operation. success(1) means upload operation is
         successful. inProgress(2) means current upload operation is in
         progress. errIvalidIp(3) means upload operation is failed due to
         invalid IP address. errTftpFailed(4) means upload operation is failed
         due to failed TFTP operation. errBusy(5) means upload operation is
         failed due to other upload in processing. errMemoryInsufficient(6)
         means upload operation is failed due to memory insufficient.
         errInvalidImage(7) means upload operation is failed due to invalid
         image. errWriteFlash(8) means upload operation is failed due to failed
         writing flash. errSameImageExisted(9) means upload operation is failed
         because the upload image is the same as the one in flash.
         errUnknownImage(10) means upload operation is failed because the type
         of upload image is unknown. errFlashImageNotFound(11) means upload
         operation is failed because the location in flash to upload the image
         is not found. errFlashEntryNotFound(12) means upload operation is
         failed because the corresponding entry in flash to upload the image is
         not found. errCrc(13) means upload operation is failed due to incorrect
         CRC in the upload image. errImageSize(14) means upload operation is
         failed due to invalid image size. errEraseFlash(15) means upload
         operation is failed due to failed erasing flash.
         errIncorrectImageVersion(16) means upload operation is failed due to
         incorrect version of the upload image. errDownloadUrl(17) means upload
         operation is failed due to fail to download image from URL.
         errInvalidUrl(18) means upload operation is failed due to invalid URL.
         errInvalidFilename(19) means upload operation is failed due to invalid
         filename of the upload image. errInvalidPath(20) means upload operation
         is failed due to invalid path of the upload image. "
    ::= { mgmtFirmwareStatusImageUpload 1 }

mgmtFirmwareStatusSwitchTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFirmwareStatusSwitchEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of status of images in switch."
    ::= { mgmtFirmwareStatus 3 }

mgmtFirmwareStatusSwitchEntry OBJECT-TYPE
    SYNTAX      MGMTFirmwareStatusSwitchEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of image status."
    INDEX       { mgmtFirmwareStatusSwitchSwitchId }
    ::= { mgmtFirmwareStatusSwitchTable 1 }

MGMTFirmwareStatusSwitchEntry ::= SEQUENCE {
    mgmtFirmwareStatusSwitchSwitchId   Integer32,
    mgmtFirmwareStatusSwitchChipId     MGMTDisplayString,
    mgmtFirmwareStatusSwitchBoardType  MGMTDisplayString,
    mgmtFirmwareStatusSwitchPortCnt    Unsigned32,
    mgmtFirmwareStatusSwitchProduct    MGMTDisplayString,
    mgmtFirmwareStatusSwitchVersion    MGMTDisplayString,
    mgmtFirmwareStatusSwitchBuiltDate  MGMTDisplayString
}

mgmtFirmwareStatusSwitchSwitchId OBJECT-TYPE
    SYNTAX      Integer32 (1..16)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The ID of switch."
    ::= { mgmtFirmwareStatusSwitchEntry 1 }

mgmtFirmwareStatusSwitchChipId OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "ID of chip."
    ::= { mgmtFirmwareStatusSwitchEntry 2 }

mgmtFirmwareStatusSwitchBoardType OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..63))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Type of board."
    ::= { mgmtFirmwareStatusSwitchEntry 3 }

mgmtFirmwareStatusSwitchPortCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Count of ports."
    ::= { mgmtFirmwareStatusSwitchEntry 4 }

mgmtFirmwareStatusSwitchProduct OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Product name."
    ::= { mgmtFirmwareStatusSwitchEntry 5 }

mgmtFirmwareStatusSwitchVersion OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Image version."
    ::= { mgmtFirmwareStatusSwitchEntry 6 }

mgmtFirmwareStatusSwitchBuiltDate OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The built date when the image is built."
    ::= { mgmtFirmwareStatusSwitchEntry 7 }

mgmtFirmwareControl OBJECT IDENTIFIER
    ::= { mgmtFirmwareMibObjects 4 }

mgmtFirmwareControlGlobals OBJECT IDENTIFIER
    ::= { mgmtFirmwareControl 1 }

mgmtFirmwareControlGlobalsSwapFirmware OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Swap firmware between active (primary) and alternative (backup). true
         is to swap the firmware. false is to do nothing."
    ::= { mgmtFirmwareControlGlobals 1 }

mgmtFirmwareControlImageUpload OBJECT IDENTIFIER
    ::= { mgmtFirmwareControl 2 }

mgmtFirmwareControlImageUploadDoUpload OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action to do upload image or not. true is to do the upload operation.
         false is to do nothing. The upload operation may need longer time to
         upload the image, so the SNMP timeout time needs to be modified
         accordingly."
    ::= { mgmtFirmwareControlImageUpload 1 }

mgmtFirmwareControlImageUploadImageType OBJECT-TYPE
    SYNTAX      MGMTFirmwareUploadImageEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Type of image to upload. bootloader(0) is to upload bootloader.
         firmware(1) is to upload application firmware."
    ::= { mgmtFirmwareControlImageUpload 2 }

mgmtFirmwareControlImageUploadUrl OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The location of image to upload. It is a specific character string that
         constitutes a reference to a resource. Syntax:
         <protocol>://[<username>[:<password>]@]<host>[:<port>][/<path>]/<file_name>
         For example, tftp://***********/new_image_path/new_image.dat,
         ***************************************/new_image_path/new_image.dat. A
         valid file name is a text string drawn from alphabet (A-Za-z), digits
         (0-9), dot (.), hyphen (-), under score(_). The maximum length is 63
         and hyphen must not be first character. The file name content that only
         contains '.' is not allowed."
    ::= { mgmtFirmwareControlImageUpload 3 }

mgmtFirmwareControlImageUploadSaveSshHostKeys OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to save new SSH host keys in local cache. "
    ::= { mgmtFirmwareControlImageUpload 4 }

mgmtFirmwareControlImageUploadFtpActiveMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to force FTP session to use active mode (default is passive
         mode). "
    ::= { mgmtFirmwareControlImageUpload 5 }

mgmtFirmwareMibConformance OBJECT IDENTIFIER
    ::= { mgmtFirmwareMib 2 }

mgmtFirmwareMibCompliances OBJECT IDENTIFIER
    ::= { mgmtFirmwareMibConformance 1 }

mgmtFirmwareMibGroups OBJECT IDENTIFIER
    ::= { mgmtFirmwareMibConformance 2 }

mgmtFirmwareStatusImageTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFirmwareStatusImageNumber,
                  mgmtFirmwareStatusImageType,
                  mgmtFirmwareStatusImageName,
                  mgmtFirmwareStatusImageVersion,
                  mgmtFirmwareStatusImageBuiltDate,
                  mgmtFirmwareStatusImageCodeRevision }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFirmwareMibGroups 1 }

mgmtFirmwareStatusImageUploadInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFirmwareStatusImageUploadStatus }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFirmwareMibGroups 2 }

mgmtFirmwareStatusSwitchTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFirmwareStatusSwitchSwitchId,
                  mgmtFirmwareStatusSwitchChipId,
                  mgmtFirmwareStatusSwitchBoardType,
                  mgmtFirmwareStatusSwitchPortCnt,
                  mgmtFirmwareStatusSwitchProduct,
                  mgmtFirmwareStatusSwitchVersion,
                  mgmtFirmwareStatusSwitchBuiltDate }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFirmwareMibGroups 3 }

mgmtFirmwareControlGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFirmwareControlGlobalsSwapFirmware }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFirmwareMibGroups 4 }

mgmtFirmwareControlImageUploadInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFirmwareControlImageUploadDoUpload,
                  mgmtFirmwareControlImageUploadImageType,
                  mgmtFirmwareControlImageUploadUrl,
                  mgmtFirmwareControlImageUploadSaveSshHostKeys,
                  mgmtFirmwareControlImageUploadFtpActiveMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFirmwareMibGroups 5 }

mgmtFirmwareMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtFirmwareStatusImageTableInfoGroup,
                       mgmtFirmwareStatusImageUploadInfoGroup,
                       mgmtFirmwareStatusSwitchTableInfoGroup,
                       mgmtFirmwareControlGlobalsInfoGroup,
                       mgmtFirmwareControlImageUploadInfoGroup }

    ::= { mgmtFirmwareMibCompliances 1 }

END
