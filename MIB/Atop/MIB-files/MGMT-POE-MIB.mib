-- *****************************************************************
-- POE-MIB:  
-- ****************************************************************

MGMT-POE-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    ;

mgmtPoeMib MODULE-IDENTITY
    LAST-UPDATED "201904100000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of Power over Ethernet (PoE)."
    REVISION    "201904100000Z"
    DESCRIPTION
        "Add ability to disable PoE lldp functionality."
    REVISION    "201903140000Z"
    DESCRIPTION
        "Refactor according to IEEE802.3BT requirement."
    REVISION    "201609300000Z"
    DESCRIPTION
        "Enhanced PoE interface status messages. Added reserved power status
         entry."
    REVISION    "201408200000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 43 }


MGMTpoeLldpDisableType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the port disable status."
    SYNTAX      INTEGER { enable(0), disable(1) }

MGMTpoeModeType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the types of port PoE mode."
    SYNTAX      INTEGER { disable(0), standard(1), plus(2) }

MGMTpoePdStructure ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the PD structure."
    SYNTAX      INTEGER { notDetected(0), open(1),
                          invalidSignature(2), ieee4PairSingleSig(3),
                          legacy4PairSingleSig(4),
                          ieee4PairDualSig(5), p2p4CandidateFalse(6),
                          ieee2Pair(7), legacy2Pair(8) }

MGMTpoePowerPriorityType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the port power priority."
    SYNTAX      INTEGER { low(0), high(1), critical(2) }

MGMTpoeStatusType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the feature status."
    SYNTAX      INTEGER { notSupported(0), budgetExceeded(1),
                          noPdDetected(2), pdOn(3), pdOff(4),
                          pdOverloaded(5), unknownState(6),
                          disabled(7), shutdown(8) }

mgmtPoeMibObjects OBJECT IDENTIFIER
    ::= { mgmtPoeMib 1 }

mgmtPoeCapabilities OBJECT IDENTIFIER
    ::= { mgmtPoeMibObjects 1 }

mgmtPoeCapabilitiesInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPoeCapabilitiesInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to interface capabilities"
    ::= { mgmtPoeCapabilities 1 }

mgmtPoeCapabilitiesInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTPoeCapabilitiesInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of capability parameters"
    INDEX       { mgmtPoeCapabilitiesInterfaceIfIndex }
    ::= { mgmtPoeCapabilitiesInterfaceTable 1 }

MGMTPoeCapabilitiesInterfaceEntry ::= SEQUENCE {
    mgmtPoeCapabilitiesInterfaceIfIndex  MGMTInterfaceIndex,
    mgmtPoeCapabilitiesInterfacePoE      TruthValue
}

mgmtPoeCapabilitiesInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPoeCapabilitiesInterfaceEntry 1 }

mgmtPoeCapabilitiesInterfacePoE OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether interface is PoE capable or not."
    ::= { mgmtPoeCapabilitiesInterfaceEntry 2 }

mgmtPoeCapabilitiesPsu OBJECT IDENTIFIER
    ::= { mgmtPoeCapabilities 2 }

mgmtPoeCapabilitiesPsuUserConfigurable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether the user can and need to specify the amount of power
         the PSU can deliver"
    ::= { mgmtPoeCapabilitiesPsu 1 }

mgmtPoeCapabilitiesPsuMaxPower OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates the max power in DeciWatt the PoE board can handle. No reason
         to use a bigger PSU than this. For systems with internal PSU, this is
         the size of the built-in PSU."
    ::= { mgmtPoeCapabilitiesPsu 2 }

mgmtPoeCapabilitiesPsuSystemReservedPower OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "For systems where the switch itself is powered by the same PSU as used
         for the PoE functionality, this shall reflect the amount of power
         required to keep the switch operating."
    ::= { mgmtPoeCapabilitiesPsu 3 }

mgmtPoeCapabilitiesPsuLegacyModeConfigurable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether the PSE supports detection of legacy PD devices, and
         that the user can configure this feature."
    ::= { mgmtPoeCapabilitiesPsu 4 }

mgmtPoeCapabilitiesPsuUninterruptablePowerSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether switch can reset software without affecting PoE
         powered devices connected to the switch."
    ::= { mgmtPoeCapabilitiesPsu 5 }

mgmtPoeConfig OBJECT IDENTIFIER
    ::= { mgmtPoeMibObjects 2 }

mgmtPoeConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtPoeConfig 1 }

mgmtPoeConfigGlobalsSwitchId OBJECT-TYPE
    SYNTAX      Unsigned32 (1..16)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User Switch Id."
    ::= { mgmtPoeConfigGlobals 1 }

mgmtPoeConfigGlobalsMaxPower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Maximum power(in Watt) that the power sourcing equipment(such as
         switch) can deliver. This value can only be configured if the
         CapabilitiesPsuUserConfigurable object is true. If the Maximum power is
         configurable, the valid range is from 0 to the value of the
         CapabilitiesPsuMaxPower object."
    ::= { mgmtPoeConfigGlobals 2 }

mgmtPoeConfigGlobalsCapacitorDetection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether switch capacitor detection feature is enabled or not."
    ::= { mgmtPoeConfigGlobals 3 }

mgmtPoeConfigGlobalsUninterruptablePower OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether software reset of switch shall affect PoE powered
         devices connected to the switch."
    ::= { mgmtPoeConfigGlobals 4 }

mgmtPoeConfigSwitch OBJECT IDENTIFIER
    ::= { mgmtPoeConfig 2 }

mgmtPoeConfigSwitchParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPoeConfigSwitchParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure PoE configurations for a switch."
    ::= { mgmtPoeConfigSwitch 1 }

mgmtPoeConfigSwitchParamEntry OBJECT-TYPE
    SYNTAX      MGMTPoeConfigSwitchParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each switch has a set of PoE configurable parameters"
    INDEX       { mgmtPoeConfigSwitchParamSwitchId }
    ::= { mgmtPoeConfigSwitchParamTable 1 }

MGMTPoeConfigSwitchParamEntry ::= SEQUENCE {
    mgmtPoeConfigSwitchParamSwitchId              Unsigned32,
    mgmtPoeConfigSwitchParamMaxPower              Integer32,
    mgmtPoeConfigSwitchParamCapacitorDetection    TruthValue,
    mgmtPoeConfigSwitchParamUninterruptablePower  TruthValue
}

mgmtPoeConfigSwitchParamSwitchId OBJECT-TYPE
    SYNTAX      Unsigned32 (1..16)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "User Switch Id."
    ::= { mgmtPoeConfigSwitchParamEntry 1 }

mgmtPoeConfigSwitchParamMaxPower OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Maximum power(in Watt) that the power sourcing equipment(such as
         switch) can deliver. This value can only be configured if the
         CapabilitiesPsuUserConfigurable object is true. If the Maximum power is
         configurable, the valid range is from 0 to the value of the
         CapabilitiesPsuMaxPower object."
    ::= { mgmtPoeConfigSwitchParamEntry 2 }

mgmtPoeConfigSwitchParamCapacitorDetection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether switch capacitor detection feature is enabled or not."
    ::= { mgmtPoeConfigSwitchParamEntry 3 }

mgmtPoeConfigSwitchParamUninterruptablePower OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether software reset of switch shall affect PoE powered
         devices connected to the switch."
    ::= { mgmtPoeConfigSwitchParamEntry 4 }

mgmtPoeConfigInterface OBJECT IDENTIFIER
    ::= { mgmtPoeConfig 3 }

mgmtPoeConfigInterfaceParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPoeConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure PoE configurations for a specific
         interface."
    ::= { mgmtPoeConfigInterface 1 }

mgmtPoeConfigInterfaceParamEntry OBJECT-TYPE
    SYNTAX      MGMTPoeConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of PoE configurable parameters"
    INDEX       { mgmtPoeConfigInterfaceParamIfIndex }
    ::= { mgmtPoeConfigInterfaceParamTable 1 }

MGMTPoeConfigInterfaceParamEntry ::= SEQUENCE {
    mgmtPoeConfigInterfaceParamIfIndex   MGMTInterfaceIndex,
    mgmtPoeConfigInterfaceParamMode      MGMTpoeModeType,
    mgmtPoeConfigInterfaceParamPriority  MGMTpoePowerPriorityType,
    mgmtPoeConfigInterfaceParamLldp      MGMTpoeLldpDisableType
}

mgmtPoeConfigInterfaceParamIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPoeConfigInterfaceParamEntry 1 }

mgmtPoeConfigInterfaceParamMode OBJECT-TYPE
    SYNTAX      MGMTpoeModeType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set PoE mode or disable PoE feature, two PoE modes are supported. POE:
         Enables PoE based on IEEE 802.3af standard, and provides power up to
         15.4W(154 deciwatt) of DC power to powered device. When changing to
         standard mode the MaxPower is automatically adjust to 15.4 W, if it
         currently exceeds 15.4 W. POE_PLUS: Enabled PoE based on IEEE 802.3at
         standard, and provides power up to 30W(300 deciwatt) of DC power to
         powered device."
    ::= { mgmtPoeConfigInterfaceParamEntry 2 }

mgmtPoeConfigInterfaceParamPriority OBJECT-TYPE
    SYNTAX      MGMTpoePowerPriorityType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set port power priority. Priority determines the order in which the
         interfaces will receive power. Interfaces with a higher priority will
         receive power before interfaces with a lower priority. PRIORITY_LOW
         means lowest priority. PRIORITY_HIGH means medium priority.
         PRIORITY_CRITICAL means highest priority."
    ::= { mgmtPoeConfigInterfaceParamEntry 3 }

mgmtPoeConfigInterfaceParamLldp OBJECT-TYPE
    SYNTAX      MGMTpoeLldpDisableType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set port lldp awareness. If this value is disable, the PSE will ignore
         PoE related parts of received LLDP frames."
    ::= { mgmtPoeConfigInterfaceParamEntry 4 }

mgmtPoeStatus OBJECT IDENTIFIER
    ::= { mgmtPoeMibObjects 3 }

mgmtPoeStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPoeStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to Power over Ethernet interface status"
    ::= { mgmtPoeStatus 1 }

mgmtPoeStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTPoeStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of status parameters"
    INDEX       { mgmtPoeStatusInterfaceIfIndex }
    ::= { mgmtPoeStatusInterfaceTable 1 }

MGMTPoeStatusInterfaceEntry ::= SEQUENCE {
    mgmtPoeStatusInterfaceIfIndex             MGMTInterfaceIndex,
    mgmtPoeStatusInterfacePDClassAltA         Integer32,
    mgmtPoeStatusInterfacePDClassAltB         Integer32,
    mgmtPoeStatusInterfaceCurrentState        MGMTpoeStatusType,
    mgmtPoeStatusInterfacePSEType             Integer32,
    mgmtPoeStatusInterfacePDStructure         MGMTpoePdStructure,
    mgmtPoeStatusInterfacePowerLimit          Integer32,
    mgmtPoeStatusInterfacePowerReserved       Integer32,
    mgmtPoeStatusInterfacePowerConsumption    Integer32,
    mgmtPoeStatusInterfaceCurrentConsumption  Integer32
}

mgmtPoeStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPoeStatusInterfaceEntry 1 }

mgmtPoeStatusInterfacePDClassAltA OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Powered device(PD) negotiates a power class with sourcing
         equipment(PSE) during the time of initial connection, each class have a
         maximum supported power. Class assigned to PD alternative A is based on
         PD electrical characteristics. Value -1 means either PD attached to the
         interface can not advertise its power class or no PD detected or PoE is
         not supported or PoE feature is disabled or unsupported PD
         class(classes 0-4 is supported)."
    ::= { mgmtPoeStatusInterfaceEntry 2 }

mgmtPoeStatusInterfacePDClassAltB OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Powered device(PD) negotiates a power class with sourcing
         equipment(PSE) during the time of initial connection, each class have a
         maximum supported power. Class assigned to PD alternative B is based on
         PD electrical characteristics. Value -1 means either PD attached to the
         interface can not advertise its power class or no PD detected or PoE is
         not supported or PoE feature is disabled or unsupported PD
         class(classes 0-4 is supported)."
    ::= { mgmtPoeStatusInterfaceEntry 3 }

mgmtPoeStatusInterfaceCurrentState OBJECT-TYPE
    SYNTAX      MGMTpoeStatusType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate port status. notSupported: PoE not supportedbudgetExceeded:
         PoE is turned OFF due to power budget exceeded on PSE.
         noPoweredDeviceDetected: No PD detected. poweredDeviceOn: PSE supplying
         power to PD through PoE. poweredDeviceOverloaded: PD consumes more
         power than the maximum limit configured on the PSE
         port.poweredDeviceOff: PD is powered downunknownState: PD state
         unknowndisabled: PoE is disabled for the
         interfacedisabledInterfaceShutdown: PD is powered down due to interface
         shut-down"
    ::= { mgmtPoeStatusInterfaceEntry 4 }

mgmtPoeStatusInterfacePSEType OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Type of power sourcing equipment(PSE) according to 802.3bt. A PSE type
         1 supports PD class 1-3 PDs, a PSE type 2 supports PD class 1-4 PDs, a
         PSE type 3 supports PD class 1-6 PDs, a PSE type 4 supports PD class
         1-8 PDs."
    ::= { mgmtPoeStatusInterfaceEntry 5 }

mgmtPoeStatusInterfacePDStructure OBJECT-TYPE
    SYNTAX      MGMTpoePdStructure
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "notPerformed: Test not yet performed, open: No device found,
         invalidSignature: No valid signature found, ieee4PairSingleSig: four
         pair single signature PD detected, legacy4PairSingleSig: four pair
         single signature legacy PD detected, ieee4PairDualSig: four
         pair dual signature PD detected, p2p4CandidateFalse: TBD, ieee2Pair:
         two pair PD detected, legacy2Pair: two pair legacy PD
         detected."
    ::= { mgmtPoeStatusInterfaceEntry 6 }

mgmtPoeStatusInterfacePowerLimit OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates the power limit (in deciwatt) given by the class of the PD."
    ::= { mgmtPoeStatusInterfaceEntry 7 }

mgmtPoeStatusInterfacePowerReserved OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The power (in deciwatt) reserved for the PD. When power is allocated on
         basis of PD class, this number will be equal to the consumed power.
         When LLDP is used to allocated power, this will be the amount of power
         reserved through LLDP. The value is only meaningful when the PD is on."
    ::= { mgmtPoeStatusInterfaceEntry 8 }

mgmtPoeStatusInterfacePowerConsumption OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates the power(in deciwatt) that the PD is consuming right now."
    ::= { mgmtPoeStatusInterfaceEntry 9 }

mgmtPoeStatusInterfaceCurrentConsumption OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates the current(in mA) that the PD is consuming right now."
    ::= { mgmtPoeStatusInterfaceEntry 10 }

mgmtPoeMibConformance OBJECT IDENTIFIER
    ::= { mgmtPoeMib 2 }

mgmtPoeMibCompliances OBJECT IDENTIFIER
    ::= { mgmtPoeMibConformance 1 }

mgmtPoeMibGroups OBJECT IDENTIFIER
    ::= { mgmtPoeMibConformance 2 }

mgmtPoeCapabilitiesInterfaceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPoeCapabilitiesInterfaceIfIndex,
                  mgmtPoeCapabilitiesInterfacePoE }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPoeMibGroups 1 }

mgmtPoeCapabilitiesPsuInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPoeCapabilitiesPsuUserConfigurable,
                  mgmtPoeCapabilitiesPsuMaxPower,
                  mgmtPoeCapabilitiesPsuSystemReservedPower,
                  mgmtPoeCapabilitiesPsuLegacyModeConfigurable,
                  mgmtPoeCapabilitiesPsuUninterruptablePowerSupported }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPoeMibGroups 2 }

mgmtPoeConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPoeConfigGlobalsSwitchId,
                  mgmtPoeConfigGlobalsMaxPower,
                  mgmtPoeConfigGlobalsCapacitorDetection,
                  mgmtPoeConfigGlobalsUninterruptablePower }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPoeMibGroups 3 }

mgmtPoeConfigSwitchParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPoeConfigSwitchParamSwitchId,
                  mgmtPoeConfigSwitchParamMaxPower,
                  mgmtPoeConfigSwitchParamCapacitorDetection,
                  mgmtPoeConfigSwitchParamUninterruptablePower }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPoeMibGroups 4 }

mgmtPoeConfigInterfaceParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPoeConfigInterfaceParamIfIndex,
                  mgmtPoeConfigInterfaceParamMode,
                  mgmtPoeConfigInterfaceParamPriority,
                  mgmtPoeConfigInterfaceParamLldp }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPoeMibGroups 5 }

mgmtPoeStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPoeStatusInterfaceIfIndex,
                  mgmtPoeStatusInterfacePDClassAltA,
                  mgmtPoeStatusInterfacePDClassAltB,
                  mgmtPoeStatusInterfaceCurrentState,
                  mgmtPoeStatusInterfacePSEType,
                  mgmtPoeStatusInterfacePDStructure,
                  mgmtPoeStatusInterfacePowerLimit,
                  mgmtPoeStatusInterfacePowerReserved,
                  mgmtPoeStatusInterfacePowerConsumption,
                  mgmtPoeStatusInterfaceCurrentConsumption }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPoeMibGroups 6 }

mgmtPoeMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtPoeCapabilitiesInterfaceInfoGroup,
                       mgmtPoeCapabilitiesPsuInfoGroup,
                       mgmtPoeConfigGlobalsInfoGroup,
                       mgmtPoeConfigSwitchParamTableInfoGroup,
                       mgmtPoeConfigInterfaceParamTableInfoGroup,
                       mgmtPoeStatusInterfaceTableInfoGroup }

    ::= { mgmtPoeMibCompliances 1 }

END
