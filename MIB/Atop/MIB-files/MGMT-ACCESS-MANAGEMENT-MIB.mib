-- *****************************************************************
-- ACCESS-MANAGEMENT-MIB:  
-- ****************************************************************

MGMT-ACCESS-MANAGEMENT-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    ;

mgmtAccessManagementMib MODULE-IDENTITY
    LAST-UPDATED "201705220000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the Access Management MIB"
    REVISION    "201705220000Z"
    DESCRIPTION
        "Remove definition of unused OID ...AccessManagementStatus"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 51 }


mgmtAccessManagementMibObjects OBJECT IDENTIFIER
    ::= { mgmtAccessManagementMib 1 }

mgmtAccessManagementConfig OBJECT IDENTIFIER
    ::= { mgmtAccessManagementMibObjects 2 }

mgmtAccessManagementConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtAccessManagementConfig 1 }

mgmtAccessManagementConfigGlobalsAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the Access Management global functionality."
    ::= { mgmtAccessManagementConfigGlobals 1 }

mgmtAccessManagementConfigIpv4Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAccessManagementConfigIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing Access Management per IPv4 basis"
    ::= { mgmtAccessManagementConfig 2 }

mgmtAccessManagementConfigIpv4Entry OBJECT-TYPE
    SYNTAX      MGMTAccessManagementConfigIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtAccessManagementConfigIpv4AccessIndex }
    ::= { mgmtAccessManagementConfigIpv4Table 1 }

MGMTAccessManagementConfigIpv4Entry ::= SEQUENCE {
    mgmtAccessManagementConfigIpv4AccessIndex     Integer32,
    mgmtAccessManagementConfigIpv4VlanId          MGMTUnsigned16,
    mgmtAccessManagementConfigIpv4StartAddress    IpAddress,
    mgmtAccessManagementConfigIpv4EndAddress      IpAddress,
    mgmtAccessManagementConfigIpv4WebServices     TruthValue,
    mgmtAccessManagementConfigIpv4SnmpServices    TruthValue,
    mgmtAccessManagementConfigIpv4TelnetServices  TruthValue,
    mgmtAccessManagementConfigIpv4Action          MGMTRowEditorState
}

mgmtAccessManagementConfigIpv4AccessIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Index for Access Management IPv4/IPv6 table."
    ::= { mgmtAccessManagementConfigIpv4Entry 1 }

mgmtAccessManagementConfigIpv4VlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ID of specific VLAN interface that Access Management should take
         effect for IPv4."
    ::= { mgmtAccessManagementConfigIpv4Entry 2 }

mgmtAccessManagementConfigIpv4StartAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The starting IPv4 address of the range that Access Management performs
         checking."
    ::= { mgmtAccessManagementConfigIpv4Entry 3 }

mgmtAccessManagementConfigIpv4EndAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ending IPv4 address of the range that Access Management performs
         checking."
    ::= { mgmtAccessManagementConfigIpv4Entry 4 }

mgmtAccessManagementConfigIpv4WebServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable HTTP and HTTPS functionality via Access Management. At
         least one of WebServices/SnmpServices/TelnetServices has to be enabled
         for a specific AccessIndex in Access Management IPv4 table."
    ::= { mgmtAccessManagementConfigIpv4Entry 5 }

mgmtAccessManagementConfigIpv4SnmpServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable SNMP functionality via Access Management. At least one
         of WebServices/SnmpServices/TelnetServices has to be enabled for a
         specific AccessIndex in Access Management IPv4 table."
    ::= { mgmtAccessManagementConfigIpv4Entry 6 }

mgmtAccessManagementConfigIpv4TelnetServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable TELNET/SSH functionality via Access Management. At least
         one of WebServices/SnmpServices/TelnetServices has to be enabled for a
         specific AccessIndex in Access Management IPv4 table."
    ::= { mgmtAccessManagementConfigIpv4Entry 7 }

mgmtAccessManagementConfigIpv4Action OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtAccessManagementConfigIpv4Entry 100 }

mgmtAccessManagementConfigIpv4TableRowEditor OBJECT IDENTIFIER
    ::= { mgmtAccessManagementConfig 3 }

mgmtAccessManagementConfigIpv4TableRowEditorAccessIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Index for Access Management IPv4/IPv6 table."
    ::= { mgmtAccessManagementConfigIpv4TableRowEditor 1 }

mgmtAccessManagementConfigIpv4TableRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ID of specific VLAN interface that Access Management should take
         effect for IPv4."
    ::= { mgmtAccessManagementConfigIpv4TableRowEditor 2 }

mgmtAccessManagementConfigIpv4TableRowEditorStartAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The starting IPv4 address of the range that Access Management performs
         checking."
    ::= { mgmtAccessManagementConfigIpv4TableRowEditor 3 }

mgmtAccessManagementConfigIpv4TableRowEditorEndAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ending IPv4 address of the range that Access Management performs
         checking."
    ::= { mgmtAccessManagementConfigIpv4TableRowEditor 4 }

mgmtAccessManagementConfigIpv4TableRowEditorWebServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable HTTP and HTTPS functionality via Access Management. At
         least one of WebServices/SnmpServices/TelnetServices has to be enabled
         for a specific AccessIndex in Access Management IPv4 table."
    ::= { mgmtAccessManagementConfigIpv4TableRowEditor 5 }

mgmtAccessManagementConfigIpv4TableRowEditorSnmpServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable SNMP functionality via Access Management. At least one
         of WebServices/SnmpServices/TelnetServices has to be enabled for a
         specific AccessIndex in Access Management IPv4 table."
    ::= { mgmtAccessManagementConfigIpv4TableRowEditor 6 }

mgmtAccessManagementConfigIpv4TableRowEditorTelnetServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable TELNET/SSH functionality via Access Management. At least
         one of WebServices/SnmpServices/TelnetServices has to be enabled for a
         specific AccessIndex in Access Management IPv4 table."
    ::= { mgmtAccessManagementConfigIpv4TableRowEditor 7 }

mgmtAccessManagementConfigIpv4TableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtAccessManagementConfigIpv4TableRowEditor 100 }

mgmtAccessManagementConfigIpv6Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAccessManagementConfigIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing Access Management per IPv6 basis"
    ::= { mgmtAccessManagementConfig 4 }

mgmtAccessManagementConfigIpv6Entry OBJECT-TYPE
    SYNTAX      MGMTAccessManagementConfigIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtAccessManagementConfigIpv6AccessIndex }
    ::= { mgmtAccessManagementConfigIpv6Table 1 }

MGMTAccessManagementConfigIpv6Entry ::= SEQUENCE {
    mgmtAccessManagementConfigIpv6AccessIndex     Integer32,
    mgmtAccessManagementConfigIpv6VlanId          MGMTUnsigned16,
    mgmtAccessManagementConfigIpv6StartAddress    InetAddressIPv6,
    mgmtAccessManagementConfigIpv6EndAddress      InetAddressIPv6,
    mgmtAccessManagementConfigIpv6WebServices     TruthValue,
    mgmtAccessManagementConfigIpv6SnmpServices    TruthValue,
    mgmtAccessManagementConfigIpv6TelnetServices  TruthValue,
    mgmtAccessManagementConfigIpv6Action          MGMTRowEditorState
}

mgmtAccessManagementConfigIpv6AccessIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Index for Access Management IPv4/IPv6 table."
    ::= { mgmtAccessManagementConfigIpv6Entry 1 }

mgmtAccessManagementConfigIpv6VlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ID of specific VLAN interface that Access Management should take
         effect for IPv6."
    ::= { mgmtAccessManagementConfigIpv6Entry 2 }

mgmtAccessManagementConfigIpv6StartAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The starting IPv6 address of the range that Access Management performs
         checking."
    ::= { mgmtAccessManagementConfigIpv6Entry 3 }

mgmtAccessManagementConfigIpv6EndAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ending IPv6 address of the range that Access Management performs
         checking."
    ::= { mgmtAccessManagementConfigIpv6Entry 4 }

mgmtAccessManagementConfigIpv6WebServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable HTTP and HTTPS functionality via Access Management. At
         least one of WebServices/SnmpServices/TelnetServices has to be enabled
         for a specific AccessIndex in Access Management IPv6 table."
    ::= { mgmtAccessManagementConfigIpv6Entry 5 }

mgmtAccessManagementConfigIpv6SnmpServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable SNMP functionality via Access Management. At least one
         of WebServices/SnmpServices/TelnetServices has to be enabled for a
         specific AccessIndex in Access Management IPv6 table."
    ::= { mgmtAccessManagementConfigIpv6Entry 6 }

mgmtAccessManagementConfigIpv6TelnetServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable TELNET/SSH functionality via Access Management. At least
         one of WebServices/SnmpServices/TelnetServices has to be enabled for a
         specific AccessIndex in Access Management IPv6 table."
    ::= { mgmtAccessManagementConfigIpv6Entry 7 }

mgmtAccessManagementConfigIpv6Action OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtAccessManagementConfigIpv6Entry 100 }

mgmtAccessManagementConfigIpv6TableRowEditor OBJECT IDENTIFIER
    ::= { mgmtAccessManagementConfig 5 }

mgmtAccessManagementConfigIpv6TableRowEditorAccessIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Index for Access Management IPv4/IPv6 table."
    ::= { mgmtAccessManagementConfigIpv6TableRowEditor 1 }

mgmtAccessManagementConfigIpv6TableRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ID of specific VLAN interface that Access Management should take
         effect for IPv6."
    ::= { mgmtAccessManagementConfigIpv6TableRowEditor 2 }

mgmtAccessManagementConfigIpv6TableRowEditorStartAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The starting IPv6 address of the range that Access Management performs
         checking."
    ::= { mgmtAccessManagementConfigIpv6TableRowEditor 3 }

mgmtAccessManagementConfigIpv6TableRowEditorEndAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The ending IPv6 address of the range that Access Management performs
         checking."
    ::= { mgmtAccessManagementConfigIpv6TableRowEditor 4 }

mgmtAccessManagementConfigIpv6TableRowEditorWebServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable HTTP and HTTPS functionality via Access Management. At
         least one of WebServices/SnmpServices/TelnetServices has to be enabled
         for a specific AccessIndex in Access Management IPv6 table."
    ::= { mgmtAccessManagementConfigIpv6TableRowEditor 5 }

mgmtAccessManagementConfigIpv6TableRowEditorSnmpServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable SNMP functionality via Access Management. At least one
         of WebServices/SnmpServices/TelnetServices has to be enabled for a
         specific AccessIndex in Access Management IPv6 table."
    ::= { mgmtAccessManagementConfigIpv6TableRowEditor 6 }

mgmtAccessManagementConfigIpv6TableRowEditorTelnetServices OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable TELNET/SSH functionality via Access Management. At least
         one of WebServices/SnmpServices/TelnetServices has to be enabled for a
         specific AccessIndex in Access Management IPv6 table."
    ::= { mgmtAccessManagementConfigIpv6TableRowEditor 7 }

mgmtAccessManagementConfigIpv6TableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtAccessManagementConfigIpv6TableRowEditor 100 }

mgmtAccessManagementControl OBJECT IDENTIFIER
    ::= { mgmtAccessManagementMibObjects 4 }

mgmtAccessManagementControlStatistics OBJECT IDENTIFIER
    ::= { mgmtAccessManagementControl 1 }

mgmtAccessManagementControlStatisticsClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "To trigger the control action (only) when TRUE."
    ::= { mgmtAccessManagementControlStatistics 1 }

mgmtAccessManagementStatistics OBJECT IDENTIFIER
    ::= { mgmtAccessManagementMibObjects 5 }

mgmtAccessManagementStatisticsHttpReceivedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received count of frames via HTTP."
    ::= { mgmtAccessManagementStatistics 1 }

mgmtAccessManagementStatisticsHttpAllowedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Permit count of frames via HTTP."
    ::= { mgmtAccessManagementStatistics 2 }

mgmtAccessManagementStatisticsHttpDiscardedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Drop count of frames via HTTP."
    ::= { mgmtAccessManagementStatistics 3 }

mgmtAccessManagementStatisticsHttpsReceivedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received count of frames via HTTPS."
    ::= { mgmtAccessManagementStatistics 4 }

mgmtAccessManagementStatisticsHttpsAllowedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Permit count of frames via HTTPS."
    ::= { mgmtAccessManagementStatistics 5 }

mgmtAccessManagementStatisticsHttpsDiscardedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Drop count of frames via HTTPS."
    ::= { mgmtAccessManagementStatistics 6 }

mgmtAccessManagementStatisticsSnmpReceivedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received count of frames via SNMP."
    ::= { mgmtAccessManagementStatistics 7 }

mgmtAccessManagementStatisticsSnmpAllowedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Permit count of frames via SNMP."
    ::= { mgmtAccessManagementStatistics 8 }

mgmtAccessManagementStatisticsSnmpDiscardedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Drop count of frames via SNMP."
    ::= { mgmtAccessManagementStatistics 9 }

mgmtAccessManagementStatisticsTelnetReceivedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received count of frames via TELNET."
    ::= { mgmtAccessManagementStatistics 10 }

mgmtAccessManagementStatisticsTelnetAllowedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Permit count of frames via TELNET."
    ::= { mgmtAccessManagementStatistics 11 }

mgmtAccessManagementStatisticsTelnetDiscardedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Drop count of frames via TELNET."
    ::= { mgmtAccessManagementStatistics 12 }

mgmtAccessManagementStatisticsSshReceivedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Received count of frames via SSH."
    ::= { mgmtAccessManagementStatistics 13 }

mgmtAccessManagementStatisticsSshAllowedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Permit count of frames via SSH."
    ::= { mgmtAccessManagementStatistics 14 }

mgmtAccessManagementStatisticsSshDiscardedPkts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Drop count of frames via SSH."
    ::= { mgmtAccessManagementStatistics 15 }

mgmtAccessManagementMibConformance OBJECT IDENTIFIER
    ::= { mgmtAccessManagementMib 2 }

mgmtAccessManagementMibCompliances OBJECT IDENTIFIER
    ::= { mgmtAccessManagementMibConformance 1 }

mgmtAccessManagementMibGroups OBJECT IDENTIFIER
    ::= { mgmtAccessManagementMibConformance 2 }

mgmtAccessManagementConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAccessManagementConfigGlobalsAdminState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAccessManagementMibGroups 1 }

mgmtAccessManagementConfigIpv4TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAccessManagementConfigIpv4AccessIndex,
                  mgmtAccessManagementConfigIpv4VlanId,
                  mgmtAccessManagementConfigIpv4StartAddress,
                  mgmtAccessManagementConfigIpv4EndAddress,
                  mgmtAccessManagementConfigIpv4WebServices,
                  mgmtAccessManagementConfigIpv4SnmpServices,
                  mgmtAccessManagementConfigIpv4TelnetServices,
                  mgmtAccessManagementConfigIpv4Action }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAccessManagementMibGroups 2 }

mgmtAccessManagementConfigIpv4TableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtAccessManagementConfigIpv4TableRowEditorAccessIndex,
                  mgmtAccessManagementConfigIpv4TableRowEditorVlanId,
                  mgmtAccessManagementConfigIpv4TableRowEditorStartAddress,
                  mgmtAccessManagementConfigIpv4TableRowEditorEndAddress,
                  mgmtAccessManagementConfigIpv4TableRowEditorWebServices,
                  mgmtAccessManagementConfigIpv4TableRowEditorSnmpServices,
                  mgmtAccessManagementConfigIpv4TableRowEditorTelnetServices,
                  mgmtAccessManagementConfigIpv4TableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAccessManagementMibGroups 3 }

mgmtAccessManagementConfigIpv6TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAccessManagementConfigIpv6AccessIndex,
                  mgmtAccessManagementConfigIpv6VlanId,
                  mgmtAccessManagementConfigIpv6StartAddress,
                  mgmtAccessManagementConfigIpv6EndAddress,
                  mgmtAccessManagementConfigIpv6WebServices,
                  mgmtAccessManagementConfigIpv6SnmpServices,
                  mgmtAccessManagementConfigIpv6TelnetServices,
                  mgmtAccessManagementConfigIpv6Action }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAccessManagementMibGroups 4 }

mgmtAccessManagementConfigIpv6TableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtAccessManagementConfigIpv6TableRowEditorAccessIndex,
                  mgmtAccessManagementConfigIpv6TableRowEditorVlanId,
                  mgmtAccessManagementConfigIpv6TableRowEditorStartAddress,
                  mgmtAccessManagementConfigIpv6TableRowEditorEndAddress,
                  mgmtAccessManagementConfigIpv6TableRowEditorWebServices,
                  mgmtAccessManagementConfigIpv6TableRowEditorSnmpServices,
                  mgmtAccessManagementConfigIpv6TableRowEditorTelnetServices,
                  mgmtAccessManagementConfigIpv6TableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAccessManagementMibGroups 5 }

mgmtAccessManagementControlStatisticsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAccessManagementControlStatisticsClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAccessManagementMibGroups 6 }

mgmtAccessManagementStatisticsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAccessManagementStatisticsHttpReceivedPkts,
                  mgmtAccessManagementStatisticsHttpAllowedPkts,
                  mgmtAccessManagementStatisticsHttpDiscardedPkts,
                  mgmtAccessManagementStatisticsHttpsReceivedPkts,
                  mgmtAccessManagementStatisticsHttpsAllowedPkts,
                  mgmtAccessManagementStatisticsHttpsDiscardedPkts,
                  mgmtAccessManagementStatisticsSnmpReceivedPkts,
                  mgmtAccessManagementStatisticsSnmpAllowedPkts,
                  mgmtAccessManagementStatisticsSnmpDiscardedPkts,
                  mgmtAccessManagementStatisticsTelnetReceivedPkts,
                  mgmtAccessManagementStatisticsTelnetAllowedPkts,
                  mgmtAccessManagementStatisticsTelnetDiscardedPkts,
                  mgmtAccessManagementStatisticsSshReceivedPkts,
                  mgmtAccessManagementStatisticsSshAllowedPkts,
                  mgmtAccessManagementStatisticsSshDiscardedPkts }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAccessManagementMibGroups 7 }

mgmtAccessManagementMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtAccessManagementConfigGlobalsInfoGroup,
                       mgmtAccessManagementConfigIpv4TableInfoGroup,
                       mgmtAccessManagementConfigIpv4TableRowEditorInfoGroup,
                       mgmtAccessManagementConfigIpv6TableInfoGroup,
                       mgmtAccessManagementConfigIpv6TableRowEditorInfoGroup,
                       mgmtAccessManagementControlStatisticsInfoGroup,
                       mgmtAccessManagementStatisticsInfoGroup }

    ::= { mgmtAccessManagementMibCompliances 1 }

END
