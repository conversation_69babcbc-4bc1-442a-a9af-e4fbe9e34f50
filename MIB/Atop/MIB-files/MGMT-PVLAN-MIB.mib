-- *****************************************************************
-- PVLAN-MIB:  
-- ****************************************************************

MGMT-PVLAN-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    ;

mgmtPvlanMib MODULE-IDENTITY
    LAST-UPDATED "201407160000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the Private VLAN MIB"
    REVISION    "201407160000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 23 }


mgmtPvlanMibObjects OBJECT IDENTIFIER
    ::= { mgmtPvlanMib 1 }

mgmtPvlanCapabilities OBJECT IDENTIFIER
    ::= { mgmtPvlanMibObjects 1 }

mgmtPvlanCapabilitiesHasVlanMembershipMgmt OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The capability to support PVLAN membership configuration by the device."
    ::= { mgmtPvlanCapabilities 1 }

mgmtPvlanCapabilitiesVlanIdMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum VLAN ID of PVLAN membership configuration supported by the
         device."
    ::= { mgmtPvlanCapabilities 2 }

mgmtPvlanCapabilitiesVlanIdMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum VLAN ID of PVLAN membership configuration supported by the
         device."
    ::= { mgmtPvlanCapabilities 3 }

mgmtPvlanConfig OBJECT IDENTIFIER
    ::= { mgmtPvlanMibObjects 2 }

mgmtPvlanConfigInterface OBJECT IDENTIFIER
    ::= { mgmtPvlanConfig 1 }

mgmtPvlanConfigInterfaceVlanMembershipTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPvlanConfigInterfaceVlanMembershipEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing Private VLAN VLAN membership entries."
    ::= { mgmtPvlanConfigInterface 1 }

mgmtPvlanConfigInterfaceVlanMembershipEntry OBJECT-TYPE
    SYNTAX      MGMTPvlanConfigInterfaceVlanMembershipEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtPvlanConfigInterfaceVlanMembershipPvlanIndex }
    ::= { mgmtPvlanConfigInterfaceVlanMembershipTable 1 }

MGMTPvlanConfigInterfaceVlanMembershipEntry ::= SEQUENCE {
    mgmtPvlanConfigInterfaceVlanMembershipPvlanIndex  Unsigned32,
    mgmtPvlanConfigInterfaceVlanMembershipPortList    MGMTPortList,
    mgmtPvlanConfigInterfaceVlanMembershipAction      MGMTRowEditorState
}

mgmtPvlanConfigInterfaceVlanMembershipPvlanIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Configuration index of the Private VLAN membership table."
    ::= { mgmtPvlanConfigInterfaceVlanMembershipEntry 1 }

mgmtPvlanConfigInterfaceVlanMembershipPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "It is used to denote the memberships of the specific Private VLAN
         configuration."
    ::= { mgmtPvlanConfigInterfaceVlanMembershipEntry 2 }

mgmtPvlanConfigInterfaceVlanMembershipAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtPvlanConfigInterfaceVlanMembershipEntry 100 }

mgmtPvlanConfigInterfaceVlanMembershipTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtPvlanConfigInterface 2 }

mgmtPvlanConfigInterfaceVlanMembershipTableRowEditorPvlanIndex OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configuration index of the Private VLAN membership table."
    ::= { mgmtPvlanConfigInterfaceVlanMembershipTableRowEditor 1 }

mgmtPvlanConfigInterfaceVlanMembershipTableRowEditorPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "It is used to denote the memberships of the specific Private VLAN
         configuration."
    ::= { mgmtPvlanConfigInterfaceVlanMembershipTableRowEditor 2 }

mgmtPvlanConfigInterfaceVlanMembershipTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtPvlanConfigInterfaceVlanMembershipTableRowEditor 100 }

mgmtPvlanConfigInterfacePortIsolatationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPvlanConfigInterfacePortIsolatationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing Private VLAN port isolation entries."
    ::= { mgmtPvlanConfigInterface 3 }

mgmtPvlanConfigInterfacePortIsolatationEntry OBJECT-TYPE
    SYNTAX      MGMTPvlanConfigInterfacePortIsolatationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtPvlanConfigInterfacePortIsolatationPortIndex }
    ::= { mgmtPvlanConfigInterfacePortIsolatationTable 1 }

MGMTPvlanConfigInterfacePortIsolatationEntry ::= SEQUENCE {
    mgmtPvlanConfigInterfacePortIsolatationPortIndex  MGMTInterfaceIndex,
    mgmtPvlanConfigInterfacePortIsolatationEnabled    TruthValue
}

mgmtPvlanConfigInterfacePortIsolatationPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the Private VLAN port isolation."
    ::= { mgmtPvlanConfigInterfacePortIsolatationEntry 1 }

mgmtPvlanConfigInterfacePortIsolatationEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the Private VLAN isolation functionality."
    ::= { mgmtPvlanConfigInterfacePortIsolatationEntry 2 }

mgmtPvlanMibConformance OBJECT IDENTIFIER
    ::= { mgmtPvlanMib 2 }

mgmtPvlanMibCompliances OBJECT IDENTIFIER
    ::= { mgmtPvlanMibConformance 1 }

mgmtPvlanMibGroups OBJECT IDENTIFIER
    ::= { mgmtPvlanMibConformance 2 }

mgmtPvlanCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPvlanCapabilitiesHasVlanMembershipMgmt,
                  mgmtPvlanCapabilitiesVlanIdMax,
                  mgmtPvlanCapabilitiesVlanIdMin }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPvlanMibGroups 1 }

mgmtPvlanConfigInterfaceVlanMembershipTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPvlanConfigInterfaceVlanMembershipPvlanIndex,
                  mgmtPvlanConfigInterfaceVlanMembershipPortList,
                  mgmtPvlanConfigInterfaceVlanMembershipAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPvlanMibGroups 2 }

mgmtPvlanConfigInterfaceVlanMembershipTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtPvlanConfigInterfaceVlanMembershipTableRowEditorPvlanIndex,
                  mgmtPvlanConfigInterfaceVlanMembershipTableRowEditorPortList,
                  mgmtPvlanConfigInterfaceVlanMembershipTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPvlanMibGroups 3 }

mgmtPvlanConfigInterfacePortIsolatationTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPvlanConfigInterfacePortIsolatationPortIndex,
                  mgmtPvlanConfigInterfacePortIsolatationEnabled }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPvlanMibGroups 4 }

mgmtPvlanMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtPvlanCapabilitiesInfoGroup,
                       mgmtPvlanConfigInterfaceVlanMembershipTableInfoGroup,
                       mgmtPvlanConfigInterfaceVlanMembershipTableRowEditorInfoGroup,
                       mgmtPvlanConfigInterfacePortIsolatationTableInfoGroup }

    ::= { mgmtPvlanMibCompliances 1 }

END
