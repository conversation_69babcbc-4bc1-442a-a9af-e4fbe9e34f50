-- *****************************************************************
-- FAN-MIB:  
-- ****************************************************************

MGMT-FAN-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    MGMTInteger16 FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtFanMib MODULE-IDENTITY
    LAST-UPDATED "201405220000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private MIB for controlling fan speed, in order to reduce
         noise and power consumption"
    REVISION    "201405220000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 75 }


mgmtFanMibObjects OBJECT IDENTIFIER
    ::= { mgmtFanMib 1 }

mgmtFanCapabilities OBJECT IDENTIFIER
    ::= { mgmtFanMibObjects 1 }

mgmtFanCapabilitiesSensorCount OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported temperature sensors in a switch. "
    ::= { mgmtFanCapabilities 1 }

mgmtFanConfig OBJECT IDENTIFIER
    ::= { mgmtFanMibObjects 2 }

mgmtFanConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtFanConfig 1 }

mgmtFanConfigGlobalsMaxSpeedTemperature OBJECT-TYPE
    SYNTAX      MGMTInteger16 (-127..127)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The temperature(in C) where fan shall be running at full speed (maximum
         cooling). Valid range:-127 to 127"
    ::= { mgmtFanConfigGlobals 1 }

mgmtFanConfigGlobalsOnTemperature OBJECT-TYPE
    SYNTAX      MGMTInteger16 (-127..127)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The temperature(in C) where cooling is needed (fan is started). Valid
         range:-127 to 127"
    ::= { mgmtFanConfigGlobals 2 }

mgmtFanStatus OBJECT IDENTIFIER
    ::= { mgmtFanMibObjects 3 }

mgmtFanStatusSpeedTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFanStatusSpeedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for switch fan speed"
    ::= { mgmtFanStatus 1 }

mgmtFanStatusSpeedEntry OBJECT-TYPE
    SYNTAX      MGMTFanStatusSpeedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each switch have fan module"
    INDEX       { mgmtFanStatusSpeedSwitchId }
    ::= { mgmtFanStatusSpeedTable 1 }

MGMTFanStatusSpeedEntry ::= SEQUENCE {
    mgmtFanStatusSpeedSwitchId  Integer32,
    mgmtFanStatusSpeedRunning   MGMTUnsigned16
}

mgmtFanStatusSpeedSwitchId OBJECT-TYPE
    SYNTAX      Integer32 (1..16)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of switch. For non-stackable switch, the valid value
         is limited to 1."
    ::= { mgmtFanStatusSpeedEntry 1 }

mgmtFanStatusSpeedRunning OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The fan speed, currently running (in RPM)."
    ::= { mgmtFanStatusSpeedEntry 3 }

mgmtFanStatusSensorTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTFanStatusSensorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to chip temperature"
    ::= { mgmtFanStatus 2 }

mgmtFanStatusSensorEntry OBJECT-TYPE
    SYNTAX      MGMTFanStatusSensorEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each switch have temperature sensor status"
    INDEX       { mgmtFanStatusSensorSwitchId,
                  mgmtFanStatusSensorIndex }
    ::= { mgmtFanStatusSensorTable 1 }

MGMTFanStatusSensorEntry ::= SEQUENCE {
    mgmtFanStatusSensorSwitchId  Integer32,
    mgmtFanStatusSensorIndex     Integer32,
    mgmtFanStatusSensorChipTemp  MGMTInteger16
}

mgmtFanStatusSensorSwitchId OBJECT-TYPE
    SYNTAX      Integer32 (1..16)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The identification of switch. For non-stackable switch, the valid value
         is limited to 1."
    ::= { mgmtFanStatusSensorEntry 1 }

mgmtFanStatusSensorIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..4)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The temperature sensor index. Some switches may also have more than one
         temperature sensor."
    ::= { mgmtFanStatusSensorEntry 2 }

mgmtFanStatusSensorChipTemp OBJECT-TYPE
    SYNTAX      MGMTInteger16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current chip temperature (in C)."
    ::= { mgmtFanStatusSensorEntry 4 }

mgmtFanMibConformance OBJECT IDENTIFIER
    ::= { mgmtFanMib 2 }

mgmtFanMibCompliances OBJECT IDENTIFIER
    ::= { mgmtFanMibConformance 1 }

mgmtFanMibGroups OBJECT IDENTIFIER
    ::= { mgmtFanMibConformance 2 }

mgmtFanCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFanCapabilitiesSensorCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFanMibGroups 1 }

mgmtFanConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFanConfigGlobalsMaxSpeedTemperature,
                  mgmtFanConfigGlobalsOnTemperature }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFanMibGroups 2 }

mgmtFanStatusSpeedInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFanStatusSpeedSwitchId,
                  mgmtFanStatusSpeedRunning }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFanMibGroups 3 }

mgmtFanStatusSensorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtFanStatusSensorSwitchId,
                  mgmtFanStatusSensorIndex,
                  mgmtFanStatusSensorChipTemp }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtFanMibGroups 4 }

mgmtFanMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtFanCapabilitiesInfoGroup,
                       mgmtFanConfigGlobalsInfoGroup,
                       mgmtFanStatusSpeedInfoGroup,
                       mgmtFanStatusSensorInfoGroup }

    ::= { mgmtFanMibCompliances 1 }

END
