-- *****************************************************************
-- IPMC-SNOOPING-MIB:  
-- ****************************************************************

MGMT-IPMC-SNOOPING-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtIpmcSnoopingMib MODULE-IDENTITY
    LAST-UPDATED "201612130000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the IPMC Snooping MIB"
    REVISION    "201612130000Z"
    DESCRIPTION
        "Removed the add/delete methods for VLAN interfaces from IGMP/MLD"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 69 }


MGMTIpmcSnoopingIgmpGroupSrcListGroupFilterModeEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the group filter mode for a IGMP group
         address."
    SYNTAX      INTEGER { exclude(0), include(1), none(2) }

MGMTIpmcSnoopingIgmpGroupSrcListSourceTypeEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the source filtering type from IGMP
         snooping."
    SYNTAX      INTEGER { deny(0), permit(1) }

MGMTIpmcSnoopingIgmpInterfaceCompatibilityEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the version compatibility for IGMP snooping
         VLAN interface."
    SYNTAX      INTEGER { auto(0), igmpv1(1), igmpv2(2), igmpv3(3) }

MGMTIpmcSnoopingIgmpRouterPortStatusEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the router port status from IGMP snooping."
    SYNTAX      INTEGER { none(0), static(1), dynamic(2), both(3) }

MGMTIpmcSnoopingIgmpVlanStatusQuerierStatusEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the querier status for IGMP snooping VLAN
         interface."
    SYNTAX      INTEGER { disabled(0), initial(1), idle(2), active(3) }

MGMTIpmcSnoopingMldGroupSrcListGroupFilterModeEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the group filter mode for a MLD group
         address."
    SYNTAX      INTEGER { exclude(0), include(1), none(2) }

MGMTIpmcSnoopingMldGroupSrcListSourceEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the source filtering type from MLD snooping."
    SYNTAX      INTEGER { deny(0), permit(1) }

MGMTIpmcSnoopingMldInterfaceCompatibilityEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the version compatibility for MLD snooping
         VLAN interface."
    SYNTAX      INTEGER { auto(0), mldv1(1), mldv2(2) }

MGMTIpmcSnoopingMldRouterPortStatusEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the router port status from MLD snooping."
    SYNTAX      INTEGER { none(0), static(1), dynamic(2), both(3) }

MGMTIpmcSnoopingMldVlanStatusQuerierStatusEnum ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the querier status for MLD snooping VLAN
         interface."
    SYNTAX      INTEGER { disabled(0), initial(1), idle(2), active(3) }

mgmtIpmcSnoopingMibObjects OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingMib 1 }

mgmtIpmcSnoopingConfig OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingMibObjects 2 }

mgmtIpmcSnoopingConfigIgmpGlobals OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingConfig 1 }

mgmtIpmcSnoopingConfigIgmpGlobalsAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the IGMP snooping global functionality."
    ::= { mgmtIpmcSnoopingConfigIgmpGlobals 1 }

mgmtIpmcSnoopingConfigIgmpGlobalsUnregisteredFlooding OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the control for flooding unregistered IPv4 multicast
         traffic."
    ::= { mgmtIpmcSnoopingConfigIgmpGlobals 2 }

mgmtIpmcSnoopingConfigIgmpGlobalsSsmRangeAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The address prefix value defined for IGMP SSM service model."
    ::= { mgmtIpmcSnoopingConfigIgmpGlobals 3 }

mgmtIpmcSnoopingConfigIgmpGlobalsSsmRangeMask OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The address prefix length defined for IGMP SSM service model."
    ::= { mgmtIpmcSnoopingConfigIgmpGlobals 4 }

mgmtIpmcSnoopingConfigIgmpGlobalsProxy OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the IGMP proxy functionality."
    ::= { mgmtIpmcSnoopingConfigIgmpGlobals 5 }

mgmtIpmcSnoopingConfigIgmpGlobalsLeaveProxy OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the IGMP leave-proxy functionality."
    ::= { mgmtIpmcSnoopingConfigIgmpGlobals 6 }

mgmtIpmcSnoopingConfigIgmpPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingConfigIgmpPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing extra IGMP snooping helper features per
         port basis"
    ::= { mgmtIpmcSnoopingConfig 2 }

mgmtIpmcSnoopingConfigIgmpPortEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingConfigIgmpPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters"
    INDEX       { mgmtIpmcSnoopingConfigIgmpPortPortIndex }
    ::= { mgmtIpmcSnoopingConfigIgmpPortTable 1 }

MGMTIpmcSnoopingConfigIgmpPortEntry ::= SEQUENCE {
    mgmtIpmcSnoopingConfigIgmpPortPortIndex         MGMTInterfaceIndex,
    mgmtIpmcSnoopingConfigIgmpPortAsRouterPort      TruthValue,
    mgmtIpmcSnoopingConfigIgmpPortDoFastLeave       TruthValue,
    mgmtIpmcSnoopingConfigIgmpPortThrottlingNumber  Integer32,
    mgmtIpmcSnoopingConfigIgmpPortFilteringProfile  MGMTDisplayString
}

mgmtIpmcSnoopingConfigIgmpPortPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcSnoopingConfigIgmpPortEntry 1 }

mgmtIpmcSnoopingConfigIgmpPortAsRouterPort OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the IGMP static router port functionality."
    ::= { mgmtIpmcSnoopingConfigIgmpPortEntry 2 }

mgmtIpmcSnoopingConfigIgmpPortDoFastLeave OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the IGMP fast leave functionality."
    ::= { mgmtIpmcSnoopingConfigIgmpPortEntry 3 }

mgmtIpmcSnoopingConfigIgmpPortThrottlingNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The maximum number of groups to be registered on the specific port."
    ::= { mgmtIpmcSnoopingConfigIgmpPortEntry 4 }

mgmtIpmcSnoopingConfigIgmpPortFilteringProfile OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The profile used for IGMP filtering per-port basis."
    ::= { mgmtIpmcSnoopingConfigIgmpPortEntry 5 }

mgmtIpmcSnoopingConfigIgmpInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingConfigIgmpInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing IGMP Snooping VLAN interface entries."
    ::= { mgmtIpmcSnoopingConfig 3 }

mgmtIpmcSnoopingConfigIgmpInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingConfigIgmpInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtIpmcSnoopingConfigIgmpInterfaceIfIndex }
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceTable 1 }

MGMTIpmcSnoopingConfigIgmpInterfaceEntry ::= SEQUENCE {
    mgmtIpmcSnoopingConfigIgmpInterfaceIfIndex          MGMTInterfaceIndex,
    mgmtIpmcSnoopingConfigIgmpInterfaceAdminState       TruthValue,
    mgmtIpmcSnoopingConfigIgmpInterfaceQuerierElection  TruthValue,
    mgmtIpmcSnoopingConfigIgmpInterfaceQuerierAddress   IpAddress,
    mgmtIpmcSnoopingConfigIgmpInterfaceCompatibility    MGMTIpmcSnoopingIgmpInterfaceCompatibilityEnum,
    mgmtIpmcSnoopingConfigIgmpInterfacePriority         MGMTUnsigned8,
    mgmtIpmcSnoopingConfigIgmpInterfaceRv               Unsigned32,
    mgmtIpmcSnoopingConfigIgmpInterfaceQi               Unsigned32,
    mgmtIpmcSnoopingConfigIgmpInterfaceQri              Unsigned32,
    mgmtIpmcSnoopingConfigIgmpInterfaceLmqi             Unsigned32,
    mgmtIpmcSnoopingConfigIgmpInterfaceUri              Unsigned32
}

mgmtIpmcSnoopingConfigIgmpInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 1 }

mgmtIpmcSnoopingConfigIgmpInterfaceAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the IGMP snooping per-VLAN functionality."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 2 }

mgmtIpmcSnoopingConfigIgmpInterfaceQuerierElection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the capability to run IGMP Querier election per-VLAN
         basis."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 3 }

mgmtIpmcSnoopingConfigIgmpInterfaceQuerierAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The static IPv4 source address of the specific IGMP interface for
         seding IGMP Query message with respect to IGMP Querier election."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 4 }

mgmtIpmcSnoopingConfigIgmpInterfaceCompatibility OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingIgmpInterfaceCompatibilityEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The compatibility control for IGMP snooping to run the corresponding
         protocol version."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 5 }

mgmtIpmcSnoopingConfigIgmpInterfacePriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting is used for prioritizing the IGMP control frames to be
         sent for IGMP snooping."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 6 }

mgmtIpmcSnoopingConfigIgmpInterfaceRv OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Robustness Variable is used to control IGMP protocol stack
         as stated in RFC-3376 8.1."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 7 }

mgmtIpmcSnoopingConfigIgmpInterfaceQi OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Query Interval is used to control IGMP protocol stack as
         stated in RFC-3376 8.2."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 8 }

mgmtIpmcSnoopingConfigIgmpInterfaceQri OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Query Response Intervalis used to control IGMP protocol
         stack as stated in RFC-3376 8.3."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 9 }

mgmtIpmcSnoopingConfigIgmpInterfaceLmqi OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Last Member Query Interval is used to control IGMP
         protocol stack as stated in RFC-3376 8.8."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 10 }

mgmtIpmcSnoopingConfigIgmpInterfaceUri OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Unsolicited Report Interval is used to control IGMP
         protocol stack as stated in RFC-3376 8.11."
    ::= { mgmtIpmcSnoopingConfigIgmpInterfaceEntry 11 }

mgmtIpmcSnoopingConfigMldGlobals OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingConfig 5 }

mgmtIpmcSnoopingConfigMldGlobalsAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the MLD snooping global functionality."
    ::= { mgmtIpmcSnoopingConfigMldGlobals 1 }

mgmtIpmcSnoopingConfigMldGlobalsUnregisteredFlooding OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the control for flooding unregistered IPv6 multicast
         traffic."
    ::= { mgmtIpmcSnoopingConfigMldGlobals 2 }

mgmtIpmcSnoopingConfigMldGlobalsSsmRangeAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The address prefix value defined for MLD SSM service model."
    ::= { mgmtIpmcSnoopingConfigMldGlobals 3 }

mgmtIpmcSnoopingConfigMldGlobalsSsmRangeMask OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The address prefix length defined for MLD SSM service model."
    ::= { mgmtIpmcSnoopingConfigMldGlobals 4 }

mgmtIpmcSnoopingConfigMldGlobalsProxy OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the MLD proxy functionality."
    ::= { mgmtIpmcSnoopingConfigMldGlobals 5 }

mgmtIpmcSnoopingConfigMldGlobalsLeaveProxy OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the MLD leave-proxy functionality."
    ::= { mgmtIpmcSnoopingConfigMldGlobals 6 }

mgmtIpmcSnoopingConfigMldPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingConfigMldPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing extra MLD snooping helper features per
         port basis"
    ::= { mgmtIpmcSnoopingConfig 6 }

mgmtIpmcSnoopingConfigMldPortEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingConfigMldPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters"
    INDEX       { mgmtIpmcSnoopingConfigMldPortPortIndex }
    ::= { mgmtIpmcSnoopingConfigMldPortTable 1 }

MGMTIpmcSnoopingConfigMldPortEntry ::= SEQUENCE {
    mgmtIpmcSnoopingConfigMldPortPortIndex         MGMTInterfaceIndex,
    mgmtIpmcSnoopingConfigMldPortAsRouterPort      TruthValue,
    mgmtIpmcSnoopingConfigMldPortDoFastLeave       TruthValue,
    mgmtIpmcSnoopingConfigMldPortThrottlingNumber  Integer32,
    mgmtIpmcSnoopingConfigMldPortFilteringProfile  MGMTDisplayString
}

mgmtIpmcSnoopingConfigMldPortPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcSnoopingConfigMldPortEntry 1 }

mgmtIpmcSnoopingConfigMldPortAsRouterPort OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the MLD static router port functionality."
    ::= { mgmtIpmcSnoopingConfigMldPortEntry 2 }

mgmtIpmcSnoopingConfigMldPortDoFastLeave OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the MLD fast leave functionality."
    ::= { mgmtIpmcSnoopingConfigMldPortEntry 3 }

mgmtIpmcSnoopingConfigMldPortThrottlingNumber OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The maximum number of groups to be registered on the specific port."
    ::= { mgmtIpmcSnoopingConfigMldPortEntry 4 }

mgmtIpmcSnoopingConfigMldPortFilteringProfile OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The profile used for MLD filtering per-port basis."
    ::= { mgmtIpmcSnoopingConfigMldPortEntry 5 }

mgmtIpmcSnoopingConfigMldInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingConfigMldInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing MLD Snooping VLAN interface entries."
    ::= { mgmtIpmcSnoopingConfig 7 }

mgmtIpmcSnoopingConfigMldInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingConfigMldInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtIpmcSnoopingConfigMldInterfaceIfIndex }
    ::= { mgmtIpmcSnoopingConfigMldInterfaceTable 1 }

MGMTIpmcSnoopingConfigMldInterfaceEntry ::= SEQUENCE {
    mgmtIpmcSnoopingConfigMldInterfaceIfIndex          MGMTInterfaceIndex,
    mgmtIpmcSnoopingConfigMldInterfaceAdminState       TruthValue,
    mgmtIpmcSnoopingConfigMldInterfaceQuerierElection  TruthValue,
    mgmtIpmcSnoopingConfigMldInterfaceCompatibility    MGMTIpmcSnoopingMldInterfaceCompatibilityEnum,
    mgmtIpmcSnoopingConfigMldInterfacePriority         MGMTUnsigned8,
    mgmtIpmcSnoopingConfigMldInterfaceRv               Unsigned32,
    mgmtIpmcSnoopingConfigMldInterfaceQi               Unsigned32,
    mgmtIpmcSnoopingConfigMldInterfaceQri              Unsigned32,
    mgmtIpmcSnoopingConfigMldInterfaceLlqi             Unsigned32,
    mgmtIpmcSnoopingConfigMldInterfaceUri              Unsigned32
}

mgmtIpmcSnoopingConfigMldInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 1 }

mgmtIpmcSnoopingConfigMldInterfaceAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the MLD snooping per-VLAN functionality."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 2 }

mgmtIpmcSnoopingConfigMldInterfaceQuerierElection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the capability to run MLD Querier election per-VLAN
         basis."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 3 }

mgmtIpmcSnoopingConfigMldInterfaceCompatibility OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingMldInterfaceCompatibilityEnum
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The compatibility control for MLD snooping to run the corresponding
         protocol version."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 4 }

mgmtIpmcSnoopingConfigMldInterfacePriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting is used for prioritizing the MLD control frames to be sent
         for MLD snooping."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 5 }

mgmtIpmcSnoopingConfigMldInterfaceRv OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Robustness Variableis used to control MLD protocol stack
         as stated in RFC-3810 9.1."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 6 }

mgmtIpmcSnoopingConfigMldInterfaceQi OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Query Intervalis used to control MLD protocol stack as
         stated in RFC-3810 9.2."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 7 }

mgmtIpmcSnoopingConfigMldInterfaceQri OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Query Response Intervalis used to control MLD protocol
         stack as stated in RFC-3810 9.3."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 8 }

mgmtIpmcSnoopingConfigMldInterfaceLlqi OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Last Listener Query Interval is used to control MLD
         protocol stack as stated in RFC-3810 9.8."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 9 }

mgmtIpmcSnoopingConfigMldInterfaceUri OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Unsolicited Report Intervalis used to control MLD protocol
         stack as stated in RFC-3810 9.11."
    ::= { mgmtIpmcSnoopingConfigMldInterfaceEntry 10 }

mgmtIpmcSnoopingStatus OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingMibObjects 3 }

mgmtIpmcSnoopingStatusGroupAddressCount OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingStatus 1 }

mgmtIpmcSnoopingStatusGroupAddressCountFromIgmp OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the total number of registered multicast group
         address from IGMP snooping."
    ::= { mgmtIpmcSnoopingStatusGroupAddressCount 1 }

mgmtIpmcSnoopingStatusGroupAddressCountFromMld OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the total number of registered multicast group
         address from MLD snooping."
    ::= { mgmtIpmcSnoopingStatusGroupAddressCount 2 }

mgmtIpmcSnoopingStatusIgmpRouterPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingStatusIgmpRouterPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the router port status from IGMP
         snooping configuration."
    ::= { mgmtIpmcSnoopingStatus 2 }

mgmtIpmcSnoopingStatusIgmpRouterPortEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingStatusIgmpRouterPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcSnoopingStatusIgmpRouterPortPortIndex }
    ::= { mgmtIpmcSnoopingStatusIgmpRouterPortTable 1 }

MGMTIpmcSnoopingStatusIgmpRouterPortEntry ::= SEQUENCE {
    mgmtIpmcSnoopingStatusIgmpRouterPortPortIndex  MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusIgmpRouterPortStatus     MGMTIpmcSnoopingIgmpRouterPortStatusEnum
}

mgmtIpmcSnoopingStatusIgmpRouterPortPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcSnoopingStatusIgmpRouterPortEntry 1 }

mgmtIpmcSnoopingStatusIgmpRouterPortStatus OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingIgmpRouterPortStatusEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IGMP snooping router port status."
    ::= { mgmtIpmcSnoopingStatusIgmpRouterPortEntry 2 }

mgmtIpmcSnoopingStatusIgmpVlanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingStatusIgmpVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the per VLAN interface status in IGMP
         snooping configuration."
    ::= { mgmtIpmcSnoopingStatus 3 }

mgmtIpmcSnoopingStatusIgmpVlanEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingStatusIgmpVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcSnoopingStatusIgmpVlanIfIndex }
    ::= { mgmtIpmcSnoopingStatusIgmpVlanTable 1 }

MGMTIpmcSnoopingStatusIgmpVlanEntry ::= SEQUENCE {
    mgmtIpmcSnoopingStatusIgmpVlanIfIndex                 MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusIgmpVlanQuerierStatus           MGMTIpmcSnoopingIgmpVlanStatusQuerierStatusEnum,
    mgmtIpmcSnoopingStatusIgmpVlanActiveQuerierAddress    IpAddress,
    mgmtIpmcSnoopingStatusIgmpVlanQuerierUptime           Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanQueryInterval           Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanStartupQueryCount       Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanQuerierExpiryTime       Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanQuerierVersion          MGMTUnsigned8,
    mgmtIpmcSnoopingStatusIgmpVlanQuerierPresentTimeout   Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanHostVersion             MGMTUnsigned8,
    mgmtIpmcSnoopingStatusIgmpVlanHostPresentTimeout      Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanCounterTxQuery          Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanCounterTxSpecificQuery  Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanCounterRxQuery          Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanCounterRxV1Join         Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanCounterRxV2Join         Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanCounterRxV2Leave        Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanCounterRxV3Join         Unsigned32,
    mgmtIpmcSnoopingStatusIgmpVlanCounterRxErrors         Unsigned32
}

mgmtIpmcSnoopingStatusIgmpVlanIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 1 }

mgmtIpmcSnoopingStatusIgmpVlanQuerierStatus OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingIgmpVlanStatusQuerierStatusEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IGMP Querier status of the specific VLAN interface."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 2 }

mgmtIpmcSnoopingStatusIgmpVlanActiveQuerierAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The active IGMP Querier address on the specific VLAN interface."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 3 }

mgmtIpmcSnoopingStatusIgmpVlanQuerierUptime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the in operation timer for the specific interface act as a
         IGMP Querier."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 4 }

mgmtIpmcSnoopingStatusIgmpVlanQueryInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control IGMP protocol stack as stated in
         RFC-3376 8.2."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 5 }

mgmtIpmcSnoopingStatusIgmpVlanStartupQueryCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control IGMP protocol stack as stated in
         RFC-3376 8.7."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 6 }

mgmtIpmcSnoopingStatusIgmpVlanQuerierExpiryTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control IGMP protocol stack as stated in
         RFC-3376 8.5."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 7 }

mgmtIpmcSnoopingStatusIgmpVlanQuerierVersion OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current IGMP version that the IGMP interface should
         behave in running IGMP protocol as a router."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 8 }

mgmtIpmcSnoopingStatusIgmpVlanQuerierPresentTimeout OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control IGMP protocol stack as stated in
         RFC-3376 8.12."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 9 }

mgmtIpmcSnoopingStatusIgmpVlanHostVersion OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current IGMP version that the IGMP interface should
         behave in running IGMP protocol as a host."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 10 }

mgmtIpmcSnoopingStatusIgmpVlanHostPresentTimeout OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control IGMP protocol stack as stated in
         RFC-3376 8.13."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 11 }

mgmtIpmcSnoopingStatusIgmpVlanCounterTxQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         transmitting IGMP Query control frames."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 12 }

mgmtIpmcSnoopingStatusIgmpVlanCounterTxSpecificQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         transmitting IGMP Specific Query control frames."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 13 }

mgmtIpmcSnoopingStatusIgmpVlanCounterRxQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMP Query control frames."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 14 }

mgmtIpmcSnoopingStatusIgmpVlanCounterRxV1Join OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMPv1 Join control frames."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 15 }

mgmtIpmcSnoopingStatusIgmpVlanCounterRxV2Join OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMPv2 Join control frames."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 16 }

mgmtIpmcSnoopingStatusIgmpVlanCounterRxV2Leave OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMPv2 Leave control frames."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 17 }

mgmtIpmcSnoopingStatusIgmpVlanCounterRxV3Join OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMPv3 Join control frames."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 18 }

mgmtIpmcSnoopingStatusIgmpVlanCounterRxErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving invalid IGMP control frames."
    ::= { mgmtIpmcSnoopingStatusIgmpVlanEntry 19 }

mgmtIpmcSnoopingStatusIgmpGroupAddressTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingStatusIgmpGroupAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the registered IPv4 multicast group
         address status from IGMP snooping."
    ::= { mgmtIpmcSnoopingStatus 4 }

mgmtIpmcSnoopingStatusIgmpGroupAddressEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingStatusIgmpGroupAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcSnoopingStatusIgmpGroupAddressIfIndex,
                  mgmtIpmcSnoopingStatusIgmpGroupAddressGroupAddress }
    ::= { mgmtIpmcSnoopingStatusIgmpGroupAddressTable 1 }

MGMTIpmcSnoopingStatusIgmpGroupAddressEntry ::= SEQUENCE {
    mgmtIpmcSnoopingStatusIgmpGroupAddressIfIndex         MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusIgmpGroupAddressGroupAddress    IpAddress,
    mgmtIpmcSnoopingStatusIgmpGroupAddressMemberPorts     MGMTPortList,
    mgmtIpmcSnoopingStatusIgmpGroupAddressHardwareSwitch  TruthValue
}

mgmtIpmcSnoopingStatusIgmpGroupAddressIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupAddressEntry 1 }

mgmtIpmcSnoopingStatusIgmpGroupAddressGroupAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv4 multicast address."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupAddressEntry 2 }

mgmtIpmcSnoopingStatusIgmpGroupAddressMemberPorts OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the memberships of the registered multicast group
         address from IGMP snooping."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupAddressEntry 3 }

mgmtIpmcSnoopingStatusIgmpGroupAddressHardwareSwitch OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote whether the multicast traffic destined to the
         registered group address could be forwarding by switch hardware or not."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupAddressEntry 4 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingStatusIgmpGroupSrcListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the address SFM (a.k.a Source List
         Multicast) status in source list of the registered IPv4 multicast group
         from IGMP snooping."
    ::= { mgmtIpmcSnoopingStatus 5 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingStatusIgmpGroupSrcListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcSnoopingStatusIgmpGroupSrcListIfIndex,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListGroupAddress,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListPortIndex,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListHostAddress }
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListTable 1 }

MGMTIpmcSnoopingStatusIgmpGroupSrcListEntry ::= SEQUENCE {
    mgmtIpmcSnoopingStatusIgmpGroupSrcListIfIndex          MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusIgmpGroupSrcListGroupAddress     IpAddress,
    mgmtIpmcSnoopingStatusIgmpGroupSrcListPortIndex        MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusIgmpGroupSrcListHostAddress      IpAddress,
    mgmtIpmcSnoopingStatusIgmpGroupSrcListGroupFilterMode  MGMTIpmcSnoopingIgmpGroupSrcListGroupFilterModeEnum,
    mgmtIpmcSnoopingStatusIgmpGroupSrcListFilterTimer      Unsigned32,
    mgmtIpmcSnoopingStatusIgmpGroupSrcListSourceType       MGMTIpmcSnoopingIgmpGroupSrcListSourceTypeEnum,
    mgmtIpmcSnoopingStatusIgmpGroupSrcListSourceTimer      Unsigned32,
    mgmtIpmcSnoopingStatusIgmpGroupSrcListHardwareFilter   TruthValue
}

mgmtIpmcSnoopingStatusIgmpGroupSrcListIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry 1 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListGroupAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv4 multicast address."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry 2 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry 3 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListHostAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv4 source address."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry 4 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListGroupFilterMode OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingIgmpGroupSrcListGroupFilterModeEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the source filtering mode of the specific
         registered multicast group address from IGMP snooping."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry 5 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListFilterTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to count down the timer for the specific multicast group's
         filtering mode transition."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry 6 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListSourceType OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingIgmpGroupSrcListSourceTypeEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the filtering type of the specific source address
         in multicasting to the registered group address."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry 7 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListSourceTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to count down the timer for purging the specific source
         address from the registered multicast group's source list."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry 8 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListHardwareFilter OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote whether the traffic destined to the multicast
         group address from the specific source address could be forwarding by
         switch hardware or not."
    ::= { mgmtIpmcSnoopingStatusIgmpGroupSrcListEntry 9 }

mgmtIpmcSnoopingStatusMldRouterPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingStatusMldRouterPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the router port status from MLD snooping
         configuration."
    ::= { mgmtIpmcSnoopingStatus 6 }

mgmtIpmcSnoopingStatusMldRouterPortEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingStatusMldRouterPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcSnoopingStatusMldRouterPortPortIndex }
    ::= { mgmtIpmcSnoopingStatusMldRouterPortTable 1 }

MGMTIpmcSnoopingStatusMldRouterPortEntry ::= SEQUENCE {
    mgmtIpmcSnoopingStatusMldRouterPortPortIndex  MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusMldRouterPortStatus     MGMTIpmcSnoopingMldRouterPortStatusEnum
}

mgmtIpmcSnoopingStatusMldRouterPortPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcSnoopingStatusMldRouterPortEntry 1 }

mgmtIpmcSnoopingStatusMldRouterPortStatus OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingMldRouterPortStatusEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The MLD snooping router port status."
    ::= { mgmtIpmcSnoopingStatusMldRouterPortEntry 2 }

mgmtIpmcSnoopingStatusMldVlanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingStatusMldVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the per VLAN interface status in MLD
         snooping configuration."
    ::= { mgmtIpmcSnoopingStatus 7 }

mgmtIpmcSnoopingStatusMldVlanEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingStatusMldVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcSnoopingStatusMldVlanIfIndex }
    ::= { mgmtIpmcSnoopingStatusMldVlanTable 1 }

MGMTIpmcSnoopingStatusMldVlanEntry ::= SEQUENCE {
    mgmtIpmcSnoopingStatusMldVlanIfIndex                 MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusMldVlanQuerierStatus           MGMTIpmcSnoopingMldVlanStatusQuerierStatusEnum,
    mgmtIpmcSnoopingStatusMldVlanActiveQuerierAddress    InetAddressIPv6,
    mgmtIpmcSnoopingStatusMldVlanQuerierUptime           Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanQueryInterval           Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanStartupQueryCount       Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanQuerierExpiryTime       Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanQuerierVersion          MGMTUnsigned8,
    mgmtIpmcSnoopingStatusMldVlanQuerierPresentTimeout   Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanHostVersion             MGMTUnsigned8,
    mgmtIpmcSnoopingStatusMldVlanHostPresentTimeout      Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanCounterTxQuery          Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanCounterTxSpecificQuery  Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanCounterRxQuery          Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanCounterRxV1Report       Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanCounterRxV1Done         Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanCounterRxV2Report       Unsigned32,
    mgmtIpmcSnoopingStatusMldVlanCounterRxErrors         Unsigned32
}

mgmtIpmcSnoopingStatusMldVlanIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 1 }

mgmtIpmcSnoopingStatusMldVlanQuerierStatus OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingMldVlanStatusQuerierStatusEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The MLD Querier status of the specific VLAN interface."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 2 }

mgmtIpmcSnoopingStatusMldVlanActiveQuerierAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The active MLD Querier address on the specific VLAN interface."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 3 }

mgmtIpmcSnoopingStatusMldVlanQuerierUptime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the in operation timer for the specific interface act as a
         MLD Querier."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 4 }

mgmtIpmcSnoopingStatusMldVlanQueryInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control MLD protocol stack as stated in
         RFC-3810 9.2."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 5 }

mgmtIpmcSnoopingStatusMldVlanStartupQueryCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control MLD protocol stack as stated in
         RFC-3810 9.7."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 6 }

mgmtIpmcSnoopingStatusMldVlanQuerierExpiryTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control MLD protocol stack as stated in
         RFC-3810 9.5."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 7 }

mgmtIpmcSnoopingStatusMldVlanQuerierVersion OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current MLD version that the MLD interface should
         behave in running MLD protocol as a router."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 8 }

mgmtIpmcSnoopingStatusMldVlanQuerierPresentTimeout OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control MLD protocol stack as stated in
         RFC-3810 9.12."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 9 }

mgmtIpmcSnoopingStatusMldVlanHostVersion OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current MLD version that the MLD interface should
         behave in running MLD protocol as a host."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 10 }

mgmtIpmcSnoopingStatusMldVlanHostPresentTimeout OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control MLD protocol stack as stated in
         RFC-3810 9.13."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 11 }

mgmtIpmcSnoopingStatusMldVlanCounterTxQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         transmitting MLD Query control frames."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 12 }

mgmtIpmcSnoopingStatusMldVlanCounterTxSpecificQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         transmitting MLD Specific Query control frames."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 13 }

mgmtIpmcSnoopingStatusMldVlanCounterRxQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving MLD Query control frames."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 14 }

mgmtIpmcSnoopingStatusMldVlanCounterRxV1Report OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving MLDv1 Report control frames."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 15 }

mgmtIpmcSnoopingStatusMldVlanCounterRxV1Done OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving MLDv1 Done control frames."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 16 }

mgmtIpmcSnoopingStatusMldVlanCounterRxV2Report OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving MLDv2 Report control frames."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 17 }

mgmtIpmcSnoopingStatusMldVlanCounterRxErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving invalid MLD control frames."
    ::= { mgmtIpmcSnoopingStatusMldVlanEntry 18 }

mgmtIpmcSnoopingStatusMldGroupAddressTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingStatusMldGroupAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the registered IPv6 multicast group
         address status from MLD snooping."
    ::= { mgmtIpmcSnoopingStatus 8 }

mgmtIpmcSnoopingStatusMldGroupAddressEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingStatusMldGroupAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcSnoopingStatusMldGroupAddressIfIndex,
                  mgmtIpmcSnoopingStatusMldGroupAddressGroupAddress }
    ::= { mgmtIpmcSnoopingStatusMldGroupAddressTable 1 }

MGMTIpmcSnoopingStatusMldGroupAddressEntry ::= SEQUENCE {
    mgmtIpmcSnoopingStatusMldGroupAddressIfIndex         MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusMldGroupAddressGroupAddress    InetAddressIPv6,
    mgmtIpmcSnoopingStatusMldGroupAddressMemberPorts     MGMTPortList,
    mgmtIpmcSnoopingStatusMldGroupAddressHardwareSwitch  TruthValue
}

mgmtIpmcSnoopingStatusMldGroupAddressIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingStatusMldGroupAddressEntry 1 }

mgmtIpmcSnoopingStatusMldGroupAddressGroupAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv6 multicast address."
    ::= { mgmtIpmcSnoopingStatusMldGroupAddressEntry 2 }

mgmtIpmcSnoopingStatusMldGroupAddressMemberPorts OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the memberships of the registered multicast group
         address from MLD snooping."
    ::= { mgmtIpmcSnoopingStatusMldGroupAddressEntry 3 }

mgmtIpmcSnoopingStatusMldGroupAddressHardwareSwitch OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote whether the multicast traffic destined to the
         registered group address could be forwarding by switch hardware or not."
    ::= { mgmtIpmcSnoopingStatusMldGroupAddressEntry 4 }

mgmtIpmcSnoopingStatusMldGroupSrcListTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcSnoopingStatusMldGroupSrcListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the address SFM (a.k.a Source List
         Multicast) status in source list of the registered IPv6 multicast group
         from MLD snooping."
    ::= { mgmtIpmcSnoopingStatus 9 }

mgmtIpmcSnoopingStatusMldGroupSrcListEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingStatusMldGroupSrcListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcSnoopingStatusMldGroupSrcListIfIndex,
                  mgmtIpmcSnoopingStatusMldGroupSrcListGroupAddress,
                  mgmtIpmcSnoopingStatusMldGroupSrcListPortIndex,
                  mgmtIpmcSnoopingStatusMldGroupSrcListHostAddress }
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListTable 1 }

MGMTIpmcSnoopingStatusMldGroupSrcListEntry ::= SEQUENCE {
    mgmtIpmcSnoopingStatusMldGroupSrcListIfIndex          MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusMldGroupSrcListGroupAddress     InetAddressIPv6,
    mgmtIpmcSnoopingStatusMldGroupSrcListPortIndex        MGMTInterfaceIndex,
    mgmtIpmcSnoopingStatusMldGroupSrcListHostAddress      InetAddressIPv6,
    mgmtIpmcSnoopingStatusMldGroupSrcListGroupFilterMode  MGMTIpmcSnoopingMldGroupSrcListGroupFilterModeEnum,
    mgmtIpmcSnoopingStatusMldGroupSrcListFilterTimer      Unsigned32,
    mgmtIpmcSnoopingStatusMldGroupSrcListSourceType       MGMTIpmcSnoopingMldGroupSrcListSourceEnum,
    mgmtIpmcSnoopingStatusMldGroupSrcListSourceTimer      Unsigned32,
    mgmtIpmcSnoopingStatusMldGroupSrcListHardwareFilter   TruthValue
}

mgmtIpmcSnoopingStatusMldGroupSrcListIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListEntry 1 }

mgmtIpmcSnoopingStatusMldGroupSrcListGroupAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv6 multicast address."
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListEntry 2 }

mgmtIpmcSnoopingStatusMldGroupSrcListPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListEntry 3 }

mgmtIpmcSnoopingStatusMldGroupSrcListHostAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv6 source address."
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListEntry 4 }

mgmtIpmcSnoopingStatusMldGroupSrcListGroupFilterMode OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingMldGroupSrcListGroupFilterModeEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the source filtering mode of the specific
         registered multicast group address from MLD snooping."
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListEntry 5 }

mgmtIpmcSnoopingStatusMldGroupSrcListFilterTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to count down the timer for the specific multicast group's
         filtering mode transition."
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListEntry 6 }

mgmtIpmcSnoopingStatusMldGroupSrcListSourceType OBJECT-TYPE
    SYNTAX      MGMTIpmcSnoopingMldGroupSrcListSourceEnum
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the filtering type of the specific source address
         in multicasting to the registered group address."
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListEntry 7 }

mgmtIpmcSnoopingStatusMldGroupSrcListSourceTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to count down the timer for purging the specific source
         address from the registered multicast group's source list."
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListEntry 8 }

mgmtIpmcSnoopingStatusMldGroupSrcListHardwareFilter OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote whether the traffic destined to the multicast
         group address from the specific source address could be forwarding by
         switch hardware or not."
    ::= { mgmtIpmcSnoopingStatusMldGroupSrcListEntry 9 }

mgmtIpmcSnoopingControl OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingMibObjects 4 }

mgmtIpmcSnoopingControlStatistics OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingControl 1 }

mgmtIpmcSnoopingControlStatisticsIgmpClearByIfIndex OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingControlStatistics 1 }

mgmtIpmcSnoopingControlStatisticsIgmpClearByIfIndexIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingControlStatisticsIgmpClearByIfIndex 1 }

mgmtIpmcSnoopingControlStatisticsMldClearByIfIndex OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingControlStatistics 2 }

mgmtIpmcSnoopingControlStatisticsMldClearByIfIndexIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcSnoopingControlStatisticsMldClearByIfIndex 1 }

mgmtIpmcSnoopingMibConformance OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingMib 2 }

mgmtIpmcSnoopingMibCompliances OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingMibConformance 1 }

mgmtIpmcSnoopingMibGroups OBJECT IDENTIFIER
    ::= { mgmtIpmcSnoopingMibConformance 2 }

mgmtIpmcSnoopingConfigIgmpGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingConfigIgmpGlobalsAdminState,
                  mgmtIpmcSnoopingConfigIgmpGlobalsUnregisteredFlooding,
                  mgmtIpmcSnoopingConfigIgmpGlobalsSsmRangeAddress,
                  mgmtIpmcSnoopingConfigIgmpGlobalsSsmRangeMask,
                  mgmtIpmcSnoopingConfigIgmpGlobalsProxy,
                  mgmtIpmcSnoopingConfigIgmpGlobalsLeaveProxy }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 1 }

mgmtIpmcSnoopingConfigIgmpPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingConfigIgmpPortPortIndex,
                  mgmtIpmcSnoopingConfigIgmpPortAsRouterPort,
                  mgmtIpmcSnoopingConfigIgmpPortDoFastLeave,
                  mgmtIpmcSnoopingConfigIgmpPortThrottlingNumber,
                  mgmtIpmcSnoopingConfigIgmpPortFilteringProfile }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 2 }

mgmtIpmcSnoopingConfigIgmpInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingConfigIgmpInterfaceIfIndex,
                  mgmtIpmcSnoopingConfigIgmpInterfaceAdminState,
                  mgmtIpmcSnoopingConfigIgmpInterfaceQuerierElection,
                  mgmtIpmcSnoopingConfigIgmpInterfaceQuerierAddress,
                  mgmtIpmcSnoopingConfigIgmpInterfaceCompatibility,
                  mgmtIpmcSnoopingConfigIgmpInterfacePriority,
                  mgmtIpmcSnoopingConfigIgmpInterfaceRv,
                  mgmtIpmcSnoopingConfigIgmpInterfaceQi,
                  mgmtIpmcSnoopingConfigIgmpInterfaceQri,
                  mgmtIpmcSnoopingConfigIgmpInterfaceLmqi,
                  mgmtIpmcSnoopingConfigIgmpInterfaceUri }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 3 }

mgmtIpmcSnoopingConfigMldGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingConfigMldGlobalsAdminState,
                  mgmtIpmcSnoopingConfigMldGlobalsUnregisteredFlooding,
                  mgmtIpmcSnoopingConfigMldGlobalsSsmRangeAddress,
                  mgmtIpmcSnoopingConfigMldGlobalsSsmRangeMask,
                  mgmtIpmcSnoopingConfigMldGlobalsProxy,
                  mgmtIpmcSnoopingConfigMldGlobalsLeaveProxy }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 4 }

mgmtIpmcSnoopingConfigMldPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingConfigMldPortPortIndex,
                  mgmtIpmcSnoopingConfigMldPortAsRouterPort,
                  mgmtIpmcSnoopingConfigMldPortDoFastLeave,
                  mgmtIpmcSnoopingConfigMldPortThrottlingNumber,
                  mgmtIpmcSnoopingConfigMldPortFilteringProfile }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 5 }

mgmtIpmcSnoopingConfigMldInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingConfigMldInterfaceIfIndex,
                  mgmtIpmcSnoopingConfigMldInterfaceAdminState,
                  mgmtIpmcSnoopingConfigMldInterfaceQuerierElection,
                  mgmtIpmcSnoopingConfigMldInterfaceCompatibility,
                  mgmtIpmcSnoopingConfigMldInterfacePriority,
                  mgmtIpmcSnoopingConfigMldInterfaceRv,
                  mgmtIpmcSnoopingConfigMldInterfaceQi,
                  mgmtIpmcSnoopingConfigMldInterfaceQri,
                  mgmtIpmcSnoopingConfigMldInterfaceLlqi,
                  mgmtIpmcSnoopingConfigMldInterfaceUri }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 6 }

mgmtIpmcSnoopingStatusGroupAddressCountInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingStatusGroupAddressCountFromIgmp,
                  mgmtIpmcSnoopingStatusGroupAddressCountFromMld }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 7 }

mgmtIpmcSnoopingStatusIgmpRouterPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingStatusIgmpRouterPortPortIndex,
                  mgmtIpmcSnoopingStatusIgmpRouterPortStatus }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 8 }

mgmtIpmcSnoopingStatusIgmpVlanTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingStatusIgmpVlanIfIndex,
                  mgmtIpmcSnoopingStatusIgmpVlanQuerierStatus,
                  mgmtIpmcSnoopingStatusIgmpVlanActiveQuerierAddress,
                  mgmtIpmcSnoopingStatusIgmpVlanQuerierUptime,
                  mgmtIpmcSnoopingStatusIgmpVlanQueryInterval,
                  mgmtIpmcSnoopingStatusIgmpVlanStartupQueryCount,
                  mgmtIpmcSnoopingStatusIgmpVlanQuerierExpiryTime,
                  mgmtIpmcSnoopingStatusIgmpVlanQuerierVersion,
                  mgmtIpmcSnoopingStatusIgmpVlanQuerierPresentTimeout,
                  mgmtIpmcSnoopingStatusIgmpVlanHostVersion,
                  mgmtIpmcSnoopingStatusIgmpVlanHostPresentTimeout,
                  mgmtIpmcSnoopingStatusIgmpVlanCounterTxQuery,
                  mgmtIpmcSnoopingStatusIgmpVlanCounterTxSpecificQuery,
                  mgmtIpmcSnoopingStatusIgmpVlanCounterRxQuery,
                  mgmtIpmcSnoopingStatusIgmpVlanCounterRxV1Join,
                  mgmtIpmcSnoopingStatusIgmpVlanCounterRxV2Join,
                  mgmtIpmcSnoopingStatusIgmpVlanCounterRxV2Leave,
                  mgmtIpmcSnoopingStatusIgmpVlanCounterRxV3Join,
                  mgmtIpmcSnoopingStatusIgmpVlanCounterRxErrors }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 9 }

mgmtIpmcSnoopingStatusIgmpGroupAddressTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingStatusIgmpGroupAddressIfIndex,
                  mgmtIpmcSnoopingStatusIgmpGroupAddressGroupAddress,
                  mgmtIpmcSnoopingStatusIgmpGroupAddressMemberPorts,
                  mgmtIpmcSnoopingStatusIgmpGroupAddressHardwareSwitch }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 10 }

mgmtIpmcSnoopingStatusIgmpGroupSrcListTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingStatusIgmpGroupSrcListIfIndex,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListGroupAddress,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListPortIndex,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListHostAddress,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListGroupFilterMode,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListFilterTimer,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListSourceType,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListSourceTimer,
                  mgmtIpmcSnoopingStatusIgmpGroupSrcListHardwareFilter }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 11 }

mgmtIpmcSnoopingStatusMldRouterPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingStatusMldRouterPortPortIndex,
                  mgmtIpmcSnoopingStatusMldRouterPortStatus }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 12 }

mgmtIpmcSnoopingStatusMldVlanTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingStatusMldVlanIfIndex,
                  mgmtIpmcSnoopingStatusMldVlanQuerierStatus,
                  mgmtIpmcSnoopingStatusMldVlanActiveQuerierAddress,
                  mgmtIpmcSnoopingStatusMldVlanQuerierUptime,
                  mgmtIpmcSnoopingStatusMldVlanQueryInterval,
                  mgmtIpmcSnoopingStatusMldVlanStartupQueryCount,
                  mgmtIpmcSnoopingStatusMldVlanQuerierExpiryTime,
                  mgmtIpmcSnoopingStatusMldVlanQuerierVersion,
                  mgmtIpmcSnoopingStatusMldVlanQuerierPresentTimeout,
                  mgmtIpmcSnoopingStatusMldVlanHostVersion,
                  mgmtIpmcSnoopingStatusMldVlanHostPresentTimeout,
                  mgmtIpmcSnoopingStatusMldVlanCounterTxQuery,
                  mgmtIpmcSnoopingStatusMldVlanCounterTxSpecificQuery,
                  mgmtIpmcSnoopingStatusMldVlanCounterRxQuery,
                  mgmtIpmcSnoopingStatusMldVlanCounterRxV1Report,
                  mgmtIpmcSnoopingStatusMldVlanCounterRxV1Done,
                  mgmtIpmcSnoopingStatusMldVlanCounterRxV2Report,
                  mgmtIpmcSnoopingStatusMldVlanCounterRxErrors }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 13 }

mgmtIpmcSnoopingStatusMldGroupAddressTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingStatusMldGroupAddressIfIndex,
                  mgmtIpmcSnoopingStatusMldGroupAddressGroupAddress,
                  mgmtIpmcSnoopingStatusMldGroupAddressMemberPorts,
                  mgmtIpmcSnoopingStatusMldGroupAddressHardwareSwitch }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 14 }

mgmtIpmcSnoopingStatusMldGroupSrcListTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcSnoopingStatusMldGroupSrcListIfIndex,
                  mgmtIpmcSnoopingStatusMldGroupSrcListGroupAddress,
                  mgmtIpmcSnoopingStatusMldGroupSrcListPortIndex,
                  mgmtIpmcSnoopingStatusMldGroupSrcListHostAddress,
                  mgmtIpmcSnoopingStatusMldGroupSrcListGroupFilterMode,
                  mgmtIpmcSnoopingStatusMldGroupSrcListFilterTimer,
                  mgmtIpmcSnoopingStatusMldGroupSrcListSourceType,
                  mgmtIpmcSnoopingStatusMldGroupSrcListSourceTimer,
                  mgmtIpmcSnoopingStatusMldGroupSrcListHardwareFilter }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 15 }

mgmtIpmcSnoopingControlStatisticsIgmpClearByIfIndexInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpmcSnoopingControlStatisticsIgmpClearByIfIndexIfIndex }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 16 }

mgmtIpmcSnoopingControlStatisticsMldClearByIfIndexInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpmcSnoopingControlStatisticsMldClearByIfIndexIfIndex }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcSnoopingMibGroups 17 }

mgmtIpmcSnoopingMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtIpmcSnoopingConfigIgmpGlobalsInfoGroup,
                       mgmtIpmcSnoopingConfigIgmpPortTableInfoGroup,
                       mgmtIpmcSnoopingConfigIgmpInterfaceTableInfoGroup,
                       mgmtIpmcSnoopingConfigMldGlobalsInfoGroup,
                       mgmtIpmcSnoopingConfigMldPortTableInfoGroup,
                       mgmtIpmcSnoopingConfigMldInterfaceTableInfoGroup,
                       mgmtIpmcSnoopingStatusGroupAddressCountInfoGroup,
                       mgmtIpmcSnoopingStatusIgmpRouterPortTableInfoGroup,
                       mgmtIpmcSnoopingStatusIgmpVlanTableInfoGroup,
                       mgmtIpmcSnoopingStatusIgmpGroupAddressTableInfoGroup,
                       mgmtIpmcSnoopingStatusIgmpGroupSrcListTableInfoGroup,
                       mgmtIpmcSnoopingStatusMldRouterPortTableInfoGroup,
                       mgmtIpmcSnoopingStatusMldVlanTableInfoGroup,
                       mgmtIpmcSnoopingStatusMldGroupAddressTableInfoGroup,
                       mgmtIpmcSnoopingStatusMldGroupSrcListTableInfoGroup,
                       mgmtIpmcSnoopingControlStatisticsIgmpClearByIfIndexInfoGroup,
                       mgmtIpmcSnoopingControlStatisticsMldClearByIfIndexInfoGroup }

    ::= { mgmtIpmcSnoopingMibCompliances 1 }

END
