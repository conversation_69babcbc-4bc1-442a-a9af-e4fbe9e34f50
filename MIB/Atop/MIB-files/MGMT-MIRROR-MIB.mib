-- *****************************************************************
-- MIRROR-MIB:  
-- ****************************************************************

MGMT-MIRROR-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTVlanListQuarter FROM MGMT-TC
    ;

mgmtMirrorMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of Mirror"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 15 }


MGMTmirrorSessionType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the session type in Mirror function."
    SYNTAX      INTEGER { mirror(0), rMirrorSource(1),
                          rMirrorDestination(2) }

mgmtMirrorMibObjects OBJECT IDENTIFIER
    ::= { mgmtMirrorMib 1 }

mgmtMirrorCapabilities OBJECT IDENTIFIER
    ::= { mgmtMirrorMibObjects 1 }

mgmtMirrorCapabilitiesSessionCountMax OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum number of sessions."
    ::= { mgmtMirrorCapabilities 1 }

mgmtMirrorCapabilitiesSessionSourceCountMax OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum number of Mirror and RMirror source sessions."
    ::= { mgmtMirrorCapabilities 2 }

mgmtMirrorCapabilitiesRMirrorSuport OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if RMirror feature is supported or not. true means supported.
         false means not supported."
    ::= { mgmtMirrorCapabilities 3 }

mgmtMirrorCapabilitiesInternalReflectorPortSupport OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if Internal reflector port is supported or not. true means
         supported. false means not supported."
    ::= { mgmtMirrorCapabilities 4 }

mgmtMirrorCapabilitiesCpuMirrorSupport OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if mirroring CPU traffic is supported or not. true means
         supported. false means not supported."
    ::= { mgmtMirrorCapabilities 5 }

mgmtMirrorConfig OBJECT IDENTIFIER
    ::= { mgmtMirrorMibObjects 2 }

mgmtMirrorConfigSessionTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMirrorConfigSessionEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of configuration per session"
    ::= { mgmtMirrorConfig 1 }

mgmtMirrorConfigSessionEntry OBJECT-TYPE
    SYNTAX      MGMTMirrorConfigSessionEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each session has a set of parameters"
    INDEX       { mgmtMirrorConfigSessionSessionId }
    ::= { mgmtMirrorConfigSessionTable 1 }

MGMTMirrorConfigSessionEntry ::= SEQUENCE {
    mgmtMirrorConfigSessionSessionId            Integer32,
    mgmtMirrorConfigSessionMode                 TruthValue,
    mgmtMirrorConfigSessionType                 MGMTmirrorSessionType,
    mgmtMirrorConfigSessionRMirrorVlan          MGMTUnsigned16,
    mgmtMirrorConfigSessionReflectorPort        MGMTInterfaceIndex,
    mgmtMirrorConfigSessionStripInnerTag        TruthValue,
    mgmtMirrorConfigSessionSourceVlans0KTo1K    MGMTVlanListQuarter,
    mgmtMirrorConfigSessionSourceVlans1KTo2K    MGMTVlanListQuarter,
    mgmtMirrorConfigSessionSourceVlans2KTo3K    MGMTVlanListQuarter,
    mgmtMirrorConfigSessionSourceVlans3KTo4K    MGMTVlanListQuarter,
    mgmtMirrorConfigSessionSourcePortListRx     MGMTPortList,
    mgmtMirrorConfigSessionSourcePortListTx     MGMTPortList,
    mgmtMirrorConfigSessionCpuRx                TruthValue,
    mgmtMirrorConfigSessionCpuTx                TruthValue,
    mgmtMirrorConfigSessionDestinationPortList  MGMTPortList
}

mgmtMirrorConfigSessionSessionId OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Session ID. Valid range is (1..maximum). The maximum is
         platform-specific and can be retrieved from the Mirror capabilities."
    ::= { mgmtMirrorConfigSessionEntry 1 }

mgmtMirrorConfigSessionMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether this session is enabled or disabled. true is to enable
         the function. false is to disable it.
         
         Multiple criteria must be fulfilled in order to be able to enable a
         session.The criteria depend on the 'SessionType'."
    ::= { mgmtMirrorConfigSessionEntry 2 }

mgmtMirrorConfigSessionType OBJECT-TYPE
    SYNTAX      MGMTmirrorSessionType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Session type in Mirror. 'mirror' means to do the Mirror function on the
         device.
         
         'rMirrorSource' means the device acts as source node for monitor flow.
         
         'rMirrorDestination' means the device acts as end node for monitor
         flow.
         
         "
    ::= { mgmtMirrorConfigSessionEntry 3 }

mgmtMirrorConfigSessionRMirrorVlan OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID points out where the monitor packet will copy to. The
         remote Mirror VLAN ID. Only used for RMirror types.
         
         RMirror source session:
         
         The mirrored traffic is copied onto this VLAN ID.
         
         Traffic will flood to all ports that are members of the remote Mirror
         VLAN ID.
         
         RMirror destination session:
         
         The #destination_port_list contains the port(s) that the Mirror VLAN
         will be copied to
         
         in addition to ports that are already configured (through the VLAN
         module) to be members of this VLAN."
    ::= { mgmtMirrorConfigSessionEntry 4 }

mgmtMirrorConfigSessionReflectorPort OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "A reflector port is a port that the administrator may have to specify
         in case the device does not have internal (unused) ports available.
         Whether this is the case or not for this device can be derived from
         Mirror capabilities. When 'ReflectorPort' is used, it must be specified
         when an RMirror source session is enabled. In this case, the reflector
         port will be shut down for normal front port usage, because the switch
         needs a port where it can loop frames in order to get mirrored traffic
         copied onto a particular VLAN ID (the 'RMirrorVlan')."
    ::= { mgmtMirrorConfigSessionEntry 5 }

mgmtMirrorConfigSessionStripInnerTag OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This configuration is used to strip the original VLAN ID of the
         mirrored traffic or not. When it is set to TRUE, the the original VLAN
         ID of the mirrored traffic will be stripped, otherwise the original
         VLAN ID will be carried to destination interface. It may have to
         specify in case the device does not have internal (unused) ports
         available. Whether this is the case or not for this device can be
         derived from Mirror capabilities."
    ::= { mgmtMirrorConfigSessionEntry 6 }

mgmtMirrorConfigSessionSourceVlans0KTo1K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "First quarter of bit-array indicating source VLAN list. All traffic in
         the VLANs specified in this list will get mirrored onto either the
         destination port (Mirror session) or the destination VLAN (RMirror
         source session). It's a bit-mask that indicates the VLANs. A '1'
         indicates the VLAN ID is selected, a '0' indicates that the VLAN ID
         isn't selected. "
    ::= { mgmtMirrorConfigSessionEntry 7 }

mgmtMirrorConfigSessionSourceVlans1KTo2K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Second quarter of bit-array indicating source VLAN list. All traffic in
         the VLANs specified in this list will get mirrored onto either the
         destination port (Mirror session) or the destination VLAN (RMirror
         source session). It's a bit-mask that indicates the VLANs. A '1'
         indicates the VLAN ID is selected, a '0' indicates that the VLAN ID
         isn't selected. "
    ::= { mgmtMirrorConfigSessionEntry 8 }

mgmtMirrorConfigSessionSourceVlans2KTo3K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Third quarter of bit-array indicating source VLAN list. All traffic in
         the VLANs specified in this list will get mirrored onto either the
         destination port (Mirror session) or the destination VLAN (RMirror
         source session). It's a bit-mask that indicates the VLANs. A '1'
         indicates the VLAN ID is selected, a '0' indicates that the VLAN ID
         isn't selected. "
    ::= { mgmtMirrorConfigSessionEntry 9 }

mgmtMirrorConfigSessionSourceVlans3KTo4K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Fourth quarter of bit-array indicating source VLAN list. All traffic in
         the VLANs specified in this list will get mirrored onto either the
         destination port (Mirror session) or the destination VLAN (RMirror
         source session). It's a bit-mask that indicates the VLANs. A '1'
         indicates the VLAN ID is selected, a '0' indicates that the VLAN ID
         isn't selected. "
    ::= { mgmtMirrorConfigSessionEntry 10 }

mgmtMirrorConfigSessionSourcePortListRx OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "A bit-mask that controls whether a given port is enabled for mirroring
         of incoming traffic. A '1' indicates that the port is included, whereas
         a '0' indicates it isn't. Only source sessions (Mirror and RMirror
         Source) use this value. "
    ::= { mgmtMirrorConfigSessionEntry 11 }

mgmtMirrorConfigSessionSourcePortListTx OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "A bit-mask that controls whether a given port is enabled for mirroring
         of outgoing traffic. A '1' indicates that the port is included, whereas
         a '0' indicates it isn't. Only source sessions (Mirror and RMirror
         Source) use this value. "
    ::= { mgmtMirrorConfigSessionEntry 12 }

mgmtMirrorConfigSessionCpuRx OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether mirroring of traffic received by the internal CPU is
         enabled or disabled. It is supported or not can be derived from Mirror
         capabilities.Only source sessions (Mirror and RMirror Source) use this
         value. "
    ::= { mgmtMirrorConfigSessionEntry 13 }

mgmtMirrorConfigSessionCpuTx OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls whether mirroring of traffic transmitted by the internal CPU
         is enabled or disabled. It is supported or not can be derived from
         Mirror capabilities.Only source sessions (Mirror and RMirror Source)
         use this value. "
    ::= { mgmtMirrorConfigSessionEntry 14 }

mgmtMirrorConfigSessionDestinationPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Destination port list implemented as a bit-mask, where a '1' indicates
         that the port is included and a '0' indicates that it isn't. Only used
         in plain Mirror sessions and RMirror destination sessions.
         
         Mirror session:
         
         At most one bit may be set in this mask.
         
         RMirror destination session:
         
         Zero or more bits may be set in this mask."
    ::= { mgmtMirrorConfigSessionEntry 15 }

mgmtMirrorMibConformance OBJECT IDENTIFIER
    ::= { mgmtMirrorMib 2 }

mgmtMirrorMibCompliances OBJECT IDENTIFIER
    ::= { mgmtMirrorMibConformance 1 }

mgmtMirrorMibGroups OBJECT IDENTIFIER
    ::= { mgmtMirrorMibConformance 2 }

mgmtMirrorCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMirrorCapabilitiesSessionCountMax,
                  mgmtMirrorCapabilitiesSessionSourceCountMax,
                  mgmtMirrorCapabilitiesRMirrorSuport,
                  mgmtMirrorCapabilitiesInternalReflectorPortSupport,
                  mgmtMirrorCapabilitiesCpuMirrorSupport }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMirrorMibGroups 1 }

mgmtMirrorConfigSessionTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMirrorConfigSessionSessionId,
                  mgmtMirrorConfigSessionMode,
                  mgmtMirrorConfigSessionType,
                  mgmtMirrorConfigSessionRMirrorVlan,
                  mgmtMirrorConfigSessionReflectorPort,
                  mgmtMirrorConfigSessionStripInnerTag,
                  mgmtMirrorConfigSessionSourceVlans0KTo1K,
                  mgmtMirrorConfigSessionSourceVlans1KTo2K,
                  mgmtMirrorConfigSessionSourceVlans2KTo3K,
                  mgmtMirrorConfigSessionSourceVlans3KTo4K,
                  mgmtMirrorConfigSessionSourcePortListRx,
                  mgmtMirrorConfigSessionSourcePortListTx,
                  mgmtMirrorConfigSessionCpuRx,
                  mgmtMirrorConfigSessionCpuTx,
                  mgmtMirrorConfigSessionDestinationPortList }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMirrorMibGroups 2 }

mgmtMirrorMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtMirrorCapabilitiesInfoGroup,
                       mgmtMirrorConfigSessionTableInfoGroup }

    ::= { mgmtMirrorMibCompliances 1 }

END
