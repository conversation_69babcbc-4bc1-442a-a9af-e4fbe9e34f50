-- *****************************************************************
-- HTTPS-MIB:  
-- ****************************************************************

MGMT-HTTPS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    TruthValue FROM SNMPv2-TC
    ;

mgmtHttpsMib MODULE-IDENTITY
    LAST-UPDATED "201410100000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of HTTPS"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 47 }


mgmtHttpsMibObjects OBJECT IDENTIFIER
    ::= { mgmtHttpsMib 1 }

mgmtHttpsConfig OBJECT IDENTIFIER
    ::= { mgmtHttpsMibObjects 2 }

mgmtHttpsConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtHttpsConfig 1 }

mgmtHttpsConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global mode of HTTPS. true is to enable the functions of HTTPS and
         false is to disable it."
    ::= { mgmtHttpsConfigGlobals 1 }

mgmtHttpsConfigGlobalsRedirectToHttps OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag is to enable/disable the automatic redirection from HTTP to
         HTTPS. true is to enable the redirection and false is to disable the
         redirection."
    ::= { mgmtHttpsConfigGlobals 2 }

mgmtHttpsMibConformance OBJECT IDENTIFIER
    ::= { mgmtHttpsMib 2 }

mgmtHttpsMibCompliances OBJECT IDENTIFIER
    ::= { mgmtHttpsMibConformance 1 }

mgmtHttpsMibGroups OBJECT IDENTIFIER
    ::= { mgmtHttpsMibConformance 2 }

mgmtHttpsConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtHttpsConfigGlobalsMode,
                  mgmtHttpsConfigGlobalsRedirectToHttps }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtHttpsMibGroups 1 }

mgmtHttpsMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtHttpsConfigGlobalsInfoGroup }

    ::= { mgmtHttpsMibCompliances 1 }

END
