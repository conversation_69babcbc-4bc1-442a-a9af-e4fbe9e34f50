-- *****************************************************************
-- VOICE-VLAN-MIB:  
-- ****************************************************************

MGMT-VOICE-VLAN-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTVlan FROM MGMT-TC
    ;

mgmtVoiceVlanMib MODULE-IDENTITY
    LAST-UPDATED "201508250000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the Voice VLAN MIB."
    REVISION    "201508250000Z"
    DESCRIPTION
        "Replace the SYNTAX of mgmtVoiceVlanConfigOuiTableRowEditorPrefix from
         'OCTET STRING (SIZE(3..3))' to 'OCTET STRING (SIZE(3))' according to
         RFC2578."
    REVISION    "201503250000Z"
    DESCRIPTION
        "Change syntax type of VoiceVlanConfigGlobalsMgmtVlanId."
    REVISION    "201409160000Z"
    DESCRIPTION
        "Initial version."
    ::= { mgmtSwitch 70 }


MGMTVoiceVlanPortDiscoveryProtocol ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates per port Voice VLAN discovery protocol."
    SYNTAX      INTEGER { oui(0), lldp(1), both(2) }

MGMTVoiceVlanPortManagementType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates per port Voice VLAN function administrative
         type."
    SYNTAX      INTEGER { disabled(0), forced(1), automatic(2) }

mgmtVoiceVlanMibObjects OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanMib 1 }

mgmtVoiceVlanCapabilities OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanMibObjects 1 }

mgmtVoiceVlanCapabilitiesSupportLldpDiscovery OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The capability to support voice device discovery from LLDP
         notification."
    ::= { mgmtVoiceVlanCapabilities 1 }

mgmtVoiceVlanCapabilitiesMinAgeTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum time value in second for aging telephony OUI sources in
         voice VLAN."
    ::= { mgmtVoiceVlanCapabilities 2 }

mgmtVoiceVlanCapabilitiesMaxAgeTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum time value in second for aging telephony OUI sources in
         voice VLAN."
    ::= { mgmtVoiceVlanCapabilities 3 }

mgmtVoiceVlanCapabilitiesMaxTrafficClass OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum allowed CoS (Class of Service) value to be used in
         forwarding voice VLAN traffic."
    ::= { mgmtVoiceVlanCapabilities 4 }

mgmtVoiceVlanCapabilitiesMaxOuiEntryCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum number of telephony OUI entry registration."
    ::= { mgmtVoiceVlanCapabilities 5 }

mgmtVoiceVlanConfig OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanMibObjects 2 }

mgmtVoiceVlanConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanConfig 1 }

mgmtVoiceVlanConfigGlobalsMgmt OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanConfigGlobals 1 }

mgmtVoiceVlanConfigGlobalsMgmtAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Administrative control for system wide voice VLAN function, TRUE is to
         enable the voice VLAN function and FALSE is to disable it."
    ::= { mgmtVoiceVlanConfigGlobalsMgmt 1 }

mgmtVoiceVlanConfigGlobalsMgmtVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "VLAN ID, which should be unique in the system, for voice VLAN."
    ::= { mgmtVoiceVlanConfigGlobalsMgmt 2 }

mgmtVoiceVlanConfigGlobalsMgmtAgingTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MAC address aging time (T) for telephony OUI source registrated by
         voice VLAN. The actual timing in purging the specific entry ranges from
         T to 2T."
    ::= { mgmtVoiceVlanConfigGlobalsMgmt 3 }

mgmtVoiceVlanConfigGlobalsMgmtTrafficClass OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Traffic class value used in frame CoS queuing insides voice VLAN. All
         kinds of traffic on voice VLAN apply this traffic class."
    ::= { mgmtVoiceVlanConfigGlobalsMgmt 4 }

mgmtVoiceVlanConfigInterface OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanConfig 2 }

mgmtVoiceVlanConfigInterfacePortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVoiceVlanConfigInterfacePortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing per port voice VLAN functions."
    ::= { mgmtVoiceVlanConfigInterface 1 }

mgmtVoiceVlanConfigInterfacePortEntry OBJECT-TYPE
    SYNTAX      MGMTVoiceVlanConfigInterfacePortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtVoiceVlanConfigInterfacePortIfIndex }
    ::= { mgmtVoiceVlanConfigInterfacePortTable 1 }

MGMTVoiceVlanConfigInterfacePortEntry ::= SEQUENCE {
    mgmtVoiceVlanConfigInterfacePortIfIndex            MGMTInterfaceIndex,
    mgmtVoiceVlanConfigInterfacePortMode               MGMTVoiceVlanPortManagementType,
    mgmtVoiceVlanConfigInterfacePortDiscoveryProtocol  MGMTVoiceVlanPortDiscoveryProtocol,
    mgmtVoiceVlanConfigInterfacePortSecured            TruthValue
}

mgmtVoiceVlanConfigInterfacePortIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the Voice VLAN port."
    ::= { mgmtVoiceVlanConfigInterfacePortEntry 1 }

mgmtVoiceVlanConfigInterfacePortMode OBJECT-TYPE
    SYNTAX      MGMTVoiceVlanPortManagementType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Management mode of the specific port in voice VLAN. 'disabled' will
         disjoin the port from voice VLAN. 'forced' will force the port to join
         voice VLAN. 'automatic' will join the port in voice VLAN upon detecting
         attached VoIP devices by using DiscoveryProtocol parameter."
    ::= { mgmtVoiceVlanConfigInterfacePortEntry 2 }

mgmtVoiceVlanConfigInterfacePortDiscoveryProtocol OBJECT-TYPE
    SYNTAX      MGMTVoiceVlanPortDiscoveryProtocol
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify the protocol for detecting attached VoIP devices. It only works
         when 'automatic' is set in Mode parameter, and voice VLAN will restart
         automatic detecting process upon changing the protocol. When 'oui' is
         given, voice VLAN performs VoIP device detection based on checking
         telephony OUI settings via new MAC address notification. When 'lldp' is
         given, voice VLAN performs VoIP device detection based on LLDP
         notifications.When 'both' is given, voice VLAN performs VoIP device
         detection based on either new MAC address notification or LLDP
         notifications.
         
         In addition, the first come notification will be first served."
    ::= { mgmtVoiceVlanConfigInterfacePortEntry 3 }

mgmtVoiceVlanConfigInterfacePortSecured OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Manage the security control of this port interface in voice VLAN, TRUE
         is to enable the security control and FALSE is to disable it. When it
         is disabled, all the traffic in voice VLAN will be permit. When it is
         enabled, all non-telephonic MAC addresses in the voice VLAN will be
         blocked for 10 seconds and thus the traffic from these senders will be
         deny."
    ::= { mgmtVoiceVlanConfigInterfacePortEntry 4 }

mgmtVoiceVlanConfigOui OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanConfig 3 }

mgmtVoiceVlanConfigOuiTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVoiceVlanConfigOuiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing the telephony OUI settings that will be
         used for voice VLAN functions."
    ::= { mgmtVoiceVlanConfigOui 1 }

mgmtVoiceVlanConfigOuiEntry OBJECT-TYPE
    SYNTAX      MGMTVoiceVlanConfigOuiEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtVoiceVlanConfigOuiPrefix }
    ::= { mgmtVoiceVlanConfigOuiTable 1 }

MGMTVoiceVlanConfigOuiEntry ::= SEQUENCE {
    mgmtVoiceVlanConfigOuiPrefix       OCTET STRING,
    mgmtVoiceVlanConfigOuiDescription  MGMTDisplayString,
    mgmtVoiceVlanConfigOuiAction       MGMTRowEditorState
}

mgmtVoiceVlanConfigOuiPrefix OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(3))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Address prefix of the telephony OUI. A leading 3 bytes index used to
         denote whether specific MAC address is presenting a voice device."
    ::= { mgmtVoiceVlanConfigOuiEntry 1 }

mgmtVoiceVlanConfigOuiDescription OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The description for the specific telephony OUI."
    ::= { mgmtVoiceVlanConfigOuiEntry 2 }

mgmtVoiceVlanConfigOuiAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVoiceVlanConfigOuiEntry 100 }

mgmtVoiceVlanConfigOuiTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanConfigOui 2 }

mgmtVoiceVlanConfigOuiTableRowEditorPrefix OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(3))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Address prefix of the telephony OUI. A leading 3 bytes index used to
         denote whether specific MAC address is presenting a voice device."
    ::= { mgmtVoiceVlanConfigOuiTableRowEditor 1 }

mgmtVoiceVlanConfigOuiTableRowEditorDescription OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..32))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The description for the specific telephony OUI."
    ::= { mgmtVoiceVlanConfigOuiTableRowEditor 2 }

mgmtVoiceVlanConfigOuiTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVoiceVlanConfigOuiTableRowEditor 100 }

mgmtVoiceVlanMibConformance OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanMib 2 }

mgmtVoiceVlanMibCompliances OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanMibConformance 1 }

mgmtVoiceVlanMibGroups OBJECT IDENTIFIER
    ::= { mgmtVoiceVlanMibConformance 2 }

mgmtVoiceVlanCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVoiceVlanCapabilitiesSupportLldpDiscovery,
                  mgmtVoiceVlanCapabilitiesMinAgeTime,
                  mgmtVoiceVlanCapabilitiesMaxAgeTime,
                  mgmtVoiceVlanCapabilitiesMaxTrafficClass,
                  mgmtVoiceVlanCapabilitiesMaxOuiEntryCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVoiceVlanMibGroups 1 }

mgmtVoiceVlanConfigGlobalsMgmtInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVoiceVlanConfigGlobalsMgmtAdminState,
                  mgmtVoiceVlanConfigGlobalsMgmtVlanId,
                  mgmtVoiceVlanConfigGlobalsMgmtAgingTime,
                  mgmtVoiceVlanConfigGlobalsMgmtTrafficClass }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVoiceVlanMibGroups 2 }

mgmtVoiceVlanConfigInterfacePortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVoiceVlanConfigInterfacePortIfIndex,
                  mgmtVoiceVlanConfigInterfacePortMode,
                  mgmtVoiceVlanConfigInterfacePortDiscoveryProtocol,
                  mgmtVoiceVlanConfigInterfacePortSecured }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVoiceVlanMibGroups 3 }

mgmtVoiceVlanConfigOuiTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVoiceVlanConfigOuiPrefix,
                  mgmtVoiceVlanConfigOuiDescription,
                  mgmtVoiceVlanConfigOuiAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVoiceVlanMibGroups 4 }

mgmtVoiceVlanConfigOuiTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVoiceVlanConfigOuiTableRowEditorPrefix,
                  mgmtVoiceVlanConfigOuiTableRowEditorDescription,
                  mgmtVoiceVlanConfigOuiTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVoiceVlanMibGroups 5 }

mgmtVoiceVlanMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtVoiceVlanCapabilitiesInfoGroup,
                       mgmtVoiceVlanConfigGlobalsMgmtInfoGroup,
                       mgmtVoiceVlanConfigInterfacePortTableInfoGroup,
                       mgmtVoiceVlanConfigOuiTableInfoGroup,
                       mgmtVoiceVlanConfigOuiTableRowEditorInfoGroup }

    ::= { mgmtVoiceVlanMibCompliances 1 }

END
