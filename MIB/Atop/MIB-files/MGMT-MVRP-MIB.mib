-- *****************************************************************
-- MVRP-MIB:  
-- ****************************************************************

MGMT-MVRP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Counter64 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTVlanListQuarter FROM MGMT-TC
    ;

mgmtMvrpMib MODULE-IDENTITY
    LAST-UPDATED "201510190000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private MVRP MIB."
    REVISION    "201510190000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 87 }


mgmtMvrpMibObjects OBJECT IDENTIFIER
    ::= { mgmtMvrpMib 1 }

mgmtMvrpConfig OBJECT IDENTIFIER
    ::= { mgmtMvrpMibObjects 2 }

mgmtMvrpConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtMvrpConfig 1 }

mgmtMvrpConfigGlobalsGlobalState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global state of MVRP. TRUE - enable MVRP, FALSE - disable MVRP."
    ::= { mgmtMvrpConfigGlobals 1 }

mgmtMvrpConfigGlobalsManagedVlans0KTo1K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "First quarter of bit-array indicating the MVRP-managed VLANs."
    ::= { mgmtMvrpConfigGlobals 2 }

mgmtMvrpConfigGlobalsManagedVlans1KTo2K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Second quarter of bit-array indicating the MVRP-managed VLANs."
    ::= { mgmtMvrpConfigGlobals 3 }

mgmtMvrpConfigGlobalsManagedVlans2KTo3K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Third quarter of bit-array indicating the MVRP-managed VLANs."
    ::= { mgmtMvrpConfigGlobals 4 }

mgmtMvrpConfigGlobalsManagedVlans3KTo4K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Last quarter of bit-array indicating the MVRP-managed VLANs."
    ::= { mgmtMvrpConfigGlobals 5 }

mgmtMvrpConfigInterface OBJECT IDENTIFIER
    ::= { mgmtMvrpConfig 2 }

mgmtMvrpConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMvrpConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the MVRP interface configuration table. The number of
         interfaces is the total number of ports available on the switch/stack.
         Each one of these interfaces can be set to either MVRP enabled or MVRP
         disabled."
    ::= { mgmtMvrpConfigInterface 1 }

mgmtMvrpConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTMvrpConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Entries in this table represent switch interfaces and their
         corresponding MVRP state"
    INDEX       { mgmtMvrpConfigInterfaceIfIndex }
    ::= { mgmtMvrpConfigInterfaceTable 1 }

MGMTMvrpConfigInterfaceEntry ::= SEQUENCE {
    mgmtMvrpConfigInterfaceIfIndex    MGMTInterfaceIndex,
    mgmtMvrpConfigInterfacePortState  TruthValue
}

mgmtMvrpConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtMvrpConfigInterfaceEntry 1 }

mgmtMvrpConfigInterfacePortState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Per-interface state of MVRP. TRUE - enable MVRP on the interface, FALSE
         - disable MVRP on the interface."
    ::= { mgmtMvrpConfigInterfaceEntry 2 }

mgmtMvrpStatus OBJECT IDENTIFIER
    ::= { mgmtMvrpMibObjects 3 }

mgmtMvrpStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMvrpStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the MVRP interface statistics table. The number of interfaces
         is the total number of ports available on the switch/stack. "
    ::= { mgmtMvrpStatus 1 }

mgmtMvrpStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTMvrpStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a counter and a MAC address."
    INDEX       { mgmtMvrpStatusInterfaceIfIndex }
    ::= { mgmtMvrpStatusInterfaceTable 1 }

MGMTMvrpStatusInterfaceEntry ::= SEQUENCE {
    mgmtMvrpStatusInterfaceIfIndex              MGMTInterfaceIndex,
    mgmtMvrpStatusInterfaceFailedRegistrations  Counter64,
    mgmtMvrpStatusInterfaceLastPduOrigin        MacAddress
}

mgmtMvrpStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtMvrpStatusInterfaceEntry 1 }

mgmtMvrpStatusInterfaceFailedRegistrations OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of failed VLAN registrations."
    ::= { mgmtMvrpStatusInterfaceEntry 2 }

mgmtMvrpStatusInterfaceLastPduOrigin OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Source MAC Address of the last MVRPDU received."
    ::= { mgmtMvrpStatusInterfaceEntry 3 }

mgmtMvrpMibConformance OBJECT IDENTIFIER
    ::= { mgmtMvrpMib 2 }

mgmtMvrpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtMvrpMibConformance 1 }

mgmtMvrpMibGroups OBJECT IDENTIFIER
    ::= { mgmtMvrpMibConformance 2 }

mgmtMvrpConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMvrpConfigGlobalsGlobalState,
                  mgmtMvrpConfigGlobalsManagedVlans0KTo1K,
                  mgmtMvrpConfigGlobalsManagedVlans1KTo2K,
                  mgmtMvrpConfigGlobalsManagedVlans2KTo3K,
                  mgmtMvrpConfigGlobalsManagedVlans3KTo4K }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMvrpMibGroups 1 }

mgmtMvrpConfigInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMvrpConfigInterfaceIfIndex,
                  mgmtMvrpConfigInterfacePortState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMvrpMibGroups 2 }

mgmtMvrpStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMvrpStatusInterfaceIfIndex,
                  mgmtMvrpStatusInterfaceFailedRegistrations,
                  mgmtMvrpStatusInterfaceLastPduOrigin }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMvrpMibGroups 3 }

mgmtMvrpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtMvrpConfigGlobalsInfoGroup,
                       mgmtMvrpConfigInterfaceTableInfoGroup,
                       mgmtMvrpStatusInterfaceTableInfoGroup }

    ::= { mgmtMvrpMibCompliances 1 }

END
