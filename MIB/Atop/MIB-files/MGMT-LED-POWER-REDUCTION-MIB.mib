-- *****************************************************************
-- LED-POWER-REDUCTION-MIB:  
-- ****************************************************************

MGMT-LED-POWER-REDUCTION-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtLedPowerReductionMib MODULE-IDENTITY
    LAST-UPDATED "201410100000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of LEDs power reduction. The LEDs power
         consumption can be reduced by lowering the LEDs intensity"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 77 }


mgmtLedPowerReductionMibObjects OBJECT IDENTIFIER
    ::= { mgmtLedPowerReductionMib 1 }

mgmtLedPowerReductionConfig OBJECT IDENTIFIER
    ::= { mgmtLedPowerReductionMibObjects 2 }

mgmtLedPowerReductionConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtLedPowerReductionConfig 1 }

mgmtLedPowerReductionConfigGlobalsMaintenanceDuration OBJECT-TYPE
    SYNTAX      MGMTUnsigned16 (0..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The switch maintenance duration (in seconds). During switch maintenance
         LEDs will glow in full intensity after either a port has changed link
         state or the LED push button has been pushed."
    ::= { mgmtLedPowerReductionConfigGlobals 1 }

mgmtLedPowerReductionConfigGlobalsErrorEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Turned on LEDs at full brightness(100% intensity) when LED is blinking
         in red because of either software error or fatal occurred. true means
         LEDs will glow in full brightness, false means LEDs will not glow in
         full brightness."
    ::= { mgmtLedPowerReductionConfigGlobals 2 }

mgmtLedPowerReductionConfigGlobalsParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLedPowerReductionConfigGlobalsParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to assign led intensity level to each clock hour(based
         on 24-hour time notaion) of the day"
    ::= { mgmtLedPowerReductionConfig 2 }

mgmtLedPowerReductionConfigGlobalsParamEntry OBJECT-TYPE
    SYNTAX      MGMTLedPowerReductionConfigGlobalsParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each clock hour of the day associates with led intensity level"
    INDEX       {                   mgmtLedPowerReductionConfigGlobalsParamClockTimeHour }
    ::= { mgmtLedPowerReductionConfigGlobalsParamTable 1 }

MGMTLedPowerReductionConfigGlobalsParamEntry ::= SEQUENCE {
    mgmtLedPowerReductionConfigGlobalsParamClockTimeHour   Integer32,
    mgmtLedPowerReductionConfigGlobalsParamIntensityLevel  MGMTUnsigned8
}

mgmtLedPowerReductionConfigGlobalsParamClockTimeHour OBJECT-TYPE
    SYNTAX      Integer32 (0..23)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Clock time hour. Hour is based on 24-hour time notation. If hour is x
         then LEDs will start glow at clock time x:00 of the day with associated
         LEDs intensity level."
    ::= { mgmtLedPowerReductionConfigGlobalsParamEntry 1 }

mgmtLedPowerReductionConfigGlobalsParamIntensityLevel OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..100)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Led power intensity level. The LEDs brightness in percentage. 100 means
         full intensity(maximum power consumption), 0 means LEDs are off(no
         power consumption)."
    ::= { mgmtLedPowerReductionConfigGlobalsParamEntry 2 }

mgmtLedPowerReductionMibConformance OBJECT IDENTIFIER
    ::= { mgmtLedPowerReductionMib 2 }

mgmtLedPowerReductionMibCompliances OBJECT IDENTIFIER
    ::= { mgmtLedPowerReductionMibConformance 1 }

mgmtLedPowerReductionMibGroups OBJECT IDENTIFIER
    ::= { mgmtLedPowerReductionMibConformance 2 }

mgmtLedPowerReductionConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtLedPowerReductionConfigGlobalsMaintenanceDuration,
                  mgmtLedPowerReductionConfigGlobalsErrorEnable }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLedPowerReductionMibGroups 1 }

mgmtLedPowerReductionConfigGlobalsParamTableInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtLedPowerReductionConfigGlobalsParamClockTimeHour,
                  mgmtLedPowerReductionConfigGlobalsParamIntensityLevel }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLedPowerReductionMibGroups 2 }

mgmtLedPowerReductionMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtLedPowerReductionConfigGlobalsInfoGroup,
                       mgmtLedPowerReductionConfigGlobalsParamTableInfoGroup }

    ::= { mgmtLedPowerReductionMibCompliances 1 }

END
