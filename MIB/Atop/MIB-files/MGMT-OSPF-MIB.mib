-- *****************************************************************
-- OSPF-MIB:  
-- ****************************************************************

MGMT-OSPF-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Counter64 FROM SNMPv2-SMI
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtOspfMib MODULE-IDENTITY
    LAST-UPDATED "201807090000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the OSPF MIB."
    REVISION    "201807090000Z"
    DESCRIPTION
        "Add RIP redistributed, database support, and correct the key length
         from 160 to 128 according to the cryptography."
    REVISION    "201805160000Z"
    DESCRIPTION
        "Update OSPF capabilities."
    REVISION    "201805140000Z"
    DESCRIPTION
        "Add stub router, NSSA, default route redistribution and passive
         interface support."
    REVISION    "201711110000Z"
    DESCRIPTION
        "Initial version."
    ::= { mgmtSwitch 154 }


MGMTOspfAreaType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The area type."
    SYNTAX      INTEGER { normalArea(0), stubArea(1),
                          totallyStubArea(2), nssa(3), areaUnknown(4) }

MGMTOspfAuthType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates OSPF authentication type."
    SYNTAX      INTEGER { nullAuth(0), simplePasswordAuth(1),
                          md5Auth(2), areaConfigAuth(3) }

MGMTOspfBorderRouterType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The border router type of the OSPF route entry."
    SYNTAX      INTEGER { abr(0), asbrIntra(1), asbrInter(2),
                          abrAsbr(3), none(4) }

MGMTOspfInterfaceState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The state of the link."
    SYNTAX      INTEGER { down(1), loopback(2), waiting(3),
                          pointToPoint(4), drOther(5), bdr(6), dr(7),
                          unknown(8) }

MGMTOspfLsdbType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    SYNTAX      INTEGER { none(0), router(1), network(2), summary(3),
                          asbrSummary(4), external(5),
                          nssaExternal(7) }

MGMTOspfNeighborState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The state of the neighbor node."
    SYNTAX      INTEGER { dependupon(0), deleted(1), down(2),
                          attempt(3), init(4), twoWay(5), exstart(6),
                          exchange(7), loading(8), full(9),
                          unknown(10) }

MGMTOspfNssaTranslatorRole ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The OSPF NSSA translator role."
    SYNTAX      INTEGER { candidate(0), never(1), always(2) }

MGMTOspfNssaTranslatorState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The OSPF NSSA translator state."
    SYNTAX      INTEGER { disabled(0), elected(1), enabled(2) }

MGMTOspfRedistributedMetricType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The OSPF redistributed metric type."
    SYNTAX      INTEGER { metricTypeNone(0), metricType1(1),
                          metricType2(2) }

MGMTOspfRouteType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The OSPF route type."
    SYNTAX      INTEGER { intraArea(0), interArea(1), borderRouter(2),
                          externalType1(3), externalType2(4),
                          unknown(5) }

mgmtOspfMibObjects OBJECT IDENTIFIER
    ::= { mgmtOspfMib 1 }

mgmtOspfCapabilities OBJECT IDENTIFIER
    ::= { mgmtOspfMibObjects 1 }

mgmtOspfCapabilitiesMinInstanceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "OSPF minimum instance ID"
    ::= { mgmtOspfCapabilities 1 }

mgmtOspfCapabilitiesMaxInstanceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "OSPF maximum instance ID"
    ::= { mgmtOspfCapabilities 2 }

mgmtOspfCapabilitiesMinRouterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF router ID"
    ::= { mgmtOspfCapabilities 3 }

mgmtOspfCapabilitiesMaxRouterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF router ID"
    ::= { mgmtOspfCapabilities 4 }

mgmtOspfCapabilitiesMinPriority OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF priority"
    ::= { mgmtOspfCapabilities 5 }

mgmtOspfCapabilitiesMaxPriority OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF priority"
    ::= { mgmtOspfCapabilities 6 }

mgmtOspfCapabilitiesMinGeneralCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF interface cost"
    ::= { mgmtOspfCapabilities 7 }

mgmtOspfCapabilitiesMaxGeneralCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF interface cost"
    ::= { mgmtOspfCapabilities 8 }

mgmtOspfCapabilitiesMinInterfaceCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF interface cost"
    ::= { mgmtOspfCapabilities 9 }

mgmtOspfCapabilitiesMaxInterfaceCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF interface cost"
    ::= { mgmtOspfCapabilities 10 }

mgmtOspfCapabilitiesMinRedistributeCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF redistribute cost"
    ::= { mgmtOspfCapabilities 11 }

mgmtOspfCapabilitiesMaxRedistributeCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF redistribute cost"
    ::= { mgmtOspfCapabilities 12 }

mgmtOspfCapabilitiesMinHelloInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF hello interval"
    ::= { mgmtOspfCapabilities 13 }

mgmtOspfCapabilitiesMaxHelloInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF hello interval"
    ::= { mgmtOspfCapabilities 14 }

mgmtOspfCapabilitiesMinFastHelloPackets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF fast hello packets"
    ::= { mgmtOspfCapabilities 15 }

mgmtOspfCapabilitiesMaxFastHelloPackets OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF fast hello packets"
    ::= { mgmtOspfCapabilities 16 }

mgmtOspfCapabilitiesMinRetransmitInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF retransmit interval"
    ::= { mgmtOspfCapabilities 17 }

mgmtOspfCapabilitiesMaxRetransmitInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF retransmit interval"
    ::= { mgmtOspfCapabilities 18 }

mgmtOspfCapabilitiesMinDeadInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF dead interval"
    ::= { mgmtOspfCapabilities 19 }

mgmtOspfCapabilitiesMaxDeadInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF dead interval"
    ::= { mgmtOspfCapabilities 20 }

mgmtOspfCapabilitiesMinRouterLsaOnStartup OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF router LSA on startup stage"
    ::= { mgmtOspfCapabilities 21 }

mgmtOspfCapabilitiesMaxRouterLsaOnStartup OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF router LSA on startup stage"
    ::= { mgmtOspfCapabilities 22 }

mgmtOspfCapabilitiesMinRouterLsaOnShutdown OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of router LSA on shutdown stage"
    ::= { mgmtOspfCapabilities 23 }

mgmtOspfCapabilitiesMaxRouterLsaOnShutdown OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF router LSA on shutdown stage"
    ::= { mgmtOspfCapabilities 24 }

mgmtOspfCapabilitiesMinMdKeyId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF authentication message digest key ID"
    ::= { mgmtOspfCapabilities 25 }

mgmtOspfCapabilitiesMaxMdKeyId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF authentication message digest key ID"
    ::= { mgmtOspfCapabilities 26 }

mgmtOspfCapabilitiesMinSimplePwdLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum vlength of OSPF authentication simple password length"
    ::= { mgmtOspfCapabilities 27 }

mgmtOspfCapabilitiesMaxSimplePwdLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum length of OSPF authentication simple password length"
    ::= { mgmtOspfCapabilities 28 }

mgmtOspfCapabilitiesMinMdKeyLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum length of OSPF authentication message digest key length"
    ::= { mgmtOspfCapabilities 29 }

mgmtOspfCapabilitiesMaxMdKeyLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum length of OSPF authentication message digest key length"
    ::= { mgmtOspfCapabilities 30 }

mgmtOspfCapabilitiesIsRipRedistributedSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if RIP redistributed is supported or not"
    ::= { mgmtOspfCapabilities 31 }

mgmtOspfCapabilitiesMinAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF administrative distance value"
    ::= { mgmtOspfCapabilities 32 }

mgmtOspfCapabilitiesMaxAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF administrative distance value"
    ::= { mgmtOspfCapabilities 33 }

mgmtOspfConfig OBJECT IDENTIFIER
    ::= { mgmtOspfMibObjects 2 }

mgmtOspfConfigProcessTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigProcessEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF process configuration table. It is used to enable or
         disable the routing process on a specific process ID."
    ::= { mgmtOspfConfig 1 }

mgmtOspfConfigProcessEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigProcessEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF routing process."
    INDEX       { mgmtOspfConfigProcessInstanceId }
    ::= { mgmtOspfConfigProcessTable 1 }

MGMTOspfConfigProcessEntry ::= SEQUENCE {
    mgmtOspfConfigProcessInstanceId  Integer32,
    mgmtOspfConfigProcessAction      MGMTRowEditorState
}

mgmtOspfConfigProcessInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigProcessEntry 1 }

mgmtOspfConfigProcessAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigProcessEntry 100 }

mgmtOspfConfigProcessTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspfConfig 2 }

mgmtOspfConfigProcessTableRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigProcessTableRowEditor 1 }

mgmtOspfConfigProcessTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigProcessTableRowEditor 100 }

mgmtOspfConfigRouterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF router configuration table. It is a general group to
         configure the OSPF common router parameters."
    ::= { mgmtOspfConfig 3 }

mgmtOspfConfigRouterEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents the OSPF router interface
         configuration."
    INDEX       { mgmtOspfConfigRouterInstanceId }
    ::= { mgmtOspfConfigRouterTable 1 }

MGMTOspfConfigRouterEntry ::= SEQUENCE {
    mgmtOspfConfigRouterInstanceId                          Integer32,
    mgmtOspfConfigRouterIsSpecificRouterId                  TruthValue,
    mgmtOspfConfigRouterRouterId                            IpAddress,
    mgmtOspfConfigRouterDefPassiveInterface                 TruthValue,
    mgmtOspfConfigRouterIsSpecificDefMetric                 TruthValue,
    mgmtOspfConfigRouterDefMetricVal                        Unsigned32,
    mgmtOspfConfigRouterConnectedRedistMetricType           MGMTOspfRedistributedMetricType,
    mgmtOspfConfigRouterConnectedRedistIsSpecificMetric     TruthValue,
    mgmtOspfConfigRouterConnectedRedistMetricVal            Unsigned32,
    mgmtOspfConfigRouterStaticRedistMetricType              MGMTOspfRedistributedMetricType,
    mgmtOspfConfigRouterStaticRedistIsSpecificMetric        TruthValue,
    mgmtOspfConfigRouterStaticRedistMetricVal               Unsigned32,
    mgmtOspfConfigRouterRipRedistMetricType                 MGMTOspfRedistributedMetricType,
    mgmtOspfConfigRouterRipRedistIsSpecificMetric           TruthValue,
    mgmtOspfConfigRouterRipRedistMetricVal                  Unsigned32,
    mgmtOspfConfigRouterIsOnStartup                         TruthValue,
    mgmtOspfConfigRouterOnStartupInterval                   Unsigned32,
    mgmtOspfConfigRouterIsOnShutdown                        TruthValue,
    mgmtOspfConfigRouterOnShutdownInterval                  Unsigned32,
    mgmtOspfConfigRouterIsAdministrative                    TruthValue,
    mgmtOspfConfigRouterDefaultRouteRedistMetricType        MGMTOspfRedistributedMetricType,
    mgmtOspfConfigRouterDefaultRouteRedistIsSpecificMetric  TruthValue,
    mgmtOspfConfigRouterDefaultRouteRedistMetricVal         Unsigned32,
    mgmtOspfConfigRouterDefaultRouteRedistAlways            TruthValue,
    mgmtOspfConfigRouterAdminDistance                       MGMTUnsigned8
}

mgmtOspfConfigRouterInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigRouterEntry 1 }

mgmtOspfConfigRouterIsSpecificRouterId OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the 'RouterId' argument is a specific configured value or not."
    ::= { mgmtOspfConfigRouterEntry 2 }

mgmtOspfConfigRouterRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF router ID"
    ::= { mgmtOspfConfigRouterEntry 3 }

mgmtOspfConfigRouterDefPassiveInterface OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configure all interfaces as passive-interface by default."
    ::= { mgmtOspfConfigRouterEntry 4 }

mgmtOspfConfigRouterIsSpecificDefMetric OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the default metric is a specific configured value or not."
    ::= { mgmtOspfConfigRouterEntry 5 }

mgmtOspfConfigRouterDefMetricVal OBJECT-TYPE
    SYNTAX      Unsigned32 (0..********)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified default metric value for the OSPF routing protocol. The
         field is significant only when the arugment 'IsSpecificDefMetric' is
         TRUE"
    ::= { mgmtOspfConfigRouterEntry 6 }

mgmtOspfConfigRouterConnectedRedistMetricType OBJECT-TYPE
    SYNTAX      MGMTOspfRedistributedMetricType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF redistributed metric type for the connected interfaces."
    ::= { mgmtOspfConfigRouterEntry 7 }

mgmtOspfConfigRouterConnectedRedistIsSpecificMetric OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the metric value of connected interfaces redistribution is a
         specific configured value or not."
    ::= { mgmtOspfConfigRouterEntry 8 }

mgmtOspfConfigRouterConnectedRedistMetricVal OBJECT-TYPE
    SYNTAX      Unsigned32 (0..********)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified metric value for the connected interfaces. The field is
         significant only when the argument 'ConnectedRedistIsSpecificMetric' is
         TRUE"
    ::= { mgmtOspfConfigRouterEntry 9 }

mgmtOspfConfigRouterStaticRedistMetricType OBJECT-TYPE
    SYNTAX      MGMTOspfRedistributedMetricType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF redistributed metric type for the static routes."
    ::= { mgmtOspfConfigRouterEntry 10 }

mgmtOspfConfigRouterStaticRedistIsSpecificMetric OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the metric value if static route redistribution is a specific
         configured value or not."
    ::= { mgmtOspfConfigRouterEntry 11 }

mgmtOspfConfigRouterStaticRedistMetricVal OBJECT-TYPE
    SYNTAX      Unsigned32 (0..********)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified metric value for the static routes. The field is
         significant only when the argument 'StaticRedistIsSpecificMetric' is
         TRUE"
    ::= { mgmtOspfConfigRouterEntry 12 }

mgmtOspfConfigRouterRipRedistMetricType OBJECT-TYPE
    SYNTAX      MGMTOspfRedistributedMetricType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF redistributed metric type for the RIP routes. The field is
         significant only when the RIP protocol is supported on the device."
    ::= { mgmtOspfConfigRouterEntry 13 }

mgmtOspfConfigRouterRipRedistIsSpecificMetric OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the metric value if RIP route redistribution is a specific
         configured value or not.The field is significant only when the RIP
         protocol is supported on the device."
    ::= { mgmtOspfConfigRouterEntry 14 }

mgmtOspfConfigRouterRipRedistMetricVal OBJECT-TYPE
    SYNTAX      Unsigned32 (0..********)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified metric value for the RIP routes. The field is
         significant only when the RIP protocol is supported on the device and
         argument 'RipRedistIsSpecificMetric' is TRUE."
    ::= { mgmtOspfConfigRouterEntry 15 }

mgmtOspfConfigRouterIsOnStartup OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configures OSPF to advertise a maximum metric during startup for a
         configured period of time."
    ::= { mgmtOspfConfigRouterEntry 16 }

mgmtOspfConfigRouterOnStartupInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (5..86400)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified time interval (seconds) to advertise itself as stub
         area. The field is significant only when the on-startup mode is
         enabled."
    ::= { mgmtOspfConfigRouterEntry 17 }

mgmtOspfConfigRouterIsOnShutdown OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configures OSPF to advertise a maximum metric during shutdown for a
         configured period of time. The device advertises a maximum metric when
         the OSPF router mode is disabled and notice that the mechanism also
         works when the device reboots but not for the'reload default' case."
    ::= { mgmtOspfConfigRouterEntry 18 }

mgmtOspfConfigRouterOnShutdownInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (5..100)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified time interval (seconds) to wait till shutdown completed.
         The field is significant only when the on-shutdown mode is enabled."
    ::= { mgmtOspfConfigRouterEntry 19 }

mgmtOspfConfigRouterIsAdministrative OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configures OSPF stub router mode administratively applied, for an
         indefinite period."
    ::= { mgmtOspfConfigRouterEntry 20 }

mgmtOspfConfigRouterDefaultRouteRedistMetricType OBJECT-TYPE
    SYNTAX      MGMTOspfRedistributedMetricType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF redistributed metric type for a default route."
    ::= { mgmtOspfConfigRouterEntry 21 }

mgmtOspfConfigRouterDefaultRouteRedistIsSpecificMetric OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the metric value of a default route redistribution is a
         specific configured value or not."
    ::= { mgmtOspfConfigRouterEntry 22 }

mgmtOspfConfigRouterDefaultRouteRedistMetricVal OBJECT-TYPE
    SYNTAX      Unsigned32 (0..********)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified metric value for a default route. The field is
         significant only when the argument 'DefaultRouteRedistIsSpecificMetric'
         is TRUE"
    ::= { mgmtOspfConfigRouterEntry 23 }

mgmtOspfConfigRouterDefaultRouteRedistAlways OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specifies to always advertise a default route into all external-routing
         capable areas. Otherwise, the router only to advertise the default
         route when the advertising router already has a default route."
    ::= { mgmtOspfConfigRouterEntry 24 }

mgmtOspfConfigRouterAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF administrative distance."
    ::= { mgmtOspfConfigRouterEntry 25 }

mgmtOspfConfigRouterInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigRouterInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF router interface configuration table."
    ::= { mgmtOspfConfig 4 }

mgmtOspfConfigRouterInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigRouterInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each router interface has a set of parameters."
    INDEX       { mgmtOspfConfigRouterInterfaceInstanceId,
                  mgmtOspfConfigRouterInterfaceIfIndex }
    ::= { mgmtOspfConfigRouterInterfaceTable 1 }

MGMTOspfConfigRouterInterfaceEntry ::= SEQUENCE {
    mgmtOspfConfigRouterInterfaceInstanceId        Integer32,
    mgmtOspfConfigRouterInterfaceIfIndex           MGMTInterfaceIndex,
    mgmtOspfConfigRouterInterfacePassiveInterface  TruthValue
}

mgmtOspfConfigRouterInterfaceInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigRouterInterfaceEntry 1 }

mgmtOspfConfigRouterInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspfConfigRouterInterfaceEntry 2 }

mgmtOspfConfigRouterInterfacePassiveInterface OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable the interface as OSPF passive-interface."
    ::= { mgmtOspfConfigRouterInterfaceEntry 3 }

mgmtOspfConfigAreaTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF area configuration table. It is used to specify the OSPF
         enabled interface(s). When OSPF is enabled on the specific
         interface(s), the router can provide the network information to the
         other OSPF routers via those interfaces."
    ::= { mgmtOspfConfig 5 }

mgmtOspfConfigAreaEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents the OSPF enabled interface(s) and
         its area ID."
    INDEX       { mgmtOspfConfigAreaInstanceId,
                  mgmtOspfConfigAreaNetwork,
                  mgmtOspfConfigAreaIpSubnetMaskLength }
    ::= { mgmtOspfConfigAreaTable 1 }

MGMTOspfConfigAreaEntry ::= SEQUENCE {
    mgmtOspfConfigAreaInstanceId          Integer32,
    mgmtOspfConfigAreaNetwork             IpAddress,
    mgmtOspfConfigAreaIpSubnetMaskLength  Integer32,
    mgmtOspfConfigAreaAreaId              IpAddress,
    mgmtOspfConfigAreaAction              MGMTRowEditorState
}

mgmtOspfConfigAreaInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigAreaEntry 1 }

mgmtOspfConfigAreaNetwork OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtOspfConfigAreaEntry 2 }

mgmtOspfConfigAreaIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtOspfConfigAreaEntry 3 }

mgmtOspfConfigAreaAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigAreaEntry 4 }

mgmtOspfConfigAreaAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigAreaEntry 100 }

mgmtOspfConfigAreaTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspfConfig 6 }

mgmtOspfConfigAreaTableRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigAreaTableRowEditor 1 }

mgmtOspfConfigAreaTableRowEditorNetwork OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtOspfConfigAreaTableRowEditor 2 }

mgmtOspfConfigAreaTableRowEditorIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtOspfConfigAreaTableRowEditor 3 }

mgmtOspfConfigAreaTableRowEditorAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigAreaTableRowEditor 4 }

mgmtOspfConfigAreaTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigAreaTableRowEditor 100 }

mgmtOspfConfigAreaAuthTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigAreaAuthEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF area authentication configuration table. It is used to
         applied the authentication to all the interfaces belong to the area."
    ::= { mgmtOspfConfig 7 }

mgmtOspfConfigAreaAuthEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigAreaAuthEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF area authentication
         configuration. Notice that the authentication type setting on the
         sepecific interface overrides the area's setting."
    INDEX       { mgmtOspfConfigAreaAuthInstanceId,
                  mgmtOspfConfigAreaAuthAreaId }
    ::= { mgmtOspfConfigAreaAuthTable 1 }

MGMTOspfConfigAreaAuthEntry ::= SEQUENCE {
    mgmtOspfConfigAreaAuthInstanceId    Integer32,
    mgmtOspfConfigAreaAuthAreaId        IpAddress,
    mgmtOspfConfigAreaAuthAreaAuthType  MGMTOspfAuthType,
    mgmtOspfConfigAreaAuthAction        MGMTRowEditorState
}

mgmtOspfConfigAreaAuthInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigAreaAuthEntry 1 }

mgmtOspfConfigAreaAuthAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigAreaAuthEntry 2 }

mgmtOspfConfigAreaAuthAreaAuthType OBJECT-TYPE
    SYNTAX      MGMTOspfAuthType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF authentication type."
    ::= { mgmtOspfConfigAreaAuthEntry 3 }

mgmtOspfConfigAreaAuthAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigAreaAuthEntry 100 }

mgmtOspfConfigAreaAuthTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspfConfig 8 }

mgmtOspfConfigAreaAuthTableRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigAreaAuthTableRowEditor 1 }

mgmtOspfConfigAreaAuthTableRowEditorAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigAreaAuthTableRowEditor 2 }

mgmtOspfConfigAreaAuthTableRowEditorAreaAuthType OBJECT-TYPE
    SYNTAX      MGMTOspfAuthType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF authentication type."
    ::= { mgmtOspfConfigAreaAuthTableRowEditor 3 }

mgmtOspfConfigAreaAuthTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigAreaAuthTableRowEditor 100 }

mgmtOspfConfigAreaRangeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigAreaRangeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF area range configuration table. It is used to summarize
         the intra area paths from a specific address range in one
         summary-LSA(Type-3) and advertised to other areas or configure the
         address range status as 'DoNotAdvertise' which the summary-LSA(Type-3)
         is suppressed.
         
         The area range configuration is used for Area Border Routers (ABRs) and
         only router-LSAs(Type-1) and network-LSAs (Type-2) can be summarized.
         The AS-external-LSAs(Type-5) cannot be summarized because the scope is
         OSPF autonomous system (AS). The AS-external-LSAs(Type-7) cannot be
         summarized because the feature is not supported yet."
    ::= { mgmtOspfConfig 9 }

mgmtOspfConfigAreaRangeEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigAreaRangeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF area range configuration. The
         overlap configuration of address range is not allowed in order to avoid
         the conflict."
    INDEX       { mgmtOspfConfigAreaRangeInstanceId,
                  mgmtOspfConfigAreaRangeAreaId,
                  mgmtOspfConfigAreaRangeNetwork,
                  mgmtOspfConfigAreaRangeIpSubnetMaskLength }
    ::= { mgmtOspfConfigAreaRangeTable 1 }

MGMTOspfConfigAreaRangeEntry ::= SEQUENCE {
    mgmtOspfConfigAreaRangeInstanceId          Integer32,
    mgmtOspfConfigAreaRangeAreaId              IpAddress,
    mgmtOspfConfigAreaRangeNetwork             IpAddress,
    mgmtOspfConfigAreaRangeIpSubnetMaskLength  Integer32,
    mgmtOspfConfigAreaRangeAdvertised          TruthValue,
    mgmtOspfConfigAreaRangeIsSpecificCost      TruthValue,
    mgmtOspfConfigAreaRangeCost                Unsigned32,
    mgmtOspfConfigAreaRangeAction              MGMTRowEditorState
}

mgmtOspfConfigAreaRangeInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigAreaRangeEntry 1 }

mgmtOspfConfigAreaRangeAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigAreaRangeEntry 2 }

mgmtOspfConfigAreaRangeNetwork OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtOspfConfigAreaRangeEntry 3 }

mgmtOspfConfigAreaRangeIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtOspfConfigAreaRangeEntry 4 }

mgmtOspfConfigAreaRangeAdvertised OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When the value is true, it summarizes intra area paths from the address
         range in one summary-LSA(Type-3) and advertised to other areas.
         
         Otherwise, the intra area paths from the address range are not
         advertised to other areas."
    ::= { mgmtOspfConfigAreaRangeEntry 5 }

mgmtOspfConfigAreaRangeIsSpecificCost OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the 'cost' argument is a specific configured value or not."
    ::= { mgmtOspfConfigAreaRangeEntry 6 }

mgmtOspfConfigAreaRangeCost OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified cost (or metric) for this summary route. The field is
         significant only when 'IsSpecificCost' is TRUE."
    ::= { mgmtOspfConfigAreaRangeEntry 7 }

mgmtOspfConfigAreaRangeAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigAreaRangeEntry 100 }

mgmtOspfConfigAreaRangeTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspfConfig 10 }

mgmtOspfConfigAreaRangeTableRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigAreaRangeTableRowEditor 1 }

mgmtOspfConfigAreaRangeTableRowEditorAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigAreaRangeTableRowEditor 2 }

mgmtOspfConfigAreaRangeTableRowEditorNetwork OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtOspfConfigAreaRangeTableRowEditor 3 }

mgmtOspfConfigAreaRangeTableRowEditorIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtOspfConfigAreaRangeTableRowEditor 4 }

mgmtOspfConfigAreaRangeTableRowEditorAdvertised OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When the value is true, it summarizes intra area paths from the address
         range in one summary-LSA(Type-3) and advertised to other areas.
         
         Otherwise, the intra area paths from the address range are not
         advertised to other areas."
    ::= { mgmtOspfConfigAreaRangeTableRowEditor 5 }

mgmtOspfConfigAreaRangeTableRowEditorIsSpecificCost OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the 'cost' argument is a specific configured value or not."
    ::= { mgmtOspfConfigAreaRangeTableRowEditor 6 }

mgmtOspfConfigAreaRangeTableRowEditorCost OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified cost (or metric) for this summary route. The field is
         significant only when 'IsSpecificCost' is TRUE."
    ::= { mgmtOspfConfigAreaRangeTableRowEditor 7 }

mgmtOspfConfigAreaRangeTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigAreaRangeTableRowEditor 100 }

mgmtOspfConfigInterfaceMdKeyTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigInterfaceMdKeyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is interface authentication message digest key configuration able."
    ::= { mgmtOspfConfig 11 }

mgmtOspfConfigInterfaceMdKeyEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigInterfaceMdKeyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of parameters."
    INDEX       { mgmtOspfConfigInterfaceMdKeyIfIndex,
                  mgmtOspfConfigInterfaceMdKeyMdKeyId }
    ::= { mgmtOspfConfigInterfaceMdKeyTable 1 }

MGMTOspfConfigInterfaceMdKeyEntry ::= SEQUENCE {
    mgmtOspfConfigInterfaceMdKeyIfIndex      MGMTInterfaceIndex,
    mgmtOspfConfigInterfaceMdKeyMdKeyId      MGMTUnsigned8,
    mgmtOspfConfigInterfaceMdKeyIsEncrypted  TruthValue,
    mgmtOspfConfigInterfaceMdKeyMdKey        MGMTDisplayString,
    mgmtOspfConfigInterfaceMdKeyAction       MGMTRowEditorState
}

mgmtOspfConfigInterfaceMdKeyIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspfConfigInterfaceMdKeyEntry 1 }

mgmtOspfConfigInterfaceMdKeyMdKeyId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The key ID for message digest authentication."
    ::= { mgmtOspfConfigInterfaceMdKeyEntry 2 }

mgmtOspfConfigInterfaceMdKeyIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates themessage digest key is encrypted or not. TRUE
         means the message digest key is encrypted. FALSE means the message
         digest key is plain text."
    ::= { mgmtOspfConfigInterfaceMdKeyEntry 3 }

mgmtOspfConfigInterfaceMdKeyMdKey OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The message digest key."
    ::= { mgmtOspfConfigInterfaceMdKeyEntry 4 }

mgmtOspfConfigInterfaceMdKeyAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigInterfaceMdKeyEntry 100 }

mgmtOspfConfigInterfaceMdKeyTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspfConfig 12 }

mgmtOspfConfigInterfaceMdKeyTableRowEditorIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspfConfigInterfaceMdKeyTableRowEditor 1 }

mgmtOspfConfigInterfaceMdKeyTableRowEditorMdKeyId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The key ID for message digest authentication."
    ::= { mgmtOspfConfigInterfaceMdKeyTableRowEditor 2 }

mgmtOspfConfigInterfaceMdKeyTableRowEditorIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates themessage digest key is encrypted or not. TRUE
         means the message digest key is encrypted. FALSE means the message
         digest key is plain text."
    ::= { mgmtOspfConfigInterfaceMdKeyTableRowEditor 3 }

mgmtOspfConfigInterfaceMdKeyTableRowEditorMdKey OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The message digest key."
    ::= { mgmtOspfConfigInterfaceMdKeyTableRowEditor 4 }

mgmtOspfConfigInterfaceMdKeyTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigInterfaceMdKeyTableRowEditor 100 }

mgmtOspfConfigVlinkTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigVlinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF virtual link configuration table. The virtual link is
         established between 2 ABRs to overcome that all the areas have to be
         connected directly to the backbone area."
    ::= { mgmtOspfConfig 13 }

mgmtOspfConfigVlinkEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigVlinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF virtual link configuration."
    INDEX       { mgmtOspfConfigVlinkInstanceId,
                  mgmtOspfConfigVlinkAreaId,
                  mgmtOspfConfigVlinkRouterId }
    ::= { mgmtOspfConfigVlinkTable 1 }

MGMTOspfConfigVlinkEntry ::= SEQUENCE {
    mgmtOspfConfigVlinkInstanceId          Integer32,
    mgmtOspfConfigVlinkAreaId              IpAddress,
    mgmtOspfConfigVlinkRouterId            IpAddress,
    mgmtOspfConfigVlinkHelloInterval       Unsigned32,
    mgmtOspfConfigVlinkDeadInterval        Unsigned32,
    mgmtOspfConfigVlinkRetransmitInterval  Unsigned32,
    mgmtOspfConfigVlinkAuthType            MGMTOspfAuthType,
    mgmtOspfConfigVlinkIsEncrypted         TruthValue,
    mgmtOspfConfigVlinkSimplePwd           MGMTDisplayString,
    mgmtOspfConfigVlinkAction              MGMTRowEditorState
}

mgmtOspfConfigVlinkInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigVlinkEntry 1 }

mgmtOspfConfigVlinkAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigVlinkEntry 2 }

mgmtOspfConfigVlinkRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF router ID."
    ::= { mgmtOspfConfigVlinkEntry 3 }

mgmtOspfConfigVlinkHelloInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time interval (in seconds) between hello packets."
    ::= { mgmtOspfConfigVlinkEntry 4 }

mgmtOspfConfigVlinkDeadInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of seconds to wait until the neighbor is declared to be
         dead."
    ::= { mgmtOspfConfigVlinkEntry 5 }

mgmtOspfConfigVlinkRetransmitInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time interval (in seconds) between link-state advertisement(LSA)
         retransmissions for adjacencies."
    ::= { mgmtOspfConfigVlinkEntry 6 }

mgmtOspfConfigVlinkAuthType OBJECT-TYPE
    SYNTAX      MGMTOspfAuthType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The authentication type on an area."
    ::= { mgmtOspfConfigVlinkEntry 7 }

mgmtOspfConfigVlinkIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the simple password is encrypted or not. TRUE means
         the simple password is encrypted. FALSE means the simple password is
         plain text."
    ::= { mgmtOspfConfigVlinkEntry 8 }

mgmtOspfConfigVlinkSimplePwd OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The simple password."
    ::= { mgmtOspfConfigVlinkEntry 9 }

mgmtOspfConfigVlinkAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigVlinkEntry 100 }

mgmtOspfConfigVlinkTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspfConfig 14 }

mgmtOspfConfigVlinkTableRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigVlinkTableRowEditor 1 }

mgmtOspfConfigVlinkTableRowEditorAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigVlinkTableRowEditor 2 }

mgmtOspfConfigVlinkTableRowEditorRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF router ID."
    ::= { mgmtOspfConfigVlinkTableRowEditor 3 }

mgmtOspfConfigVlinkTableRowEditorHelloInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time interval (in seconds) between hello packets."
    ::= { mgmtOspfConfigVlinkTableRowEditor 4 }

mgmtOspfConfigVlinkTableRowEditorDeadInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of seconds to wait until the neighbor is declared to be
         dead."
    ::= { mgmtOspfConfigVlinkTableRowEditor 5 }

mgmtOspfConfigVlinkTableRowEditorRetransmitInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time interval (in seconds) between link-state advertisement(LSA)
         retransmissions for adjacencies."
    ::= { mgmtOspfConfigVlinkTableRowEditor 6 }

mgmtOspfConfigVlinkTableRowEditorAuthType OBJECT-TYPE
    SYNTAX      MGMTOspfAuthType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The authentication type on an area."
    ::= { mgmtOspfConfigVlinkTableRowEditor 7 }

mgmtOspfConfigVlinkTableRowEditorIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the simple password is encrypted or not. TRUE means
         the simple password is encrypted. FALSE means the simple password is
         plain text."
    ::= { mgmtOspfConfigVlinkTableRowEditor 8 }

mgmtOspfConfigVlinkTableRowEditorSimplePwd OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The simple password."
    ::= { mgmtOspfConfigVlinkTableRowEditor 9 }

mgmtOspfConfigVlinkTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigVlinkTableRowEditor 100 }

mgmtOspfConfigVlinkMdKeyTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigVlinkMdKeyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is virtual link authentication message digest key configuration
         able."
    ::= { mgmtOspfConfig 15 }

mgmtOspfConfigVlinkMdKeyEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigVlinkMdKeyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each virtual link entry has a set of parameters."
    INDEX       { mgmtOspfConfigVlinkMdKeyInstanceId,
                  mgmtOspfConfigVlinkMdKeyAreaId,
                  mgmtOspfConfigVlinkMdKeyRouterId,
                  mgmtOspfConfigVlinkMdKeyMdKeyId }
    ::= { mgmtOspfConfigVlinkMdKeyTable 1 }

MGMTOspfConfigVlinkMdKeyEntry ::= SEQUENCE {
    mgmtOspfConfigVlinkMdKeyInstanceId   Integer32,
    mgmtOspfConfigVlinkMdKeyAreaId       IpAddress,
    mgmtOspfConfigVlinkMdKeyRouterId     IpAddress,
    mgmtOspfConfigVlinkMdKeyMdKeyId      MGMTUnsigned8,
    mgmtOspfConfigVlinkMdKeyIsEncrypted  TruthValue,
    mgmtOspfConfigVlinkMdKeyMdKey        MGMTDisplayString,
    mgmtOspfConfigVlinkMdKeyAction       MGMTRowEditorState
}

mgmtOspfConfigVlinkMdKeyInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigVlinkMdKeyEntry 1 }

mgmtOspfConfigVlinkMdKeyAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigVlinkMdKeyEntry 2 }

mgmtOspfConfigVlinkMdKeyRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF router ID."
    ::= { mgmtOspfConfigVlinkMdKeyEntry 3 }

mgmtOspfConfigVlinkMdKeyMdKeyId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The key ID for message digest authentication."
    ::= { mgmtOspfConfigVlinkMdKeyEntry 4 }

mgmtOspfConfigVlinkMdKeyIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates themessage digest key is encrypted or not. TRUE
         means the message digest key is encrypted. FALSE means the message
         digest key is plain text."
    ::= { mgmtOspfConfigVlinkMdKeyEntry 5 }

mgmtOspfConfigVlinkMdKeyMdKey OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The message digest key."
    ::= { mgmtOspfConfigVlinkMdKeyEntry 6 }

mgmtOspfConfigVlinkMdKeyAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigVlinkMdKeyEntry 100 }

mgmtOspfConfigVlinkTableMdKeyRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspfConfig 16 }

mgmtOspfConfigVlinkTableMdKeyRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigVlinkTableMdKeyRowEditor 1 }

mgmtOspfConfigVlinkTableMdKeyRowEditorAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigVlinkTableMdKeyRowEditor 2 }

mgmtOspfConfigVlinkTableMdKeyRowEditorRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF router ID."
    ::= { mgmtOspfConfigVlinkTableMdKeyRowEditor 3 }

mgmtOspfConfigVlinkTableMdKeyRowEditorMdKeyId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The key ID for message digest authentication."
    ::= { mgmtOspfConfigVlinkTableMdKeyRowEditor 4 }

mgmtOspfConfigVlinkTableMdKeyRowEditorIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates themessage digest key is encrypted or not. TRUE
         means the message digest key is encrypted. FALSE means the message
         digest key is plain text."
    ::= { mgmtOspfConfigVlinkTableMdKeyRowEditor 5 }

mgmtOspfConfigVlinkTableMdKeyRowEditorMdKey OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The message digest key."
    ::= { mgmtOspfConfigVlinkTableMdKeyRowEditor 6 }

mgmtOspfConfigVlinkTableMdKeyRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigVlinkTableMdKeyRowEditor 100 }

mgmtOspfConfigStubAreaTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigStubAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF stub area configuration table. The configuration is used
         to reduce the link-state database size and therefore the memory and CPU
         requirement by forbidding some LSAs."
    ::= { mgmtOspfConfig 17 }

mgmtOspfConfigStubAreaEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigStubAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF stub area configuration."
    INDEX       { mgmtOspfConfigStubAreaInstanceId,
                  mgmtOspfConfigStubAreaAreaId }
    ::= { mgmtOspfConfigStubAreaTable 1 }

MGMTOspfConfigStubAreaEntry ::= SEQUENCE {
    mgmtOspfConfigStubAreaInstanceId          Integer32,
    mgmtOspfConfigStubAreaAreaId              IpAddress,
    mgmtOspfConfigStubAreaIsNssa              TruthValue,
    mgmtOspfConfigStubAreaNoSummary           TruthValue,
    mgmtOspfConfigStubAreaNssaTranslatorRole  MGMTOspfNssaTranslatorRole,
    mgmtOspfConfigStubAreaAction              MGMTRowEditorState
}

mgmtOspfConfigStubAreaInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigStubAreaEntry 1 }

mgmtOspfConfigStubAreaAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigStubAreaEntry 2 }

mgmtOspfConfigStubAreaIsNssa OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF stub configured type."
    ::= { mgmtOspfConfigStubAreaEntry 3 }

mgmtOspfConfigStubAreaNoSummary OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value is true to configure the inter-area routes do not inject into
         this stub area."
    ::= { mgmtOspfConfigStubAreaEntry 4 }

mgmtOspfConfigStubAreaNssaTranslatorRole OBJECT-TYPE
    SYNTAX      MGMTOspfNssaTranslatorRole
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF NSSA translator role."
    ::= { mgmtOspfConfigStubAreaEntry 5 }

mgmtOspfConfigStubAreaAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigStubAreaEntry 100 }

mgmtOspfConfigStubAreaTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspfConfig 18 }

mgmtOspfConfigStubAreaTableRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfConfigStubAreaTableRowEditor 1 }

mgmtOspfConfigStubAreaTableRowEditorAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfConfigStubAreaTableRowEditor 2 }

mgmtOspfConfigStubAreaTableRowEditorIsNssa OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF stub configured type."
    ::= { mgmtOspfConfigStubAreaTableRowEditor 3 }

mgmtOspfConfigStubAreaTableRowEditorNoSummary OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value is true to configure the inter-area routes do not inject into
         this stub area."
    ::= { mgmtOspfConfigStubAreaTableRowEditor 4 }

mgmtOspfConfigStubAreaTableRowEditorNssaTranslatorRole OBJECT-TYPE
    SYNTAX      MGMTOspfNssaTranslatorRole
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF NSSA translator role."
    ::= { mgmtOspfConfigStubAreaTableRowEditor 5 }

mgmtOspfConfigStubAreaTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspfConfigStubAreaTableRowEditor 100 }

mgmtOspfConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is interface configuration parameter table."
    ::= { mgmtOspfConfig 19 }

mgmtOspfConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTOspfConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of parameters."
    INDEX       { mgmtOspfConfigInterfaceIfIndex }
    ::= { mgmtOspfConfigInterfaceTable 1 }

MGMTOspfConfigInterfaceEntry ::= SEQUENCE {
    mgmtOspfConfigInterfaceIfIndex             MGMTInterfaceIndex,
    mgmtOspfConfigInterfacePriority            Unsigned32,
    mgmtOspfConfigInterfaceIsSpecificCost      TruthValue,
    mgmtOspfConfigInterfaceCost                Unsigned32,
    mgmtOspfConfigInterfaceIsFastHelloEnabled  TruthValue,
    mgmtOspfConfigInterfaceFastHelloPackets    Unsigned32,
    mgmtOspfConfigInterfaceDeadInterval        Unsigned32,
    mgmtOspfConfigInterfaceHelloInterval       Unsigned32,
    mgmtOspfConfigInterfaceRetransmitInterval  Unsigned32,
    mgmtOspfConfigInterfaceAuthType            MGMTOspfAuthType,
    mgmtOspfConfigInterfaceIsEncrypted         TruthValue,
    mgmtOspfConfigInterfaceSimplePwd           MGMTDisplayString
}

mgmtOspfConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspfConfigInterfaceEntry 1 }

mgmtOspfConfigInterfacePriority OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified router priority for the interface."
    ::= { mgmtOspfConfigInterfaceEntry 2 }

mgmtOspfConfigInterfaceIsSpecificCost OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the 'cost' argument is a specific configured value or not."
    ::= { mgmtOspfConfigInterfaceEntry 3 }

mgmtOspfConfigInterfaceCost OBJECT-TYPE
    SYNTAX      Unsigned32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified cost for this interface. It's link state metric for the
         interface. The field is significant only when 'IsSpecificCost' is TRUE."
    ::= { mgmtOspfConfigInterfaceEntry 4 }

mgmtOspfConfigInterfaceIsFastHelloEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable the feature of fast hello packets or not."
    ::= { mgmtOspfConfigInterfaceEntry 5 }

mgmtOspfConfigInterfaceFastHelloPackets OBJECT-TYPE
    SYNTAX      Unsigned32 (1..10)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "How many Hello packets will be sent per second."
    ::= { mgmtOspfConfigInterfaceEntry 6 }

mgmtOspfConfigInterfaceDeadInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time interval (in seconds) between hello packets."
    ::= { mgmtOspfConfigInterfaceEntry 7 }

mgmtOspfConfigInterfaceHelloInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "How many Hello packets will be sent per second."
    ::= { mgmtOspfConfigInterfaceEntry 8 }

mgmtOspfConfigInterfaceRetransmitInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (3..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time interval (in seconds) between link-state advertisement(LSA)
         retransmissions for adjacencies."
    ::= { mgmtOspfConfigInterfaceEntry 9 }

mgmtOspfConfigInterfaceAuthType OBJECT-TYPE
    SYNTAX      MGMTOspfAuthType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The authentication type."
    ::= { mgmtOspfConfigInterfaceEntry 10 }

mgmtOspfConfigInterfaceIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the simple password is encrypted or not. TRUE means
         the simple password is encrypted. FALSE means the simple password is
         plain text."
    ::= { mgmtOspfConfigInterfaceEntry 11 }

mgmtOspfConfigInterfaceSimplePwd OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..128))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The simple password."
    ::= { mgmtOspfConfigInterfaceEntry 12 }

mgmtOspfConfigInterface OBJECT IDENTIFIER
    ::= { mgmtOspfConfig 100 }

mgmtOspfStatus OBJECT IDENTIFIER
    ::= { mgmtOspfMibObjects 3 }

mgmtOspfStatusRouterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF router status table. It is used to provide the OSPF router
         status information."
    ::= { mgmtOspfStatus 1 }

mgmtOspfStatusRouterEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF router status information."
    INDEX       { mgmtOspfStatusRouterInstanceId }
    ::= { mgmtOspfStatusRouterTable 1 }

MGMTOspfStatusRouterEntry ::= SEQUENCE {
    mgmtOspfStatusRouterInstanceId           Integer32,
    mgmtOspfStatusRouterRouterId             IpAddress,
    mgmtOspfStatusRouterSpfDelay             Unsigned32,
    mgmtOspfStatusRouterSpfHoldTime          Unsigned32,
    mgmtOspfStatusRouterSpfMaxWaitTime       Unsigned32,
    mgmtOspfStatusRouterLastExcutedSpfTs     Counter64,
    mgmtOspfStatusRouterMinLsaInterval       Unsigned32,
    mgmtOspfStatusRouterMinLsaArrival        Unsigned32,
    mgmtOspfStatusRouterExternalLsaCount     Unsigned32,
    mgmtOspfStatusRouterExternalLsaChecksum  Unsigned32,
    mgmtOspfStatusRouterAttachedAreaCount    Unsigned32
}

mgmtOspfStatusRouterInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusRouterEntry 1 }

mgmtOspfStatusRouterRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "OSPF router ID"
    ::= { mgmtOspfStatusRouterEntry 2 }

mgmtOspfStatusRouterSpfDelay OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Delay time (in seconds)of SPF calculations."
    ::= { mgmtOspfStatusRouterEntry 3 }

mgmtOspfStatusRouterSpfHoldTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum hold time (in milliseconds) between consecutive SPF
         calculations."
    ::= { mgmtOspfStatusRouterEntry 4 }

mgmtOspfStatusRouterSpfMaxWaitTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum wait time (in milliseconds) between consecutive SPF
         calculations."
    ::= { mgmtOspfStatusRouterEntry 5 }

mgmtOspfStatusRouterLastExcutedSpfTs OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time (in milliseconds) that has passed between the start of the SPF
         algorithm execution and the current time."
    ::= { mgmtOspfStatusRouterEntry 6 }

mgmtOspfStatusRouterMinLsaInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum interval (in seconds) between link-state advertisements."
    ::= { mgmtOspfStatusRouterEntry 7 }

mgmtOspfStatusRouterMinLsaArrival OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum arrival time (in milliseconds) of link-state advertisements."
    ::= { mgmtOspfStatusRouterEntry 8 }

mgmtOspfStatusRouterExternalLsaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of external link-state advertisements."
    ::= { mgmtOspfStatusRouterEntry 9 }

mgmtOspfStatusRouterExternalLsaChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of external link-state checksum."
    ::= { mgmtOspfStatusRouterEntry 10 }

mgmtOspfStatusRouterAttachedAreaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of areas attached for the router."
    ::= { mgmtOspfStatusRouterEntry 11 }

mgmtOspfStatusRouteAreaTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusRouteAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF network area status table. It is used to provide the OSPF
         network area status information."
    ::= { mgmtOspfStatus 2 }

mgmtOspfStatusRouteAreaEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusRouteAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF network area status
         information."
    INDEX       { mgmtOspfStatusRouteAreaInstanceId,
                  mgmtOspfStatusRouteAreaAreaId }
    ::= { mgmtOspfStatusRouteAreaTable 1 }

MGMTOspfStatusRouteAreaEntry ::= SEQUENCE {
    mgmtOspfStatusRouteAreaInstanceId               Integer32,
    mgmtOspfStatusRouteAreaAreaId                   IpAddress,
    mgmtOspfStatusRouteAreaIsBackbone               TruthValue,
    mgmtOspfStatusRouteAreaAreaType                 MGMTOspfAreaType,
    mgmtOspfStatusRouteAreaAttachedIntfActiveCount  Unsigned32,
    mgmtOspfStatusRouteAreaAuthType                 MGMTOspfAuthType,
    mgmtOspfStatusRouteAreaSpfExecutedCount         Unsigned32,
    mgmtOspfStatusRouteAreaLsaCount                 Unsigned32,
    mgmtOspfStatusRouteAreaRouterLsaCount           Unsigned32,
    mgmtOspfStatusRouteAreaRouterLsaChecksum        Unsigned32,
    mgmtOspfStatusRouteAreaNetworkLsaCount          Unsigned32,
    mgmtOspfStatusRouteAreaNetworkLsaChecksum       Unsigned32,
    mgmtOspfStatusRouteAreaSummaryLsaCount          Unsigned32,
    mgmtOspfStatusRouteAreaSummaryLsaChecksum       Unsigned32,
    mgmtOspfStatusRouteAreaAsbrSummaryLsaCount      Unsigned32,
    mgmtOspfStatusRouteAreaAsbrSummaryLsaChecksum   Unsigned32,
    mgmtOspfStatusRouteAreaNssaLsaCount             Unsigned32,
    mgmtOspfStatusRouteAreaNssaLsaChecksum          Unsigned32,
    mgmtOspfStatusRouteAreaNssaTranslatorState      MGMTOspfNssaTranslatorState
}

mgmtOspfStatusRouteAreaInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusRouteAreaEntry 1 }

mgmtOspfStatusRouteAreaAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfStatusRouteAreaEntry 2 }

mgmtOspfStatusRouteAreaIsBackbone OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if it's backbone area or not."
    ::= { mgmtOspfStatusRouteAreaEntry 3 }

mgmtOspfStatusRouteAreaAreaType OBJECT-TYPE
    SYNTAX      MGMTOspfAreaType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The area type."
    ::= { mgmtOspfStatusRouteAreaEntry 4 }

mgmtOspfStatusRouteAreaAttachedIntfActiveCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of active interfaces attached in the area."
    ::= { mgmtOspfStatusRouteAreaEntry 5 }

mgmtOspfStatusRouteAreaAuthType OBJECT-TYPE
    SYNTAX      MGMTOspfAuthType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The authentication type in the area."
    ::= { mgmtOspfStatusRouteAreaEntry 6 }

mgmtOspfStatusRouteAreaSpfExecutedCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of times SPF algorithm has been executed for the particular
         area."
    ::= { mgmtOspfStatusRouteAreaEntry 7 }

mgmtOspfStatusRouteAreaLsaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of the total LSAs for the particular area."
    ::= { mgmtOspfStatusRouteAreaEntry 8 }

mgmtOspfStatusRouteAreaRouterLsaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of the router-LSAs(Type-1) of a given type for the particular
         area."
    ::= { mgmtOspfStatusRouteAreaEntry 9 }

mgmtOspfStatusRouteAreaRouterLsaChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The the router-LSAs(Type-1) checksum."
    ::= { mgmtOspfStatusRouteAreaEntry 10 }

mgmtOspfStatusRouteAreaNetworkLsaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of the network-LSAs(Type-2) of a given type for the particular
         area."
    ::= { mgmtOspfStatusRouteAreaEntry 11 }

mgmtOspfStatusRouteAreaNetworkLsaChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The the network-LSAs(Type-2) checksum."
    ::= { mgmtOspfStatusRouteAreaEntry 12 }

mgmtOspfStatusRouteAreaSummaryLsaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of the summary-LSAs(Type-3) of a given type for the particular
         area."
    ::= { mgmtOspfStatusRouteAreaEntry 13 }

mgmtOspfStatusRouteAreaSummaryLsaChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The the summary-LSAs(Type-3) checksum."
    ::= { mgmtOspfStatusRouteAreaEntry 14 }

mgmtOspfStatusRouteAreaAsbrSummaryLsaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of the ASBR-summary-LSAs(Type-4) of a given type for the
         particular area."
    ::= { mgmtOspfStatusRouteAreaEntry 15 }

mgmtOspfStatusRouteAreaAsbrSummaryLsaChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The the ASBR-summary-LSAs(Type-4) checksum."
    ::= { mgmtOspfStatusRouteAreaEntry 16 }

mgmtOspfStatusRouteAreaNssaLsaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of the NSSA LSAs of a given type for the particular area."
    ::= { mgmtOspfStatusRouteAreaEntry 17 }

mgmtOspfStatusRouteAreaNssaLsaChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The the NSSA LSAs checksum."
    ::= { mgmtOspfStatusRouteAreaEntry 18 }

mgmtOspfStatusRouteAreaNssaTranslatorState OBJECT-TYPE
    SYNTAX      MGMTOspfNssaTranslatorState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate the current state of the NSSA-ABR translator which the router
         uses to translate Type-7 LSAs in the NSSA to Type-5 LSAs in backbone
         area."
    ::= { mgmtOspfStatusRouteAreaEntry 19 }

mgmtOspfStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF interface status table. It is used to provide the OSPF
         interface status information."
    ::= { mgmtOspfStatus 5 }

mgmtOspfStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF interface status information."
    INDEX       { mgmtOspfStatusInterfaceIfIndex }
    ::= { mgmtOspfStatusInterfaceTable 1 }

MGMTOspfStatusInterfaceEntry ::= SEQUENCE {
    mgmtOspfStatusInterfaceIfIndex             MGMTInterfaceIndex,
    mgmtOspfStatusInterfaceStatus              TruthValue,
    mgmtOspfStatusInterfacePassive             TruthValue,
    mgmtOspfStatusInterfaceNetwork             IpAddress,
    mgmtOspfStatusInterfaceIpSubnetMaskLength  Integer32,
    mgmtOspfStatusInterfaceAreaId              IpAddress,
    mgmtOspfStatusInterfaceRouterId            IpAddress,
    mgmtOspfStatusInterfaceCost                Integer32,
    mgmtOspfStatusInterfaceState               MGMTOspfInterfaceState,
    mgmtOspfStatusInterfacePriority            Integer32,
    mgmtOspfStatusInterfaceDrId                IpAddress,
    mgmtOspfStatusInterfaceDrAddr              IpAddress,
    mgmtOspfStatusInterfaceBdrId               IpAddress,
    mgmtOspfStatusInterfaceBdrAddr             IpAddress,
    mgmtOspfStatusInterfaceHelloTime           Unsigned32,
    mgmtOspfStatusInterfaceDeadTime            Unsigned32,
    mgmtOspfStatusInterfaceWaitTime            Unsigned32,
    mgmtOspfStatusInterfaceRetransmitTime      Unsigned32,
    mgmtOspfStatusInterfaceHelloDueTime        Unsigned32,
    mgmtOspfStatusInterfaceNeighborCount       Unsigned32,
    mgmtOspfStatusInterfaceAdjNeighborCount    Unsigned32,
    mgmtOspfStatusInterfaceTransmitDelay       Unsigned32
}

mgmtOspfStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspfStatusInterfaceEntry 1 }

mgmtOspfStatusInterfaceStatus OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It's used to indicate if the interface is up or down."
    ::= { mgmtOspfStatusInterfaceEntry 2 }

mgmtOspfStatusInterfacePassive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if the interface is passive interface."
    ::= { mgmtOspfStatusInterfaceEntry 3 }

mgmtOspfStatusInterfaceNetwork OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtOspfStatusInterfaceEntry 4 }

mgmtOspfStatusInterfaceIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtOspfStatusInterfaceEntry 5 }

mgmtOspfStatusInterfaceAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfStatusInterfaceEntry 6 }

mgmtOspfStatusInterfaceRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF router ID."
    ::= { mgmtOspfStatusInterfaceEntry 7 }

mgmtOspfStatusInterfaceCost OBJECT-TYPE
    SYNTAX      Integer32 (0..********)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The cost of the interface."
    ::= { mgmtOspfStatusInterfaceEntry 8 }

mgmtOspfStatusInterfaceState OBJECT-TYPE
    SYNTAX      MGMTOspfInterfaceState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The state of the link."
    ::= { mgmtOspfStatusInterfaceEntry 9 }

mgmtOspfStatusInterfacePriority OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF priority. It helps determine the DR and BDR on the network to
         which this interface is connected."
    ::= { mgmtOspfStatusInterfaceEntry 10 }

mgmtOspfStatusInterfaceDrId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The router ID of DR."
    ::= { mgmtOspfStatusInterfaceEntry 11 }

mgmtOspfStatusInterfaceDrAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP address of DR."
    ::= { mgmtOspfStatusInterfaceEntry 12 }

mgmtOspfStatusInterfaceBdrId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The router ID of BDR."
    ::= { mgmtOspfStatusInterfaceEntry 13 }

mgmtOspfStatusInterfaceBdrAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP address of BDR."
    ::= { mgmtOspfStatusInterfaceEntry 14 }

mgmtOspfStatusInterfaceHelloTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Hello timer. A time interval that a router sends an OSPF hello packet."
    ::= { mgmtOspfStatusInterfaceEntry 15 }

mgmtOspfStatusInterfaceDeadTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Dead timer. Dead timer is a time interval to wait before declaring a
         neighbor dead. The unit of time is the second."
    ::= { mgmtOspfStatusInterfaceEntry 16 }

mgmtOspfStatusInterfaceWaitTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This interval is used in Wait Timer. Wait timer is a single shot timer
         that causes the interface to exit waiting and select a DR on the
         network. Wait Time interval is the same as Dead time interval."
    ::= { mgmtOspfStatusInterfaceEntry 17 }

mgmtOspfStatusInterfaceRetransmitTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Retransmit timer. A time interval to wait before retransmitting a
         database description packet when it has not been acknowledged."
    ::= { mgmtOspfStatusInterfaceEntry 18 }

mgmtOspfStatusInterfaceHelloDueTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Hello due timer. An OSPF hello packet will be sent on this interface
         after this due time."
    ::= { mgmtOspfStatusInterfaceEntry 19 }

mgmtOspfStatusInterfaceNeighborCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Neighbor count. This is the number of OSPF neighbors discovered on this
         interface."
    ::= { mgmtOspfStatusInterfaceEntry 20 }

mgmtOspfStatusInterfaceAdjNeighborCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Adjacent neighbor count. This is the number of routers running OSPF
         that are fully adjacent with this router."
    ::= { mgmtOspfStatusInterfaceEntry 21 }

mgmtOspfStatusInterfaceTransmitDelay OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The estimated time to transmit a link-state update packet on the
         interface."
    ::= { mgmtOspfStatusInterfaceEntry 22 }

mgmtOspfStatusNeighborIpv4Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusNeighborIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF IPv4 neighbor status table. It is used to provide the OSPF
         neighbor status information."
    ::= { mgmtOspfStatus 6 }

mgmtOspfStatusNeighborIpv4Entry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusNeighborIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF IPv4 neighbor status
         information."
    INDEX       { mgmtOspfStatusNeighborIpv4InstanceId,
                  mgmtOspfStatusNeighborIpv4RouterId,
                  mgmtOspfStatusNeighborIpv4Ipv4Addr,
                  mgmtOspfStatusNeighborIpv4IfIndex }
    ::= { mgmtOspfStatusNeighborIpv4Table 1 }

MGMTOspfStatusNeighborIpv4Entry ::= SEQUENCE {
    mgmtOspfStatusNeighborIpv4InstanceId     Integer32,
    mgmtOspfStatusNeighborIpv4RouterId       IpAddress,
    mgmtOspfStatusNeighborIpv4Ipv4Addr       IpAddress,
    mgmtOspfStatusNeighborIpv4IfIndex        MGMTInterfaceIndex,
    mgmtOspfStatusNeighborIpv4IpAddr         IpAddress,
    mgmtOspfStatusNeighborIpv4AreaId         IpAddress,
    mgmtOspfStatusNeighborIpv4Priority       Integer32,
    mgmtOspfStatusNeighborIpv4State          MGMTOspfNeighborState,
    mgmtOspfStatusNeighborIpv4DrId           IpAddress,
    mgmtOspfStatusNeighborIpv4DrAddr         IpAddress,
    mgmtOspfStatusNeighborIpv4BdrId          IpAddress,
    mgmtOspfStatusNeighborIpv4BdrAddr        IpAddress,
    mgmtOspfStatusNeighborIpv4Options        MGMTUnsigned8,
    mgmtOspfStatusNeighborIpv4DeadTime       Unsigned32,
    mgmtOspfStatusNeighborIpv4TransitAreaId  IpAddress
}

mgmtOspfStatusNeighborIpv4InstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusNeighborIpv4Entry 1 }

mgmtOspfStatusNeighborIpv4RouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF router ID."
    ::= { mgmtOspfStatusNeighborIpv4Entry 2 }

mgmtOspfStatusNeighborIpv4Ipv4Addr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The IPv4 address."
    ::= { mgmtOspfStatusNeighborIpv4Entry 3 }

mgmtOspfStatusNeighborIpv4IfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspfStatusNeighborIpv4Entry 4 }

mgmtOspfStatusNeighborIpv4IpAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP address."
    ::= { mgmtOspfStatusNeighborIpv4Entry 5 }

mgmtOspfStatusNeighborIpv4AreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfStatusNeighborIpv4Entry 6 }

mgmtOspfStatusNeighborIpv4Priority OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The priority of OSPF neighbor. It indicates the priority of the
         neighbor router. This item is used when selecting the DR for the
         network. The router with the highest priority becomes the DR."
    ::= { mgmtOspfStatusNeighborIpv4Entry 7 }

mgmtOspfStatusNeighborIpv4State OBJECT-TYPE
    SYNTAX      MGMTOspfNeighborState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The state of OSPF neighbor. It indicates the functional state of the
         neighbor router."
    ::= { mgmtOspfStatusNeighborIpv4Entry 8 }

mgmtOspfStatusNeighborIpv4DrId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The router ID of DR."
    ::= { mgmtOspfStatusNeighborIpv4Entry 9 }

mgmtOspfStatusNeighborIpv4DrAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP address of DR."
    ::= { mgmtOspfStatusNeighborIpv4Entry 10 }

mgmtOspfStatusNeighborIpv4BdrId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The router ID of BDR."
    ::= { mgmtOspfStatusNeighborIpv4Entry 11 }

mgmtOspfStatusNeighborIpv4BdrAddr OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP address of BDR."
    ::= { mgmtOspfStatusNeighborIpv4Entry 12 }

mgmtOspfStatusNeighborIpv4Options OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF option field which is present in OSPF hello packets, which
         enables OSPF routers to support (or not support) optional capabilities,
         and to communicate their capability level to other OSPF routers."
    ::= { mgmtOspfStatusNeighborIpv4Entry 13 }

mgmtOspfStatusNeighborIpv4DeadTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Dead timer. It indicates the amount of time remaining that the router
         waits to receive an OSPF hello packet from the neighbor before
         declaring the neighbor down."
    ::= { mgmtOspfStatusNeighborIpv4Entry 14 }

mgmtOspfStatusNeighborIpv4TransitAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF transit area ID for the neighbor on virtual link interface."
    ::= { mgmtOspfStatusNeighborIpv4Entry 15 }

mgmtOspfStatusInterfaceMdKeyPrecedenceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusInterfaceMdKeyPrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is virtual link authentication message digest key
         precedenceconfiguration able."
    ::= { mgmtOspfStatus 8 }

mgmtOspfStatusInterfaceMdKeyPrecedenceEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusInterfaceMdKeyPrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding message digest key ID."
    INDEX       { mgmtOspfStatusInterfaceMdKeyPrecedenceIfIndex,
                  mgmtOspfStatusInterfaceMdKeyPrecedencePrecedence }
    ::= { mgmtOspfStatusInterfaceMdKeyPrecedenceTable 1 }

MGMTOspfStatusInterfaceMdKeyPrecedenceEntry ::= SEQUENCE {
    mgmtOspfStatusInterfaceMdKeyPrecedenceIfIndex     MGMTInterfaceIndex,
    mgmtOspfStatusInterfaceMdKeyPrecedencePrecedence  Unsigned32,
    mgmtOspfStatusInterfaceMdKeyPrecedenceMdKeyId     MGMTUnsigned8
}

mgmtOspfStatusInterfaceMdKeyPrecedenceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspfStatusInterfaceMdKeyPrecedenceEntry 1 }

mgmtOspfStatusInterfaceMdKeyPrecedencePrecedence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The precedence of message digest key."
    ::= { mgmtOspfStatusInterfaceMdKeyPrecedenceEntry 2 }

mgmtOspfStatusInterfaceMdKeyPrecedenceMdKeyId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The key ID for message digest authentication."
    ::= { mgmtOspfStatusInterfaceMdKeyPrecedenceEntry 3 }

mgmtOspfStatusVlinkMdkeyPrecedenceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusVlinkMdkeyPrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is virtual link authentication message digest key
         precedenceconfiguration able."
    ::= { mgmtOspfStatus 9 }

mgmtOspfStatusVlinkMdkeyPrecedenceEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusVlinkMdkeyPrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding message digest key ID."
    INDEX       { mgmtOspfStatusVlinkMdkeyPrecedenceInstanceId,
                  mgmtOspfStatusVlinkMdkeyPrecedenceAreaId,
                  mgmtOspfStatusVlinkMdkeyPrecedenceRouterId,
                  mgmtOspfStatusVlinkMdkeyPrecedencePrecedence }
    ::= { mgmtOspfStatusVlinkMdkeyPrecedenceTable 1 }

MGMTOspfStatusVlinkMdkeyPrecedenceEntry ::= SEQUENCE {
    mgmtOspfStatusVlinkMdkeyPrecedenceInstanceId  Integer32,
    mgmtOspfStatusVlinkMdkeyPrecedenceAreaId      IpAddress,
    mgmtOspfStatusVlinkMdkeyPrecedenceRouterId    IpAddress,
    mgmtOspfStatusVlinkMdkeyPrecedencePrecedence  Unsigned32,
    mgmtOspfStatusVlinkMdkeyPrecedenceMdKeyId     MGMTUnsigned8
}

mgmtOspfStatusVlinkMdkeyPrecedenceInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusVlinkMdkeyPrecedenceEntry 1 }

mgmtOspfStatusVlinkMdkeyPrecedenceAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfStatusVlinkMdkeyPrecedenceEntry 2 }

mgmtOspfStatusVlinkMdkeyPrecedenceRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF router ID."
    ::= { mgmtOspfStatusVlinkMdkeyPrecedenceEntry 3 }

mgmtOspfStatusVlinkMdkeyPrecedencePrecedence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The precedence of message digest key."
    ::= { mgmtOspfStatusVlinkMdkeyPrecedenceEntry 4 }

mgmtOspfStatusVlinkMdkeyPrecedenceMdKeyId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The key ID for message digest authentication."
    ::= { mgmtOspfStatusVlinkMdkeyPrecedenceEntry 5 }

mgmtOspfStatusRouteIpv4Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusRouteIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF routing status table. It is used to provide the OSPF
         routing status information."
    ::= { mgmtOspfStatus 10 }

mgmtOspfStatusRouteIpv4Entry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusRouteIpv4Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF routing status information."
    INDEX       { mgmtOspfStatusRouteIpv4InstanceId,
                  mgmtOspfStatusRouteIpv4RouteType,
                  mgmtOspfStatusRouteIpv4Network,
                  mgmtOspfStatusRouteIpv4IpSubnetMaskLength,
                  mgmtOspfStatusRouteIpv4AreaId,
                  mgmtOspfStatusRouteIpv4NextHop }
    ::= { mgmtOspfStatusRouteIpv4Table 1 }

MGMTOspfStatusRouteIpv4Entry ::= SEQUENCE {
    mgmtOspfStatusRouteIpv4InstanceId          Integer32,
    mgmtOspfStatusRouteIpv4RouteType           MGMTOspfRouteType,
    mgmtOspfStatusRouteIpv4Network             IpAddress,
    mgmtOspfStatusRouteIpv4IpSubnetMaskLength  Integer32,
    mgmtOspfStatusRouteIpv4AreaId              IpAddress,
    mgmtOspfStatusRouteIpv4NextHop             IpAddress,
    mgmtOspfStatusRouteIpv4Cost                Unsigned32,
    mgmtOspfStatusRouteIpv4AsCost              Unsigned32,
    mgmtOspfStatusRouteIpv4BorderRouterType    MGMTOspfBorderRouterType,
    mgmtOspfStatusRouteIpv4Ifindex             MGMTInterfaceIndex,
    mgmtOspfStatusRouteIpv4IsConnected         TruthValue
}

mgmtOspfStatusRouteIpv4InstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusRouteIpv4Entry 1 }

mgmtOspfStatusRouteIpv4RouteType OBJECT-TYPE
    SYNTAX      MGMTOspfRouteType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF route type."
    ::= { mgmtOspfStatusRouteIpv4Entry 2 }

mgmtOspfStatusRouteIpv4Network OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtOspfStatusRouteIpv4Entry 3 }

mgmtOspfStatusRouteIpv4IpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtOspfStatusRouteIpv4Entry 4 }

mgmtOspfStatusRouteIpv4AreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "It indicates which area the route or router can be reached via/to."
    ::= { mgmtOspfStatusRouteIpv4Entry 5 }

mgmtOspfStatusRouteIpv4NextHop OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The nexthop to the route."
    ::= { mgmtOspfStatusRouteIpv4Entry 6 }

mgmtOspfStatusRouteIpv4Cost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The cost of the route."
    ::= { mgmtOspfStatusRouteIpv4Entry 7 }

mgmtOspfStatusRouteIpv4AsCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The cost of the route within the OSPF network. It is valid for external
         Type-2 route and always '0' for other route type."
    ::= { mgmtOspfStatusRouteIpv4Entry 8 }

mgmtOspfStatusRouteIpv4BorderRouterType OBJECT-TYPE
    SYNTAX      MGMTOspfBorderRouterType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The border router type of the OSPF route entry."
    ::= { mgmtOspfStatusRouteIpv4Entry 9 }

mgmtOspfStatusRouteIpv4Ifindex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The interface where the ip packet is outgoing."
    ::= { mgmtOspfStatusRouteIpv4Entry 10 }

mgmtOspfStatusRouteIpv4IsConnected OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The destination is connected directly or not."
    ::= { mgmtOspfStatusRouteIpv4Entry 11 }

mgmtOspfStatusDbTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusDbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF LSA link state database information table."
    ::= { mgmtOspfStatus 11 }

mgmtOspfStatusDbEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusDbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspfStatusDbInstanceId, mgmtOspfStatusDbAreaId,
                  mgmtOspfStatusDbLsdbType,
                  mgmtOspfStatusDbLinkStateId,
                  mgmtOspfStatusDbAdvRouterId }
    ::= { mgmtOspfStatusDbTable 1 }

MGMTOspfStatusDbEntry ::= SEQUENCE {
    mgmtOspfStatusDbInstanceId       Integer32,
    mgmtOspfStatusDbAreaId           IpAddress,
    mgmtOspfStatusDbLsdbType         MGMTOspfLsdbType,
    mgmtOspfStatusDbLinkStateId      IpAddress,
    mgmtOspfStatusDbAdvRouterId      IpAddress,
    mgmtOspfStatusDbAge              Unsigned32,
    mgmtOspfStatusDbSequence         Unsigned32,
    mgmtOspfStatusDbChecksum         Unsigned32,
    mgmtOspfStatusDbRouterLinkCount  Unsigned32
}

mgmtOspfStatusDbInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusDbEntry 1 }

mgmtOspfStatusDbAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfStatusDbEntry 2 }

mgmtOspfStatusDbLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspfLsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspfStatusDbEntry 3 }

mgmtOspfStatusDbLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF link state ID."
    ::= { mgmtOspfStatusDbEntry 4 }

mgmtOspfStatusDbAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspfStatusDbEntry 5 }

mgmtOspfStatusDbAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspfStatusDbEntry 6 }

mgmtOspfStatusDbSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspfStatusDbEntry 7 }

mgmtOspfStatusDbChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspfStatusDbEntry 8 }

mgmtOspfStatusDbRouterLinkCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The link count of the LSA. The field is significant only when the link
         state type is 'Router Link State' (Type 1)."
    ::= { mgmtOspfStatusDbEntry 9 }

mgmtOspfStatusDbRouterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusDbRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF LSA Router link state database information table."
    ::= { mgmtOspfStatus 12 }

mgmtOspfStatusDbRouterEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusDbRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspfStatusDbRouterInstanceId,
                  mgmtOspfStatusDbRouterAreaId,
                  mgmtOspfStatusDbRouterLsdbType,
                  mgmtOspfStatusDbRouterLinkStateId,
                  mgmtOspfStatusDbRouterAdvRouterId }
    ::= { mgmtOspfStatusDbRouterTable 1 }

MGMTOspfStatusDbRouterEntry ::= SEQUENCE {
    mgmtOspfStatusDbRouterInstanceId       Integer32,
    mgmtOspfStatusDbRouterAreaId           IpAddress,
    mgmtOspfStatusDbRouterLsdbType         MGMTOspfLsdbType,
    mgmtOspfStatusDbRouterLinkStateId      IpAddress,
    mgmtOspfStatusDbRouterAdvRouterId      IpAddress,
    mgmtOspfStatusDbRouterAge              Unsigned32,
    mgmtOspfStatusDbRouterOptions          MGMTUnsigned8,
    mgmtOspfStatusDbRouterSequence         Unsigned32,
    mgmtOspfStatusDbRouterChecksum         Unsigned32,
    mgmtOspfStatusDbRouterLength           Unsigned32,
    mgmtOspfStatusDbRouterRouterLinkCount  Unsigned32
}

mgmtOspfStatusDbRouterInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusDbRouterEntry 1 }

mgmtOspfStatusDbRouterAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfStatusDbRouterEntry 2 }

mgmtOspfStatusDbRouterLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspfLsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspfStatusDbRouterEntry 3 }

mgmtOspfStatusDbRouterLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF link state ID."
    ::= { mgmtOspfStatusDbRouterEntry 4 }

mgmtOspfStatusDbRouterAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspfStatusDbRouterEntry 5 }

mgmtOspfStatusDbRouterAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspfStatusDbRouterEntry 6 }

mgmtOspfStatusDbRouterOptions OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF option field which is present in OSPF hello packets, which
         enables OSPF routers to support (or not support) optional capabilities,
         and to communicate their capability level to other OSPF routers."
    ::= { mgmtOspfStatusDbRouterEntry 7 }

mgmtOspfStatusDbRouterSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspfStatusDbRouterEntry 8 }

mgmtOspfStatusDbRouterChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspfStatusDbRouterEntry 9 }

mgmtOspfStatusDbRouterLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspfStatusDbRouterEntry 10 }

mgmtOspfStatusDbRouterRouterLinkCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The link count of the LSA. The field is significant only when the link
         state type is 'Router Link State' (Type 1)."
    ::= { mgmtOspfStatusDbRouterEntry 11 }

mgmtOspfStatusDbNetworkTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusDbNetworkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF LSA Network link state database information table."
    ::= { mgmtOspfStatus 13 }

mgmtOspfStatusDbNetworkEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusDbNetworkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspfStatusDbNetworkInstanceId,
                  mgmtOspfStatusDbNetworkAreaId,
                  mgmtOspfStatusDbNetworkLsdbType,
                  mgmtOspfStatusDbNetworkLinkStateId,
                  mgmtOspfStatusDbNetworkAdvRouterId }
    ::= { mgmtOspfStatusDbNetworkTable 1 }

MGMTOspfStatusDbNetworkEntry ::= SEQUENCE {
    mgmtOspfStatusDbNetworkInstanceId           Integer32,
    mgmtOspfStatusDbNetworkAreaId               IpAddress,
    mgmtOspfStatusDbNetworkLsdbType             MGMTOspfLsdbType,
    mgmtOspfStatusDbNetworkLinkStateId          IpAddress,
    mgmtOspfStatusDbNetworkAdvRouterId          IpAddress,
    mgmtOspfStatusDbNetworkAge                  Unsigned32,
    mgmtOspfStatusDbNetworkOptions              MGMTUnsigned8,
    mgmtOspfStatusDbNetworkSequence             Unsigned32,
    mgmtOspfStatusDbNetworkChecksum             Unsigned32,
    mgmtOspfStatusDbNetworkLength               Unsigned32,
    mgmtOspfStatusDbNetworkNetworkMask          Integer32,
    mgmtOspfStatusDbNetworkAttachedRouterCount  Unsigned32
}

mgmtOspfStatusDbNetworkInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusDbNetworkEntry 1 }

mgmtOspfStatusDbNetworkAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfStatusDbNetworkEntry 2 }

mgmtOspfStatusDbNetworkLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspfLsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspfStatusDbNetworkEntry 3 }

mgmtOspfStatusDbNetworkLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF link state ID."
    ::= { mgmtOspfStatusDbNetworkEntry 4 }

mgmtOspfStatusDbNetworkAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspfStatusDbNetworkEntry 5 }

mgmtOspfStatusDbNetworkAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspfStatusDbNetworkEntry 6 }

mgmtOspfStatusDbNetworkOptions OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF option field which is present in OSPF hello packets, which
         enables OSPF routers to support (or not support) optional capabilities,
         and to communicate their capability level to other OSPF routers."
    ::= { mgmtOspfStatusDbNetworkEntry 7 }

mgmtOspfStatusDbNetworkSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspfStatusDbNetworkEntry 8 }

mgmtOspfStatusDbNetworkChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspfStatusDbNetworkEntry 9 }

mgmtOspfStatusDbNetworkLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspfStatusDbNetworkEntry 10 }

mgmtOspfStatusDbNetworkNetworkMask OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Network mask length. The field is significant only when the link state
         type is 'Network Link State' (Type 2)."
    ::= { mgmtOspfStatusDbNetworkEntry 11 }

mgmtOspfStatusDbNetworkAttachedRouterCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The attached router count of the LSA. The field is significant only
         when the link state type is 'Network Link State' (Type 2)."
    ::= { mgmtOspfStatusDbNetworkEntry 12 }

mgmtOspfStatusDbSummaryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusDbSummaryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF LSA Summary link state database information table."
    ::= { mgmtOspfStatus 14 }

mgmtOspfStatusDbSummaryEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusDbSummaryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspfStatusDbSummaryInstanceId,
                  mgmtOspfStatusDbSummaryAreaId,
                  mgmtOspfStatusDbSummaryLsdbType,
                  mgmtOspfStatusDbSummaryLinkStateId,
                  mgmtOspfStatusDbSummaryAdvRouterId }
    ::= { mgmtOspfStatusDbSummaryTable 1 }

MGMTOspfStatusDbSummaryEntry ::= SEQUENCE {
    mgmtOspfStatusDbSummaryInstanceId   Integer32,
    mgmtOspfStatusDbSummaryAreaId       IpAddress,
    mgmtOspfStatusDbSummaryLsdbType     MGMTOspfLsdbType,
    mgmtOspfStatusDbSummaryLinkStateId  IpAddress,
    mgmtOspfStatusDbSummaryAdvRouterId  IpAddress,
    mgmtOspfStatusDbSummaryAge          Unsigned32,
    mgmtOspfStatusDbSummaryOptions      MGMTUnsigned8,
    mgmtOspfStatusDbSummarySequence     Unsigned32,
    mgmtOspfStatusDbSummaryChecksum     Unsigned32,
    mgmtOspfStatusDbSummaryLength       Unsigned32,
    mgmtOspfStatusDbSummaryNetworkMask  Integer32,
    mgmtOspfStatusDbSummaryMetric       Unsigned32
}

mgmtOspfStatusDbSummaryInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusDbSummaryEntry 1 }

mgmtOspfStatusDbSummaryAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfStatusDbSummaryEntry 2 }

mgmtOspfStatusDbSummaryLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspfLsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspfStatusDbSummaryEntry 3 }

mgmtOspfStatusDbSummaryLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF link state ID."
    ::= { mgmtOspfStatusDbSummaryEntry 4 }

mgmtOspfStatusDbSummaryAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspfStatusDbSummaryEntry 5 }

mgmtOspfStatusDbSummaryAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspfStatusDbSummaryEntry 6 }

mgmtOspfStatusDbSummaryOptions OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF option field which is present in OSPF hello packets, which
         enables OSPF routers to support (or not support) optional capabilities,
         and to communicate their capability level to other OSPF routers."
    ::= { mgmtOspfStatusDbSummaryEntry 7 }

mgmtOspfStatusDbSummarySequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspfStatusDbSummaryEntry 8 }

mgmtOspfStatusDbSummaryChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspfStatusDbSummaryEntry 9 }

mgmtOspfStatusDbSummaryLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspfStatusDbSummaryEntry 10 }

mgmtOspfStatusDbSummaryNetworkMask OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Network mask length. The field is significant only when the link state
         type is 'Summary/ASBR Summary Link State' (Type 3, 4)."
    ::= { mgmtOspfStatusDbSummaryEntry 11 }

mgmtOspfStatusDbSummaryMetric OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User specified metric for this summary route. The field is significant
         only when the link state type is 'Summary/ASBR Summary Link State'
         (Type 3, 4)."
    ::= { mgmtOspfStatusDbSummaryEntry 12 }

mgmtOspfStatusDbASBRSummaryTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusDbASBRSummaryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF LSA ASBR Summary link state database information table."
    ::= { mgmtOspfStatus 15 }

mgmtOspfStatusDbASBRSummaryEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusDbASBRSummaryEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspfStatusDbASBRSummaryInstanceId,
                  mgmtOspfStatusDbASBRSummaryAreaId,
                  mgmtOspfStatusDbASBRSummaryLsdbType,
                  mgmtOspfStatusDbASBRSummaryLinkStateId,
                  mgmtOspfStatusDbASBRSummaryAdvRouterId }
    ::= { mgmtOspfStatusDbASBRSummaryTable 1 }

MGMTOspfStatusDbASBRSummaryEntry ::= SEQUENCE {
    mgmtOspfStatusDbASBRSummaryInstanceId   Integer32,
    mgmtOspfStatusDbASBRSummaryAreaId       IpAddress,
    mgmtOspfStatusDbASBRSummaryLsdbType     MGMTOspfLsdbType,
    mgmtOspfStatusDbASBRSummaryLinkStateId  IpAddress,
    mgmtOspfStatusDbASBRSummaryAdvRouterId  IpAddress,
    mgmtOspfStatusDbASBRSummaryAge          Unsigned32,
    mgmtOspfStatusDbASBRSummaryOptions      MGMTUnsigned8,
    mgmtOspfStatusDbASBRSummarySequence     Unsigned32,
    mgmtOspfStatusDbASBRSummaryChecksum     Unsigned32,
    mgmtOspfStatusDbASBRSummaryLength       Unsigned32,
    mgmtOspfStatusDbASBRSummaryNetworkMask  Integer32,
    mgmtOspfStatusDbASBRSummaryMetric       Unsigned32
}

mgmtOspfStatusDbASBRSummaryInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 1 }

mgmtOspfStatusDbASBRSummaryAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 2 }

mgmtOspfStatusDbASBRSummaryLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspfLsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 3 }

mgmtOspfStatusDbASBRSummaryLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF link state ID."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 4 }

mgmtOspfStatusDbASBRSummaryAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 5 }

mgmtOspfStatusDbASBRSummaryAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 6 }

mgmtOspfStatusDbASBRSummaryOptions OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF option field which is present in OSPF hello packets, which
         enables OSPF routers to support (or not support) optional capabilities,
         and to communicate their capability level to other OSPF routers."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 7 }

mgmtOspfStatusDbASBRSummarySequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 8 }

mgmtOspfStatusDbASBRSummaryChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 9 }

mgmtOspfStatusDbASBRSummaryLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 10 }

mgmtOspfStatusDbASBRSummaryNetworkMask OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Network mask length. The field is significant only when the link state
         type is 'Summary/ASBR Summary Link State' (Type 3, 4)."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 11 }

mgmtOspfStatusDbASBRSummaryMetric OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User specified metric for this summary route. The field is significant
         only when the link state type is 'Summary/ASBR Summary Link State'
         (Type 3, 4)."
    ::= { mgmtOspfStatusDbASBRSummaryEntry 12 }

mgmtOspfStatusDbExternalTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusDbExternalEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF LSA External link state database information table."
    ::= { mgmtOspfStatus 16 }

mgmtOspfStatusDbExternalEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusDbExternalEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspfStatusDbExternalInstanceId,
                  mgmtOspfStatusDbExternalAreaId,
                  mgmtOspfStatusDbExternalLsdbType,
                  mgmtOspfStatusDbExternalLinkStateId,
                  mgmtOspfStatusDbExternalAdvRouterId }
    ::= { mgmtOspfStatusDbExternalTable 1 }

MGMTOspfStatusDbExternalEntry ::= SEQUENCE {
    mgmtOspfStatusDbExternalInstanceId      Integer32,
    mgmtOspfStatusDbExternalAreaId          IpAddress,
    mgmtOspfStatusDbExternalLsdbType        MGMTOspfLsdbType,
    mgmtOspfStatusDbExternalLinkStateId     IpAddress,
    mgmtOspfStatusDbExternalAdvRouterId     IpAddress,
    mgmtOspfStatusDbExternalAge             Unsigned32,
    mgmtOspfStatusDbExternalOptions         MGMTUnsigned8,
    mgmtOspfStatusDbExternalSequence        Unsigned32,
    mgmtOspfStatusDbExternalChecksum        Unsigned32,
    mgmtOspfStatusDbExternalLength          Unsigned32,
    mgmtOspfStatusDbExternalNetworkMask     Integer32,
    mgmtOspfStatusDbExternalMetric          Unsigned32,
    mgmtOspfStatusDbExternalMetricType      Unsigned32,
    mgmtOspfStatusDbExternalForwardAddress  IpAddress
}

mgmtOspfStatusDbExternalInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusDbExternalEntry 1 }

mgmtOspfStatusDbExternalAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID of the link state advertisement. For type 5 and type
         7, the value is always *************** since it is not required for
         these two types."
    ::= { mgmtOspfStatusDbExternalEntry 2 }

mgmtOspfStatusDbExternalLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspfLsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspfStatusDbExternalEntry 3 }

mgmtOspfStatusDbExternalLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF link state ID."
    ::= { mgmtOspfStatusDbExternalEntry 4 }

mgmtOspfStatusDbExternalAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspfStatusDbExternalEntry 5 }

mgmtOspfStatusDbExternalAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspfStatusDbExternalEntry 6 }

mgmtOspfStatusDbExternalOptions OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF option field which is present in OSPF hello packets, which
         enables OSPF routers to support (or not support) optional capabilities,
         and to communicate their capability level to other OSPF routers."
    ::= { mgmtOspfStatusDbExternalEntry 7 }

mgmtOspfStatusDbExternalSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspfStatusDbExternalEntry 8 }

mgmtOspfStatusDbExternalChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspfStatusDbExternalEntry 9 }

mgmtOspfStatusDbExternalLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspfStatusDbExternalEntry 10 }

mgmtOspfStatusDbExternalNetworkMask OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Network mask length. The field is significant only when the link state
         type is 'External/NSSA External Link State' (Type 5, 7)."
    ::= { mgmtOspfStatusDbExternalEntry 11 }

mgmtOspfStatusDbExternalMetric OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User specified metric for this summary route. The field is significant
         only when the link state type is 'External/NSSA External Link State'
         (Type 5, 7)."
    ::= { mgmtOspfStatusDbExternalEntry 12 }

mgmtOspfStatusDbExternalMetricType OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The External type of the LSA. The field is significant only when the
         link state type is 'External/NSSA External Link State' (Type 5, 7)."
    ::= { mgmtOspfStatusDbExternalEntry 13 }

mgmtOspfStatusDbExternalForwardAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP address of forward address. The field is significant only when
         the link state type is 'External/NSSA External Link State' (Type 5, 7)."
    ::= { mgmtOspfStatusDbExternalEntry 14 }

mgmtOspfStatusDbNSSAExternalTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspfStatusDbNSSAExternalEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF LSA NSSA External link state database information table."
    ::= { mgmtOspfStatus 17 }

mgmtOspfStatusDbNSSAExternalEntry OBJECT-TYPE
    SYNTAX      MGMTOspfStatusDbNSSAExternalEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspfStatusDbNSSAExternalInstanceId,
                  mgmtOspfStatusDbNSSAExternalAreaId,
                  mgmtOspfStatusDbNSSAExternalLsdbType,
                  mgmtOspfStatusDbNSSAExternalLinkStateId,
                  mgmtOspfStatusDbNSSAExternalAdvRouterId }
    ::= { mgmtOspfStatusDbNSSAExternalTable 1 }

MGMTOspfStatusDbNSSAExternalEntry ::= SEQUENCE {
    mgmtOspfStatusDbNSSAExternalInstanceId      Integer32,
    mgmtOspfStatusDbNSSAExternalAreaId          IpAddress,
    mgmtOspfStatusDbNSSAExternalLsdbType        MGMTOspfLsdbType,
    mgmtOspfStatusDbNSSAExternalLinkStateId     IpAddress,
    mgmtOspfStatusDbNSSAExternalAdvRouterId     IpAddress,
    mgmtOspfStatusDbNSSAExternalAge             Unsigned32,
    mgmtOspfStatusDbNSSAExternalOptions         MGMTUnsigned8,
    mgmtOspfStatusDbNSSAExternalSequence        Unsigned32,
    mgmtOspfStatusDbNSSAExternalChecksum        Unsigned32,
    mgmtOspfStatusDbNSSAExternalLength          Unsigned32,
    mgmtOspfStatusDbNSSAExternalNetworkMask     Integer32,
    mgmtOspfStatusDbNSSAExternalMetric          Unsigned32,
    mgmtOspfStatusDbNSSAExternalMetricType      Unsigned32,
    mgmtOspfStatusDbNSSAExternalForwardAddress  IpAddress
}

mgmtOspfStatusDbNSSAExternalInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF process instance ID."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 1 }

mgmtOspfStatusDbNSSAExternalAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF area ID of the link state advertisement. For type 5 and type
         7, the value is always *************** since it is not required for
         these two types."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 2 }

mgmtOspfStatusDbNSSAExternalLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspfLsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 3 }

mgmtOspfStatusDbNSSAExternalLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF link state ID."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 4 }

mgmtOspfStatusDbNSSAExternalAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 5 }

mgmtOspfStatusDbNSSAExternalAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 6 }

mgmtOspfStatusDbNSSAExternalOptions OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF option field which is present in OSPF hello packets, which
         enables OSPF routers to support (or not support) optional capabilities,
         and to communicate their capability level to other OSPF routers."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 7 }

mgmtOspfStatusDbNSSAExternalSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 8 }

mgmtOspfStatusDbNSSAExternalChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 9 }

mgmtOspfStatusDbNSSAExternalLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 10 }

mgmtOspfStatusDbNSSAExternalNetworkMask OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Network mask length. The field is significant only when the link state
         type is 'External/NSSA External Link State' (Type 5, 7)."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 11 }

mgmtOspfStatusDbNSSAExternalMetric OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User specified metric for this summary route. The field is significant
         only when the link state type is 'External/NSSA External Link State'
         (Type 5, 7)."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 12 }

mgmtOspfStatusDbNSSAExternalMetricType OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The External type of the LSA. The field is significant only when the
         link state type is 'External/NSSA External Link State' (Type 5, 7)."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 13 }

mgmtOspfStatusDbNSSAExternalForwardAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP address of forward address. The field is significant only when
         the link state type is 'External/NSSA External Link State' (Type 5, 7)."
    ::= { mgmtOspfStatusDbNSSAExternalEntry 14 }

mgmtOspfControl OBJECT IDENTIFIER
    ::= { mgmtOspfMibObjects 4 }

mgmtOspfControlGlobals OBJECT IDENTIFIER
    ::= { mgmtOspfControl 4 }

mgmtOspfControlGlobalsReloadProcess OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set true to reload OSPF process."
    ::= { mgmtOspfControlGlobals 1 }

mgmtOspfMibConformance OBJECT IDENTIFIER
    ::= { mgmtOspfMib 2 }

mgmtOspfMibCompliances OBJECT IDENTIFIER
    ::= { mgmtOspfMibConformance 1 }

mgmtOspfMibGroups OBJECT IDENTIFIER
    ::= { mgmtOspfMibConformance 2 }

mgmtOspfCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfCapabilitiesMinInstanceId,
                  mgmtOspfCapabilitiesMaxInstanceId,
                  mgmtOspfCapabilitiesMinRouterId,
                  mgmtOspfCapabilitiesMaxRouterId,
                  mgmtOspfCapabilitiesMinPriority,
                  mgmtOspfCapabilitiesMaxPriority,
                  mgmtOspfCapabilitiesMinGeneralCost,
                  mgmtOspfCapabilitiesMaxGeneralCost,
                  mgmtOspfCapabilitiesMinInterfaceCost,
                  mgmtOspfCapabilitiesMaxInterfaceCost,
                  mgmtOspfCapabilitiesMinRedistributeCost,
                  mgmtOspfCapabilitiesMaxRedistributeCost,
                  mgmtOspfCapabilitiesMinHelloInterval,
                  mgmtOspfCapabilitiesMaxHelloInterval,
                  mgmtOspfCapabilitiesMinFastHelloPackets,
                  mgmtOspfCapabilitiesMaxFastHelloPackets,
                  mgmtOspfCapabilitiesMinRetransmitInterval,
                  mgmtOspfCapabilitiesMaxRetransmitInterval,
                  mgmtOspfCapabilitiesMinDeadInterval,
                  mgmtOspfCapabilitiesMaxDeadInterval,
                  mgmtOspfCapabilitiesMinRouterLsaOnStartup,
                  mgmtOspfCapabilitiesMaxRouterLsaOnStartup,
                  mgmtOspfCapabilitiesMinRouterLsaOnShutdown,
                  mgmtOspfCapabilitiesMaxRouterLsaOnShutdown,
                  mgmtOspfCapabilitiesMinMdKeyId,
                  mgmtOspfCapabilitiesMaxMdKeyId,
                  mgmtOspfCapabilitiesMinSimplePwdLen,
                  mgmtOspfCapabilitiesMaxSimplePwdLen,
                  mgmtOspfCapabilitiesMinMdKeyLen,
                  mgmtOspfCapabilitiesMaxMdKeyLen,
                  mgmtOspfCapabilitiesIsRipRedistributedSupported,
                  mgmtOspfCapabilitiesMinAdminDistance,
                  mgmtOspfCapabilitiesMaxAdminDistance }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 1 }

mgmtOspfConfigProcessTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigProcessInstanceId,
                  mgmtOspfConfigProcessAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 2 }

mgmtOspfConfigProcessTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigProcessTableRowEditorInstanceId,
                  mgmtOspfConfigProcessTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 3 }

mgmtOspfConfigRouterTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigRouterInstanceId,
                  mgmtOspfConfigRouterIsSpecificRouterId,
                  mgmtOspfConfigRouterRouterId,
                  mgmtOspfConfigRouterDefPassiveInterface,
                  mgmtOspfConfigRouterIsSpecificDefMetric,
                  mgmtOspfConfigRouterDefMetricVal,
                  mgmtOspfConfigRouterConnectedRedistMetricType,
                  mgmtOspfConfigRouterConnectedRedistIsSpecificMetric,
                  mgmtOspfConfigRouterConnectedRedistMetricVal,
                  mgmtOspfConfigRouterStaticRedistMetricType,
                  mgmtOspfConfigRouterStaticRedistIsSpecificMetric,
                  mgmtOspfConfigRouterStaticRedistMetricVal,
                  mgmtOspfConfigRouterRipRedistMetricType,
                  mgmtOspfConfigRouterRipRedistIsSpecificMetric,
                  mgmtOspfConfigRouterRipRedistMetricVal,
                  mgmtOspfConfigRouterIsOnStartup,
                  mgmtOspfConfigRouterOnStartupInterval,
                  mgmtOspfConfigRouterIsOnShutdown,
                  mgmtOspfConfigRouterOnShutdownInterval,
                  mgmtOspfConfigRouterIsAdministrative,
                  mgmtOspfConfigRouterDefaultRouteRedistMetricType,
                  mgmtOspfConfigRouterDefaultRouteRedistIsSpecificMetric,
                  mgmtOspfConfigRouterDefaultRouteRedistMetricVal,
                  mgmtOspfConfigRouterDefaultRouteRedistAlways,
                  mgmtOspfConfigRouterAdminDistance }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 4 }

mgmtOspfConfigRouterInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigRouterInterfaceInstanceId,
                  mgmtOspfConfigRouterInterfaceIfIndex,
                  mgmtOspfConfigRouterInterfacePassiveInterface }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 5 }

mgmtOspfConfigAreaTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigAreaInstanceId,
                  mgmtOspfConfigAreaNetwork,
                  mgmtOspfConfigAreaIpSubnetMaskLength,
                  mgmtOspfConfigAreaAreaId, mgmtOspfConfigAreaAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 6 }

mgmtOspfConfigAreaTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigAreaTableRowEditorInstanceId,
                  mgmtOspfConfigAreaTableRowEditorNetwork,
                  mgmtOspfConfigAreaTableRowEditorIpSubnetMaskLength,
                  mgmtOspfConfigAreaTableRowEditorAreaId,
                  mgmtOspfConfigAreaTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 7 }

mgmtOspfConfigAreaAuthTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigAreaAuthInstanceId,
                  mgmtOspfConfigAreaAuthAreaId,
                  mgmtOspfConfigAreaAuthAreaAuthType,
                  mgmtOspfConfigAreaAuthAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 8 }

mgmtOspfConfigAreaAuthTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigAreaAuthTableRowEditorInstanceId,
                  mgmtOspfConfigAreaAuthTableRowEditorAreaId,
                  mgmtOspfConfigAreaAuthTableRowEditorAreaAuthType,
                  mgmtOspfConfigAreaAuthTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 9 }

mgmtOspfConfigAreaRangeTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigAreaRangeInstanceId,
                  mgmtOspfConfigAreaRangeAreaId,
                  mgmtOspfConfigAreaRangeNetwork,
                  mgmtOspfConfigAreaRangeIpSubnetMaskLength,
                  mgmtOspfConfigAreaRangeAdvertised,
                  mgmtOspfConfigAreaRangeIsSpecificCost,
                  mgmtOspfConfigAreaRangeCost,
                  mgmtOspfConfigAreaRangeAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 10 }

mgmtOspfConfigAreaRangeTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigAreaRangeTableRowEditorInstanceId,
                  mgmtOspfConfigAreaRangeTableRowEditorAreaId,
                  mgmtOspfConfigAreaRangeTableRowEditorNetwork,
                  mgmtOspfConfigAreaRangeTableRowEditorIpSubnetMaskLength,
                  mgmtOspfConfigAreaRangeTableRowEditorAdvertised,
                  mgmtOspfConfigAreaRangeTableRowEditorIsSpecificCost,
                  mgmtOspfConfigAreaRangeTableRowEditorCost,
                  mgmtOspfConfigAreaRangeTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 11 }

mgmtOspfConfigInterfaceMdKeyTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigInterfaceMdKeyIfIndex,
                  mgmtOspfConfigInterfaceMdKeyMdKeyId,
                  mgmtOspfConfigInterfaceMdKeyIsEncrypted,
                  mgmtOspfConfigInterfaceMdKeyMdKey,
                  mgmtOspfConfigInterfaceMdKeyAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 12 }

mgmtOspfConfigInterfaceMdKeyTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigInterfaceMdKeyTableRowEditorIfIndex,
                  mgmtOspfConfigInterfaceMdKeyTableRowEditorMdKeyId,
                  mgmtOspfConfigInterfaceMdKeyTableRowEditorIsEncrypted,
                  mgmtOspfConfigInterfaceMdKeyTableRowEditorMdKey,
                  mgmtOspfConfigInterfaceMdKeyTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 13 }

mgmtOspfConfigVlinkTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigVlinkInstanceId,
                  mgmtOspfConfigVlinkAreaId,
                  mgmtOspfConfigVlinkRouterId,
                  mgmtOspfConfigVlinkHelloInterval,
                  mgmtOspfConfigVlinkDeadInterval,
                  mgmtOspfConfigVlinkRetransmitInterval,
                  mgmtOspfConfigVlinkAuthType,
                  mgmtOspfConfigVlinkIsEncrypted,
                  mgmtOspfConfigVlinkSimplePwd,
                  mgmtOspfConfigVlinkAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 14 }

mgmtOspfConfigVlinkTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigVlinkTableRowEditorInstanceId,
                  mgmtOspfConfigVlinkTableRowEditorAreaId,
                  mgmtOspfConfigVlinkTableRowEditorRouterId,
                  mgmtOspfConfigVlinkTableRowEditorHelloInterval,
                  mgmtOspfConfigVlinkTableRowEditorDeadInterval,
                  mgmtOspfConfigVlinkTableRowEditorRetransmitInterval,
                  mgmtOspfConfigVlinkTableRowEditorAuthType,
                  mgmtOspfConfigVlinkTableRowEditorIsEncrypted,
                  mgmtOspfConfigVlinkTableRowEditorSimplePwd,
                  mgmtOspfConfigVlinkTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 15 }

mgmtOspfConfigVlinkMdKeyTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigVlinkMdKeyInstanceId,
                  mgmtOspfConfigVlinkMdKeyAreaId,
                  mgmtOspfConfigVlinkMdKeyRouterId,
                  mgmtOspfConfigVlinkMdKeyMdKeyId,
                  mgmtOspfConfigVlinkMdKeyIsEncrypted,
                  mgmtOspfConfigVlinkMdKeyMdKey,
                  mgmtOspfConfigVlinkMdKeyAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 16 }

mgmtOspfConfigVlinkTableMdKeyRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigVlinkTableMdKeyRowEditorInstanceId,
                  mgmtOspfConfigVlinkTableMdKeyRowEditorAreaId,
                  mgmtOspfConfigVlinkTableMdKeyRowEditorRouterId,
                  mgmtOspfConfigVlinkTableMdKeyRowEditorMdKeyId,
                  mgmtOspfConfigVlinkTableMdKeyRowEditorIsEncrypted,
                  mgmtOspfConfigVlinkTableMdKeyRowEditorMdKey,
                  mgmtOspfConfigVlinkTableMdKeyRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 17 }

mgmtOspfConfigStubAreaTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigStubAreaInstanceId,
                  mgmtOspfConfigStubAreaAreaId,
                  mgmtOspfConfigStubAreaIsNssa,
                  mgmtOspfConfigStubAreaNoSummary,
                  mgmtOspfConfigStubAreaNssaTranslatorRole,
                  mgmtOspfConfigStubAreaAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 18 }

mgmtOspfConfigStubAreaTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigStubAreaTableRowEditorInstanceId,
                  mgmtOspfConfigStubAreaTableRowEditorAreaId,
                  mgmtOspfConfigStubAreaTableRowEditorIsNssa,
                  mgmtOspfConfigStubAreaTableRowEditorNoSummary,
                  mgmtOspfConfigStubAreaTableRowEditorNssaTranslatorRole,
                  mgmtOspfConfigStubAreaTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 19 }

mgmtOspfConfigInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfConfigInterfaceIfIndex,
                  mgmtOspfConfigInterfacePriority,
                  mgmtOspfConfigInterfaceIsSpecificCost,
                  mgmtOspfConfigInterfaceCost,
                  mgmtOspfConfigInterfaceIsFastHelloEnabled,
                  mgmtOspfConfigInterfaceFastHelloPackets,
                  mgmtOspfConfigInterfaceDeadInterval,
                  mgmtOspfConfigInterfaceHelloInterval,
                  mgmtOspfConfigInterfaceRetransmitInterval,
                  mgmtOspfConfigInterfaceAuthType,
                  mgmtOspfConfigInterfaceIsEncrypted,
                  mgmtOspfConfigInterfaceSimplePwd }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 20 }

mgmtOspfStatusRouterTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusRouterInstanceId,
                  mgmtOspfStatusRouterRouterId,
                  mgmtOspfStatusRouterSpfDelay,
                  mgmtOspfStatusRouterSpfHoldTime,
                  mgmtOspfStatusRouterSpfMaxWaitTime,
                  mgmtOspfStatusRouterLastExcutedSpfTs,
                  mgmtOspfStatusRouterMinLsaInterval,
                  mgmtOspfStatusRouterMinLsaArrival,
                  mgmtOspfStatusRouterExternalLsaCount,
                  mgmtOspfStatusRouterExternalLsaChecksum,
                  mgmtOspfStatusRouterAttachedAreaCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 21 }

mgmtOspfStatusRouteAreaTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusRouteAreaInstanceId,
                  mgmtOspfStatusRouteAreaAreaId,
                  mgmtOspfStatusRouteAreaIsBackbone,
                  mgmtOspfStatusRouteAreaAreaType,
                  mgmtOspfStatusRouteAreaAttachedIntfActiveCount,
                  mgmtOspfStatusRouteAreaAuthType,
                  mgmtOspfStatusRouteAreaSpfExecutedCount,
                  mgmtOspfStatusRouteAreaLsaCount,
                  mgmtOspfStatusRouteAreaRouterLsaCount,
                  mgmtOspfStatusRouteAreaRouterLsaChecksum,
                  mgmtOspfStatusRouteAreaNetworkLsaCount,
                  mgmtOspfStatusRouteAreaNetworkLsaChecksum,
                  mgmtOspfStatusRouteAreaSummaryLsaCount,
                  mgmtOspfStatusRouteAreaSummaryLsaChecksum,
                  mgmtOspfStatusRouteAreaAsbrSummaryLsaCount,
                  mgmtOspfStatusRouteAreaAsbrSummaryLsaChecksum,
                  mgmtOspfStatusRouteAreaNssaLsaCount,
                  mgmtOspfStatusRouteAreaNssaLsaChecksum,
                  mgmtOspfStatusRouteAreaNssaTranslatorState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 22 }

mgmtOspfStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusInterfaceIfIndex,
                  mgmtOspfStatusInterfaceStatus,
                  mgmtOspfStatusInterfacePassive,
                  mgmtOspfStatusInterfaceNetwork,
                  mgmtOspfStatusInterfaceIpSubnetMaskLength,
                  mgmtOspfStatusInterfaceAreaId,
                  mgmtOspfStatusInterfaceRouterId,
                  mgmtOspfStatusInterfaceCost,
                  mgmtOspfStatusInterfaceState,
                  mgmtOspfStatusInterfacePriority,
                  mgmtOspfStatusInterfaceDrId,
                  mgmtOspfStatusInterfaceDrAddr,
                  mgmtOspfStatusInterfaceBdrId,
                  mgmtOspfStatusInterfaceBdrAddr,
                  mgmtOspfStatusInterfaceHelloTime,
                  mgmtOspfStatusInterfaceDeadTime,
                  mgmtOspfStatusInterfaceWaitTime,
                  mgmtOspfStatusInterfaceRetransmitTime,
                  mgmtOspfStatusInterfaceHelloDueTime,
                  mgmtOspfStatusInterfaceNeighborCount,
                  mgmtOspfStatusInterfaceAdjNeighborCount,
                  mgmtOspfStatusInterfaceTransmitDelay }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 23 }

mgmtOspfStatusNeighborIpv4TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusNeighborIpv4InstanceId,
                  mgmtOspfStatusNeighborIpv4RouterId,
                  mgmtOspfStatusNeighborIpv4Ipv4Addr,
                  mgmtOspfStatusNeighborIpv4IfIndex,
                  mgmtOspfStatusNeighborIpv4IpAddr,
                  mgmtOspfStatusNeighborIpv4AreaId,
                  mgmtOspfStatusNeighborIpv4Priority,
                  mgmtOspfStatusNeighborIpv4State,
                  mgmtOspfStatusNeighborIpv4DrId,
                  mgmtOspfStatusNeighborIpv4DrAddr,
                  mgmtOspfStatusNeighborIpv4BdrId,
                  mgmtOspfStatusNeighborIpv4BdrAddr,
                  mgmtOspfStatusNeighborIpv4Options,
                  mgmtOspfStatusNeighborIpv4DeadTime,
                  mgmtOspfStatusNeighborIpv4TransitAreaId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 24 }

mgmtOspfStatusInterfaceMdKeyPrecedenceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusInterfaceMdKeyPrecedenceIfIndex,
                  mgmtOspfStatusInterfaceMdKeyPrecedencePrecedence,
                  mgmtOspfStatusInterfaceMdKeyPrecedenceMdKeyId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 25 }

mgmtOspfStatusVlinkMdkeyPrecedenceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusVlinkMdkeyPrecedenceInstanceId,
                  mgmtOspfStatusVlinkMdkeyPrecedenceAreaId,
                  mgmtOspfStatusVlinkMdkeyPrecedenceRouterId,
                  mgmtOspfStatusVlinkMdkeyPrecedencePrecedence,
                  mgmtOspfStatusVlinkMdkeyPrecedenceMdKeyId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 26 }

mgmtOspfStatusRouteIpv4TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusRouteIpv4InstanceId,
                  mgmtOspfStatusRouteIpv4RouteType,
                  mgmtOspfStatusRouteIpv4Network,
                  mgmtOspfStatusRouteIpv4IpSubnetMaskLength,
                  mgmtOspfStatusRouteIpv4AreaId,
                  mgmtOspfStatusRouteIpv4NextHop,
                  mgmtOspfStatusRouteIpv4Cost,
                  mgmtOspfStatusRouteIpv4AsCost,
                  mgmtOspfStatusRouteIpv4BorderRouterType,
                  mgmtOspfStatusRouteIpv4Ifindex,
                  mgmtOspfStatusRouteIpv4IsConnected }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 27 }

mgmtOspfStatusDbTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusDbInstanceId, mgmtOspfStatusDbAreaId,
                  mgmtOspfStatusDbLsdbType,
                  mgmtOspfStatusDbLinkStateId,
                  mgmtOspfStatusDbAdvRouterId, mgmtOspfStatusDbAge,
                  mgmtOspfStatusDbSequence, mgmtOspfStatusDbChecksum,
                  mgmtOspfStatusDbRouterLinkCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 28 }

mgmtOspfStatusDbRouterTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusDbRouterInstanceId,
                  mgmtOspfStatusDbRouterAreaId,
                  mgmtOspfStatusDbRouterLsdbType,
                  mgmtOspfStatusDbRouterLinkStateId,
                  mgmtOspfStatusDbRouterAdvRouterId,
                  mgmtOspfStatusDbRouterAge,
                  mgmtOspfStatusDbRouterOptions,
                  mgmtOspfStatusDbRouterSequence,
                  mgmtOspfStatusDbRouterChecksum,
                  mgmtOspfStatusDbRouterLength,
                  mgmtOspfStatusDbRouterRouterLinkCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 29 }

mgmtOspfStatusDbNetworkTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusDbNetworkInstanceId,
                  mgmtOspfStatusDbNetworkAreaId,
                  mgmtOspfStatusDbNetworkLsdbType,
                  mgmtOspfStatusDbNetworkLinkStateId,
                  mgmtOspfStatusDbNetworkAdvRouterId,
                  mgmtOspfStatusDbNetworkAge,
                  mgmtOspfStatusDbNetworkOptions,
                  mgmtOspfStatusDbNetworkSequence,
                  mgmtOspfStatusDbNetworkChecksum,
                  mgmtOspfStatusDbNetworkLength,
                  mgmtOspfStatusDbNetworkNetworkMask,
                  mgmtOspfStatusDbNetworkAttachedRouterCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 30 }

mgmtOspfStatusDbSummaryTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusDbSummaryInstanceId,
                  mgmtOspfStatusDbSummaryAreaId,
                  mgmtOspfStatusDbSummaryLsdbType,
                  mgmtOspfStatusDbSummaryLinkStateId,
                  mgmtOspfStatusDbSummaryAdvRouterId,
                  mgmtOspfStatusDbSummaryAge,
                  mgmtOspfStatusDbSummaryOptions,
                  mgmtOspfStatusDbSummarySequence,
                  mgmtOspfStatusDbSummaryChecksum,
                  mgmtOspfStatusDbSummaryLength,
                  mgmtOspfStatusDbSummaryNetworkMask,
                  mgmtOspfStatusDbSummaryMetric }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 31 }

mgmtOspfStatusDbASBRSummaryTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusDbASBRSummaryInstanceId,
                  mgmtOspfStatusDbASBRSummaryAreaId,
                  mgmtOspfStatusDbASBRSummaryLsdbType,
                  mgmtOspfStatusDbASBRSummaryLinkStateId,
                  mgmtOspfStatusDbASBRSummaryAdvRouterId,
                  mgmtOspfStatusDbASBRSummaryAge,
                  mgmtOspfStatusDbASBRSummaryOptions,
                  mgmtOspfStatusDbASBRSummarySequence,
                  mgmtOspfStatusDbASBRSummaryChecksum,
                  mgmtOspfStatusDbASBRSummaryLength,
                  mgmtOspfStatusDbASBRSummaryNetworkMask,
                  mgmtOspfStatusDbASBRSummaryMetric }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 32 }

mgmtOspfStatusDbExternalTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusDbExternalInstanceId,
                  mgmtOspfStatusDbExternalAreaId,
                  mgmtOspfStatusDbExternalLsdbType,
                  mgmtOspfStatusDbExternalLinkStateId,
                  mgmtOspfStatusDbExternalAdvRouterId,
                  mgmtOspfStatusDbExternalAge,
                  mgmtOspfStatusDbExternalOptions,
                  mgmtOspfStatusDbExternalSequence,
                  mgmtOspfStatusDbExternalChecksum,
                  mgmtOspfStatusDbExternalLength,
                  mgmtOspfStatusDbExternalNetworkMask,
                  mgmtOspfStatusDbExternalMetric,
                  mgmtOspfStatusDbExternalMetricType,
                  mgmtOspfStatusDbExternalForwardAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 33 }

mgmtOspfStatusDbNSSAExternalTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfStatusDbNSSAExternalInstanceId,
                  mgmtOspfStatusDbNSSAExternalAreaId,
                  mgmtOspfStatusDbNSSAExternalLsdbType,
                  mgmtOspfStatusDbNSSAExternalLinkStateId,
                  mgmtOspfStatusDbNSSAExternalAdvRouterId,
                  mgmtOspfStatusDbNSSAExternalAge,
                  mgmtOspfStatusDbNSSAExternalOptions,
                  mgmtOspfStatusDbNSSAExternalSequence,
                  mgmtOspfStatusDbNSSAExternalChecksum,
                  mgmtOspfStatusDbNSSAExternalLength,
                  mgmtOspfStatusDbNSSAExternalNetworkMask,
                  mgmtOspfStatusDbNSSAExternalMetric,
                  mgmtOspfStatusDbNSSAExternalMetricType,
                  mgmtOspfStatusDbNSSAExternalForwardAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 34 }

mgmtOspfControlGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspfControlGlobalsReloadProcess }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspfMibGroups 35 }

mgmtOspfMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtOspfCapabilitiesInfoGroup,
                       mgmtOspfConfigProcessTableInfoGroup,
                       mgmtOspfConfigProcessTableRowEditorInfoGroup,
                       mgmtOspfConfigRouterTableInfoGroup,
                       mgmtOspfConfigRouterInterfaceTableInfoGroup,
                       mgmtOspfConfigAreaTableInfoGroup,
                       mgmtOspfConfigAreaTableRowEditorInfoGroup,
                       mgmtOspfConfigAreaAuthTableInfoGroup,
                       mgmtOspfConfigAreaAuthTableRowEditorInfoGroup,
                       mgmtOspfConfigAreaRangeTableInfoGroup,
                       mgmtOspfConfigAreaRangeTableRowEditorInfoGroup,
                       mgmtOspfConfigInterfaceMdKeyTableInfoGroup,
                       mgmtOspfConfigInterfaceMdKeyTableRowEditorInfoGroup,
                       mgmtOspfConfigVlinkTableInfoGroup,
                       mgmtOspfConfigVlinkTableRowEditorInfoGroup,
                       mgmtOspfConfigVlinkMdKeyTableInfoGroup,
                       mgmtOspfConfigVlinkTableMdKeyRowEditorInfoGroup,
                       mgmtOspfConfigStubAreaTableInfoGroup,
                       mgmtOspfConfigStubAreaTableRowEditorInfoGroup,
                       mgmtOspfConfigInterfaceTableInfoGroup,
                       mgmtOspfStatusRouterTableInfoGroup,
                       mgmtOspfStatusRouteAreaTableInfoGroup,
                       mgmtOspfStatusInterfaceTableInfoGroup,
                       mgmtOspfStatusNeighborIpv4TableInfoGroup,
                       mgmtOspfStatusInterfaceMdKeyPrecedenceTableInfoGroup,
                       mgmtOspfStatusVlinkMdkeyPrecedenceTableInfoGroup,
                       mgmtOspfStatusRouteIpv4TableInfoGroup,
                       mgmtOspfStatusDbTableInfoGroup,
                       mgmtOspfStatusDbRouterTableInfoGroup,
                       mgmtOspfStatusDbNetworkTableInfoGroup,
                       mgmtOspfStatusDbSummaryTableInfoGroup,
                       mgmtOspfStatusDbASBRSummaryTableInfoGroup,
                       mgmtOspfStatusDbExternalTableInfoGroup,
                       mgmtOspfStatusDbNSSAExternalTableInfoGroup,
                       mgmtOspfControlGlobalsInfoGroup }

    ::= { mgmtOspfMibCompliances 1 }

END
