-- *****************************************************************
-- VCL-MIB:  
-- ****************************************************************

MGMT-VCL-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTVclProtoEncap FROM MGMT-TC
    ;

mgmtVclMib MODULE-IDENTITY
    LAST-UPDATED "201603210000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private VCL MIB."
    REVISION    "201603210000Z"
    DESCRIPTION
        "Allow subnet length zero."
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 79 }


mgmtVclMibObjects OBJECT IDENTIFIER
    ::= { mgmtVclMib 1 }

mgmtVclConfig OBJECT IDENTIFIER
    ::= { mgmtVclMibObjects 2 }

mgmtVclConfigMac OBJECT IDENTIFIER
    ::= { mgmtVclConfig 1 }

mgmtVclConfigMacTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVclConfigMacEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the MAC address to VLAN ID configuration table."
    ::= { mgmtVclConfigMac 1 }

mgmtVclConfigMacEntry OBJECT-TYPE
    SYNTAX      MGMTVclConfigMacEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents a configured MAC-based
         classification.
         
         "
    INDEX       { mgmtVclConfigMacMacAddress }
    ::= { mgmtVclConfigMacTable 1 }

MGMTVclConfigMacEntry ::= SEQUENCE {
    mgmtVclConfigMacMacAddress  MacAddress,
    mgmtVclConfigMacVlanId      Integer32,
    mgmtVclConfigMacPortList    MGMTPortList,
    mgmtVclConfigMacAction      MGMTRowEditorState
}

mgmtVclConfigMacMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The MAC address for which this entry is applicable."
    ::= { mgmtVclConfigMacEntry 1 }

mgmtVclConfigMacVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vlan id of the mapping."
    ::= { mgmtVclConfigMacEntry 2 }

mgmtVclConfigMacPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of stack/switch ports on which this entry is active."
    ::= { mgmtVclConfigMacEntry 3 }

mgmtVclConfigMacAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVclConfigMacEntry 100 }

mgmtVclConfigMacRowEditor OBJECT IDENTIFIER
    ::= { mgmtVclConfigMac 2 }

mgmtVclConfigMacRowEditorMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The MAC address for which this entry is applicable."
    ::= { mgmtVclConfigMacRowEditor 1 }

mgmtVclConfigMacRowEditorVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vlan id of the mapping."
    ::= { mgmtVclConfigMacRowEditor 2 }

mgmtVclConfigMacRowEditorPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of stack/switch ports on which this entry is active."
    ::= { mgmtVclConfigMacRowEditor 3 }

mgmtVclConfigMacRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVclConfigMacRowEditor 100 }

mgmtVclConfigIp OBJECT IDENTIFIER
    ::= { mgmtVclConfig 2 }

mgmtVclConfigIpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVclConfigIpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the IP Subnet to VLAN ID configuration table. The key of this
         table is the IP subnet expressed as x.x.x.x/x, where the first 4 octets
         represent the IPv4 address and the last one is the mask length.
         
         NOTE#1: Inside the VCL module these entries are actually sorted based
         on a priority defined by the mask length, so that subnets with larger
         mask lengths are first in the list, followed by entries with smaller
         mask lengths. SNMP cannot follow this sorting, therefore the order the
         entries are retrieved by the iterator may not be the same as the
         actually stored order. (This is not an issue, but should be taken into
         consideration when using the SNMP interface to create a user interface.
         
         NOTE#2: Even though only the subnet address is stored in the table
         (i.e. both ***********/4 and ***********/4 will end up as *********/4),
         the SNMP iterator will NOT take this into consideration. So, when
         searching the next subnet of ***********/4, the result could be
         *********/4 but not ***********/24 (granted that these entries are
         present)"
    ::= { mgmtVclConfigIp 1 }

mgmtVclConfigIpEntry OBJECT-TYPE
    SYNTAX      MGMTVclConfigIpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents a configured IP Subnet-based
         classification.
         
         "
    INDEX       { mgmtVclConfigIpIpSubnetAddress,
                  mgmtVclConfigIpIpSubnetMaskLength }
    ::= { mgmtVclConfigIpTable 1 }

MGMTVclConfigIpEntry ::= SEQUENCE {
    mgmtVclConfigIpIpSubnetAddress     IpAddress,
    mgmtVclConfigIpIpSubnetMaskLength  Integer32,
    mgmtVclConfigIpVlanId              Integer32,
    mgmtVclConfigIpPortList            MGMTPortList,
    mgmtVclConfigIpAction              MGMTRowEditorState
}

mgmtVclConfigIpIpSubnetAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The IP subnet address for which this entry is applicable."
    ::= { mgmtVclConfigIpEntry 1 }

mgmtVclConfigIpIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The IP subnet mask length for which this entry is applicable."
    ::= { mgmtVclConfigIpEntry 2 }

mgmtVclConfigIpVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vlan id of the mapping."
    ::= { mgmtVclConfigIpEntry 3 }

mgmtVclConfigIpPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of stack/switch ports on which this entry is active."
    ::= { mgmtVclConfigIpEntry 4 }

mgmtVclConfigIpAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVclConfigIpEntry 100 }

mgmtVclConfigIpRowEditor OBJECT IDENTIFIER
    ::= { mgmtVclConfigIp 2 }

mgmtVclConfigIpRowEditorIpSubnetAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The IP subnet address for which this entry is applicable."
    ::= { mgmtVclConfigIpRowEditor 1 }

mgmtVclConfigIpRowEditorIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The IP subnet mask length for which this entry is applicable."
    ::= { mgmtVclConfigIpRowEditor 2 }

mgmtVclConfigIpRowEditorVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vlan id of the mapping."
    ::= { mgmtVclConfigIpRowEditor 3 }

mgmtVclConfigIpRowEditorPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of stack/switch ports on which this entry is active."
    ::= { mgmtVclConfigIpRowEditor 4 }

mgmtVclConfigIpRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVclConfigIpRowEditor 100 }

mgmtVclConfigProtocol OBJECT IDENTIFIER
    ::= { mgmtVclConfig 3 }

mgmtVclConfigProtocolProto OBJECT IDENTIFIER
    ::= { mgmtVclConfigProtocol 1 }

mgmtVclConfigProtocolProtoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVclConfigProtocolProtoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the Protocol to Protocol Group mapping table."
    ::= { mgmtVclConfigProtocolProto 1 }

mgmtVclConfigProtocolProtoEntry OBJECT-TYPE
    SYNTAX      MGMTVclConfigProtocolProtoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents a Protocol to Group mapping.
         
         "
    INDEX       { mgmtVclConfigProtocolProtoProtocolEncapsulation }
    ::= { mgmtVclConfigProtocolProtoTable 1 }

MGMTVclConfigProtocolProtoEntry ::= SEQUENCE {
    mgmtVclConfigProtocolProtoProtocolEncapsulation  MGMTVclProtoEncap,
    mgmtVclConfigProtocolProtoProtocolGroupName      MGMTDisplayString,
    mgmtVclConfigProtocolProtoAction                 MGMTRowEditorState
}

mgmtVclConfigProtocolProtoProtocolEncapsulation OBJECT-TYPE
    SYNTAX      MGMTVclProtoEncap
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The protocol encapsulation of the Protocol to Group mapping."
    ::= { mgmtVclConfigProtocolProtoEntry 1 }

mgmtVclConfigProtocolProtoProtocolGroupName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a name identifying the protocol group."
    ::= { mgmtVclConfigProtocolProtoEntry 2 }

mgmtVclConfigProtocolProtoAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVclConfigProtocolProtoEntry 100 }

mgmtVclConfigProtocolProtoRowEditor OBJECT IDENTIFIER
    ::= { mgmtVclConfigProtocolProto 2 }

mgmtVclConfigProtocolProtoRowEditorProtocolEncapsulation OBJECT-TYPE
    SYNTAX      MGMTVclProtoEncap
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The protocol encapsulation of the Protocol to Group mapping."
    ::= { mgmtVclConfigProtocolProtoRowEditor 1 }

mgmtVclConfigProtocolProtoRowEditorProtocolGroupName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a name identifying the protocol group."
    ::= { mgmtVclConfigProtocolProtoRowEditor 2 }

mgmtVclConfigProtocolProtoRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVclConfigProtocolProtoRowEditor 100 }

mgmtVclConfigProtocolGroup OBJECT IDENTIFIER
    ::= { mgmtVclConfigProtocol 2 }

mgmtVclConfigProtocolGroupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTVclConfigProtocolGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the Protocol Group to VLAN ID configuration table."
    ::= { mgmtVclConfigProtocolGroup 1 }

mgmtVclConfigProtocolGroupEntry OBJECT-TYPE
    SYNTAX      MGMTVclConfigProtocolGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents a Protocol Group to VLAN ID
         mapping.
         
         "
    INDEX       { mgmtVclConfigProtocolGroupProtocolGroupName }
    ::= { mgmtVclConfigProtocolGroupTable 1 }

MGMTVclConfigProtocolGroupEntry ::= SEQUENCE {
    mgmtVclConfigProtocolGroupProtocolGroupName  MGMTDisplayString,
    mgmtVclConfigProtocolGroupVlanId             Integer32,
    mgmtVclConfigProtocolGroupPortList           MGMTPortList,
    mgmtVclConfigProtocolGroupAction             MGMTRowEditorState
}

mgmtVclConfigProtocolGroupProtocolGroupName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "This is a name identifying the protocol group."
    ::= { mgmtVclConfigProtocolGroupEntry 1 }

mgmtVclConfigProtocolGroupVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vlan id of the mapping."
    ::= { mgmtVclConfigProtocolGroupEntry 2 }

mgmtVclConfigProtocolGroupPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of stack/switch ports on which this entry is active."
    ::= { mgmtVclConfigProtocolGroupEntry 3 }

mgmtVclConfigProtocolGroupAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVclConfigProtocolGroupEntry 100 }

mgmtVclConfigProtocolGroupRowEditor OBJECT IDENTIFIER
    ::= { mgmtVclConfigProtocolGroup 2 }

mgmtVclConfigProtocolGroupRowEditorProtocolGroupName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a name identifying the protocol group."
    ::= { mgmtVclConfigProtocolGroupRowEditor 1 }

mgmtVclConfigProtocolGroupRowEditorVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Vlan id of the mapping."
    ::= { mgmtVclConfigProtocolGroupRowEditor 2 }

mgmtVclConfigProtocolGroupRowEditorPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of stack/switch ports on which this entry is active."
    ::= { mgmtVclConfigProtocolGroupRowEditor 3 }

mgmtVclConfigProtocolGroupRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtVclConfigProtocolGroupRowEditor 100 }

mgmtVclMibConformance OBJECT IDENTIFIER
    ::= { mgmtVclMib 2 }

mgmtVclMibCompliances OBJECT IDENTIFIER
    ::= { mgmtVclMibConformance 1 }

mgmtVclMibGroups OBJECT IDENTIFIER
    ::= { mgmtVclMibConformance 2 }

mgmtVclConfigMacTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVclConfigMacMacAddress, mgmtVclConfigMacVlanId,
                  mgmtVclConfigMacPortList, mgmtVclConfigMacAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVclMibGroups 1 }

mgmtVclConfigMacRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVclConfigMacRowEditorMacAddress,
                  mgmtVclConfigMacRowEditorVlanId,
                  mgmtVclConfigMacRowEditorPortList,
                  mgmtVclConfigMacRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVclMibGroups 2 }

mgmtVclConfigIpTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVclConfigIpIpSubnetAddress,
                  mgmtVclConfigIpIpSubnetMaskLength,
                  mgmtVclConfigIpVlanId, mgmtVclConfigIpPortList,
                  mgmtVclConfigIpAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVclMibGroups 3 }

mgmtVclConfigIpRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVclConfigIpRowEditorIpSubnetAddress,
                  mgmtVclConfigIpRowEditorIpSubnetMaskLength,
                  mgmtVclConfigIpRowEditorVlanId,
                  mgmtVclConfigIpRowEditorPortList,
                  mgmtVclConfigIpRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVclMibGroups 4 }

mgmtVclConfigProtocolProtoTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVclConfigProtocolProtoProtocolEncapsulation,
                  mgmtVclConfigProtocolProtoProtocolGroupName,
                  mgmtVclConfigProtocolProtoAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVclMibGroups 5 }

mgmtVclConfigProtocolProtoRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtVclConfigProtocolProtoRowEditorProtocolEncapsulation,
                  mgmtVclConfigProtocolProtoRowEditorProtocolGroupName,
                  mgmtVclConfigProtocolProtoRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVclMibGroups 6 }

mgmtVclConfigProtocolGroupTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtVclConfigProtocolGroupProtocolGroupName,
                  mgmtVclConfigProtocolGroupVlanId,
                  mgmtVclConfigProtocolGroupPortList,
                  mgmtVclConfigProtocolGroupAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVclMibGroups 7 }

mgmtVclConfigProtocolGroupRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtVclConfigProtocolGroupRowEditorProtocolGroupName,
                  mgmtVclConfigProtocolGroupRowEditorVlanId,
                  mgmtVclConfigProtocolGroupRowEditorPortList,
                  mgmtVclConfigProtocolGroupRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtVclMibGroups 8 }

mgmtVclMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtVclConfigMacTableInfoGroup,
                       mgmtVclConfigMacRowEditorInfoGroup,
                       mgmtVclConfigIpTableInfoGroup,
                       mgmtVclConfigIpRowEditorInfoGroup,
                       mgmtVclConfigProtocolProtoTableInfoGroup,
                       mgmtVclConfigProtocolProtoRowEditorInfoGroup,
                       mgmtVclConfigProtocolGroupTableInfoGroup,
                       mgmtVclConfigProtocolGroupRowEditorInfoGroup }

    ::= { mgmtVclMibCompliances 1 }

END
