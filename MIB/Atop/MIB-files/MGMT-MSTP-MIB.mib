-- *****************************************************************
-- MSTP-MIB:  
-- ****************************************************************

MGMT-MSTP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    MGMTVlanListQuarter FROM MGMT-TC
    ;

mgmtMstpMib MODULE-IDENTITY
    LAST-UPDATED "201511130000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the 802.1Q-2005 MSTP MIB"
    REVISION    "201511130000Z"
    DESCRIPTION
        "Add TCN counters"
    REVISION    "201511040000Z"
    DESCRIPTION
        "Added msti config table with msti value as the iteration key"
    REVISION    "201509290000Z"
    DESCRIPTION
        "Added range information for bridge parameters"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 20 }


MGMTMSTPForceVersion ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration control the STP protocol variant to run."
    SYNTAX      INTEGER { stp(0), rstp(2), mstp(3) }

MGMTMstpPoint2Point ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration describe the values of adminPointToPointMAC and
         operPointToPointMAC parameters. (Full duplex port administrative and
         operational status.) See 6.4.3 of IEEE Std 802.1D."
    SYNTAX      INTEGER { forceTrue(0), forceFalse(1), auto(2) }

MGMTMstpPortState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration describe the forwarding state of an interface."
    SYNTAX      INTEGER { disabled(0), discarding(1), learning(2),
                          forwarding(3) }

mgmtMstpMibObjects OBJECT IDENTIFIER
    ::= { mgmtMstpMib 1 }

mgmtMstpConfig OBJECT IDENTIFIER
    ::= { mgmtMstpMibObjects 2 }

mgmtMstpConfigBridgeParams OBJECT IDENTIFIER
    ::= { mgmtMstpConfig 1 }

mgmtMstpConfigBridgeParamsBridgeMaxAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Bridge Max Age, IEEE-802.1D-2004 sect 13.23.4"
    ::= { mgmtMstpConfigBridgeParams 1 }

mgmtMstpConfigBridgeParamsBridgeHelloTime OBJECT-TYPE
    SYNTAX      Unsigned32 (1..2)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Bridge Hello Time, 13.25.7 of IEEE-802.1Q-2005. Fixed value of two
         seconds by the standard, but this implementation allow a compatibility
         range from 1 to 2 seconds, in stipulated in 802.1Q-2005"
    ::= { mgmtMstpConfigBridgeParams 2 }

mgmtMstpConfigBridgeParamsBridgeForwardDelay OBJECT-TYPE
    SYNTAX      Unsigned32 (4..30)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Bridge Forward Delay, IEEE-802.1D-2004 sect 17.20"
    ::= { mgmtMstpConfigBridgeParams 3 }

mgmtMstpConfigBridgeParamsForceVersion OBJECT-TYPE
    SYNTAX      MGMTMSTPForceVersion
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "value of the Force Protocol Version parameter - 13.6.2 of
         IEEE-802.1Q-2005"
    ::= { mgmtMstpConfigBridgeParams 4 }

mgmtMstpConfigBridgeParamsTxHoldCount OBJECT-TYPE
    SYNTAX      Unsigned32 (1..10)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "TxHoldCount - 17.13.12 of IEEE Std 802.1D"
    ::= { mgmtMstpConfigBridgeParams 5 }

mgmtMstpConfigBridgeParamsMaxHops OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (6..40)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MaxHops - 13.22.1 of IEEE-802.1Q-2005"
    ::= { mgmtMstpConfigBridgeParams 6 }

mgmtMstpConfigBridgeParamsBpduFiltering OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "BPDU filtering for edge ports. Control whether a port explicitly
         configured as Edge will transmit and receive BPDUs"
    ::= { mgmtMstpConfigBridgeParams 7 }

mgmtMstpConfigBridgeParamsBpduGuard OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "BPDU guard for edge ports. Control whether a port explicitly configured
         as Edge will disable itself upon reception of a BPDU. The port will
         enter the error-disabled state, and will be removed from the active
         topology. "
    ::= { mgmtMstpConfigBridgeParams 8 }

mgmtMstpConfigBridgeParamsErrorRecoveryDelay OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of seconds until a STP inconsistent port is recovered. Valid
         values are zero (recovery disabled) or between 30 and 86400 (24 hours)"
    ::= { mgmtMstpConfigBridgeParams 9 }

mgmtMstpConfigMstiParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMstpConfigMstiParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of the bridge instance (MSTIs) parameters"
    ::= { mgmtMstpConfig 2 }

mgmtMstpConfigMstiParamEntry OBJECT-TYPE
    SYNTAX      MGMTMstpConfigMstiParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each MSTI has a set of parameters"
    INDEX       { mgmtMstpConfigMstiParamInstance }
    ::= { mgmtMstpConfigMstiParamTable 1 }

MGMTMstpConfigMstiParamEntry ::= SEQUENCE {
    mgmtMstpConfigMstiParamInstance  Integer32,
    mgmtMstpConfigMstiParamPriority  MGMTUnsigned8
}

mgmtMstpConfigMstiParamInstance OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Bridge instance number. The CIST = 0, MSTI1 = 1, etc"
    ::= { mgmtMstpConfigMstiParamEntry 1 }

mgmtMstpConfigMstiParamPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Bridge Priority"
    ::= { mgmtMstpConfigMstiParamEntry 2 }

mgmtMstpConfigMstiConfig OBJECT IDENTIFIER
    ::= { mgmtMstpConfig 3 }

mgmtMstpConfigMstiConfigId OBJECT IDENTIFIER
    ::= { mgmtMstpConfigMstiConfig 1 }

mgmtMstpConfigMstiConfigIdName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The configuration name"
    ::= { mgmtMstpConfigMstiConfigId 1 }

mgmtMstpConfigMstiConfigIdRevision OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The configuration revision"
    ::= { mgmtMstpConfigMstiConfigId 2 }

mgmtMstpConfigMstiConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMstpConfigMstiConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the 802.1Q - 8.9 MST Configuration table
         
         For the purposes of calculating the Configuration Digest, the MST
         Configuration Table is considered to contain 4096 consecutive single
         octet elements, where each element of the table (with the exception of
         the first and last) contains an MSTID value."
    ::= { mgmtMstpConfigMstiConfig 2 }

mgmtMstpConfigMstiConfigEntry OBJECT-TYPE
    SYNTAX      MGMTMstpConfigMstiConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The first element of the table contains the value 0, the second element
         the MSTID value corresponding to VID 1, the third element the MSTID
         value corresponding to VID 2, and so on, with the next to last element
         of the table containing the MSTID value corresponding to VID 4094, and
         the last element containing the value 0."
    INDEX       { mgmtMstpConfigMstiConfigVid }
    ::= { mgmtMstpConfigMstiConfigTable 1 }

MGMTMstpConfigMstiConfigEntry ::= SEQUENCE {
    mgmtMstpConfigMstiConfigVid    Integer32,
    mgmtMstpConfigMstiConfigMstid  MGMTUnsigned8
}

mgmtMstpConfigMstiConfigVid OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Vlan id"
    ::= { mgmtMstpConfigMstiConfigEntry 1 }

mgmtMstpConfigMstiConfigMstid OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The MSTID value associated with the vlan id"
    ::= { mgmtMstpConfigMstiConfigEntry 2 }

mgmtMstpConfigMstiConfigVlanBitmapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMstpConfigMstiConfigVlanBitmapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the 802.1Q - 8.9 MST Configuration table
         
         For the purposes of calculating the Configuration Digest, the MST
         Configuration Table is considered to contain 8 consecutive single octet
         elements, where each element of the table contains an vlan bitmap
         (vlans that have the MSTI value specified are set to one)."
    ::= { mgmtMstpConfigMstiConfig 3 }

mgmtMstpConfigMstiConfigVlanBitmapEntry OBJECT-TYPE
    SYNTAX      MGMTMstpConfigMstiConfigVlanBitmapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The first element of the table contains vlan bitmap corresponding to
         MSTI 0, the second element contains vlan bitmap corresponding to MSTI 1
         until the 7th element. MSTI range is 0-7."
    INDEX       { mgmtMstpConfigMstiConfigVlanBitmapMstiValue }
    ::= { mgmtMstpConfigMstiConfigVlanBitmapTable 1 }

MGMTMstpConfigMstiConfigVlanBitmapEntry ::= SEQUENCE {
    mgmtMstpConfigMstiConfigVlanBitmapMstiValue          Integer32,
    mgmtMstpConfigMstiConfigVlanBitmapAccessVlans0To1K   MGMTVlanListQuarter,
    mgmtMstpConfigMstiConfigVlanBitmapAccessVlans1KTo2K  MGMTVlanListQuarter,
    mgmtMstpConfigMstiConfigVlanBitmapAccessVlans2KTo3K  MGMTVlanListQuarter,
    mgmtMstpConfigMstiConfigVlanBitmapAccessVlans3KTo4K  MGMTVlanListQuarter
}

mgmtMstpConfigMstiConfigVlanBitmapMstiValue OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "MSTI value."
    ::= { mgmtMstpConfigMstiConfigVlanBitmapEntry 1 }

mgmtMstpConfigMstiConfigVlanBitmapAccessVlans0To1K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "First quarter of bit-array indicating the enabled access VLANs."
    ::= { mgmtMstpConfigMstiConfigVlanBitmapEntry 2 }

mgmtMstpConfigMstiConfigVlanBitmapAccessVlans1KTo2K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Second quarter of bit-array indicating the enabled access VLANs."
    ::= { mgmtMstpConfigMstiConfigVlanBitmapEntry 3 }

mgmtMstpConfigMstiConfigVlanBitmapAccessVlans2KTo3K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Third quarter of bit-array indicating the enabled access VLANs."
    ::= { mgmtMstpConfigMstiConfigVlanBitmapEntry 4 }

mgmtMstpConfigMstiConfigVlanBitmapAccessVlans3KTo4K OBJECT-TYPE
    SYNTAX      MGMTVlanListQuarter
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Last quarter of bit-array indicating the enabled access VLANs."
    ::= { mgmtMstpConfigMstiConfigVlanBitmapEntry 5 }

mgmtMstpConfigCistInterfaceParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMstpConfigCistInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of the CIST physical interface parameters"
    ::= { mgmtMstpConfig 4 }

mgmtMstpConfigCistInterfaceParamEntry OBJECT-TYPE
    SYNTAX      MGMTMstpConfigCistInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each CIST physical interface has a set of parameters"
    INDEX       { mgmtMstpConfigCistInterfaceParamInterfaceNo }
    ::= { mgmtMstpConfigCistInterfaceParamTable 1 }

MGMTMstpConfigCistInterfaceParamEntry ::= SEQUENCE {
    mgmtMstpConfigCistInterfaceParamInterfaceNo           MGMTInterfaceIndex,
    mgmtMstpConfigCistInterfaceParamEnable                TruthValue,
    mgmtMstpConfigCistInterfaceParamAdminEdgePort         TruthValue,
    mgmtMstpConfigCistInterfaceParamAdminAutoEdgePort     TruthValue,
    mgmtMstpConfigCistInterfaceParamAdminPointToPointMAC  MGMTMstpPoint2Point,
    mgmtMstpConfigCistInterfaceParamRestrictedRole        TruthValue,
    mgmtMstpConfigCistInterfaceParamRestrictedTcn         TruthValue,
    mgmtMstpConfigCistInterfaceParamBpduGuard             TruthValue
}

mgmtMstpConfigCistInterfaceParamInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtMstpConfigCistInterfaceParamEntry 1 }

mgmtMstpConfigCistInterfaceParamEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Control whether port is controlled by xSTP. If disabled, the port
         forwarding state follow the MAC state"
    ::= { mgmtMstpConfigCistInterfaceParamEntry 2 }

mgmtMstpConfigCistInterfaceParamAdminEdgePort OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "adminEdgePort parameter - 18.3.3 of IEEE Std 802.1D"
    ::= { mgmtMstpConfigCistInterfaceParamEntry 3 }

mgmtMstpConfigCistInterfaceParamAdminAutoEdgePort OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "adminAutoEdgePort parameter - 17.13.3 of IEEE Std 802.1D"
    ::= { mgmtMstpConfigCistInterfaceParamEntry 4 }

mgmtMstpConfigCistInterfaceParamAdminPointToPointMAC OBJECT-TYPE
    SYNTAX      MGMTMstpPoint2Point
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "adminPointToPointMAC parameter - 6.4.3 of IEEE Std 802.1D"
    ::= { mgmtMstpConfigCistInterfaceParamEntry 5 }

mgmtMstpConfigCistInterfaceParamRestrictedRole OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "restrictedRole parameter - 13.25.14 of IEEE Std 802.1Q-2005"
    ::= { mgmtMstpConfigCistInterfaceParamEntry 6 }

mgmtMstpConfigCistInterfaceParamRestrictedTcn OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "restrictedRole parameter - 13.25.15 of IEEE Std 802.1Q-2005"
    ::= { mgmtMstpConfigCistInterfaceParamEntry 7 }

mgmtMstpConfigCistInterfaceParamBpduGuard OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If enabled, causes the port to disable itself upon receiving valid
         BPDU's. Contrary to the similar bridge setting, the port Edge status
         does not effect this setting. A port entering error-disabled state due
         to this setting is subject to the bridge ErrorRecoveryDelay setting as
         well"
    ::= { mgmtMstpConfigCistInterfaceParamEntry 8 }

mgmtMstpConfigAggrParams OBJECT IDENTIFIER
    ::= { mgmtMstpConfig 5 }

mgmtMstpConfigAggrParamsEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Control whether port is controlled by xSTP. If disabled, the port
         forwarding state follow the MAC state"
    ::= { mgmtMstpConfigAggrParams 1 }

mgmtMstpConfigAggrParamsAdminEdgePort OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "adminEdgePort parameter - 18.3.3 of IEEE Std 802.1D"
    ::= { mgmtMstpConfigAggrParams 2 }

mgmtMstpConfigAggrParamsAdminAutoEdgePort OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "adminAutoEdgePort parameter - 17.13.3 of IEEE Std 802.1D"
    ::= { mgmtMstpConfigAggrParams 3 }

mgmtMstpConfigAggrParamsAdminPointToPointMAC OBJECT-TYPE
    SYNTAX      MGMTMstpPoint2Point
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "adminPointToPointMAC parameter - 6.4.3 of IEEE Std 802.1D"
    ::= { mgmtMstpConfigAggrParams 4 }

mgmtMstpConfigAggrParamsRestrictedRole OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "restrictedRole parameter - 13.25.14 of IEEE Std 802.1Q-2005"
    ::= { mgmtMstpConfigAggrParams 5 }

mgmtMstpConfigAggrParamsRestrictedTcn OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "restrictedRole parameter - 13.25.15 of IEEE Std 802.1Q-2005"
    ::= { mgmtMstpConfigAggrParams 6 }

mgmtMstpConfigAggrParamsBpduGuard OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If enabled, causes the port to disable itself upon receiving valid
         BPDU's. Contrary to the similar bridge setting, the port Edge status
         does not effect this setting. A port entering error-disabled state due
         to this setting is subject to the bridge ErrorRecoveryDelay setting as
         well"
    ::= { mgmtMstpConfigAggrParams 7 }

mgmtMstpConfigMstiInterfaceParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMstpConfigMstiInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of the MSTI interface parameters"
    ::= { mgmtMstpConfig 6 }

mgmtMstpConfigMstiInterfaceParamEntry OBJECT-TYPE
    SYNTAX      MGMTMstpConfigMstiInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each MSTI interface has a set of parameters"
    INDEX       { mgmtMstpConfigMstiInterfaceParamInterfaceNo,
                  mgmtMstpConfigMstiInterfaceParamInstance }
    ::= { mgmtMstpConfigMstiInterfaceParamTable 1 }

MGMTMstpConfigMstiInterfaceParamEntry ::= SEQUENCE {
    mgmtMstpConfigMstiInterfaceParamInterfaceNo        MGMTInterfaceIndex,
    mgmtMstpConfigMstiInterfaceParamInstance           Integer32,
    mgmtMstpConfigMstiInterfaceParamAdminPathCost      Unsigned32,
    mgmtMstpConfigMstiInterfaceParamAdminPortPriority  MGMTUnsigned8
}

mgmtMstpConfigMstiInterfaceParamInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtMstpConfigMstiInterfaceParamEntry 1 }

mgmtMstpConfigMstiInterfaceParamInstance OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Bridge instance number. The CIST = 0, MSTI1 = 1, etc"
    ::= { mgmtMstpConfigMstiInterfaceParamEntry 2 }

mgmtMstpConfigMstiInterfaceParamAdminPathCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Path Cost - 13.37.1 of 802.1Q-2005"
    ::= { mgmtMstpConfigMstiInterfaceParamEntry 3 }

mgmtMstpConfigMstiInterfaceParamAdminPortPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "priority field for the Port Identifier - 13.24.12 of 802.1Q-2005"
    ::= { mgmtMstpConfigMstiInterfaceParamEntry 4 }

mgmtMstpConfigMstiAggrParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMstpConfigMstiAggrParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of the MSTI aggregations parameters"
    ::= { mgmtMstpConfig 7 }

mgmtMstpConfigMstiAggrParamEntry OBJECT-TYPE
    SYNTAX      MGMTMstpConfigMstiAggrParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The aggregations for each MSTI has a set of parameters"
    INDEX       { mgmtMstpConfigMstiAggrParamInstance }
    ::= { mgmtMstpConfigMstiAggrParamTable 1 }

MGMTMstpConfigMstiAggrParamEntry ::= SEQUENCE {
    mgmtMstpConfigMstiAggrParamInstance           Integer32,
    mgmtMstpConfigMstiAggrParamAdminPathCost      Unsigned32,
    mgmtMstpConfigMstiAggrParamAdminPortPriority  MGMTUnsigned8
}

mgmtMstpConfigMstiAggrParamInstance OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Bridge instance number. The CIST = 0, MSTI1 = 1, etc"
    ::= { mgmtMstpConfigMstiAggrParamEntry 1 }

mgmtMstpConfigMstiAggrParamAdminPathCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Path Cost - 13.37.1 of 802.1Q-2005"
    ::= { mgmtMstpConfigMstiAggrParamEntry 3 }

mgmtMstpConfigMstiAggrParamAdminPortPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "priority field for the Port Identifier - 13.24.12 of 802.1Q-2005"
    ::= { mgmtMstpConfigMstiAggrParamEntry 4 }

mgmtMstpStatus OBJECT IDENTIFIER
    ::= { mgmtMstpMibObjects 3 }

mgmtMstpStatusBridgeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMstpStatusBridgeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represent the status of the bridge instances"
    ::= { mgmtMstpStatus 1 }

mgmtMstpStatusBridgeEntry OBJECT-TYPE
    SYNTAX      MGMTMstpStatusBridgeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A MSTP Bridge instance set of status objects"
    INDEX       { mgmtMstpStatusBridgeInstance }
    ::= { mgmtMstpStatusBridgeTable 1 }

MGMTMstpStatusBridgeEntry ::= SEQUENCE {
    mgmtMstpStatusBridgeInstance                 Integer32,
    mgmtMstpStatusBridgeBridgeId                 OCTET STRING,
    mgmtMstpStatusBridgeTimeSinceTopologyChange  Unsigned32,
    mgmtMstpStatusBridgeTopologyChangeCount      Unsigned32,
    mgmtMstpStatusBridgeTopologyChange           TruthValue,
    mgmtMstpStatusBridgeDesignatedRoot           OCTET STRING,
    mgmtMstpStatusBridgeRootPathCost             Unsigned32,
    mgmtMstpStatusBridgeRootPort                 Unsigned32,
    mgmtMstpStatusBridgeMaxAge                   Unsigned32,
    mgmtMstpStatusBridgeForwardDelay             Unsigned32,
    mgmtMstpStatusBridgeBridgeMaxAge             Unsigned32,
    mgmtMstpStatusBridgeBridgeHelloTime          Unsigned32,
    mgmtMstpStatusBridgeBridgeForwardDelay       Unsigned32,
    mgmtMstpStatusBridgeTxHoldCount              Unsigned32,
    mgmtMstpStatusBridgeForceVersion             MGMTMSTPForceVersion,
    mgmtMstpStatusBridgeCistRegionalRoot         OCTET STRING,
    mgmtMstpStatusBridgeCistInternalPathCost     Unsigned32,
    mgmtMstpStatusBridgeMaxHops                  MGMTUnsigned8
}

mgmtMstpStatusBridgeInstance OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Bridge instance number. The CIST = 0, MSTI1 = 1, etc"
    ::= { mgmtMstpStatusBridgeEntry 1 }

mgmtMstpStatusBridgeBridgeId OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Bridge Identifier of this bridge"
    ::= { mgmtMstpStatusBridgeEntry 2 }

mgmtMstpStatusBridgeTimeSinceTopologyChange OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The count in seconds of the time elapsed since the Topology Change flag
         was last True"
    ::= { mgmtMstpStatusBridgeEntry 3 }

mgmtMstpStatusBridgeTopologyChangeCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The count of the times the Topology Change flag parameter for the
         Bridge has been set since the Bridge was powered on or initialized"
    ::= { mgmtMstpStatusBridgeEntry 4 }

mgmtMstpStatusBridgeTopologyChange OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Topology Change Flag current status"
    ::= { mgmtMstpStatusBridgeEntry 5 }

mgmtMstpStatusBridgeDesignatedRoot OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Designated Root Bridge"
    ::= { mgmtMstpStatusBridgeEntry 6 }

mgmtMstpStatusBridgeRootPathCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Root Path Cost"
    ::= { mgmtMstpStatusBridgeEntry 7 }

mgmtMstpStatusBridgeRootPort OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Root Port"
    ::= { mgmtMstpStatusBridgeEntry 8 }

mgmtMstpStatusBridgeMaxAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Max Age, IEEE-802.1D-2004 sect 13.23.7"
    ::= { mgmtMstpStatusBridgeEntry 9 }

mgmtMstpStatusBridgeForwardDelay OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Forward Delay, IEEE-802.1D-2004 sect 13.23.7"
    ::= { mgmtMstpStatusBridgeEntry 10 }

mgmtMstpStatusBridgeBridgeMaxAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Bridge Max Age, IEEE-802.1D-2004 sect 13.23.4"
    ::= { mgmtMstpStatusBridgeEntry 11 }

mgmtMstpStatusBridgeBridgeHelloTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Bridge Hello Time, IEEE-802.1D-2004 sect 13.23.4"
    ::= { mgmtMstpStatusBridgeEntry 12 }

mgmtMstpStatusBridgeBridgeForwardDelay OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Bridge Forward Delay, IEEE-802.1D-2004 sect 13.23.4"
    ::= { mgmtMstpStatusBridgeEntry 13 }

mgmtMstpStatusBridgeTxHoldCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Hold Time / Transmission Limit, IEEE-802.1D-2004 sect 13.22"
    ::= { mgmtMstpStatusBridgeEntry 14 }

mgmtMstpStatusBridgeForceVersion OBJECT-TYPE
    SYNTAX      MGMTMSTPForceVersion
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Value of the Force Protocol Version parameter - IEEE-802.1D-2004 sect
         17.16.1"
    ::= { mgmtMstpStatusBridgeEntry 15 }

mgmtMstpStatusBridgeCistRegionalRoot OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "CIST Regional Root Identifier (13.16.4)"
    ::= { mgmtMstpStatusBridgeEntry 16 }

mgmtMstpStatusBridgeCistInternalPathCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "CIST Path Cost"
    ::= { mgmtMstpStatusBridgeEntry 17 }

mgmtMstpStatusBridgeMaxHops OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "MaxHops (13.22.1)"
    ::= { mgmtMstpStatusBridgeEntry 18 }

mgmtMstpStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMstpStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represent the status of the interface instances"
    ::= { mgmtMstpStatus 2 }

mgmtMstpStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTMstpStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A MSTP interface instance set of status objects"
    INDEX       { mgmtMstpStatusInterfaceInterfaceNo,
                  mgmtMstpStatusInterfaceInstance }
    ::= { mgmtMstpStatusInterfaceTable 1 }

MGMTMstpStatusInterfaceEntry ::= SEQUENCE {
    mgmtMstpStatusInterfaceInterfaceNo           MGMTInterfaceIndex,
    mgmtMstpStatusInterfaceInstance              Integer32,
    mgmtMstpStatusInterfaceEnabled               TruthValue,
    mgmtMstpStatusInterfaceActive                TruthValue,
    mgmtMstpStatusInterfaceParentPort            Unsigned32,
    mgmtMstpStatusInterfaceUpTime                Unsigned32,
    mgmtMstpStatusInterfacePortState             MGMTMstpPortState,
    mgmtMstpStatusInterfacePortId                OCTET STRING,
    mgmtMstpStatusInterfacePathCost              Unsigned32,
    mgmtMstpStatusInterfaceDesignatedRoot        OCTET STRING,
    mgmtMstpStatusInterfaceDesignatedCost        Unsigned32,
    mgmtMstpStatusInterfaceDesignatedBridge      OCTET STRING,
    mgmtMstpStatusInterfaceDesignatedPort        OCTET STRING,
    mgmtMstpStatusInterfaceTcAck                 TruthValue,
    mgmtMstpStatusInterfaceHelloTime             Unsigned32,
    mgmtMstpStatusInterfaceAdminEdgePort         TruthValue,
    mgmtMstpStatusInterfaceOperEdgePort          TruthValue,
    mgmtMstpStatusInterfaceAutoEdgePort          TruthValue,
    mgmtMstpStatusInterfaceMacOperational        TruthValue,
    mgmtMstpStatusInterfaceAdminPointToPointMAC  MGMTMstpPoint2Point,
    mgmtMstpStatusInterfaceOperPointToPointMAC   TruthValue,
    mgmtMstpStatusInterfaceRestrictedRole        TruthValue,
    mgmtMstpStatusInterfaceRestrictedTcn         TruthValue,
    mgmtMstpStatusInterfacePortRole              MGMTDisplayString,
    mgmtMstpStatusInterfaceDisputed              TruthValue
}

mgmtMstpStatusInterfaceInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtMstpStatusInterfaceEntry 1 }

mgmtMstpStatusInterfaceInstance OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Bridge instance number. The CIST = 0, MSTI1 = 1, etc"
    ::= { mgmtMstpStatusInterfaceEntry 2 }

mgmtMstpStatusInterfaceEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether port is controlled by xSTP"
    ::= { mgmtMstpStatusInterfaceEntry 3 }

mgmtMstpStatusInterfaceActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether port is active"
    ::= { mgmtMstpStatusInterfaceEntry 4 }

mgmtMstpStatusInterfaceParentPort OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Parent port if physical port is aggregated. (Otherwise 0xffff)"
    ::= { mgmtMstpStatusInterfaceEntry 5 }

mgmtMstpStatusInterfaceUpTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "seconds of the time elapsed since the Port was last reset or
         initialized"
    ::= { mgmtMstpStatusInterfaceEntry 6 }

mgmtMstpStatusInterfacePortState OBJECT-TYPE
    SYNTAX      MGMTMstpPortState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current state of the Port"
    ::= { mgmtMstpStatusInterfaceEntry 7 }

mgmtMstpStatusInterfacePortId OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(2))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Unique Port identifier comprising two parts, the Port Number and the
         Port Priority field"
    ::= { mgmtMstpStatusInterfaceEntry 8 }

mgmtMstpStatusInterfacePathCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Path Cost (17.16.5 of IEEE Std 802.1D)"
    ::= { mgmtMstpStatusInterfaceEntry 9 }

mgmtMstpStatusInterfaceDesignatedRoot OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Designated Root"
    ::= { mgmtMstpStatusInterfaceEntry 10 }

mgmtMstpStatusInterfaceDesignatedCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Designated Cost"
    ::= { mgmtMstpStatusInterfaceEntry 11 }

mgmtMstpStatusInterfaceDesignatedBridge OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(8))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Designated Bridge"
    ::= { mgmtMstpStatusInterfaceEntry 12 }

mgmtMstpStatusInterfaceDesignatedPort OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(2))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Designated Port"
    ::= { mgmtMstpStatusInterfaceEntry 13 }

mgmtMstpStatusInterfaceTcAck OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Topology Change Acknowledge"
    ::= { mgmtMstpStatusInterfaceEntry 14 }

mgmtMstpStatusInterfaceHelloTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Hello Time"
    ::= { mgmtMstpStatusInterfaceEntry 15 }

mgmtMstpStatusInterfaceAdminEdgePort OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "adminEdgePort (18.3.3 of IEEE Std 802.1D)"
    ::= { mgmtMstpStatusInterfaceEntry 16 }

mgmtMstpStatusInterfaceOperEdgePort OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "operEdgePort (18.3.4 of IEEE Std 802.1D)"
    ::= { mgmtMstpStatusInterfaceEntry 17 }

mgmtMstpStatusInterfaceAutoEdgePort OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "autoEdgePort (17.13.3 of IEEE Std 802.1D)"
    ::= { mgmtMstpStatusInterfaceEntry 18 }

mgmtMstpStatusInterfaceMacOperational OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current state of the MAC Operational parameter (6.4.2 of IEEE Std
         802.1D,)"
    ::= { mgmtMstpStatusInterfaceEntry 19 }

mgmtMstpStatusInterfaceAdminPointToPointMAC OBJECT-TYPE
    SYNTAX      MGMTMstpPoint2Point
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current state of the adminPointToPointMAC parameter (6.4.3 of IEEE Std
         802.1D)"
    ::= { mgmtMstpStatusInterfaceEntry 20 }

mgmtMstpStatusInterfaceOperPointToPointMAC OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current state of the operPointToPointMAC parameter (6.4.3 of IEEE Std
         802.1D)"
    ::= { mgmtMstpStatusInterfaceEntry 21 }

mgmtMstpStatusInterfaceRestrictedRole OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current state of the restrictedRole parameter for the Port (13.25.14)"
    ::= { mgmtMstpStatusInterfaceEntry 22 }

mgmtMstpStatusInterfaceRestrictedTcn OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current state of the restrictedTcn parameter for the Port (13.25.15)"
    ::= { mgmtMstpStatusInterfaceEntry 23 }

mgmtMstpStatusInterfacePortRole OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..15))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current Port Role"
    ::= { mgmtMstpStatusInterfaceEntry 24 }

mgmtMstpStatusInterfaceDisputed OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current value of the disputed variable for the CIST Port"
    ::= { mgmtMstpStatusInterfaceEntry 25 }

mgmtMstpStatusInterfaceStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMstpStatusInterfaceStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represent the statistics of the CIST interfaces"
    ::= { mgmtMstpStatus 3 }

mgmtMstpStatusInterfaceStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTMstpStatusInterfaceStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "A CIST interface set of statistics"
    INDEX       { mgmtMstpStatusInterfaceStatisticsInterfaceNo }
    ::= { mgmtMstpStatusInterfaceStatisticsTable 1 }

MGMTMstpStatusInterfaceStatisticsEntry ::= SEQUENCE {
    mgmtMstpStatusInterfaceStatisticsInterfaceNo            MGMTInterfaceIndex,
    mgmtMstpStatusInterfaceStatisticsStpFrameXmits          Unsigned32,
    mgmtMstpStatusInterfaceStatisticsStpFrameReceived       Unsigned32,
    mgmtMstpStatusInterfaceStatisticsRstpFrameXmits         Unsigned32,
    mgmtMstpStatusInterfaceStatisticsRstpFrameReceived      Unsigned32,
    mgmtMstpStatusInterfaceStatisticsMstpFrameXmits         Unsigned32,
    mgmtMstpStatusInterfaceStatisticsMstpFrameReceived      Unsigned32,
    mgmtMstpStatusInterfaceStatisticsUnknownFramesReceived  Unsigned32,
    mgmtMstpStatusInterfaceStatisticsIllegalFrameReceived   Unsigned32,
    mgmtMstpStatusInterfaceStatisticsTcnFrameXmits          Unsigned32,
    mgmtMstpStatusInterfaceStatisticsTcnFrameReceived       Unsigned32
}

mgmtMstpStatusInterfaceStatisticsInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 1 }

mgmtMstpStatusInterfaceStatisticsStpFrameXmits OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of STP frames transmitted"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 2 }

mgmtMstpStatusInterfaceStatisticsStpFrameReceived OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of STP frames received"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 3 }

mgmtMstpStatusInterfaceStatisticsRstpFrameXmits OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of RSTP frames transmitted"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 4 }

mgmtMstpStatusInterfaceStatisticsRstpFrameReceived OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of RSTP frames received"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 5 }

mgmtMstpStatusInterfaceStatisticsMstpFrameXmits OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of MSTP frames transmitted"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 6 }

mgmtMstpStatusInterfaceStatisticsMstpFrameReceived OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of MSTP frames received"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 7 }

mgmtMstpStatusInterfaceStatisticsUnknownFramesReceived OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of unknown frames received and discarded in error"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 8 }

mgmtMstpStatusInterfaceStatisticsIllegalFrameReceived OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of illegal frames received and discarded in error"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 9 }

mgmtMstpStatusInterfaceStatisticsTcnFrameXmits OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of TCN frames transmitted"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 10 }

mgmtMstpStatusInterfaceStatisticsTcnFrameReceived OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of TCN frames received"
    ::= { mgmtMstpStatusInterfaceStatisticsEntry 11 }

mgmtMstpMibConformance OBJECT IDENTIFIER
    ::= { mgmtMstpMib 2 }

mgmtMstpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtMstpMibConformance 1 }

mgmtMstpMibGroups OBJECT IDENTIFIER
    ::= { mgmtMstpMibConformance 2 }

mgmtMstpConfigBridgeParamsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpConfigBridgeParamsBridgeMaxAge,
                  mgmtMstpConfigBridgeParamsBridgeHelloTime,
                  mgmtMstpConfigBridgeParamsBridgeForwardDelay,
                  mgmtMstpConfigBridgeParamsForceVersion,
                  mgmtMstpConfigBridgeParamsTxHoldCount,
                  mgmtMstpConfigBridgeParamsMaxHops,
                  mgmtMstpConfigBridgeParamsBpduFiltering,
                  mgmtMstpConfigBridgeParamsBpduGuard,
                  mgmtMstpConfigBridgeParamsErrorRecoveryDelay }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 1 }

mgmtMstpConfigMstiParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpConfigMstiParamInstance,
                  mgmtMstpConfigMstiParamPriority }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 2 }

mgmtMstpConfigMstiConfigIdInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpConfigMstiConfigIdName,
                  mgmtMstpConfigMstiConfigIdRevision }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 3 }

mgmtMstpConfigMstiConfigTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpConfigMstiConfigVid,
                  mgmtMstpConfigMstiConfigMstid }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 4 }

mgmtMstpConfigMstiConfigVlanBitmapTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpConfigMstiConfigVlanBitmapMstiValue,
                  mgmtMstpConfigMstiConfigVlanBitmapAccessVlans0To1K,
                  mgmtMstpConfigMstiConfigVlanBitmapAccessVlans1KTo2K,
                  mgmtMstpConfigMstiConfigVlanBitmapAccessVlans2KTo3K,
                  mgmtMstpConfigMstiConfigVlanBitmapAccessVlans3KTo4K }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 5 }

mgmtMstpConfigCistInterfaceParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpConfigCistInterfaceParamInterfaceNo,
                  mgmtMstpConfigCistInterfaceParamEnable,
                  mgmtMstpConfigCistInterfaceParamAdminEdgePort,
                  mgmtMstpConfigCistInterfaceParamAdminAutoEdgePort,
                  mgmtMstpConfigCistInterfaceParamAdminPointToPointMAC,
                  mgmtMstpConfigCistInterfaceParamRestrictedRole,
                  mgmtMstpConfigCistInterfaceParamRestrictedTcn,
                  mgmtMstpConfigCistInterfaceParamBpduGuard }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 6 }

mgmtMstpConfigAggrParamsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpConfigAggrParamsEnable,
                  mgmtMstpConfigAggrParamsAdminEdgePort,
                  mgmtMstpConfigAggrParamsAdminAutoEdgePort,
                  mgmtMstpConfigAggrParamsAdminPointToPointMAC,
                  mgmtMstpConfigAggrParamsRestrictedRole,
                  mgmtMstpConfigAggrParamsRestrictedTcn,
                  mgmtMstpConfigAggrParamsBpduGuard }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 7 }

mgmtMstpConfigMstiInterfaceParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpConfigMstiInterfaceParamInterfaceNo,
                  mgmtMstpConfigMstiInterfaceParamInstance,
                  mgmtMstpConfigMstiInterfaceParamAdminPathCost,
                  mgmtMstpConfigMstiInterfaceParamAdminPortPriority }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 8 }

mgmtMstpConfigMstiAggrParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpConfigMstiAggrParamInstance,
                  mgmtMstpConfigMstiAggrParamAdminPathCost,
                  mgmtMstpConfigMstiAggrParamAdminPortPriority }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 9 }

mgmtMstpStatusBridgeTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpStatusBridgeInstance,
                  mgmtMstpStatusBridgeBridgeId,
                  mgmtMstpStatusBridgeTimeSinceTopologyChange,
                  mgmtMstpStatusBridgeTopologyChangeCount,
                  mgmtMstpStatusBridgeTopologyChange,
                  mgmtMstpStatusBridgeDesignatedRoot,
                  mgmtMstpStatusBridgeRootPathCost,
                  mgmtMstpStatusBridgeRootPort,
                  mgmtMstpStatusBridgeMaxAge,
                  mgmtMstpStatusBridgeForwardDelay,
                  mgmtMstpStatusBridgeBridgeMaxAge,
                  mgmtMstpStatusBridgeBridgeHelloTime,
                  mgmtMstpStatusBridgeBridgeForwardDelay,
                  mgmtMstpStatusBridgeTxHoldCount,
                  mgmtMstpStatusBridgeForceVersion,
                  mgmtMstpStatusBridgeCistRegionalRoot,
                  mgmtMstpStatusBridgeCistInternalPathCost,
                  mgmtMstpStatusBridgeMaxHops }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 10 }

mgmtMstpStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpStatusInterfaceInterfaceNo,
                  mgmtMstpStatusInterfaceInstance,
                  mgmtMstpStatusInterfaceEnabled,
                  mgmtMstpStatusInterfaceActive,
                  mgmtMstpStatusInterfaceParentPort,
                  mgmtMstpStatusInterfaceUpTime,
                  mgmtMstpStatusInterfacePortState,
                  mgmtMstpStatusInterfacePortId,
                  mgmtMstpStatusInterfacePathCost,
                  mgmtMstpStatusInterfaceDesignatedRoot,
                  mgmtMstpStatusInterfaceDesignatedCost,
                  mgmtMstpStatusInterfaceDesignatedBridge,
                  mgmtMstpStatusInterfaceDesignatedPort,
                  mgmtMstpStatusInterfaceTcAck,
                  mgmtMstpStatusInterfaceHelloTime,
                  mgmtMstpStatusInterfaceAdminEdgePort,
                  mgmtMstpStatusInterfaceOperEdgePort,
                  mgmtMstpStatusInterfaceAutoEdgePort,
                  mgmtMstpStatusInterfaceMacOperational,
                  mgmtMstpStatusInterfaceAdminPointToPointMAC,
                  mgmtMstpStatusInterfaceOperPointToPointMAC,
                  mgmtMstpStatusInterfaceRestrictedRole,
                  mgmtMstpStatusInterfaceRestrictedTcn,
                  mgmtMstpStatusInterfacePortRole,
                  mgmtMstpStatusInterfaceDisputed }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 11 }

mgmtMstpStatusInterfaceStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMstpStatusInterfaceStatisticsInterfaceNo,
                  mgmtMstpStatusInterfaceStatisticsStpFrameXmits,
                  mgmtMstpStatusInterfaceStatisticsStpFrameReceived,
                  mgmtMstpStatusInterfaceStatisticsRstpFrameXmits,
                  mgmtMstpStatusInterfaceStatisticsRstpFrameReceived,
                  mgmtMstpStatusInterfaceStatisticsMstpFrameXmits,
                  mgmtMstpStatusInterfaceStatisticsMstpFrameReceived,
                  mgmtMstpStatusInterfaceStatisticsUnknownFramesReceived,
                  mgmtMstpStatusInterfaceStatisticsIllegalFrameReceived,
                  mgmtMstpStatusInterfaceStatisticsTcnFrameXmits,
                  mgmtMstpStatusInterfaceStatisticsTcnFrameReceived }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMstpMibGroups 12 }

mgmtMstpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtMstpConfigBridgeParamsInfoGroup,
                       mgmtMstpConfigMstiParamTableInfoGroup,
                       mgmtMstpConfigMstiConfigIdInfoGroup,
                       mgmtMstpConfigMstiConfigTableInfoGroup,
                       mgmtMstpConfigMstiConfigVlanBitmapTableInfoGroup,
                       mgmtMstpConfigCistInterfaceParamTableInfoGroup,
                       mgmtMstpConfigAggrParamsInfoGroup,
                       mgmtMstpConfigMstiInterfaceParamTableInfoGroup,
                       mgmtMstpConfigMstiAggrParamTableInfoGroup,
                       mgmtMstpStatusBridgeTableInfoGroup,
                       mgmtMstpStatusInterfaceTableInfoGroup,
                       mgmtMstpStatusInterfaceStatisticsTableInfoGroup }

    ::= { mgmtMstpMibCompliances 1 }

END
