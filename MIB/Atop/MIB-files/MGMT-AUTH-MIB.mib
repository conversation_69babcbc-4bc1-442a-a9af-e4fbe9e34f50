-- *****************************************************************
-- AUTH-MIB:  
-- ****************************************************************

MGMT-AUTH-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtAuthMib MODULE-IDENTITY
    LAST-UPDATED "201701160000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private MIB for authentication"
    REVISION    "201701160000Z"
    DESCRIPTION
        "Update the valid range of authentication method priority index"
    REVISION    "201603210000Z"
    DESCRIPTION
        "Support the encrypted secret key"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 48 }


MGMTAuthAcctMethod ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the available accounting methods."
    SYNTAX      INTEGER { none(0), tacacs(3) }

MGMTAuthAuthenMethod ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the available authentication methods."
    SYNTAX      INTEGER { none(0), local(1), radius(2), tacacs(3) }

MGMTAuthAuthorMethod ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the available authorization methods."
    SYNTAX      INTEGER { none(0), tacacs(3) }

mgmtAuthMibObjects OBJECT IDENTIFIER
    ::= { mgmtAuthMib 1 }

mgmtAuthConfig OBJECT IDENTIFIER
    ::= { mgmtAuthMibObjects 2 }

mgmtAuthConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtAuthConfig 1 }

mgmtAuthConfigGlobalsAgents OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobals 1 }

mgmtAuthConfigGlobalsAgentsConsoleAuthen OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 1 }

mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAuthConfigGlobalsAgentsConsoleAuthenMethodsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is an ordered table of methods used to authenticate console access"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAuthen 1 }

mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsEntry OBJECT-TYPE
    SYNTAX      MGMTAuthConfigGlobalsAgentsConsoleAuthenMethodsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry defines a method to be consulted with a priorty equal to the
         index"
    INDEX       {                   mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsIndex }
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsTable 1 }

MGMTAuthConfigGlobalsAgentsConsoleAuthenMethodsEntry ::= SEQUENCE {
    mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsIndex   Integer32,
    mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsMethod  MGMTAuthAuthenMethod
}

mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Method priority index, from 0 to 2 where 0 is the highest priority
         index"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsEntry 1 }

mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAuthenMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Authentication method"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsEntry 2 }

mgmtAuthConfigGlobalsAgentsTelnetAuthen OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 2 }

mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAuthConfigGlobalsAgentsTelnetAuthenMethodsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is an ordered table of methods used to authenticate telnet access"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAuthen 1 }

mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsEntry OBJECT-TYPE
    SYNTAX      MGMTAuthConfigGlobalsAgentsTelnetAuthenMethodsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry defines a method to be consulted with a priorty equal to the
         index"
    INDEX       {                   mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsIndex }
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsTable 1 }

MGMTAuthConfigGlobalsAgentsTelnetAuthenMethodsEntry ::= SEQUENCE {
    mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsIndex   Integer32,
    mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsMethod  MGMTAuthAuthenMethod
}

mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Method priority index, from 0 to 2 where 0 is the highest priority
         index"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsEntry 1 }

mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAuthenMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Authentication method"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsEntry 2 }

mgmtAuthConfigGlobalsAgentsSshAuthen OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 3 }

mgmtAuthConfigGlobalsAgentsSshAuthenMethodsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAuthConfigGlobalsAgentsSshAuthenMethodsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is an ordered table of methods used to authenticate ssh access"
    ::= { mgmtAuthConfigGlobalsAgentsSshAuthen 1 }

mgmtAuthConfigGlobalsAgentsSshAuthenMethodsEntry OBJECT-TYPE
    SYNTAX      MGMTAuthConfigGlobalsAgentsSshAuthenMethodsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry defines a method to be consulted with a priorty equal to the
         index"
    INDEX       { mgmtAuthConfigGlobalsAgentsSshAuthenMethodsIndex }
    ::= { mgmtAuthConfigGlobalsAgentsSshAuthenMethodsTable 1 }

MGMTAuthConfigGlobalsAgentsSshAuthenMethodsEntry ::= SEQUENCE {
    mgmtAuthConfigGlobalsAgentsSshAuthenMethodsIndex   Integer32,
    mgmtAuthConfigGlobalsAgentsSshAuthenMethodsMethod  MGMTAuthAuthenMethod
}

mgmtAuthConfigGlobalsAgentsSshAuthenMethodsIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Method priority index, from 0 to 2 where 0 is the highest priority
         index"
    ::= { mgmtAuthConfigGlobalsAgentsSshAuthenMethodsEntry 1 }

mgmtAuthConfigGlobalsAgentsSshAuthenMethodsMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAuthenMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Authentication method"
    ::= { mgmtAuthConfigGlobalsAgentsSshAuthenMethodsEntry 2 }

mgmtAuthConfigGlobalsAgentsHttpAuthen OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 4 }

mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAuthConfigGlobalsAgentsHttpAuthenMethodsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is an ordered table of methods used to authenticate HTTP access"
    ::= { mgmtAuthConfigGlobalsAgentsHttpAuthen 1 }

mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsEntry OBJECT-TYPE
    SYNTAX      MGMTAuthConfigGlobalsAgentsHttpAuthenMethodsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry defines a method to be consulted with a priorty equal to the
         index"
    INDEX       { mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsIndex }
    ::= { mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsTable 1 }

MGMTAuthConfigGlobalsAgentsHttpAuthenMethodsEntry ::= SEQUENCE {
    mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsIndex   Integer32,
    mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsMethod  MGMTAuthAuthenMethod
}

mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Method priority index, from 0 to 2 where 0 is the highest priority
         index"
    ::= { mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsEntry 1 }

mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAuthenMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Authentication method"
    ::= { mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsEntry 2 }

mgmtAuthConfigGlobalsAgentsConsoleAuthor OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 11 }

mgmtAuthConfigGlobalsAgentsConsoleAuthorMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAuthorMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Authorization method"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAuthor 1 }

mgmtAuthConfigGlobalsAgentsConsoleAuthorCmdEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable authorization of commands"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAuthor 2 }

mgmtAuthConfigGlobalsAgentsConsoleAuthorCmdPrivLvl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Command privilege level. Authorize all commands with a privilege level
         higher than or equal to this level. Valid values are in the range 0 to
         15"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAuthor 3 }

mgmtAuthConfigGlobalsAgentsConsoleAuthorCfgCmdEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Also authorize configuration commands"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAuthor 4 }

mgmtAuthConfigGlobalsAgentsTelnetAuthor OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 12 }

mgmtAuthConfigGlobalsAgentsTelnetAuthorMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAuthorMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Authorization method"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAuthor 1 }

mgmtAuthConfigGlobalsAgentsTelnetAuthorCmdEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable authorization of commands"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAuthor 2 }

mgmtAuthConfigGlobalsAgentsTelnetAuthorCmdPrivLvl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Command privilege level. Authorize all commands with a privilege level
         higher than or equal to this level. Valid values are in the range 0 to
         15"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAuthor 3 }

mgmtAuthConfigGlobalsAgentsTelnetAuthorCfgCmdEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Also authorize configuration commands"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAuthor 4 }

mgmtAuthConfigGlobalsAgentsSshAuthor OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 13 }

mgmtAuthConfigGlobalsAgentsSshAuthorMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAuthorMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Authorization method"
    ::= { mgmtAuthConfigGlobalsAgentsSshAuthor 1 }

mgmtAuthConfigGlobalsAgentsSshAuthorCmdEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable authorization of commands"
    ::= { mgmtAuthConfigGlobalsAgentsSshAuthor 2 }

mgmtAuthConfigGlobalsAgentsSshAuthorCmdPrivLvl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Command privilege level. Authorize all commands with a privilege level
         higher than or equal to this level. Valid values are in the range 0 to
         15"
    ::= { mgmtAuthConfigGlobalsAgentsSshAuthor 3 }

mgmtAuthConfigGlobalsAgentsSshAuthorCfgCmdEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Also authorize configuration commands"
    ::= { mgmtAuthConfigGlobalsAgentsSshAuthor 4 }

mgmtAuthConfigGlobalsAgentsConsoleAcct OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 21 }

mgmtAuthConfigGlobalsAgentsConsoleAcctMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAcctMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Accounting method"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAcct 1 }

mgmtAuthConfigGlobalsAgentsConsoleAcctCmdEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable accounting of commands"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAcct 2 }

mgmtAuthConfigGlobalsAgentsConsoleAcctCmdPrivLvl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Command privilege level. Log all commands with a privilege level higher
         than or equal to this level. Valid values are in the range 0 to 15"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAcct 3 }

mgmtAuthConfigGlobalsAgentsConsoleAcctExecEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable exec (login) accounting"
    ::= { mgmtAuthConfigGlobalsAgentsConsoleAcct 4 }

mgmtAuthConfigGlobalsAgentsTelnetAcct OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 22 }

mgmtAuthConfigGlobalsAgentsTelnetAcctMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAcctMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Accounting method"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAcct 1 }

mgmtAuthConfigGlobalsAgentsTelnetAcctCmdEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable accounting of commands"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAcct 2 }

mgmtAuthConfigGlobalsAgentsTelnetAcctCmdPrivLvl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Command privilege level. Log all commands with a privilege level higher
         than or equal to this level. Valid values are in the range 0 to 15"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAcct 3 }

mgmtAuthConfigGlobalsAgentsTelnetAcctExecEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable exec (login) accounting"
    ::= { mgmtAuthConfigGlobalsAgentsTelnetAcct 4 }

mgmtAuthConfigGlobalsAgentsSshAcct OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsAgents 23 }

mgmtAuthConfigGlobalsAgentsSshAcctMethod OBJECT-TYPE
    SYNTAX      MGMTAuthAcctMethod
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Accounting method"
    ::= { mgmtAuthConfigGlobalsAgentsSshAcct 1 }

mgmtAuthConfigGlobalsAgentsSshAcctCmdEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable accounting of commands"
    ::= { mgmtAuthConfigGlobalsAgentsSshAcct 2 }

mgmtAuthConfigGlobalsAgentsSshAcctCmdPrivLvl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Command privilege level. Log all commands with a privilege level higher
         than or equal to this level. Valid values are in the range 0 to 15"
    ::= { mgmtAuthConfigGlobalsAgentsSshAcct 3 }

mgmtAuthConfigGlobalsAgentsSshAcctExecEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable exec (login) accounting"
    ::= { mgmtAuthConfigGlobalsAgentsSshAcct 4 }

mgmtAuthConfigGlobalsRadius OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobals 2 }

mgmtAuthConfigGlobalsRadiusGlobal OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsRadius 1 }

mgmtAuthConfigGlobalsRadiusGlobalTimeout OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global timeout for for RADIUS servers. Can be overridden by individual
         host entries. (1 to 1000 seconds)"
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 1 }

mgmtAuthConfigGlobalsRadiusGlobalRetransmit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global retransmit count for RADIUS servers. Can be overridden by
         individual host entries. (1 to 1000 times)"
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 2 }

mgmtAuthConfigGlobalsRadiusGlobalDeadtime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global deadtime for RADIUS servers. (0 to 1440 minutes)"
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 3 }

mgmtAuthConfigGlobalsRadiusGlobalKey OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..224))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global secret key for RADIUS servers. Can be overridden by individual
         host entries. If the secret key is unencrypted, then the maximum length
         is 63."
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 4 }

mgmtAuthConfigGlobalsRadiusGlobalEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the secret key is encrypted or not"
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 5 }

mgmtAuthConfigGlobalsRadiusGlobalNasIpv4Enable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable Global NAS IPv4 address"
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 11 }

mgmtAuthConfigGlobalsRadiusGlobalNasIpv4Address OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global NAS IPv4 address"
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 12 }

mgmtAuthConfigGlobalsRadiusGlobalNasIpv6Enable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable Global NAS IPv6 address"
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 13 }

mgmtAuthConfigGlobalsRadiusGlobalNasIpv6Address OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global NAS IPv6 address"
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 14 }

mgmtAuthConfigGlobalsRadiusGlobalNasIdentifier OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global NAS Identifier"
    ::= { mgmtAuthConfigGlobalsRadiusGlobal 15 }

mgmtAuthConfigGlobalsRadiusHostTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAuthConfigGlobalsRadiusHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of Radius servers useed to query for RADIUS
         authentication"
    ::= { mgmtAuthConfigGlobalsRadius 3 }

mgmtAuthConfigGlobalsRadiusHostEntry OBJECT-TYPE
    SYNTAX      MGMTAuthConfigGlobalsRadiusHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry defines a RADIUS server, with attributes used for contacting
         it. Host entries are consulted in numerical order of the entry index"
    INDEX       { mgmtAuthConfigGlobalsRadiusHostIndex }
    ::= { mgmtAuthConfigGlobalsRadiusHostTable 1 }

MGMTAuthConfigGlobalsRadiusHostEntry ::= SEQUENCE {
    mgmtAuthConfigGlobalsRadiusHostIndex       Integer32,
    mgmtAuthConfigGlobalsRadiusHostAddress     MGMTDisplayString,
    mgmtAuthConfigGlobalsRadiusHostAuthPort    Unsigned32,
    mgmtAuthConfigGlobalsRadiusHostAcctPort    Unsigned32,
    mgmtAuthConfigGlobalsRadiusHostTimeout     Unsigned32,
    mgmtAuthConfigGlobalsRadiusHostRetransmit  Unsigned32,
    mgmtAuthConfigGlobalsRadiusHostKey         MGMTDisplayString,
    mgmtAuthConfigGlobalsRadiusHostEncrypted   TruthValue
}

mgmtAuthConfigGlobalsRadiusHostIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Host entry index"
    ::= { mgmtAuthConfigGlobalsRadiusHostEntry 1 }

mgmtAuthConfigGlobalsRadiusHostAddress OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4/IPv6 address or hostname of this server"
    ::= { mgmtAuthConfigGlobalsRadiusHostEntry 2 }

mgmtAuthConfigGlobalsRadiusHostAuthPort OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Authentication port number (UDP) for use for this server"
    ::= { mgmtAuthConfigGlobalsRadiusHostEntry 3 }

mgmtAuthConfigGlobalsRadiusHostAcctPort OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Accounting port number (UDP) to use for this server"
    ::= { mgmtAuthConfigGlobalsRadiusHostEntry 4 }

mgmtAuthConfigGlobalsRadiusHostTimeout OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Seconds to wait for a response from this server. Use global timeout if
         zero"
    ::= { mgmtAuthConfigGlobalsRadiusHostEntry 5 }

mgmtAuthConfigGlobalsRadiusHostRetransmit OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of times a request is resent to an unresponding server. Use
         global retransmit if zero"
    ::= { mgmtAuthConfigGlobalsRadiusHostEntry 6 }

mgmtAuthConfigGlobalsRadiusHostKey OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..224))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The secret key to use for this server. Use global key if empty If the
         secret key is unencrypted, then the maximum length is 63."
    ::= { mgmtAuthConfigGlobalsRadiusHostEntry 7 }

mgmtAuthConfigGlobalsRadiusHostEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the secret key is encrypted or not"
    ::= { mgmtAuthConfigGlobalsRadiusHostEntry 8 }

mgmtAuthConfigGlobalsTacacs OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobals 3 }

mgmtAuthConfigGlobalsTacacsGlobal OBJECT IDENTIFIER
    ::= { mgmtAuthConfigGlobalsTacacs 1 }

mgmtAuthConfigGlobalsTacacsGlobalTimeout OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global timeout for for TACACS servers. Can be overridden by individual
         host entries. (1 to 1000 seconds)"
    ::= { mgmtAuthConfigGlobalsTacacsGlobal 1 }

mgmtAuthConfigGlobalsTacacsGlobalDeadtime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global deadtime for TACACS servers. (0 to 1440 minutes)"
    ::= { mgmtAuthConfigGlobalsTacacsGlobal 2 }

mgmtAuthConfigGlobalsTacacsGlobalKey OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..224))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global secret key for TACACS servers. Can be overridden by individual
         host entries. If the secret key is unencrypted, then the maximum length
         is 63."
    ::= { mgmtAuthConfigGlobalsTacacsGlobal 3 }

mgmtAuthConfigGlobalsTacacsGlobalEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the secret key is encrypted or not"
    ::= { mgmtAuthConfigGlobalsTacacsGlobal 4 }

mgmtAuthConfigGlobalsTacacsHostTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTAuthConfigGlobalsTacacsHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of Tacacs servers useed to query for TACACS
         authentication"
    ::= { mgmtAuthConfigGlobalsTacacs 2 }

mgmtAuthConfigGlobalsTacacsHostEntry OBJECT-TYPE
    SYNTAX      MGMTAuthConfigGlobalsTacacsHostEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry defines a TACACS server, with attributes used for contacting
         it. Host entries are consulted in numerical order of the entry index"
    INDEX       { mgmtAuthConfigGlobalsTacacsHostIndex }
    ::= { mgmtAuthConfigGlobalsTacacsHostTable 1 }

MGMTAuthConfigGlobalsTacacsHostEntry ::= SEQUENCE {
    mgmtAuthConfigGlobalsTacacsHostIndex      Integer32,
    mgmtAuthConfigGlobalsTacacsHostAddress    MGMTDisplayString,
    mgmtAuthConfigGlobalsTacacsHostAuthPort   Unsigned32,
    mgmtAuthConfigGlobalsTacacsHostTimeout    Unsigned32,
    mgmtAuthConfigGlobalsTacacsHostKey        MGMTDisplayString,
    mgmtAuthConfigGlobalsTacacsHostEncrypted  TruthValue
}

mgmtAuthConfigGlobalsTacacsHostIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Host entry index"
    ::= { mgmtAuthConfigGlobalsTacacsHostEntry 1 }

mgmtAuthConfigGlobalsTacacsHostAddress OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..255))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4/IPv6 address or hostname of this server"
    ::= { mgmtAuthConfigGlobalsTacacsHostEntry 2 }

mgmtAuthConfigGlobalsTacacsHostAuthPort OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Authentication port number (TCP) to use for this server"
    ::= { mgmtAuthConfigGlobalsTacacsHostEntry 3 }

mgmtAuthConfigGlobalsTacacsHostTimeout OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Seconds to wait for a response from this server. Use global timeout if
         zero"
    ::= { mgmtAuthConfigGlobalsTacacsHostEntry 4 }

mgmtAuthConfigGlobalsTacacsHostKey OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..224))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The secret key to use for this server. Use global key if empty. If the
         secret key is unencrypted, then the maximum length is 63."
    ::= { mgmtAuthConfigGlobalsTacacsHostEntry 5 }

mgmtAuthConfigGlobalsTacacsHostEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the secret key is encrypted or not"
    ::= { mgmtAuthConfigGlobalsTacacsHostEntry 6 }

mgmtAuthMibConformance OBJECT IDENTIFIER
    ::= { mgmtAuthMib 2 }

mgmtAuthMibCompliances OBJECT IDENTIFIER
    ::= { mgmtAuthMibConformance 1 }

mgmtAuthMibGroups OBJECT IDENTIFIER
    ::= { mgmtAuthMibConformance 2 }

mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsTableInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsIndex,
                  mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsMethod }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 1 }

mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsTableInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsIndex,
                  mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsMethod }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 2 }

mgmtAuthConfigGlobalsAgentsSshAuthenMethodsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsAgentsSshAuthenMethodsIndex,
                  mgmtAuthConfigGlobalsAgentsSshAuthenMethodsMethod }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 3 }

mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsIndex,
                  mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsMethod }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 4 }

mgmtAuthConfigGlobalsAgentsConsoleAuthorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsAgentsConsoleAuthorMethod,
                  mgmtAuthConfigGlobalsAgentsConsoleAuthorCmdEnable,
                  mgmtAuthConfigGlobalsAgentsConsoleAuthorCmdPrivLvl,
                  mgmtAuthConfigGlobalsAgentsConsoleAuthorCfgCmdEnable }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 5 }

mgmtAuthConfigGlobalsAgentsTelnetAuthorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsAgentsTelnetAuthorMethod,
                  mgmtAuthConfigGlobalsAgentsTelnetAuthorCmdEnable,
                  mgmtAuthConfigGlobalsAgentsTelnetAuthorCmdPrivLvl,
                  mgmtAuthConfigGlobalsAgentsTelnetAuthorCfgCmdEnable }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 6 }

mgmtAuthConfigGlobalsAgentsSshAuthorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsAgentsSshAuthorMethod,
                  mgmtAuthConfigGlobalsAgentsSshAuthorCmdEnable,
                  mgmtAuthConfigGlobalsAgentsSshAuthorCmdPrivLvl,
                  mgmtAuthConfigGlobalsAgentsSshAuthorCfgCmdEnable }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 7 }

mgmtAuthConfigGlobalsAgentsConsoleAcctInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsAgentsConsoleAcctMethod,
                  mgmtAuthConfigGlobalsAgentsConsoleAcctCmdEnable,
                  mgmtAuthConfigGlobalsAgentsConsoleAcctCmdPrivLvl,
                  mgmtAuthConfigGlobalsAgentsConsoleAcctExecEnable }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 8 }

mgmtAuthConfigGlobalsAgentsTelnetAcctInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsAgentsTelnetAcctMethod,
                  mgmtAuthConfigGlobalsAgentsTelnetAcctCmdEnable,
                  mgmtAuthConfigGlobalsAgentsTelnetAcctCmdPrivLvl,
                  mgmtAuthConfigGlobalsAgentsTelnetAcctExecEnable }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 9 }

mgmtAuthConfigGlobalsAgentsSshAcctInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsAgentsSshAcctMethod,
                  mgmtAuthConfigGlobalsAgentsSshAcctCmdEnable,
                  mgmtAuthConfigGlobalsAgentsSshAcctCmdPrivLvl,
                  mgmtAuthConfigGlobalsAgentsSshAcctExecEnable }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 10 }

mgmtAuthConfigGlobalsRadiusGlobalInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsRadiusGlobalTimeout,
                  mgmtAuthConfigGlobalsRadiusGlobalRetransmit,
                  mgmtAuthConfigGlobalsRadiusGlobalDeadtime,
                  mgmtAuthConfigGlobalsRadiusGlobalKey,
                  mgmtAuthConfigGlobalsRadiusGlobalEncrypted,
                  mgmtAuthConfigGlobalsRadiusGlobalNasIpv4Enable,
                  mgmtAuthConfigGlobalsRadiusGlobalNasIpv4Address,
                  mgmtAuthConfigGlobalsRadiusGlobalNasIpv6Enable,
                  mgmtAuthConfigGlobalsRadiusGlobalNasIpv6Address,
                  mgmtAuthConfigGlobalsRadiusGlobalNasIdentifier }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 11 }

mgmtAuthConfigGlobalsRadiusHostTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsRadiusHostIndex,
                  mgmtAuthConfigGlobalsRadiusHostAddress,
                  mgmtAuthConfigGlobalsRadiusHostAuthPort,
                  mgmtAuthConfigGlobalsRadiusHostAcctPort,
                  mgmtAuthConfigGlobalsRadiusHostTimeout,
                  mgmtAuthConfigGlobalsRadiusHostRetransmit,
                  mgmtAuthConfigGlobalsRadiusHostKey,
                  mgmtAuthConfigGlobalsRadiusHostEncrypted }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 12 }

mgmtAuthConfigGlobalsTacacsGlobalInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsTacacsGlobalTimeout,
                  mgmtAuthConfigGlobalsTacacsGlobalDeadtime,
                  mgmtAuthConfigGlobalsTacacsGlobalKey,
                  mgmtAuthConfigGlobalsTacacsGlobalEncrypted }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 13 }

mgmtAuthConfigGlobalsTacacsHostTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtAuthConfigGlobalsTacacsHostIndex,
                  mgmtAuthConfigGlobalsTacacsHostAddress,
                  mgmtAuthConfigGlobalsTacacsHostAuthPort,
                  mgmtAuthConfigGlobalsTacacsHostTimeout,
                  mgmtAuthConfigGlobalsTacacsHostKey,
                  mgmtAuthConfigGlobalsTacacsHostEncrypted }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtAuthMibGroups 14 }

mgmtAuthMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS {                        mgmtAuthConfigGlobalsAgentsConsoleAuthenMethodsTableInfoGroup,
                       mgmtAuthConfigGlobalsAgentsTelnetAuthenMethodsTableInfoGroup,
                       mgmtAuthConfigGlobalsAgentsSshAuthenMethodsTableInfoGroup,
                       mgmtAuthConfigGlobalsAgentsHttpAuthenMethodsTableInfoGroup,
                       mgmtAuthConfigGlobalsAgentsConsoleAuthorInfoGroup,
                       mgmtAuthConfigGlobalsAgentsTelnetAuthorInfoGroup,
                       mgmtAuthConfigGlobalsAgentsSshAuthorInfoGroup,
                       mgmtAuthConfigGlobalsAgentsConsoleAcctInfoGroup,
                       mgmtAuthConfigGlobalsAgentsTelnetAcctInfoGroup,
                       mgmtAuthConfigGlobalsAgentsSshAcctInfoGroup,
                       mgmtAuthConfigGlobalsRadiusGlobalInfoGroup,
                       mgmtAuthConfigGlobalsRadiusHostTableInfoGroup,
                       mgmtAuthConfigGlobalsTacacsGlobalInfoGroup,
                       mgmtAuthConfigGlobalsTacacsHostTableInfoGroup }

    ::= { mgmtAuthMibCompliances 1 }

END
