-- *****************************************************************
-- OSPF6-MIB:  
-- ****************************************************************

MGMT-OSPF6-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Counter64 FROM SNMPv2-SMI
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtOspf6Mib MODULE-IDENTITY
    LAST-UPDATED "202008030000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the OSPF6 MIB."
    REVISION    "202008030000Z"
    DESCRIPTION
        "Initial version."
    ::= { mgmtSwitch 156 }


MGMTOspf6AreaType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The area type."
    SYNTAX      INTEGER { normalArea(0), stubArea(1),
                          totallyStubArea(2), areaUnknown(3) }

MGMTOspf6BorderRouterType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The border router type of the OSPF6 route entry."
    SYNTAX      INTEGER { abr(0), asbrIntra(1), asbrInter(2),
                          abrAsbr(3), none(4) }

MGMTOspf6InterfaceState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The state of the link."
    SYNTAX      INTEGER { down(1), loopback(2), waiting(3),
                          pointToPoint(4), drOther(5), bdr(6), dr(7),
                          unknown(8) }

MGMTOspf6LsdbType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    SYNTAX      INTEGER { none(0), link(1), router(2), network(3),
                          interAreaPrefix(4), interAreaRouter(5),
                          nssa(6), intraAreaPrefix(7), external(8) }

MGMTOspf6NeighborState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The state of the neighbor node."
    SYNTAX      INTEGER { dependupon(0), deleted(1), down(2),
                          attempt(3), init(4), twoWay(5), exstart(6),
                          exchange(7), loading(8), full(9),
                          unknown(10) }

MGMTOspf6RouteType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The OSPF6 route type."
    SYNTAX      INTEGER { intraArea(0), interArea(1), borderRouter(2),
                          externalType1(3), externalType2(4),
                          unknown(5) }

mgmtOspf6MibObjects OBJECT IDENTIFIER
    ::= { mgmtOspf6Mib 1 }

mgmtOspf6Capabilities OBJECT IDENTIFIER
    ::= { mgmtOspf6MibObjects 1 }

mgmtOspf6CapabilitiesMinInstanceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "OSPF6 minimum instance ID"
    ::= { mgmtOspf6Capabilities 1 }

mgmtOspf6CapabilitiesMaxInstanceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "OSPF6 maximum instance ID"
    ::= { mgmtOspf6Capabilities 2 }

mgmtOspf6CapabilitiesMinRouterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF6 router ID"
    ::= { mgmtOspf6Capabilities 3 }

mgmtOspf6CapabilitiesMaxRouterId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF6 router ID"
    ::= { mgmtOspf6Capabilities 4 }

mgmtOspf6CapabilitiesMinPriority OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF6 priority"
    ::= { mgmtOspf6Capabilities 5 }

mgmtOspf6CapabilitiesMaxPriority OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF6 priority"
    ::= { mgmtOspf6Capabilities 6 }

mgmtOspf6CapabilitiesMinGeneralCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF6 interface cost"
    ::= { mgmtOspf6Capabilities 7 }

mgmtOspf6CapabilitiesMaxGeneralCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF6 interface cost"
    ::= { mgmtOspf6Capabilities 8 }

mgmtOspf6CapabilitiesMinInterfaceCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF6 interface cost"
    ::= { mgmtOspf6Capabilities 9 }

mgmtOspf6CapabilitiesMaxInterfaceCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF6 interface cost"
    ::= { mgmtOspf6Capabilities 10 }

mgmtOspf6CapabilitiesMinHelloInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF6 hello interval"
    ::= { mgmtOspf6Capabilities 11 }

mgmtOspf6CapabilitiesMaxHelloInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF6 hello interval"
    ::= { mgmtOspf6Capabilities 12 }

mgmtOspf6CapabilitiesMinRetransmitInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF6 retransmit interval"
    ::= { mgmtOspf6Capabilities 13 }

mgmtOspf6CapabilitiesMaxRetransmitInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF6 retransmit interval"
    ::= { mgmtOspf6Capabilities 14 }

mgmtOspf6CapabilitiesMinDeadInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF6 dead interval"
    ::= { mgmtOspf6Capabilities 15 }

mgmtOspf6CapabilitiesMaxDeadInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF6 dead interval"
    ::= { mgmtOspf6Capabilities 16 }

mgmtOspf6CapabilitiesIsRipngRedistributedSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if RIPNG redistributed is supported or not"
    ::= { mgmtOspf6Capabilities 17 }

mgmtOspf6CapabilitiesMinAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of OSPF6 administrative distance value"
    ::= { mgmtOspf6Capabilities 18 }

mgmtOspf6CapabilitiesMaxAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of OSPF6 administrative distance value"
    ::= { mgmtOspf6Capabilities 19 }

mgmtOspf6Config OBJECT IDENTIFIER
    ::= { mgmtOspf6MibObjects 2 }

mgmtOspf6ConfigProcessTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6ConfigProcessEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 process configuration table. It is used to enable or
         disable the routing process on a specific process ID."
    ::= { mgmtOspf6Config 1 }

mgmtOspf6ConfigProcessEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6ConfigProcessEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF6 routing process."
    INDEX       { mgmtOspf6ConfigProcessInstanceId }
    ::= { mgmtOspf6ConfigProcessTable 1 }

MGMTOspf6ConfigProcessEntry ::= SEQUENCE {
    mgmtOspf6ConfigProcessInstanceId  Integer32,
    mgmtOspf6ConfigProcessAction      MGMTRowEditorState
}

mgmtOspf6ConfigProcessInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6ConfigProcessEntry 1 }

mgmtOspf6ConfigProcessAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspf6ConfigProcessEntry 100 }

mgmtOspf6ConfigProcessTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspf6Config 2 }

mgmtOspf6ConfigProcessTableRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6ConfigProcessTableRowEditor 1 }

mgmtOspf6ConfigProcessTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspf6ConfigProcessTableRowEditor 100 }

mgmtOspf6ConfigRouterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6ConfigRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 router configuration table. It is a general group to
         configure the OSPF6 common router parameters."
    ::= { mgmtOspf6Config 3 }

mgmtOspf6ConfigRouterEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6ConfigRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents the OSPF6 router interface
         configuration."
    INDEX       { mgmtOspf6ConfigRouterInstanceId }
    ::= { mgmtOspf6ConfigRouterTable 1 }

MGMTOspf6ConfigRouterEntry ::= SEQUENCE {
    mgmtOspf6ConfigRouterInstanceId             Integer32,
    mgmtOspf6ConfigRouterIsSpecificRouterId     TruthValue,
    mgmtOspf6ConfigRouterRouterId               IpAddress,
    mgmtOspf6ConfigRouterConnectedRedistEnable  MGMTUnsigned8,
    mgmtOspf6ConfigRouterStaticRedistEnable     MGMTUnsigned8,
    mgmtOspf6ConfigRouterAdminDistance          MGMTUnsigned8
}

mgmtOspf6ConfigRouterInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6ConfigRouterEntry 1 }

mgmtOspf6ConfigRouterIsSpecificRouterId OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the 'RouterId' argument is a specific configured value or not."
    ::= { mgmtOspf6ConfigRouterEntry 2 }

mgmtOspf6ConfigRouterRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF6 router ID"
    ::= { mgmtOspf6ConfigRouterEntry 3 }

mgmtOspf6ConfigRouterConnectedRedistEnable OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF redistribute enabled for connected route or not."
    ::= { mgmtOspf6ConfigRouterEntry 4 }

mgmtOspf6ConfigRouterStaticRedistEnable OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF redistributeenabled for the static routes or not."
    ::= { mgmtOspf6ConfigRouterEntry 5 }

mgmtOspf6ConfigRouterAdminDistance OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF6 administrative distance."
    ::= { mgmtOspf6ConfigRouterEntry 6 }

mgmtOspf6ConfigRouterInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6ConfigRouterInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 router interface configuration table."
    ::= { mgmtOspf6Config 4 }

mgmtOspf6ConfigRouterInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6ConfigRouterInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each router interface has a set of parameters."
    INDEX       { mgmtOspf6ConfigRouterInterfaceInstanceId,
                  mgmtOspf6ConfigRouterInterfaceIfIndex }
    ::= { mgmtOspf6ConfigRouterInterfaceTable 1 }

MGMTOspf6ConfigRouterInterfaceEntry ::= SEQUENCE {
    mgmtOspf6ConfigRouterInterfaceInstanceId        Integer32,
    mgmtOspf6ConfigRouterInterfaceIfIndex           MGMTInterfaceIndex,
    mgmtOspf6ConfigRouterInterfaceIsSpecificAreaId  TruthValue,
    mgmtOspf6ConfigRouterInterfaceAreaId            IpAddress
}

mgmtOspf6ConfigRouterInterfaceInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6ConfigRouterInterfaceEntry 1 }

mgmtOspf6ConfigRouterInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspf6ConfigRouterInterfaceEntry 2 }

mgmtOspf6ConfigRouterInterfaceIsSpecificAreaId OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether the AreaId is valid or not."
    ::= { mgmtOspf6ConfigRouterInterfaceEntry 3 }

mgmtOspf6ConfigRouterInterfaceAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF6 interface Area ID.Only valid if 'is_specific_id' is true"
    ::= { mgmtOspf6ConfigRouterInterfaceEntry 4 }

mgmtOspf6ConfigAreaRangeTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6ConfigAreaRangeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 area range configuration table. It is used to summarize
         the intra area paths from a specific address range in one
         summary-LSA(Type-0x2003) and advertised to other areas or configure the
         address range status as 'DoNotAdvertise' which the
         summary-LSA(Type-0x2003) is suppressed.
         
         The area range configuration is used for Area Border Routers (ABRs) and
         only router-LSAs(Type-0x2001) and network-LSAs (Type-0x2002) can be
         summarized. The AS-external-LSAs(Type-0x4005) cannot be summarized
         because the scope is OSPF6 autonomous system (AS). The
         AS-external-LSAs(Type-0x4007) cannot be summarized because the feature
         is not supported yet."
    ::= { mgmtOspf6Config 5 }

mgmtOspf6ConfigAreaRangeEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6ConfigAreaRangeEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF6 area range configuration. The
         overlap configuration of address range is not allowed in order to avoid
         the conflict."
    INDEX       { mgmtOspf6ConfigAreaRangeInstanceId,
                  mgmtOspf6ConfigAreaRangeAreaId,
                  mgmtOspf6ConfigAreaRangeNetwork,
                  mgmtOspf6ConfigAreaRangeIpSubnetMaskLength }
    ::= { mgmtOspf6ConfigAreaRangeTable 1 }

MGMTOspf6ConfigAreaRangeEntry ::= SEQUENCE {
    mgmtOspf6ConfigAreaRangeInstanceId          Integer32,
    mgmtOspf6ConfigAreaRangeAreaId              IpAddress,
    mgmtOspf6ConfigAreaRangeNetwork             InetAddressIPv6,
    mgmtOspf6ConfigAreaRangeIpSubnetMaskLength  Integer32,
    mgmtOspf6ConfigAreaRangeAdvertised          TruthValue,
    mgmtOspf6ConfigAreaRangeIsSpecificCost      TruthValue,
    mgmtOspf6ConfigAreaRangeCost                Unsigned32,
    mgmtOspf6ConfigAreaRangeAction              MGMTRowEditorState
}

mgmtOspf6ConfigAreaRangeInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6ConfigAreaRangeEntry 1 }

mgmtOspf6ConfigAreaRangeAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6ConfigAreaRangeEntry 2 }

mgmtOspf6ConfigAreaRangeNetwork OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv6 network address."
    ::= { mgmtOspf6ConfigAreaRangeEntry 3 }

mgmtOspf6ConfigAreaRangeIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv6 network mask length."
    ::= { mgmtOspf6ConfigAreaRangeEntry 4 }

mgmtOspf6ConfigAreaRangeAdvertised OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When the value is true, it summarizes intra area paths from the address
         range in one Inter-Area Prefix LSA(Type-0x2003) and advertised to other
         areas.
         
         Otherwise, the intra area paths from the address range are not
         advertised to other areas."
    ::= { mgmtOspf6ConfigAreaRangeEntry 5 }

mgmtOspf6ConfigAreaRangeIsSpecificCost OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the 'cost' argument is a specific configured value or not."
    ::= { mgmtOspf6ConfigAreaRangeEntry 6 }

mgmtOspf6ConfigAreaRangeCost OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified cost (or metric) for this summary route. The field is
         significant only when 'IsSpecificCost' is TRUE."
    ::= { mgmtOspf6ConfigAreaRangeEntry 7 }

mgmtOspf6ConfigAreaRangeAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspf6ConfigAreaRangeEntry 100 }

mgmtOspf6ConfigAreaRangeTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspf6Config 6 }

mgmtOspf6ConfigAreaRangeTableRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6ConfigAreaRangeTableRowEditor 1 }

mgmtOspf6ConfigAreaRangeTableRowEditorAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6ConfigAreaRangeTableRowEditor 2 }

mgmtOspf6ConfigAreaRangeTableRowEditorNetwork OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv6 network address."
    ::= { mgmtOspf6ConfigAreaRangeTableRowEditor 3 }

mgmtOspf6ConfigAreaRangeTableRowEditorIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv6 network mask length."
    ::= { mgmtOspf6ConfigAreaRangeTableRowEditor 4 }

mgmtOspf6ConfigAreaRangeTableRowEditorAdvertised OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "When the value is true, it summarizes intra area paths from the address
         range in one Inter-Area Prefix LSA(Type-0x2003) and advertised to other
         areas.
         
         Otherwise, the intra area paths from the address range are not
         advertised to other areas."
    ::= { mgmtOspf6ConfigAreaRangeTableRowEditor 5 }

mgmtOspf6ConfigAreaRangeTableRowEditorIsSpecificCost OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the 'cost' argument is a specific configured value or not."
    ::= { mgmtOspf6ConfigAreaRangeTableRowEditor 6 }

mgmtOspf6ConfigAreaRangeTableRowEditorCost OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified cost (or metric) for this summary route. The field is
         significant only when 'IsSpecificCost' is TRUE."
    ::= { mgmtOspf6ConfigAreaRangeTableRowEditor 7 }

mgmtOspf6ConfigAreaRangeTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspf6ConfigAreaRangeTableRowEditor 100 }

mgmtOspf6ConfigStubAreaTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6ConfigStubAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 stub area configuration table. The configuration is used
         to reduce the link-state database size and therefore the memory and CPU
         requirement by forbidding some LSAs."
    ::= { mgmtOspf6Config 7 }

mgmtOspf6ConfigStubAreaEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6ConfigStubAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF6 stub area configuration."
    INDEX       { mgmtOspf6ConfigStubAreaInstanceId,
                  mgmtOspf6ConfigStubAreaAreaId }
    ::= { mgmtOspf6ConfigStubAreaTable 1 }

MGMTOspf6ConfigStubAreaEntry ::= SEQUENCE {
    mgmtOspf6ConfigStubAreaInstanceId  Integer32,
    mgmtOspf6ConfigStubAreaAreaId      IpAddress,
    mgmtOspf6ConfigStubAreaNoSummary   TruthValue,
    mgmtOspf6ConfigStubAreaAction      MGMTRowEditorState
}

mgmtOspf6ConfigStubAreaInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6ConfigStubAreaEntry 1 }

mgmtOspf6ConfigStubAreaAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6ConfigStubAreaEntry 2 }

mgmtOspf6ConfigStubAreaNoSummary OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value is true to configure the inter-area routes do not inject into
         this stub area."
    ::= { mgmtOspf6ConfigStubAreaEntry 3 }

mgmtOspf6ConfigStubAreaAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspf6ConfigStubAreaEntry 100 }

mgmtOspf6ConfigStubAreaTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtOspf6Config 8 }

mgmtOspf6ConfigStubAreaTableRowEditorInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6ConfigStubAreaTableRowEditor 1 }

mgmtOspf6ConfigStubAreaTableRowEditorAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6ConfigStubAreaTableRowEditor 2 }

mgmtOspf6ConfigStubAreaTableRowEditorNoSummary OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value is true to configure the inter-area routes do not inject into
         this stub area."
    ::= { mgmtOspf6ConfigStubAreaTableRowEditor 3 }

mgmtOspf6ConfigStubAreaTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtOspf6ConfigStubAreaTableRowEditor 100 }

mgmtOspf6ConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6ConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is interface configuration parameter table."
    ::= { mgmtOspf6Config 9 }

mgmtOspf6ConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6ConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of parameters."
    INDEX       { mgmtOspf6ConfigInterfaceIfIndex }
    ::= { mgmtOspf6ConfigInterfaceTable 1 }

MGMTOspf6ConfigInterfaceEntry ::= SEQUENCE {
    mgmtOspf6ConfigInterfaceIfIndex             MGMTInterfaceIndex,
    mgmtOspf6ConfigInterfacePriority            Unsigned32,
    mgmtOspf6ConfigInterfaceIsSpecificCost      TruthValue,
    mgmtOspf6ConfigInterfaceCost                Unsigned32,
    mgmtOspf6ConfigInterfaceDeadInterval        Unsigned32,
    mgmtOspf6ConfigInterfaceHelloInterval       Unsigned32,
    mgmtOspf6ConfigInterfaceRetransmitInterval  Unsigned32,
    mgmtOspf6ConfigInterfaceIsPassiveInterface  TruthValue,
    mgmtOspf6ConfigInterfaceTransmitDelay       Unsigned32
}

mgmtOspf6ConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspf6ConfigInterfaceEntry 1 }

mgmtOspf6ConfigInterfacePriority OBJECT-TYPE
    SYNTAX      Unsigned32 (0..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified router priority for the interface."
    ::= { mgmtOspf6ConfigInterfaceEntry 2 }

mgmtOspf6ConfigInterfaceIsSpecificCost OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicate the 'cost' argument is a specific configured value or not."
    ::= { mgmtOspf6ConfigInterfaceEntry 3 }

mgmtOspf6ConfigInterfaceCost OBJECT-TYPE
    SYNTAX      Unsigned32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "User specified cost for this interface. It's link state metric for the
         interface. The field is significant only when 'IsSpecificCost' is TRUE."
    ::= { mgmtOspf6ConfigInterfaceEntry 4 }

mgmtOspf6ConfigInterfaceDeadInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time interval (in seconds) between hello packets."
    ::= { mgmtOspf6ConfigInterfaceEntry 5 }

mgmtOspf6ConfigInterfaceHelloInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "How many Hello packets will be sent per second."
    ::= { mgmtOspf6ConfigInterfaceEntry 6 }

mgmtOspf6ConfigInterfaceRetransmitInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (3..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time interval (in seconds) between link-state advertisement(LSA)
         retransmissions for adjacencies."
    ::= { mgmtOspf6ConfigInterfaceEntry 7 }

mgmtOspf6ConfigInterfaceIsPassiveInterface OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates whether the interface is passive or not"
    ::= { mgmtOspf6ConfigInterfaceEntry 8 }

mgmtOspf6ConfigInterfaceTransmitDelay OBJECT-TYPE
    SYNTAX      Unsigned32 (1..3600)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The time (in seconds) taken to transmit a packet."
    ::= { mgmtOspf6ConfigInterfaceEntry 9 }

mgmtOspf6ConfigInterface OBJECT IDENTIFIER
    ::= { mgmtOspf6Config 100 }

mgmtOspf6Status OBJECT IDENTIFIER
    ::= { mgmtOspf6MibObjects 3 }

mgmtOspf6StatusRouterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 router status table. It is used to provide the OSPF6
         router status information."
    ::= { mgmtOspf6Status 1 }

mgmtOspf6StatusRouterEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF6 router status information."
    INDEX       { mgmtOspf6StatusRouterInstanceId }
    ::= { mgmtOspf6StatusRouterTable 1 }

MGMTOspf6StatusRouterEntry ::= SEQUENCE {
    mgmtOspf6StatusRouterInstanceId         Integer32,
    mgmtOspf6StatusRouterRouterId           IpAddress,
    mgmtOspf6StatusRouterSpfDelay           Unsigned32,
    mgmtOspf6StatusRouterSpfHoldTime        Unsigned32,
    mgmtOspf6StatusRouterSpfMaxWaitTime     Unsigned32,
    mgmtOspf6StatusRouterLastExcutedSpfTs   Counter64,
    mgmtOspf6StatusRouterAttachedAreaCount  Unsigned32
}

mgmtOspf6StatusRouterInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusRouterEntry 1 }

mgmtOspf6StatusRouterRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "OSPF6 router ID"
    ::= { mgmtOspf6StatusRouterEntry 2 }

mgmtOspf6StatusRouterSpfDelay OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Delay time (in seconds)of SPF calculations."
    ::= { mgmtOspf6StatusRouterEntry 3 }

mgmtOspf6StatusRouterSpfHoldTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum hold time (in milliseconds) between consecutive SPF
         calculations."
    ::= { mgmtOspf6StatusRouterEntry 4 }

mgmtOspf6StatusRouterSpfMaxWaitTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum wait time (in milliseconds) between consecutive SPF
         calculations."
    ::= { mgmtOspf6StatusRouterEntry 5 }

mgmtOspf6StatusRouterLastExcutedSpfTs OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time (in milliseconds) that has passed between the start of the SPF
         algorithm execution and the current time."
    ::= { mgmtOspf6StatusRouterEntry 6 }

mgmtOspf6StatusRouterAttachedAreaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of areas attached for the router."
    ::= { mgmtOspf6StatusRouterEntry 7 }

mgmtOspf6StatusRouteAreaTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusRouteAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 network area status table. It is used to provide the
         OSPF6 network area status information."
    ::= { mgmtOspf6Status 2 }

mgmtOspf6StatusRouteAreaEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusRouteAreaEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF6 network area status
         information."
    INDEX       { mgmtOspf6StatusRouteAreaInstanceId,
                  mgmtOspf6StatusRouteAreaAreaId }
    ::= { mgmtOspf6StatusRouteAreaTable 1 }

MGMTOspf6StatusRouteAreaEntry ::= SEQUENCE {
    mgmtOspf6StatusRouteAreaInstanceId         Integer32,
    mgmtOspf6StatusRouteAreaAreaId             IpAddress,
    mgmtOspf6StatusRouteAreaIsBackbone         TruthValue,
    mgmtOspf6StatusRouteAreaAreaType           MGMTOspf6AreaType,
    mgmtOspf6StatusRouteAreaAttachedIntfCount  Unsigned32,
    mgmtOspf6StatusRouteAreaSpfExecutedCount   Unsigned32,
    mgmtOspf6StatusRouteAreaLsaCount           Unsigned32
}

mgmtOspf6StatusRouteAreaInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusRouteAreaEntry 1 }

mgmtOspf6StatusRouteAreaAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusRouteAreaEntry 2 }

mgmtOspf6StatusRouteAreaIsBackbone OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if it's backbone area or not."
    ::= { mgmtOspf6StatusRouteAreaEntry 3 }

mgmtOspf6StatusRouteAreaAreaType OBJECT-TYPE
    SYNTAX      MGMTOspf6AreaType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The area type."
    ::= { mgmtOspf6StatusRouteAreaEntry 4 }

mgmtOspf6StatusRouteAreaAttachedIntfCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of active interfaces attached in the area."
    ::= { mgmtOspf6StatusRouteAreaEntry 5 }

mgmtOspf6StatusRouteAreaSpfExecutedCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of times SPF algorithm has been executed for the particular
         area."
    ::= { mgmtOspf6StatusRouteAreaEntry 6 }

mgmtOspf6StatusRouteAreaLsaCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of the total LSAs for the particular area."
    ::= { mgmtOspf6StatusRouteAreaEntry 7 }

mgmtOspf6StatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 interface status table. It is used to provide the OSPF6
         interface status information."
    ::= { mgmtOspf6Status 5 }

mgmtOspf6StatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF6 interface status information."
    INDEX       { mgmtOspf6StatusInterfaceIfIndex }
    ::= { mgmtOspf6StatusInterfaceTable 1 }

MGMTOspf6StatusInterfaceEntry ::= SEQUENCE {
    mgmtOspf6StatusInterfaceIfIndex             MGMTInterfaceIndex,
    mgmtOspf6StatusInterfaceStatus              TruthValue,
    mgmtOspf6StatusInterfacePassive             TruthValue,
    mgmtOspf6StatusInterfaceNetwork             InetAddressIPv6,
    mgmtOspf6StatusInterfaceIpSubnetMaskLength  Integer32,
    mgmtOspf6StatusInterfaceAreaId              IpAddress,
    mgmtOspf6StatusInterfaceRouterId            IpAddress,
    mgmtOspf6StatusInterfaceCost                Integer32,
    mgmtOspf6StatusInterfaceState               MGMTOspf6InterfaceState,
    mgmtOspf6StatusInterfacePriority            Integer32,
    mgmtOspf6StatusInterfaceDrId                IpAddress,
    mgmtOspf6StatusInterfaceBdrId               IpAddress,
    mgmtOspf6StatusInterfaceHelloTime           Unsigned32,
    mgmtOspf6StatusInterfaceDeadTime            Unsigned32,
    mgmtOspf6StatusInterfaceRetransmitTime      Unsigned32,
    mgmtOspf6StatusInterfaceTransmitDelay       Unsigned32
}

mgmtOspf6StatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspf6StatusInterfaceEntry 1 }

mgmtOspf6StatusInterfaceStatus OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It's used to indicate if the interface is up or down."
    ::= { mgmtOspf6StatusInterfaceEntry 2 }

mgmtOspf6StatusInterfacePassive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate if the interface is passive interface."
    ::= { mgmtOspf6StatusInterfaceEntry 3 }

mgmtOspf6StatusInterfaceNetwork OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv6 network address."
    ::= { mgmtOspf6StatusInterfaceEntry 4 }

mgmtOspf6StatusInterfaceIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv6 network mask length."
    ::= { mgmtOspf6StatusInterfaceEntry 5 }

mgmtOspf6StatusInterfaceAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusInterfaceEntry 6 }

mgmtOspf6StatusInterfaceRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 router ID."
    ::= { mgmtOspf6StatusInterfaceEntry 7 }

mgmtOspf6StatusInterfaceCost OBJECT-TYPE
    SYNTAX      Integer32 (1..65535)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The cost of the interface."
    ::= { mgmtOspf6StatusInterfaceEntry 8 }

mgmtOspf6StatusInterfaceState OBJECT-TYPE
    SYNTAX      MGMTOspf6InterfaceState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The state of the link."
    ::= { mgmtOspf6StatusInterfaceEntry 9 }

mgmtOspf6StatusInterfacePriority OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 priority. It helps determine the DR and BDR on the network to
         which this interface is connected."
    ::= { mgmtOspf6StatusInterfaceEntry 10 }

mgmtOspf6StatusInterfaceDrId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The router ID of DR."
    ::= { mgmtOspf6StatusInterfaceEntry 11 }

mgmtOspf6StatusInterfaceBdrId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The router ID of BDR."
    ::= { mgmtOspf6StatusInterfaceEntry 12 }

mgmtOspf6StatusInterfaceHelloTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Hello timer. A time interval that a router sends an OSPF6 hello packet."
    ::= { mgmtOspf6StatusInterfaceEntry 13 }

mgmtOspf6StatusInterfaceDeadTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Dead timer. Dead timer is a time interval to wait before declaring a
         neighbor dead. The unit of time is the second."
    ::= { mgmtOspf6StatusInterfaceEntry 14 }

mgmtOspf6StatusInterfaceRetransmitTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Retransmit timer. A time interval to wait before retransmitting a
         database description packet when it has not been acknowledged."
    ::= { mgmtOspf6StatusInterfaceEntry 15 }

mgmtOspf6StatusInterfaceTransmitDelay OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The estimated time to transmit a link-state update packet on the
         interface."
    ::= { mgmtOspf6StatusInterfaceEntry 16 }

mgmtOspf6StatusNeighborIpv6Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusNeighborIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 IPv6 neighbor status table. It is used to provide the
         OSPF6 neighbor status information."
    ::= { mgmtOspf6Status 6 }

mgmtOspf6StatusNeighborIpv6Entry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusNeighborIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF6 IPv6 neighbor status
         information."
    INDEX       { mgmtOspf6StatusNeighborIpv6InstanceId,
                  mgmtOspf6StatusNeighborIpv6RouterId,
                  mgmtOspf6StatusNeighborIpv6Ipv6Addr,
                  mgmtOspf6StatusNeighborIpv6IfIndex }
    ::= { mgmtOspf6StatusNeighborIpv6Table 1 }

MGMTOspf6StatusNeighborIpv6Entry ::= SEQUENCE {
    mgmtOspf6StatusNeighborIpv6InstanceId     Integer32,
    mgmtOspf6StatusNeighborIpv6RouterId       IpAddress,
    mgmtOspf6StatusNeighborIpv6Ipv6Addr       InetAddressIPv6,
    mgmtOspf6StatusNeighborIpv6IfIndex        MGMTInterfaceIndex,
    mgmtOspf6StatusNeighborIpv6IpAddr         InetAddressIPv6,
    mgmtOspf6StatusNeighborIpv6AreaId         IpAddress,
    mgmtOspf6StatusNeighborIpv6NeighborId     IpAddress,
    mgmtOspf6StatusNeighborIpv6Priority       Integer32,
    mgmtOspf6StatusNeighborIpv6State          MGMTOspf6NeighborState,
    mgmtOspf6StatusNeighborIpv6DrId           IpAddress,
    mgmtOspf6StatusNeighborIpv6BdrId          IpAddress,
    mgmtOspf6StatusNeighborIpv6DeadTime       Unsigned32,
    mgmtOspf6StatusNeighborIpv6TransitAreaId  IpAddress
}

mgmtOspf6StatusNeighborIpv6InstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 1 }

mgmtOspf6StatusNeighborIpv6RouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 router ID."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 2 }

mgmtOspf6StatusNeighborIpv6Ipv6Addr OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The IPv6 address."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 3 }

mgmtOspf6StatusNeighborIpv6IfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 4 }

mgmtOspf6StatusNeighborIpv6IpAddr OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP address."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 5 }

mgmtOspf6StatusNeighborIpv6AreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 6 }

mgmtOspf6StatusNeighborIpv6NeighborId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The router ID of Neighbor."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 7 }

mgmtOspf6StatusNeighborIpv6Priority OBJECT-TYPE
    SYNTAX      Integer32 (0..255)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The priority of OSPF6 neighbor. It indicates the priority of the
         neighbor router. This item is used when selecting the DR for the
         network. The router with the highest priority becomes the DR."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 8 }

mgmtOspf6StatusNeighborIpv6State OBJECT-TYPE
    SYNTAX      MGMTOspf6NeighborState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The state of OSPF6 neighbor. It indicates the functional state of the
         neighbor router."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 9 }

mgmtOspf6StatusNeighborIpv6DrId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The router ID of DR."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 10 }

mgmtOspf6StatusNeighborIpv6BdrId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The router ID of BDR."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 11 }

mgmtOspf6StatusNeighborIpv6DeadTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Dead timer. It indicates the amount of time remaining that the router
         waits to receive an OSPF6 hello packet from the neighbor before
         declaring the neighbor down."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 12 }

mgmtOspf6StatusNeighborIpv6TransitAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 transit area ID for the neighbor on virtual link interface."
    ::= { mgmtOspf6StatusNeighborIpv6Entry 13 }

mgmtOspf6StatusRouteIpv6Table OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusRouteIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is OSPF6 routing status table. It is used to provide the OSPF6
         routing status information."
    ::= { mgmtOspf6Status 10 }

mgmtOspf6StatusRouteIpv6Entry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusRouteIpv6Entry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry in this table represents OSPF6 routing status information."
    INDEX       { mgmtOspf6StatusRouteIpv6InstanceId,
                  mgmtOspf6StatusRouteIpv6RouteType,
                  mgmtOspf6StatusRouteIpv6Network,
                  mgmtOspf6StatusRouteIpv6IpSubnetMaskLength,
                  mgmtOspf6StatusRouteIpv6AreaId,
                  mgmtOspf6StatusRouteIpv6NextHop }
    ::= { mgmtOspf6StatusRouteIpv6Table 1 }

MGMTOspf6StatusRouteIpv6Entry ::= SEQUENCE {
    mgmtOspf6StatusRouteIpv6InstanceId          Integer32,
    mgmtOspf6StatusRouteIpv6RouteType           MGMTOspf6RouteType,
    mgmtOspf6StatusRouteIpv6Network             InetAddressIPv6,
    mgmtOspf6StatusRouteIpv6IpSubnetMaskLength  Integer32,
    mgmtOspf6StatusRouteIpv6AreaId              IpAddress,
    mgmtOspf6StatusRouteIpv6NextHop             InetAddressIPv6,
    mgmtOspf6StatusRouteIpv6Cost                Unsigned32,
    mgmtOspf6StatusRouteIpv6AsCost              Unsigned32,
    mgmtOspf6StatusRouteIpv6BorderRouterType    MGMTOspf6BorderRouterType,
    mgmtOspf6StatusRouteIpv6Ifindex             MGMTInterfaceIndex,
    mgmtOspf6StatusRouteIpv6IsConnected         TruthValue
}

mgmtOspf6StatusRouteIpv6InstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusRouteIpv6Entry 1 }

mgmtOspf6StatusRouteIpv6RouteType OBJECT-TYPE
    SYNTAX      MGMTOspf6RouteType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 route type."
    ::= { mgmtOspf6StatusRouteIpv6Entry 2 }

mgmtOspf6StatusRouteIpv6Network OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv6 network address."
    ::= { mgmtOspf6StatusRouteIpv6Entry 3 }

mgmtOspf6StatusRouteIpv6IpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv6 network mask length."
    ::= { mgmtOspf6StatusRouteIpv6Entry 4 }

mgmtOspf6StatusRouteIpv6AreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "It indicates which area the route or router can be reached via/to."
    ::= { mgmtOspf6StatusRouteIpv6Entry 5 }

mgmtOspf6StatusRouteIpv6NextHop OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The nexthop to the route."
    ::= { mgmtOspf6StatusRouteIpv6Entry 6 }

mgmtOspf6StatusRouteIpv6Cost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The cost of the route."
    ::= { mgmtOspf6StatusRouteIpv6Entry 7 }

mgmtOspf6StatusRouteIpv6AsCost OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The cost of the route within the OSPF6 network. It is valid for
         external Type-2 route and always '0' for other route type."
    ::= { mgmtOspf6StatusRouteIpv6Entry 8 }

mgmtOspf6StatusRouteIpv6BorderRouterType OBJECT-TYPE
    SYNTAX      MGMTOspf6BorderRouterType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The border router type of the OSPF6 route entry."
    ::= { mgmtOspf6StatusRouteIpv6Entry 9 }

mgmtOspf6StatusRouteIpv6Ifindex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The interface where the ip packet is outgoing."
    ::= { mgmtOspf6StatusRouteIpv6Entry 10 }

mgmtOspf6StatusRouteIpv6IsConnected OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The destination is connected directly or not."
    ::= { mgmtOspf6StatusRouteIpv6Entry 11 }

mgmtOspf6StatusDbTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusDbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF6 LSA link state database information table."
    ::= { mgmtOspf6Status 11 }

mgmtOspf6StatusDbEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusDbEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspf6StatusDbInstanceId,
                  mgmtOspf6StatusDbAreaId, mgmtOspf6StatusDbLsdbType,
                  mgmtOspf6StatusDbLinkStateId,
                  mgmtOspf6StatusDbAdvRouterId }
    ::= { mgmtOspf6StatusDbTable 1 }

MGMTOspf6StatusDbEntry ::= SEQUENCE {
    mgmtOspf6StatusDbInstanceId       Integer32,
    mgmtOspf6StatusDbAreaId           IpAddress,
    mgmtOspf6StatusDbLsdbType         MGMTOspf6LsdbType,
    mgmtOspf6StatusDbLinkStateId      IpAddress,
    mgmtOspf6StatusDbAdvRouterId      IpAddress,
    mgmtOspf6StatusDbAge              Unsigned32,
    mgmtOspf6StatusDbSequence         Unsigned32,
    mgmtOspf6StatusDbRouterLinkCount  Unsigned32
}

mgmtOspf6StatusDbInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusDbEntry 1 }

mgmtOspf6StatusDbAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusDbEntry 2 }

mgmtOspf6StatusDbLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspf6LsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspf6StatusDbEntry 3 }

mgmtOspf6StatusDbLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 link state ID."
    ::= { mgmtOspf6StatusDbEntry 4 }

mgmtOspf6StatusDbAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspf6StatusDbEntry 5 }

mgmtOspf6StatusDbAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspf6StatusDbEntry 6 }

mgmtOspf6StatusDbSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspf6StatusDbEntry 7 }

mgmtOspf6StatusDbRouterLinkCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The link count of the LSA. The field is significant only when the link
         state type is 'Router Link State' (Type 1)."
    ::= { mgmtOspf6StatusDbEntry 8 }

mgmtOspf6StatusDbRouterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusDbRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF6 LSA Router link state database information table."
    ::= { mgmtOspf6Status 12 }

mgmtOspf6StatusDbRouterEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusDbRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspf6StatusDbRouterInstanceId,
                  mgmtOspf6StatusDbRouterAreaId,
                  mgmtOspf6StatusDbRouterLsdbType,
                  mgmtOspf6StatusDbRouterLinkStateId,
                  mgmtOspf6StatusDbRouterAdvRouterId }
    ::= { mgmtOspf6StatusDbRouterTable 1 }

MGMTOspf6StatusDbRouterEntry ::= SEQUENCE {
    mgmtOspf6StatusDbRouterInstanceId       Integer32,
    mgmtOspf6StatusDbRouterAreaId           IpAddress,
    mgmtOspf6StatusDbRouterLsdbType         MGMTOspf6LsdbType,
    mgmtOspf6StatusDbRouterLinkStateId      IpAddress,
    mgmtOspf6StatusDbRouterAdvRouterId      IpAddress,
    mgmtOspf6StatusDbRouterAge              Unsigned32,
    mgmtOspf6StatusDbRouterOptions          Unsigned32,
    mgmtOspf6StatusDbRouterSequence         Unsigned32,
    mgmtOspf6StatusDbRouterChecksum         Unsigned32,
    mgmtOspf6StatusDbRouterLength           Unsigned32,
    mgmtOspf6StatusDbRouterRouterLinkCount  Unsigned32
}

mgmtOspf6StatusDbRouterInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusDbRouterEntry 1 }

mgmtOspf6StatusDbRouterAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusDbRouterEntry 2 }

mgmtOspf6StatusDbRouterLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspf6LsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspf6StatusDbRouterEntry 3 }

mgmtOspf6StatusDbRouterLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 link state ID."
    ::= { mgmtOspf6StatusDbRouterEntry 4 }

mgmtOspf6StatusDbRouterAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspf6StatusDbRouterEntry 5 }

mgmtOspf6StatusDbRouterAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspf6StatusDbRouterEntry 6 }

mgmtOspf6StatusDbRouterOptions OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 option field which is present in OSPF6 hello packets, which
         enables OSPF6 routers to support (or not support) optional
         capabilities, and to communicate their capability level to other OSPF6
         routers."
    ::= { mgmtOspf6StatusDbRouterEntry 7 }

mgmtOspf6StatusDbRouterSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspf6StatusDbRouterEntry 8 }

mgmtOspf6StatusDbRouterChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspf6StatusDbRouterEntry 9 }

mgmtOspf6StatusDbRouterLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspf6StatusDbRouterEntry 10 }

mgmtOspf6StatusDbRouterRouterLinkCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The link count of the LSA. The field is significant only when the link
         state type is 'Router Link State' (Type 1)."
    ::= { mgmtOspf6StatusDbRouterEntry 11 }

mgmtOspf6StatusDbNetworkTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusDbNetworkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF6 LSA Network link state database information table."
    ::= { mgmtOspf6Status 13 }

mgmtOspf6StatusDbNetworkEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusDbNetworkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspf6StatusDbNetworkInstanceId,
                  mgmtOspf6StatusDbNetworkAreaId,
                  mgmtOspf6StatusDbNetworkLsdbType,
                  mgmtOspf6StatusDbNetworkLinkStateId,
                  mgmtOspf6StatusDbNetworkAdvRouterId }
    ::= { mgmtOspf6StatusDbNetworkTable 1 }

MGMTOspf6StatusDbNetworkEntry ::= SEQUENCE {
    mgmtOspf6StatusDbNetworkInstanceId           Integer32,
    mgmtOspf6StatusDbNetworkAreaId               IpAddress,
    mgmtOspf6StatusDbNetworkLsdbType             MGMTOspf6LsdbType,
    mgmtOspf6StatusDbNetworkLinkStateId          IpAddress,
    mgmtOspf6StatusDbNetworkAdvRouterId          IpAddress,
    mgmtOspf6StatusDbNetworkAge                  Unsigned32,
    mgmtOspf6StatusDbNetworkOptions              Unsigned32,
    mgmtOspf6StatusDbNetworkSequence             Unsigned32,
    mgmtOspf6StatusDbNetworkChecksum             Unsigned32,
    mgmtOspf6StatusDbNetworkLength               Unsigned32,
    mgmtOspf6StatusDbNetworkAttachedRouterCount  Unsigned32
}

mgmtOspf6StatusDbNetworkInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusDbNetworkEntry 1 }

mgmtOspf6StatusDbNetworkAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusDbNetworkEntry 2 }

mgmtOspf6StatusDbNetworkLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspf6LsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspf6StatusDbNetworkEntry 3 }

mgmtOspf6StatusDbNetworkLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 link state ID."
    ::= { mgmtOspf6StatusDbNetworkEntry 4 }

mgmtOspf6StatusDbNetworkAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspf6StatusDbNetworkEntry 5 }

mgmtOspf6StatusDbNetworkAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspf6StatusDbNetworkEntry 6 }

mgmtOspf6StatusDbNetworkOptions OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 option field which is present in OSPF6 hello packets, which
         enables OSPF6 routers to support (or not support) optional
         capabilities, and to communicate their capability level to other OSPF6
         routers."
    ::= { mgmtOspf6StatusDbNetworkEntry 7 }

mgmtOspf6StatusDbNetworkSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspf6StatusDbNetworkEntry 8 }

mgmtOspf6StatusDbNetworkChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspf6StatusDbNetworkEntry 9 }

mgmtOspf6StatusDbNetworkLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspf6StatusDbNetworkEntry 10 }

mgmtOspf6StatusDbNetworkAttachedRouterCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The attached router count of the LSA. The field is significant only
         when the link state type is 'Network Link State' (Type 2)."
    ::= { mgmtOspf6StatusDbNetworkEntry 11 }

mgmtOspf6StatusDbInterAreaPrefixTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusDbInterAreaPrefixEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF6 LSA Summary link state database information table."
    ::= { mgmtOspf6Status 14 }

mgmtOspf6StatusDbInterAreaPrefixEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusDbInterAreaPrefixEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspf6StatusDbInterAreaPrefixInstanceId,
                  mgmtOspf6StatusDbInterAreaPrefixAreaId,
                  mgmtOspf6StatusDbInterAreaPrefixLsdbType,
                  mgmtOspf6StatusDbInterAreaPrefixLinkStateId,
                  mgmtOspf6StatusDbInterAreaPrefixAdvRouterId }
    ::= { mgmtOspf6StatusDbInterAreaPrefixTable 1 }

MGMTOspf6StatusDbInterAreaPrefixEntry ::= SEQUENCE {
    mgmtOspf6StatusDbInterAreaPrefixInstanceId          Integer32,
    mgmtOspf6StatusDbInterAreaPrefixAreaId              IpAddress,
    mgmtOspf6StatusDbInterAreaPrefixLsdbType            MGMTOspf6LsdbType,
    mgmtOspf6StatusDbInterAreaPrefixLinkStateId         IpAddress,
    mgmtOspf6StatusDbInterAreaPrefixAdvRouterId         IpAddress,
    mgmtOspf6StatusDbInterAreaPrefixAge                 Unsigned32,
    mgmtOspf6StatusDbInterAreaPrefixOptions             Unsigned32,
    mgmtOspf6StatusDbInterAreaPrefixSequence            Unsigned32,
    mgmtOspf6StatusDbInterAreaPrefixChecksum            Unsigned32,
    mgmtOspf6StatusDbInterAreaPrefixLength              Unsigned32,
    mgmtOspf6StatusDbInterAreaPrefixPrefixAddress       InetAddressIPv6,
    mgmtOspf6StatusDbInterAreaPrefixIpSubnetMaskLength  Integer32,
    mgmtOspf6StatusDbInterAreaPrefixMetric              Unsigned32
}

mgmtOspf6StatusDbInterAreaPrefixInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 1 }

mgmtOspf6StatusDbInterAreaPrefixAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 2 }

mgmtOspf6StatusDbInterAreaPrefixLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspf6LsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 3 }

mgmtOspf6StatusDbInterAreaPrefixLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 link state ID."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 4 }

mgmtOspf6StatusDbInterAreaPrefixAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 5 }

mgmtOspf6StatusDbInterAreaPrefixAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 6 }

mgmtOspf6StatusDbInterAreaPrefixOptions OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 option field which is present in OSPF6 hello packets, which
         enables OSPF6 routers to support (or not support) optional
         capabilities, and to communicate their capability level to other OSPF6
         routers."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 7 }

mgmtOspf6StatusDbInterAreaPrefixSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 8 }

mgmtOspf6StatusDbInterAreaPrefixChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 9 }

mgmtOspf6StatusDbInterAreaPrefixLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 10 }

mgmtOspf6StatusDbInterAreaPrefixPrefixAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv6 network address."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 11 }

mgmtOspf6StatusDbInterAreaPrefixIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv6 network mask length."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 12 }

mgmtOspf6StatusDbInterAreaPrefixMetric OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User specified metric for this summary route. The field is significant
         only when the link state type is 'Inter_Area Prefix/Router Link State'
         (Type 3, 4)."
    ::= { mgmtOspf6StatusDbInterAreaPrefixEntry 13 }

mgmtOspf6StatusDbInterAreaRouterTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusDbInterAreaRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF6 LSA ASBR Summary link state database information table."
    ::= { mgmtOspf6Status 15 }

mgmtOspf6StatusDbInterAreaRouterEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusDbInterAreaRouterEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspf6StatusDbInterAreaRouterInstanceId,
                  mgmtOspf6StatusDbInterAreaRouterAreaId,
                  mgmtOspf6StatusDbInterAreaRouterLsdbType,
                  mgmtOspf6StatusDbInterAreaRouterLinkStateId,
                  mgmtOspf6StatusDbInterAreaRouterAdvRouterId }
    ::= { mgmtOspf6StatusDbInterAreaRouterTable 1 }

MGMTOspf6StatusDbInterAreaRouterEntry ::= SEQUENCE {
    mgmtOspf6StatusDbInterAreaRouterInstanceId   Integer32,
    mgmtOspf6StatusDbInterAreaRouterAreaId       IpAddress,
    mgmtOspf6StatusDbInterAreaRouterLsdbType     MGMTOspf6LsdbType,
    mgmtOspf6StatusDbInterAreaRouterLinkStateId  IpAddress,
    mgmtOspf6StatusDbInterAreaRouterAdvRouterId  IpAddress,
    mgmtOspf6StatusDbInterAreaRouterAge          Unsigned32,
    mgmtOspf6StatusDbInterAreaRouterOptions      Unsigned32,
    mgmtOspf6StatusDbInterAreaRouterSequence     Unsigned32,
    mgmtOspf6StatusDbInterAreaRouterChecksum     Unsigned32,
    mgmtOspf6StatusDbInterAreaRouterLength       Unsigned32,
    mgmtOspf6StatusDbInterAreaRouterMetric       Unsigned32
}

mgmtOspf6StatusDbInterAreaRouterInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 1 }

mgmtOspf6StatusDbInterAreaRouterAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 2 }

mgmtOspf6StatusDbInterAreaRouterLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspf6LsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 3 }

mgmtOspf6StatusDbInterAreaRouterLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 link state ID."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 4 }

mgmtOspf6StatusDbInterAreaRouterAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 5 }

mgmtOspf6StatusDbInterAreaRouterAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 6 }

mgmtOspf6StatusDbInterAreaRouterOptions OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 option field which is present in OSPF6 hello packets, which
         enables OSPF6 routers to support (or not support) optional
         capabilities, and to communicate their capability level to other OSPF6
         routers."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 7 }

mgmtOspf6StatusDbInterAreaRouterSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 8 }

mgmtOspf6StatusDbInterAreaRouterChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 9 }

mgmtOspf6StatusDbInterAreaRouterLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 10 }

mgmtOspf6StatusDbInterAreaRouterMetric OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User specified metric for this summary route. The field is significant
         only when the link state type is 'Summary/ASBR Summary Link State'
         (Type 3, 4)."
    ::= { mgmtOspf6StatusDbInterAreaRouterEntry 11 }

mgmtOspf6StatusDbExternalTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusDbExternalEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF6 LSA External link state database information table."
    ::= { mgmtOspf6Status 16 }

mgmtOspf6StatusDbExternalEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusDbExternalEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspf6StatusDbExternalInstanceId,
                  mgmtOspf6StatusDbExternalAreaId,
                  mgmtOspf6StatusDbExternalLsdbType,
                  mgmtOspf6StatusDbExternalLinkStateId,
                  mgmtOspf6StatusDbExternalAdvRouterId }
    ::= { mgmtOspf6StatusDbExternalTable 1 }

MGMTOspf6StatusDbExternalEntry ::= SEQUENCE {
    mgmtOspf6StatusDbExternalInstanceId          Integer32,
    mgmtOspf6StatusDbExternalAreaId              IpAddress,
    mgmtOspf6StatusDbExternalLsdbType            MGMTOspf6LsdbType,
    mgmtOspf6StatusDbExternalLinkStateId         IpAddress,
    mgmtOspf6StatusDbExternalAdvRouterId         IpAddress,
    mgmtOspf6StatusDbExternalAge                 Unsigned32,
    mgmtOspf6StatusDbExternalOptions             Unsigned32,
    mgmtOspf6StatusDbExternalSequence            Unsigned32,
    mgmtOspf6StatusDbExternalChecksum            Unsigned32,
    mgmtOspf6StatusDbExternalLength              Unsigned32,
    mgmtOspf6StatusDbExternalPrefixAddress       InetAddressIPv6,
    mgmtOspf6StatusDbExternalIpSubnetMaskLength  Integer32,
    mgmtOspf6StatusDbExternalMetric              Unsigned32,
    mgmtOspf6StatusDbExternalMetricType          Unsigned32,
    mgmtOspf6StatusDbExternalForwardAddress      InetAddressIPv6
}

mgmtOspf6StatusDbExternalInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusDbExternalEntry 1 }

mgmtOspf6StatusDbExternalAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusDbExternalEntry 2 }

mgmtOspf6StatusDbExternalLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspf6LsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspf6StatusDbExternalEntry 3 }

mgmtOspf6StatusDbExternalLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 link state ID."
    ::= { mgmtOspf6StatusDbExternalEntry 4 }

mgmtOspf6StatusDbExternalAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspf6StatusDbExternalEntry 5 }

mgmtOspf6StatusDbExternalAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspf6StatusDbExternalEntry 6 }

mgmtOspf6StatusDbExternalOptions OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 option field which is present in OSPF6 hello packets, which
         enables OSPF6 routers to support (or not support) optional
         capabilities, and to communicate their capability level to other OSPF6
         routers."
    ::= { mgmtOspf6StatusDbExternalEntry 7 }

mgmtOspf6StatusDbExternalSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspf6StatusDbExternalEntry 8 }

mgmtOspf6StatusDbExternalChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspf6StatusDbExternalEntry 9 }

mgmtOspf6StatusDbExternalLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspf6StatusDbExternalEntry 10 }

mgmtOspf6StatusDbExternalPrefixAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv6 network address."
    ::= { mgmtOspf6StatusDbExternalEntry 11 }

mgmtOspf6StatusDbExternalIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..128)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "IPv6 network mask length."
    ::= { mgmtOspf6StatusDbExternalEntry 12 }

mgmtOspf6StatusDbExternalMetric OBJECT-TYPE
    SYNTAX      Unsigned32 (0..16777215)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "User specified metric for this summary route. The field is significant
         only when the link state type is 'External/NSSA External Link State'
         (Type 5, 7)."
    ::= { mgmtOspf6StatusDbExternalEntry 13 }

mgmtOspf6StatusDbExternalMetricType OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The External type of the LSA. The field is significant only when the
         link state type is 'External/NSSA External Link State' (Type 5, 7)."
    ::= { mgmtOspf6StatusDbExternalEntry 14 }

mgmtOspf6StatusDbExternalForwardAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IP address of forward address. The field is significant only when
         the link state type is 'External/NSSA External Link State' (Type 5, 7)."
    ::= { mgmtOspf6StatusDbExternalEntry 15 }

mgmtOspf6StatusDbLinkTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusDbLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF6 LSA Link link state database information table."
    ::= { mgmtOspf6Status 17 }

mgmtOspf6StatusDbLinkEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusDbLinkEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspf6StatusDbLinkInstanceId,
                  mgmtOspf6StatusDbLinkAreaId,
                  mgmtOspf6StatusDbLinkLsdbType,
                  mgmtOspf6StatusDbLinkLinkStateId,
                  mgmtOspf6StatusDbLinkAdvRouterId }
    ::= { mgmtOspf6StatusDbLinkTable 1 }

MGMTOspf6StatusDbLinkEntry ::= SEQUENCE {
    mgmtOspf6StatusDbLinkInstanceId        Integer32,
    mgmtOspf6StatusDbLinkAreaId            IpAddress,
    mgmtOspf6StatusDbLinkLsdbType          MGMTOspf6LsdbType,
    mgmtOspf6StatusDbLinkLinkStateId       IpAddress,
    mgmtOspf6StatusDbLinkAdvRouterId       IpAddress,
    mgmtOspf6StatusDbLinkAge               Unsigned32,
    mgmtOspf6StatusDbLinkOptions           Unsigned32,
    mgmtOspf6StatusDbLinkSequence          Unsigned32,
    mgmtOspf6StatusDbLinkChecksum          Unsigned32,
    mgmtOspf6StatusDbLinkLength            Unsigned32,
    mgmtOspf6StatusDbLinkLinkLocalAddress  InetAddressIPv6,
    mgmtOspf6StatusDbLinkPrefixCnt         Unsigned32
}

mgmtOspf6StatusDbLinkInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusDbLinkEntry 1 }

mgmtOspf6StatusDbLinkAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusDbLinkEntry 2 }

mgmtOspf6StatusDbLinkLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspf6LsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspf6StatusDbLinkEntry 3 }

mgmtOspf6StatusDbLinkLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 link state ID."
    ::= { mgmtOspf6StatusDbLinkEntry 4 }

mgmtOspf6StatusDbLinkAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspf6StatusDbLinkEntry 5 }

mgmtOspf6StatusDbLinkAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspf6StatusDbLinkEntry 6 }

mgmtOspf6StatusDbLinkOptions OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 option field which is present in OSPF6 hello packets, which
         enables OSPF6 routers to support (or not support) optional
         capabilities, and to communicate their capability level to other OSPF6
         routers."
    ::= { mgmtOspf6StatusDbLinkEntry 7 }

mgmtOspf6StatusDbLinkSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspf6StatusDbLinkEntry 8 }

mgmtOspf6StatusDbLinkChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspf6StatusDbLinkEntry 9 }

mgmtOspf6StatusDbLinkLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspf6StatusDbLinkEntry 10 }

mgmtOspf6StatusDbLinkLinkLocalAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Link Local Address of the LSA."
    ::= { mgmtOspf6StatusDbLinkEntry 11 }

mgmtOspf6StatusDbLinkPrefixCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The count of the LSA."
    ::= { mgmtOspf6StatusDbLinkEntry 12 }

mgmtOspf6StatusDbintraAreaPrefixTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTOspf6StatusDbintraAreaPrefixEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The OSPF6 LSA Link link state database information table."
    ::= { mgmtOspf6Status 18 }

mgmtOspf6StatusDbintraAreaPrefixEntry OBJECT-TYPE
    SYNTAX      MGMTOspf6StatusDbintraAreaPrefixEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the corresponding information of a single link state
         advertisement."
    INDEX       { mgmtOspf6StatusDbintraAreaPrefixInstanceId,
                  mgmtOspf6StatusDbintraAreaPrefixAreaId,
                  mgmtOspf6StatusDbintraAreaPrefixLsdbType,
                  mgmtOspf6StatusDbintraAreaPrefixLinkStateId,
                  mgmtOspf6StatusDbintraAreaPrefixAdvRouterId }
    ::= { mgmtOspf6StatusDbintraAreaPrefixTable 1 }

MGMTOspf6StatusDbintraAreaPrefixEntry ::= SEQUENCE {
    mgmtOspf6StatusDbintraAreaPrefixInstanceId        Integer32,
    mgmtOspf6StatusDbintraAreaPrefixAreaId            IpAddress,
    mgmtOspf6StatusDbintraAreaPrefixLsdbType          MGMTOspf6LsdbType,
    mgmtOspf6StatusDbintraAreaPrefixLinkStateId       IpAddress,
    mgmtOspf6StatusDbintraAreaPrefixAdvRouterId       IpAddress,
    mgmtOspf6StatusDbintraAreaPrefixAge               Unsigned32,
    mgmtOspf6StatusDbintraAreaPrefixOptions           Unsigned32,
    mgmtOspf6StatusDbintraAreaPrefixSequence          Unsigned32,
    mgmtOspf6StatusDbintraAreaPrefixChecksum          Unsigned32,
    mgmtOspf6StatusDbintraAreaPrefixLength            Unsigned32,
    mgmtOspf6StatusDbintraAreaPrefixLinkLocalAddress  InetAddressIPv6,
    mgmtOspf6StatusDbintraAreaPrefixPrefixCnt         Unsigned32
}

mgmtOspf6StatusDbintraAreaPrefixInstanceId OBJECT-TYPE
    SYNTAX      Integer32 (1..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 process instance ID."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 1 }

mgmtOspf6StatusDbintraAreaPrefixAreaId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 area ID."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 2 }

mgmtOspf6StatusDbintraAreaPrefixLsdbType OBJECT-TYPE
    SYNTAX      MGMTOspf6LsdbType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The type of the link state advertisement."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 3 }

mgmtOspf6StatusDbintraAreaPrefixLinkStateId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The OSPF6 link state ID."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 4 }

mgmtOspf6StatusDbintraAreaPrefixAdvRouterId OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The advertising router ID which originated the LSA."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 5 }

mgmtOspf6StatusDbintraAreaPrefixAge OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in seconds since the LSA was originated."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 6 }

mgmtOspf6StatusDbintraAreaPrefixOptions OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The OSPF6 option field which is present in OSPF6 hello packets, which
         enables OSPF6 routers to support (or not support) optional
         capabilities, and to communicate their capability level to other OSPF6
         routers."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 7 }

mgmtOspf6StatusDbintraAreaPrefixSequence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LS sequence number of the LSA."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 8 }

mgmtOspf6StatusDbintraAreaPrefixChecksum OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The checksum of the LSA contents."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 9 }

mgmtOspf6StatusDbintraAreaPrefixLength OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Length in bytes of the LSA."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 10 }

mgmtOspf6StatusDbintraAreaPrefixLinkLocalAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Link Local Address of the LSA."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 11 }

mgmtOspf6StatusDbintraAreaPrefixPrefixCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The count of the LSA."
    ::= { mgmtOspf6StatusDbintraAreaPrefixEntry 12 }

mgmtOspf6Control OBJECT IDENTIFIER
    ::= { mgmtOspf6MibObjects 4 }

mgmtOspf6ControlGlobals OBJECT IDENTIFIER
    ::= { mgmtOspf6Control 4 }

mgmtOspf6ControlGlobalsReloadProcess OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set true to reload OSPF6 process."
    ::= { mgmtOspf6ControlGlobals 1 }

mgmtOspf6MibConformance OBJECT IDENTIFIER
    ::= { mgmtOspf6Mib 2 }

mgmtOspf6MibCompliances OBJECT IDENTIFIER
    ::= { mgmtOspf6MibConformance 1 }

mgmtOspf6MibGroups OBJECT IDENTIFIER
    ::= { mgmtOspf6MibConformance 2 }

mgmtOspf6CapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6CapabilitiesMinInstanceId,
                  mgmtOspf6CapabilitiesMaxInstanceId,
                  mgmtOspf6CapabilitiesMinRouterId,
                  mgmtOspf6CapabilitiesMaxRouterId,
                  mgmtOspf6CapabilitiesMinPriority,
                  mgmtOspf6CapabilitiesMaxPriority,
                  mgmtOspf6CapabilitiesMinGeneralCost,
                  mgmtOspf6CapabilitiesMaxGeneralCost,
                  mgmtOspf6CapabilitiesMinInterfaceCost,
                  mgmtOspf6CapabilitiesMaxInterfaceCost,
                  mgmtOspf6CapabilitiesMinHelloInterval,
                  mgmtOspf6CapabilitiesMaxHelloInterval,
                  mgmtOspf6CapabilitiesMinRetransmitInterval,
                  mgmtOspf6CapabilitiesMaxRetransmitInterval,
                  mgmtOspf6CapabilitiesMinDeadInterval,
                  mgmtOspf6CapabilitiesMaxDeadInterval,
                  mgmtOspf6CapabilitiesIsRipngRedistributedSupported,
                  mgmtOspf6CapabilitiesMinAdminDistance,
                  mgmtOspf6CapabilitiesMaxAdminDistance }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 1 }

mgmtOspf6ConfigProcessTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ConfigProcessInstanceId,
                  mgmtOspf6ConfigProcessAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 2 }

mgmtOspf6ConfigProcessTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ConfigProcessTableRowEditorInstanceId,
                  mgmtOspf6ConfigProcessTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 3 }

mgmtOspf6ConfigRouterTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ConfigRouterInstanceId,
                  mgmtOspf6ConfigRouterIsSpecificRouterId,
                  mgmtOspf6ConfigRouterRouterId,
                  mgmtOspf6ConfigRouterConnectedRedistEnable,
                  mgmtOspf6ConfigRouterStaticRedistEnable,
                  mgmtOspf6ConfigRouterAdminDistance }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 4 }

mgmtOspf6ConfigRouterInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ConfigRouterInterfaceInstanceId,
                  mgmtOspf6ConfigRouterInterfaceIfIndex,
                  mgmtOspf6ConfigRouterInterfaceIsSpecificAreaId,
                  mgmtOspf6ConfigRouterInterfaceAreaId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 5 }

mgmtOspf6ConfigAreaRangeTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ConfigAreaRangeInstanceId,
                  mgmtOspf6ConfigAreaRangeAreaId,
                  mgmtOspf6ConfigAreaRangeNetwork,
                  mgmtOspf6ConfigAreaRangeIpSubnetMaskLength,
                  mgmtOspf6ConfigAreaRangeAdvertised,
                  mgmtOspf6ConfigAreaRangeIsSpecificCost,
                  mgmtOspf6ConfigAreaRangeCost,
                  mgmtOspf6ConfigAreaRangeAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 6 }

mgmtOspf6ConfigAreaRangeTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ConfigAreaRangeTableRowEditorInstanceId,
                  mgmtOspf6ConfigAreaRangeTableRowEditorAreaId,
                  mgmtOspf6ConfigAreaRangeTableRowEditorNetwork,
                  mgmtOspf6ConfigAreaRangeTableRowEditorIpSubnetMaskLength,
                  mgmtOspf6ConfigAreaRangeTableRowEditorAdvertised,
                  mgmtOspf6ConfigAreaRangeTableRowEditorIsSpecificCost,
                  mgmtOspf6ConfigAreaRangeTableRowEditorCost,
                  mgmtOspf6ConfigAreaRangeTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 7 }

mgmtOspf6ConfigStubAreaTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ConfigStubAreaInstanceId,
                  mgmtOspf6ConfigStubAreaAreaId,
                  mgmtOspf6ConfigStubAreaNoSummary,
                  mgmtOspf6ConfigStubAreaAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 8 }

mgmtOspf6ConfigStubAreaTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ConfigStubAreaTableRowEditorInstanceId,
                  mgmtOspf6ConfigStubAreaTableRowEditorAreaId,
                  mgmtOspf6ConfigStubAreaTableRowEditorNoSummary,
                  mgmtOspf6ConfigStubAreaTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 9 }

mgmtOspf6ConfigInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ConfigInterfaceIfIndex,
                  mgmtOspf6ConfigInterfacePriority,
                  mgmtOspf6ConfigInterfaceIsSpecificCost,
                  mgmtOspf6ConfigInterfaceCost,
                  mgmtOspf6ConfigInterfaceDeadInterval,
                  mgmtOspf6ConfigInterfaceHelloInterval,
                  mgmtOspf6ConfigInterfaceRetransmitInterval,
                  mgmtOspf6ConfigInterfaceIsPassiveInterface,
                  mgmtOspf6ConfigInterfaceTransmitDelay }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 10 }

mgmtOspf6StatusRouterTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusRouterInstanceId,
                  mgmtOspf6StatusRouterRouterId,
                  mgmtOspf6StatusRouterSpfDelay,
                  mgmtOspf6StatusRouterSpfHoldTime,
                  mgmtOspf6StatusRouterSpfMaxWaitTime,
                  mgmtOspf6StatusRouterLastExcutedSpfTs,
                  mgmtOspf6StatusRouterAttachedAreaCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 11 }

mgmtOspf6StatusRouteAreaTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusRouteAreaInstanceId,
                  mgmtOspf6StatusRouteAreaAreaId,
                  mgmtOspf6StatusRouteAreaIsBackbone,
                  mgmtOspf6StatusRouteAreaAreaType,
                  mgmtOspf6StatusRouteAreaAttachedIntfCount,
                  mgmtOspf6StatusRouteAreaSpfExecutedCount,
                  mgmtOspf6StatusRouteAreaLsaCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 12 }

mgmtOspf6StatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusInterfaceIfIndex,
                  mgmtOspf6StatusInterfaceStatus,
                  mgmtOspf6StatusInterfacePassive,
                  mgmtOspf6StatusInterfaceNetwork,
                  mgmtOspf6StatusInterfaceIpSubnetMaskLength,
                  mgmtOspf6StatusInterfaceAreaId,
                  mgmtOspf6StatusInterfaceRouterId,
                  mgmtOspf6StatusInterfaceCost,
                  mgmtOspf6StatusInterfaceState,
                  mgmtOspf6StatusInterfacePriority,
                  mgmtOspf6StatusInterfaceDrId,
                  mgmtOspf6StatusInterfaceBdrId,
                  mgmtOspf6StatusInterfaceHelloTime,
                  mgmtOspf6StatusInterfaceDeadTime,
                  mgmtOspf6StatusInterfaceRetransmitTime,
                  mgmtOspf6StatusInterfaceTransmitDelay }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 13 }

mgmtOspf6StatusNeighborIpv6TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusNeighborIpv6InstanceId,
                  mgmtOspf6StatusNeighborIpv6RouterId,
                  mgmtOspf6StatusNeighborIpv6Ipv6Addr,
                  mgmtOspf6StatusNeighborIpv6IfIndex,
                  mgmtOspf6StatusNeighborIpv6IpAddr,
                  mgmtOspf6StatusNeighborIpv6AreaId,
                  mgmtOspf6StatusNeighborIpv6NeighborId,
                  mgmtOspf6StatusNeighborIpv6Priority,
                  mgmtOspf6StatusNeighborIpv6State,
                  mgmtOspf6StatusNeighborIpv6DrId,
                  mgmtOspf6StatusNeighborIpv6BdrId,
                  mgmtOspf6StatusNeighborIpv6DeadTime,
                  mgmtOspf6StatusNeighborIpv6TransitAreaId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 14 }

mgmtOspf6StatusRouteIpv6TableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusRouteIpv6InstanceId,
                  mgmtOspf6StatusRouteIpv6RouteType,
                  mgmtOspf6StatusRouteIpv6Network,
                  mgmtOspf6StatusRouteIpv6IpSubnetMaskLength,
                  mgmtOspf6StatusRouteIpv6AreaId,
                  mgmtOspf6StatusRouteIpv6NextHop,
                  mgmtOspf6StatusRouteIpv6Cost,
                  mgmtOspf6StatusRouteIpv6AsCost,
                  mgmtOspf6StatusRouteIpv6BorderRouterType,
                  mgmtOspf6StatusRouteIpv6Ifindex,
                  mgmtOspf6StatusRouteIpv6IsConnected }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 15 }

mgmtOspf6StatusDbTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusDbInstanceId,
                  mgmtOspf6StatusDbAreaId, mgmtOspf6StatusDbLsdbType,
                  mgmtOspf6StatusDbLinkStateId,
                  mgmtOspf6StatusDbAdvRouterId, mgmtOspf6StatusDbAge,
                  mgmtOspf6StatusDbSequence,
                  mgmtOspf6StatusDbRouterLinkCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 16 }

mgmtOspf6StatusDbRouterTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusDbRouterInstanceId,
                  mgmtOspf6StatusDbRouterAreaId,
                  mgmtOspf6StatusDbRouterLsdbType,
                  mgmtOspf6StatusDbRouterLinkStateId,
                  mgmtOspf6StatusDbRouterAdvRouterId,
                  mgmtOspf6StatusDbRouterAge,
                  mgmtOspf6StatusDbRouterOptions,
                  mgmtOspf6StatusDbRouterSequence,
                  mgmtOspf6StatusDbRouterChecksum,
                  mgmtOspf6StatusDbRouterLength,
                  mgmtOspf6StatusDbRouterRouterLinkCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 17 }

mgmtOspf6StatusDbNetworkTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusDbNetworkInstanceId,
                  mgmtOspf6StatusDbNetworkAreaId,
                  mgmtOspf6StatusDbNetworkLsdbType,
                  mgmtOspf6StatusDbNetworkLinkStateId,
                  mgmtOspf6StatusDbNetworkAdvRouterId,
                  mgmtOspf6StatusDbNetworkAge,
                  mgmtOspf6StatusDbNetworkOptions,
                  mgmtOspf6StatusDbNetworkSequence,
                  mgmtOspf6StatusDbNetworkChecksum,
                  mgmtOspf6StatusDbNetworkLength,
                  mgmtOspf6StatusDbNetworkAttachedRouterCount }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 18 }

mgmtOspf6StatusDbInterAreaPrefixTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusDbInterAreaPrefixInstanceId,
                  mgmtOspf6StatusDbInterAreaPrefixAreaId,
                  mgmtOspf6StatusDbInterAreaPrefixLsdbType,
                  mgmtOspf6StatusDbInterAreaPrefixLinkStateId,
                  mgmtOspf6StatusDbInterAreaPrefixAdvRouterId,
                  mgmtOspf6StatusDbInterAreaPrefixAge,
                  mgmtOspf6StatusDbInterAreaPrefixOptions,
                  mgmtOspf6StatusDbInterAreaPrefixSequence,
                  mgmtOspf6StatusDbInterAreaPrefixChecksum,
                  mgmtOspf6StatusDbInterAreaPrefixLength,
                  mgmtOspf6StatusDbInterAreaPrefixPrefixAddress,
                  mgmtOspf6StatusDbInterAreaPrefixIpSubnetMaskLength,
                  mgmtOspf6StatusDbInterAreaPrefixMetric }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 19 }

mgmtOspf6StatusDbInterAreaRouterTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusDbInterAreaRouterInstanceId,
                  mgmtOspf6StatusDbInterAreaRouterAreaId,
                  mgmtOspf6StatusDbInterAreaRouterLsdbType,
                  mgmtOspf6StatusDbInterAreaRouterLinkStateId,
                  mgmtOspf6StatusDbInterAreaRouterAdvRouterId,
                  mgmtOspf6StatusDbInterAreaRouterAge,
                  mgmtOspf6StatusDbInterAreaRouterOptions,
                  mgmtOspf6StatusDbInterAreaRouterSequence,
                  mgmtOspf6StatusDbInterAreaRouterChecksum,
                  mgmtOspf6StatusDbInterAreaRouterLength,
                  mgmtOspf6StatusDbInterAreaRouterMetric }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 20 }

mgmtOspf6StatusDbExternalTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusDbExternalInstanceId,
                  mgmtOspf6StatusDbExternalAreaId,
                  mgmtOspf6StatusDbExternalLsdbType,
                  mgmtOspf6StatusDbExternalLinkStateId,
                  mgmtOspf6StatusDbExternalAdvRouterId,
                  mgmtOspf6StatusDbExternalAge,
                  mgmtOspf6StatusDbExternalOptions,
                  mgmtOspf6StatusDbExternalSequence,
                  mgmtOspf6StatusDbExternalChecksum,
                  mgmtOspf6StatusDbExternalLength,
                  mgmtOspf6StatusDbExternalPrefixAddress,
                  mgmtOspf6StatusDbExternalIpSubnetMaskLength,
                  mgmtOspf6StatusDbExternalMetric,
                  mgmtOspf6StatusDbExternalMetricType,
                  mgmtOspf6StatusDbExternalForwardAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 21 }

mgmtOspf6StatusDbLinkTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusDbLinkInstanceId,
                  mgmtOspf6StatusDbLinkAreaId,
                  mgmtOspf6StatusDbLinkLsdbType,
                  mgmtOspf6StatusDbLinkLinkStateId,
                  mgmtOspf6StatusDbLinkAdvRouterId,
                  mgmtOspf6StatusDbLinkAge,
                  mgmtOspf6StatusDbLinkOptions,
                  mgmtOspf6StatusDbLinkSequence,
                  mgmtOspf6StatusDbLinkChecksum,
                  mgmtOspf6StatusDbLinkLength,
                  mgmtOspf6StatusDbLinkLinkLocalAddress,
                  mgmtOspf6StatusDbLinkPrefixCnt }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 22 }

mgmtOspf6StatusDbintraAreaPrefixTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6StatusDbintraAreaPrefixInstanceId,
                  mgmtOspf6StatusDbintraAreaPrefixAreaId,
                  mgmtOspf6StatusDbintraAreaPrefixLsdbType,
                  mgmtOspf6StatusDbintraAreaPrefixLinkStateId,
                  mgmtOspf6StatusDbintraAreaPrefixAdvRouterId,
                  mgmtOspf6StatusDbintraAreaPrefixAge,
                  mgmtOspf6StatusDbintraAreaPrefixOptions,
                  mgmtOspf6StatusDbintraAreaPrefixSequence,
                  mgmtOspf6StatusDbintraAreaPrefixChecksum,
                  mgmtOspf6StatusDbintraAreaPrefixLength,
                  mgmtOspf6StatusDbintraAreaPrefixLinkLocalAddress,
                  mgmtOspf6StatusDbintraAreaPrefixPrefixCnt }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 23 }

mgmtOspf6ControlGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtOspf6ControlGlobalsReloadProcess }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtOspf6MibGroups 24 }

mgmtOspf6MibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtOspf6CapabilitiesInfoGroup,
                       mgmtOspf6ConfigProcessTableInfoGroup,
                       mgmtOspf6ConfigProcessTableRowEditorInfoGroup,
                       mgmtOspf6ConfigRouterTableInfoGroup,
                       mgmtOspf6ConfigRouterInterfaceTableInfoGroup,
                       mgmtOspf6ConfigAreaRangeTableInfoGroup,
                       mgmtOspf6ConfigAreaRangeTableRowEditorInfoGroup,
                       mgmtOspf6ConfigStubAreaTableInfoGroup,
                       mgmtOspf6ConfigStubAreaTableRowEditorInfoGroup,
                       mgmtOspf6ConfigInterfaceTableInfoGroup,
                       mgmtOspf6StatusRouterTableInfoGroup,
                       mgmtOspf6StatusRouteAreaTableInfoGroup,
                       mgmtOspf6StatusInterfaceTableInfoGroup,
                       mgmtOspf6StatusNeighborIpv6TableInfoGroup,
                       mgmtOspf6StatusRouteIpv6TableInfoGroup,
                       mgmtOspf6StatusDbTableInfoGroup,
                       mgmtOspf6StatusDbRouterTableInfoGroup,
                       mgmtOspf6StatusDbNetworkTableInfoGroup,
                       mgmtOspf6StatusDbInterAreaPrefixTableInfoGroup,
                       mgmtOspf6StatusDbInterAreaRouterTableInfoGroup,
                       mgmtOspf6StatusDbExternalTableInfoGroup,
                       mgmtOspf6StatusDbLinkTableInfoGroup,
                       mgmtOspf6StatusDbintraAreaPrefixTableInfoGroup,
                       mgmtOspf6ControlGlobalsInfoGroup }

    ::= { mgmtOspf6MibCompliances 1 }

END
