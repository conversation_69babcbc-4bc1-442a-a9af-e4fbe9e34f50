Atop-EHG2408-L2MGMT-MIB DEFINITIONS ::= BEGIN

IMPORTS

       DateAndTime
          FROM SNMPv2-TC
       enterprises
                FROM RFC1155-SMI;

atop              OBJECT IDENTIFIER ::= { enterprises 13576 }
atop-ieswitch     OBJECT IDENTIFIER ::= { atop 7 }
insatopEHG2408     OBJECT IDENTIFIER ::= { atop-ieswitch 5001 }

atopEHG2408 MODULE-IDENTITY
              LAST-UPDATED "1503050000Z"
              ORGANIZATION ""
              CONTACT-INFO
                " :         		
            
            
            	"

        DESCRIPTION
                "The MIB module for Industrial Ethernet Switch"
        REVISION "1503050000Z"
        DESCRIPTION
                "Initial version of this MIB."
        ::= { insatopEHG2408 1 }

systemInfo            OBJECT IDENTIFIER ::= { atopEHG2408 1 }
basicSetting          OBJECT IDENTIFIER ::= { atopEHG2408 2 }
snmp                  OBJECT IDENTIFIER ::= { atopEHG2408 8 }

DisplayString ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS       current
    DESCRIPTION  ""
    SYNTAX       OCTET STRING (SIZE (0..255))

MacAddress ::= OCTET STRING (SIZE (6))    -- a 6 octet address
                                          -- in the
                                          -- "canonical"
                                          -- order

PortList ::= OCTET STRING

--
-- systemInfo
--

systemDescr OBJECT-TYPE
       SYNTAX  DisplayString (SIZE (0..64))
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "A textual description of the entity.  This value
                      should include the full name and version
                      identification of the system's hardware type,
                      software operating-system, and networking
                      software.  It is mandatory that this only contain
                      printable ASCII characters."
       ::= { systemInfo 4 }

systemFwVer OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..20))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Firmware version of the device."
        ::= { systemInfo 5 }

systemMacAddress OBJECT-TYPE
       SYNTAX      MacAddress
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION "The MAC address of switch."
       ::= { systemInfo 6 }

systemKernelVer OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..20))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Kernel version of the device."
        ::= { systemInfo 7 }

systemModelName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..32))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Model name of the device."
        ::= { systemInfo 10 }

consoleInfo                  OBJECT IDENTIFIER ::= { systemInfo 8 }

consoleInfoBaudRate OBJECT-TYPE
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console baud rate(bits/second)."
       ::= { consoleInfo 1 }

consoleInfoDataBits OBJECT-TYPE
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console data bits."
       ::= { consoleInfo 2 }

consoleInfoParity OBJECT-TYPE
       SYNTAX  INTEGER
               {
                   none(1)
               }
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console parity check."
       ::= { consoleInfo 3 }

consoleInfoStopBit OBJECT-TYPE
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console stop bit."
       ::= { consoleInfo 4 }

consoleInfoFlowControl OBJECT-TYPE
       SYNTAX  INTEGER
               {
                   none(1)
               }
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console flow control."
       ::= { consoleInfo 5 }

--
-- basicSetting
--

adminPassword         OBJECT IDENTIFIER ::= { basicSetting 2 }
ipConfiguration       OBJECT IDENTIFIER ::= { basicSetting 3 }
factoryDefault        OBJECT IDENTIFIER ::= { basicSetting 8 }
systemReboot          OBJECT IDENTIFIER ::= { basicSetting 9 }

--
-- adminPassword
--

adminPasswordUserName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..15))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The name(ID) of security manager."
        ::= { adminPassword 1 }

adminPasswordPassword OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(6..15))
        MAX-ACCESS  write-only
        STATUS      current
        DESCRIPTION "The password of security manager.
                     This object can't be read. it's write-only."
        ::= { adminPassword 2 }

--
-- ipConfiguration
--

ipConfigurationTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF IPconfigurationEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     IP of switch."
        ::= { ipConfiguration 1 }

ipConfigurationEntry OBJECT-TYPE
        SYNTAX      IPconfigurationEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing IP/DHCP-client information
                     of the switch. "
        INDEX       { ipConfigurationIndex }
        ::= { ipConfigurationTable 1 }

IPconfigurationEntry ::= SEQUENCE
{
    ipConfigurationIndex             Integer32,
    ipConfigurationDHCPStatus        INTEGER,
    ipConfigurationAddress           IpAddress,
    ipConfigurationSubMask           IpAddress,
    ipConfigurationGateway           IpAddress,
    ipConfigurationDNS1              IpAddress
-- // [20190906][#13529][VincentYao][EHG2408][systemMIB-DNS2]WAGO's web haven't DNS2,so MIB shouldn't appear DNS2	
--    ipConfigurationDNS2              IpAddress
}

ipConfigurationIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "This object identifies the switch within the system
                     for which this entry contains information. This
                     value can never be greater than switchNumber."
        ::= { ipConfigurationEntry 1 }


ipConfigurationDHCPStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to enable or disable DHCP client function of the
                     switch. When enabled, device will be a DHCP client and request
                     the IP configuration from DHCP server.
                     Note: Other items in this table couldn't be modified, when
                     ipConfigurationDHCPStatus is enabled."
        ::= { ipConfigurationEntry 2 }

ipConfigurationAddress OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The IP address of switch."
        ::= { ipConfigurationEntry 3 }

ipConfigurationSubMask OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The subnet mask of switch."
        ::= { ipConfigurationEntry 4 }

ipConfigurationGateway OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The gateway address of switch."
        ::= { ipConfigurationEntry 5 }

ipConfigurationDNS1 OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The DNS1 address of switch."
        ::= { ipConfigurationEntry 6 }

-- // [20190906][#13529][VincentYao][EHG2408][systemMIB-DNS2]WAGO's web haven't DNS2,so MIB shouldn't appear DNS2 
--ipConfigurationDNS2 OBJECT-TYPE
--        SYNTAX      IpAddress
--        MAX-ACCESS  read-write
--        STATUS      current
--        DESCRIPTION "The DNS2 address of switch."
--        ::= { ipConfigurationEntry 7 }

--
-- factoryDefault
--

factoryDefaultAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger loading
                     factory default configuration. Need to reboot mamually when active factory default.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { factoryDefault 1 }

--
-- systemReboot
--

systemRebootAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger the system
                     restart.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { systemReboot 1 }

snmpCommunityStringTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SnmpCommunityStringEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and information about
                     community string of SNMP agent."
        ::= { snmp 5 }         
        
snmpCommunityStringEntry OBJECT-TYPE
        SYNTAX      SnmpCommunityStringEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about community string of SNMP agent."
        INDEX       { snmpCommunityStringIndex }
        ::= { snmpCommunityStringTable 1 }

SnmpCommunityStringEntry ::= SEQUENCE
{
    snmpCommunityStringIndex      Integer32,
    snmpCommunityStringName       DisplayString,
    snmpCommunityStringAttribute  INTEGER,
    snmpCommunityStringStatus     INTEGER
}

snmpCommunityStringIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of community string."
        ::= { snmpCommunityStringEntry 1 }

snmpCommunityStringName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..15))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The community string of SNMP agent."
        ::= { snmpCommunityStringEntry 2 }

snmpCommunityStringAttribute OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        ro(1),
                        rw(2),
                        r-sysinfo-only(3)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The attribute of community string."
        ::= { snmpCommunityStringEntry 3 }

snmpCommunityStringStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        creatrequest(2),
                        undercreation(3),
                        invalid(4)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of this entry. If this object is not equal to valid(1), all associated
 	                 SNMP entries shall be deleted by the agent."
        ::= { snmpCommunityStringEntry 4 }

snmpTrapServerTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SnmpTrapServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and information about
                     trap server of SNMP agent."
        ::= { snmp 6 }

snmpTrapServerEntry OBJECT-TYPE
        SYNTAX      SnmpTrapServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about trap server of SNMP agent."
        INDEX       { snmpTrapServerIndex }
        ::= { snmpTrapServerTable 1 }

SnmpTrapServerEntry ::= SEQUENCE
{
    snmpTrapServerIndex      Integer32,
    snmpTrapServerIPAddr     IpAddress,
    snmpTrapServerTrapComm   DisplayString,
    snmpTrapServerStatus     INTEGER,
    snmpTrapServerPort       Integer32,   
    snmpTrapServerIP         DisplayString
}

snmpTrapServerIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of trap server."
        ::= { snmpTrapServerEntry 1 }

-- snmpTrapServerIPAddr OBJECT-TYPE
--         SYNTAX      IpAddress
--         MAX-ACCESS  read-write
--         STATUS      current
--         DESCRIPTION "Trap Server IP Address."
--         ::= { snmpTrapServerEntry 2 }

snmpTrapServerTrapComm OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..15))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The community string of trap server."
        ::= { snmpTrapServerEntry 3 }

snmpTrapServerStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        creatrequest(2),
                        undercreation(3),
                        invalid(4)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of this entry. If this object is not equal to valid(1), all associated
 	                 SNMP entries shall be deleted by the agent."
        ::= { snmpTrapServerEntry 5 }

snmpTrapServerPort OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The port number of the trap server."
        ::= { snmpTrapServerEntry 6 }

snmpTrapServerIP OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..128))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Trap Server IP Address(IPv4 or IPv6)."
        ::= { snmpTrapServerEntry 7 }		
		
snmpUserTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SnmpUserEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and information about
                     the SNMP user configuration."
        ::= { snmp 7 }

snmpUserEntry OBJECT-TYPE
        SYNTAX      SnmpUserEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about the SNMP user configuration."
        INDEX       { snmpUserIndex }
        ::= { snmpUserTable 1 }

SnmpUserEntry ::= SEQUENCE
{
    snmpUserIndex      Integer32,
    snmpUserName       DisplayString,
    snmpUserAuthType   INTEGER,
    snmpUserAuthPwd    DisplayString,
    snmpUserEncrypType INTEGER,
    snmpUserEncrypKey  DisplayString,
    snmpUserStatus     INTEGER
}

snmpUserIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The index of the SNMP user entry"
        ::= { snmpUserEntry 1 }

snmpUserName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The SNMP user name"
        ::= { snmpUserEntry 2 }

snmpUserAuthType OBJECT-TYPE
        SYNTAX      INTEGER{
                        none(1),
                        md5(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The type of SNMP user authentication password."
        ::= { snmpUserEntry 3 }

snmpUserAuthPwd OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0|8..32))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The SNMP user authentication password."
        ::= { snmpUserEntry 4 }

snmpUserEncrypType OBJECT-TYPE
        SYNTAX      INTEGER{
                        none(1),
                        des(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The type of SNMP user privacy password."
        ::= { snmpUserEntry 5 }

snmpUserEncrypKey OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0|8..32))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The SNMP user privacy password."
        ::= { snmpUserEntry 6 }

snmpUserStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        invalid(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The SNMP user status"
        ::= { snmpUserEntry 7 }

snmpStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The SNMP status"
        ::= { snmp 8 }
END

