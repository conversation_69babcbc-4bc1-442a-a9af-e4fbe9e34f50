-- *****************************************************************
-- ARP-INSPECTION-MIB:  
-- ****************************************************************

MGMT-ARP-INSPECTION-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-<PERSON><PERSON>
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    ;

mgmtArpInspectionMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the ARP Inspection MIB"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 63 }


MGMTArpInspectionLogType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the ARP entry log type."
    SYNTAX      INTEGER { none(0), deny(1), permit(2), all(3) }

MGMTArpInspectionRegisterStatus ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the ARP entry registration type."
    SYNTAX      INTEGER { static(0), dynamic(1) }

mgmtArpInspectionMibObjects OBJECT IDENTIFIER
    ::= { mgmtArpInspectionMib 1 }

mgmtArpInspectionConfig OBJECT IDENTIFIER
    ::= { mgmtArpInspectionMibObjects 2 }

mgmtArpInspectionConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtArpInspectionConfig 1 }

mgmtArpInspectionConfigGlobalsAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the ARP Inspection global functionality."
    ::= { mgmtArpInspectionConfigGlobals 1 }

mgmtArpInspectionConfigPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTArpInspectionConfigPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing ARP Inspection per port basis"
    ::= { mgmtArpInspectionConfig 2 }

mgmtArpInspectionConfigPortEntry OBJECT-TYPE
    SYNTAX      MGMTArpInspectionConfigPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters"
    INDEX       { mgmtArpInspectionConfigPortIfIndex }
    ::= { mgmtArpInspectionConfigPortTable 1 }

MGMTArpInspectionConfigPortEntry ::= SEQUENCE {
    mgmtArpInspectionConfigPortIfIndex    MGMTInterfaceIndex,
    mgmtArpInspectionConfigPortMode       TruthValue,
    mgmtArpInspectionConfigPortCheckVlan  TruthValue,
    mgmtArpInspectionConfigPortLogType    MGMTArpInspectionLogType
}

mgmtArpInspectionConfigPortIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtArpInspectionConfigPortEntry 1 }

mgmtArpInspectionConfigPortMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the ARP Inspection per-port functionality. Only when
         both Global Mode and Port Mode on a given port are enabled, ARP
         Inspection is enabled on this given port."
    ::= { mgmtArpInspectionConfigPortEntry 2 }

mgmtArpInspectionConfigPortCheckVlan OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable the ARP Inspection VLAN checking will log the inspected entries
         by referring to arpInspectionVlanConfigTable setting. Disable the ARP
         Inspection VLAN checking will log the inspected entries by referring to
         arpInspectionPortConfigTable setting."
    ::= { mgmtArpInspectionConfigPortEntry 3 }

mgmtArpInspectionConfigPortLogType OBJECT-TYPE
    SYNTAX      MGMTArpInspectionLogType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The capability to log the inspected entries per port basis. none(0)
         will log nothing. deny(1) will log the denied entries. permit(2) will
         log the permitted entries. all(3) will log all kinds of inspected
         entries."
    ::= { mgmtArpInspectionConfigPortEntry 4 }

mgmtArpInspectionConfigVlanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTArpInspectionConfigVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing ARP Inspection per VLAN basis"
    ::= { mgmtArpInspectionConfig 3 }

mgmtArpInspectionConfigVlanEntry OBJECT-TYPE
    SYNTAX      MGMTArpInspectionConfigVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each VLAN has a set of parameters"
    INDEX       { mgmtArpInspectionConfigVlanVlanId }
    ::= { mgmtArpInspectionConfigVlanTable 1 }

MGMTArpInspectionConfigVlanEntry ::= SEQUENCE {
    mgmtArpInspectionConfigVlanVlanId   Integer32,
    mgmtArpInspectionConfigVlanLogType  MGMTArpInspectionLogType,
    mgmtArpInspectionConfigVlanAction   MGMTRowEditorState
}

mgmtArpInspectionConfigVlanVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The VID of the VLAN."
    ::= { mgmtArpInspectionConfigVlanEntry 1 }

mgmtArpInspectionConfigVlanLogType OBJECT-TYPE
    SYNTAX      MGMTArpInspectionLogType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The capability to log the inspected entries per VLAN basis. none(0)
         will log nothing. deny(1) will log the denied entries. permit(2) will
         log the permitted entries. all(3) will log all kinds of inspected
         entries."
    ::= { mgmtArpInspectionConfigVlanEntry 2 }

mgmtArpInspectionConfigVlanAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtArpInspectionConfigVlanEntry 100 }

mgmtArpInspectionConfigVlanTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtArpInspectionConfig 4 }

mgmtArpInspectionConfigVlanTableRowEditorVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VID of the VLAN."
    ::= { mgmtArpInspectionConfigVlanTableRowEditor 1 }

mgmtArpInspectionConfigVlanTableRowEditorLogType OBJECT-TYPE
    SYNTAX      MGMTArpInspectionLogType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The capability to log the inspected entries per VLAN basis. none(0)
         will log nothing. deny(1) will log the denied entries. permit(2) will
         log the permitted entries. all(3) will log all kinds of inspected
         entries."
    ::= { mgmtArpInspectionConfigVlanTableRowEditor 2 }

mgmtArpInspectionConfigVlanTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtArpInspectionConfigVlanTableRowEditor 100 }

mgmtArpInspectionConfigStaticTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTArpInspectionConfigStaticEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing static ARP Inspection configuration"
    ::= { mgmtArpInspectionConfig 5 }

mgmtArpInspectionConfigStaticEntry OBJECT-TYPE
    SYNTAX      MGMTArpInspectionConfigStaticEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtArpInspectionConfigStaticIfIndex,
                  mgmtArpInspectionConfigStaticVlanId,
                  mgmtArpInspectionConfigStaticMacAddress,
                  mgmtArpInspectionConfigStaticIpAddress }
    ::= { mgmtArpInspectionConfigStaticTable 1 }

MGMTArpInspectionConfigStaticEntry ::= SEQUENCE {
    mgmtArpInspectionConfigStaticIfIndex     MGMTInterfaceIndex,
    mgmtArpInspectionConfigStaticVlanId      Integer32,
    mgmtArpInspectionConfigStaticMacAddress  MacAddress,
    mgmtArpInspectionConfigStaticIpAddress   IpAddress,
    mgmtArpInspectionConfigStaticAction      MGMTRowEditorState
}

mgmtArpInspectionConfigStaticIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtArpInspectionConfigStaticEntry 1 }

mgmtArpInspectionConfigStaticVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The VID of the VLAN."
    ::= { mgmtArpInspectionConfigStaticEntry 2 }

mgmtArpInspectionConfigStaticMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned MAC address."
    ::= { mgmtArpInspectionConfigStaticEntry 3 }

mgmtArpInspectionConfigStaticIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv4 address."
    ::= { mgmtArpInspectionConfigStaticEntry 4 }

mgmtArpInspectionConfigStaticAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtArpInspectionConfigStaticEntry 100 }

mgmtArpInspectionConfigStaticTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtArpInspectionConfig 6 }

mgmtArpInspectionConfigStaticTableRowEditorIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtArpInspectionConfigStaticTableRowEditor 1 }

mgmtArpInspectionConfigStaticTableRowEditorVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VID of the VLAN."
    ::= { mgmtArpInspectionConfigStaticTableRowEditor 2 }

mgmtArpInspectionConfigStaticTableRowEditorMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Assigned MAC address."
    ::= { mgmtArpInspectionConfigStaticTableRowEditor 3 }

mgmtArpInspectionConfigStaticTableRowEditorIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Assigned IPv4 address."
    ::= { mgmtArpInspectionConfigStaticTableRowEditor 4 }

mgmtArpInspectionConfigStaticTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtArpInspectionConfigStaticTableRowEditor 100 }

mgmtArpInspectionStatus OBJECT IDENTIFIER
    ::= { mgmtArpInspectionMibObjects 3 }

mgmtArpInspectionStatusDynamicAddressTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTArpInspectionStatusDynamicAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying all ARP Inspection entries"
    ::= { mgmtArpInspectionStatus 1 }

mgmtArpInspectionStatusDynamicAddressEntry OBJECT-TYPE
    SYNTAX      MGMTArpInspectionStatusDynamicAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtArpInspectionStatusDynamicAddressIfIndex,
                  mgmtArpInspectionStatusDynamicAddressVlanId,
                  mgmtArpInspectionStatusDynamicAddressMacAddress,
                  mgmtArpInspectionStatusDynamicAddressIpAddress }
    ::= { mgmtArpInspectionStatusDynamicAddressTable 1 }

MGMTArpInspectionStatusDynamicAddressEntry ::= SEQUENCE {
    mgmtArpInspectionStatusDynamicAddressIfIndex     MGMTInterfaceIndex,
    mgmtArpInspectionStatusDynamicAddressVlanId      Integer32,
    mgmtArpInspectionStatusDynamicAddressMacAddress  MacAddress,
    mgmtArpInspectionStatusDynamicAddressIpAddress   IpAddress,
    mgmtArpInspectionStatusDynamicAddressType        MGMTArpInspectionRegisterStatus
}

mgmtArpInspectionStatusDynamicAddressIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtArpInspectionStatusDynamicAddressEntry 1 }

mgmtArpInspectionStatusDynamicAddressVlanId OBJECT-TYPE
    SYNTAX      Integer32 (1..4095)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The VID of the VLAN."
    ::= { mgmtArpInspectionStatusDynamicAddressEntry 2 }

mgmtArpInspectionStatusDynamicAddressMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned MAC address."
    ::= { mgmtArpInspectionStatusDynamicAddressEntry 3 }

mgmtArpInspectionStatusDynamicAddressIpAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv4 address."
    ::= { mgmtArpInspectionStatusDynamicAddressEntry 4 }

mgmtArpInspectionStatusDynamicAddressType OBJECT-TYPE
    SYNTAX      MGMTArpInspectionRegisterStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Either static(0) or dynamic(1) for the specific ARP entry."
    ::= { mgmtArpInspectionStatusDynamicAddressEntry 5 }

mgmtArpInspectionControl OBJECT IDENTIFIER
    ::= { mgmtArpInspectionMibObjects 4 }

mgmtArpInspectionControlGlobals OBJECT IDENTIFIER
    ::= { mgmtArpInspectionControl 1 }

mgmtArpInspectionControlGlobalsTranslateDynamicToStatic OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "To trigger the control action (only) when TRUE."
    ::= { mgmtArpInspectionControlGlobals 1 }

mgmtArpInspectionMibConformance OBJECT IDENTIFIER
    ::= { mgmtArpInspectionMib 2 }

mgmtArpInspectionMibCompliances OBJECT IDENTIFIER
    ::= { mgmtArpInspectionMibConformance 1 }

mgmtArpInspectionMibGroups OBJECT IDENTIFIER
    ::= { mgmtArpInspectionMibConformance 2 }

mgmtArpInspectionConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtArpInspectionConfigGlobalsAdminState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtArpInspectionMibGroups 1 }

mgmtArpInspectionConfigPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtArpInspectionConfigPortIfIndex,
                  mgmtArpInspectionConfigPortMode,
                  mgmtArpInspectionConfigPortCheckVlan,
                  mgmtArpInspectionConfigPortLogType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtArpInspectionMibGroups 2 }

mgmtArpInspectionConfigVlanTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtArpInspectionConfigVlanVlanId,
                  mgmtArpInspectionConfigVlanLogType,
                  mgmtArpInspectionConfigVlanAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtArpInspectionMibGroups 3 }

mgmtArpInspectionConfigVlanTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtArpInspectionConfigVlanTableRowEditorVlanId,
                  mgmtArpInspectionConfigVlanTableRowEditorLogType,
                  mgmtArpInspectionConfigVlanTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtArpInspectionMibGroups 4 }

mgmtArpInspectionConfigStaticTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtArpInspectionConfigStaticIfIndex,
                  mgmtArpInspectionConfigStaticVlanId,
                  mgmtArpInspectionConfigStaticMacAddress,
                  mgmtArpInspectionConfigStaticIpAddress,
                  mgmtArpInspectionConfigStaticAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtArpInspectionMibGroups 5 }

mgmtArpInspectionConfigStaticTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtArpInspectionConfigStaticTableRowEditorIfIndex,
                  mgmtArpInspectionConfigStaticTableRowEditorVlanId,
                  mgmtArpInspectionConfigStaticTableRowEditorMacAddress,
                  mgmtArpInspectionConfigStaticTableRowEditorIpAddress,
                  mgmtArpInspectionConfigStaticTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtArpInspectionMibGroups 6 }

mgmtArpInspectionStatusDynamicAddressTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtArpInspectionStatusDynamicAddressIfIndex,
                  mgmtArpInspectionStatusDynamicAddressVlanId,
                  mgmtArpInspectionStatusDynamicAddressMacAddress,
                  mgmtArpInspectionStatusDynamicAddressIpAddress,
                  mgmtArpInspectionStatusDynamicAddressType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtArpInspectionMibGroups 7 }

mgmtArpInspectionControlGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtArpInspectionControlGlobalsTranslateDynamicToStatic }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtArpInspectionMibGroups 8 }

mgmtArpInspectionMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtArpInspectionConfigGlobalsInfoGroup,
                       mgmtArpInspectionConfigPortTableInfoGroup,
                       mgmtArpInspectionConfigVlanTableInfoGroup,
                       mgmtArpInspectionConfigVlanTableRowEditorInfoGroup,
                       mgmtArpInspectionConfigStaticTableInfoGroup,
                       mgmtArpInspectionConfigStaticTableRowEditorInfoGroup,
                       mgmtArpInspectionStatusDynamicAddressTableInfoGroup,
                       mgmtArpInspectionControlGlobalsInfoGroup }

    ::= { mgmtArpInspectionMibCompliances 1 }

END
