-- *****************************************************************
-- QOS-MIB:  
-- ****************************************************************

MGMT-QOS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Dscp FROM DIFFSERV-DSCP-TC
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-<PERSON><PERSON>
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTASRType FROM MGMT-TC
    MGMTBitType FROM MGMT-TC
    MGMTDestMacType FROM MGMT-TC
    MGMTEtherType FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPercent FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    MGMTVcapKeyType FROM MGMT-TC
    MGMTVlanTagPriority FROM MGMT-TC
    MGMTVlanTagType FROM MGMT-TC
    ;

mgmtQosMib MODULE-IDENTITY
    LAST-UPDATED "201905290000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private MIB for QoS"
    REVISION    "201905290000Z"
    DESCRIPTION
        "Removed unused PSFP support."
    REVISION    "201904050000Z"
    DESCRIPTION
        "Obsoleted various MPLS-related fields."
    REVISION    "201806270000Z"
    DESCRIPTION
        "More QCE users."
    REVISION    "201711270000Z"
    DESCRIPTION
        "Added MPLS TC capability test. Split AdminBaseTimes into Secs and
         NanoSecs. Updated various descriptions slightly."
    REVISION    "201703200000Z"
    DESCRIPTION
        "Added Frame Preemption support. Added PSFP support."
    REVISION    "201610170000Z"
    DESCRIPTION
        "Updated table dependencies and descriptions. Added new capability
         element for Wred"
    REVISION    "201602250000Z"
    DESCRIPTION
        "Added QosCapabilitiesHasQueueCutThrough object."
    REVISION    "201602110000Z"
    DESCRIPTION
        "Added QosCapabilitiesHasQueueShapersCredit object."
    REVISION    "201601190000Z"
    DESCRIPTION
        "Added (IEEE 802.1Qbv) Time Aware Shaper support."
    REVISION    "201511130000Z"
    DESCRIPTION
        "Updated descriptions."
    REVISION    "201509300000Z"
    DESCRIPTION
        "Added QosConfigInterfaceCosId."
    REVISION    "201508130000Z"
    DESCRIPTION
        "Changed to use Dscp types in QCEs."
    REVISION    "201506230000Z"
    DESCRIPTION
        "Removed QosConfigIngressMapPcpAction, QosConfigIngressMapDscpAction,
         QosConfigEgressMapCosidAction and QosConfigEgressMapDscpAction objects."
    REVISION    "201505270000Z"
    DESCRIPTION
        "Added QosConfigIngressMap, QosConfigEgressMap objects and related
         changes."
    REVISION    "201504070000Z"
    DESCRIPTION
        "Added QosConfigInterfaceQueueShaperRateType,
         QosConfigInterfaceShaperRateType objects."
    REVISION    "201411060000Z"
    DESCRIPTION
        "Added new WredV3 related objects and adapted WredTable to WredV3."
    REVISION    "201410080000Z"
    DESCRIPTION
        "Removed QosCapabilitiesBitrateMin, QosCapabilitiesBitrateMax,
         QosCapabilitiesBurstSizeMin and QosCapabilitiesBurstSizeMax.
         
         Added policer and shaper specific min/max capabilities objects.
         
         Added QosConfigGlobalStormPolicersUnicastFrameRate,
         QosConfigGlobalStormPolicersMulticastFrameRate and
         QosConfigGlobalStormPolicersBroadcastFrameRate."
    REVISION    "201409170000Z"
    DESCRIPTION
        "Added QosCapabilitiesHasQueueShapersExcess object."
    REVISION    "201408120000Z"
    DESCRIPTION
        "Updated descriptions when referring to a capability."
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 14 }


MGMTQosDscpClassify ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates the DSCP classify mode."
    SYNTAX      INTEGER { none(0), zero(1), selected(2), all(3) }

MGMTQosDscpRemark ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates the DSCP remark mode."
    SYNTAX      INTEGER { disabled(0), rewrite(1), remap(2),
                          remapDp(3) }

MGMTQosEgressMapKey ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates the key to use when matching frames in the
         egress map.
         
         If the value is zero, use classified COS ID.
         
         If the value is one, use classified COS ID and DPL.
         
         If the value is two, use classified DSCP.
         
         If the value is three, use classified DSCP and DPL.
         
         The values four and five are obsolete."
    SYNTAX      INTEGER { cosid(0), cosidDpl(1), dscp(2), dscpDpl(3),
                          tcOsolete(4), tcDplObsolete(5) }

MGMTQosIngressMapKey ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates the key to use when matching frames in the
         ingress map.
         
         If the value is zero, use PCP for tagged frames and none for the rest.
         
         If the value is one, use PCP/DEI for tagged frames and none for the
         rest.
         
         If the value is two, use DSCP as key for IP frames and none for the
         rest.
         
         If the value is three, use DSCP as key for IP frames, PCP/DEI for
         tagged frames and none for the rest.
         
         The value four is obsolete."
    SYNTAX      INTEGER { pcp(0), pcpDei(1), dscp(2), dscpPcpDei(3),
                          tcObsolete(4) }

MGMTQosQceFrameType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates the QCE frame type key."
    SYNTAX      INTEGER { any(0), etype(1), llc(2), snap(3), ipv4(4),
                          ipv6(5) }

MGMTQosShaperRateType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates the shaper rate type.
         
         If the value is zero, it indicates the shaper uses line-rate.
         
         If the value is one, it indicates the shaper uses data-rate.
         
         NOTE: For QoS Queue shapers, this configuration is valid only for
         Normal port mode. If the port is in Basic or Hierarchical mode, the
         rate-type will be stored and become active when the port is switched
         back to Normal Scheduling mode."
    SYNTAX      INTEGER { line(0), data(1) }

MGMTQosTagRemarkingMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that indicates the tag remarking mode."
    SYNTAX      INTEGER { classified(0), default(2), mapped(3) }

MGMTQosWredMaxSelector ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "An integer that selects between 'Maximum Drop Probability' or 'Maximum
         Fill Level'."
    SYNTAX      INTEGER { maximumDropProbability(0),
                          maximumFillLevel(1) }

mgmtQosMibObjects OBJECT IDENTIFIER
    ::= { mgmtQosMib 1 }

mgmtQosCapabilities OBJECT IDENTIFIER
    ::= { mgmtQosMibObjects 1 }

mgmtQosCapabilitiesClassMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum allowed QoS class."
    ::= { mgmtQosCapabilities 1 }

mgmtQosCapabilitiesClassMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum allowed QoS class."
    ::= { mgmtQosCapabilities 2 }

mgmtQosCapabilitiesDplMin OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum allowed drop precedence level."
    ::= { mgmtQosCapabilities 3 }

mgmtQosCapabilitiesDplMax OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum allowed drop precedence level."
    ::= { mgmtQosCapabilities 4 }

mgmtQosCapabilitiesWredGroupMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum allowed WRED group number."
    ::= { mgmtQosCapabilities 5 }

mgmtQosCapabilitiesWredGroupMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum allowed WRED group number."
    ::= { mgmtQosCapabilities 6 }

mgmtQosCapabilitiesWredDplMin OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum allowed WRED drop precedence level."
    ::= { mgmtQosCapabilities 7 }

mgmtQosCapabilitiesWredDplMax OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum allowed WRED drop precedence level."
    ::= { mgmtQosCapabilities 8 }

mgmtQosCapabilitiesQceIdMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum allowed QCE Id."
    ::= { mgmtQosCapabilities 9 }

mgmtQosCapabilitiesQceIdMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum allowed QCE Id."
    ::= { mgmtQosCapabilities 10 }

mgmtQosCapabilitiesPortPolicerBitRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported bit rate in kbps for port policers using bit rate
         mode."
    ::= { mgmtQosCapabilities 11 }

mgmtQosCapabilitiesPortPolicerBitRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported bit rate in kbps for port policers using bit rate
         mode.
         
         If zero, the port policers does not support bit rate mode."
    ::= { mgmtQosCapabilities 12 }

mgmtQosCapabilitiesPortPolicerBitBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in bytes for port policers using bit rate
         mode."
    ::= { mgmtQosCapabilities 13 }

mgmtQosCapabilitiesPortPolicerBitBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in bytes for port policers using bit rate
         mode."
    ::= { mgmtQosCapabilities 14 }

mgmtQosCapabilitiesPortPolicerFrameRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported frame rate in fps for port policers using frame rate
         mode."
    ::= { mgmtQosCapabilities 15 }

mgmtQosCapabilitiesPortPolicerFrameRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported frame rate in fps for port policers using frame rate
         mode.
         
         If zero, the port policers does not support frame rate mode."
    ::= { mgmtQosCapabilities 16 }

mgmtQosCapabilitiesPortPolicerFrameBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in frames for port policers using frame
         rate mode."
    ::= { mgmtQosCapabilities 17 }

mgmtQosCapabilitiesPortPolicerFrameBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in frames for port policers using frame
         rate mode."
    ::= { mgmtQosCapabilities 18 }

mgmtQosCapabilitiesQueuePolicerBitRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported bit rate in kbps for queue policers using bit rate
         mode."
    ::= { mgmtQosCapabilities 19 }

mgmtQosCapabilitiesQueuePolicerBitRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported bit rate in kbps for queue policers using bit rate
         mode.
         
         If zero, the queue policers does not support bit rate mode."
    ::= { mgmtQosCapabilities 20 }

mgmtQosCapabilitiesQueuePolicerBitBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in bytes for queue policers using bit rate
         mode."
    ::= { mgmtQosCapabilities 21 }

mgmtQosCapabilitiesQueuePolicerBitBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in bytes for queue policers using bit rate
         mode."
    ::= { mgmtQosCapabilities 22 }

mgmtQosCapabilitiesQueuePolicerFrameRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported frame rate in fps for queue policers using frame rate
         mode."
    ::= { mgmtQosCapabilities 23 }

mgmtQosCapabilitiesQueuePolicerFrameRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported frame rate in fps for queue policers using frame rate
         mode.
         
         If zero, the port policers does not support frame rate mode."
    ::= { mgmtQosCapabilities 24 }

mgmtQosCapabilitiesQueuePolicerFrameBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in frames for queue policers using frame
         rate mode."
    ::= { mgmtQosCapabilities 25 }

mgmtQosCapabilitiesQueuePolicerFrameBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in frames for queue policers using frame
         rate mode."
    ::= { mgmtQosCapabilities 26 }

mgmtQosCapabilitiesPortShaperBitRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported bit rate in kbps for port shapers using bit rate
         mode."
    ::= { mgmtQosCapabilities 27 }

mgmtQosCapabilitiesPortShaperBitRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported bit rate in kbps for port shapers using bit rate
         mode.
         
         If zero, the port shapers does not support bit rate mode."
    ::= { mgmtQosCapabilities 28 }

mgmtQosCapabilitiesPortShaperBitBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in bytes for port shapers using bit rate
         mode."
    ::= { mgmtQosCapabilities 29 }

mgmtQosCapabilitiesPortShaperBitBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in bytes for port shapers using bit rate
         mode."
    ::= { mgmtQosCapabilities 30 }

mgmtQosCapabilitiesPortShaperFrameRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported frame rate in fps for port shapers using frame rate
         mode."
    ::= { mgmtQosCapabilities 31 }

mgmtQosCapabilitiesPortShaperFrameRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported frame rate in fps for port shapers using frame rate
         mode.
         
         If zero, the port shapers does not support frame rate mode."
    ::= { mgmtQosCapabilities 32 }

mgmtQosCapabilitiesPortShaperFrameBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in frames for port shapers using frame
         rate mode."
    ::= { mgmtQosCapabilities 33 }

mgmtQosCapabilitiesPortShaperFrameBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in frames for port shapers using frame
         rate mode."
    ::= { mgmtQosCapabilities 34 }

mgmtQosCapabilitiesQueueShaperBitRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported bit rate in kbps for queue shapers using bit rate
         mode."
    ::= { mgmtQosCapabilities 35 }

mgmtQosCapabilitiesQueueShaperBitRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported bit rate in kbps for queue shapers using bit rate
         mode.
         
         If zero, the queue shapers does not support bit rate mode."
    ::= { mgmtQosCapabilities 36 }

mgmtQosCapabilitiesQueueShaperBitBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in bytes for queue shapers using bit rate
         mode."
    ::= { mgmtQosCapabilities 37 }

mgmtQosCapabilitiesQueueShaperBitBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in bytes for queue shapers using bit rate
         mode."
    ::= { mgmtQosCapabilities 38 }

mgmtQosCapabilitiesQueueShaperFrameRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported frame rate in fps for queue shapers using frame rate
         mode."
    ::= { mgmtQosCapabilities 39 }

mgmtQosCapabilitiesQueueShaperFrameRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported frame rate in fps for queue shapers using frame rate
         mode.
         
         If zero, the port shapers does not support frame rate mode."
    ::= { mgmtQosCapabilities 40 }

mgmtQosCapabilitiesQueueShaperFrameBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in frames for queue shapers using frame
         rate mode."
    ::= { mgmtQosCapabilities 41 }

mgmtQosCapabilitiesQueueShaperFrameBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in frames for queue shapers using frame
         rate mode."
    ::= { mgmtQosCapabilities 42 }

mgmtQosCapabilitiesGlobalStormBitRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported bit rate in kbps for global storm policers using bit
         rate mode."
    ::= { mgmtQosCapabilities 43 }

mgmtQosCapabilitiesGlobalStormBitRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported bit rate in kbps for global storm policers using bit
         rate mode.
         
         If zero, the global storm policers does not support bit rate mode."
    ::= { mgmtQosCapabilities 44 }

mgmtQosCapabilitiesGlobalStormBitBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in bytes for global storm policers using
         bit rate mode."
    ::= { mgmtQosCapabilities 45 }

mgmtQosCapabilitiesGlobalStormBitBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in bytes for global storm policers using
         bit rate mode."
    ::= { mgmtQosCapabilities 46 }

mgmtQosCapabilitiesGlobalStormFrameRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported frame rate in fps for global storm policers using
         frame rate mode."
    ::= { mgmtQosCapabilities 47 }

mgmtQosCapabilitiesGlobalStormFrameRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported frame rate in fps for global storm policers using
         frame rate mode.
         
         If zero, the global storm policers does not support frame rate mode."
    ::= { mgmtQosCapabilities 48 }

mgmtQosCapabilitiesGlobalStormFrameBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in frames for global storm policers using
         frame rate mode."
    ::= { mgmtQosCapabilities 49 }

mgmtQosCapabilitiesGlobalStormFrameBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in frames for global storm policers using
         frame rate mode."
    ::= { mgmtQosCapabilities 50 }

mgmtQosCapabilitiesPortStormBitRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported bit rate in kbps for port storm policers using bit
         rate mode."
    ::= { mgmtQosCapabilities 51 }

mgmtQosCapabilitiesPortStormBitRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported bit rate in kbps for port storm policers using bit
         rate mode.
         
         If zero, the port storm policers does not support bit rate mode."
    ::= { mgmtQosCapabilities 52 }

mgmtQosCapabilitiesPortStormBitBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in bytes for port storm policers using bit
         rate mode."
    ::= { mgmtQosCapabilities 53 }

mgmtQosCapabilitiesPortStormBitBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in bytes for port storm policers using bit
         rate mode."
    ::= { mgmtQosCapabilities 54 }

mgmtQosCapabilitiesPortStormFrameRateMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported frame rate in fps for port storm policers using frame
         rate mode."
    ::= { mgmtQosCapabilities 55 }

mgmtQosCapabilitiesPortStormFrameRateMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported frame rate in fps for port storm policers using frame
         rate mode.
         
         If zero, the port storm policers does not support frame rate mode."
    ::= { mgmtQosCapabilities 56 }

mgmtQosCapabilitiesPortStormFrameBurstMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported burst size in frames for port storm policers using
         frame rate mode."
    ::= { mgmtQosCapabilities 57 }

mgmtQosCapabilitiesPortStormFrameBurstMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported burst size in frames for port storm policers using
         frame rate mode."
    ::= { mgmtQosCapabilities 58 }

mgmtQosCapabilitiesIngressMapIdMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported index of Ingress map."
    ::= { mgmtQosCapabilities 59 }

mgmtQosCapabilitiesIngressMapIdMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported index of Ingress map."
    ::= { mgmtQosCapabilities 60 }

mgmtQosCapabilitiesEgressMapIdMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum supported index of Egress map."
    ::= { mgmtQosCapabilities 61 }

mgmtQosCapabilitiesEgressMapIdMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported index of Egress map."
    ::= { mgmtQosCapabilities 62 }

mgmtQosCapabilitiesCosIdMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum allowed COSID value."
    ::= { mgmtQosCapabilities 63 }

mgmtQosCapabilitiesCosIdMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum allowed COSID value."
    ::= { mgmtQosCapabilities 64 }

mgmtQosCapabilitiesQbvGclLenMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum supported length of gate control list."
    ::= { mgmtQosCapabilities 65 }

mgmtQosCapabilitiesDwrrCountMask OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A bitmask used to show allowed values of DwrrCount (zero is always
         allowed).
         
         Example:
         
         00100000 (0x20): Allowed values are 0 and 6
         
         11111110 (0xFE): Allowed values are 0 and 2..8"
    ::= { mgmtQosCapabilities 101 }

mgmtQosCapabilitiesHasGlobalStormPolicers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports global storm policers."
    ::= { mgmtQosCapabilities 201 }

mgmtQosCapabilitiesHasPortStormPolicers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports per port storm policers."
    ::= { mgmtQosCapabilities 202 }

mgmtQosCapabilitiesHasPortQueuePolicers OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports per port queue policers."
    ::= { mgmtQosCapabilities 203 }

mgmtQosCapabilitiesHasWredV1 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports WRED version 1."
    ::= { mgmtQosCapabilities 204 }

mgmtQosCapabilitiesHasWredV2 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports WRED version 2."
    ::= { mgmtQosCapabilities 205 }

mgmtQosCapabilitiesHasFixedTagCosMap OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports fixed tag to cos mapping only."
    ::= { mgmtQosCapabilities 206 }

mgmtQosCapabilitiesHasTagClassification OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports using VLAN tag for classification."
    ::= { mgmtQosCapabilities 207 }

mgmtQosCapabilitiesHasTagRemarking OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports VLAN tag remarking."
    ::= { mgmtQosCapabilities 208 }

mgmtQosCapabilitiesHasDscp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports DSCP translation and remarking."
    ::= { mgmtQosCapabilities 209 }

mgmtQosCapabilitiesHasDscpDplClassification OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports DPL based DSCP classification."
    ::= { mgmtQosCapabilities 210 }

mgmtQosCapabilitiesHasDscpDplRemarking OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports DPL based DSCP remarking."
    ::= { mgmtQosCapabilities 211 }

mgmtQosCapabilitiesHasPortPolicersFc OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports flow control in port policers."
    ::= { mgmtQosCapabilities 212 }

mgmtQosCapabilitiesHasQueuePolicersFc OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports flow control in queue policers."
    ::= { mgmtQosCapabilities 213 }

mgmtQosCapabilitiesHasPortShapersDlb OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports dual leaky bucket egress port shapers."
    ::= { mgmtQosCapabilities 214 }

mgmtQosCapabilitiesHasQueueShapersDlb OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports dual leaky bucket egress queue shapers."
    ::= { mgmtQosCapabilities 215 }

mgmtQosCapabilitiesHasQueueShapersExcess OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports excess bandwidth in egress queue
         shapers."
    ::= { mgmtQosCapabilities 216 }

mgmtQosCapabilitiesHasWredV3 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports WRED version 3."
    ::= { mgmtQosCapabilities 217 }

mgmtQosCapabilitiesHasQueueShapersCredit OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports bursty traffic in egress queue shapers."
    ::= { mgmtQosCapabilities 218 }

mgmtQosCapabilitiesHasQueueCutThrough OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports cut through in egress queue."
    ::= { mgmtQosCapabilities 219 }

mgmtQosCapabilitiesHasQueueFramePreemption OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports frame preemption in egress queue."
    ::= { mgmtQosCapabilities 220 }

mgmtQosCapabilitiesHasQce OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCEs."
    ::= { mgmtQosCapabilities 301 }

mgmtQosCapabilitiesHasQceAddressMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE address mode."
    ::= { mgmtQosCapabilities 302 }

mgmtQosCapabilitiesHasQceKeyType OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE key type."
    ::= { mgmtQosCapabilities 303 }

mgmtQosCapabilitiesHasQceMacOui OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE MAC OUI part only (24 most
         significant bits)."
    ::= { mgmtQosCapabilities 304 }

mgmtQosCapabilitiesHasQceDmac OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE destination MAC keys."
    ::= { mgmtQosCapabilities 305 }

mgmtQosCapabilitiesHasQceDip OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE destination IP keys."
    ::= { mgmtQosCapabilities 306 }

mgmtQosCapabilitiesHasQceCTag OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE VLAN C-TAG keys."
    ::= { mgmtQosCapabilities 307 }

mgmtQosCapabilitiesHasQceSTag OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE VLAN S-TAG keys."
    ::= { mgmtQosCapabilities 308 }

mgmtQosCapabilitiesHasQceInnerTag OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE inner VLAN tag keys."
    ::= { mgmtQosCapabilities 309 }

mgmtQosCapabilitiesHasQceActionPcpDei OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE PCP and DEI actions."
    ::= { mgmtQosCapabilities 310 }

mgmtQosCapabilitiesHasQceActionPolicy OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE policy actions."
    ::= { mgmtQosCapabilities 311 }

mgmtQosCapabilitiesHasShapersRt OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports rate-type configurable egress shapers."
    ::= { mgmtQosCapabilities 312 }

mgmtQosCapabilitiesHasQceActionMap OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports QCE ingress map actions."
    ::= { mgmtQosCapabilities 313 }

mgmtQosCapabilitiesHasIngressMap OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports Ingress mapping tables"
    ::= { mgmtQosCapabilities 314 }

mgmtQosCapabilitiesHasEgressMap OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports Egress mapping tables"
    ::= { mgmtQosCapabilities 315 }

mgmtQosCapabilitiesHasWred2orWredw3 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object is only valid if qosCapabilitiesHasWredV2 or
         qosCapabilitiesHasWredV3 is true."
    ::= { mgmtQosCapabilities 316 }

mgmtQosCapabilitiesHasDscpDp2 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object is only valid if qosCapabilitiesHasDscpDplClassification is
         true and qosCapabilitiesDplMax is greater than 1."
    ::= { mgmtQosCapabilities 317 }

mgmtQosCapabilitiesHasDscpDp3 OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object is only valid if qosCapabilitiesHasDscpDplClassification is
         true and qosCapabilitiesDplMax is greater than 2."
    ::= { mgmtQosCapabilities 318 }

mgmtQosCapabilitiesHasDefaultPcpAndDei OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object is only valid if qosCapabilitiesHasDefaultPcpAndDei is
         true."
    ::= { mgmtQosCapabilities 319 }

mgmtQosCapabilitiesHasTrustTag OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object is only valid if qosCapabilitiesHasFixedTagCosMap is false
         and qosCapabilitiesHasTagClassification is true."
    ::= { mgmtQosCapabilities 320 }

mgmtQosCapabilitiesHasCosIdClassification OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports COSID classification."
    ::= { mgmtQosCapabilities 321 }

mgmtQosCapabilitiesHasQbv OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports Time Aware Shaper."
    ::= { mgmtQosCapabilities 322 }

mgmtQosCapabilitiesHasWred OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This object is only valid if the platform supports any form of Wred."
    ::= { mgmtQosCapabilities 323 }

mgmtQosCapabilitiesHasPsfp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports Per stream Filtering and Policing."
    ::= { mgmtQosCapabilities 324 }

mgmtQosCapabilitiesHasTc OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "If true, the platform supports MPLS TC in ingress and egress maps
         (obsolete. Always false)."
    ::= { mgmtQosCapabilities 325 }

mgmtQosConfig OBJECT IDENTIFIER
    ::= { mgmtQosMibObjects 2 }

mgmtQosConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtQosConfig 1 }

mgmtQosConfigGlobalsStormPolicers OBJECT IDENTIFIER
    ::= { mgmtQosConfigGlobals 1 }

mgmtQosConfigGlobalsStormPolicersUnicast OBJECT IDENTIFIER
    ::= { mgmtQosConfigGlobalsStormPolicers 1 }

mgmtQosConfigGlobalsStormPolicersUnicastEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the storm policer is enabled.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasGlobalStormPolicers' is True."
    ::= { mgmtQosConfigGlobalsStormPolicersUnicast 1 }

mgmtQosConfigGlobalsStormPolicersUnicastRate OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configure the storm policer rate.
         
         Valid range is from the smallest value of
         qosCapabilitiesGlobalStormBitRateMin and
         qosCapabilitiesGlobalStormFrameRateMin to the largest value of
         qosCapabilitiesGlobalStormBitRateMax and
         qosCapabilitiesGlobalStormFrameRateMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasGlobalStormPolicers' is True."
    ::= { mgmtQosConfigGlobalsStormPolicersUnicast 2 }

mgmtQosConfigGlobalsStormPolicersUnicastFrameRate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the rate is measured in fps instead of kbps.
         
         fps (FrameRate == TRUE) is only valid if
         qosCapabilitiesGlobalStormFrameRateMax is non-zero.
         
         kbps (FrameRate == FALSE) is only valid if
         qosCapabilitiesGlobalStormBitRateMax is non-zero.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasGlobalStormPolicers' is True."
    ::= { mgmtQosConfigGlobalsStormPolicersUnicast 3 }

mgmtQosConfigGlobalsStormPolicersMulticast OBJECT IDENTIFIER
    ::= { mgmtQosConfigGlobalsStormPolicers 2 }

mgmtQosConfigGlobalsStormPolicersMulticastEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the storm policer is enabled.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasGlobalStormPolicers' is True."
    ::= { mgmtQosConfigGlobalsStormPolicersMulticast 1 }

mgmtQosConfigGlobalsStormPolicersMulticastRate OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configure the storm policer rate.
         
         Valid range is from the smallest value of
         qosCapabilitiesGlobalStormBitRateMin and
         qosCapabilitiesGlobalStormFrameRateMin to the largest value of
         qosCapabilitiesGlobalStormBitRateMax and
         qosCapabilitiesGlobalStormFrameRateMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasGlobalStormPolicers' is True."
    ::= { mgmtQosConfigGlobalsStormPolicersMulticast 2 }

mgmtQosConfigGlobalsStormPolicersMulticastFrameRate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the rate is measured in fps instead of kbps.
         
         fps (FrameRate == TRUE) is only valid if
         qosCapabilitiesGlobalStormFrameRateMax is non-zero.
         
         kbps (FrameRate == FALSE) is only valid if
         qosCapabilitiesGlobalStormBitRateMax is non-zero.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasGlobalStormPolicers' is True."
    ::= { mgmtQosConfigGlobalsStormPolicersMulticast 3 }

mgmtQosConfigGlobalsStormPolicersBroadcast OBJECT IDENTIFIER
    ::= { mgmtQosConfigGlobalsStormPolicers 3 }

mgmtQosConfigGlobalsStormPolicersBroadcastEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the storm policer is enabled.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasGlobalStormPolicers' is True."
    ::= { mgmtQosConfigGlobalsStormPolicersBroadcast 1 }

mgmtQosConfigGlobalsStormPolicersBroadcastRate OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configure the storm policer rate.
         
         Valid range is from the smallest value of
         qosCapabilitiesGlobalStormBitRateMin and
         qosCapabilitiesGlobalStormFrameRateMin to the largest value of
         qosCapabilitiesGlobalStormBitRateMax and
         qosCapabilitiesGlobalStormFrameRateMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasGlobalStormPolicers' is True."
    ::= { mgmtQosConfigGlobalsStormPolicersBroadcast 2 }

mgmtQosConfigGlobalsStormPolicersBroadcastFrameRate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the rate is measured in fps instead of kbps.
         
         fps (FrameRate == TRUE) is only valid if
         qosCapabilitiesGlobalStormFrameRateMax is non-zero.
         
         kbps (FrameRate == FALSE) is only valid if
         qosCapabilitiesGlobalStormBitRateMax is non-zero.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasGlobalStormPolicers' is True."
    ::= { mgmtQosConfigGlobalsStormPolicersBroadcast 3 }

mgmtQosConfigGlobalsWredTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigGlobalsWredEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the configuration of WRED (Weighted Random Early
         Discard).
         
         This is an optional table and is only present if qosCapabilitiesHasWred
         is true."
    ::= { mgmtQosConfigGlobals 2 }

mgmtQosConfigGlobalsWredEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigGlobalsWredEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific WRED profile"
    INDEX       { mgmtQosConfigGlobalsWredGroup,
                  mgmtQosConfigGlobalsWredQueue,
                  mgmtQosConfigGlobalsWredDpl }
    ::= { mgmtQosConfigGlobalsWredTable 1 }

MGMTQosConfigGlobalsWredEntry ::= SEQUENCE {
    mgmtQosConfigGlobalsWredGroup             Integer32,
    mgmtQosConfigGlobalsWredQueue             Integer32,
    mgmtQosConfigGlobalsWredDpl               Integer32,
    mgmtQosConfigGlobalsWredEnable            TruthValue,
    mgmtQosConfigGlobalsWredMinimumFillLevel  MGMTPercent,
    mgmtQosConfigGlobalsWredMaximum           MGMTPercent,
    mgmtQosConfigGlobalsWredMaxSelector       MGMTQosWredMaxSelector,
    mgmtQosConfigGlobalsWredMaximumDp1        MGMTPercent,
    mgmtQosConfigGlobalsWredMaximumDp2        MGMTPercent,
    mgmtQosConfigGlobalsWredMaximumDp3        MGMTPercent
}

mgmtQosConfigGlobalsWredGroup OBJECT-TYPE
    SYNTAX      Integer32 (1..3)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "WRED group index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWred' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 1 }

mgmtQosConfigGlobalsWredQueue OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Queue index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWred' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 2 }

mgmtQosConfigGlobalsWredDpl OBJECT-TYPE
    SYNTAX      Integer32 (0..3)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DPL index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWred' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 3 }

mgmtQosConfigGlobalsWredEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, RED is enabled for this queue.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWred' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 4 }

mgmtQosConfigGlobalsWredMinimumFillLevel OBJECT-TYPE
    SYNTAX      MGMTPercent
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls the lower RED fill level threshold in percent of the whole
         queue.
         
         If the queue filling level is below this threshold, the drop
         probability is zero.
         
         Valid range is 0-100.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWred' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 5 }

mgmtQosConfigGlobalsWredMaximum OBJECT-TYPE
    SYNTAX      MGMTPercent
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls the upper RED drop probability or fill level threshold for
         frames marked with Drop Precedence Level 1 (yellow frames).
         
         Valid range is 1-100.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWred2orWredw3' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 6 }

mgmtQosConfigGlobalsWredMaxSelector OBJECT-TYPE
    SYNTAX      MGMTQosWredMaxSelector
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Selects whether 'Maximum' controls 'Maximum Drop Probability' or
         'Maximum Fill Level'.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWred2orWredw3' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 7 }

mgmtQosConfigGlobalsWredMaximumDp1 OBJECT-TYPE
    SYNTAX      MGMTPercent
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls the upper RED drop probability for frames marked with Drop
         Precedence Level 1 (yellow frames).
         
         Valid range is 0-100.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWredV1' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 8 }

mgmtQosConfigGlobalsWredMaximumDp2 OBJECT-TYPE
    SYNTAX      MGMTPercent
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls the upper RED drop probability for frames marked with Drop
         Precedence Level 2 (yellow frames).
         
         Valid range is 0-100.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWredV1' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 9 }

mgmtQosConfigGlobalsWredMaximumDp3 OBJECT-TYPE
    SYNTAX      MGMTPercent
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Controls the upper RED drop probability for frames marked with Drop
         Precedence Level 3 (yellow frames).
         
         Valid range is 0-100.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWredV1' is True."
    ::= { mgmtQosConfigGlobalsWredEntry 10 }

mgmtQosConfigGlobalsDscpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigGlobalsDscpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table has 64 entries, one for each DSCP value.
         
         This is an optional table and is only present if qosCapabilitiesHasDscp
         is true."
    ::= { mgmtQosConfigGlobals 3 }

mgmtQosConfigGlobalsDscpEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigGlobalsDscpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific DSCP"
    INDEX       { mgmtQosConfigGlobalsDscpDscp }
    ::= { mgmtQosConfigGlobalsDscpTable 1 }

MGMTQosConfigGlobalsDscpEntry ::= SEQUENCE {
    mgmtQosConfigGlobalsDscpDscp                  Dscp,
    mgmtQosConfigGlobalsDscpTrust                 TruthValue,
    mgmtQosConfigGlobalsDscpCos                   Unsigned32,
    mgmtQosConfigGlobalsDscpDpl                   MGMTUnsigned8,
    mgmtQosConfigGlobalsDscpIngressTranslation    Dscp,
    mgmtQosConfigGlobalsDscpClassify              TruthValue,
    mgmtQosConfigGlobalsDscpEgressTranslation     Dscp,
    mgmtQosConfigGlobalsDscpEgressTranslationDp1  Dscp
}

mgmtQosConfigGlobalsDscpDscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DSCP index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigGlobalsDscpEntry 1 }

mgmtQosConfigGlobalsDscpTrust OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, this DSCP value is trusted. Packets arriving on interfaces
         where 'TrustDscp' is enabled will be classified to the corresponding
         CoS and DPL in this table.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigGlobalsDscpEntry 2 }

mgmtQosConfigGlobalsDscpCos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The CoS value which the DSCP value maps to.
         
         Valid range is qosCapabilitiesClassMin-qosCapabilitiesClassMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigGlobalsDscpEntry 3 }

mgmtQosConfigGlobalsDscpDpl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DPL (Drop Precedence Level) which the DSCP value maps to.
         
         Valid range is qosCapabilitiesDplMin-qosCapabilitiesDplMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigGlobalsDscpEntry 4 }

mgmtQosConfigGlobalsDscpIngressTranslation OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The new classified DSCP value which the original DSCP value is replaced
         with if 'DscpTranslate' is enabled on the interface
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigGlobalsDscpEntry 5 }

mgmtQosConfigGlobalsDscpClassify OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, then packets received on interfaces where 'DscpClassify' is
         set to 'selected' will be classified to a new DSCP value based on the
         values configured in the qosConfigGlobalsCosToDscpTable.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigGlobalsDscpEntry 6 }

mgmtQosConfigGlobalsDscpEgressTranslation OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The new DSCP value that will be written into the packet at egress if
         'DscpRemark' on the interface is set to 'remap' or 'remapDp'.
         
         If 'remapDp' is set then only packets classified to Dpl 0 will use this
         table.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigGlobalsDscpEntry 7 }

mgmtQosConfigGlobalsDscpEgressTranslationDp1 OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The new DSCP value that will be written into the packet at egress if
         'DscpRemark' on the interface is set to 'remapDp'.
         
         Only packets classified to Dpl 1 will use this table.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscpDplRemarking' is True."
    ::= { mgmtQosConfigGlobalsDscpEntry 8 }

mgmtQosConfigGlobalsCosToDscpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigGlobalsCosToDscpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table has 8 entries, one for each Cos value.
         
         This is an optional table and is only present if qosCapabilitiesHasDscp
         is true."
    ::= { mgmtQosConfigGlobals 4 }

mgmtQosConfigGlobalsCosToDscpEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigGlobalsCosToDscpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific Cos"
    INDEX       { mgmtQosConfigGlobalsCosToDscpCos }
    ::= { mgmtQosConfigGlobalsCosToDscpTable 1 }

MGMTQosConfigGlobalsCosToDscpEntry ::= SEQUENCE {
    mgmtQosConfigGlobalsCosToDscpCos      Integer32,
    mgmtQosConfigGlobalsCosToDscpDscp     Dscp,
    mgmtQosConfigGlobalsCosToDscpDscpDp1  Dscp,
    mgmtQosConfigGlobalsCosToDscpDscpDp2  Dscp,
    mgmtQosConfigGlobalsCosToDscpDscpDp3  Dscp
}

mgmtQosConfigGlobalsCosToDscpCos OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "CoS index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigGlobalsCosToDscpEntry 1 }

mgmtQosConfigGlobalsCosToDscpDscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If qosCapabilitiesHasDscpDplClassification is true: This is the DSCP
         value which the classified CoS value maps to when the classified DPL is
         0.
         
         If qosCapabilitiesHasDscpDplClassification is false: This is the DSCP
         value which the classified CoS value maps to.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigGlobalsCosToDscpEntry 2 }

mgmtQosConfigGlobalsCosToDscpDscpDp1 OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value which the classified CoS value maps to when the
         classified DPL is 1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscpDplClassification' is True."
    ::= { mgmtQosConfigGlobalsCosToDscpEntry 3 }

mgmtQosConfigGlobalsCosToDscpDscpDp2 OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value which the classified CoS value maps to when the
         classified DPL is 2.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscpDp2' is True."
    ::= { mgmtQosConfigGlobalsCosToDscpEntry 4 }

mgmtQosConfigGlobalsCosToDscpDscpDp3 OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value which the classified CoS value maps to when the
         classified DPL is 3.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscpDp3' is True."
    ::= { mgmtQosConfigGlobalsCosToDscpEntry 5 }

mgmtQosConfigInterface OBJECT IDENTIFIER
    ::= { mgmtQosConfig 2 }

mgmtQosConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides QoS configuration for QoS manageable interfaces"
    ::= { mgmtQosConfigInterface 1 }

mgmtQosConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific interface"
    INDEX       { mgmtQosConfigInterfaceIfIndex }
    ::= { mgmtQosConfigInterfaceTable 1 }

MGMTQosConfigInterfaceEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceIfIndex           MGMTInterfaceIndex,
    mgmtQosConfigInterfaceCos               Unsigned32,
    mgmtQosConfigInterfaceDpl               MGMTUnsigned8,
    mgmtQosConfigInterfacePcp               Unsigned32,
    mgmtQosConfigInterfaceDei               MGMTUnsigned8,
    mgmtQosConfigInterfaceTrustTag          TruthValue,
    mgmtQosConfigInterfaceTrustDscp         TruthValue,
    mgmtQosConfigInterfaceDwrrCount         MGMTUnsigned8,
    mgmtQosConfigInterfaceTagRemarkingMode  MGMTQosTagRemarkingMode,
    mgmtQosConfigInterfaceTagPcp            Unsigned32,
    mgmtQosConfigInterfaceTagDei            MGMTUnsigned8,
    mgmtQosConfigInterfaceDscpTranslate     TruthValue,
    mgmtQosConfigInterfaceDscpClassify      MGMTQosDscpClassify,
    mgmtQosConfigInterfaceDscpRemark        MGMTQosDscpRemark,
    mgmtQosConfigInterfaceQceAddressMode    TruthValue,
    mgmtQosConfigInterfaceQceKeyType        MGMTVcapKeyType,
    mgmtQosConfigInterfaceWredGroup         Unsigned32,
    mgmtQosConfigInterfaceIngressMap        MGMTUnsigned16,
    mgmtQosConfigInterfaceEgressMap         MGMTUnsigned16,
    mgmtQosConfigInterfaceCosId             MGMTUnsigned8
}

mgmtQosConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtQosConfigInterfaceEntry 1 }

mgmtQosConfigInterfaceCos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The default CoS value a packet is classified to.
         
         Valid range is qosCapabilitiesClassMin-qosCapabilitiesClassMax."
    ::= { mgmtQosConfigInterfaceEntry 2 }

mgmtQosConfigInterfaceDpl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The default DPL (Drop Precedence Level) a packet is classified to.
         
         Valid range is qosCapabilitiesDplMin-qosCapabilitiesDplMax."
    ::= { mgmtQosConfigInterfaceEntry 3 }

mgmtQosConfigInterfacePcp OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The default PCP value a packet is classified to.
         
         Valid range is 0-7.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDefaultPcpAndDei' is True."
    ::= { mgmtQosConfigInterfaceEntry 4 }

mgmtQosConfigInterfaceDei OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The default DEI value a packet is classified to.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDefaultPcpAndDei' is True."
    ::= { mgmtQosConfigInterfaceEntry 5 }

mgmtQosConfigInterfaceTrustTag OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true and the packet is VLAN tagged, then the CoS and DPL assigned to
         the packet is the PCP and DEI value in the packet mapped to a CoS and
         DPL value defined in the qosConfigInterfaceTagToCosTable.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTrustTag' is True."
    ::= { mgmtQosConfigInterfaceEntry 6 }

mgmtQosConfigInterfaceTrustDscp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true and the packet type is IPv4 or IPv6 and the packet DSCP value
         is trusted in qosConfigGlobalsDscpTable, then the CoS and DPL assigned
         to the packet is the DSCP value in the packet mapped to a CoS and DPL
         value defined in the qosConfigGlobalsDscpTable.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigInterfaceEntry 7 }

mgmtQosConfigInterfaceDwrrCount OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The number of queues that are running Deficit Weighted Round Robin
         scheduling.
         
         This value is restricted to 0 or one of the values defined by
         qosCapabilitiesDwrrCountMask."
    ::= { mgmtQosConfigInterfaceEntry 8 }

mgmtQosConfigInterfaceTagRemarkingMode OBJECT-TYPE
    SYNTAX      MGMTQosTagRemarkingMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Selects the tag remarking mode for egress. Valid values are:
         
         'classified' : Remark the tag with the classified values of PCP and
         DEI.
         
         'default' : Remark the tag with the configured TagPcp and TagDei.
         
         'mapped' : Remark the tag with the values from the classified CoS and
         DPL mapped through the qosConfigInterfaceCosToTagTable.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTagRemarking' is True."
    ::= { mgmtQosConfigInterfaceEntry 9 }

mgmtQosConfigInterfaceTagPcp OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value to put in the packet if TagRemarkingMode is set to
         'default'.
         
         Valid range is 0-7.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTagRemarking' is True."
    ::= { mgmtQosConfigInterfaceEntry 10 }

mgmtQosConfigInterfaceTagDei OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DEI value to put in the packet if TagRemarkingMode is set to
         'default'.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTagRemarking' is True."
    ::= { mgmtQosConfigInterfaceEntry 11 }

mgmtQosConfigInterfaceDscpTranslate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true then the classified DSCP value is the DSCP value in the packed
         mapped to a new DSCP value defined in
         qosConfigGlobalsDscpTable.IngressTranslation.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigInterfaceEntry 12 }

mgmtQosConfigInterfaceDscpClassify OBJECT-TYPE
    SYNTAX      MGMTQosDscpClassify
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If qosCapabilitiesHasDscpDplClassification is true: Selects if and how
         the classified DSCP is based on the classified CoS and DPL for IPv4 and
         IPv6 packets on ingress.
         
         If a packet is selected for being classified to a new DSCP, the
         classification in based on the classified CoS mapped to a new DSCP
         value through the qosConfigGlobalsCosToDscpTable. If the classified DPL
         is 1, the new DSCP value is taken from the DscpDp1 entry, otherwise it
         is taken from the Dscp entry. Valid values are:
         
         'none' : Always classify to the DSCP value in the received packet and
         ignore the classified CoS and DPL.
         
         'zero' : If the DSCP value in the received packet is 0, then classify
         to a new DSCP value based on the classified CoS and DPL.
         
         'selected' : If the DSCP value in the received packet is enabled in
         qosConfigGlobalsDscpTable.Classify, then classify to a new DSCP value
         based on the classified CoS and DPL.
         
         'all' : Always classify to a new DSCP value based on the classified CoS
         and DPL.
         
         
         
         If qosCapabilitiesHasDscpDplClassification is false: Selects if and how
         the classified DSCP is based on the classified CoS for IPv4 and IPv6
         packets on ingress.
         
         If a packet is selected for being classified to a new DSCP, the
         classification in based on the classified CoS mapped to a new DSCP
         value through the qosConfigGlobalsCosToDscpTable.Dscp Valid values are:
         
         'none' : Always classify to the DSCP value in the received packet and
         ignore the classified CoS.
         
         'zero' : If the DSCP value in the received packet is 0, then classify
         to a new DSCP value based on the classified CoS.
         
         'selected' : If the DSCP value in the received packet is enabled in
         qosConfigGlobalsDscpTable.Classify, then classify to a new DSCP value
         based on the classified CoS.
         
         'all' : Always classify to a new DSCP value based on the classified
         CoS.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigInterfaceEntry 13 }

mgmtQosConfigInterfaceDscpRemark OBJECT-TYPE
    SYNTAX      MGMTQosDscpRemark
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If qosCapabilitiesHasDscpDplRemarking is true: Selects if and how the
         classified DSCP is written into the packet at egress. Valid values are:
         
         'disabled' : Do not write the classified DSCP into the packet.
         
         'rewrite' : Write the classified DSCP into the packet.
         
         'remap' : Remap the classified DSCP through qosConfigGlobalsDscpTable,
         and write the value from EgressTranslation into the packet.
         
         'remapDp' : Remap the classified DSCP through
         qosConfigGlobalsDscpTable. If the classified DPL is 0 then write the
         value from EgressTranslation into the packet, otherwise write the value
         from EgressTranslationDp1 into the packet.
         
         If qosCapabilitiesHasDscpDplRemarking is false: Selects if and how the
         classified DSCP is written into the packet at egress. Valid values are:
         
         'disabled' : Do not write the classified DSCP into the packet.
         
         'rewrite' : Write the classified DSCP into the packet.
         
         'remap' : Remap the classified DSCP through qosConfigGlobalsDscpTable,
         and write the value from EgressTranslation into the packet.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasDscp' is True."
    ::= { mgmtQosConfigInterfaceEntry 14 }

mgmtQosConfigInterfaceQceAddressMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If false, the QCE classification for this interface is based on source
         (SMAC/SIP) addresses.
         
         If true, the QCE classification for this interface is based on
         destination (DMAC/DIP) addresses.
         
         This parameter is only used when qosCapabilitiesHasQceKeyType is false
         or QceKeyType is normal.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceAddressMode' is True."
    ::= { mgmtQosConfigInterfaceEntry 15 }

mgmtQosConfigInterfaceQceKeyType OBJECT-TYPE
    SYNTAX      MGMTVcapKeyType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The key type specifying the key generated for packets received on the
         interface. Valid values are:
         
         'normal' : Half key, match outer tag, SIP and SMAC.
         
         'doubleTag' : Quarter key, match inner and outer tag.
         
         'ipAddr' : Half key, match inner and outer tag, SIP and DIP. For non-IP
         frames, match outer tag only.
         
         'macIpAddr' : Full key, match inner and outer tag, SMAC, DMAC, SIP and
         DIP.
         
         Filtering on DMAC type (unicast/multicast/broadcast) is supported for
         any key type.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceKeyType' is True."
    ::= { mgmtQosConfigInterfaceEntry 16 }

mgmtQosConfigInterfaceWredGroup OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The WRED group an interface is a member of.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasWredV3' is True."
    ::= { mgmtQosConfigInterfaceEntry 17 }

mgmtQosConfigInterfaceIngressMap OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Ingress Map an interface is associated with for packet
         classification.
         
         Valid range is
         qosCapabilitiesIngressMapIdMin-qosCapabilitiesIngressMapIdMax and 4095
         for NONE
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigInterfaceEntry 18 }

mgmtQosConfigInterfaceEgressMap OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Egress Map an interface is associated with for packet mapping.
         
         Valid range is
         qosCapabilitiesEgressMapIdMin-qosCapabilitiesEgressMapIdMax and 4095
         for NONE
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigInterfaceEntry 19 }

mgmtQosConfigInterfaceCosId OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The default COSID value a packet is classified to.
         
         Valid range is qosCapabilitiesCosIdMin-qosCapabilitiesCosIdMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasCosIdClassification' is True."
    ::= { mgmtQosConfigInterfaceEntry 20 }

mgmtQosConfigInterfaceTagToCosTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceTagToCosEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the mapping of (interface, PCP, DEI) to (CoS, DPL)
         values.
         
         The mappings given by this table is used for all tagged packets
         received on an interface if and only if that interface has the value of
         TrustTag set to 'true'
         
         This is an optional table and is only present if
         qosCapabilitiesHasFixedTagCosMap is false and
         qosCapabilitiesHasTagClassification is true."
    ::= { mgmtQosConfigInterface 2 }

mgmtQosConfigInterfaceTagToCosEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceTagToCosEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the mapping from (interface, PCP, DEI) to (CoS, DPL)
         values."
    INDEX       { mgmtQosConfigInterfaceTagToCosIfIndex,
                  mgmtQosConfigInterfaceTagToCosPcp,
                  mgmtQosConfigInterfaceTagToCosDei }
    ::= { mgmtQosConfigInterfaceTagToCosTable 1 }

MGMTQosConfigInterfaceTagToCosEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceTagToCosIfIndex  MGMTInterfaceIndex,
    mgmtQosConfigInterfaceTagToCosPcp      Integer32,
    mgmtQosConfigInterfaceTagToCosDei      Integer32,
    mgmtQosConfigInterfaceTagToCosCos      Unsigned32,
    mgmtQosConfigInterfaceTagToCosDpl      MGMTUnsigned8
}

mgmtQosConfigInterfaceTagToCosIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTrustTag' is True."
    ::= { mgmtQosConfigInterfaceTagToCosEntry 1 }

mgmtQosConfigInterfaceTagToCosPcp OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "PCP index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTrustTag' is True."
    ::= { mgmtQosConfigInterfaceTagToCosEntry 2 }

mgmtQosConfigInterfaceTagToCosDei OBJECT-TYPE
    SYNTAX      Integer32 (0..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DEI index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTrustTag' is True."
    ::= { mgmtQosConfigInterfaceTagToCosEntry 3 }

mgmtQosConfigInterfaceTagToCosCos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The CoS value which this entry maps to.
         
         Valid range is qosCapabilitiesClassMin-qosCapabilitiesClassMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTrustTag' is True."
    ::= { mgmtQosConfigInterfaceTagToCosEntry 4 }

mgmtQosConfigInterfaceTagToCosDpl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DPL (Drop Precedence Level) value which this entry maps to.
         
         Valid range is qosCapabilitiesDplMin-qosCapabilitiesDplMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTrustTag' is True."
    ::= { mgmtQosConfigInterfaceTagToCosEntry 5 }

mgmtQosConfigInterfaceCosToTagTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceCosToTagEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the mapping of (interface, CoS, DPL) to (PCP, DEI)
         values.
         
         The mappings given by this table is used for packets transmitted on an
         interface if and only if that interface has the value of
         TagRemarkingMode set to 'mapped'
         
         This is an optional table and is only present if
         qosCapabilitiesHasTagRemarking is true."
    ::= { mgmtQosConfigInterface 3 }

mgmtQosConfigInterfaceCosToTagEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceCosToTagEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the mapping from (interface, CoS, DPL) to (PCP, DEI)
         values."
    INDEX       { mgmtQosConfigInterfaceCosToTagIfIndex,
                  mgmtQosConfigInterfaceCosToTagCos,
                  mgmtQosConfigInterfaceCosToTagDpl }
    ::= { mgmtQosConfigInterfaceCosToTagTable 1 }

MGMTQosConfigInterfaceCosToTagEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceCosToTagIfIndex  MGMTInterfaceIndex,
    mgmtQosConfigInterfaceCosToTagCos      Integer32,
    mgmtQosConfigInterfaceCosToTagDpl      Integer32,
    mgmtQosConfigInterfaceCosToTagPcp      Unsigned32,
    mgmtQosConfigInterfaceCosToTagDei      MGMTUnsigned8
}

mgmtQosConfigInterfaceCosToTagIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTagRemarking' is True."
    ::= { mgmtQosConfigInterfaceCosToTagEntry 1 }

mgmtQosConfigInterfaceCosToTagCos OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "CoS index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTagRemarking' is True."
    ::= { mgmtQosConfigInterfaceCosToTagEntry 2 }

mgmtQosConfigInterfaceCosToTagDpl OBJECT-TYPE
    SYNTAX      Integer32 (0..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Color index. It is 0 for DPL 0 and 1 for all other values of DPL
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTagRemarking' is True."
    ::= { mgmtQosConfigInterfaceCosToTagEntry 3 }

mgmtQosConfigInterfaceCosToTagPcp OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value which this entry maps to.
         
         Valid range is 0-7.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTagRemarking' is True."
    ::= { mgmtQosConfigInterfaceCosToTagEntry 4 }

mgmtQosConfigInterfaceCosToTagDei OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DEI value which this entry maps to.
         
         Valid range is 0-1.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasTagRemarking' is True."
    ::= { mgmtQosConfigInterfaceCosToTagEntry 5 }

mgmtQosConfigInterfacePolicerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfacePolicerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides policer configuration for an interface"
    ::= { mgmtQosConfigInterface 4 }

mgmtQosConfigInterfacePolicerEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfacePolicerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific policer"
    INDEX       { mgmtQosConfigInterfacePolicerIfIndex }
    ::= { mgmtQosConfigInterfacePolicerTable 1 }

MGMTQosConfigInterfacePolicerEntry ::= SEQUENCE {
    mgmtQosConfigInterfacePolicerIfIndex      MGMTInterfaceIndex,
    mgmtQosConfigInterfacePolicerEnable       TruthValue,
    mgmtQosConfigInterfacePolicerFrameRate    TruthValue,
    mgmtQosConfigInterfacePolicerFlowControl  TruthValue,
    mgmtQosConfigInterfacePolicerCir          Unsigned32
}

mgmtQosConfigInterfacePolicerIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtQosConfigInterfacePolicerEntry 1 }

mgmtQosConfigInterfacePolicerEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the port policer is enabled."
    ::= { mgmtQosConfigInterfacePolicerEntry 2 }

mgmtQosConfigInterfacePolicerFrameRate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the rate is measured in fps instead of kbps."
    ::= { mgmtQosConfigInterfacePolicerEntry 3 }

mgmtQosConfigInterfacePolicerFlowControl OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, and flow control is enabled on the interface, then issue flow
         control pause frames instead of discarding frames.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortPolicersFc' is True."
    ::= { mgmtQosConfigInterfacePolicerEntry 4 }

mgmtQosConfigInterfacePolicerCir OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Committed Information Rate. Measured in kbps if FrameRate is false and
         fps if FrameRate is true.
         
         Valid range is from the smallest value of
         qosCapabilitiesPortPolicerBitRateMin and
         qosCapabilitiesPortPolicerFrameRateMin to the largest value of
         qosCapabilitiesPortPolicerBitRateMax and
         qosCapabilitiesPortPolicerFrameRateMax.
         
         "
    ::= { mgmtQosConfigInterfacePolicerEntry 5 }

mgmtQosConfigInterfaceQueuePolicerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceQueuePolicerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides queue policer configuration for interfaces
         
         This is an optional table and is only present if
         qosCapabilitiesHasPortQueuePolicers is true."
    ::= { mgmtQosConfigInterface 5 }

mgmtQosConfigInterfaceQueuePolicerEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceQueuePolicerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific queue policer"
    INDEX       { mgmtQosConfigInterfaceQueuePolicerIfIndex,
                  mgmtQosConfigInterfaceQueuePolicerQueue }
    ::= { mgmtQosConfigInterfaceQueuePolicerTable 1 }

MGMTQosConfigInterfaceQueuePolicerEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceQueuePolicerIfIndex  MGMTInterfaceIndex,
    mgmtQosConfigInterfaceQueuePolicerQueue    Integer32,
    mgmtQosConfigInterfaceQueuePolicerEnable   TruthValue,
    mgmtQosConfigInterfaceQueuePolicerCir      Unsigned32
}

mgmtQosConfigInterfaceQueuePolicerIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortQueuePolicers' is True."
    ::= { mgmtQosConfigInterfaceQueuePolicerEntry 1 }

mgmtQosConfigInterfaceQueuePolicerQueue OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Queue index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortQueuePolicers' is True."
    ::= { mgmtQosConfigInterfaceQueuePolicerEntry 2 }

mgmtQosConfigInterfaceQueuePolicerEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the queue policer is enabled.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortQueuePolicers' is True."
    ::= { mgmtQosConfigInterfaceQueuePolicerEntry 3 }

mgmtQosConfigInterfaceQueuePolicerCir OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Committed Information Rate. Measured in kbps.
         
         Valid range is from the smallest value of
         qosCapabilitiesQueuePolicerBitRateMin and
         qosCapabilitiesQueuePolicerFrameRateMin to the largest value of
         qosCapabilitiesQueuePolicerBitRateMax and
         qosCapabilitiesQueuePolicerFrameRateMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortQueuePolicers' is True."
    ::= { mgmtQosConfigInterfaceQueuePolicerEntry 6 }

mgmtQosConfigInterfaceShaperTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceShaperEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides shaper configuration for an interface"
    ::= { mgmtQosConfigInterface 6 }

mgmtQosConfigInterfaceShaperEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceShaperEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific shaper"
    INDEX       { mgmtQosConfigInterfaceShaperIfIndex }
    ::= { mgmtQosConfigInterfaceShaperTable 1 }

MGMTQosConfigInterfaceShaperEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceShaperIfIndex   MGMTInterfaceIndex,
    mgmtQosConfigInterfaceShaperEnable    TruthValue,
    mgmtQosConfigInterfaceShaperCir       Unsigned32,
    mgmtQosConfigInterfaceShaperRateType  MGMTQosShaperRateType
}

mgmtQosConfigInterfaceShaperIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtQosConfigInterfaceShaperEntry 1 }

mgmtQosConfigInterfaceShaperEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the port shaper is enabled."
    ::= { mgmtQosConfigInterfaceShaperEntry 2 }

mgmtQosConfigInterfaceShaperCir OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Committed Information Rate. Measured in kbps.
         
         Valid range is from the smallest value of
         qosCapabilitiesPortShaperBitRateMin and
         qosCapabilitiesPortShaperFrameRateMin to the largest value of
         qosCapabilitiesPortShaperBitRateMax and
         qosCapabilitiesPortShaperFrameRateMax.
         
         "
    ::= { mgmtQosConfigInterfaceShaperEntry 3 }

mgmtQosConfigInterfaceShaperRateType OBJECT-TYPE
    SYNTAX      MGMTQosShaperRateType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the shaper's rate type.
         
         Valid selections are line (0) and data (1).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasShapersRt' is True."
    ::= { mgmtQosConfigInterfaceShaperEntry 4 }

mgmtQosConfigInterfaceQueueShaperTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceQueueShaperEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides queue shaper configuration for interfaces"
    ::= { mgmtQosConfigInterface 7 }

mgmtQosConfigInterfaceQueueShaperEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceQueueShaperEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific queue shaper"
    INDEX       { mgmtQosConfigInterfaceQueueShaperIfIndex,
                  mgmtQosConfigInterfaceQueueShaperQueue }
    ::= { mgmtQosConfigInterfaceQueueShaperTable 1 }

MGMTQosConfigInterfaceQueueShaperEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceQueueShaperIfIndex   MGMTInterfaceIndex,
    mgmtQosConfigInterfaceQueueShaperQueue     Integer32,
    mgmtQosConfigInterfaceQueueShaperEnable    TruthValue,
    mgmtQosConfigInterfaceQueueShaperExcess    TruthValue,
    mgmtQosConfigInterfaceQueueShaperCir       Unsigned32,
    mgmtQosConfigInterfaceQueueShaperRateType  MGMTQosShaperRateType,
    mgmtQosConfigInterfaceQueueShaperCredit    TruthValue
}

mgmtQosConfigInterfaceQueueShaperIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtQosConfigInterfaceQueueShaperEntry 1 }

mgmtQosConfigInterfaceQueueShaperQueue OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Queue index."
    ::= { mgmtQosConfigInterfaceQueueShaperEntry 2 }

mgmtQosConfigInterfaceQueueShaperEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the queue shaper is enabled."
    ::= { mgmtQosConfigInterfaceQueueShaperEntry 3 }

mgmtQosConfigInterfaceQueueShaperExcess OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, then allow this queue to get a share of the excess bandwidth.
         
         Excess bandwidth is allowed if this shaper is closed and no other
         queues with open shapers have frames for transmission.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQueueShapersExcess' is True."
    ::= { mgmtQosConfigInterfaceQueueShaperEntry 4 }

mgmtQosConfigInterfaceQueueShaperCir OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Committed Information Rate. Measured in kbps.
         
         Valid range is from the smallest value of
         qosCapabilitiesQueueShaperBitRateMin and
         qosCapabilitiesQueueShaperFrameRateMin to the largest value of
         qosCapabilitiesQueueShaperBitRateMax and
         qosCapabilitiesQueueShaperFrameRateMax.
         
         "
    ::= { mgmtQosConfigInterfaceQueueShaperEntry 5 }

mgmtQosConfigInterfaceQueueShaperRateType OBJECT-TYPE
    SYNTAX      MGMTQosShaperRateType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the shaper's rate type.
         
         Valid selections are line (0) and data (1).
         
         NOTE: This configuration is valid only for Normal port mode. If the
         port is in Basic or Hierarchical mode, the rate-type will be stored and
         become active when the port is switched back to Normal Scheduling mode.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasShapersRt' is True."
    ::= { mgmtQosConfigInterfaceQueueShaperEntry 6 }

mgmtQosConfigInterfaceQueueShaperCredit OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, then this queue supports the credit based shaper.
         
         Creating burst capacity only when data is available.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQueueShapersCredit' is True."
    ::= { mgmtQosConfigInterfaceQueueShaperEntry 7 }

mgmtQosConfigInterfaceSchedulerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceSchedulerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the mapping of (interface, queue) to weight values.
         
         The mappings given by this table is used when an interface has the
         value of DwrrCount greater than 0.
         
         Read the qosSchedulerStatusTable in order to get the 'real' weights in
         percent as used by the hardware."
    ::= { mgmtQosConfigInterface 8 }

mgmtQosConfigInterfaceSchedulerEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceSchedulerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the scheduler configuration for a specific queue."
    INDEX       { mgmtQosConfigInterfaceSchedulerIfIndex,
                  mgmtQosConfigInterfaceSchedulerQueue }
    ::= { mgmtQosConfigInterfaceSchedulerTable 1 }

MGMTQosConfigInterfaceSchedulerEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceSchedulerIfIndex          MGMTInterfaceIndex,
    mgmtQosConfigInterfaceSchedulerQueue            Integer32,
    mgmtQosConfigInterfaceSchedulerWeight           MGMTPercent,
    mgmtQosConfigInterfaceSchedulerCutThrough       TruthValue,
    mgmtQosConfigInterfaceSchedulerFramePreemption  TruthValue
}

mgmtQosConfigInterfaceSchedulerIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtQosConfigInterfaceSchedulerEntry 1 }

mgmtQosConfigInterfaceSchedulerQueue OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Queue index."
    ::= { mgmtQosConfigInterfaceSchedulerEntry 2 }

mgmtQosConfigInterfaceSchedulerWeight OBJECT-TYPE
    SYNTAX      MGMTPercent
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The weight for this queue.
         
         Valid range is 1-100."
    ::= { mgmtQosConfigInterfaceSchedulerEntry 3 }

mgmtQosConfigInterfaceSchedulerCutThrough OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, then this queue supports the cut through capability.
         
         Supported only when egress speed is lower or equal to ingress speed.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQueueCutThrough' is True."
    ::= { mgmtQosConfigInterfaceSchedulerEntry 4 }

mgmtQosConfigInterfaceSchedulerFramePreemption OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, then this queue supports frame preemption capability.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQueueFramePreemption' is True."
    ::= { mgmtQosConfigInterfaceSchedulerEntry 5 }

mgmtQosConfigInterfaceStormPolicerUnicastTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceStormPolicerUnicastEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides storm policer configuration for unicast packets
         received on an interface
         
         This is an optional table and is only present if
         qosCapabilitiesHasPortStormPolicers is true."
    ::= { mgmtQosConfigInterface 9 }

mgmtQosConfigInterfaceStormPolicerUnicastEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceStormPolicerUnicastEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific storm policer"
    INDEX       { mgmtQosConfigInterfaceStormPolicerUnicastIfIndex }
    ::= { mgmtQosConfigInterfaceStormPolicerUnicastTable 1 }

MGMTQosConfigInterfaceStormPolicerUnicastEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceStormPolicerUnicastIfIndex    MGMTInterfaceIndex,
    mgmtQosConfigInterfaceStormPolicerUnicastEnable     TruthValue,
    mgmtQosConfigInterfaceStormPolicerUnicastFrameRate  TruthValue,
    mgmtQosConfigInterfaceStormPolicerUnicastCir        Unsigned32
}

mgmtQosConfigInterfaceStormPolicerUnicastIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerUnicastEntry 1 }

mgmtQosConfigInterfaceStormPolicerUnicastEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the storm policer is enabled.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerUnicastEntry 2 }

mgmtQosConfigInterfaceStormPolicerUnicastFrameRate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the rate is measured in fps instead of kbps.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerUnicastEntry 3 }

mgmtQosConfigInterfaceStormPolicerUnicastCir OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Committed Information Rate. Measured in kbps if FrameRate is false and
         fps if FrameRate is true.
         
         Valid range is from the smallest value of
         qosCapabilitiesPortStormBitRateMin and
         qosCapabilitiesPortStormFrameRateMin to the largest value of
         qosCapabilitiesPortStormBitRateMax and
         qosCapabilitiesPortStormFrameRateMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerUnicastEntry 4 }

mgmtQosConfigInterfaceStormPolicerBroadcastTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceStormPolicerBroadcastEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides storm policer configuration for broadcast packets
         received on an interface
         
         This is an optional table and is only present if
         qosCapabilitiesHasPortStormPolicers is true."
    ::= { mgmtQosConfigInterface 10 }

mgmtQosConfigInterfaceStormPolicerBroadcastEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceStormPolicerBroadcastEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific storm policer"
    INDEX       {                   mgmtQosConfigInterfaceStormPolicerBroadcastIfIndex }
    ::= { mgmtQosConfigInterfaceStormPolicerBroadcastTable 1 }

MGMTQosConfigInterfaceStormPolicerBroadcastEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceStormPolicerBroadcastIfIndex    MGMTInterfaceIndex,
    mgmtQosConfigInterfaceStormPolicerBroadcastEnable     TruthValue,
    mgmtQosConfigInterfaceStormPolicerBroadcastFrameRate  TruthValue,
    mgmtQosConfigInterfaceStormPolicerBroadcastCir        Unsigned32
}

mgmtQosConfigInterfaceStormPolicerBroadcastIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerBroadcastEntry 1 }

mgmtQosConfigInterfaceStormPolicerBroadcastEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the storm policer is enabled.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerBroadcastEntry 2 }

mgmtQosConfigInterfaceStormPolicerBroadcastFrameRate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the rate is measured in fps instead of kbps.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerBroadcastEntry 3 }

mgmtQosConfigInterfaceStormPolicerBroadcastCir OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Committed Information Rate. Measured in kbps if FrameRate is false and
         fps if FrameRate is true.
         
         Valid range is from the smallest value of
         qosCapabilitiesPortStormBitRateMin and
         qosCapabilitiesPortStormFrameRateMin to the largest value of
         qosCapabilitiesPortStormBitRateMax and
         qosCapabilitiesPortStormFrameRateMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerBroadcastEntry 4 }

mgmtQosConfigInterfaceStormPolicerUnknownTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigInterfaceStormPolicerUnknownEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides storm policer configuration for unknown (flooded)
         packets received on an interface
         
         This is an optional table and is only present if
         qosCapabilitiesHasPortStormPolicers is true."
    ::= { mgmtQosConfigInterface 11 }

mgmtQosConfigInterfaceStormPolicerUnknownEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigInterfaceStormPolicerUnknownEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration for a specific storm policer"
    INDEX       { mgmtQosConfigInterfaceStormPolicerUnknownIfIndex }
    ::= { mgmtQosConfigInterfaceStormPolicerUnknownTable 1 }

MGMTQosConfigInterfaceStormPolicerUnknownEntry ::= SEQUENCE {
    mgmtQosConfigInterfaceStormPolicerUnknownIfIndex    MGMTInterfaceIndex,
    mgmtQosConfigInterfaceStormPolicerUnknownEnable     TruthValue,
    mgmtQosConfigInterfaceStormPolicerUnknownFrameRate  TruthValue,
    mgmtQosConfigInterfaceStormPolicerUnknownCir        Unsigned32
}

mgmtQosConfigInterfaceStormPolicerUnknownIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerUnknownEntry 1 }

mgmtQosConfigInterfaceStormPolicerUnknownEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the storm policer is enabled.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerUnknownEntry 2 }

mgmtQosConfigInterfaceStormPolicerUnknownFrameRate OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the rate is measured in fps instead of kbps.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerUnknownEntry 3 }

mgmtQosConfigInterfaceStormPolicerUnknownCir OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Committed Information Rate. Measured in kbps if FrameRate is false and
         fps if FrameRate is true.
         
         Valid range is from the smallest value of
         qosCapabilitiesPortStormBitRateMin and
         qosCapabilitiesPortStormFrameRateMin to the largest value of
         qosCapabilitiesPortStormBitRateMax and
         qosCapabilitiesPortStormFrameRateMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasPortStormPolicers' is True."
    ::= { mgmtQosConfigInterfaceStormPolicerUnknownEntry 4 }

mgmtQosConfigQce OBJECT IDENTIFIER
    ::= { mgmtQosConfig 3 }

mgmtQosConfigQceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigQceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the configuration of QCEs. The index is QceId.
         
         This is an optional table and is only present if qosCapabilitiesHasQce
         is true."
    ::= { mgmtQosConfigQce 1 }

mgmtQosConfigQceEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigQceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration of a single QCE."
    INDEX       { mgmtQosConfigQceQceId }
    ::= { mgmtQosConfigQceTable 1 }

MGMTQosConfigQceEntry ::= SEQUENCE {
    mgmtQosConfigQceQceId               Integer32,
    mgmtQosConfigQceNextQceId           Unsigned32,
    mgmtQosConfigQceSwitchId            Unsigned32,
    mgmtQosConfigQcePortList            MGMTPortList,
    mgmtQosConfigQceDestMacType         MGMTDestMacType,
    mgmtQosConfigQceDestMac             MacAddress,
    mgmtQosConfigQceDestMacMask         MacAddress,
    mgmtQosConfigQceSrcMac              MacAddress,
    mgmtQosConfigQceSrcMacMask          MacAddress,
    mgmtQosConfigQceVlanTagType         MGMTVlanTagType,
    mgmtQosConfigQceVlanIdOp            MGMTASRType,
    mgmtQosConfigQceVlanId              MGMTUnsigned16,
    mgmtQosConfigQceVlanIdRange         MGMTUnsigned16,
    mgmtQosConfigQcePcp                 MGMTVlanTagPriority,
    mgmtQosConfigQceDei                 MGMTBitType,
    mgmtQosConfigQceInnerVlanTagType    MGMTVlanTagType,
    mgmtQosConfigQceInnerVlanIdOp       MGMTASRType,
    mgmtQosConfigQceInnerVlanId         MGMTUnsigned16,
    mgmtQosConfigQceInnerVlanIdRange    MGMTUnsigned16,
    mgmtQosConfigQceInnerPcp            MGMTVlanTagPriority,
    mgmtQosConfigQceInnerDei            MGMTBitType,
    mgmtQosConfigQceFrameType           MGMTQosQceFrameType,
    mgmtQosConfigQceEtype               MGMTEtherType,
    mgmtQosConfigQceLlcDsap             MGMTUnsigned8,
    mgmtQosConfigQceLlcDsapMask         MGMTUnsigned8,
    mgmtQosConfigQceLlcSsap             MGMTUnsigned8,
    mgmtQosConfigQceLlcSsapMask         MGMTUnsigned8,
    mgmtQosConfigQceLlcControl          MGMTUnsigned8,
    mgmtQosConfigQceLlcControlMask      MGMTUnsigned8,
    mgmtQosConfigQceSnapPid             MGMTUnsigned16,
    mgmtQosConfigQceSnapPidMask         MGMTUnsigned16,
    mgmtQosConfigQceIpv4Fragment        MGMTBitType,
    mgmtQosConfigQceIpv4DscpOp          MGMTASRType,
    mgmtQosConfigQceIpv4Dscp            Dscp,
    mgmtQosConfigQceIpv4DscpRange       Dscp,
    mgmtQosConfigQceIpv4Protocol        MGMTUnsigned8,
    mgmtQosConfigQceIpv4ProtocolMask    MGMTUnsigned8,
    mgmtQosConfigQceIpv4SrcIp           IpAddress,
    mgmtQosConfigQceIpv4SrcIpMask       IpAddress,
    mgmtQosConfigQceIpv4DestIp          IpAddress,
    mgmtQosConfigQceIpv4DestIpMask      IpAddress,
    mgmtQosConfigQceIpv4SrcPortOp       MGMTASRType,
    mgmtQosConfigQceIpv4SrcPort         MGMTUnsigned16,
    mgmtQosConfigQceIpv4SrcPortRange    MGMTUnsigned16,
    mgmtQosConfigQceIpv4DestPortOp      MGMTASRType,
    mgmtQosConfigQceIpv4DestPort        MGMTUnsigned16,
    mgmtQosConfigQceIpv4DestPortRange   MGMTUnsigned16,
    mgmtQosConfigQceIpv6DscpOp          MGMTASRType,
    mgmtQosConfigQceIpv6Dscp            Dscp,
    mgmtQosConfigQceIpv6DscpRange       Dscp,
    mgmtQosConfigQceIpv6Protocol        MGMTUnsigned8,
    mgmtQosConfigQceIpv6ProtocolMask    MGMTUnsigned8,
    mgmtQosConfigQceIpv6SrcIp           InetAddressIPv6,
    mgmtQosConfigQceIpv6SrcIpMask       InetAddressIPv6,
    mgmtQosConfigQceIpv6DestIp          InetAddressIPv6,
    mgmtQosConfigQceIpv6DestIpMask      InetAddressIPv6,
    mgmtQosConfigQceIpv6SrcPortOp       MGMTASRType,
    mgmtQosConfigQceIpv6SrcPort         MGMTUnsigned16,
    mgmtQosConfigQceIpv6SrcPortRange    MGMTUnsigned16,
    mgmtQosConfigQceIpv6DestPortOp      MGMTASRType,
    mgmtQosConfigQceIpv6DestPort        MGMTUnsigned16,
    mgmtQosConfigQceIpv6DestPortRange   MGMTUnsigned16,
    mgmtQosConfigQceActionCosEnable     TruthValue,
    mgmtQosConfigQceActionCos           Unsigned32,
    mgmtQosConfigQceActionDplEnable     TruthValue,
    mgmtQosConfigQceActionDpl           MGMTUnsigned8,
    mgmtQosConfigQceActionDscpEnable    TruthValue,
    mgmtQosConfigQceActionDscp          MGMTUnsigned8,
    mgmtQosConfigQceActionPcpDeiEnable  TruthValue,
    mgmtQosConfigQceActionPcp           Unsigned32,
    mgmtQosConfigQceActionDei           MGMTUnsigned8,
    mgmtQosConfigQceActionPolicyEnable  TruthValue,
    mgmtQosConfigQceActionPolicy        Unsigned32,
    mgmtQosConfigQceActionMapEnable     TruthValue,
    mgmtQosConfigQceActionMap           MGMTUnsigned16,
    mgmtQosConfigQceAction              MGMTRowEditorState
}

mgmtQosConfigQceQceId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "QCE Id.
         
         Valid range is qosCapabilitiesQceIdMin-qosCapabilitiesQceIdMax or 0.
         
         Using a value of 0 when adding a new QCE, will assign the QCE Id in the
         new QCE to the first unused QCE Id.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 1 }

mgmtQosConfigQceNextQceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "QCE Id of the next QCE. Valid range is
         qosCapabilitiesQceIdMin-qosCapabilitiesQceIdMax or 0.
         
         The value 0, denotes the end of the list.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 2 }

mgmtQosConfigQceSwitchId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the switch on which this QCE is active.Valid range is
         documented in the Stacking MIB.
         
         This object is only valid on stacking platforms.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 3 }

mgmtQosConfigQcePortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of ports that are member of this QCE.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 4 }

mgmtQosConfigQceDestMacType OBJECT-TYPE
    SYNTAX      MGMTDestMacType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the destination MAC type to match.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 5 }

mgmtQosConfigQceDestMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits destination MAC address.
         
         The packet's destination address is AND-ed with the value of
         DestMacMask and then compared against the value of this object.
         
         If this object value and the value of DestMacMask is 00-00-00-00-00-00,
         this entry matches any destination MAC address.
         
         This object can only be configured if DestMacType is any(0) and
         DestMacMask is ff-ff-ff-ff-ff-ff.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDmac' is True."
    ::= { mgmtQosConfigQceEntry 6 }

mgmtQosConfigQceDestMacMask OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits destination MAC address mask.
         
         Valid values are 00-00-00-00-00-00 or ff-ff-ff-ff-ff-ff.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDmac' is True."
    ::= { mgmtQosConfigQceEntry 7 }

mgmtQosConfigQceSrcMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits source MAC address.
         
         The packet's source address is AND-ed with the value of SrcMacMask and
         then compared against the value of this object.
         
         If this object value and the value of SrcMacMask is 00-00-00-00-00-00,
         this entry matches any source MAC address.
         
         This object can only be configured if SrcMacMask is ff-ff-ff-ff-ff-ff.
         
         If the value of qosCapabilitiesHasQceMacOui is true then only the OUI
         part of the MAC address (24 most significant bits) is used.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 8 }

mgmtQosConfigQceSrcMacMask OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits source MAC address mask.
         
         Valid values are 00-00-00-00-00-00 or ff-ff-ff-ff-ff-ff.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 9 }

mgmtQosConfigQceVlanTagType OBJECT-TYPE
    SYNTAX      MGMTVlanTagType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the VLAN tag type to match.
         
         The value cTagged(3) is only supported if qosCapabilitiesHasQceCTag is
         true.
         
         The value sTagged(3) is only supported if qosCapabilitiesHasQceSTag is
         true.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 10 }

mgmtQosConfigQceVlanIdOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packets's VLAN ID is to be compared.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 11 }

mgmtQosConfigQceVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID to be compared.
         
         If the VlanIdOp object in the same row is range(2), this object will be
         the starting VLAN ID of the range.
         
         Valid range is 0-4095.
         
         This object can only be configured if VlanIdOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 12 }

mgmtQosConfigQceVlanIdRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID to be compared.
         
         If the VlanIdOp object in the same row is range(2), this object will be
         the ending VLAN ID of the range.
         
         Valid range is 0-4095.
         
         This object can only be configured if VlanIdOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 13 }

mgmtQosConfigQcePcp OBJECT-TYPE
    SYNTAX      MGMTVlanTagPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value(s) to be compared.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 14 }

mgmtQosConfigQceDei OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DEI value to be compared.
         
         Valid range is 0-1.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 15 }

mgmtQosConfigQceInnerVlanTagType OBJECT-TYPE
    SYNTAX      MGMTVlanTagType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the inner VLAN tag type to match.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceEntry 16 }

mgmtQosConfigQceInnerVlanIdOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packets's inner VLAN ID is to be compared.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceEntry 17 }

mgmtQosConfigQceInnerVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The inner VLAN ID to be compared.
         
         If the InnerVlanIdOp object in the same row is range(2), this object
         will be the starting VLAN ID of the range.
         
         Valid range is 0-4095.
         
         This object can only be configured if InnerVlanIdOp is not any(0).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceEntry 18 }

mgmtQosConfigQceInnerVlanIdRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The inner VLAN ID to be compared.
         
         If the InnerVlanIdOp object in the same row is range(2), this object
         will be the ending VLAN ID of the range.
         
         Valid range is 0-4095.
         
         This object can only be configured if InnerVlanIdOp is range(2).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceEntry 19 }

mgmtQosConfigQceInnerPcp OBJECT-TYPE
    SYNTAX      MGMTVlanTagPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The inner PCP value(s) to be compared.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceEntry 20 }

mgmtQosConfigQceInnerDei OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The inner DEI value to be compared.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceEntry 21 }

mgmtQosConfigQceFrameType OBJECT-TYPE
    SYNTAX      MGMTQosQceFrameType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packet's frame type.
         
         Modifying the frame type on an existing QCE will restore the content of
         all frame type dependent configuration to default.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 100 }

mgmtQosConfigQceEtype OBJECT-TYPE
    SYNTAX      MGMTEtherType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packet's 16 bit Ethernet type field.
         
         Valid values are: 0(match any), 0x600-0xFFFF but excluding 0x800(IPv4)
         and 0x86DD(IPv6).
         
         This object can only be configured if FrameType is etype(1).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 101 }

mgmtQosConfigQceLlcDsap OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packets's 8 bit LLC DSAP field.
         
         The packet's LLC DSAP field is AND-ed with the value of LlcDsapMask and
         then compared against the value of this object.
         
         If this object value and the value of LlcDsapMask is 0x00, this entry
         matches any LLC DSAP.
         
         Valid range is 0x00-0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 200 }

mgmtQosConfigQceLlcDsapMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 8 bit LLC DSAP mask.
         
         Valid values are 0x00 or 0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 201 }

mgmtQosConfigQceLlcSsap OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packets's 8 bit LLC SSAP field.
         
         The packet's LLC SSAP field is AND-ed with the value of LlcSsapMask and
         then compared against the value of this object.
         
         If this object value and the value of LlcSsapMask is 0x00, this entry
         matches any LLC SSAP.
         
         Valid range is 0x00-0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 202 }

mgmtQosConfigQceLlcSsapMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 8 bit LLC SSAP mask.
         
         Valid values are 0x00 or 0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 203 }

mgmtQosConfigQceLlcControl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packets's 8 bit LLC Control field.
         
         The packet's LLC Control field is AND-ed with the value of
         LlcControlMask and then compared against the value of this object.
         
         If this object value and the value of LlcControlMask is 0x00, this
         entry matches any LLC Control.
         
         Valid range is 0x00-0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 204 }

mgmtQosConfigQceLlcControlMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 8 bit LLC Control mask.
         
         Valid values are 0x00 or 0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 205 }

mgmtQosConfigQceSnapPid OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packet's 16 bit SNAP PID field.
         
         The packet's SNAP PID field is AND-ed with the value of SnapPidMask and
         then compared against the value of this object.
         
         If this object value and the value of SnapPidMask is 0x0000, this entry
         matches any ethertype.
         
         This object can only be configured if FrameType is snap(3).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 302 }

mgmtQosConfigQceSnapPidMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 16 bit SNAP PID mask.
         
         Valid values are 0x0000 or 0xFFFF.
         
         This object can only be configured if FrameType is snap(3).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 303 }

mgmtQosConfigQceIpv4Fragment OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how an IPv4 fragment bit is to be compared.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 400 }

mgmtQosConfigQceIpv4DscpOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how an IPv4 DSCP field is to be compared.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 401 }

mgmtQosConfigQceIpv4Dscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value to be compared.
         
         If the Ipv4DscpOp object in the same row is range(2), this object will
         be the starting DSCP value of the range.
         
         Valid range is 0-63.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4DscpOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 402 }

mgmtQosConfigQceIpv4DscpRange OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value to be compared.
         
         If the Ipv4DscpOp object in the same row is range(2), this object will
         be the ending DSCP value of the range.
         
         Valid range is 0-63.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4DscpOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 403 }

mgmtQosConfigQceIpv4Protocol OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The protocol number field in the IPv4 header used to indicate a higher
         layer protocol.
         
         The packet's protocol number field is AND-ed with the value of
         Ipv4ProtocolMask and then compared with the value of this object.
         
         If Ipv4Protocol and Ipv4protocolMask are 0, this entry matches any IPv4
         protocol.
         
         Valid range is 0-255.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 404 }

mgmtQosConfigQceIpv4ProtocolMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 protocol number mask.
         
         Valid values are 0 or 255.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 405 }

mgmtQosConfigQceIpv4SrcIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 source address.
         
         The packet's source address is AND-ed with the value of Ipv4SrcIpMask
         and then compared with the value of this object.
         
         If Ipv4SrcIP and Ipv4SrcIpMask are 0.0.0.0, this entry matches any IPv4
         source address.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 406 }

mgmtQosConfigQceIpv4SrcIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 source address mask.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 407 }

mgmtQosConfigQceIpv4DestIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 destination address.
         
         The packet's destination address is AND-ed with the value of
         Ipv4DestIpMask and then compared with the value of this object.
         
         If Ipv4DestIP and Ipv4DestIpMask are 0.0.0.0, this entry matches any
         IPv4 destination address.
         
         This object can only be configured if FrameType is ipv4(4).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDip' is True."
    ::= { mgmtQosConfigQceEntry 408 }

mgmtQosConfigQceIpv4DestIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 destination address mask.
         
         This object can only be configured if FrameType is ipv4(4).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDip' is True."
    ::= { mgmtQosConfigQceEntry 409 }

mgmtQosConfigQceIpv4SrcPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's source TCP/UDP port number is to be compared.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 410 }

mgmtQosConfigQceIpv4SrcPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv4SrcPortOp object in the same row is range(2), this object
         will be the starting port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4SrcPortOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 411 }

mgmtQosConfigQceIpv4SrcPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv4SrcPortOp object in the same row is range(2), this object
         will be the ending port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4SrcPortOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 412 }

mgmtQosConfigQceIpv4DestPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination TCP/UDP port number is to be
         compared.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 413 }

mgmtQosConfigQceIpv4DestPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol.
         
         If the Ipv4DestPortOp object in the same row is range(2), this object
         will be the starting port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4DestPortOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 414 }

mgmtQosConfigQceIpv4DestPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv4DestPortOp object in the same row is range(2), this object
         will be the ending port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4DestPortOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 415 }

mgmtQosConfigQceIpv6DscpOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how an IPv6 DSCP field is to be compared.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 500 }

mgmtQosConfigQceIpv6Dscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value to be compared.
         
         If the Ipv6DscpOp object in the same row is range(2), this object will
         be the starting DSCP value of the range.
         
         Valid range is 0-63.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6DscpOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 501 }

mgmtQosConfigQceIpv6DscpRange OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value to be compared.
         
         If the Ipv6DscpOp object in the same row is range(2), this object will
         be the ending DSCP value of the range.
         
         Valid range is 0-63.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6DscpOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 502 }

mgmtQosConfigQceIpv6Protocol OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The protocol number field in the IPv6 header used to indicate a higher
         layer protocol.
         
         The packet's protocol number field is AND-ed with the value of
         Ipv6ProtocolMask and then compared with the value of this object.
         
         If Ipv46rotocol and Ipv6protocolMask are 0, this entry matches any IPv6
         protocol.
         
         Valid range is 0-255.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 503 }

mgmtQosConfigQceIpv6ProtocolMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 protocol number mask.
         
         Valid values are 0 or 255.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 504 }

mgmtQosConfigQceIpv6SrcIp OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 source address.
         
         The packet's source address is AND-ed with the value of Ipv6SrcIpMask
         and then compared with the value of this object.
         
         If Ipv6SrcIP and Ipv6SrcIpMask are all zeros, this entry matches any
         IPv6 source address.
         
         Only the least significant 32 bits of this object are used.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 506 }

mgmtQosConfigQceIpv6SrcIpMask OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 source address mask.
         
         Only the least significant 32 bits of this object are used.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 508 }

mgmtQosConfigQceIpv6DestIp OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 destination address.
         
         The packet's destination address is AND-ed with the value of
         Ipv6DestIpMask and then compared with the value of this object.
         
         If Ipv6DestIP and Ipv6DestIpMask are all zeros, this entry matches any
         IPv6 destination address.
         
         Only the least significant 32 bits of this object are used.
         
         This object can only be configured if FrameType is ipv6(5).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDip' is True."
    ::= { mgmtQosConfigQceEntry 510 }

mgmtQosConfigQceIpv6DestIpMask OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 destination address mask.
         
         Only the least significant 32 bits of this object are used.
         
         This object can only be configured if FrameType is ipv6(5).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDip' is True."
    ::= { mgmtQosConfigQceEntry 512 }

mgmtQosConfigQceIpv6SrcPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's source TCP/UDP port number is to be compared.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 513 }

mgmtQosConfigQceIpv6SrcPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv6SrcPortOp object in the same row is range(2), this object
         will be the starting port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6SrcPortOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 514 }

mgmtQosConfigQceIpv6SrcPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv6SrcPortOp object in the same row is range(2), this object
         will be the ending port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6SrcPortOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 515 }

mgmtQosConfigQceIpv6DestPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination TCP/UDP port number is to be
         compared.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 516 }

mgmtQosConfigQceIpv6DestPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol.
         
         If the Ipv6DestPortOp object in the same row is range(2), this object
         will be the starting port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6DestPortOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 517 }

mgmtQosConfigQceIpv6DestPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv6DestPortOp object in the same row is range(2), this object
         will be the ending port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6DestPortOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 518 }

mgmtQosConfigQceActionCosEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the CoS value in ActionCos.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 1000 }

mgmtQosConfigQceActionCos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The CoS value used for classification.
         
         Valid range is qosCapabilitiesClassMin-qosCapabilitiesClassMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 1001 }

mgmtQosConfigQceActionDplEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the DPL value in ActionDpl.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 1002 }

mgmtQosConfigQceActionDpl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DPL value used for classification.
         
         Valid range is qosCapabilitiesDplMin-qosCapabilitiesDplMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 1003 }

mgmtQosConfigQceActionDscpEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the DSCP value in ActionDscp.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 1004 }

mgmtQosConfigQceActionDscp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value used for classification.
         
         Valid range is 0-63.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 1005 }

mgmtQosConfigQceActionPcpDeiEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the PCP value in ActionPcp and the
         DEI value in ActionDei.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPcpDei' is True."
    ::= { mgmtQosConfigQceEntry 1006 }

mgmtQosConfigQceActionPcp OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value used for classification.
         
         Valid range is 0-7.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPcpDei' is True."
    ::= { mgmtQosConfigQceEntry 1007 }

mgmtQosConfigQceActionDei OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DEI value used for classification.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPcpDei' is True."
    ::= { mgmtQosConfigQceEntry 1008 }

mgmtQosConfigQceActionPolicyEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the policy number in ActionPolicy.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPolicy' is True."
    ::= { mgmtQosConfigQceEntry 1009 }

mgmtQosConfigQceActionPolicy OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The policy number used for classification.
         
         Valid range is documented in the ACL MIB.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPolicy' is True."
    ::= { mgmtQosConfigQceEntry 1010 }

mgmtQosConfigQceActionMapEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified by the Ingress Map Id in ActionMap.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionMap' is True."
    ::= { mgmtQosConfigQceEntry 1011 }

mgmtQosConfigQceActionMap OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Id of an Ingress Map used for classification.
         
         Valid range is
         qosCapabilitiesIngressMapIdMin-qosCapabilitiesIngressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionMap' is True."
    ::= { mgmtQosConfigQceEntry 1012 }

mgmtQosConfigQceAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceEntry 10000 }

mgmtQosConfigQceTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtQosConfigQce 2 }

mgmtQosConfigQceTableRowEditorQceId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "QCE Id.
         
         Valid range is qosCapabilitiesQceIdMin-qosCapabilitiesQceIdMax or 0.
         
         Using a value of 0 when adding a new QCE, will assign the QCE Id in the
         new QCE to the first unused QCE Id.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1 }

mgmtQosConfigQceTableRowEditorNextQceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "QCE Id of the next QCE. Valid range is
         qosCapabilitiesQceIdMin-qosCapabilitiesQceIdMax or 0.
         
         The value 0, denotes the end of the list.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 2 }

mgmtQosConfigQceTableRowEditorSwitchId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the switch on which this QCE is active.Valid range is
         documented in the Stacking MIB.
         
         This object is only valid on stacking platforms.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 3 }

mgmtQosConfigQceTableRowEditorPortList OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "List of ports that are member of this QCE.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 4 }

mgmtQosConfigQceTableRowEditorDestMacType OBJECT-TYPE
    SYNTAX      MGMTDestMacType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the destination MAC type to match.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 5 }

mgmtQosConfigQceTableRowEditorDestMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits destination MAC address.
         
         The packet's destination address is AND-ed with the value of
         DestMacMask and then compared against the value of this object.
         
         If this object value and the value of DestMacMask is 00-00-00-00-00-00,
         this entry matches any destination MAC address.
         
         This object can only be configured if DestMacType is any(0) and
         DestMacMask is ff-ff-ff-ff-ff-ff.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDmac' is True."
    ::= { mgmtQosConfigQceTableRowEditor 6 }

mgmtQosConfigQceTableRowEditorDestMacMask OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits destination MAC address mask.
         
         Valid values are 00-00-00-00-00-00 or ff-ff-ff-ff-ff-ff.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDmac' is True."
    ::= { mgmtQosConfigQceTableRowEditor 7 }

mgmtQosConfigQceTableRowEditorSrcMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits source MAC address.
         
         The packet's source address is AND-ed with the value of SrcMacMask and
         then compared against the value of this object.
         
         If this object value and the value of SrcMacMask is 00-00-00-00-00-00,
         this entry matches any source MAC address.
         
         This object can only be configured if SrcMacMask is ff-ff-ff-ff-ff-ff.
         
         If the value of qosCapabilitiesHasQceMacOui is true then only the OUI
         part of the MAC address (24 most significant bits) is used.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 8 }

mgmtQosConfigQceTableRowEditorSrcMacMask OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 48 bits source MAC address mask.
         
         Valid values are 00-00-00-00-00-00 or ff-ff-ff-ff-ff-ff.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 9 }

mgmtQosConfigQceTableRowEditorVlanTagType OBJECT-TYPE
    SYNTAX      MGMTVlanTagType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the VLAN tag type to match.
         
         The value cTagged(3) is only supported if qosCapabilitiesHasQceCTag is
         true.
         
         The value sTagged(3) is only supported if qosCapabilitiesHasQceSTag is
         true.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 10 }

mgmtQosConfigQceTableRowEditorVlanIdOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packets's VLAN ID is to be compared.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 11 }

mgmtQosConfigQceTableRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID to be compared.
         
         If the VlanIdOp object in the same row is range(2), this object will be
         the starting VLAN ID of the range.
         
         Valid range is 0-4095.
         
         This object can only be configured if VlanIdOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 12 }

mgmtQosConfigQceTableRowEditorVlanIdRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID to be compared.
         
         If the VlanIdOp object in the same row is range(2), this object will be
         the ending VLAN ID of the range.
         
         Valid range is 0-4095.
         
         This object can only be configured if VlanIdOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 13 }

mgmtQosConfigQceTableRowEditorPcp OBJECT-TYPE
    SYNTAX      MGMTVlanTagPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value(s) to be compared.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 14 }

mgmtQosConfigQceTableRowEditorDei OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DEI value to be compared.
         
         Valid range is 0-1.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 15 }

mgmtQosConfigQceTableRowEditorInnerVlanTagType OBJECT-TYPE
    SYNTAX      MGMTVlanTagType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the inner VLAN tag type to match.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceTableRowEditor 16 }

mgmtQosConfigQceTableRowEditorInnerVlanIdOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packets's inner VLAN ID is to be compared.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceTableRowEditor 17 }

mgmtQosConfigQceTableRowEditorInnerVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The inner VLAN ID to be compared.
         
         If the InnerVlanIdOp object in the same row is range(2), this object
         will be the starting VLAN ID of the range.
         
         Valid range is 0-4095.
         
         This object can only be configured if InnerVlanIdOp is not any(0).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceTableRowEditor 18 }

mgmtQosConfigQceTableRowEditorInnerVlanIdRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The inner VLAN ID to be compared.
         
         If the InnerVlanIdOp object in the same row is range(2), this object
         will be the ending VLAN ID of the range.
         
         Valid range is 0-4095.
         
         This object can only be configured if InnerVlanIdOp is range(2).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceTableRowEditor 19 }

mgmtQosConfigQceTableRowEditorInnerPcp OBJECT-TYPE
    SYNTAX      MGMTVlanTagPriority
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The inner PCP value(s) to be compared.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceTableRowEditor 20 }

mgmtQosConfigQceTableRowEditorInnerDei OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The inner DEI value to be compared.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceInnerTag' is True."
    ::= { mgmtQosConfigQceTableRowEditor 21 }

mgmtQosConfigQceTableRowEditorFrameType OBJECT-TYPE
    SYNTAX      MGMTQosQceFrameType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packet's frame type.
         
         Modifying the frame type on an existing QCE will restore the content of
         all frame type dependent configuration to default.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 100 }

mgmtQosConfigQceTableRowEditorEtype OBJECT-TYPE
    SYNTAX      MGMTEtherType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packet's 16 bit Ethernet type field.
         
         Valid values are: 0(match any), 0x600-0xFFFF but excluding 0x800(IPv4)
         and 0x86DD(IPv6).
         
         This object can only be configured if FrameType is etype(1).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 101 }

mgmtQosConfigQceTableRowEditorLlcDsap OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packets's 8 bit LLC DSAP field.
         
         The packet's LLC DSAP field is AND-ed with the value of LlcDsapMask and
         then compared against the value of this object.
         
         If this object value and the value of LlcDsapMask is 0x00, this entry
         matches any LLC DSAP.
         
         Valid range is 0x00-0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 200 }

mgmtQosConfigQceTableRowEditorLlcDsapMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 8 bit LLC DSAP mask.
         
         Valid values are 0x00 or 0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 201 }

mgmtQosConfigQceTableRowEditorLlcSsap OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packets's 8 bit LLC SSAP field.
         
         The packet's LLC SSAP field is AND-ed with the value of LlcSsapMask and
         then compared against the value of this object.
         
         If this object value and the value of LlcSsapMask is 0x00, this entry
         matches any LLC SSAP.
         
         Valid range is 0x00-0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 202 }

mgmtQosConfigQceTableRowEditorLlcSsapMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 8 bit LLC SSAP mask.
         
         Valid values are 0x00 or 0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 203 }

mgmtQosConfigQceTableRowEditorLlcControl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packets's 8 bit LLC Control field.
         
         The packet's LLC Control field is AND-ed with the value of
         LlcControlMask and then compared against the value of this object.
         
         If this object value and the value of LlcControlMask is 0x00, this
         entry matches any LLC Control.
         
         Valid range is 0x00-0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 204 }

mgmtQosConfigQceTableRowEditorLlcControlMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 8 bit LLC Control mask.
         
         Valid values are 0x00 or 0xFF.
         
         This object can only be configured if FrameType is llc(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 205 }

mgmtQosConfigQceTableRowEditorSnapPid OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the packet's 16 bit SNAP PID field.
         
         The packet's SNAP PID field is AND-ed with the value of SnapPidMask and
         then compared against the value of this object.
         
         If this object value and the value of SnapPidMask is 0x0000, this entry
         matches any ethertype.
         
         This object can only be configured if FrameType is snap(3).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 302 }

mgmtQosConfigQceTableRowEditorSnapPidMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates the 16 bit SNAP PID mask.
         
         Valid values are 0x0000 or 0xFFFF.
         
         This object can only be configured if FrameType is snap(3).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 303 }

mgmtQosConfigQceTableRowEditorIpv4Fragment OBJECT-TYPE
    SYNTAX      MGMTBitType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how an IPv4 fragment bit is to be compared.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 400 }

mgmtQosConfigQceTableRowEditorIpv4DscpOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how an IPv4 DSCP field is to be compared.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 401 }

mgmtQosConfigQceTableRowEditorIpv4Dscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value to be compared.
         
         If the Ipv4DscpOp object in the same row is range(2), this object will
         be the starting DSCP value of the range.
         
         Valid range is 0-63.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4DscpOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 402 }

mgmtQosConfigQceTableRowEditorIpv4DscpRange OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value to be compared.
         
         If the Ipv4DscpOp object in the same row is range(2), this object will
         be the ending DSCP value of the range.
         
         Valid range is 0-63.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4DscpOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 403 }

mgmtQosConfigQceTableRowEditorIpv4Protocol OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The protocol number field in the IPv4 header used to indicate a higher
         layer protocol.
         
         The packet's protocol number field is AND-ed with the value of
         Ipv4ProtocolMask and then compared with the value of this object.
         
         If Ipv4Protocol and Ipv4protocolMask are 0, this entry matches any IPv4
         protocol.
         
         Valid range is 0-255.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 404 }

mgmtQosConfigQceTableRowEditorIpv4ProtocolMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 protocol number mask.
         
         Valid values are 0 or 255.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 405 }

mgmtQosConfigQceTableRowEditorIpv4SrcIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 source address.
         
         The packet's source address is AND-ed with the value of Ipv4SrcIpMask
         and then compared with the value of this object.
         
         If Ipv4SrcIP and Ipv4SrcIpMask are 0.0.0.0, this entry matches any IPv4
         source address.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 406 }

mgmtQosConfigQceTableRowEditorIpv4SrcIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 source address mask.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 407 }

mgmtQosConfigQceTableRowEditorIpv4DestIp OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 destination address.
         
         The packet's destination address is AND-ed with the value of
         Ipv4DestIpMask and then compared with the value of this object.
         
         If Ipv4DestIP and Ipv4DestIpMask are 0.0.0.0, this entry matches any
         IPv4 destination address.
         
         This object can only be configured if FrameType is ipv4(4).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDip' is True."
    ::= { mgmtQosConfigQceTableRowEditor 408 }

mgmtQosConfigQceTableRowEditorIpv4DestIpMask OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv4 destination address mask.
         
         This object can only be configured if FrameType is ipv4(4).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDip' is True."
    ::= { mgmtQosConfigQceTableRowEditor 409 }

mgmtQosConfigQceTableRowEditorIpv4SrcPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's source TCP/UDP port number is to be compared.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 410 }

mgmtQosConfigQceTableRowEditorIpv4SrcPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv4SrcPortOp object in the same row is range(2), this object
         will be the starting port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4SrcPortOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 411 }

mgmtQosConfigQceTableRowEditorIpv4SrcPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv4SrcPortOp object in the same row is range(2), this object
         will be the ending port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4SrcPortOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 412 }

mgmtQosConfigQceTableRowEditorIpv4DestPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination TCP/UDP port number is to be
         compared.
         
         This object can only be configured if FrameType is ipv4(4).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 413 }

mgmtQosConfigQceTableRowEditorIpv4DestPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol.
         
         If the Ipv4DestPortOp object in the same row is range(2), this object
         will be the starting port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4DestPortOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 414 }

mgmtQosConfigQceTableRowEditorIpv4DestPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv4DestPortOp object in the same row is range(2), this object
         will be the ending port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv4(4) and
         Ipv4DestPortOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 415 }

mgmtQosConfigQceTableRowEditorIpv6DscpOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how an IPv6 DSCP field is to be compared.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 500 }

mgmtQosConfigQceTableRowEditorIpv6Dscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value to be compared.
         
         If the Ipv6DscpOp object in the same row is range(2), this object will
         be the starting DSCP value of the range.
         
         Valid range is 0-63.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6DscpOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 501 }

mgmtQosConfigQceTableRowEditorIpv6DscpRange OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value to be compared.
         
         If the Ipv6DscpOp object in the same row is range(2), this object will
         be the ending DSCP value of the range.
         
         Valid range is 0-63.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6DscpOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 502 }

mgmtQosConfigQceTableRowEditorIpv6Protocol OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The protocol number field in the IPv6 header used to indicate a higher
         layer protocol.
         
         The packet's protocol number field is AND-ed with the value of
         Ipv6ProtocolMask and then compared with the value of this object.
         
         If Ipv46rotocol and Ipv6protocolMask are 0, this entry matches any IPv6
         protocol.
         
         Valid range is 0-255.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 503 }

mgmtQosConfigQceTableRowEditorIpv6ProtocolMask OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 protocol number mask.
         
         Valid values are 0 or 255.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 504 }

mgmtQosConfigQceTableRowEditorIpv6SrcIp OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 source address.
         
         The packet's source address is AND-ed with the value of Ipv6SrcIpMask
         and then compared with the value of this object.
         
         If Ipv6SrcIP and Ipv6SrcIpMask are all zeros, this entry matches any
         IPv6 source address.
         
         Only the least significant 32 bits of this object are used.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 506 }

mgmtQosConfigQceTableRowEditorIpv6SrcIpMask OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 source address mask.
         
         Only the least significant 32 bits of this object are used.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 508 }

mgmtQosConfigQceTableRowEditorIpv6DestIp OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 destination address.
         
         The packet's destination address is AND-ed with the value of
         Ipv6DestIpMask and then compared with the value of this object.
         
         If Ipv6DestIP and Ipv6DestIpMask are all zeros, this entry matches any
         IPv6 destination address.
         
         Only the least significant 32 bits of this object are used.
         
         This object can only be configured if FrameType is ipv6(5).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDip' is True."
    ::= { mgmtQosConfigQceTableRowEditor 510 }

mgmtQosConfigQceTableRowEditorIpv6DestIpMask OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The specified IPv6 destination address mask.
         
         Only the least significant 32 bits of this object are used.
         
         This object can only be configured if FrameType is ipv6(5).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceDip' is True."
    ::= { mgmtQosConfigQceTableRowEditor 512 }

mgmtQosConfigQceTableRowEditorIpv6SrcPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's source TCP/UDP port number is to be compared.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 513 }

mgmtQosConfigQceTableRowEditorIpv6SrcPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv6SrcPortOp object in the same row is range(2), this object
         will be the starting port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6SrcPortOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 514 }

mgmtQosConfigQceTableRowEditorIpv6SrcPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv6SrcPortOp object in the same row is range(2), this object
         will be the ending port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6SrcPortOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 515 }

mgmtQosConfigQceTableRowEditorIpv6DestPortOp OBJECT-TYPE
    SYNTAX      MGMTASRType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Indicates how a packet's destination TCP/UDP port number is to be
         compared.
         
         This object can only be configured if FrameType is ipv6(5).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 516 }

mgmtQosConfigQceTableRowEditorIpv6DestPort OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The destination port number of the TCP or UDP protocol.
         
         If the Ipv6DestPortOp object in the same row is range(2), this object
         will be the starting port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6DestPortOp is not any(0).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 517 }

mgmtQosConfigQceTableRowEditorIpv6DestPortRange OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The source port number of the TCP or UDP protocol.
         
         If the Ipv6DestPortOp object in the same row is range(2), this object
         will be the ending port number of the port range.
         
         Valid range is 0-65535.
         
         This object can only be configured if FrameType is ipv6(5) and
         Ipv6DestPortOp is range(2).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 518 }

mgmtQosConfigQceTableRowEditorActionCosEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the CoS value in ActionCos.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1000 }

mgmtQosConfigQceTableRowEditorActionCos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The CoS value used for classification.
         
         Valid range is qosCapabilitiesClassMin-qosCapabilitiesClassMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1001 }

mgmtQosConfigQceTableRowEditorActionDplEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the DPL value in ActionDpl.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1002 }

mgmtQosConfigQceTableRowEditorActionDpl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DPL value used for classification.
         
         Valid range is qosCapabilitiesDplMin-qosCapabilitiesDplMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1003 }

mgmtQosConfigQceTableRowEditorActionDscpEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the DSCP value in ActionDscp.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1004 }

mgmtQosConfigQceTableRowEditorActionDscp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DSCP value used for classification.
         
         Valid range is 0-63.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1005 }

mgmtQosConfigQceTableRowEditorActionPcpDeiEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the PCP value in ActionPcp and the
         DEI value in ActionDei.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPcpDei' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1006 }

mgmtQosConfigQceTableRowEditorActionPcp OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The PCP value used for classification.
         
         Valid range is 0-7.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPcpDei' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1007 }

mgmtQosConfigQceTableRowEditorActionDei OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The DEI value used for classification.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPcpDei' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1008 }

mgmtQosConfigQceTableRowEditorActionPolicyEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified to the policy number in ActionPolicy.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPolicy' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1009 }

mgmtQosConfigQceTableRowEditorActionPolicy OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The policy number used for classification.
         
         Valid range is documented in the ACL MIB.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionPolicy' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1010 }

mgmtQosConfigQceTableRowEditorActionMapEnable OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, the packet is classified by the Ingress Map Id in ActionMap.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionMap' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1011 }

mgmtQosConfigQceTableRowEditorActionMap OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Id of an Ingress Map used for classification.
         
         Valid range is
         qosCapabilitiesIngressMapIdMin-qosCapabilitiesIngressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQceActionMap' is True."
    ::= { mgmtQosConfigQceTableRowEditor 1012 }

mgmtQosConfigQceTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQceTableRowEditor 10000 }

mgmtQosConfigQcePrecedenceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigQcePrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the precedence of QCEs ordered by their position in
         the QCL.
         
         This is an optional table and is only present if qosCapabilitiesHasQce
         is true."
    ::= { mgmtQosConfigQce 3 }

mgmtQosConfigQcePrecedenceEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigQcePrecedenceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the precedence of a single QCE."
    INDEX       { mgmtQosConfigQcePrecedenceIndex }
    ::= { mgmtQosConfigQcePrecedenceTable 1 }

MGMTQosConfigQcePrecedenceEntry ::= SEQUENCE {
    mgmtQosConfigQcePrecedenceIndex  Integer32,
    mgmtQosConfigQcePrecedenceQceId  Unsigned32
}

mgmtQosConfigQcePrecedenceIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "QCE precedence index. Indicates the position in QCL.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQcePrecedenceEntry 1 }

mgmtQosConfigQcePrecedenceQceId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "QCE Id.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosConfigQcePrecedenceEntry 2 }

mgmtQosConfigIngressMap OBJECT IDENTIFIER
    ::= { mgmtQosConfig 4 }

mgmtQosConfigIngressMapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigIngressMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the configuration of QoS Ingress Maps. The index is
         IngressMapId.
         
         This is an optional table and is only present if
         qosCapabilitiesHasIngressMap is true."
    ::= { mgmtQosConfigIngressMap 1 }

mgmtQosConfigIngressMapEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigIngressMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration of Key and Action values for a
         single QoS Ingress Map."
    INDEX       { mgmtQosConfigIngressMapIngressMapId }
    ::= { mgmtQosConfigIngressMapTable 1 }

MGMTQosConfigIngressMapEntry ::= SEQUENCE {
    mgmtQosConfigIngressMapIngressMapId  Integer32,
    mgmtQosConfigIngressMapKey           MGMTQosIngressMapKey,
    mgmtQosConfigIngressMapActionCos     TruthValue,
    mgmtQosConfigIngressMapActionDpl     TruthValue,
    mgmtQosConfigIngressMapActionPcp     TruthValue,
    mgmtQosConfigIngressMapActionDei     TruthValue,
    mgmtQosConfigIngressMapActionDscp    TruthValue,
    mgmtQosConfigIngressMapActionCosid   TruthValue,
    mgmtQosConfigIngressMapActionPath    TruthValue,
    mgmtQosConfigIngressMapAction        MGMTRowEditorState
}

mgmtQosConfigIngressMapIngressMapId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Ingress Map Id.
         
         Valid range is
         qosCapabilitiesIngressMapIdMin-qosCapabilitiesIngressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 1 }

mgmtQosConfigIngressMapKey OBJECT-TYPE
    SYNTAX      MGMTQosIngressMapKey
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "An integer that indicates the key to use when matching frames in the
         ingress map. If the value is pcp(0), use classified PCP value as key
         (default).
         
         If the value is pcpDei(1), use classified PCP and DEI values as key.
         
         If the value is dscp(2), use the frame's DSCP value as key. For non-IP
         frames, no mapping is done.
         
         If the value is dscpPcpDei(3), use the frame's DSCP value as key. For
         non-IP frames, use classified PCP and DEI values as key.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 2 }

mgmtQosConfigIngressMapActionCos OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of CoS.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 3 }

mgmtQosConfigIngressMapActionDpl OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of DPL.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 4 }

mgmtQosConfigIngressMapActionPcp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of PCP.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 5 }

mgmtQosConfigIngressMapActionDei OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of DEI.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 6 }

mgmtQosConfigIngressMapActionDscp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of DSCP.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 7 }

mgmtQosConfigIngressMapActionCosid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of COSID.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 8 }

mgmtQosConfigIngressMapActionPath OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of PATH-COSID (obsolete).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 9 }

mgmtQosConfigIngressMapAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapEntry 10000 }

mgmtQosConfigIngressMapTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtQosConfigIngressMap 2 }

mgmtQosConfigIngressMapTableRowEditorIngressMapId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Ingress Map Id.
         
         Valid range is
         qosCapabilitiesIngressMapIdMin-qosCapabilitiesIngressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 1 }

mgmtQosConfigIngressMapTableRowEditorKey OBJECT-TYPE
    SYNTAX      MGMTQosIngressMapKey
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "An integer that indicates the key to use when matching frames in the
         ingress map. If the value is pcp(0), use classified PCP value as key
         (default).
         
         If the value is pcpDei(1), use classified PCP and DEI values as key.
         
         If the value is dscp(2), use the frame's DSCP value as key. For non-IP
         frames, no mapping is done.
         
         If the value is dscpPcpDei(3), use the frame's DSCP value as key. For
         non-IP frames, use classified PCP and DEI values as key.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 2 }

mgmtQosConfigIngressMapTableRowEditorActionCos OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of CoS.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 3 }

mgmtQosConfigIngressMapTableRowEditorActionDpl OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of DPL.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 4 }

mgmtQosConfigIngressMapTableRowEditorActionPcp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of PCP.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 5 }

mgmtQosConfigIngressMapTableRowEditorActionDei OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of DEI.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 6 }

mgmtQosConfigIngressMapTableRowEditorActionDscp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of DSCP.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 7 }

mgmtQosConfigIngressMapTableRowEditorActionCosid OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of COSID.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 8 }

mgmtQosConfigIngressMapTableRowEditorActionPath OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable classification of PATH-COSID (obsolete).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 9 }

mgmtQosConfigIngressMapTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapTableRowEditor 10000 }

mgmtQosConfigIngressMapPcpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigIngressMapPcpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the configuration of QoS Ingress Maps entries.
         
         This is an optional table and is only present if
         qosCapabilitiesHasIngressMap is true."
    ::= { mgmtQosConfigIngressMap 3 }

mgmtQosConfigIngressMapPcpEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigIngressMapPcpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration of a single entry in a QoS Ingress
         Map with PCP and DEI as index."
    INDEX       { mgmtQosConfigIngressMapPcpIngressMapId,
                  mgmtQosConfigIngressMapPcpPcp,
                  mgmtQosConfigIngressMapPcpDei }
    ::= { mgmtQosConfigIngressMapPcpTable 1 }

MGMTQosConfigIngressMapPcpEntry ::= SEQUENCE {
    mgmtQosConfigIngressMapPcpIngressMapId  Integer32,
    mgmtQosConfigIngressMapPcpPcp           Integer32,
    mgmtQosConfigIngressMapPcpDei           Integer32,
    mgmtQosConfigIngressMapPcpToCos         MGMTUnsigned8,
    mgmtQosConfigIngressMapPcpToDpl         MGMTUnsigned8,
    mgmtQosConfigIngressMapPcpToPcp         MGMTUnsigned8,
    mgmtQosConfigIngressMapPcpToDei         MGMTUnsigned8,
    mgmtQosConfigIngressMapPcpToDscp        Dscp,
    mgmtQosConfigIngressMapPcpToCosid       MGMTUnsigned8,
    mgmtQosConfigIngressMapPcpToPathCosid   MGMTUnsigned8
}

mgmtQosConfigIngressMapPcpIngressMapId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Ingress Map Id.
         
         Valid range is
         qosCapabilitiesIngressMapIdMin-qosCapabilitiesIngressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 1 }

mgmtQosConfigIngressMapPcpPcp OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "PCP index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 2 }

mgmtQosConfigIngressMapPcpDei OBJECT-TYPE
    SYNTAX      Integer32 (0..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DEI index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 3 }

mgmtQosConfigIngressMapPcpToCos OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped CoS value.
         
         Valid range is qosCapabilitiesClassMin-qosCapabilitiesClassMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 4 }

mgmtQosConfigIngressMapPcpToDpl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DPL (Drop Precedence Level) value.
         
         Valid range is qosCapabilitiesDplMin-qosCapabilitiesDplMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 5 }

mgmtQosConfigIngressMapPcpToPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped PCP value.
         
         Valid range is 0-7.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 6 }

mgmtQosConfigIngressMapPcpToDei OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DEI value.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 7 }

mgmtQosConfigIngressMapPcpToDscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DSCP value.
         
         Valid range is 0-63.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 8 }

mgmtQosConfigIngressMapPcpToCosid OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped COSID value.
         
         Valid range is qosCapabilitiesCosIdMin-qosCapabilitiesCosIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 9 }

mgmtQosConfigIngressMapPcpToPathCosid OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped PATH-COSID value.
         
         Valid range is qosCapabilitiesCosIdMin-qosCapabilitiesCosIdMax
         (obsolete).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapPcpEntry 10 }

mgmtQosConfigIngressMapDscpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigIngressMapDscpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the configuration of QoS Ingress Maps entries.
         
         This is an optional table and is only present if
         qosCapabilitiesHasIngressMap is true."
    ::= { mgmtQosConfigIngressMap 4 }

mgmtQosConfigIngressMapDscpEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigIngressMapDscpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration of a single entry in a QoS Ingress
         Map with DSCP as index."
    INDEX       { mgmtQosConfigIngressMapDscpIngressMapId,
                  mgmtQosConfigIngressMapDscpDscp }
    ::= { mgmtQosConfigIngressMapDscpTable 1 }

MGMTQosConfigIngressMapDscpEntry ::= SEQUENCE {
    mgmtQosConfigIngressMapDscpIngressMapId  Integer32,
    mgmtQosConfigIngressMapDscpDscp          Dscp,
    mgmtQosConfigIngressMapDscpToCos         MGMTUnsigned8,
    mgmtQosConfigIngressMapDscpToDpl         MGMTUnsigned8,
    mgmtQosConfigIngressMapDscpToPcp         MGMTUnsigned8,
    mgmtQosConfigIngressMapDscpToDei         MGMTUnsigned8,
    mgmtQosConfigIngressMapDscpToDscp        Dscp,
    mgmtQosConfigIngressMapDscpToCosid       MGMTUnsigned8,
    mgmtQosConfigIngressMapDscpToPathCosid   MGMTUnsigned8
}

mgmtQosConfigIngressMapDscpIngressMapId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Ingress Map Id.
         
         Valid range is
         qosCapabilitiesIngressMapIdMin-qosCapabilitiesIngressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapDscpEntry 1 }

mgmtQosConfigIngressMapDscpDscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DSCP index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapDscpEntry 2 }

mgmtQosConfigIngressMapDscpToCos OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped CoS value.
         
         Valid range is qosCapabilitiesClassMin-qosCapabilitiesClassMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapDscpEntry 3 }

mgmtQosConfigIngressMapDscpToDpl OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DPL (Drop Precedence Level) value.
         
         Valid range is qosCapabilitiesDplMin-qosCapabilitiesDplMax.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapDscpEntry 4 }

mgmtQosConfigIngressMapDscpToPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped PCP value.
         
         Valid range is 0-7.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapDscpEntry 5 }

mgmtQosConfigIngressMapDscpToDei OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DEI value.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapDscpEntry 6 }

mgmtQosConfigIngressMapDscpToDscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DSCP value.
         
         Valid range is 0-63.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapDscpEntry 7 }

mgmtQosConfigIngressMapDscpToCosid OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped COSID value.
         
         Valid range is qosCapabilitiesCosIdMin-qosCapabilitiesCosIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapDscpEntry 8 }

mgmtQosConfigIngressMapDscpToPathCosid OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped PATH-COSID value.
         
         Valid range is qosCapabilitiesCosIdMin-qosCapabilitiesCosIdMax
         (obsolete).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasIngressMap' is True."
    ::= { mgmtQosConfigIngressMapDscpEntry 9 }

mgmtQosConfigEgressMap OBJECT IDENTIFIER
    ::= { mgmtQosConfig 5 }

mgmtQosConfigEgressMapTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigEgressMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the configuration of Egress Maps. The index is
         EgressMapId.
         
         This is an optional table and is only present if
         qosCapabilitiesHasEgressMap is true."
    ::= { mgmtQosConfigEgressMap 1 }

mgmtQosConfigEgressMapEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigEgressMapEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration of key and Action values for a
         single Egress Map."
    INDEX       { mgmtQosConfigEgressMapEgressMapId }
    ::= { mgmtQosConfigEgressMapTable 1 }

MGMTQosConfigEgressMapEntry ::= SEQUENCE {
    mgmtQosConfigEgressMapEgressMapId  Integer32,
    mgmtQosConfigEgressMapKey          MGMTQosEgressMapKey,
    mgmtQosConfigEgressMapActionPcp    TruthValue,
    mgmtQosConfigEgressMapActionDei    TruthValue,
    mgmtQosConfigEgressMapActionDscp   TruthValue,
    mgmtQosConfigEgressMapActionPath   TruthValue,
    mgmtQosConfigEgressMapAction       MGMTRowEditorState
}

mgmtQosConfigEgressMapEgressMapId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Egress Map Id.
         
         Valid range is
         qosCapabilitiesEgressMapIdMin-qosCapabilitiesEgressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapEntry 1 }

mgmtQosConfigEgressMapKey OBJECT-TYPE
    SYNTAX      MGMTQosEgressMapKey
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "An integer that indicates the key to use when matching frames in the
         egress map.
         
         If the value is cosid(0), use classified COSID.
         
         If the value is cosidDpl(1), use classified COSID and DPL.
         
         If the value is dscp(2), use classified DSCP.
         
         If the value is dscpDpl(3), use classified DSCP and DPL.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapEntry 2 }

mgmtQosConfigEgressMapActionPcp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable rewriting of PCP.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapEntry 3 }

mgmtQosConfigEgressMapActionDei OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable rewriting of DEI.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapEntry 4 }

mgmtQosConfigEgressMapActionDscp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable rewriting of DSCP.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapEntry 5 }

mgmtQosConfigEgressMapActionPath OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable rewriting of PATH-COSID (obsolete).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapEntry 6 }

mgmtQosConfigEgressMapAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapEntry 10000 }

mgmtQosConfigEgressMapTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtQosConfigEgressMap 2 }

mgmtQosConfigEgressMapTableRowEditorEgressMapId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Egress Map Id.
         
         Valid range is
         qosCapabilitiesEgressMapIdMin-qosCapabilitiesEgressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapTableRowEditor 1 }

mgmtQosConfigEgressMapTableRowEditorKey OBJECT-TYPE
    SYNTAX      MGMTQosEgressMapKey
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "An integer that indicates the key to use when matching frames in the
         egress map.
         
         If the value is cosid(0), use classified COSID.
         
         If the value is cosidDpl(1), use classified COSID and DPL.
         
         If the value is dscp(2), use classified DSCP.
         
         If the value is dscpDpl(3), use classified DSCP and DPL.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapTableRowEditor 2 }

mgmtQosConfigEgressMapTableRowEditorActionPcp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable rewriting of PCP.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapTableRowEditor 3 }

mgmtQosConfigEgressMapTableRowEditorActionDei OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable rewriting of DEI.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapTableRowEditor 4 }

mgmtQosConfigEgressMapTableRowEditorActionDscp OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable rewriting of DSCP.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapTableRowEditor 5 }

mgmtQosConfigEgressMapTableRowEditorActionPath OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If true, enable rewriting of PATH-COSID (obsolete).
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapTableRowEditor 6 }

mgmtQosConfigEgressMapTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapTableRowEditor 10000 }

mgmtQosConfigEgressMapCosidTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigEgressMapCosidEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the configuration of QoS Egress Maps entries.
         
         This is an optional table and is only present if
         qosCapabilitiesHasEgressMap is true."
    ::= { mgmtQosConfigEgressMap 3 }

mgmtQosConfigEgressMapCosidEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigEgressMapCosidEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration of a single entry in a QoS Egress
         Map with COSID and DPL as index."
    INDEX       { mgmtQosConfigEgressMapCosidEgressMapId,
                  mgmtQosConfigEgressMapCosidCosid,
                  mgmtQosConfigEgressMapCosidDpl }
    ::= { mgmtQosConfigEgressMapCosidTable 1 }

MGMTQosConfigEgressMapCosidEntry ::= SEQUENCE {
    mgmtQosConfigEgressMapCosidEgressMapId  Integer32,
    mgmtQosConfigEgressMapCosidCosid        Integer32,
    mgmtQosConfigEgressMapCosidDpl          Integer32,
    mgmtQosConfigEgressMapCosidToPcp        MGMTUnsigned8,
    mgmtQosConfigEgressMapCosidToDei        MGMTUnsigned8,
    mgmtQosConfigEgressMapCosidToDscp       Dscp,
    mgmtQosConfigEgressMapCosidToPathCosid  MGMTUnsigned8
}

mgmtQosConfigEgressMapCosidEgressMapId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Egress Map Id.
         
         Valid range is
         qosCapabilitiesEgressMapIdMin-qosCapabilitiesEgressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapCosidEntry 1 }

mgmtQosConfigEgressMapCosidCosid OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "COSID index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapCosidEntry 2 }

mgmtQosConfigEgressMapCosidDpl OBJECT-TYPE
    SYNTAX      Integer32 (0..3)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DPL index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapCosidEntry 3 }

mgmtQosConfigEgressMapCosidToPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped PCP value.
         
         Valid range is 0-7.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapCosidEntry 4 }

mgmtQosConfigEgressMapCosidToDei OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DEI value.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapCosidEntry 5 }

mgmtQosConfigEgressMapCosidToDscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DSCP value.
         
         Valid range is 0-63.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapCosidEntry 6 }

mgmtQosConfigEgressMapCosidToPathCosid OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped PATH-COSID value.
         
         Valid range is qosCapabilitiesCosIdMin-qosCapabilitiesCosIdMax
         (obsolete).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapCosidEntry 7 }

mgmtQosConfigEgressMapDscpTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosConfigEgressMapDscpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the configuration of QoS Egress Maps entries.
         
         This is an optional table and is only present if
         qosCapabilitiesHasEgressMap is true."
    ::= { mgmtQosConfigEgressMap 4 }

mgmtQosConfigEgressMapDscpEntry OBJECT-TYPE
    SYNTAX      MGMTQosConfigEgressMapDscpEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the configuration of a single entry in a QoS Egress
         Map with DSCP and DPL as index."
    INDEX       { mgmtQosConfigEgressMapDscpEgressMapId,
                  mgmtQosConfigEgressMapDscpDscp,
                  mgmtQosConfigEgressMapDscpDpl }
    ::= { mgmtQosConfigEgressMapDscpTable 1 }

MGMTQosConfigEgressMapDscpEntry ::= SEQUENCE {
    mgmtQosConfigEgressMapDscpEgressMapId  Integer32,
    mgmtQosConfigEgressMapDscpDscp         Dscp,
    mgmtQosConfigEgressMapDscpDpl          Integer32,
    mgmtQosConfigEgressMapDscpToPcp        MGMTUnsigned8,
    mgmtQosConfigEgressMapDscpToDei        MGMTUnsigned8,
    mgmtQosConfigEgressMapDscpToDscp       Dscp,
    mgmtQosConfigEgressMapDscpToPathCosid  MGMTUnsigned8
}

mgmtQosConfigEgressMapDscpEgressMapId OBJECT-TYPE
    SYNTAX      Integer32 (0..2147483647)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Egress Map Id.
         
         Valid range is
         qosCapabilitiesEgressMapIdMin-qosCapabilitiesEgressMapIdMax.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapDscpEntry 1 }

mgmtQosConfigEgressMapDscpDscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DSCP index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapDscpEntry 2 }

mgmtQosConfigEgressMapDscpDpl OBJECT-TYPE
    SYNTAX      Integer32 (0..3)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "DPL index.
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapDscpEntry 3 }

mgmtQosConfigEgressMapDscpToPcp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped PCP value.
         
         Valid range is 0-7.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapDscpEntry 4 }

mgmtQosConfigEgressMapDscpToDei OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DEI value.
         
         Valid range is 0-1.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapDscpEntry 5 }

mgmtQosConfigEgressMapDscpToDscp OBJECT-TYPE
    SYNTAX      Dscp
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped DSCP value.
         
         Valid range is 0-63.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapDscpEntry 6 }

mgmtQosConfigEgressMapDscpToPathCosid OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setup the mapped PATH-COSID value.
         
         Valid range is qosCapabilitiesCosIdMin-qosCapabilitiesCosIdMax
         (obsolete).
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasEgressMap' is True."
    ::= { mgmtQosConfigEgressMapDscpEntry 7 }

mgmtQosStatus OBJECT IDENTIFIER
    ::= { mgmtQosMibObjects 3 }

mgmtQosStatusInterface OBJECT IDENTIFIER
    ::= { mgmtQosStatus 2 }

mgmtQosStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides QoS status for QoS manageable interfaces"
    ::= { mgmtQosStatusInterface 1 }

mgmtQosStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTQosStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the status for a specific interface"
    INDEX       { mgmtQosStatusInterfaceIfIndex }
    ::= { mgmtQosStatusInterfaceTable 1 }

MGMTQosStatusInterfaceEntry ::= SEQUENCE {
    mgmtQosStatusInterfaceIfIndex  MGMTInterfaceIndex,
    mgmtQosStatusInterfaceCos      Unsigned32
}

mgmtQosStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtQosStatusInterfaceEntry 1 }

mgmtQosStatusInterfaceCos OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Some subsystems are allowed to modify the default CoS value for an
         interface. This object shows the actual default CoS value a packet is
         classified to."
    ::= { mgmtQosStatusInterfaceEntry 2 }

mgmtQosStatusInterfaceSchedulerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTQosStatusInterfaceSchedulerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the mapping of (interface, queue) to weight values.
         
         The mappings given by this table are the 'real' weights in percent as
         used by the hardware."
    ::= { mgmtQosStatusInterface 2 }

mgmtQosStatusInterfaceSchedulerEntry OBJECT-TYPE
    SYNTAX      MGMTQosStatusInterfaceSchedulerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the scheduler status for a specific queue."
    INDEX       { mgmtQosStatusInterfaceSchedulerIfIndex,
                  mgmtQosStatusInterfaceSchedulerQueue }
    ::= { mgmtQosStatusInterfaceSchedulerTable 1 }

MGMTQosStatusInterfaceSchedulerEntry ::= SEQUENCE {
    mgmtQosStatusInterfaceSchedulerIfIndex  MGMTInterfaceIndex,
    mgmtQosStatusInterfaceSchedulerQueue    Integer32,
    mgmtQosStatusInterfaceSchedulerWeight   MGMTPercent
}

mgmtQosStatusInterfaceSchedulerIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface index."
    ::= { mgmtQosStatusInterfaceSchedulerEntry 1 }

mgmtQosStatusInterfaceSchedulerQueue OBJECT-TYPE
    SYNTAX      Integer32 (0..7)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Queue index."
    ::= { mgmtQosStatusInterfaceSchedulerEntry 2 }

mgmtQosStatusInterfaceSchedulerWeight OBJECT-TYPE
    SYNTAX      MGMTPercent
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The actual weight for this queue."
    ::= { mgmtQosStatusInterfaceSchedulerEntry 3 }

mgmtQosStatusQce OBJECT IDENTIFIER
    ::= { mgmtQosStatus 3 }

mgmtQosControl OBJECT IDENTIFIER
    ::= { mgmtQosMibObjects 4 }

mgmtQosControlQce OBJECT IDENTIFIER
    ::= { mgmtQosControl 3 }

mgmtQosControlQceConflictResolve OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to resolve resource conflicts.
         
         If different components competes for the same resources in the switch,
         it is possible that some of resources needed by one or more QCE are not
         available.
         
         These conflicts, if any, are shown in the qosStatusQceTable.
         
         To solve these conflicts, you must delete one or more of the competing
         settings and set ConflictResolve to true. The switch will then reapply
         the current QCE configuration.
         
         
         
         This object is only available if the capability object
         'mgmtQosCapabilitiesHasQce' is True."
    ::= { mgmtQosControlQce 1 }

mgmtQosMibConformance OBJECT IDENTIFIER
    ::= { mgmtQosMib 2 }

mgmtQosMibCompliances OBJECT IDENTIFIER
    ::= { mgmtQosMibConformance 1 }

mgmtQosMibGroups OBJECT IDENTIFIER
    ::= { mgmtQosMibConformance 2 }

mgmtQosCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosCapabilitiesClassMin,
                  mgmtQosCapabilitiesClassMax,
                  mgmtQosCapabilitiesDplMin,
                  mgmtQosCapabilitiesDplMax,
                  mgmtQosCapabilitiesWredGroupMin,
                  mgmtQosCapabilitiesWredGroupMax,
                  mgmtQosCapabilitiesWredDplMin,
                  mgmtQosCapabilitiesWredDplMax,
                  mgmtQosCapabilitiesQceIdMin,
                  mgmtQosCapabilitiesQceIdMax,
                  mgmtQosCapabilitiesPortPolicerBitRateMin,
                  mgmtQosCapabilitiesPortPolicerBitRateMax,
                  mgmtQosCapabilitiesPortPolicerBitBurstMin,
                  mgmtQosCapabilitiesPortPolicerBitBurstMax,
                  mgmtQosCapabilitiesPortPolicerFrameRateMin,
                  mgmtQosCapabilitiesPortPolicerFrameRateMax,
                  mgmtQosCapabilitiesPortPolicerFrameBurstMin,
                  mgmtQosCapabilitiesPortPolicerFrameBurstMax,
                  mgmtQosCapabilitiesQueuePolicerBitRateMin,
                  mgmtQosCapabilitiesQueuePolicerBitRateMax,
                  mgmtQosCapabilitiesQueuePolicerBitBurstMin,
                  mgmtQosCapabilitiesQueuePolicerBitBurstMax,
                  mgmtQosCapabilitiesQueuePolicerFrameRateMin,
                  mgmtQosCapabilitiesQueuePolicerFrameRateMax,
                  mgmtQosCapabilitiesQueuePolicerFrameBurstMin,
                  mgmtQosCapabilitiesQueuePolicerFrameBurstMax,
                  mgmtQosCapabilitiesPortShaperBitRateMin,
                  mgmtQosCapabilitiesPortShaperBitRateMax,
                  mgmtQosCapabilitiesPortShaperBitBurstMin,
                  mgmtQosCapabilitiesPortShaperBitBurstMax,
                  mgmtQosCapabilitiesPortShaperFrameRateMin,
                  mgmtQosCapabilitiesPortShaperFrameRateMax,
                  mgmtQosCapabilitiesPortShaperFrameBurstMin,
                  mgmtQosCapabilitiesPortShaperFrameBurstMax,
                  mgmtQosCapabilitiesQueueShaperBitRateMin,
                  mgmtQosCapabilitiesQueueShaperBitRateMax,
                  mgmtQosCapabilitiesQueueShaperBitBurstMin,
                  mgmtQosCapabilitiesQueueShaperBitBurstMax,
                  mgmtQosCapabilitiesQueueShaperFrameRateMin,
                  mgmtQosCapabilitiesQueueShaperFrameRateMax,
                  mgmtQosCapabilitiesQueueShaperFrameBurstMin,
                  mgmtQosCapabilitiesQueueShaperFrameBurstMax,
                  mgmtQosCapabilitiesGlobalStormBitRateMin,
                  mgmtQosCapabilitiesGlobalStormBitRateMax,
                  mgmtQosCapabilitiesGlobalStormBitBurstMin,
                  mgmtQosCapabilitiesGlobalStormBitBurstMax,
                  mgmtQosCapabilitiesGlobalStormFrameRateMin,
                  mgmtQosCapabilitiesGlobalStormFrameRateMax,
                  mgmtQosCapabilitiesGlobalStormFrameBurstMin,
                  mgmtQosCapabilitiesGlobalStormFrameBurstMax,
                  mgmtQosCapabilitiesPortStormBitRateMin,
                  mgmtQosCapabilitiesPortStormBitRateMax,
                  mgmtQosCapabilitiesPortStormBitBurstMin,
                  mgmtQosCapabilitiesPortStormBitBurstMax,
                  mgmtQosCapabilitiesPortStormFrameRateMin,
                  mgmtQosCapabilitiesPortStormFrameRateMax,
                  mgmtQosCapabilitiesPortStormFrameBurstMin,
                  mgmtQosCapabilitiesPortStormFrameBurstMax,
                  mgmtQosCapabilitiesIngressMapIdMin,
                  mgmtQosCapabilitiesIngressMapIdMax,
                  mgmtQosCapabilitiesEgressMapIdMin,
                  mgmtQosCapabilitiesEgressMapIdMax,
                  mgmtQosCapabilitiesCosIdMin,
                  mgmtQosCapabilitiesCosIdMax,
                  mgmtQosCapabilitiesQbvGclLenMax,
                  mgmtQosCapabilitiesDwrrCountMask,
                  mgmtQosCapabilitiesHasGlobalStormPolicers,
                  mgmtQosCapabilitiesHasPortStormPolicers,
                  mgmtQosCapabilitiesHasPortQueuePolicers,
                  mgmtQosCapabilitiesHasWredV1,
                  mgmtQosCapabilitiesHasWredV2,
                  mgmtQosCapabilitiesHasFixedTagCosMap,
                  mgmtQosCapabilitiesHasTagClassification,
                  mgmtQosCapabilitiesHasTagRemarking,
                  mgmtQosCapabilitiesHasDscp,
                  mgmtQosCapabilitiesHasDscpDplClassification,
                  mgmtQosCapabilitiesHasDscpDplRemarking,
                  mgmtQosCapabilitiesHasPortPolicersFc,
                  mgmtQosCapabilitiesHasQueuePolicersFc,
                  mgmtQosCapabilitiesHasPortShapersDlb,
                  mgmtQosCapabilitiesHasQueueShapersDlb,
                  mgmtQosCapabilitiesHasQueueShapersExcess,
                  mgmtQosCapabilitiesHasWredV3,
                  mgmtQosCapabilitiesHasQueueShapersCredit,
                  mgmtQosCapabilitiesHasQueueCutThrough,
                  mgmtQosCapabilitiesHasQueueFramePreemption,
                  mgmtQosCapabilitiesHasQce,
                  mgmtQosCapabilitiesHasQceAddressMode,
                  mgmtQosCapabilitiesHasQceKeyType,
                  mgmtQosCapabilitiesHasQceMacOui,
                  mgmtQosCapabilitiesHasQceDmac,
                  mgmtQosCapabilitiesHasQceDip,
                  mgmtQosCapabilitiesHasQceCTag,
                  mgmtQosCapabilitiesHasQceSTag,
                  mgmtQosCapabilitiesHasQceInnerTag,
                  mgmtQosCapabilitiesHasQceActionPcpDei,
                  mgmtQosCapabilitiesHasQceActionPolicy,
                  mgmtQosCapabilitiesHasShapersRt,
                  mgmtQosCapabilitiesHasQceActionMap,
                  mgmtQosCapabilitiesHasIngressMap,
                  mgmtQosCapabilitiesHasEgressMap,
                  mgmtQosCapabilitiesHasWred2orWredw3,
                  mgmtQosCapabilitiesHasDscpDp2,
                  mgmtQosCapabilitiesHasDscpDp3,
                  mgmtQosCapabilitiesHasDefaultPcpAndDei,
                  mgmtQosCapabilitiesHasTrustTag,
                  mgmtQosCapabilitiesHasCosIdClassification,
                  mgmtQosCapabilitiesHasQbv,
                  mgmtQosCapabilitiesHasWred,
                  mgmtQosCapabilitiesHasPsfp,
                  mgmtQosCapabilitiesHasTc }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 1 }

mgmtQosConfigGlobalsStormPolicersUnicastInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigGlobalsStormPolicersUnicastEnable,
                  mgmtQosConfigGlobalsStormPolicersUnicastRate,
                  mgmtQosConfigGlobalsStormPolicersUnicastFrameRate }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 2 }

mgmtQosConfigGlobalsStormPolicersMulticastInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigGlobalsStormPolicersMulticastEnable,
                  mgmtQosConfigGlobalsStormPolicersMulticastRate,
                  mgmtQosConfigGlobalsStormPolicersMulticastFrameRate }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 3 }

mgmtQosConfigGlobalsStormPolicersBroadcastInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigGlobalsStormPolicersBroadcastEnable,
                  mgmtQosConfigGlobalsStormPolicersBroadcastRate,
                  mgmtQosConfigGlobalsStormPolicersBroadcastFrameRate }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 4 }

mgmtQosConfigGlobalsWredTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigGlobalsWredGroup,
                  mgmtQosConfigGlobalsWredQueue,
                  mgmtQosConfigGlobalsWredDpl,
                  mgmtQosConfigGlobalsWredEnable,
                  mgmtQosConfigGlobalsWredMinimumFillLevel,
                  mgmtQosConfigGlobalsWredMaximum,
                  mgmtQosConfigGlobalsWredMaxSelector,
                  mgmtQosConfigGlobalsWredMaximumDp1,
                  mgmtQosConfigGlobalsWredMaximumDp2,
                  mgmtQosConfigGlobalsWredMaximumDp3 }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 5 }

mgmtQosConfigGlobalsDscpTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigGlobalsDscpDscp,
                  mgmtQosConfigGlobalsDscpTrust,
                  mgmtQosConfigGlobalsDscpCos,
                  mgmtQosConfigGlobalsDscpDpl,
                  mgmtQosConfigGlobalsDscpIngressTranslation,
                  mgmtQosConfigGlobalsDscpClassify,
                  mgmtQosConfigGlobalsDscpEgressTranslation,
                  mgmtQosConfigGlobalsDscpEgressTranslationDp1 }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 6 }

mgmtQosConfigGlobalsCosToDscpTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigGlobalsCosToDscpCos,
                  mgmtQosConfigGlobalsCosToDscpDscp,
                  mgmtQosConfigGlobalsCosToDscpDscpDp1,
                  mgmtQosConfigGlobalsCosToDscpDscpDp2,
                  mgmtQosConfigGlobalsCosToDscpDscpDp3 }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 7 }

mgmtQosConfigInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfaceIfIndex,
                  mgmtQosConfigInterfaceCos,
                  mgmtQosConfigInterfaceDpl,
                  mgmtQosConfigInterfacePcp,
                  mgmtQosConfigInterfaceDei,
                  mgmtQosConfigInterfaceTrustTag,
                  mgmtQosConfigInterfaceTrustDscp,
                  mgmtQosConfigInterfaceDwrrCount,
                  mgmtQosConfigInterfaceTagRemarkingMode,
                  mgmtQosConfigInterfaceTagPcp,
                  mgmtQosConfigInterfaceTagDei,
                  mgmtQosConfigInterfaceDscpTranslate,
                  mgmtQosConfigInterfaceDscpClassify,
                  mgmtQosConfigInterfaceDscpRemark,
                  mgmtQosConfigInterfaceQceAddressMode,
                  mgmtQosConfigInterfaceQceKeyType,
                  mgmtQosConfigInterfaceWredGroup,
                  mgmtQosConfigInterfaceIngressMap,
                  mgmtQosConfigInterfaceEgressMap,
                  mgmtQosConfigInterfaceCosId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 8 }

mgmtQosConfigInterfaceTagToCosTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfaceTagToCosIfIndex,
                  mgmtQosConfigInterfaceTagToCosPcp,
                  mgmtQosConfigInterfaceTagToCosDei,
                  mgmtQosConfigInterfaceTagToCosCos,
                  mgmtQosConfigInterfaceTagToCosDpl }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 9 }

mgmtQosConfigInterfaceCosToTagTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfaceCosToTagIfIndex,
                  mgmtQosConfigInterfaceCosToTagCos,
                  mgmtQosConfigInterfaceCosToTagDpl,
                  mgmtQosConfigInterfaceCosToTagPcp,
                  mgmtQosConfigInterfaceCosToTagDei }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 10 }

mgmtQosConfigInterfacePolicerTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfacePolicerIfIndex,
                  mgmtQosConfigInterfacePolicerEnable,
                  mgmtQosConfigInterfacePolicerFrameRate,
                  mgmtQosConfigInterfacePolicerFlowControl,
                  mgmtQosConfigInterfacePolicerCir }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 11 }

mgmtQosConfigInterfaceQueuePolicerTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfaceQueuePolicerIfIndex,
                  mgmtQosConfigInterfaceQueuePolicerQueue,
                  mgmtQosConfigInterfaceQueuePolicerEnable,
                  mgmtQosConfigInterfaceQueuePolicerCir }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 12 }

mgmtQosConfigInterfaceShaperTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfaceShaperIfIndex,
                  mgmtQosConfigInterfaceShaperEnable,
                  mgmtQosConfigInterfaceShaperCir,
                  mgmtQosConfigInterfaceShaperRateType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 13 }

mgmtQosConfigInterfaceQueueShaperTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfaceQueueShaperIfIndex,
                  mgmtQosConfigInterfaceQueueShaperQueue,
                  mgmtQosConfigInterfaceQueueShaperEnable,
                  mgmtQosConfigInterfaceQueueShaperExcess,
                  mgmtQosConfigInterfaceQueueShaperCir,
                  mgmtQosConfigInterfaceQueueShaperRateType,
                  mgmtQosConfigInterfaceQueueShaperCredit }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 14 }

mgmtQosConfigInterfaceSchedulerTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfaceSchedulerIfIndex,
                  mgmtQosConfigInterfaceSchedulerQueue,
                  mgmtQosConfigInterfaceSchedulerWeight,
                  mgmtQosConfigInterfaceSchedulerCutThrough,
                  mgmtQosConfigInterfaceSchedulerFramePreemption }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 15 }

mgmtQosConfigInterfaceStormPolicerUnicastTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfaceStormPolicerUnicastIfIndex,
                  mgmtQosConfigInterfaceStormPolicerUnicastEnable,
                  mgmtQosConfigInterfaceStormPolicerUnicastFrameRate,
                  mgmtQosConfigInterfaceStormPolicerUnicastCir }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 16 }

mgmtQosConfigInterfaceStormPolicerBroadcastTableInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtQosConfigInterfaceStormPolicerBroadcastIfIndex,
                  mgmtQosConfigInterfaceStormPolicerBroadcastEnable,
                  mgmtQosConfigInterfaceStormPolicerBroadcastFrameRate,
                  mgmtQosConfigInterfaceStormPolicerBroadcastCir }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 17 }

mgmtQosConfigInterfaceStormPolicerUnknownTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigInterfaceStormPolicerUnknownIfIndex,
                  mgmtQosConfigInterfaceStormPolicerUnknownEnable,
                  mgmtQosConfigInterfaceStormPolicerUnknownFrameRate,
                  mgmtQosConfigInterfaceStormPolicerUnknownCir }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 18 }

mgmtQosConfigQceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigQceQceId, mgmtQosConfigQceNextQceId,
                  mgmtQosConfigQceSwitchId, mgmtQosConfigQcePortList,
                  mgmtQosConfigQceDestMacType,
                  mgmtQosConfigQceDestMac,
                  mgmtQosConfigQceDestMacMask, mgmtQosConfigQceSrcMac,
                  mgmtQosConfigQceSrcMacMask,
                  mgmtQosConfigQceVlanTagType,
                  mgmtQosConfigQceVlanIdOp, mgmtQosConfigQceVlanId,
                  mgmtQosConfigQceVlanIdRange, mgmtQosConfigQcePcp,
                  mgmtQosConfigQceDei,
                  mgmtQosConfigQceInnerVlanTagType,
                  mgmtQosConfigQceInnerVlanIdOp,
                  mgmtQosConfigQceInnerVlanId,
                  mgmtQosConfigQceInnerVlanIdRange,
                  mgmtQosConfigQceInnerPcp, mgmtQosConfigQceInnerDei,
                  mgmtQosConfigQceFrameType, mgmtQosConfigQceEtype,
                  mgmtQosConfigQceLlcDsap,
                  mgmtQosConfigQceLlcDsapMask,
                  mgmtQosConfigQceLlcSsap,
                  mgmtQosConfigQceLlcSsapMask,
                  mgmtQosConfigQceLlcControl,
                  mgmtQosConfigQceLlcControlMask,
                  mgmtQosConfigQceSnapPid,
                  mgmtQosConfigQceSnapPidMask,
                  mgmtQosConfigQceIpv4Fragment,
                  mgmtQosConfigQceIpv4DscpOp,
                  mgmtQosConfigQceIpv4Dscp,
                  mgmtQosConfigQceIpv4DscpRange,
                  mgmtQosConfigQceIpv4Protocol,
                  mgmtQosConfigQceIpv4ProtocolMask,
                  mgmtQosConfigQceIpv4SrcIp,
                  mgmtQosConfigQceIpv4SrcIpMask,
                  mgmtQosConfigQceIpv4DestIp,
                  mgmtQosConfigQceIpv4DestIpMask,
                  mgmtQosConfigQceIpv4SrcPortOp,
                  mgmtQosConfigQceIpv4SrcPort,
                  mgmtQosConfigQceIpv4SrcPortRange,
                  mgmtQosConfigQceIpv4DestPortOp,
                  mgmtQosConfigQceIpv4DestPort,
                  mgmtQosConfigQceIpv4DestPortRange,
                  mgmtQosConfigQceIpv6DscpOp,
                  mgmtQosConfigQceIpv6Dscp,
                  mgmtQosConfigQceIpv6DscpRange,
                  mgmtQosConfigQceIpv6Protocol,
                  mgmtQosConfigQceIpv6ProtocolMask,
                  mgmtQosConfigQceIpv6SrcIp,
                  mgmtQosConfigQceIpv6SrcIpMask,
                  mgmtQosConfigQceIpv6DestIp,
                  mgmtQosConfigQceIpv6DestIpMask,
                  mgmtQosConfigQceIpv6SrcPortOp,
                  mgmtQosConfigQceIpv6SrcPort,
                  mgmtQosConfigQceIpv6SrcPortRange,
                  mgmtQosConfigQceIpv6DestPortOp,
                  mgmtQosConfigQceIpv6DestPort,
                  mgmtQosConfigQceIpv6DestPortRange,
                  mgmtQosConfigQceActionCosEnable,
                  mgmtQosConfigQceActionCos,
                  mgmtQosConfigQceActionDplEnable,
                  mgmtQosConfigQceActionDpl,
                  mgmtQosConfigQceActionDscpEnable,
                  mgmtQosConfigQceActionDscp,
                  mgmtQosConfigQceActionPcpDeiEnable,
                  mgmtQosConfigQceActionPcp,
                  mgmtQosConfigQceActionDei,
                  mgmtQosConfigQceActionPolicyEnable,
                  mgmtQosConfigQceActionPolicy,
                  mgmtQosConfigQceActionMapEnable,
                  mgmtQosConfigQceActionMap, mgmtQosConfigQceAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 19 }

mgmtQosConfigQceTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigQceTableRowEditorQceId,
                  mgmtQosConfigQceTableRowEditorNextQceId,
                  mgmtQosConfigQceTableRowEditorSwitchId,
                  mgmtQosConfigQceTableRowEditorPortList,
                  mgmtQosConfigQceTableRowEditorDestMacType,
                  mgmtQosConfigQceTableRowEditorDestMac,
                  mgmtQosConfigQceTableRowEditorDestMacMask,
                  mgmtQosConfigQceTableRowEditorSrcMac,
                  mgmtQosConfigQceTableRowEditorSrcMacMask,
                  mgmtQosConfigQceTableRowEditorVlanTagType,
                  mgmtQosConfigQceTableRowEditorVlanIdOp,
                  mgmtQosConfigQceTableRowEditorVlanId,
                  mgmtQosConfigQceTableRowEditorVlanIdRange,
                  mgmtQosConfigQceTableRowEditorPcp,
                  mgmtQosConfigQceTableRowEditorDei,
                  mgmtQosConfigQceTableRowEditorInnerVlanTagType,
                  mgmtQosConfigQceTableRowEditorInnerVlanIdOp,
                  mgmtQosConfigQceTableRowEditorInnerVlanId,
                  mgmtQosConfigQceTableRowEditorInnerVlanIdRange,
                  mgmtQosConfigQceTableRowEditorInnerPcp,
                  mgmtQosConfigQceTableRowEditorInnerDei,
                  mgmtQosConfigQceTableRowEditorFrameType,
                  mgmtQosConfigQceTableRowEditorEtype,
                  mgmtQosConfigQceTableRowEditorLlcDsap,
                  mgmtQosConfigQceTableRowEditorLlcDsapMask,
                  mgmtQosConfigQceTableRowEditorLlcSsap,
                  mgmtQosConfigQceTableRowEditorLlcSsapMask,
                  mgmtQosConfigQceTableRowEditorLlcControl,
                  mgmtQosConfigQceTableRowEditorLlcControlMask,
                  mgmtQosConfigQceTableRowEditorSnapPid,
                  mgmtQosConfigQceTableRowEditorSnapPidMask,
                  mgmtQosConfigQceTableRowEditorIpv4Fragment,
                  mgmtQosConfigQceTableRowEditorIpv4DscpOp,
                  mgmtQosConfigQceTableRowEditorIpv4Dscp,
                  mgmtQosConfigQceTableRowEditorIpv4DscpRange,
                  mgmtQosConfigQceTableRowEditorIpv4Protocol,
                  mgmtQosConfigQceTableRowEditorIpv4ProtocolMask,
                  mgmtQosConfigQceTableRowEditorIpv4SrcIp,
                  mgmtQosConfigQceTableRowEditorIpv4SrcIpMask,
                  mgmtQosConfigQceTableRowEditorIpv4DestIp,
                  mgmtQosConfigQceTableRowEditorIpv4DestIpMask,
                  mgmtQosConfigQceTableRowEditorIpv4SrcPortOp,
                  mgmtQosConfigQceTableRowEditorIpv4SrcPort,
                  mgmtQosConfigQceTableRowEditorIpv4SrcPortRange,
                  mgmtQosConfigQceTableRowEditorIpv4DestPortOp,
                  mgmtQosConfigQceTableRowEditorIpv4DestPort,
                  mgmtQosConfigQceTableRowEditorIpv4DestPortRange,
                  mgmtQosConfigQceTableRowEditorIpv6DscpOp,
                  mgmtQosConfigQceTableRowEditorIpv6Dscp,
                  mgmtQosConfigQceTableRowEditorIpv6DscpRange,
                  mgmtQosConfigQceTableRowEditorIpv6Protocol,
                  mgmtQosConfigQceTableRowEditorIpv6ProtocolMask,
                  mgmtQosConfigQceTableRowEditorIpv6SrcIp,
                  mgmtQosConfigQceTableRowEditorIpv6SrcIpMask,
                  mgmtQosConfigQceTableRowEditorIpv6DestIp,
                  mgmtQosConfigQceTableRowEditorIpv6DestIpMask,
                  mgmtQosConfigQceTableRowEditorIpv6SrcPortOp,
                  mgmtQosConfigQceTableRowEditorIpv6SrcPort,
                  mgmtQosConfigQceTableRowEditorIpv6SrcPortRange,
                  mgmtQosConfigQceTableRowEditorIpv6DestPortOp,
                  mgmtQosConfigQceTableRowEditorIpv6DestPort,
                  mgmtQosConfigQceTableRowEditorIpv6DestPortRange,
                  mgmtQosConfigQceTableRowEditorActionCosEnable,
                  mgmtQosConfigQceTableRowEditorActionCos,
                  mgmtQosConfigQceTableRowEditorActionDplEnable,
                  mgmtQosConfigQceTableRowEditorActionDpl,
                  mgmtQosConfigQceTableRowEditorActionDscpEnable,
                  mgmtQosConfigQceTableRowEditorActionDscp,
                  mgmtQosConfigQceTableRowEditorActionPcpDeiEnable,
                  mgmtQosConfigQceTableRowEditorActionPcp,
                  mgmtQosConfigQceTableRowEditorActionDei,
                  mgmtQosConfigQceTableRowEditorActionPolicyEnable,
                  mgmtQosConfigQceTableRowEditorActionPolicy,
                  mgmtQosConfigQceTableRowEditorActionMapEnable,
                  mgmtQosConfigQceTableRowEditorActionMap,
                  mgmtQosConfigQceTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 20 }

mgmtQosConfigQcePrecedenceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigQcePrecedenceIndex,
                  mgmtQosConfigQcePrecedenceQceId }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 21 }

mgmtQosConfigIngressMapTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigIngressMapIngressMapId,
                  mgmtQosConfigIngressMapKey,
                  mgmtQosConfigIngressMapActionCos,
                  mgmtQosConfigIngressMapActionDpl,
                  mgmtQosConfigIngressMapActionPcp,
                  mgmtQosConfigIngressMapActionDei,
                  mgmtQosConfigIngressMapActionDscp,
                  mgmtQosConfigIngressMapActionCosid,
                  mgmtQosConfigIngressMapActionPath,
                  mgmtQosConfigIngressMapAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 22 }

mgmtQosConfigIngressMapTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigIngressMapTableRowEditorIngressMapId,
                  mgmtQosConfigIngressMapTableRowEditorKey,
                  mgmtQosConfigIngressMapTableRowEditorActionCos,
                  mgmtQosConfigIngressMapTableRowEditorActionDpl,
                  mgmtQosConfigIngressMapTableRowEditorActionPcp,
                  mgmtQosConfigIngressMapTableRowEditorActionDei,
                  mgmtQosConfigIngressMapTableRowEditorActionDscp,
                  mgmtQosConfigIngressMapTableRowEditorActionCosid,
                  mgmtQosConfigIngressMapTableRowEditorActionPath,
                  mgmtQosConfigIngressMapTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 23 }

mgmtQosConfigIngressMapPcpTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigIngressMapPcpIngressMapId,
                  mgmtQosConfigIngressMapPcpPcp,
                  mgmtQosConfigIngressMapPcpDei,
                  mgmtQosConfigIngressMapPcpToCos,
                  mgmtQosConfigIngressMapPcpToDpl,
                  mgmtQosConfigIngressMapPcpToPcp,
                  mgmtQosConfigIngressMapPcpToDei,
                  mgmtQosConfigIngressMapPcpToDscp,
                  mgmtQosConfigIngressMapPcpToCosid,
                  mgmtQosConfigIngressMapPcpToPathCosid }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 24 }

mgmtQosConfigIngressMapDscpTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigIngressMapDscpIngressMapId,
                  mgmtQosConfigIngressMapDscpDscp,
                  mgmtQosConfigIngressMapDscpToCos,
                  mgmtQosConfigIngressMapDscpToDpl,
                  mgmtQosConfigIngressMapDscpToPcp,
                  mgmtQosConfigIngressMapDscpToDei,
                  mgmtQosConfigIngressMapDscpToDscp,
                  mgmtQosConfigIngressMapDscpToCosid,
                  mgmtQosConfigIngressMapDscpToPathCosid }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 25 }

mgmtQosConfigEgressMapTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigEgressMapEgressMapId,
                  mgmtQosConfigEgressMapKey,
                  mgmtQosConfigEgressMapActionPcp,
                  mgmtQosConfigEgressMapActionDei,
                  mgmtQosConfigEgressMapActionDscp,
                  mgmtQosConfigEgressMapActionPath,
                  mgmtQosConfigEgressMapAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 26 }

mgmtQosConfigEgressMapTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigEgressMapTableRowEditorEgressMapId,
                  mgmtQosConfigEgressMapTableRowEditorKey,
                  mgmtQosConfigEgressMapTableRowEditorActionPcp,
                  mgmtQosConfigEgressMapTableRowEditorActionDei,
                  mgmtQosConfigEgressMapTableRowEditorActionDscp,
                  mgmtQosConfigEgressMapTableRowEditorActionPath,
                  mgmtQosConfigEgressMapTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 27 }

mgmtQosConfigEgressMapCosidTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigEgressMapCosidEgressMapId,
                  mgmtQosConfigEgressMapCosidCosid,
                  mgmtQosConfigEgressMapCosidDpl,
                  mgmtQosConfigEgressMapCosidToPcp,
                  mgmtQosConfigEgressMapCosidToDei,
                  mgmtQosConfigEgressMapCosidToDscp,
                  mgmtQosConfigEgressMapCosidToPathCosid }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 28 }

mgmtQosConfigEgressMapDscpTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosConfigEgressMapDscpEgressMapId,
                  mgmtQosConfigEgressMapDscpDscp,
                  mgmtQosConfigEgressMapDscpDpl,
                  mgmtQosConfigEgressMapDscpToPcp,
                  mgmtQosConfigEgressMapDscpToDei,
                  mgmtQosConfigEgressMapDscpToDscp,
                  mgmtQosConfigEgressMapDscpToPathCosid }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 29 }

mgmtQosStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosStatusInterfaceIfIndex,
                  mgmtQosStatusInterfaceCos }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 30 }

mgmtQosStatusInterfaceSchedulerTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosStatusInterfaceSchedulerIfIndex,
                  mgmtQosStatusInterfaceSchedulerQueue,
                  mgmtQosStatusInterfaceSchedulerWeight }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 31 }

mgmtQosControlQceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtQosControlQceConflictResolve }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtQosMibGroups 32 }

mgmtQosMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtQosCapabilitiesInfoGroup,
                       mgmtQosConfigGlobalsStormPolicersUnicastInfoGroup,
                       mgmtQosConfigGlobalsStormPolicersMulticastInfoGroup,
                       mgmtQosConfigGlobalsStormPolicersBroadcastInfoGroup,
                       mgmtQosConfigGlobalsWredTableInfoGroup,
                       mgmtQosConfigGlobalsDscpTableInfoGroup,
                       mgmtQosConfigGlobalsCosToDscpTableInfoGroup,
                       mgmtQosConfigInterfaceTableInfoGroup,
                       mgmtQosConfigInterfaceTagToCosTableInfoGroup,
                       mgmtQosConfigInterfaceCosToTagTableInfoGroup,
                       mgmtQosConfigInterfacePolicerTableInfoGroup,
                       mgmtQosConfigInterfaceQueuePolicerTableInfoGroup,
                       mgmtQosConfigInterfaceShaperTableInfoGroup,
                       mgmtQosConfigInterfaceQueueShaperTableInfoGroup,
                       mgmtQosConfigInterfaceSchedulerTableInfoGroup,
                       mgmtQosConfigInterfaceStormPolicerUnicastTableInfoGroup,
                       mgmtQosConfigInterfaceStormPolicerBroadcastTableInfoGroup,
                       mgmtQosConfigInterfaceStormPolicerUnknownTableInfoGroup,
                       mgmtQosConfigQceTableInfoGroup,
                       mgmtQosConfigQceTableRowEditorInfoGroup,
                       mgmtQosConfigQcePrecedenceTableInfoGroup,
                       mgmtQosConfigIngressMapTableInfoGroup,
                       mgmtQosConfigIngressMapTableRowEditorInfoGroup,
                       mgmtQosConfigIngressMapPcpTableInfoGroup,
                       mgmtQosConfigIngressMapDscpTableInfoGroup,
                       mgmtQosConfigEgressMapTableInfoGroup,
                       mgmtQosConfigEgressMapTableRowEditorInfoGroup,
                       mgmtQosConfigEgressMapCosidTableInfoGroup,
                       mgmtQosConfigEgressMapDscpTableInfoGroup,
                       mgmtQosStatusInterfaceTableInfoGroup,
                       mgmtQosStatusInterfaceSchedulerTableInfoGroup,
                       mgmtQosControlQceInfoGroup }

    ::= { mgmtQosMibCompliances 1 }

END
