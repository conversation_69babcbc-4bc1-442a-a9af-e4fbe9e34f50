-- *****************************************************************
-- IPV6-SOURCE-GUARD-MIB:  
-- ****************************************************************

MGMT-IPV6-SOURCE-GUARD-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    Unsigned32 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTVlan FROM MGMT-TC
    ;

mgmtIpv6SourceGuardMib MODULE-IDENTITY
    LAST-UPDATED "201805230000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the IPv6 Source Guard MIB"
    REVISION    "201805230000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 147 }


mgmtIpv6SourceGuardMibObjects OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardMib 1 }

mgmtIpv6SourceGuardConfig OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardMibObjects 2 }

mgmtIpv6SourceGuardConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardConfig 1 }

mgmtIpv6SourceGuardConfigGlobalsEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global config of IPv6 source guard. TRUE is to enable IP source guard
         and FALSE is to disable it."
    ::= { mgmtIpv6SourceGuardConfigGlobals 1 }

mgmtIpv6SourceGuardConfigInterface OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardConfig 2 }

mgmtIpv6SourceGuardConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpv6SourceGuardConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure IPv6 Source Guard for a specific port."
    ::= { mgmtIpv6SourceGuardConfigInterface 1 }

mgmtIpv6SourceGuardConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTIpv6SourceGuardConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface can be configured for ipv6 source guard"
    INDEX       { mgmtIpv6SourceGuardConfigInterfaceIfIndex }
    ::= { mgmtIpv6SourceGuardConfigInterfaceTable 1 }

MGMTIpv6SourceGuardConfigInterfaceEntry ::= SEQUENCE {
    mgmtIpv6SourceGuardConfigInterfaceIfIndex            MGMTInterfaceIndex,
    mgmtIpv6SourceGuardConfigInterfaceEnabled            TruthValue,
    mgmtIpv6SourceGuardConfigInterfaceMaxDynamicEntries  Unsigned32
}

mgmtIpv6SourceGuardConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpv6SourceGuardConfigInterfaceEntry 1 }

mgmtIpv6SourceGuardConfigInterfaceEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "True means that ipv6 source guard is enabled on port."
    ::= { mgmtIpv6SourceGuardConfigInterfaceEntry 2 }

mgmtIpv6SourceGuardConfigInterfaceMaxDynamicEntries OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Max number of allowed dynamic entries per port."
    ::= { mgmtIpv6SourceGuardConfigInterfaceEntry 3 }

mgmtIpv6SourceGuardConfigStatic OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardConfig 3 }

mgmtIpv6SourceGuardConfigStaticTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpv6SourceGuardConfigStaticEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing the static binding entries of IPv6 source
         guard."
    ::= { mgmtIpv6SourceGuardConfigStatic 1 }

mgmtIpv6SourceGuardConfigStaticEntry OBJECT-TYPE
    SYNTAX      MGMTIpv6SourceGuardConfigStaticEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpv6SourceGuardConfigStaticIfindex,
                  mgmtIpv6SourceGuardConfigStaticVlanId,
                  mgmtIpv6SourceGuardConfigStaticIpv6Address }
    ::= { mgmtIpv6SourceGuardConfigStaticTable 1 }

MGMTIpv6SourceGuardConfigStaticEntry ::= SEQUENCE {
    mgmtIpv6SourceGuardConfigStaticIfindex      MGMTInterfaceIndex,
    mgmtIpv6SourceGuardConfigStaticVlanId       MGMTVlan,
    mgmtIpv6SourceGuardConfigStaticIpv6Address  InetAddressIPv6,
    mgmtIpv6SourceGuardConfigStaticMacAddress   MacAddress,
    mgmtIpv6SourceGuardConfigStaticAction       MGMTRowEditorState
}

mgmtIpv6SourceGuardConfigStaticIfindex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port entry is bound to."
    ::= { mgmtIpv6SourceGuardConfigStaticEntry 1 }

mgmtIpv6SourceGuardConfigStaticVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The VLAN ID. 0 means no vlan id is needed."
    ::= { mgmtIpv6SourceGuardConfigStaticEntry 2 }

mgmtIpv6SourceGuardConfigStaticIpv6Address OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Entry's IPv6 address."
    ::= { mgmtIpv6SourceGuardConfigStaticEntry 3 }

mgmtIpv6SourceGuardConfigStaticMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Assigned MAC Address."
    ::= { mgmtIpv6SourceGuardConfigStaticEntry 4 }

mgmtIpv6SourceGuardConfigStaticAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpv6SourceGuardConfigStaticEntry 100 }

mgmtIpv6SourceGuardConfigStaticTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardConfigStatic 5 }

mgmtIpv6SourceGuardConfigStaticTableRowEditorIfindex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port entry is bound to."
    ::= { mgmtIpv6SourceGuardConfigStaticTableRowEditor 1 }

mgmtIpv6SourceGuardConfigStaticTableRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The VLAN ID. 0 means no vlan id is needed."
    ::= { mgmtIpv6SourceGuardConfigStaticTableRowEditor 2 }

mgmtIpv6SourceGuardConfigStaticTableRowEditorIpv6Address OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Entry's IPv6 address."
    ::= { mgmtIpv6SourceGuardConfigStaticTableRowEditor 3 }

mgmtIpv6SourceGuardConfigStaticTableRowEditorMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Assigned MAC Address."
    ::= { mgmtIpv6SourceGuardConfigStaticTableRowEditor 4 }

mgmtIpv6SourceGuardConfigStaticTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpv6SourceGuardConfigStaticTableRowEditor 100 }

mgmtIpv6SourceGuardStatus OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardMibObjects 3 }

mgmtIpv6SourceGuardStatusDynamicTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpv6SourceGuardStatusDynamicEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing the static binding entries of IPv6 source
         guard."
    ::= { mgmtIpv6SourceGuardStatus 1 }

mgmtIpv6SourceGuardStatusDynamicEntry OBJECT-TYPE
    SYNTAX      MGMTIpv6SourceGuardStatusDynamicEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpv6SourceGuardStatusDynamicIfindex,
                  mgmtIpv6SourceGuardStatusDynamicVlanId,
                  mgmtIpv6SourceGuardStatusDynamicIpv6Address }
    ::= { mgmtIpv6SourceGuardStatusDynamicTable 1 }

MGMTIpv6SourceGuardStatusDynamicEntry ::= SEQUENCE {
    mgmtIpv6SourceGuardStatusDynamicIfindex      MGMTInterfaceIndex,
    mgmtIpv6SourceGuardStatusDynamicVlanId       MGMTVlan,
    mgmtIpv6SourceGuardStatusDynamicIpv6Address  InetAddressIPv6,
    mgmtIpv6SourceGuardStatusDynamicMacAddress   MacAddress
}

mgmtIpv6SourceGuardStatusDynamicIfindex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port entry is bound to."
    ::= { mgmtIpv6SourceGuardStatusDynamicEntry 1 }

mgmtIpv6SourceGuardStatusDynamicVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The VLAN ID. 0 means no vlan id is needed."
    ::= { mgmtIpv6SourceGuardStatusDynamicEntry 2 }

mgmtIpv6SourceGuardStatusDynamicIpv6Address OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Entry's IPv6 address."
    ::= { mgmtIpv6SourceGuardStatusDynamicEntry 3 }

mgmtIpv6SourceGuardStatusDynamicMacAddress OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Assigned MAC Address."
    ::= { mgmtIpv6SourceGuardStatusDynamicEntry 4 }

mgmtIpv6SourceGuardControl OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardMibObjects 4 }

mgmtIpv6SourceGuardControlTranslate OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardControl 1 }

mgmtIpv6SourceGuardControlTranslateTranslateDynamicToStatic OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Translate all the current dynamic entries to static ones. Set it as
         TRUE to do the action."
    ::= { mgmtIpv6SourceGuardControlTranslate 1 }

mgmtIpv6SourceGuardStatistics OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardMibObjects 5 }

mgmtIpv6SourceGuardMibConformance OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardMib 2 }

mgmtIpv6SourceGuardMibCompliances OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardMibConformance 1 }

mgmtIpv6SourceGuardMibGroups OBJECT IDENTIFIER
    ::= { mgmtIpv6SourceGuardMibConformance 2 }

mgmtIpv6SourceGuardConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpv6SourceGuardConfigGlobalsEnabled }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpv6SourceGuardMibGroups 1 }

mgmtIpv6SourceGuardConfigInterfaceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpv6SourceGuardConfigInterfaceIfIndex,
                  mgmtIpv6SourceGuardConfigInterfaceEnabled,
                  mgmtIpv6SourceGuardConfigInterfaceMaxDynamicEntries }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpv6SourceGuardMibGroups 2 }

mgmtIpv6SourceGuardConfigStaticTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpv6SourceGuardConfigStaticIfindex,
                  mgmtIpv6SourceGuardConfigStaticVlanId,
                  mgmtIpv6SourceGuardConfigStaticIpv6Address,
                  mgmtIpv6SourceGuardConfigStaticMacAddress,
                  mgmtIpv6SourceGuardConfigStaticAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpv6SourceGuardMibGroups 3 }

mgmtIpv6SourceGuardConfigStaticTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpv6SourceGuardConfigStaticTableRowEditorIfindex,
                  mgmtIpv6SourceGuardConfigStaticTableRowEditorVlanId,
                  mgmtIpv6SourceGuardConfigStaticTableRowEditorIpv6Address,
                  mgmtIpv6SourceGuardConfigStaticTableRowEditorMacAddress,
                  mgmtIpv6SourceGuardConfigStaticTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpv6SourceGuardMibGroups 4 }

mgmtIpv6SourceGuardStatusDynamicInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpv6SourceGuardStatusDynamicIfindex,
                  mgmtIpv6SourceGuardStatusDynamicVlanId,
                  mgmtIpv6SourceGuardStatusDynamicIpv6Address,
                  mgmtIpv6SourceGuardStatusDynamicMacAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpv6SourceGuardMibGroups 5 }

mgmtIpv6SourceGuardControlTranslateInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtIpv6SourceGuardControlTranslateTranslateDynamicToStatic }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpv6SourceGuardMibGroups 6 }

mgmtIpv6SourceGuardMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtIpv6SourceGuardConfigGlobalsInfoGroup,
                       mgmtIpv6SourceGuardConfigInterfaceInfoGroup,
                       mgmtIpv6SourceGuardConfigStaticTableInfoGroup,
                       mgmtIpv6SourceGuardConfigStaticTableRowEditorInfoGroup,
                       mgmtIpv6SourceGuardStatusDynamicInfoGroup,
                       mgmtIpv6SourceGuardControlTranslateInfoGroup }

    ::= { mgmtIpv6SourceGuardMibCompliances 1 }

END
