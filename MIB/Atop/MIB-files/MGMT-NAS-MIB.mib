-- *****************************************************************
-- NAS-MIB:  
-- ****************************************************************

MGMT-NAS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTVlan FROM MGMT-TC
    ;

mgmtNasMib MODULE-IDENTITY
    LAST-UPDATED "201712120000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is the Vitesse NAS private MIB."
    REVISION    "201712120000Z"
    DESCRIPTION
        "1) Changed nasPortStatus::cnt to nasPortStatus::multiMode. 2) Added
         authCnt and unauthCnt to NasStatusEntry. 3) Minor editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 21 }


MGMTnasPortControl ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration the NAS admin state.
         
         "
    SYNTAX      INTEGER { none(0), forceAuthorized(1), auto(2),
                          forceUnAuthorized(3), macBased(4),
                          dot1xSingle(5), dot1xmulti(6) }

MGMTnasPortStatus ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumerates the NAS interface status."
    SYNTAX      INTEGER { linkDown(0), authorized(1), unAuthorized(2),
                          disabled(3), multiMode(4) }

MGMTnasVlanType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration the NAS VLAN type."
    SYNTAX      INTEGER { none(0), radiusAssigned(1), guestVlan(2) }

mgmtNasMibObjects OBJECT IDENTIFIER
    ::= { mgmtNasMib 1 }

mgmtNasConfig OBJECT IDENTIFIER
    ::= { mgmtNasMibObjects 2 }

mgmtNasConfigGlobal OBJECT IDENTIFIER
    ::= { mgmtNasConfig 1 }

mgmtNasConfigGlobalNasMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to globally enabled or disabled NAS for the switch. If
         globally disabled, all physical interfaces are allowed forwarding of
         frames."
    ::= { mgmtNasConfigGlobal 1 }

mgmtNasConfigGlobalReauthenticationMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "If set to TRUE, successfully authenticated supplicants/clients are
         re-authenticated after the interval specified by the Reauthentication
         Period. Re-authentication for 802.1X-enabled interfaces can be used to
         detect if a new device is plugged into a switch port or if a supplicant
         is no longer attached. For MAC-based ports, re-authentication is only
         useful, if the RADIUS server configuration has changed. It does not
         involve communication between the switch and the client, and therefore
         does not imply that a client is still present on a port (see Aging
         Period)."
    ::= { mgmtNasConfigGlobal 2 }

mgmtNasConfigGlobalReauthenticationPeriod OBJECT-TYPE
    SYNTAX      MGMTUnsigned16 (1..3600)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Sets the period in seconds, after which a connected client must be
         re-authenticated. This is only active if the ReauthenticationMode is
         set to TRUE."
    ::= { mgmtNasConfigGlobal 3 }

mgmtNasConfigGlobalEapolTimeout OBJECT-TYPE
    SYNTAX      MGMTUnsigned16 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Determines the time for re-transmission of Request Identity EAPOL
         frames. This has no effect for MAC-based ports."
    ::= { mgmtNasConfigGlobal 4 }

mgmtNasConfigGlobalAgingPeriod OBJECT-TYPE
    SYNTAX      Unsigned32 (10..1000000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specific the PSEC aging period in seconds. In the period the CPU starts
         listening to frames from the given MAC address, and if none arrives
         before period end, the entry will be removed."
    ::= { mgmtNasConfigGlobal 5 }

mgmtNasConfigGlobalAuthFailureHoldTime OBJECT-TYPE
    SYNTAX      Unsigned32 (10..1000000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Time in seconds to wait before attempting to re-authenticate if
         re-authentication failed for a given client."
    ::= { mgmtNasConfigGlobal 6 }

mgmtNasConfigGlobalRadiusAssignedQosMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to enable RADIUS assigned QoS."
    ::= { mgmtNasConfigGlobal 7 }

mgmtNasConfigGlobalRadiusAssignedVlanMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to enable RADIUS assigned VLAN."
    ::= { mgmtNasConfigGlobal 8 }

mgmtNasConfigGlobalGuestVlanMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to enable Guest VLAN Mode."
    ::= { mgmtNasConfigGlobal 9 }

mgmtNasConfigGlobalGuestVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Guest VLAN ID to get assigned to an interface moved to Guest VLAN mode."
    ::= { mgmtNasConfigGlobal 10 }

mgmtNasConfigGlobalMaxReauthrequestsCount OBJECT-TYPE
    SYNTAX      Unsigned32 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Maximum re-authentication request count."
    ::= { mgmtNasConfigGlobal 11 }

mgmtNasConfigGlobalGuestVlanAllowEapols OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to allow an interface to move to Guest VLAN even when EAPOL
         packets has been received at an interface."
    ::= { mgmtNasConfigGlobal 12 }

mgmtNasConfigReAuthTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTNasConfigReAuthEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for port configuration"
    ::= { mgmtNasConfig 2 }

mgmtNasConfigReAuthEntry OBJECT-TYPE
    SYNTAX      MGMTNasConfigReAuthEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical interface has a set of parameters"
    INDEX       { mgmtNasConfigReAuthInterfaceNo }
    ::= { mgmtNasConfigReAuthTable 1 }

MGMTNasConfigReAuthEntry ::= SEQUENCE {
    mgmtNasConfigReAuthInterfaceNo              MGMTInterfaceIndex,
    mgmtNasConfigReAuthAdminState               MGMTnasPortControl,
    mgmtNasConfigReAuthRadiusAssignedQosState   TruthValue,
    mgmtNasConfigReAuthRadiusAssignedVlanState  TruthValue,
    mgmtNasConfigReAuthGuestVlanState           TruthValue
}

mgmtNasConfigReAuthInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtNasConfigReAuthEntry 1 }

mgmtNasConfigReAuthAdminState OBJECT-TYPE
    SYNTAX      MGMTnasPortControl
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Administrative State.
         
         TypeNone : Forces an interface to be disabled.
         
         forceAuthorized : Forces an interface to grant access to all clients,
         802.1X-aware or not.
         
         auto : Requires an 802.1X-aware client to be authorized by the
         authentication server. Clients that are not 802.1X-aware will be denied
         access.
         
         unauthorized : Forces an interface to deny access to all clients,
         802.1X-aware or not.
         
         macBased : The switch authenticates on behalf of the client, using the
         client MAC-address as the username and password and MD5 EAP method.
         
         dot1xSingle : At most one supplicant is allowed to authenticate, and it
         authenticates using normal 802.1X frames.
         
         dot1xmulti : One or more supplicants are allowed to authenticate
         individually using an 802.1X variant, where EAPOL frames sent from the
         switch are directed towards the supplicants MAC address instead of
         using the multi-cast BPDU MAC address. Unauthenticated supplicants will
         not get access.
         
         
         
         Note: The 802.1X Admin State must be set to Authorized for interfaces
         that are enabled forSpanning Tree"
    ::= { mgmtNasConfigReAuthEntry 2 }

mgmtNasConfigReAuthRadiusAssignedQosState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to enable RADIUS-assigned QoS for this interface."
    ::= { mgmtNasConfigReAuthEntry 3 }

mgmtNasConfigReAuthRadiusAssignedVlanState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to enable RADIUS-assigned VLAN for this interface."
    ::= { mgmtNasConfigReAuthEntry 4 }

mgmtNasConfigReAuthGuestVlanState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to enable Guest-VLAN for this interface."
    ::= { mgmtNasConfigReAuthEntry 5 }

mgmtNasStatus OBJECT IDENTIFIER
    ::= { mgmtNasMibObjects 3 }

mgmtNasStatusTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTNasStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to get NAS per-port status table."
    ::= { mgmtNasStatus 1 }

mgmtNasStatusEntry OBJECT-TYPE
    SYNTAX      MGMTNasStatusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical interface has a set of per-port status parameters."
    INDEX       { mgmtNasStatusInterfaceNo }
    ::= { mgmtNasStatusTable 1 }

MGMTNasStatusEntry ::= SEQUENCE {
    mgmtNasStatusInterfaceNo  MGMTInterfaceIndex,
    mgmtNasStatusStatus       MGMTnasPortStatus,
    mgmtNasStatusQosClass     Unsigned32,
    mgmtNasStatusVlanType     MGMTnasVlanType,
    mgmtNasStatusVlanId       MGMTVlan,
    mgmtNasStatusAuthCnt      Unsigned32,
    mgmtNasStatusUnauthCnt    Unsigned32
}

mgmtNasStatusInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtNasStatusEntry 1 }

mgmtNasStatusStatus OBJECT-TYPE
    SYNTAX      MGMTnasPortStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "dot1x port status."
    ::= { mgmtNasStatusEntry 2 }

mgmtNasStatusQosClass OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "QoS class for this port. If value is 4294967295 it means that the QoS
         is not overridden."
    ::= { mgmtNasStatusEntry 3 }

mgmtNasStatusVlanType OBJECT-TYPE
    SYNTAX      MGMTnasVlanType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VLAN Type for this port."
    ::= { mgmtNasStatusEntry 4 }

mgmtNasStatusVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VLAN Id for this port. 0 if not overridden."
    ::= { mgmtNasStatusEntry 5 }

mgmtNasStatusAuthCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "In multi-client modes, number of authenticated clients on this port."
    ::= { mgmtNasStatusEntry 6 }

mgmtNasStatusUnauthCnt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "In multi-client modes, number of unauthenticated clients on this port."
    ::= { mgmtNasStatusEntry 7 }

mgmtNasStatusLastSupplicantTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTNasStatusLastSupplicantEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to get NAS last supplicant"
    ::= { mgmtNasStatus 2 }

mgmtNasStatusLastSupplicantEntry OBJECT-TYPE
    SYNTAX      MGMTNasStatusLastSupplicantEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical interface has a set of parameters"
    INDEX       { mgmtNasStatusLastSupplicantInterfaceNo }
    ::= { mgmtNasStatusLastSupplicantTable 1 }

MGMTNasStatusLastSupplicantEntry ::= SEQUENCE {
    mgmtNasStatusLastSupplicantInterfaceNo  MGMTInterfaceIndex,
    mgmtNasStatusLastSupplicantVlanId       MGMTVlan,
    mgmtNasStatusLastSupplicantMac          MacAddress,
    mgmtNasStatusLastSupplicantIdentity     MGMTDisplayString
}

mgmtNasStatusLastSupplicantInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtNasStatusLastSupplicantEntry 1 }

mgmtNasStatusLastSupplicantVlanId OBJECT-TYPE
    SYNTAX      MGMTVlan
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "VLAN ID."
    ::= { mgmtNasStatusLastSupplicantEntry 2 }

mgmtNasStatusLastSupplicantMac OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Binary version of MacAddrStr."
    ::= { mgmtNasStatusLastSupplicantEntry 3 }

mgmtNasStatusLastSupplicantIdentity OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..39))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Identity string."
    ::= { mgmtNasStatusLastSupplicantEntry 4 }

mgmtNasControl OBJECT IDENTIFIER
    ::= { mgmtNasMibObjects 4 }

mgmtNasControlPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTNasControlPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to start NAS re-authorization"
    ::= { mgmtNasControl 1 }

mgmtNasControlPortEntry OBJECT-TYPE
    SYNTAX      MGMTNasControlPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical interface has a set of parameters"
    INDEX       { mgmtNasControlPortInterfaceNo }
    ::= { mgmtNasControlPortTable 1 }

MGMTNasControlPortEntry ::= SEQUENCE {
    mgmtNasControlPortInterfaceNo  MGMTInterfaceIndex,
    mgmtNasControlPortNow          TruthValue
}

mgmtNasControlPortInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtNasControlPortEntry 1 }

mgmtNasControlPortNow OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "TRUE to force re-authentication immediately. FALSE to refresh (restart)
         802.1X authentication process."
    ::= { mgmtNasControlPortEntry 2 }

mgmtNasControlStatisticsClearTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTNasControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to clear NAS statistics for a specific interface."
    ::= { mgmtNasControl 2 }

mgmtNasControlStatisticsClearEntry OBJECT-TYPE
    SYNTAX      MGMTNasControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of statistics counters"
    INDEX       { mgmtNasControlStatisticsClearInterfaceNo }
    ::= { mgmtNasControlStatisticsClearTable 1 }

MGMTNasControlStatisticsClearEntry ::= SEQUENCE {
    mgmtNasControlStatisticsClearInterfaceNo  MGMTInterfaceIndex,
    mgmtNasControlStatisticsClearClear        TruthValue
}

mgmtNasControlStatisticsClearInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtNasControlStatisticsClearEntry 1 }

mgmtNasControlStatisticsClearClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "TRUE to clear NAS statistics."
    ::= { mgmtNasControlStatisticsClearEntry 5 }

mgmtNasStatistics OBJECT IDENTIFIER
    ::= { mgmtNasMibObjects 5 }

mgmtNasStatisticsEapolTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTNasStatisticsEapolEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to get NAS EAPOL statistics."
    ::= { mgmtNasStatistics 1 }

mgmtNasStatisticsEapolEntry OBJECT-TYPE
    SYNTAX      MGMTNasStatisticsEapolEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical interface has a set of EAPOL counters."
    INDEX       { mgmtNasStatisticsEapolInterfaceNo }
    ::= { mgmtNasStatisticsEapolTable 1 }

MGMTNasStatisticsEapolEntry ::= SEQUENCE {
    mgmtNasStatisticsEapolInterfaceNo                      MGMTInterfaceIndex,
    mgmtNasStatisticsEapolDot1xAuthEapolFramesRx           Unsigned32,
    mgmtNasStatisticsEapolDot1xAuthEapolFramesTx           Unsigned32,
    mgmtNasStatisticsEapolDot1xAuthEapolStartFramesRx      Unsigned32,
    mgmtNasStatisticsEapolDot1xAuthEapolLogoffFramesRx     Unsigned32,
    mgmtNasStatisticsEapolDot1xAuthEapolRespIdFramesRx     Unsigned32,
    mgmtNasStatisticsEapolDot1xAuthEapolRespFramesRx       Unsigned32,
    mgmtNasStatisticsEapolDot1xAuthEapolReqIdFramesTx      Unsigned32,
    mgmtNasStatisticsEapolDot1xAuthEapolReqFramesTx        Unsigned32,
    mgmtNasStatisticsEapolDot1xAuthInvalidEapolFramesRx    Unsigned32,
    mgmtNasStatisticsEapolDot1xAuthEapLengthErrorFramesRx  Unsigned32
}

mgmtNasStatisticsEapolInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtNasStatisticsEapolEntry 1 }

mgmtNasStatisticsEapolDot1xAuthEapolFramesRx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Eapol Frames Received."
    ::= { mgmtNasStatisticsEapolEntry 5 }

mgmtNasStatisticsEapolDot1xAuthEapolFramesTx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Eapol Frames Transmitted."
    ::= { mgmtNasStatisticsEapolEntry 6 }

mgmtNasStatisticsEapolDot1xAuthEapolStartFramesRx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Eapol Start Frames Received."
    ::= { mgmtNasStatisticsEapolEntry 7 }

mgmtNasStatisticsEapolDot1xAuthEapolLogoffFramesRx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Eapol Logoff Frames Received."
    ::= { mgmtNasStatisticsEapolEntry 8 }

mgmtNasStatisticsEapolDot1xAuthEapolRespIdFramesRx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Eapol RespId Frames Received."
    ::= { mgmtNasStatisticsEapolEntry 9 }

mgmtNasStatisticsEapolDot1xAuthEapolRespFramesRx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Eapol Resp Frames Received."
    ::= { mgmtNasStatisticsEapolEntry 10 }

mgmtNasStatisticsEapolDot1xAuthEapolReqIdFramesTx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Eapol Req Id Frames Transmitted."
    ::= { mgmtNasStatisticsEapolEntry 11 }

mgmtNasStatisticsEapolDot1xAuthEapolReqFramesTx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Eapol Req Frames Transmitted."
    ::= { mgmtNasStatisticsEapolEntry 12 }

mgmtNasStatisticsEapolDot1xAuthInvalidEapolFramesRx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Invalid Eapol Frames Received."
    ::= { mgmtNasStatisticsEapolEntry 13 }

mgmtNasStatisticsEapolDot1xAuthEapLengthErrorFramesRx OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of dot1x Auth Eap Length Error Frames Received."
    ::= { mgmtNasStatisticsEapolEntry 14 }

mgmtNasStatisticsRadiusTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTNasStatisticsRadiusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to get NAS EAPOL statistics."
    ::= { mgmtNasStatistics 2 }

mgmtNasStatisticsRadiusEntry OBJECT-TYPE
    SYNTAX      MGMTNasStatisticsRadiusEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical interface has a set of EAPOL counters."
    INDEX       { mgmtNasStatisticsRadiusInterfaceNo }
    ::= { mgmtNasStatisticsRadiusTable 1 }

MGMTNasStatisticsRadiusEntry ::= SEQUENCE {
    mgmtNasStatisticsRadiusInterfaceNo                       MGMTInterfaceIndex,
    mgmtNasStatisticsRadiusBackendResponses                  Unsigned32,
    mgmtNasStatisticsRadiusBackendAccessChallenges           Unsigned32,
    mgmtNasStatisticsRadiusBackendOtherRequestsToSupplicant  Unsigned32,
    mgmtNasStatisticsRadiusBackendAuthSuccesses              Unsigned32,
    mgmtNasStatisticsRadiusBackendAuthFails                  Unsigned32
}

mgmtNasStatisticsRadiusInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtNasStatisticsRadiusEntry 1 }

mgmtNasStatisticsRadiusBackendResponses OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of backend Responses."
    ::= { mgmtNasStatisticsRadiusEntry 5 }

mgmtNasStatisticsRadiusBackendAccessChallenges OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of backend Access Challenges."
    ::= { mgmtNasStatisticsRadiusEntry 6 }

mgmtNasStatisticsRadiusBackendOtherRequestsToSupplicant OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of backend Other Requests To Supplicant."
    ::= { mgmtNasStatisticsRadiusEntry 7 }

mgmtNasStatisticsRadiusBackendAuthSuccesses OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of backend Auth Successes."
    ::= { mgmtNasStatisticsRadiusEntry 8 }

mgmtNasStatisticsRadiusBackendAuthFails OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Numbers of backend Auth Fails."
    ::= { mgmtNasStatisticsRadiusEntry 9 }

mgmtNasMibConformance OBJECT IDENTIFIER
    ::= { mgmtNasMib 2 }

mgmtNasMibCompliances OBJECT IDENTIFIER
    ::= { mgmtNasMibConformance 1 }

mgmtNasMibGroups OBJECT IDENTIFIER
    ::= { mgmtNasMibConformance 2 }

mgmtNasConfigGlobalInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNasConfigGlobalNasMode,
                  mgmtNasConfigGlobalReauthenticationMode,
                  mgmtNasConfigGlobalReauthenticationPeriod,
                  mgmtNasConfigGlobalEapolTimeout,
                  mgmtNasConfigGlobalAgingPeriod,
                  mgmtNasConfigGlobalAuthFailureHoldTime,
                  mgmtNasConfigGlobalRadiusAssignedQosMode,
                  mgmtNasConfigGlobalRadiusAssignedVlanMode,
                  mgmtNasConfigGlobalGuestVlanMode,
                  mgmtNasConfigGlobalGuestVlanId,
                  mgmtNasConfigGlobalMaxReauthrequestsCount,
                  mgmtNasConfigGlobalGuestVlanAllowEapols }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNasMibGroups 1 }

mgmtNasConfigReAuthInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNasConfigReAuthInterfaceNo,
                  mgmtNasConfigReAuthAdminState,
                  mgmtNasConfigReAuthRadiusAssignedQosState,
                  mgmtNasConfigReAuthRadiusAssignedVlanState,
                  mgmtNasConfigReAuthGuestVlanState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNasMibGroups 2 }

mgmtNasStatusTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNasStatusInterfaceNo, mgmtNasStatusStatus,
                  mgmtNasStatusQosClass, mgmtNasStatusVlanType,
                  mgmtNasStatusVlanId, mgmtNasStatusAuthCnt,
                  mgmtNasStatusUnauthCnt }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNasMibGroups 3 }

mgmtNasStatusLastSupplicantInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNasStatusLastSupplicantInterfaceNo,
                  mgmtNasStatusLastSupplicantVlanId,
                  mgmtNasStatusLastSupplicantMac,
                  mgmtNasStatusLastSupplicantIdentity }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNasMibGroups 4 }

mgmtNasControlPortInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNasControlPortInterfaceNo,
                  mgmtNasControlPortNow }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNasMibGroups 5 }

mgmtNasControlStatisticsClearInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNasControlStatisticsClearInterfaceNo,
                  mgmtNasControlStatisticsClearClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNasMibGroups 6 }

mgmtNasStatisticsEapolInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNasStatisticsEapolInterfaceNo,
                  mgmtNasStatisticsEapolDot1xAuthEapolFramesRx,
                  mgmtNasStatisticsEapolDot1xAuthEapolFramesTx,
                  mgmtNasStatisticsEapolDot1xAuthEapolStartFramesRx,
                  mgmtNasStatisticsEapolDot1xAuthEapolLogoffFramesRx,
                  mgmtNasStatisticsEapolDot1xAuthEapolRespIdFramesRx,
                  mgmtNasStatisticsEapolDot1xAuthEapolRespFramesRx,
                  mgmtNasStatisticsEapolDot1xAuthEapolReqIdFramesTx,
                  mgmtNasStatisticsEapolDot1xAuthEapolReqFramesTx,
                  mgmtNasStatisticsEapolDot1xAuthInvalidEapolFramesRx,
                  mgmtNasStatisticsEapolDot1xAuthEapLengthErrorFramesRx }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNasMibGroups 7 }

mgmtNasStatisticsRadiusInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNasStatisticsRadiusInterfaceNo,
                  mgmtNasStatisticsRadiusBackendResponses,
                  mgmtNasStatisticsRadiusBackendAccessChallenges,
                  mgmtNasStatisticsRadiusBackendOtherRequestsToSupplicant,
                  mgmtNasStatisticsRadiusBackendAuthSuccesses,
                  mgmtNasStatisticsRadiusBackendAuthFails }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNasMibGroups 8 }

mgmtNasMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtNasConfigGlobalInfoGroup,
                       mgmtNasConfigReAuthInfoGroup,
                       mgmtNasStatusTableInfoGroup,
                       mgmtNasStatusLastSupplicantInfoGroup,
                       mgmtNasControlPortInfoGroup,
                       mgmtNasControlStatisticsClearInfoGroup,
                       mgmtNasStatisticsEapolInfoGroup,
                       mgmtNasStatisticsRadiusInfoGroup }

    ::= { mgmtNasMibCompliances 1 }

END
