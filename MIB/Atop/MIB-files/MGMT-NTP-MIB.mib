-- *****************************************************************
-- NTP-MIB:  
-- ****************************************************************

MGMT-NTP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInetAddress FROM MGMT-TC
    ;

mgmtNtpMib MODULE-IDENTITY
    LAST-UPDATED "201410100000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of NTP"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 57 }


mgmtNtpMibObjects OBJECT IDENTIFIER
    ::= { mgmtNtpMib 1 }

mgmtNtpConfig OBJECT IDENTIFIER
    ::= { mgmtNtpMibObjects 2 }

mgmtNtpConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtNtpConfig 1 }

mgmtNtpConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global config mode of NTP. true is to enable NTP function in the system
         and false is to disable it."
    ::= { mgmtNtpConfigGlobals 1 }

mgmtNtpConfigServerTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTNtpConfigServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of NTP server."
    ::= { mgmtNtpConfig 2 }

mgmtNtpConfigServerEntry OBJECT-TYPE
    SYNTAX      MGMTNtpConfigServerEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each server has a set of parameters."
    INDEX       { mgmtNtpConfigServerIndex }
    ::= { mgmtNtpConfigServerTable 1 }

MGMTNtpConfigServerEntry ::= SEQUENCE {
    mgmtNtpConfigServerIndex    Integer32,
    mgmtNtpConfigServerAddress  MGMTInetAddress
}

mgmtNtpConfigServerIndex OBJECT-TYPE
    SYNTAX      Integer32 (1..5)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The index of NTP servers."
    ::= { mgmtNtpConfigServerEntry 1 }

mgmtNtpConfigServerAddress OBJECT-TYPE
    SYNTAX      MGMTInetAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Internet address of a NTP server."
    ::= { mgmtNtpConfigServerEntry 2 }

mgmtNtpMibConformance OBJECT IDENTIFIER
    ::= { mgmtNtpMib 2 }

mgmtNtpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtNtpMibConformance 1 }

mgmtNtpMibGroups OBJECT IDENTIFIER
    ::= { mgmtNtpMibConformance 2 }

mgmtNtpConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNtpConfigGlobalsMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNtpMibGroups 1 }

mgmtNtpConfigServerTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtNtpConfigServerIndex,
                  mgmtNtpConfigServerAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtNtpMibGroups 2 }

mgmtNtpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtNtpConfigGlobalsInfoGroup,
                       mgmtNtpConfigServerTableInfoGroup }

    ::= { mgmtNtpMibCompliances 1 }

END
