-- *****************************************************************
-- ROUTER-MIB:  
-- ****************************************************************

MGMT-ROUTER-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    ;

mgmtRouterMib MODULE-IDENTITY
    LAST-UPDATED "201807270000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the ROUTER MIB."
    REVISION    "201807270000Z"
    DESCRIPTION
        "Initial version."
    ::= { mgmtSwitch 153 }


MGMTRouterAccessListMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "The access right mode of the router access-list entry."
    SYNTAX      INTEGER { deny(0), permit(1) }

mgmtRouterMibObjects OBJECT IDENTIFIER
    ::= { mgmtRouterMib 1 }

mgmtRouterCapabilities OBJECT IDENTIFIER
    ::= { mgmtRouterMibObjects 1 }

mgmtRouterCapabilitiesMaxKeyChainNameMaxCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum count of the router key-chain name list"
    ::= { mgmtRouterCapabilities 1 }

mgmtRouterCapabilitiesMinKeyChainNameLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum name length of router key chain"
    ::= { mgmtRouterCapabilities 2 }

mgmtRouterCapabilitiesMaxKeyChainNameLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum name length of router key chain"
    ::= { mgmtRouterCapabilities 3 }

mgmtRouterCapabilitiesMinKeyChainKeyId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of router key chain key ID"
    ::= { mgmtRouterCapabilities 4 }

mgmtRouterCapabilitiesMaxKeyChainKeyId OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of router key chain key ID"
    ::= { mgmtRouterCapabilities 5 }

mgmtRouterCapabilitiesMinKeyChainPlainTextKeyStringLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum length of router key chain plain text key string"
    ::= { mgmtRouterCapabilities 6 }

mgmtRouterCapabilitiesMaxKeyChainPlainTextKeyStringLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum length of router key chain plain text key string"
    ::= { mgmtRouterCapabilities 7 }

mgmtRouterCapabilitiesMinKeyChainKeyEncryptedStringLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum length of router key chain encrypted key string"
    ::= { mgmtRouterCapabilities 8 }

mgmtRouterCapabilitiesMaxKeyChainKeyEncryptedStringLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum length of router key chain encrypted key string"
    ::= { mgmtRouterCapabilities 9 }

mgmtRouterCapabilitiesAccessListMaxCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum count of the router access-list"
    ::= { mgmtRouterCapabilities 10 }

mgmtRouterCapabilitiesMinAccessListNameLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum name length of router access-list"
    ::= { mgmtRouterCapabilities 11 }

mgmtRouterCapabilitiesMaxAccessListNameLen OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum name length of router access-list"
    ::= { mgmtRouterCapabilities 12 }

mgmtRouterCapabilitiesMinAcePrecedence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The minimum value of RIP access-list entry precedence"
    ::= { mgmtRouterCapabilities 13 }

mgmtRouterCapabilitiesMaxAcePrecedence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The maximum value of RIP access-list entry precedence"
    ::= { mgmtRouterCapabilities 14 }

mgmtRouterConfig OBJECT IDENTIFIER
    ::= { mgmtRouterMibObjects 2 }

mgmtRouterConfigKeyChainTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRouterConfigKeyChainEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is router key chain name table."
    ::= { mgmtRouterConfig 1 }

mgmtRouterConfigKeyChainEntry OBJECT-TYPE
    SYNTAX      MGMTRouterConfigKeyChainEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the name for the key chain."
    INDEX       { mgmtRouterConfigKeyChainKeyChainName }
    ::= { mgmtRouterConfigKeyChainTable 1 }

MGMTRouterConfigKeyChainEntry ::= SEQUENCE {
    mgmtRouterConfigKeyChainKeyChainName  MGMTDisplayString,
    mgmtRouterConfigKeyChainAction        MGMTRowEditorState
}

mgmtRouterConfigKeyChainKeyChainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The key chain name."
    ::= { mgmtRouterConfigKeyChainEntry 1 }

mgmtRouterConfigKeyChainAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRouterConfigKeyChainEntry 100 }

mgmtRouterConfigKeyChainTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtRouterConfig 2 }

mgmtRouterConfigKeyChainTableRowEditorKeyChainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The key chain name."
    ::= { mgmtRouterConfigKeyChainTableRowEditor 1 }

mgmtRouterConfigKeyChainTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRouterConfigKeyChainTableRowEditor 100 }

mgmtRouterConfigKeyChainKeyIdTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRouterConfigKeyChainKeyIdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is router key chain key ID configuration table."
    ::= { mgmtRouterConfig 3 }

mgmtRouterConfigKeyChainKeyIdEntry OBJECT-TYPE
    SYNTAX      MGMTRouterConfigKeyChainKeyIdEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each row contains the key configuration of the corresponding key chain
         and key ID."
    INDEX       { mgmtRouterConfigKeyChainKeyIdKeyChainName,
                  mgmtRouterConfigKeyChainKeyIdKeyId }
    ::= { mgmtRouterConfigKeyChainKeyIdTable 1 }

MGMTRouterConfigKeyChainKeyIdEntry ::= SEQUENCE {
    mgmtRouterConfigKeyChainKeyIdKeyChainName  MGMTDisplayString,
    mgmtRouterConfigKeyChainKeyIdKeyId         Integer32,
    mgmtRouterConfigKeyChainKeyIdIsEncrypted   TruthValue,
    mgmtRouterConfigKeyChainKeyIdKeyString     MGMTDisplayString,
    mgmtRouterConfigKeyChainKeyIdAction        MGMTRowEditorState
}

mgmtRouterConfigKeyChainKeyIdKeyChainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The key chain name."
    ::= { mgmtRouterConfigKeyChainKeyIdEntry 1 }

mgmtRouterConfigKeyChainKeyIdKeyId OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The key ID of key chain."
    ::= { mgmtRouterConfigKeyChainKeyIdEntry 2 }

mgmtRouterConfigKeyChainKeyIdIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the key string is encrypted or not. TRUE means the
         key string is encrypted. FALSE means the key string is plain text."
    ::= { mgmtRouterConfigKeyChainKeyIdEntry 3 }

mgmtRouterConfigKeyChainKeyIdKeyString OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..224))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The key string."
    ::= { mgmtRouterConfigKeyChainKeyIdEntry 4 }

mgmtRouterConfigKeyChainKeyIdAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRouterConfigKeyChainKeyIdEntry 100 }

mgmtRouterConfigKeyChainKeyIdTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtRouterConfig 4 }

mgmtRouterConfigKeyChainKeyIdTableRowEditorKeyChainName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The key chain name."
    ::= { mgmtRouterConfigKeyChainKeyIdTableRowEditor 1 }

mgmtRouterConfigKeyChainKeyIdTableRowEditorKeyId OBJECT-TYPE
    SYNTAX      Integer32 (1..255)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The key ID of key chain."
    ::= { mgmtRouterConfigKeyChainKeyIdTableRowEditor 2 }

mgmtRouterConfigKeyChainKeyIdTableRowEditorIsEncrypted OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The flag indicates the key string is encrypted or not. TRUE means the
         key string is encrypted. FALSE means the key string is plain text."
    ::= { mgmtRouterConfigKeyChainKeyIdTableRowEditor 3 }

mgmtRouterConfigKeyChainKeyIdTableRowEditorKeyString OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..224))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The key string."
    ::= { mgmtRouterConfigKeyChainKeyIdTableRowEditor 4 }

mgmtRouterConfigKeyChainKeyIdTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRouterConfigKeyChainKeyIdTableRowEditor 100 }

mgmtRouterConfigAccessListTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRouterConfigAccessListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is router access-list configuration table."
    ::= { mgmtRouterConfig 5 }

mgmtRouterConfigAccessListEntry OBJECT-TYPE
    SYNTAX      MGMTRouterConfigAccessListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each access-list entry has a set of parameters."
    INDEX       { mgmtRouterConfigAccessListName,
                  mgmtRouterConfigAccessListMode,
                  mgmtRouterConfigAccessListNetwork,
                  mgmtRouterConfigAccessListIpSubnetMaskLength }
    ::= { mgmtRouterConfigAccessListTable 1 }

MGMTRouterConfigAccessListEntry ::= SEQUENCE {
    mgmtRouterConfigAccessListName                MGMTDisplayString,
    mgmtRouterConfigAccessListMode                MGMTRouterAccessListMode,
    mgmtRouterConfigAccessListNetwork             IpAddress,
    mgmtRouterConfigAccessListIpSubnetMaskLength  Integer32,
    mgmtRouterConfigAccessListAction              MGMTRowEditorState
}

mgmtRouterConfigAccessListName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Access-list name."
    ::= { mgmtRouterConfigAccessListEntry 1 }

mgmtRouterConfigAccessListMode OBJECT-TYPE
    SYNTAX      MGMTRouterAccessListMode
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The access right mode of the access-list entry."
    ::= { mgmtRouterConfigAccessListEntry 2 }

mgmtRouterConfigAccessListNetwork OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtRouterConfigAccessListEntry 3 }

mgmtRouterConfigAccessListIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtRouterConfigAccessListEntry 4 }

mgmtRouterConfigAccessListAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRouterConfigAccessListEntry 100 }

mgmtRouterConfigAccessListTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtRouterConfig 6 }

mgmtRouterConfigAccessListTableRowEditorName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Access-list name."
    ::= { mgmtRouterConfigAccessListTableRowEditor 1 }

mgmtRouterConfigAccessListTableRowEditorMode OBJECT-TYPE
    SYNTAX      MGMTRouterAccessListMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The access right mode of the access-list entry."
    ::= { mgmtRouterConfigAccessListTableRowEditor 2 }

mgmtRouterConfigAccessListTableRowEditorNetwork OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 network address."
    ::= { mgmtRouterConfigAccessListTableRowEditor 3 }

mgmtRouterConfigAccessListTableRowEditorIpSubnetMaskLength OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "IPv4 network mask length."
    ::= { mgmtRouterConfigAccessListTableRowEditor 4 }

mgmtRouterConfigAccessListTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtRouterConfigAccessListTableRowEditor 100 }

mgmtRouterStatus OBJECT IDENTIFIER
    ::= { mgmtRouterMibObjects 3 }

mgmtRouterStatusAccessListTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTRouterStatusAccessListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is router access-list configuration table."
    ::= { mgmtRouterStatus 2 }

mgmtRouterStatusAccessListEntry OBJECT-TYPE
    SYNTAX      MGMTRouterStatusAccessListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each access-list entry has a set of parameters."
    INDEX       { mgmtRouterStatusAccessListName,
                  mgmtRouterStatusAccessListRouterAccessListPrecedence }
    ::= { mgmtRouterStatusAccessListTable 1 }

MGMTRouterStatusAccessListEntry ::= SEQUENCE {
    mgmtRouterStatusAccessListName                        MGMTDisplayString,
    mgmtRouterStatusAccessListRouterAccessListPrecedence  Unsigned32,
    mgmtRouterStatusAccessListMode                        MGMTRouterAccessListMode,
    mgmtRouterStatusAccessListNetworkAddress              IpAddress,
    mgmtRouterStatusAccessListNetworkPrefixSize           Integer32
}

mgmtRouterStatusAccessListName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Access-list name."
    ::= { mgmtRouterStatusAccessListEntry 1 }

mgmtRouterStatusAccessListRouterAccessListPrecedence OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The precedence of router access-list entry."
    ::= { mgmtRouterStatusAccessListEntry 2 }

mgmtRouterStatusAccessListMode OBJECT-TYPE
    SYNTAX      MGMTRouterAccessListMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The access right mode of the access-list entry."
    ::= { mgmtRouterStatusAccessListEntry 3 }

mgmtRouterStatusAccessListNetworkAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IPv4 address of the access-list entry."
    ::= { mgmtRouterStatusAccessListEntry 4 }

mgmtRouterStatusAccessListNetworkPrefixSize OBJECT-TYPE
    SYNTAX      Integer32 (0..32)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The network prefix size of the access-list entry."
    ::= { mgmtRouterStatusAccessListEntry 5 }

mgmtRouterMibConformance OBJECT IDENTIFIER
    ::= { mgmtRouterMib 2 }

mgmtRouterMibCompliances OBJECT IDENTIFIER
    ::= { mgmtRouterMibConformance 1 }

mgmtRouterMibGroups OBJECT IDENTIFIER
    ::= { mgmtRouterMibConformance 2 }

mgmtRouterCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRouterCapabilitiesMaxKeyChainNameMaxCount,
                  mgmtRouterCapabilitiesMinKeyChainNameLen,
                  mgmtRouterCapabilitiesMaxKeyChainNameLen,
                  mgmtRouterCapabilitiesMinKeyChainKeyId,
                  mgmtRouterCapabilitiesMaxKeyChainKeyId,
                  mgmtRouterCapabilitiesMinKeyChainPlainTextKeyStringLen,
                  mgmtRouterCapabilitiesMaxKeyChainPlainTextKeyStringLen,
                  mgmtRouterCapabilitiesMinKeyChainKeyEncryptedStringLen,
                  mgmtRouterCapabilitiesMaxKeyChainKeyEncryptedStringLen,
                  mgmtRouterCapabilitiesAccessListMaxCount,
                  mgmtRouterCapabilitiesMinAccessListNameLen,
                  mgmtRouterCapabilitiesMaxAccessListNameLen,
                  mgmtRouterCapabilitiesMinAcePrecedence,
                  mgmtRouterCapabilitiesMaxAcePrecedence }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRouterMibGroups 1 }

mgmtRouterConfigKeyChainTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRouterConfigKeyChainKeyChainName,
                  mgmtRouterConfigKeyChainAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRouterMibGroups 2 }

mgmtRouterConfigKeyChainTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtRouterConfigKeyChainTableRowEditorKeyChainName,
                  mgmtRouterConfigKeyChainTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRouterMibGroups 3 }

mgmtRouterConfigKeyChainKeyIdTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRouterConfigKeyChainKeyIdKeyChainName,
                  mgmtRouterConfigKeyChainKeyIdKeyId,
                  mgmtRouterConfigKeyChainKeyIdIsEncrypted,
                  mgmtRouterConfigKeyChainKeyIdKeyString,
                  mgmtRouterConfigKeyChainKeyIdAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRouterMibGroups 4 }

mgmtRouterConfigKeyChainKeyIdTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtRouterConfigKeyChainKeyIdTableRowEditorKeyChainName,
                  mgmtRouterConfigKeyChainKeyIdTableRowEditorKeyId,
                  mgmtRouterConfigKeyChainKeyIdTableRowEditorIsEncrypted,
                  mgmtRouterConfigKeyChainKeyIdTableRowEditorKeyString,
                  mgmtRouterConfigKeyChainKeyIdTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRouterMibGroups 5 }

mgmtRouterConfigAccessListTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRouterConfigAccessListName,
                  mgmtRouterConfigAccessListMode,
                  mgmtRouterConfigAccessListNetwork,
                  mgmtRouterConfigAccessListIpSubnetMaskLength,
                  mgmtRouterConfigAccessListAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRouterMibGroups 6 }

mgmtRouterConfigAccessListTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRouterConfigAccessListTableRowEditorName,
                  mgmtRouterConfigAccessListTableRowEditorMode,
                  mgmtRouterConfigAccessListTableRowEditorNetwork,
                  mgmtRouterConfigAccessListTableRowEditorIpSubnetMaskLength,
                  mgmtRouterConfigAccessListTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRouterMibGroups 7 }

mgmtRouterStatusAccessListTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtRouterStatusAccessListName,
                  mgmtRouterStatusAccessListRouterAccessListPrecedence,
                  mgmtRouterStatusAccessListMode,
                  mgmtRouterStatusAccessListNetworkAddress,
                  mgmtRouterStatusAccessListNetworkPrefixSize }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtRouterMibGroups 8 }

mgmtRouterMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtRouterCapabilitiesInfoGroup,
                       mgmtRouterConfigKeyChainTableInfoGroup,
                       mgmtRouterConfigKeyChainTableRowEditorInfoGroup,
                       mgmtRouterConfigKeyChainKeyIdTableInfoGroup,
                       mgmtRouterConfigKeyChainKeyIdTableRowEditorInfoGroup,
                       mgmtRouterConfigAccessListTableInfoGroup,
                       mgmtRouterConfigAccessListTableRowEditorInfoGroup,
                       mgmtRouterStatusAccessListTableInfoGroup }

    ::= { mgmtRouterMibCompliances 1 }

END
