-- *****************************************************************
-- LLDP-MIB:  
-- ****************************************************************

MGMT-LLDP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInteger64 FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned64 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtLldpMib MODULE-IDENTITY
    LAST-UPDATED "201604070000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the LLDP MIB"
    REVISION    "201604070000Z"
    DESCRIPTION
        "Add SnmpNotificationEna to lldpConfig table"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 34 }


MGMTlldpAdminState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumerations the admin state."
    SYNTAX      INTEGER { disabled(0), txAndRx(1), txOnly(2),
                          rxOnly(3) }

MGMTlldpmedAltitudeType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumerations the altitude type."
    SYNTAX      INTEGER { undefined(0), meters(1), floors(2) }

MGMTlldpmedCivicAddressType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumerations the civic address type."
    SYNTAX      INTEGER { state(1), county(2), city(3), district(4),
                          block(5), street(6),
                          leadingStreetDirection(16),
                          trailingStreetSuffix(17), streetSuffix(18),
                          houseNo(19), houseNoSuffix(20),
                          landmark(21), additionalInfo(22), name(23),
                          zipCode(24), building(25), apartment(26),
                          floor(27), roomNumber(28), placeType(29),
                          postalCommunityName(30), poBox(31),
                          additionalCode(32) }

MGMTlldpmedDatumType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumerations the datum (geodetic system)."
    SYNTAX      INTEGER { undefined(0), wgs84(1), nad83navd88(2),
                          nad83mllw(3) }

MGMTlldpmedDeviceType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumerations the device type that the device shall operate as."
    SYNTAX      INTEGER { connectivity(0), endpoint(1) }

MGMTlldpmedRemoteDeviceType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumerations the remote neighbor's device type."
    SYNTAX      INTEGER { notDefined(0), endpointClassI(1),
                          endpointClassII(2), endpointClassIII(3),
                          networkConnectivity(4), reserved(5) }

MGMTlldpmedRemoteNetworkPolicyApplicationType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumerations the remote neighbor's network policy's application
         type."
    SYNTAX      INTEGER { voice(1), voiceSignaling(2), guestVoice(3),
                          guestVoiceSignaling(4), softphoneVoice(5),
                          videoConferencing(6), streamingVideo(7),
                          videoSignaling(8) }

mgmtLldpMibObjects OBJECT IDENTIFIER
    ::= { mgmtLldpMib 1 }

mgmtLldpConfig OBJECT IDENTIFIER
    ::= { mgmtLldpMibObjects 2 }

mgmtLldpConfigGlobal OBJECT IDENTIFIER
    ::= { mgmtLldpConfig 1 }

mgmtLldpConfigGlobalReInitDelay OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set the LLDP tx reinitialization delay in seconds. Valid range 1-10.
         
         When a port is disabled, LLDP is disabled or the switch is rebooted, a
         LLDP shutdown frame is transmitted to the neighboring units, signaling
         that the LLDP information isn't valid anymore.
         
         Tx reinitialization delay controls the amount of seconds between the
         shutdown frame and a new LLDP initialization."
    ::= { mgmtLldpConfigGlobal 1 }

mgmtLldpConfigGlobalMsgTxHold OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set the LLDP tx hold times . Valid range 2-10.
         
         Each LLDP frame contains information about how long time the
         information in the LLDP frame shall be considered valid. The LLDP
         information valid period is set to tx hold times multiplied by tx
         interval seconds."
    ::= { mgmtLldpConfigGlobal 2 }

mgmtLldpConfigGlobalMsgTxInterval OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set the LLDP tx interval in seconds.
         
         The switch periodically transmits LLDP frames to its neighbors for
         having the network discovery information up-to-date. The interval
         between each LLDP frame is determined by the tx Interval value.
         
         
         
         Valid range 5-32768 seconds."
    ::= { mgmtLldpConfigGlobal 3 }

mgmtLldpConfigGlobalTxDelay OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set the LLDP tx delay in seconds. Valid range 1-8192.
         
         If some configuration is changed (e.g. the IP address) a new LLDP frame
         is transmitted, but the time between the LLDP frames will always be at
         least the value of tx delay seconds.
         
         Note: tx Delay cannot be larger than 1/4 of the tx interval."
    ::= { mgmtLldpConfigGlobal 4 }

mgmtLldpConfigTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure LLDP configurations for a specific
         interface."
    ::= { mgmtLldpConfig 2 }

mgmtLldpConfigEntry OBJECT-TYPE
    SYNTAX      MGMTLldpConfigEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of control parameters"
    INDEX       { mgmtLldpConfigIfIndex }
    ::= { mgmtLldpConfigTable 1 }

MGMTLldpConfigEntry ::= SEQUENCE {
    mgmtLldpConfigIfIndex              MGMTInterfaceIndex,
    mgmtLldpConfigAdminState           MGMTlldpAdminState,
    mgmtLldpConfigCdpAware             TruthValue,
    mgmtLldpConfigOptionalTlvs         MGMTUnsigned8,
    mgmtLldpConfigSnmpNotificationEna  TruthValue
}

mgmtLldpConfigIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpConfigEntry 1 }

mgmtLldpConfigAdminState OBJECT-TYPE
    SYNTAX      MGMTlldpAdminState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Sets the LLDP admin state for the interface."
    ::= { mgmtLldpConfigEntry 3 }

mgmtLldpConfigCdpAware OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/disables CDP awareness for the interface.
         
         CDP is Cisco's equivalent to LLDP.
         
         
         
         The CDP operation is restricted to decoding incoming CDP frames (The
         switch doesn't transmit CDP frames). CDP frames are only decoded if
         LLDP on the port is enabled.
         
         
         
         Only CDP TLVs that can be mapped to a corresponding field in the LLDP
         neighbors' table are decoded. All other TLVs are discarded
         (Unrecognized CDP TLVs and discarded CDP frames are not shown in the
         LLDP statistics.).
         
         CDP TLVs are mapped onto LLDP neighbors' table as shown below.
         
         CDP TLV 'Device ID' is mapped to the LLDP 'Chassis ID' field.
         
         CDP TLV 'Address' is mapped to the LLDP 'Management Address' field. The
         CDP address TLV can contain multiple addresses, but only the first
         address is shown in the LLDP neighbors table.
         
         CDP TLV 'Port ID' is mapped to the LLDP 'Port ID' field.
         
         CDP TLV 'Version and Platform' is mapped to the LLDP 'System
         Description' field.
         
         Both the CDP and LLDP support 'system capabilities', but the CDP
         capabilities cover capabilities that are not part of the LLDP. These
         capabilities are shown as 'others' in the LLDP neighbors' table.
         
         
         
         If all ports have CDP awareness disabled the switch forwards CDP frames
         received from neighbor devices. If at least one port has CDP awareness
         enabled all CDP frames are terminated by the switch.
         
         
         
         Note: When CDP awareness on a port is disabled the CDP information
         isn't removed immediately, but gets removed when the hold time is
         exceeded."
    ::= { mgmtLldpConfigEntry 4 }

mgmtLldpConfigOptionalTlvs OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/Disables the LLDP optional TLVs. Bit mask, where setting the
         bit to 1 means enable transmission of the corresponding TLV.
         
         Bit 0 represents Port Description TLV.
         
         Bit 1 represents System Name TLV.
         
         Bit 2 represents System Description.
         
         Bit 3 represents System Capabilities TLV.
         
         Bit 4 represents Management Address TLV."
    ::= { mgmtLldpConfigEntry 5 }

mgmtLldpConfigSnmpNotificationEna OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/disable of SNMP Trap notification."
    ::= { mgmtLldpConfigEntry 6 }

mgmtLldpConfigMed OBJECT IDENTIFIER
    ::= { mgmtLldpConfig 3 }

mgmtLldpConfigMedTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpConfigMedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure LLDP MEDIA configurations for a specific
         interface."
    ::= { mgmtLldpConfigMed 1 }

mgmtLldpConfigMedEntry OBJECT-TYPE
    SYNTAX      MGMTLldpConfigMedEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of control parameters"
    INDEX       { mgmtLldpConfigMedIfIndex }
    ::= { mgmtLldpConfigMedTable 1 }

MGMTLldpConfigMedEntry ::= SEQUENCE {
    mgmtLldpConfigMedIfIndex       MGMTInterfaceIndex,
    mgmtLldpConfigMedOptionalTlvs  MGMTUnsigned8,
    mgmtLldpConfigMedDeviceType    MGMTlldpmedDeviceType
}

mgmtLldpConfigMedIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpConfigMedEntry 1 }

mgmtLldpConfigMedOptionalTlvs OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enables/Disables the LLDP optional TLVs. Bit mask, where setting the
         bit to 1 means
         
         enable transmission of the corresponding optional TLV.
         
         Bit 0 represents the capabilities TLV.
         
         Bit 1 represents the network Policy TLV.
         
         Bit 2 represents the location TLV.
         
         Bit 3 represents the PoE TLV."
    ::= { mgmtLldpConfigMedEntry 3 }

mgmtLldpConfigMedDeviceType OBJECT-TYPE
    SYNTAX      MGMTlldpmedDeviceType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting device type to configure the mode the device shall operate as."
    ::= { mgmtLldpConfigMedEntry 4 }

mgmtLldpConfigMedPolicyTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpConfigMedPolicyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to configure LLDP MED Policies for the device."
    ::= { mgmtLldpConfigMed 2 }

mgmtLldpConfigMedPolicyEntry OBJECT-TYPE
    SYNTAX      MGMTLldpConfigMedPolicyEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Network Policy Discovery enables the efficient discovery and diagnosis
         of mismatch issues with the VLAN configuration, along with the
         associated Layer 2 and Layer 3 attributes, which apply for a set of
         specific protocol applications on that port. Improper network policy
         configurations are a very significant issue in VoIP environments that
         frequently result in voice quality degradation or loss of service.
         Policies are only intended for use with applications that have specific
         'real-time' network policy requirements, such as interactive voice
         and/or video services."
    INDEX       { mgmtLldpConfigMedPolicyLldpmedPolicy }
    ::= { mgmtLldpConfigMedPolicyTable 1 }

MGMTLldpConfigMedPolicyEntry ::= SEQUENCE {
    mgmtLldpConfigMedPolicyLldpmedPolicy    Integer32,
    mgmtLldpConfigMedPolicyApplicationType  MGMTlldpmedRemoteNetworkPolicyApplicationType,
    mgmtLldpConfigMedPolicyTagged           TruthValue,
    mgmtLldpConfigMedPolicyVlanId           MGMTUnsigned16,
    mgmtLldpConfigMedPolicyL2Priority       MGMTUnsigned8,
    mgmtLldpConfigMedPolicyDscp             MGMTUnsigned8,
    mgmtLldpConfigMedPolicyAction           MGMTRowEditorState
}

mgmtLldpConfigMedPolicyLldpmedPolicy OBJECT-TYPE
    SYNTAX      Integer32 (0..31)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Policy index."
    ::= { mgmtLldpConfigMedPolicyEntry 1 }

mgmtLldpConfigMedPolicyApplicationType OBJECT-TYPE
    SYNTAX      MGMTlldpmedRemoteNetworkPolicyApplicationType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy application type."
    ::= { mgmtLldpConfigMedPolicyEntry 3 }

mgmtLldpConfigMedPolicyTagged OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy tagged flag. Defines if LLDP policy uses tagged VLAN."
    ::= { mgmtLldpConfigMedPolicyEntry 4 }

mgmtLldpConfigMedPolicyVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy VLAN ID. Only valid when policy 'Tagged' is TRUE"
    ::= { mgmtLldpConfigMedPolicyEntry 5 }

mgmtLldpConfigMedPolicyL2Priority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..7)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy L2 priority."
    ::= { mgmtLldpConfigMedPolicyEntry 6 }

mgmtLldpConfigMedPolicyDscp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..63)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy DSCP."
    ::= { mgmtLldpConfigMedPolicyEntry 7 }

mgmtLldpConfigMedPolicyAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtLldpConfigMedPolicyEntry 100 }

mgmtLldpConfigMedPolicyListTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpConfigMedPolicyListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface can be mapped to multiple policies. Set to TRUE in order
         to enable the corresponding policy to be transmitted at the interface.
         It is a requirement that the policy is defined."
    ::= { mgmtLldpConfigMed 3 }

mgmtLldpConfigMedPolicyListEntry OBJECT-TYPE
    SYNTAX      MGMTLldpConfigMedPolicyListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each location information has a control parameter"
    INDEX       { mgmtLldpConfigMedPolicyListIfIndex,
                  mgmtLldpConfigMedPolicyListLldpmedPolicy }
    ::= { mgmtLldpConfigMedPolicyListTable 1 }

MGMTLldpConfigMedPolicyListEntry ::= SEQUENCE {
    mgmtLldpConfigMedPolicyListIfIndex              MGMTInterfaceIndex,
    mgmtLldpConfigMedPolicyListLldpmedPolicy        Integer32,
    mgmtLldpConfigMedPolicyListLldpmedPoliciesList  TruthValue
}

mgmtLldpConfigMedPolicyListIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpConfigMedPolicyListEntry 1 }

mgmtLldpConfigMedPolicyListLldpmedPolicy OBJECT-TYPE
    SYNTAX      Integer32 (0..31)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Policy index."
    ::= { mgmtLldpConfigMedPolicyListEntry 3 }

mgmtLldpConfigMedPolicyListLldpmedPoliciesList OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE assign the corresponding policy index to the interface."
    ::= { mgmtLldpConfigMedPolicyListEntry 4 }

mgmtLldpConfigMedGlobal OBJECT IDENTIFIER
    ::= { mgmtLldpConfigMed 4 }

mgmtLldpConfigMedGlobalFastRepeatCount OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..10)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Number of times to repeat LLDP frame transmission at fast start
         
         Rapid startup and Emergency Call Service Location Identification
         Discovery of endpoints is a critically important aspect of VoIP systems
         in general. In addition, it is best to advertise only those pieces of
         information which are specifically relevant to particular endpoint
         types (for example only advertise the voice network policy to permitted
         voice-capable devices), both in order to conserve the limited LLDPU
         space and to reduce security and system integrity issues that can come
         with inappropriate knowledge of the network policy.
         
         With this in mind LLDP-MED defines an LLDP-MED Fast Start interaction
         between the protocol and the application layers on top of the protocol,
         in order to achieve these related properties. Initially, a Network
         Connectivity Device will only transmit LLDP TLVs in an LLDPDU. Only
         after an LLDP-MED Endpoint Device is detected, will an LLDP-MED capable
         Network Connectivity Device start to advertise LLDP-MED TLVs in
         outgoing LLDPDUs on the associated port. The LLDP-MED application will
         temporarily speed up the transmission of the LLDPDU to start within a
         second, when a new LLDP-MED neighbor has been detected in order share
         LLDP-MED information as fast as possible to new neighbors.
         
         Because there is a risk of an LLDP frame being lost during transmission
         between neighbors, it is recommended to repeat the fast start
         transmission multiple times to increase the possibility of the
         neighbors receiving the LLDP frame. With fast start repeat count it is
         possible to specify the number of times the fast start transmission
         would be repeated. The recommended value is 4 times, given that 4 LLDP
         frames with a 1 second interval will be transmitted, when an LLDP frame
         with new information is received.
         
         It should be noted that LLDP-MED and the LLDP-MED Fast Start mechanism
         is only intended to run on links between LLDP-MED Network Connectivity
         Devices and Endpoint Devices, and as such does not apply to links
         between LAN infrastructure elements, including Network Connectivity
         Devices, or other types of links."
    ::= { mgmtLldpConfigMedGlobal 1 }

mgmtLldpConfigMedGlobalLatitude OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Latitude degrees in 2s-complement as specified in RFC 3825. Positive
         numbers are north of the equator and negative numbers are south of the
         equator."
    ::= { mgmtLldpConfigMedGlobal 2 }

mgmtLldpConfigMedGlobalLongitude OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Longitude degrees in 2s-complement as specified in RFC 3825. Positive
         values are East of the prime meridian and negative numbers are West of
         the prime meridian."
    ::= { mgmtLldpConfigMedGlobal 3 }

mgmtLldpConfigMedGlobalAltitudeType OBJECT-TYPE
    SYNTAX      MGMTlldpmedAltitudeType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting altitude type
         
         Possible to select between two altitude types (floors or meters).
         Meters are representing meters of altitude defined by the vertical
         datum specified. Floors are representing altitude in a form more
         relevant in buildings which have different floor-to-floor dimensions.
         An altitude = 0.0 is meaningful even outside a building, and represents
         ground level at the given latitude and longitude. Inside a building,
         0.0 represents the floor level associated with ground level at the main
         entrance."
    ::= { mgmtLldpConfigMedGlobal 4 }

mgmtLldpConfigMedGlobalAltitude OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Altitude value in 2s-complement as specified in RFC 3825"
    ::= { mgmtLldpConfigMedGlobal 5 }

mgmtLldpConfigMedGlobalElinAddr OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..25))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Emergency Call Service ELIN identifier data format is defined to carry
         the ELIN identifier as used during emergency call setup to a
         traditional CAMA or ISDN trunk-based PSAP. This format consists of a
         numerical digit string, corresponding to the ELIN to be used for
         emergency calling. Maximum number of octets are 25."
    ::= { mgmtLldpConfigMedGlobal 6 }

mgmtLldpConfigMedGlobalDatum OBJECT-TYPE
    SYNTAX      MGMTlldpmedDatumType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Setting datum to configure the datum (geodetic system) to use.
         
         The Map Datum is used for the coordinates given in these options:
         
         WGS84: (Geographical 3D) - World Geodesic System 1984, CRS Code 4327,
         Prime Meridian Name: Greenwich.
         
         NAD83/NAVD88: North American Datum 1983, CRS Code 4269, Prime Meridian
         Name: Greenwich;The associated vertical datum is the North American
         Vertical Datum of 1988 (NAVD88).This datum pair is to be used when
         referencing locations on land, not near tidal water(which would use
         Datum = NAD83/MLLW).
         
         NAD83/MLLW: North American Datum 1983, CRS Code 4269, Prime Meridian
         Name: Greenwich; The associated vertical datum is Mean Lower Low Water
         (MLLW). This datum pair is to be used when referencing locations on
         water/sea/ocean."
    ::= { mgmtLldpConfigMedGlobal 7 }

mgmtLldpConfigMedGlobalCountryCode OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..2))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The two-letter ISO 3166 country code in capital ASCII letters -
         Example: DK, DE or US."
    ::= { mgmtLldpConfigMedGlobal 8 }

mgmtLldpConfigMedLocationInformationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpConfigMedLocationInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "The civic address location information. Each civic address can contain
         up to 250 characters, but the total amount of characters for the
         combined civic address locations must not exceed 250 bytes. Note: If an
         civic address location is non-empty it uses the amount of characters
         plus addition two characters. This is described in TIA1057, Section
         ********.2."
    ::= { mgmtLldpConfigMed 5 }

mgmtLldpConfigMedLocationInformationEntry OBJECT-TYPE
    SYNTAX      MGMTLldpConfigMedLocationInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each civic address type as defined in TIA1057, Section 3.4 in Annex B"
    INDEX       { mgmtLldpConfigMedLocationInformationLldpmedIndex }
    ::= { mgmtLldpConfigMedLocationInformationTable 1 }

MGMTLldpConfigMedLocationInformationEntry ::= SEQUENCE {
    mgmtLldpConfigMedLocationInformationLldpmedIndex  MGMTlldpmedCivicAddressType,
    mgmtLldpConfigMedLocationInformationCivicAddress  MGMTDisplayString
}

mgmtLldpConfigMedLocationInformationLldpmedIndex OBJECT-TYPE
    SYNTAX      MGMTlldpmedCivicAddressType
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Civic address type.
         
         1 - State/National subdivisions
         
         2 - County, parish, gun (JP), district (IN)
         
         3 - City, township
         
         4 - City division, borough, city district, ward, chou (JP)
         
         5 - Neighborhood, block
         
         6 - Street
         
         16 - Leading street direction
         
         17 - Trailing street direction
         
         18 - Street suffix
         
         19 - House number
         
         20 - House number suffix
         
         21 - Landmark or vanity address
         
         22 - Additional location information
         
         23 - Name
         
         24 - Postal/zip code
         
         25 - Building
         
         26 - Unit
         
         27 - Floor
         
         28 - Room
         
         29 - Place type
         
         30 - Postal
         
         31 - Post office
         
         32 - Additional code"
    ::= { mgmtLldpConfigMedLocationInformationEntry 2 }

mgmtLldpConfigMedLocationInformationCivicAddress OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Civic address"
    ::= { mgmtLldpConfigMedLocationInformationEntry 3 }

mgmtLldpConfigMedPolicyRowEditor OBJECT IDENTIFIER
    ::= { mgmtLldpConfigMed 6 }

mgmtLldpConfigMedPolicyRowEditorLldpmedPolicy OBJECT-TYPE
    SYNTAX      Integer32 (0..31)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Policy index."
    ::= { mgmtLldpConfigMedPolicyRowEditor 1 }

mgmtLldpConfigMedPolicyRowEditorApplicationType OBJECT-TYPE
    SYNTAX      MGMTlldpmedRemoteNetworkPolicyApplicationType
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy application type."
    ::= { mgmtLldpConfigMedPolicyRowEditor 3 }

mgmtLldpConfigMedPolicyRowEditorTagged OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy tagged flag. Defines if LLDP policy uses tagged VLAN."
    ::= { mgmtLldpConfigMedPolicyRowEditor 4 }

mgmtLldpConfigMedPolicyRowEditorVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16 (1..4095)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy VLAN ID. Only valid when policy 'Tagged' is TRUE"
    ::= { mgmtLldpConfigMedPolicyRowEditor 5 }

mgmtLldpConfigMedPolicyRowEditorL2Priority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..7)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy L2 priority."
    ::= { mgmtLldpConfigMedPolicyRowEditor 6 }

mgmtLldpConfigMedPolicyRowEditorDscp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..63)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The LLDP policy DSCP."
    ::= { mgmtLldpConfigMedPolicyRowEditor 7 }

mgmtLldpConfigMedPolicyRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtLldpConfigMedPolicyRowEditor 100 }

mgmtLldpStatus OBJECT IDENTIFIER
    ::= { mgmtLldpMibObjects 3 }

mgmtLldpStatusStatistics OBJECT IDENTIFIER
    ::= { mgmtLldpStatus 1 }

mgmtLldpStatusStatisticsGlobalCounters OBJECT IDENTIFIER
    ::= { mgmtLldpStatusStatistics 1 }

mgmtLldpStatusStatisticsGlobalCountersTableInserts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of new entries added since switch reboot."
    ::= { mgmtLldpStatusStatisticsGlobalCounters 1 }

mgmtLldpStatusStatisticsGlobalCountersTableDeletes OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of new entries deleted since switch reboot."
    ::= { mgmtLldpStatusStatisticsGlobalCounters 2 }

mgmtLldpStatusStatisticsGlobalCountersTableDrops OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of LLDP frames dropped due to the entry table being
         full."
    ::= { mgmtLldpStatusStatisticsGlobalCounters 3 }

mgmtLldpStatusStatisticsGlobalCountersTableAgeOuts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of entries deleted due to Time-To-Live expiring."
    ::= { mgmtLldpStatusStatisticsGlobalCounters 4 }

mgmtLldpStatusStatisticsGlobalCountersLastChangeTime OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the time when the last entry was last deleted or added. It also
         shows the time elapsed since the last change was detected."
    ::= { mgmtLldpStatusStatisticsGlobalCounters 5 }

mgmtLldpStatusStatisticsTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpStatusStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table represents the LLDP interface counters"
    ::= { mgmtLldpStatusStatistics 2 }

mgmtLldpStatusStatisticsEntry OBJECT-TYPE
    SYNTAX      MGMTLldpStatusStatisticsEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port interface has a set of statistics counters"
    INDEX       { mgmtLldpStatusStatisticsIfIndex }
    ::= { mgmtLldpStatusStatisticsTable 1 }

MGMTLldpStatusStatisticsEntry ::= SEQUENCE {
    mgmtLldpStatusStatisticsIfIndex           MGMTInterfaceIndex,
    mgmtLldpStatusStatisticsTxTotal           Unsigned32,
    mgmtLldpStatusStatisticsRxTotal           Unsigned32,
    mgmtLldpStatusStatisticsRxError           Unsigned32,
    mgmtLldpStatusStatisticsRxDiscarded       Unsigned32,
    mgmtLldpStatusStatisticsTLVsDiscarded     Unsigned32,
    mgmtLldpStatusStatisticsTLVsUnrecognized  Unsigned32,
    mgmtLldpStatusStatisticsTLVsOrgDiscarded  Unsigned32,
    mgmtLldpStatusStatisticsAgeOuts           Unsigned32
}

mgmtLldpStatusStatisticsIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpStatusStatisticsEntry 1 }

mgmtLldpStatusStatisticsTxTotal OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of LLDP frames transmitted."
    ::= { mgmtLldpStatusStatisticsEntry 2 }

mgmtLldpStatusStatisticsRxTotal OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of LLDP frames received."
    ::= { mgmtLldpStatusStatisticsEntry 3 }

mgmtLldpStatusStatisticsRxError OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of received LLDP frames containing some kind of error."
    ::= { mgmtLldpStatusStatisticsEntry 4 }

mgmtLldpStatusStatisticsRxDiscarded OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Show the number of LLDP frames discarded. If a LLDP frame is received
         at an interface, and the switch's internal table has run full, the LLDP
         frame is counted and discarded. This situation is known as 'Too Many
         Neighbors' in the LLDP standard. LLDP frames require a new entry in the
         table when the Chassis ID or Remote Port ID is not already contained
         within the table. Entries are removed from the table when a given
         interface's link is down, an LLDP shutdown frame is received, or when
         the entry ages out."
    ::= { mgmtLldpStatusStatisticsEntry 5 }

mgmtLldpStatusStatisticsTLVsDiscarded OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of TLVs discarded. Each LLDP frame can contain
         multiple pieces of information, known as TLVs (TLV is short for 'Type
         Length Value'). If a TLV is malformed, it is counted and discarded."
    ::= { mgmtLldpStatusStatisticsEntry 6 }

mgmtLldpStatusStatisticsTLVsUnrecognized OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of well-formed TLVs, but with an unknown type value."
    ::= { mgmtLldpStatusStatisticsEntry 7 }

mgmtLldpStatusStatisticsTLVsOrgDiscarded OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the number of well-formed TLVs, but with an organizationally TLV
         which is not supported."
    ::= { mgmtLldpStatusStatisticsEntry 8 }

mgmtLldpStatusStatisticsAgeOuts OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Show the number of age-outs. Each frame contains information about how
         long time the LLDP information is valid (age-out time). If no new LLDP
         frame is received within the age out time, the information is removed,
         and the counter is incremented."
    ::= { mgmtLldpStatusStatisticsEntry 9 }

mgmtLldpStatusNeighborsInformationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpStatusNeighborsInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to show the LLDP neighbors information for a specific
         interface."
    ::= { mgmtLldpStatus 2 }

mgmtLldpStatusNeighborsInformationEntry OBJECT-TYPE
    SYNTAX      MGMTLldpStatusNeighborsInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of neighbors information"
    INDEX       { mgmtLldpStatusNeighborsInformationIfIndex,
                  mgmtLldpStatusNeighborsInformationLldpmedIndex }
    ::= { mgmtLldpStatusNeighborsInformationTable 1 }

MGMTLldpStatusNeighborsInformationEntry ::= SEQUENCE {
    mgmtLldpStatusNeighborsInformationIfIndex                   MGMTInterfaceIndex,
    mgmtLldpStatusNeighborsInformationLldpmedIndex              Integer32,
    mgmtLldpStatusNeighborsInformationChassisId                 MGMTDisplayString,
    mgmtLldpStatusNeighborsInformationPortId                    MGMTDisplayString,
    mgmtLldpStatusNeighborsInformationPortDescription           MGMTDisplayString,
    mgmtLldpStatusNeighborsInformationSystemName                MGMTDisplayString,
    mgmtLldpStatusNeighborsInformationSystemDescription         MGMTDisplayString,
    mgmtLldpStatusNeighborsInformationSystemCapabilities        MGMTUnsigned16,
    mgmtLldpStatusNeighborsInformationSystemCapabilitiesEnable  MGMTUnsigned16
}

mgmtLldpStatusNeighborsInformationIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpStatusNeighborsInformationEntry 1 }

mgmtLldpStatusNeighborsInformationLldpmedIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..192)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Neighbor information table entry index."
    ::= { mgmtLldpStatusNeighborsInformationEntry 2 }

mgmtLldpStatusNeighborsInformationChassisId OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's chassis Id."
    ::= { mgmtLldpStatusNeighborsInformationEntry 4 }

mgmtLldpStatusNeighborsInformationPortId OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's port id."
    ::= { mgmtLldpStatusNeighborsInformationEntry 5 }

mgmtLldpStatusNeighborsInformationPortDescription OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's port description."
    ::= { mgmtLldpStatusNeighborsInformationEntry 6 }

mgmtLldpStatusNeighborsInformationSystemName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's system name."
    ::= { mgmtLldpStatusNeighborsInformationEntry 7 }

mgmtLldpStatusNeighborsInformationSystemDescription OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's system description."
    ::= { mgmtLldpStatusNeighborsInformationEntry 8 }

mgmtLldpStatusNeighborsInformationSystemCapabilities OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's system capabilities as bit mask.
         
         If the bit is set, it means that the functionality is supported by the
         neighbor system.
         
         Bit 0 represents Other.
         
         Bit 1 represents Repeater.
         
         Bit 2 represents Bridge.
         
         Bit 3 represents WLAN Access Point.
         
         Bit 4 represents Router.
         
         Bit 5 represents Telephone.
         
         Bit 6 represents DOCSIS cable device.
         
         Bit 7 represents Station Only.
         
         Bit 8 represents Reserved."
    ::= { mgmtLldpStatusNeighborsInformationEntry 9 }

mgmtLldpStatusNeighborsInformationSystemCapabilitiesEnable OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's system capabilities which is enabled.
         
         If the bit is set, it means that the functionality is currently enabled
         at the neighbor system.
         
         Bit 0 represents Other.
         
         Bit 1 represents Repeater.
         
         Bit 2 represents Bridge.
         
         Bit 3 represents WLAN Access Point.
         
         Bit 4 represents Router.
         
         Bit 5 represents Telephone.
         
         Bit 6 represents DOCSIS cable device.
         
         Bit 7 represents Station Only.
         
         Bit 8 represents Reserved."
    ::= { mgmtLldpStatusNeighborsInformationEntry 10 }

mgmtLldpStatusNeighborsMgmtInformationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpStatusNeighborsMgmtInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to show the LLDP neighbors information for a specific
         interface."
    ::= { mgmtLldpStatus 3 }

mgmtLldpStatusNeighborsMgmtInformationEntry OBJECT-TYPE
    SYNTAX      MGMTLldpStatusNeighborsMgmtInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of neighbors information"
    INDEX       { mgmtLldpStatusNeighborsMgmtInformationIfIndex,
                  mgmtLldpStatusNeighborsMgmtInformationLldpmedIndex,
                  mgmtLldpStatusNeighborsMgmtInformationLldpManagement }
    ::= { mgmtLldpStatusNeighborsMgmtInformationTable 1 }

MGMTLldpStatusNeighborsMgmtInformationEntry ::= SEQUENCE {
    mgmtLldpStatusNeighborsMgmtInformationIfIndex                     MGMTInterfaceIndex,
    mgmtLldpStatusNeighborsMgmtInformationLldpmedIndex                Integer32,
    mgmtLldpStatusNeighborsMgmtInformationLldpManagement              Integer32,
    mgmtLldpStatusNeighborsMgmtInformationSystemMgmAddressSubtype     MGMTUnsigned8,
    mgmtLldpStatusNeighborsMgmtInformationSystemMgmtAddress           MGMTDisplayString,
    mgmtLldpStatusNeighborsMgmtInformationSystemMgmtInterfaceSubtype  Integer32,
    mgmtLldpStatusNeighborsMgmtInformationSystemMgmtInterface         Integer32,
    mgmtLldpStatusNeighborsMgmtInformationSystemMgmtOid               OBJECT IDENTIFIER
}

mgmtLldpStatusNeighborsMgmtInformationIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpStatusNeighborsMgmtInformationEntry 1 }

mgmtLldpStatusNeighborsMgmtInformationLldpmedIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..192)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Neighbor information table entry index."
    ::= { mgmtLldpStatusNeighborsMgmtInformationEntry 2 }

mgmtLldpStatusNeighborsMgmtInformationLldpManagement OBJECT-TYPE
    SYNTAX      Integer32 (0..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Neighbor management information table entry index."
    ::= { mgmtLldpStatusNeighborsMgmtInformationEntry 4 }

mgmtLldpStatusNeighborsMgmtInformationSystemMgmAddressSubtype OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "LLDP neighbor's management address subtype , section *******
         IEEE802.1AB-2005."
    ::= { mgmtLldpStatusNeighborsMgmtInformationEntry 5 }

mgmtLldpStatusNeighborsMgmtInformationSystemMgmtAddress OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "LLDP neighbor's management address string, section *******
         IEEE802.1AB-2005."
    ::= { mgmtLldpStatusNeighborsMgmtInformationEntry 6 }

mgmtLldpStatusNeighborsMgmtInformationSystemMgmtInterfaceSubtype OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Interface numbering subtype, section ******* IEEE802.1AB-2005."
    ::= { mgmtLldpStatusNeighborsMgmtInformationEntry 7 }

mgmtLldpStatusNeighborsMgmtInformationSystemMgmtInterface OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Interface number, section ******* IEEE802.1AB-2005."
    ::= { mgmtLldpStatusNeighborsMgmtInformationEntry 8 }

mgmtLldpStatusNeighborsMgmtInformationSystemMgmtOid OBJECT-TYPE
    SYNTAX      OBJECT IDENTIFIER
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Object identifier, section ******* IEEE802.1AB-2005."
    ::= { mgmtLldpStatusNeighborsMgmtInformationEntry 9 }

mgmtLldpStatusPreemptInformationTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpStatusPreemptInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to show the LLDP Frame Preemption information for a
         specific interface."
    ::= { mgmtLldpStatus 4 }

mgmtLldpStatusPreemptInformationEntry OBJECT-TYPE
    SYNTAX      MGMTLldpStatusPreemptInformationEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a Frame preemption information"
    INDEX       { mgmtLldpStatusPreemptInformationIfIndex,
                  mgmtLldpStatusPreemptInformationLldpmedIndex,
                  mgmtLldpStatusPreemptInformationLldpManagement }
    ::= { mgmtLldpStatusPreemptInformationTable 1 }

MGMTLldpStatusPreemptInformationEntry ::= SEQUENCE {
    mgmtLldpStatusPreemptInformationIfIndex                   MGMTInterfaceIndex,
    mgmtLldpStatusPreemptInformationLldpmedIndex              Integer32,
    mgmtLldpStatusPreemptInformationLldpManagement            Integer32,
    mgmtLldpStatusPreemptInformationLocPreemptSupported       TruthValue,
    mgmtLldpStatusPreemptInformationLocPreemptEnabled         TruthValue,
    mgmtLldpStatusPreemptInformationLocPreemptActive          TruthValue,
    mgmtLldpStatusPreemptInformationLocAddFragSize            MGMTUnsigned8,
    mgmtLldpStatusPreemptInformationRemFramePreemptSupported  TruthValue,
    mgmtLldpStatusPreemptInformationRemFramePreemptEnabled    TruthValue,
    mgmtLldpStatusPreemptInformationRemFramePreemptActive     TruthValue,
    mgmtLldpStatusPreemptInformationRemFrameAddFragSize       MGMTUnsigned8
}

mgmtLldpStatusPreemptInformationIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpStatusPreemptInformationEntry 1 }

mgmtLldpStatusPreemptInformationLldpmedIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..192)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Neighbor information table entry index."
    ::= { mgmtLldpStatusPreemptInformationEntry 2 }

mgmtLldpStatusPreemptInformationLldpManagement OBJECT-TYPE
    SYNTAX      Integer32 (0..1)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Neighbor management information table entry index."
    ::= { mgmtLldpStatusPreemptInformationEntry 4 }

mgmtLldpStatusPreemptInformationLocPreemptSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the 802.3br LocPreemptSupported parameter for the port.
         The value is TRUE when preemption is supported on the port, and FALSE
         otherwise."
    ::= { mgmtLldpStatusPreemptInformationEntry 5 }

mgmtLldpStatusPreemptInformationLocPreemptEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the 802.3br LocPreemptEnabled parameter for the port. The
         value is TRUE when preemption is enabled on the port, and FALSE
         otherwise."
    ::= { mgmtLldpStatusPreemptInformationEntry 6 }

mgmtLldpStatusPreemptInformationLocPreemptActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the 802.3br LocPreemptActive parameter for the port. The
         value is TRUE when preemption is operationally active on the port, and
         FALSE otherwise."
    ::= { mgmtLldpStatusPreemptInformationEntry 7 }

mgmtLldpStatusPreemptInformationLocAddFragSize OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the 802.3br LocAddFragSize parameter for the port. The
         minimum size of non-final fragments supported by the receiver on the
         local port. This value is expressed in units of 64 octets of additional
         fragment length. The minimum non-final fragment size is:
         (LocAddFragSize + 1) * 64 octets"
    ::= { mgmtLldpStatusPreemptInformationEntry 8 }

mgmtLldpStatusPreemptInformationRemFramePreemptSupported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the 802.3br RemPreemptSupported parameter for the port.
         The value is TRUE when preemption is supported on the port, and FALSE
         otherwise."
    ::= { mgmtLldpStatusPreemptInformationEntry 9 }

mgmtLldpStatusPreemptInformationRemFramePreemptEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the 802.3br RemPreemptEnabled parameter for the port. The
         value is TRUE when preemption is enabled on the port, and FALSE
         otherwise."
    ::= { mgmtLldpStatusPreemptInformationEntry 10 }

mgmtLldpStatusPreemptInformationRemFramePreemptActive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the 802.3br RemPreemptActive parameter for the port. The
         value is TRUE when preemption is operationally active on the port, and
         FALSE otherwise."
    ::= { mgmtLldpStatusPreemptInformationEntry 11 }

mgmtLldpStatusPreemptInformationRemFrameAddFragSize OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The value of the 802.3br RemAddFragSize parameter for the port. The
         minimum size of non-final fragments supported by the receiver on the
         local port. This value is expressed in units of 64 octets of additional
         fragment length. The minimum non-final fragment size is:
         (LocAddFragSize + 1) * 64 octets"
    ::= { mgmtLldpStatusPreemptInformationEntry 12 }

mgmtLldpStatusMed OBJECT IDENTIFIER
    ::= { mgmtLldpStatus 5 }

mgmtLldpStatusMedRemoteDeviceInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpStatusMedRemoteDeviceInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to show the LLDP neighbors information for a specific
         interface."
    ::= { mgmtLldpStatusMed 1 }

mgmtLldpStatusMedRemoteDeviceInfoEntry OBJECT-TYPE
    SYNTAX      MGMTLldpStatusMedRemoteDeviceInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of neighbors information"
    INDEX       { mgmtLldpStatusMedRemoteDeviceInfoIfIndex,
                  mgmtLldpStatusMedRemoteDeviceInfoLldpmedIndex }
    ::= { mgmtLldpStatusMedRemoteDeviceInfoTable 1 }

MGMTLldpStatusMedRemoteDeviceInfoEntry ::= SEQUENCE {
    mgmtLldpStatusMedRemoteDeviceInfoIfIndex              MGMTInterfaceIndex,
    mgmtLldpStatusMedRemoteDeviceInfoLldpmedIndex         Integer32,
    mgmtLldpStatusMedRemoteDeviceInfoCapabilities         MGMTUnsigned16,
    mgmtLldpStatusMedRemoteDeviceInfoCapabilitiesEnabled  MGMTUnsigned16,
    mgmtLldpStatusMedRemoteDeviceInfoLatitude             MGMTInteger64,
    mgmtLldpStatusMedRemoteDeviceInfoLongitude            MGMTInteger64,
    mgmtLldpStatusMedRemoteDeviceInfoAltitudeType         MGMTlldpmedAltitudeType,
    mgmtLldpStatusMedRemoteDeviceInfoAltitude             Integer32,
    mgmtLldpStatusMedRemoteDeviceInfoDatum                MGMTlldpmedDatumType,
    mgmtLldpStatusMedRemoteDeviceInfoElinaddr             MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceInfoDeviceType           MGMTlldpmedRemoteDeviceType,
    mgmtLldpStatusMedRemoteDeviceInfoHwRev                MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceInfoFwRev                MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceInfoSwRev                MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceInfoSerialNo             MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceInfoManufacturerName     MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceInfoModelName            MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceInfoAssetId              MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceInfoEeeRxTwSys           MGMTUnsigned16,
    mgmtLldpStatusMedRemoteDeviceInfoEeeTxTwSys           MGMTUnsigned16,
    mgmtLldpStatusMedRemoteDeviceInfoEeeFbTwSys           MGMTUnsigned16,
    mgmtLldpStatusMedRemoteDeviceInfoEeeTxTwSysEcho       MGMTUnsigned16,
    mgmtLldpStatusMedRemoteDeviceInfoEeeRxTwSysEcho       MGMTUnsigned16
}

mgmtLldpStatusMedRemoteDeviceInfoIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 1 }

mgmtLldpStatusMedRemoteDeviceInfoLldpmedIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..192)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Neighbor information table entry index."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 2 }

mgmtLldpStatusMedRemoteDeviceInfoCapabilities OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "LLDP neighbor's capabilities bit mask.
         
         Bit 0 represents LLDP-MED capabilities.
         
         Bit 1 represents Network Policy.
         
         Bit 2 represents Location Identification.
         
         Bit 3 represents Extended Power via MDI - PSE.
         
         Bit 4 represents Extended Power via MDI - PD
         
         Bit 5 represents Inventory
         
         "
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 4 }

mgmtLldpStatusMedRemoteDeviceInfoCapabilitiesEnabled OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "LLDP neighbor's capabilities bit mask for the capabilities which are
         currently enabled.
         
         Bit 0 represents LLDP-MED capabilities.
         
         Bit 1 represents Network Policy.
         
         Bit 2 represents Location Identification.
         
         Bit 3 represents Extended Power via MDI - PSE.
         
         Bit 4 represents Extended Power via MDI - PD
         
         Bit 5 represents Inventory
         
         "
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 5 }

mgmtLldpStatusMedRemoteDeviceInfoLatitude OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Latitude degrees in 2s-complement as specified in RFC 3825. Positive
         numbers are north of the equator and negative numbers are south of the
         equator."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 6 }

mgmtLldpStatusMedRemoteDeviceInfoLongitude OBJECT-TYPE
    SYNTAX      MGMTInteger64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Longitude degrees in 2s-complement as specified in RFC 3825. Positive
         values are East of the prime meridian and negative numbers are West of
         the prime meridian."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 7 }

mgmtLldpStatusMedRemoteDeviceInfoAltitudeType OBJECT-TYPE
    SYNTAX      MGMTlldpmedAltitudeType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Altitude type as either floors or meters. Meters are representing
         meters of altitude defined by the vertical datum specified. Floors are
         representing altitude in a form more relevant in buildings which have
         different floor-to-floor dimensions. An altitude = 0.0 is meaningful
         even outside a building, and represents ground level at the given
         latitude and longitude. Inside a building, 0.0 represents the floor
         level associated with ground level at the main entrance."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 8 }

mgmtLldpStatusMedRemoteDeviceInfoAltitude OBJECT-TYPE
    SYNTAX      Integer32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Altitude value in 2s-complement as specified in RFC 3825"
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 9 }

mgmtLldpStatusMedRemoteDeviceInfoDatum OBJECT-TYPE
    SYNTAX      MGMTlldpmedDatumType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Datum (geodetic system) .
         
         The Map Datum is used for the coordinates given in these options:
         
         WGS84: (Geographical 3D) - World Geodesic System 1984, CRS Code 4327,
         Prime Meridian Name: Greenwich.
         
         NAD83/NAVD88: North American Datum 1983, CRS Code 4269, Prime Meridian
         Name: Greenwich; The associated vertical datum is the North American
         Vertical Datum of 1988 (NAVD88). This datum pair is to be used when
         referencing locations on land, not near tidal water(which would use
         Datum = NAD83/MLLW).
         
         NAD83/MLLW: North American Datum 1983, CRS Code 4269, Prime Meridian
         Name: Greenwich; The associated vertical datum is Mean Lower Low Water
         (MLLW). This datum pair is to be used when referencing locations on
         water/sea/ocean."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 10 }

mgmtLldpStatusMedRemoteDeviceInfoElinaddr OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..25))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Emergency Call Service ELIN identifier data format is defined to carry
         the ELIN identifier as used during emergency call setup to a
         traditional CAMA or ISDN trunk-based PSAP. This format consists of a
         numerical digit string, corresponding to the ELIN to be used for
         emergency calling. Maximum number of octets are 25."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 11 }

mgmtLldpStatusMedRemoteDeviceInfoDeviceType OBJECT-TYPE
    SYNTAX      MGMTlldpmedRemoteDeviceType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's device type."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 12 }

mgmtLldpStatusMedRemoteDeviceInfoHwRev OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's hardware revision."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 13 }

mgmtLldpStatusMedRemoteDeviceInfoFwRev OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's firmware revision."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 14 }

mgmtLldpStatusMedRemoteDeviceInfoSwRev OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's software revision."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 15 }

mgmtLldpStatusMedRemoteDeviceInfoSerialNo OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's serial number."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 16 }

mgmtLldpStatusMedRemoteDeviceInfoManufacturerName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's manufacturer name."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 17 }

mgmtLldpStatusMedRemoteDeviceInfoModelName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's model name."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 18 }

mgmtLldpStatusMedRemoteDeviceInfoAssetId OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..31))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's asset id."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 19 }

mgmtLldpStatusMedRemoteDeviceInfoEeeRxTwSys OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's Receive tw_sys_rx . Tw_sys_rx is defined as
         the time (expressed in microseconds) that the transmitting link partner
         will wait before it starts transmitting data after leaving the Low
         Power Idle (LPI) mode. Section ********, IEEE802.3az."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 20 }

mgmtLldpStatusMedRemoteDeviceInfoEeeTxTwSys OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's Transmit Tw_sys_tx . Tw_sys_tx is defined as
         the time (expressed in microseconds) that the receiving link partner is
         requesting the transmitting link partner to wait before starting the
         transmission data following the Low Power Idle (LPI) mode. Section
         ********, IEEE802.3az."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 21 }

mgmtLldpStatusMedRemoteDeviceInfoEeeFbTwSys OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's Fallback tw_sys_tx. A receiving link partner
         may inform the transmitter of an alternate desired Tw_sys_tx. Since a
         receiving link partner is likely to have discrete levels for savings,
         this provides the transmitter with additional information that it may
         use for a more efficient allocation. Section ********, IEEE802.3az."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 22 }

mgmtLldpStatusMedRemoteDeviceInfoEeeTxTwSysEcho OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's echo transmit Tw. The respective echo values
         shall be defined as the local link partners reflection (echo) of the
         remote link partners respective values. When a local link partner
         receives its echoed values from the remote link partner it can
         determine whether or not the remote link partner has received,
         registered, and processed its most recent values. Section ********,
         IEEE802.3az."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 23 }

mgmtLldpStatusMedRemoteDeviceInfoEeeRxTwSysEcho OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's echo receive Tw. The respective echo values
         shall be defined as the local link partners reflection (echo) of the
         remote link partners respective values. When a local link partner
         receives its echoed values from the remote link partner it can
         determine whether or not the remote link partner has received,
         registered, and processed its most recent values. Section ********,
         IEEE802.3az."
    ::= { mgmtLldpStatusMedRemoteDeviceInfoEntry 24 }

mgmtLldpStatusMedRemoteDeviceLocInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpStatusMedRemoteDeviceLocInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to show the LLDP-MED remote device (neighbor) civic
         location information for a specific interface."
    ::= { mgmtLldpStatusMed 2 }

mgmtLldpStatusMedRemoteDeviceLocInfoEntry OBJECT-TYPE
    SYNTAX      MGMTLldpStatusMedRemoteDeviceLocInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of neighbors information"
    INDEX       { mgmtLldpStatusMedRemoteDeviceLocInfoIfIndex,
                  mgmtLldpStatusMedRemoteDeviceLocInfoLldpmedIndex }
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoTable 1 }

MGMTLldpStatusMedRemoteDeviceLocInfoEntry ::= SEQUENCE {
    mgmtLldpStatusMedRemoteDeviceLocInfoIfIndex                 MGMTInterfaceIndex,
    mgmtLldpStatusMedRemoteDeviceLocInfoLldpmedIndex            Integer32,
    mgmtLldpStatusMedRemoteDeviceLocInfoState                   MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoCounty                  MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoCity                    MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoDistrict                MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoBlock                   MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoStreet                  MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoLeadingStreetDirection  MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoTrailingStreetSuffix    MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoStreetSuffix            MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoHouseNo                 MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoHouseNoSuffix           MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoLandmark                MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoAdditionalInfo          MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoName                    MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoZipCode                 MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoBuilding                MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoApartment               MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoFloor                   MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoRoomNumber              MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoPlaceType               MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoPostalCommunityName     MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoPoBox                   MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoAdditionalCode          MGMTDisplayString,
    mgmtLldpStatusMedRemoteDeviceLocInfoCountryCode             MGMTDisplayString
}

mgmtLldpStatusMedRemoteDeviceLocInfoIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 1 }

mgmtLldpStatusMedRemoteDeviceLocInfoLldpmedIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..192)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Neighbor information table entry index."
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 2 }

mgmtLldpStatusMedRemoteDeviceLocInfoState OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "National subdivision"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 5 }

mgmtLldpStatusMedRemoteDeviceLocInfoCounty OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "County"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 6 }

mgmtLldpStatusMedRemoteDeviceLocInfoCity OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "City"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 7 }

mgmtLldpStatusMedRemoteDeviceLocInfoDistrict OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "City district"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 8 }

mgmtLldpStatusMedRemoteDeviceLocInfoBlock OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Block (Neighborhood)"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 9 }

mgmtLldpStatusMedRemoteDeviceLocInfoStreet OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Street"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 10 }

mgmtLldpStatusMedRemoteDeviceLocInfoLeadingStreetDirection OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Street Direction"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 11 }

mgmtLldpStatusMedRemoteDeviceLocInfoTrailingStreetSuffix OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Trailing Street Suffix"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 12 }

mgmtLldpStatusMedRemoteDeviceLocInfoStreetSuffix OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Street Suffix"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 13 }

mgmtLldpStatusMedRemoteDeviceLocInfoHouseNo OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "House No."
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 14 }

mgmtLldpStatusMedRemoteDeviceLocInfoHouseNoSuffix OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "House No. Suffix"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 15 }

mgmtLldpStatusMedRemoteDeviceLocInfoLandmark OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Landmark"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 16 }

mgmtLldpStatusMedRemoteDeviceLocInfoAdditionalInfo OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Additional Location Info"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 17 }

mgmtLldpStatusMedRemoteDeviceLocInfoName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Name"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 18 }

mgmtLldpStatusMedRemoteDeviceLocInfoZipCode OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Zip code"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 19 }

mgmtLldpStatusMedRemoteDeviceLocInfoBuilding OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Building"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 20 }

mgmtLldpStatusMedRemoteDeviceLocInfoApartment OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Apartment/unit"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 21 }

mgmtLldpStatusMedRemoteDeviceLocInfoFloor OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Floor"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 22 }

mgmtLldpStatusMedRemoteDeviceLocInfoRoomNumber OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Room Number"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 23 }

mgmtLldpStatusMedRemoteDeviceLocInfoPlaceType OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Place type"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 24 }

mgmtLldpStatusMedRemoteDeviceLocInfoPostalCommunityName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Postal Community Name"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 25 }

mgmtLldpStatusMedRemoteDeviceLocInfoPoBox OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Post Office Box"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 26 }

mgmtLldpStatusMedRemoteDeviceLocInfoAdditionalCode OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..250))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Additional Code"
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 27 }

mgmtLldpStatusMedRemoteDeviceLocInfoCountryCode OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..2))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The two-letter ISO 3166 country code in capital ASCII letters -
         Example: DK, DE or US."
    ::= { mgmtLldpStatusMedRemoteDeviceLocInfoEntry 28 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to show the LLDP-MED remote device (neighbor) network
         policies information for a specific interface."
    ::= { mgmtLldpStatusMed 3 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry OBJECT-TYPE
    SYNTAX      MGMTLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of neighbors information"
    INDEX       {                   mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoIfIndex,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoLldpmedIndex,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoLldpmedPolicy }
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoTable 1 }

MGMTLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry ::= SEQUENCE {
    mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoIfIndex          MGMTInterfaceIndex,
    mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoLldpmedIndex     Integer32,
    mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoLldpmedPolicy    Integer32,
    mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoApplicationType  MGMTlldpmedRemoteNetworkPolicyApplicationType,
    mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoUnknownPolicy    TruthValue,
    mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoTagged           TruthValue,
    mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoVlanId           MGMTUnsigned16,
    mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoL2Priority       MGMTUnsigned8,
    mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoDscp             MGMTUnsigned8
}

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry 1 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoLldpmedIndex OBJECT-TYPE
    SYNTAX      Integer32 (0..192)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Neighbor information table entry index."
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry 2 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoLldpmedPolicy OBJECT-TYPE
    SYNTAX      Integer32 (0..31)
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Policy index."
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry 3 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoApplicationType OBJECT-TYPE
    SYNTAX      MGMTlldpmedRemoteNetworkPolicyApplicationType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The LLDP policy application type."
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry 5 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoUnknownPolicy OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Policy indicates that an Endpoint Device wants to explicitly advertise
         that the policy is required by the device. Can be either Defined or
         Unknown
         
         Unknown: The network policy for the specified application type is
         currently unknown.
         
         Defined: The network policy is defined (known)."
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry 6 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoTagged OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Defines if LLDP policy uses tagged VLAN."
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry 7 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoVlanId OBJECT-TYPE
    SYNTAX      MGMTUnsigned16 (1..4095)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The policy VLAN ID. Only valid when policy 'Tagged' is TRUE"
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry 8 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoL2Priority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..7)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the LLDP neighbor's policy L2 priority."
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry 9 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoDscp OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..63)
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "LLDP policy DSCP."
    ::= { mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoEntry 10 }

mgmtLldpControl OBJECT IDENTIFIER
    ::= { mgmtLldpMibObjects 4 }

mgmtLldpControlStatisticsClear OBJECT IDENTIFIER
    ::= { mgmtLldpControl 1 }

mgmtLldpControlStatisticsClearTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLldpControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to clear LLDP statistics for a specific interface."
    ::= { mgmtLldpControlStatisticsClear 1 }

mgmtLldpControlStatisticsClearEntry OBJECT-TYPE
    SYNTAX      MGMTLldpControlStatisticsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of statistics counters"
    INDEX       { mgmtLldpControlStatisticsClearIfIndex }
    ::= { mgmtLldpControlStatisticsClearTable 1 }

MGMTLldpControlStatisticsClearEntry ::= SEQUENCE {
    mgmtLldpControlStatisticsClearIfIndex          MGMTInterfaceIndex,
    mgmtLldpControlStatisticsClearStatisticsClear  TruthValue
}

mgmtLldpControlStatisticsClearIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number index."
    ::= { mgmtLldpControlStatisticsClearEntry 1 }

mgmtLldpControlStatisticsClearStatisticsClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to clear the LLDP statistics of an interface."
    ::= { mgmtLldpControlStatisticsClearEntry 2 }

mgmtLldpControlStatisticsClearGlobal OBJECT IDENTIFIER
    ::= { mgmtLldpControlStatisticsClear 2 }

mgmtLldpControlStatisticsClearGlobalClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to TRUE to clear the LLDP global statistics."
    ::= { mgmtLldpControlStatisticsClearGlobal 1 }

mgmtLldpMibConformance OBJECT IDENTIFIER
    ::= { mgmtLldpMib 2 }

mgmtLldpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtLldpMibConformance 1 }

mgmtLldpMibGroups OBJECT IDENTIFIER
    ::= { mgmtLldpMibConformance 2 }

mgmtLldpConfigGlobalInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpConfigGlobalReInitDelay,
                  mgmtLldpConfigGlobalMsgTxHold,
                  mgmtLldpConfigGlobalMsgTxInterval,
                  mgmtLldpConfigGlobalTxDelay }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 1 }

mgmtLldpConfigInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpConfigIfIndex, mgmtLldpConfigAdminState,
                  mgmtLldpConfigCdpAware, mgmtLldpConfigOptionalTlvs,
                  mgmtLldpConfigSnmpNotificationEna }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 2 }

mgmtLldpConfigMedInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpConfigMedIfIndex,
                  mgmtLldpConfigMedOptionalTlvs,
                  mgmtLldpConfigMedDeviceType }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 3 }

mgmtLldpConfigMedPolicyInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpConfigMedPolicyLldpmedPolicy,
                  mgmtLldpConfigMedPolicyApplicationType,
                  mgmtLldpConfigMedPolicyTagged,
                  mgmtLldpConfigMedPolicyVlanId,
                  mgmtLldpConfigMedPolicyL2Priority,
                  mgmtLldpConfigMedPolicyDscp,
                  mgmtLldpConfigMedPolicyAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 4 }

mgmtLldpConfigMedPolicyListInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpConfigMedPolicyListIfIndex,
                  mgmtLldpConfigMedPolicyListLldpmedPolicy,
                  mgmtLldpConfigMedPolicyListLldpmedPoliciesList }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 5 }

mgmtLldpConfigMedGlobalInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpConfigMedGlobalFastRepeatCount,
                  mgmtLldpConfigMedGlobalLatitude,
                  mgmtLldpConfigMedGlobalLongitude,
                  mgmtLldpConfigMedGlobalAltitudeType,
                  mgmtLldpConfigMedGlobalAltitude,
                  mgmtLldpConfigMedGlobalElinAddr,
                  mgmtLldpConfigMedGlobalDatum,
                  mgmtLldpConfigMedGlobalCountryCode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 6 }

mgmtLldpConfigMedLocationInformationInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpConfigMedLocationInformationLldpmedIndex,
                  mgmtLldpConfigMedLocationInformationCivicAddress }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 7 }

mgmtLldpConfigMedPolicyRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpConfigMedPolicyRowEditorLldpmedPolicy,
                  mgmtLldpConfigMedPolicyRowEditorApplicationType,
                  mgmtLldpConfigMedPolicyRowEditorTagged,
                  mgmtLldpConfigMedPolicyRowEditorVlanId,
                  mgmtLldpConfigMedPolicyRowEditorL2Priority,
                  mgmtLldpConfigMedPolicyRowEditorDscp,
                  mgmtLldpConfigMedPolicyRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 8 }

mgmtLldpStatusStatisticsGlobalCountersInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtLldpStatusStatisticsGlobalCountersTableInserts,
                  mgmtLldpStatusStatisticsGlobalCountersTableDeletes,
                  mgmtLldpStatusStatisticsGlobalCountersTableDrops,
                  mgmtLldpStatusStatisticsGlobalCountersTableAgeOuts,
                  mgmtLldpStatusStatisticsGlobalCountersLastChangeTime }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 9 }

mgmtLldpStatusStatisticsTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpStatusStatisticsIfIndex,
                  mgmtLldpStatusStatisticsTxTotal,
                  mgmtLldpStatusStatisticsRxTotal,
                  mgmtLldpStatusStatisticsRxError,
                  mgmtLldpStatusStatisticsRxDiscarded,
                  mgmtLldpStatusStatisticsTLVsDiscarded,
                  mgmtLldpStatusStatisticsTLVsUnrecognized,
                  mgmtLldpStatusStatisticsTLVsOrgDiscarded,
                  mgmtLldpStatusStatisticsAgeOuts }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 10 }

mgmtLldpStatusNeighborsInformationInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpStatusNeighborsInformationIfIndex,
                  mgmtLldpStatusNeighborsInformationLldpmedIndex,
                  mgmtLldpStatusNeighborsInformationChassisId,
                  mgmtLldpStatusNeighborsInformationPortId,
                  mgmtLldpStatusNeighborsInformationPortDescription,
                  mgmtLldpStatusNeighborsInformationSystemName,
                  mgmtLldpStatusNeighborsInformationSystemDescription,
                  mgmtLldpStatusNeighborsInformationSystemCapabilities,
                  mgmtLldpStatusNeighborsInformationSystemCapabilitiesEnable }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 11 }

mgmtLldpStatusNeighborsMgmtInformationInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpStatusNeighborsMgmtInformationIfIndex,
                  mgmtLldpStatusNeighborsMgmtInformationLldpmedIndex,
                  mgmtLldpStatusNeighborsMgmtInformationLldpManagement,
                  mgmtLldpStatusNeighborsMgmtInformationSystemMgmAddressSubtype,
                  mgmtLldpStatusNeighborsMgmtInformationSystemMgmtAddress,
                  mgmtLldpStatusNeighborsMgmtInformationSystemMgmtInterfaceSubtype,
                  mgmtLldpStatusNeighborsMgmtInformationSystemMgmtInterface,
                  mgmtLldpStatusNeighborsMgmtInformationSystemMgmtOid }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 12 }

mgmtLldpStatusPreemptInformationInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpStatusPreemptInformationIfIndex,
                  mgmtLldpStatusPreemptInformationLldpmedIndex,
                  mgmtLldpStatusPreemptInformationLldpManagement,
                  mgmtLldpStatusPreemptInformationLocPreemptSupported,
                  mgmtLldpStatusPreemptInformationLocPreemptEnabled,
                  mgmtLldpStatusPreemptInformationLocPreemptActive,
                  mgmtLldpStatusPreemptInformationLocAddFragSize,
                  mgmtLldpStatusPreemptInformationRemFramePreemptSupported,
                  mgmtLldpStatusPreemptInformationRemFramePreemptEnabled,
                  mgmtLldpStatusPreemptInformationRemFramePreemptActive,
                  mgmtLldpStatusPreemptInformationRemFrameAddFragSize }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 13 }

mgmtLldpStatusMedRemoteDeviceInfoInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpStatusMedRemoteDeviceInfoIfIndex,
                  mgmtLldpStatusMedRemoteDeviceInfoLldpmedIndex,
                  mgmtLldpStatusMedRemoteDeviceInfoCapabilities,
                  mgmtLldpStatusMedRemoteDeviceInfoCapabilitiesEnabled,
                  mgmtLldpStatusMedRemoteDeviceInfoLatitude,
                  mgmtLldpStatusMedRemoteDeviceInfoLongitude,
                  mgmtLldpStatusMedRemoteDeviceInfoAltitudeType,
                  mgmtLldpStatusMedRemoteDeviceInfoAltitude,
                  mgmtLldpStatusMedRemoteDeviceInfoDatum,
                  mgmtLldpStatusMedRemoteDeviceInfoElinaddr,
                  mgmtLldpStatusMedRemoteDeviceInfoDeviceType,
                  mgmtLldpStatusMedRemoteDeviceInfoHwRev,
                  mgmtLldpStatusMedRemoteDeviceInfoFwRev,
                  mgmtLldpStatusMedRemoteDeviceInfoSwRev,
                  mgmtLldpStatusMedRemoteDeviceInfoSerialNo,
                  mgmtLldpStatusMedRemoteDeviceInfoManufacturerName,
                  mgmtLldpStatusMedRemoteDeviceInfoModelName,
                  mgmtLldpStatusMedRemoteDeviceInfoAssetId,
                  mgmtLldpStatusMedRemoteDeviceInfoEeeRxTwSys,
                  mgmtLldpStatusMedRemoteDeviceInfoEeeTxTwSys,
                  mgmtLldpStatusMedRemoteDeviceInfoEeeFbTwSys,
                  mgmtLldpStatusMedRemoteDeviceInfoEeeTxTwSysEcho,
                  mgmtLldpStatusMedRemoteDeviceInfoEeeRxTwSysEcho }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 14 }

mgmtLldpStatusMedRemoteDeviceLocInfoInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpStatusMedRemoteDeviceLocInfoIfIndex,
                  mgmtLldpStatusMedRemoteDeviceLocInfoLldpmedIndex,
                  mgmtLldpStatusMedRemoteDeviceLocInfoState,
                  mgmtLldpStatusMedRemoteDeviceLocInfoCounty,
                  mgmtLldpStatusMedRemoteDeviceLocInfoCity,
                  mgmtLldpStatusMedRemoteDeviceLocInfoDistrict,
                  mgmtLldpStatusMedRemoteDeviceLocInfoBlock,
                  mgmtLldpStatusMedRemoteDeviceLocInfoStreet,
                  mgmtLldpStatusMedRemoteDeviceLocInfoLeadingStreetDirection,
                  mgmtLldpStatusMedRemoteDeviceLocInfoTrailingStreetSuffix,
                  mgmtLldpStatusMedRemoteDeviceLocInfoStreetSuffix,
                  mgmtLldpStatusMedRemoteDeviceLocInfoHouseNo,
                  mgmtLldpStatusMedRemoteDeviceLocInfoHouseNoSuffix,
                  mgmtLldpStatusMedRemoteDeviceLocInfoLandmark,
                  mgmtLldpStatusMedRemoteDeviceLocInfoAdditionalInfo,
                  mgmtLldpStatusMedRemoteDeviceLocInfoName,
                  mgmtLldpStatusMedRemoteDeviceLocInfoZipCode,
                  mgmtLldpStatusMedRemoteDeviceLocInfoBuilding,
                  mgmtLldpStatusMedRemoteDeviceLocInfoApartment,
                  mgmtLldpStatusMedRemoteDeviceLocInfoFloor,
                  mgmtLldpStatusMedRemoteDeviceLocInfoRoomNumber,
                  mgmtLldpStatusMedRemoteDeviceLocInfoPlaceType,
                  mgmtLldpStatusMedRemoteDeviceLocInfoPostalCommunityName,
                  mgmtLldpStatusMedRemoteDeviceLocInfoPoBox,
                  mgmtLldpStatusMedRemoteDeviceLocInfoAdditionalCode,
                  mgmtLldpStatusMedRemoteDeviceLocInfoCountryCode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 15 }

mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoInfoGroup OBJECT-GROUP
    OBJECTS     {                   mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoIfIndex,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoLldpmedIndex,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoLldpmedPolicy,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoApplicationType,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoUnknownPolicy,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoTagged,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoVlanId,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoL2Priority,
                  mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoDscp }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 16 }

mgmtLldpControlStatisticsClearTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpControlStatisticsClearIfIndex,
                  mgmtLldpControlStatisticsClearStatisticsClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 17 }

mgmtLldpControlStatisticsClearGlobalInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLldpControlStatisticsClearGlobalClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLldpMibGroups 18 }

mgmtLldpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtLldpConfigGlobalInfoGroup,
                       mgmtLldpConfigInfoGroup,
                       mgmtLldpConfigMedInfoGroup,
                       mgmtLldpConfigMedPolicyInfoGroup,
                       mgmtLldpConfigMedPolicyListInfoGroup,
                       mgmtLldpConfigMedGlobalInfoGroup,
                       mgmtLldpConfigMedLocationInformationInfoGroup,
                       mgmtLldpConfigMedPolicyRowEditorInfoGroup,
                       mgmtLldpStatusStatisticsGlobalCountersInfoGroup,
                       mgmtLldpStatusStatisticsTableInfoGroup,
                       mgmtLldpStatusNeighborsInformationInfoGroup,
                       mgmtLldpStatusNeighborsMgmtInformationInfoGroup,
                       mgmtLldpStatusPreemptInformationInfoGroup,
                       mgmtLldpStatusMedRemoteDeviceInfoInfoGroup,
                       mgmtLldpStatusMedRemoteDeviceLocInfoInfoGroup,
                       mgmtLldpStatusMedRemoteDeviceNetworkPolicyInfoInfoGroup,
                       mgmtLldpControlStatisticsClearTableInfoGroup,
                       mgmtLldpControlStatisticsClearGlobalInfoGroup }

    ::= { mgmtLldpMibCompliances 1 }

END
