-- *****************************************************************
-- PORT-POWER-SAVINGS-MIB:  
-- ****************************************************************

MGMT-PORT-POWER-SAVINGS-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    ;

mgmtPortPowerSavingsMib MODULE-IDENTITY
    LAST-UPDATED "201408070000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of Port Power Saving. Port power saving
         reduces the switch power consumptionby lowering the port power supply
         when there is no link partner connected to a port as well as when link
         partner is connected through a short cable."
    REVISION    "201408070000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 100 }


MGMTPortPowerSavingsStatusType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the feature status."
    SYNTAX      INTEGER { no(0), yes(1), notSupported(2) }

mgmtPortPowerSavingsMibObjects OBJECT IDENTIFIER
    ::= { mgmtPortPowerSavingsMib 1 }

mgmtPortPowerSavingsCapabilities OBJECT IDENTIFIER
    ::= { mgmtPortPowerSavingsMibObjects 1 }

mgmtPortPowerSavingsCapabilitiesInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortPowerSavingsCapabilitiesInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to interface capabilities"
    ::= { mgmtPortPowerSavingsCapabilities 1 }

mgmtPortPowerSavingsCapabilitiesInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTPortPowerSavingsCapabilitiesInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of capability parameters"
    INDEX       { mgmtPortPowerSavingsCapabilitiesInterfaceIfIndex }
    ::= { mgmtPortPowerSavingsCapabilitiesInterfaceTable 1 }

MGMTPortPowerSavingsCapabilitiesInterfaceEntry ::= SEQUENCE {
    mgmtPortPowerSavingsCapabilitiesInterfaceIfIndex      MGMTInterfaceIndex,
    mgmtPortPowerSavingsCapabilitiesInterfaceLinkPartner  TruthValue,
    mgmtPortPowerSavingsCapabilitiesInterfaceShortReach   TruthValue
}

mgmtPortPowerSavingsCapabilitiesInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortPowerSavingsCapabilitiesInterfaceEntry 1 }

mgmtPortPowerSavingsCapabilitiesInterfaceLinkPartner OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate whether interface is capable for detecting link partner or
         not. true means interface is capable to detect link partner, false
         means interface is not capable to detect link partner."
    ::= { mgmtPortPowerSavingsCapabilitiesInterfaceEntry 2 }

mgmtPortPowerSavingsCapabilitiesInterfaceShortReach OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicates whether interface is able to determine the cable length
         connected to partner port. true means interface is capable to determine
         the cable length, false means interface is not capable to determine the
         cable length."
    ::= { mgmtPortPowerSavingsCapabilitiesInterfaceEntry 3 }

mgmtPortPowerSavingsConfig OBJECT IDENTIFIER
    ::= { mgmtPortPowerSavingsMibObjects 2 }

mgmtPortPowerSavingsConfigInterfaceParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortPowerSavingsConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table provides Port Power Savings configuration for an interface"
    ::= { mgmtPortPowerSavingsConfig 1 }

mgmtPortPowerSavingsConfigInterfaceParamEntry OBJECT-TYPE
    SYNTAX      MGMTPortPowerSavingsConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of Port Power Savings configurable parameters"
    INDEX       { mgmtPortPowerSavingsConfigInterfaceParamIfIndex }
    ::= { mgmtPortPowerSavingsConfigInterfaceParamTable 1 }

MGMTPortPowerSavingsConfigInterfaceParamEntry ::= SEQUENCE {
    mgmtPortPowerSavingsConfigInterfaceParamIfIndex      MGMTInterfaceIndex,
    mgmtPortPowerSavingsConfigInterfaceParamLinkPartner  TruthValue,
    mgmtPortPowerSavingsConfigInterfaceParamShortReach   TruthValue
}

mgmtPortPowerSavingsConfigInterfaceParamIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortPowerSavingsConfigInterfaceParamEntry 1 }

mgmtPortPowerSavingsConfigInterfaceParamLinkPartner OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Save port power if there is no link partner connected to the port. true
         is to enable port power saving when there is no link partner connected,
         false is to disable it."
    ::= { mgmtPortPowerSavingsConfigInterfaceParamEntry 2 }

mgmtPortPowerSavingsConfigInterfaceParamShortReach OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Save port power if port is connected to link partner through short
         cable. true is to enable port power saving when link partner connected
         through short cable, false is to disable it."
    ::= { mgmtPortPowerSavingsConfigInterfaceParamEntry 3 }

mgmtPortPowerSavingsStatus OBJECT IDENTIFIER
    ::= { mgmtPortPowerSavingsMibObjects 3 }

mgmtPortPowerSavingsStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTPortPowerSavingsStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to Port Power Savings interface status"
    ::= { mgmtPortPowerSavingsStatus 1 }

mgmtPortPowerSavingsStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTPortPowerSavingsStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of status parameters"
    INDEX       { mgmtPortPowerSavingsStatusInterfaceIfIndex }
    ::= { mgmtPortPowerSavingsStatusInterfaceTable 1 }

MGMTPortPowerSavingsStatusInterfaceEntry ::= SEQUENCE {
    mgmtPortPowerSavingsStatusInterfaceIfIndex        MGMTInterfaceIndex,
    mgmtPortPowerSavingsStatusInterfaceNoLinkPartner  MGMTPortPowerSavingsStatusType,
    mgmtPortPowerSavingsStatusInterfaceShortCable     MGMTPortPowerSavingsStatusType
}

mgmtPortPowerSavingsStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtPortPowerSavingsStatusInterfaceEntry 1 }

mgmtPortPowerSavingsStatusInterfaceNoLinkPartner OBJECT-TYPE
    SYNTAX      MGMTPortPowerSavingsStatusType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate whether port is saving power due to no link partner connected."
    ::= { mgmtPortPowerSavingsStatusInterfaceEntry 2 }

mgmtPortPowerSavingsStatusInterfaceShortCable OBJECT-TYPE
    SYNTAX      MGMTPortPowerSavingsStatusType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Indicate whether port is saving power due to link partner connected
         through short cable."
    ::= { mgmtPortPowerSavingsStatusInterfaceEntry 3 }

mgmtPortPowerSavingsMibConformance OBJECT IDENTIFIER
    ::= { mgmtPortPowerSavingsMib 2 }

mgmtPortPowerSavingsMibCompliances OBJECT IDENTIFIER
    ::= { mgmtPortPowerSavingsMibConformance 1 }

mgmtPortPowerSavingsMibGroups OBJECT IDENTIFIER
    ::= { mgmtPortPowerSavingsMibConformance 2 }

mgmtPortPowerSavingsCapabilitiesInterfaceInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortPowerSavingsCapabilitiesInterfaceIfIndex,
                  mgmtPortPowerSavingsCapabilitiesInterfaceLinkPartner,
                  mgmtPortPowerSavingsCapabilitiesInterfaceShortReach }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortPowerSavingsMibGroups 1 }

mgmtPortPowerSavingsConfigInterfaceParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortPowerSavingsConfigInterfaceParamIfIndex,
                  mgmtPortPowerSavingsConfigInterfaceParamLinkPartner,
                  mgmtPortPowerSavingsConfigInterfaceParamShortReach }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortPowerSavingsMibGroups 2 }

mgmtPortPowerSavingsStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtPortPowerSavingsStatusInterfaceIfIndex,
                  mgmtPortPowerSavingsStatusInterfaceNoLinkPartner,
                  mgmtPortPowerSavingsStatusInterfaceShortCable }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtPortPowerSavingsMibGroups 3 }

mgmtPortPowerSavingsMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS {                        mgmtPortPowerSavingsCapabilitiesInterfaceInfoGroup,
                       mgmtPortPowerSavingsConfigInterfaceParamTableInfoGroup,
                       mgmtPortPowerSavingsStatusInterfaceTableInfoGroup }

    ::= { mgmtPortPowerSavingsMibCompliances 1 }

END
