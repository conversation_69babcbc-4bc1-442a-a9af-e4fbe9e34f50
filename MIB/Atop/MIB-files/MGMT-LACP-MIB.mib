-- *****************************************************************
-- LACP-MIB:  
-- ****************************************************************

MGMT-LACP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Counter64 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    MacAddress FROM SNMPv2-TC
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    ;

mgmtLacpMib MODULE-IDENTITY
    LAST-UPDATED "201807030000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the IEEE802.3ad LAG MIB"
    REVISION    "201807030000Z"
    DESCRIPTION
        "Added LacpConfigGroupTable."
    REVISION    "201707310000Z"
    DESCRIPTION
        "Removed members (AdminMode, AdminKey and AdminKey) from
         mgmtLacpConfigPortEntry as these parameters are now controlled by the
         AGGR-MIB."
    REVISION    "201704060000Z"
    DESCRIPTION
        "Changed lacpPortConfigTable: 1) Made dot3adAggrActorAdminMode read-only
         as this parameter is now controlled by the AGGR-MIB. 2) Made
         dot3adAggrRole object read-only as this is not a port-level but
         group-level property. Added new objects to lacpPortStatusTable."
    REVISION    "201411140000Z"
    DESCRIPTION
        "Added a new leaf for LACP system priority"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 35 }


mgmtLacpMibObjects OBJECT IDENTIFIER
    ::= { mgmtLacpMib 1 }

mgmtLacpConfig OBJECT IDENTIFIER
    ::= { mgmtLacpMibObjects 2 }

mgmtLacpConfigPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLacpConfigPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of the LACP port configurations."
    ::= { mgmtLacpConfig 1 }

mgmtLacpConfigPortEntry OBJECT-TYPE
    SYNTAX      MGMTLacpConfigPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters."
    INDEX       { mgmtLacpConfigPortInterfaceNo }
    ::= { mgmtLacpConfigPortTable 1 }

MGMTLacpConfigPortEntry ::= SEQUENCE {
    mgmtLacpConfigPortInterfaceNo             MGMTInterfaceIndex,
    mgmtLacpConfigPortDot3adAggrTimeout       TruthValue,
    mgmtLacpConfigPortDot3adAggrPortPriority  Unsigned32
}

mgmtLacpConfigPortInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtLacpConfigPortEntry 1 }

mgmtLacpConfigPortDot3adAggrTimeout OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Timeout controls the period between BPDU transmissions. Fast(true)
         will transmit LACP packets each second, while Slow(0) will wait for 30
         seconds before sending a LACP packet."
    ::= { mgmtLacpConfigPortEntry 2 }

mgmtLacpConfigPortDot3adAggrPortPriority OBJECT-TYPE
    SYNTAX      Unsigned32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The Port Priority controls the priority of the port. If the LACP
         partner wants to form a larger group than is supported by this device
         then this parameter will control which ports will be active and which
         ports will be in a backup role. Lower number means greater priority."
    ::= { mgmtLacpConfigPortEntry 3 }

mgmtLacpConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtLacpConfig 2 }

mgmtLacpConfigGlobalsDot3adAggrSystemPriority OBJECT-TYPE
    SYNTAX      Unsigned32 (1..65535)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "LACP system priority is a value."
    ::= { mgmtLacpConfigGlobals 1 }

mgmtLacpConfigGroupTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLacpConfigGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of the LACP group configurations. Entries in this table
         are also present in the AggrConfigGroupTable in the LACP-MIB but the
         LacpGroupConfTable will only contain group entries configured for LACP
         operation."
    ::= { mgmtLacpConfig 3 }

mgmtLacpConfigGroupEntry OBJECT-TYPE
    SYNTAX      MGMTLacpConfigGroupEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each group has a set of parameters."
    INDEX       { mgmtLacpConfigGroupInterfaceNo }
    ::= { mgmtLacpConfigGroupTable 1 }

MGMTLacpConfigGroupEntry ::= SEQUENCE {
    mgmtLacpConfigGroupInterfaceNo  MGMTInterfaceIndex,
    mgmtLacpConfigGroupRevertive    TruthValue,
    mgmtLacpConfigGroupMaxBundle    Unsigned32
}

mgmtLacpConfigGroupInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtLacpConfigGroupEntry 1 }

mgmtLacpConfigGroupRevertive OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Determines whether the LACP group failover is revertive or not. A
         revertive (TRUE) group will change back to the active port if it comes
         back up. A non-revertive (FALSE) group will remain on the standby port
         even of the active port comes back up."
    ::= { mgmtLacpConfigGroupEntry 2 }

mgmtLacpConfigGroupMaxBundle OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Max number of ports that can bundle up in an aggregation. Remaining
         ports will go into standby mode. The maximum number of ports in a
         bundle is 16 (or the number of physical ports on the device if that is
         lower)."
    ::= { mgmtLacpConfigGroupEntry 3 }

mgmtLacpStatus OBJECT IDENTIFIER
    ::= { mgmtLacpMibObjects 3 }

mgmtLacpStatusSystemTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLacpStatusSystemEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains the LACP aggregation group system status. Each
         entry represents a single aggregation group and is indexed with the
         ifIndex of the aggregation group. The table is auto-populated by the
         system when valid parther information exist for the group."
    ::= { mgmtLacpStatus 1 }

mgmtLacpStatusSystemEntry OBJECT-TYPE
    SYNTAX      MGMTLacpStatusSystemEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry represents a set of status parameters for an aggregation
         group."
    INDEX       { mgmtLacpStatusSystemInterfaceNo }
    ::= { mgmtLacpStatusSystemTable 1 }

MGMTLacpStatusSystemEntry ::= SEQUENCE {
    mgmtLacpStatusSystemInterfaceNo                          MGMTInterfaceIndex,
    mgmtLacpStatusSystemDot3adAggrID                         MGMTUnsigned16,
    mgmtLacpStatusSystemDot3adAggrPartnerSystemID            MacAddress,
    mgmtLacpStatusSystemDot3adAggrPartnerOperKey             MGMTUnsigned16,
    mgmtLacpStatusSystemDot3adAggrPartnerOperSystemPriority  MGMTUnsigned16,
    mgmtLacpStatusSystemDot3adAggrPartnerStateLastChanged    Unsigned32,
    mgmtLacpStatusSystemDot3adAggrLocalPorts                 MGMTPortList
}

mgmtLacpStatusSystemInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtLacpStatusSystemEntry 1 }

mgmtLacpStatusSystemDot3adAggrID OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The aggregation ID for a particular link aggregation group."
    ::= { mgmtLacpStatusSystemEntry 2 }

mgmtLacpStatusSystemDot3adAggrPartnerSystemID OBJECT-TYPE
    SYNTAX      MacAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The system ID (MAC address) of the aggregation partner."
    ::= { mgmtLacpStatusSystemEntry 3 }

mgmtLacpStatusSystemDot3adAggrPartnerOperKey OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The Key that the partner has assigned to this aggregation ID."
    ::= { mgmtLacpStatusSystemEntry 4 }

mgmtLacpStatusSystemDot3adAggrPartnerOperSystemPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "A 2-octet read-only value indicating the operational value of priority
         associated with the Partner's System ID. The value of this attribute
         may contain the manually configured value carried in
         aAggPortPartnerAdminSystemPriority if there is no protocol Partner."
    ::= { mgmtLacpStatusSystemEntry 5 }

mgmtLacpStatusSystemDot3adAggrPartnerStateLastChanged OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The time in second since this aggregation changed"
    ::= { mgmtLacpStatusSystemEntry 6 }

mgmtLacpStatusSystemDot3adAggrLocalPorts OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Local port list"
    ::= { mgmtLacpStatusSystemEntry 7 }

mgmtLacpStatusPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLacpStatusPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains LACP port operational status parameters. Each table
         entry represents a single aggregation port and is indexed with the
         ifIndex of the port. The table is auto-populated by the system when
         valid LACP status exist for the port."
    ::= { mgmtLacpStatus 2 }

mgmtLacpStatusPortEntry OBJECT-TYPE
    SYNTAX      MGMTLacpStatusPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry represents a set of status parameters for an LACP port."
    INDEX       { mgmtLacpStatusPortInterfaceNo }
    ::= { mgmtLacpStatusPortTable 1 }

MGMTLacpStatusPortEntry ::= SEQUENCE {
    mgmtLacpStatusPortInterfaceNo                        MGMTInterfaceIndex,
    mgmtLacpStatusPortDot3adAggrActorAdminMode           TruthValue,
    mgmtLacpStatusPortDot3adAggrActorAdminKey            MGMTUnsigned16,
    mgmtLacpStatusPortDot3adAggrPartnerOperPortIndex     MGMTUnsigned16,
    mgmtLacpStatusPortDot3adAggrPartnerOperPortPriority  MGMTUnsigned16,
    mgmtLacpStatusPortDot3adActorAggrPortPriority        MGMTUnsigned16,
    mgmtLacpStatusPortDot3adAggrPortOperState            OCTET STRING,
    mgmtLacpStatusPortDot3adAggrPartnerKey               MGMTUnsigned16,
    mgmtLacpStatusPortDot3adAggrPartnerOperState         OCTET STRING
}

mgmtLacpStatusPortInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtLacpStatusPortEntry 1 }

mgmtLacpStatusPortDot3adAggrActorAdminMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the current Admin mode of port, if LACP enabled then returns true
         else returns false."
    ::= { mgmtLacpStatusPortEntry 2 }

mgmtLacpStatusPortDot3adAggrActorAdminKey OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the current administrative value of the Key for the Aggregator.
         The administrative Key value may differ from the operational Key value
         for the reasons discussed in 43.6.2. This is a 16-bit, read-write
         value. The meaning of particular Key values is of local significance"
    ::= { mgmtLacpStatusPortEntry 3 }

mgmtLacpStatusPortDot3adAggrPartnerOperPortIndex OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the port index of the partner port connected to this port."
    ::= { mgmtLacpStatusPortEntry 4 }

mgmtLacpStatusPortDot3adAggrPartnerOperPortPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows the port priority of the port partner port connected to this
         port."
    ::= { mgmtLacpStatusPortEntry 5 }

mgmtLacpStatusPortDot3adActorAggrPortPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current administrative priority assigned to the port."
    ::= { mgmtLacpStatusPortEntry 6 }

mgmtLacpStatusPortDot3adAggrPortOperState OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current operational state of the port as a set of bits according to
         the defintion of the Actor_State octet (IEEE 802.1AX-2014, section
         *******)."
    ::= { mgmtLacpStatusPortEntry 7 }

mgmtLacpStatusPortDot3adAggrPartnerKey OBJECT-TYPE
    SYNTAX      MGMTUnsigned16
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current operational value of the key for the partner. The
         administrative key value may differ from the operational key value for
         the reasons discussed in 43.6.2. This is a 16-bit value. The meaning of
         particular key values is of local significance."
    ::= { mgmtLacpStatusPortEntry 8 }

mgmtLacpStatusPortDot3adAggrPartnerOperState OBJECT-TYPE
    SYNTAX      OCTET STRING (SIZE(1))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The current operational state of the partner port as a set of bits
         according to the definition of the Partner_State octet (IEEE
         802.1AX-2014, section *******)."
    ::= { mgmtLacpStatusPortEntry 9 }

mgmtLacpControl OBJECT IDENTIFIER
    ::= { mgmtLacpMibObjects 4 }

mgmtLacpControlPortStatsClearTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLacpControlPortStatsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table to clear LACP port statistics"
    ::= { mgmtLacpControl 1 }

mgmtLacpControlPortStatsClearEntry OBJECT-TYPE
    SYNTAX      MGMTLacpControlPortStatsClearEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters"
    INDEX       { mgmtLacpControlPortStatsClearInterfaceNo }
    ::= { mgmtLacpControlPortStatsClearTable 1 }

MGMTLacpControlPortStatsClearEntry ::= SEQUENCE {
    mgmtLacpControlPortStatsClearInterfaceNo          MGMTInterfaceIndex,
    mgmtLacpControlPortStatsClearPortStatisticsClear  TruthValue
}

mgmtLacpControlPortStatsClearInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtLacpControlPortStatsClearEntry 1 }

mgmtLacpControlPortStatsClearPortStatisticsClear OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to clear the statistics of a port."
    ::= { mgmtLacpControlPortStatsClearEntry 2 }

mgmtLacpStatistics OBJECT IDENTIFIER
    ::= { mgmtLacpMibObjects 5 }

mgmtLacpStatisticsPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLacpStatisticsPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This table contains LACP port statistics counters. Each table entry
         represents a single aggregation port and is indexed with the ifIndex of
         the port. The table is auto-populated by the system when valid LACP
         statistics exist for the port."
    ::= { mgmtLacpStatistics 3 }

mgmtLacpStatisticsPortEntry OBJECT-TYPE
    SYNTAX      MGMTLacpStatisticsPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters"
    INDEX       { mgmtLacpStatisticsPortInterfaceNo }
    ::= { mgmtLacpStatisticsPortTable 1 }

MGMTLacpStatisticsPortEntry ::= SEQUENCE {
    mgmtLacpStatisticsPortInterfaceNo                MGMTInterfaceIndex,
    mgmtLacpStatisticsPortDot3adAggrRxFrames         Counter64,
    mgmtLacpStatisticsPortDot3adAggrTxFrames         Counter64,
    mgmtLacpStatisticsPortDot3adAggrRxIllegalFrames  Counter64,
    mgmtLacpStatisticsPortDot3adAggrRxUnknownFrames  Counter64
}

mgmtLacpStatisticsPortInterfaceNo OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtLacpStatisticsPortEntry 1 }

mgmtLacpStatisticsPortDot3adAggrRxFrames OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows received LACP frame count."
    ::= { mgmtLacpStatisticsPortEntry 2 }

mgmtLacpStatisticsPortDot3adAggrTxFrames OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows transmitted LACP frame count."
    ::= { mgmtLacpStatisticsPortEntry 3 }

mgmtLacpStatisticsPortDot3adAggrRxIllegalFrames OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows received illegal LACP frame count."
    ::= { mgmtLacpStatisticsPortEntry 4 }

mgmtLacpStatisticsPortDot3adAggrRxUnknownFrames OBJECT-TYPE
    SYNTAX      Counter64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Shows received unknown LACP frame count."
    ::= { mgmtLacpStatisticsPortEntry 5 }

mgmtLacpMibConformance OBJECT IDENTIFIER
    ::= { mgmtLacpMib 2 }

mgmtLacpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtLacpMibConformance 1 }

mgmtLacpMibGroups OBJECT IDENTIFIER
    ::= { mgmtLacpMibConformance 2 }

mgmtLacpConfigPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLacpConfigPortInterfaceNo,
                  mgmtLacpConfigPortDot3adAggrTimeout,
                  mgmtLacpConfigPortDot3adAggrPortPriority }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLacpMibGroups 1 }

mgmtLacpConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLacpConfigGlobalsDot3adAggrSystemPriority }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLacpMibGroups 2 }

mgmtLacpConfigGroupTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLacpConfigGroupInterfaceNo,
                  mgmtLacpConfigGroupRevertive,
                  mgmtLacpConfigGroupMaxBundle }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLacpMibGroups 3 }

mgmtLacpStatusSystemTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLacpStatusSystemInterfaceNo,
                  mgmtLacpStatusSystemDot3adAggrID,
                  mgmtLacpStatusSystemDot3adAggrPartnerSystemID,
                  mgmtLacpStatusSystemDot3adAggrPartnerOperKey,
                  mgmtLacpStatusSystemDot3adAggrPartnerOperSystemPriority,
                  mgmtLacpStatusSystemDot3adAggrPartnerStateLastChanged,
                  mgmtLacpStatusSystemDot3adAggrLocalPorts }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLacpMibGroups 4 }

mgmtLacpStatusPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLacpStatusPortInterfaceNo,
                  mgmtLacpStatusPortDot3adAggrActorAdminMode,
                  mgmtLacpStatusPortDot3adAggrActorAdminKey,
                  mgmtLacpStatusPortDot3adAggrPartnerOperPortIndex,
                  mgmtLacpStatusPortDot3adAggrPartnerOperPortPriority,
                  mgmtLacpStatusPortDot3adActorAggrPortPriority,
                  mgmtLacpStatusPortDot3adAggrPortOperState,
                  mgmtLacpStatusPortDot3adAggrPartnerKey,
                  mgmtLacpStatusPortDot3adAggrPartnerOperState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLacpMibGroups 5 }

mgmtLacpControlPortStatsClearTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLacpControlPortStatsClearInterfaceNo,
                  mgmtLacpControlPortStatsClearPortStatisticsClear }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLacpMibGroups 6 }

mgmtLacpStatisticsPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLacpStatisticsPortInterfaceNo,
                  mgmtLacpStatisticsPortDot3adAggrRxFrames,
                  mgmtLacpStatisticsPortDot3adAggrTxFrames,
                  mgmtLacpStatisticsPortDot3adAggrRxIllegalFrames,
                  mgmtLacpStatisticsPortDot3adAggrRxUnknownFrames }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLacpMibGroups 7 }

mgmtLacpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtLacpConfigPortTableInfoGroup,
                       mgmtLacpConfigGlobalsInfoGroup,
                       mgmtLacpConfigGroupTableInfoGroup,
                       mgmtLacpStatusSystemTableInfoGroup,
                       mgmtLacpStatusPortTableInfoGroup,
                       mgmtLacpControlPortStatsClearTableInfoGroup,
                       mgmtLacpStatisticsPortTableInfoGroup }

    ::= { mgmtLacpMibCompliances 1 }

END
