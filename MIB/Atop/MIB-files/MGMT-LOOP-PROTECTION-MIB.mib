-- *****************************************************************
-- LOOP-PROTECTION-MIB:  
-- ****************************************************************

MGMT-LOOP-PROTECTION-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTUnsigned64 FROM MGMT-TC
    ;

mgmtLoopProtectionMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private MIB for loop protection"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 91 }


MGMTLoopProtectionAction ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the available actions for when a loop on an
         interface is detected."
    SYNTAX      INTEGER { shutdown(0), shutdownAndLogEvent(1),
                          logEvent(2) }

mgmtLoopProtectionMibObjects OBJECT IDENTIFIER
    ::= { mgmtLoopProtectionMib 1 }

mgmtLoopProtectionConfig OBJECT IDENTIFIER
    ::= { mgmtLoopProtectionMibObjects 2 }

mgmtLoopProtectionConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtLoopProtectionConfig 1 }

mgmtLoopProtectionConfigGlobalsEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global enabled for loop protection on any port."
    ::= { mgmtLoopProtectionConfigGlobals 1 }

mgmtLoopProtectionConfigGlobalsTransmitInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Port transmission interval (seconds). Valid range: 1-10 seconds."
    ::= { mgmtLoopProtectionConfigGlobals 2 }

mgmtLoopProtectionConfigGlobalsShutdownPeriod OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Port shutdown period (seconds). Valid range: 0 to 604800 seconds."
    ::= { mgmtLoopProtectionConfigGlobals 3 }

mgmtLoopProtectionConfigInterface OBJECT IDENTIFIER
    ::= { mgmtLoopProtectionConfig 2 }

mgmtLoopProtectionConfigInterfaceParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLoopProtectionConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of loop protection interface parameters"
    ::= { mgmtLoopProtectionConfigInterface 1 }

mgmtLoopProtectionConfigInterfaceParamEntry OBJECT-TYPE
    SYNTAX      MGMTLoopProtectionConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical interface has a set of parameters"
    INDEX       { mgmtLoopProtectionConfigInterfaceParamIfIndex }
    ::= { mgmtLoopProtectionConfigInterfaceParamTable 1 }

MGMTLoopProtectionConfigInterfaceParamEntry ::= SEQUENCE {
    mgmtLoopProtectionConfigInterfaceParamIfIndex   MGMTInterfaceIndex,
    mgmtLoopProtectionConfigInterfaceParamEnabled   TruthValue,
    mgmtLoopProtectionConfigInterfaceParamAction    MGMTLoopProtectionAction,
    mgmtLoopProtectionConfigInterfaceParamTransmit  TruthValue
}

mgmtLoopProtectionConfigInterfaceParamIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtLoopProtectionConfigInterfaceParamEntry 1 }

mgmtLoopProtectionConfigInterfaceParamEnabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enabled loop protection on port"
    ::= { mgmtLoopProtectionConfigInterfaceParamEntry 2 }

mgmtLoopProtectionConfigInterfaceParamAction OBJECT-TYPE
    SYNTAX      MGMTLoopProtectionAction
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action if loop detected"
    ::= { mgmtLoopProtectionConfigInterfaceParamEntry 3 }

mgmtLoopProtectionConfigInterfaceParamTransmit OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Actively generate PDUs"
    ::= { mgmtLoopProtectionConfigInterfaceParamEntry 4 }

mgmtLoopProtectionStatus OBJECT IDENTIFIER
    ::= { mgmtLoopProtectionMibObjects 3 }

mgmtLoopProtectionStatusInterface OBJECT IDENTIFIER
    ::= { mgmtLoopProtectionStatus 2 }

mgmtLoopProtectionStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTLoopProtectionStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of loop protection interface status"
    ::= { mgmtLoopProtectionStatusInterface 1 }

mgmtLoopProtectionStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTLoopProtectionStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical interface has a set of status objects"
    INDEX       { mgmtLoopProtectionStatusInterfaceIfIndex }
    ::= { mgmtLoopProtectionStatusInterfaceTable 1 }

MGMTLoopProtectionStatusInterfaceEntry ::= SEQUENCE {
    mgmtLoopProtectionStatusInterfaceIfIndex       MGMTInterfaceIndex,
    mgmtLoopProtectionStatusInterfaceDisabled      TruthValue,
    mgmtLoopProtectionStatusInterfaceLoopDetected  TruthValue,
    mgmtLoopProtectionStatusInterfaceLoopCount     Unsigned32,
    mgmtLoopProtectionStatusInterfaceLastLoop      MGMTUnsigned64
}

mgmtLoopProtectionStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtLoopProtectionStatusInterfaceEntry 1 }

mgmtLoopProtectionStatusInterfaceDisabled OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether a port is currently disabled"
    ::= { mgmtLoopProtectionStatusInterfaceEntry 2 }

mgmtLoopProtectionStatusInterfaceLoopDetected OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Whether a port has a loop detected"
    ::= { mgmtLoopProtectionStatusInterfaceEntry 3 }

mgmtLoopProtectionStatusInterfaceLoopCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Number of times a loop has been detected on a port"
    ::= { mgmtLoopProtectionStatusInterfaceEntry 4 }

mgmtLoopProtectionStatusInterfaceLastLoop OBJECT-TYPE
    SYNTAX      MGMTUnsigned64
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Time of last loop condition"
    ::= { mgmtLoopProtectionStatusInterfaceEntry 5 }

mgmtLoopProtectionMibConformance OBJECT IDENTIFIER
    ::= { mgmtLoopProtectionMib 2 }

mgmtLoopProtectionMibCompliances OBJECT IDENTIFIER
    ::= { mgmtLoopProtectionMibConformance 1 }

mgmtLoopProtectionMibGroups OBJECT IDENTIFIER
    ::= { mgmtLoopProtectionMibConformance 2 }

mgmtLoopProtectionConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLoopProtectionConfigGlobalsEnabled,
                  mgmtLoopProtectionConfigGlobalsTransmitInterval,
                  mgmtLoopProtectionConfigGlobalsShutdownPeriod }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLoopProtectionMibGroups 1 }

mgmtLoopProtectionConfigInterfaceParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLoopProtectionConfigInterfaceParamIfIndex,
                  mgmtLoopProtectionConfigInterfaceParamEnabled,
                  mgmtLoopProtectionConfigInterfaceParamAction,
                  mgmtLoopProtectionConfigInterfaceParamTransmit }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLoopProtectionMibGroups 2 }

mgmtLoopProtectionStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtLoopProtectionStatusInterfaceIfIndex,
                  mgmtLoopProtectionStatusInterfaceDisabled,
                  mgmtLoopProtectionStatusInterfaceLoopDetected,
                  mgmtLoopProtectionStatusInterfaceLoopCount,
                  mgmtLoopProtectionStatusInterfaceLastLoop }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtLoopProtectionMibGroups 3 }

mgmtLoopProtectionMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtLoopProtectionConfigGlobalsInfoGroup,
                       mgmtLoopProtectionConfigInterfaceParamTableInfoGroup,
                       mgmtLoopProtectionStatusInterfaceTableInfoGroup }

    ::= { mgmtLoopProtectionMibCompliances 1 }

END
