XRG95XX-L2 DEFINITIONS ::= BEGIN

IMPORTS

       DateAndTime
          FROM SNMPv2-TC
       enterprises
                FROM RFC1155-SMI;

agatel            OBJECT IDENTIFIER ::= { enterprises 3755 }
products          OBJECT IDENTIFIER ::= { agatel 0 }
managedSwitch     OBJECT IDENTIFIER ::= { products 0 }

xrg95xx-l2 MODULE-IDENTITY
        LAST-UPDATED "2009210000Z"
        ORGANIZATION ""
        CONTACT-INFO
                "Tel:      Fax:

                 Business service :

                 Customer service: "

        DESCRIPTION
                "The MIB module for Industrial Ethernet Switch"
        REVISION "2206170000Z"
        DESCRIPTION
                "Initial version of this MIB."
        ::= { managedSwitch 2031 }

systemInfo            OBJECT IDENTIFIER ::= { xrg95xx-l2 1 }
basicSetting          OBJECT IDENTIFIER ::= { xrg95xx-l2 2 }
portConfiguration     OBJECT IDENTIFIER ::= { xrg95xx-l2 3 }
ringRedundancy        OBJECT IDENTIFIER ::= { xrg95xx-l2 4 }
ieee8021qVlan         OBJECT IDENTIFIER ::= { xrg95xx-l2 5 }
trafficPrioritization OBJECT IDENTIFIER ::= { xrg95xx-l2 6 }
multicastFiltering    OBJECT IDENTIFIER ::= { xrg95xx-l2 7 }
snmp                  OBJECT IDENTIFIER ::= { xrg95xx-l2 8 }
security              OBJECT IDENTIFIER ::= { xrg95xx-l2 9 }
warning               OBJECT IDENTIFIER ::= { xrg95xx-l2 10 }
monitorandDiag        OBJECT IDENTIFIER ::= { xrg95xx-l2 11 }
lldpMgt               OBJECT IDENTIFIER ::= { xrg95xx-l2 12 }
save                  OBJECT IDENTIFIER ::= { xrg95xx-l2 13 }
fdb                   OBJECT IDENTIFIER ::= { xrg95xx-l2 15 }
garp                  OBJECT IDENTIFIER ::= { xrg95xx-l2 16 }
diagnosis             OBJECT IDENTIFIER ::= { xrg95xx-l2 17 }
temperatureLog        OBJECT IDENTIFIER ::= { xrg95xx-l2 18 }

DisplayString ::= TEXTUAL-CONVENTION
    DISPLAY-HINT "255a"
    STATUS       current
    DESCRIPTION  ""
    SYNTAX       OCTET STRING (SIZE (0..255))

MacAddress ::= OCTET STRING (SIZE (6))    -- a 6 octet address
                                          -- in the
                                          -- "canonical"
                                          -- order

PortList ::= OCTET STRING

--
-- systemInfo
--

systemDescr OBJECT-TYPE
       SYNTAX  DisplayString (SIZE (0..64))
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "A textual description of the entity.  This value
                      should include the full name and version
                      identification of the system's hardware type,
                      software operating-system, and networking
                      software.  It is mandatory that this only contain
                      printable ASCII characters."
       ::= { systemInfo 4 }

systemFwVer OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..20))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Firmware version of the device."
        ::= { systemInfo 5 }

systemMacAddress OBJECT-TYPE
       SYNTAX      MacAddress
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION "The MAC address of switch."
       ::= { systemInfo 6 }

systemKernelVer OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..20))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Kernel version of the device."
        ::= { systemInfo 7 }

systemModelName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..32))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Model name of the device."
        ::= { systemInfo 10 }

systemCpuUsage OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The CPU usage percent."
        ::= { systemInfo 11 }

consoleInfo                  OBJECT IDENTIFIER ::= { systemInfo 8 }

consoleInfoBaudRate OBJECT-TYPE
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console baud rate(bits/second)."
       ::= { consoleInfo 1 }

consoleInfoDataBits OBJECT-TYPE
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console data bits."
       ::= { consoleInfo 2 }

consoleInfoParity OBJECT-TYPE
       SYNTAX  INTEGER
               {
                   none(1)
               }
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console parity check."
       ::= { consoleInfo 3 }

consoleInfoStopBit OBJECT-TYPE
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console stop bit."
       ::= { consoleInfo 4 }

consoleInfoFlowControl OBJECT-TYPE
       SYNTAX  INTEGER
               {
                   none(1)
               }
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "Console flow control."
       ::= { consoleInfo 5 }

powerInfo  OBJECT IDENTIFIER ::= { systemInfo 9 }

powerInfoTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF PowerInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     power Information."
        ::= { powerInfo 1 }

powerInfoEntry OBJECT-TYPE
        SYNTAX      PowerInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing power information."
        INDEX       { powerInfoNumber }
        ::= { powerInfoTable 1 }

PowerInfoEntry ::= SEQUENCE
{
    powerInfoNumber          Integer32,
    powerInfoStatus          INTEGER   
}

powerInfoNumber OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The power number."
        ::= {powerInfoEntry 1 }

powerInfoStatus    OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        ok(1),
                        fault(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The power status."
        ::= { powerInfoEntry 2 }

--
-- basicSetting
--

adminPassword         OBJECT IDENTIFIER ::= { basicSetting 2 }
ipConfiguration       OBJECT IDENTIFIER ::= { basicSetting 3 }
sntp                  OBJECT IDENTIFIER ::= { basicSetting 4 }
backupAndRestore      OBJECT IDENTIFIER ::= { basicSetting 6 }
factoryDefault        OBJECT IDENTIFIER ::= { basicSetting 8 }
systemReboot          OBJECT IDENTIFIER ::= { basicSetting 9 }

--
-- adminPassword
--

adminPasswordUserName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..15))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The name(ID) of security manager."
        ::= { adminPassword 1 }

adminPasswordPassword OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(6..15))
        MAX-ACCESS  write-only
        STATUS      current
        DESCRIPTION "The password of security manager.
                     This object can't be read. it's write-only."
        ::= { adminPassword 2 }

--
-- ipConfiguration
--

ipConfigurationTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF IPconfigurationEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     IP of switch."
        ::= { ipConfiguration 1 }

ipConfigurationEntry OBJECT-TYPE
        SYNTAX      IPconfigurationEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing IP/DHCP-client information
                     of the switch. "
        INDEX       { ipConfigurationIndex }
        ::= { ipConfigurationTable 1 }

IPconfigurationEntry ::= SEQUENCE
{
    ipConfigurationIndex             Integer32,
    ipConfigurationDHCPStatus        INTEGER,
    ipConfigurationAddress           IpAddress,
    ipConfigurationSubMask           IpAddress,
    ipConfigurationGateway           IpAddress,
    ipConfigurationDNS1              IpAddress,
    ipConfigurationDNS2              IpAddress
}

ipConfigurationIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "This object identifies the switch within the system
                     for which this entry contains information. This
                     value can never be greater than switchNumber."
        ::= { ipConfigurationEntry 1 }


ipConfigurationDHCPStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to enable or disable DHCP client function of the
                     switch. When enabled, device will be a DHCP client and request
                     the IP configuration from DHCP server.
                     Note: Other items in this table couldn't be modified, when
                     ipConfigurationDHCPStatus is enabled."
        ::= { ipConfigurationEntry 2 }

ipConfigurationAddress OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The IP address of switch."
        ::= { ipConfigurationEntry 3 }

ipConfigurationSubMask OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The subnet mask of switch."
        ::= { ipConfigurationEntry 4 }

ipConfigurationGateway OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The gateway address of switch."
        ::= { ipConfigurationEntry 5 }

ipConfigurationDNS1 OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The DNS1 address of switch."
        ::= { ipConfigurationEntry 6 }

ipConfigurationDNS2 OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The DNS2 address of switch."
        ::= { ipConfigurationEntry 7 }

--
-- sntp
--

sntpClientStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "(1)Enable SNTP client.
                     (2)Disable SNTP client.
                     SNTP is simple network time protocol.
                     Use this OID to Enable/Disable SNTP client."
        ::= { sntp 1 }

sntpUTCTimezone OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        gmt-negative-12-00-Eniwetok-Kwajalein(1),
                        gmt-negative-11-00-Midway-Island-Samoa(2),
                        gmt-negative-10-00-Hawaii(3),
                        gmt-negative-09-00-Alaska(4),
                        gmt-negative-08-00-Pacific-Time-US-and-Canada-Tijuana(5),
                        gmt-negative-07-00-Arizona(6),
                        gmt-negative-07-00-Mountain-Time-US-and-Canada(7),
                        gmt-negative-06-00-Central-Time-US-and-Canada(8),
                        gmt-negative-06-00-Mexico-City-Tegucigalpa(9),
                        gmt-negative-06-00-Saskatchewan(10),
                        gmt-negative-05-00-Bogota-Lima-Quito(11),
                        gmt-negative-05-00-Eastern-Time-US-and-Canada(12),
                        gmt-negative-05-00-Indiana-East(13),
                        gmt-negative-04-00-Atlantic-Time-Canada(14),
                        gmt-negative-04-00-Caracas-La-Paz(15),
                        gmt-negative-04-00-Santiago(16),
                        gmt-negative-03-30-Newfoundland(17),
                        gmt-negative-03-00-Brasilia(18),
                        gmt-negative-03-00-Buenos-Aires-Georgetown(19),
                        gmt-negative-02-00-Mid-Atlantic(20),
                        gmt-negative-01-00-Azores-Cape-Verde-Is(21),
                        gmt-Casablanca-Monrovia(22),
                        gmt-Greenwich-Mean-Time-Dublin-Edinburgh-Lisbon-London(23),
                        gmt-positive-01-00-Amsterdam-Berlin-Bern-Rome-Stockholm-Vienna(24),
                        gmt-positive-01-00-Belgrade-Bratislava-Budapest-Ljubljana-Prague(25),
                        gmt-positive-01-00-Brussels-Copenhagen-Madrid-Paris-Vilnius(26),
                        gmt-positive-01-00-Sarajevo-Skopje-Sofija-Warsaw-Zagreb(27),
                        gmt-positive-02-00-Athens-Istanbul-Minsk(28),
                        gmt-positive-02-00-Bucharest(29),
                        gmt-positive-02-00-Cairo(30),
                        gmt-positive-02-00-Harare-Pretoria(31),
                        gmt-positive-02-00-Helsinki-Riga-Tallinn(32),
                        gmt-positive-02-00-Jerusalem(33),
                        gmt-positive-03-00-Baghdad-Kuwait-Riyadh(34),
                        gmt-positive-03-00-Moscow-St-Petersburg-Volgograd(35),
                        gmt-positive-03-00-Mairobi(36),
                        gmt-positive-03-30-Tehran(37),
                        gmt-positive-04-00-Abu-Dhabi-Muscat(38),
                        gmt-positive-04-00-Baku-Tbilisi(39),
                        gmt-positive-04-30-Kabul(40),
                        gmt-positive-05-00-Ekaterinburg(41),
                        gmt-positive-05-00-Islamabad-Karachi-Tashkent(42),
                        gmt-positive-05-30-Bombay-Calcutta-Madras-New-Delhi(43),
                        gmt-positive-06-00-Astana-Almaty-Dhaka(44),
                        gmt-positive-06-00-Colombo(45),
                        gmt-positive-07-00-Bangkok-Hanoi-Jakarta(46),
                        gmt-positive-08-00-Beijing-Chongqing-Hong-Kong-Urumqi(47),
                        gmt-positive-08-00-Perth(48),
                        gmt-positive-08-00-Singapore(49),
                        gmt-positive-08-00-Taipei(50),
                        gmt-positive-09-00-Osaka-Sapporo-Tokyo(51),
                        gmt-positive-09-00-Seoul(52),
                        gmt-positive-09-00-Yakutsk(53),
                        gmt-positive-09-30-Adelaide(54),
                        gmt-positive-09-30-Darwin(55),
                        gmt-positive-10-00-Brisbane(56),
                        gmt-positive-10-00-Canberra-Melbourne-Sydney(57),
                        gmt-positive-10-00-Guam-Port-Moresby(58),
                        gmt-positive-10-00-Hobart(59),
                        gmt-positive-10-00-Vladivostok(60),
                        gmt-positive-11-00-Magadan-Solomon-Is-New-Caledonia(61),
                        gmt-positive-12-00-Auckland-Wllington(62),
                        gmt-positive-12-00-Fiji-Kamchatka-Marshall-Is(63)
                        }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "UTC Timezone."
        ::= { sntp 3 }


sntpSwitchTimer OBJECT-TYPE
                SYNTAX      DisplayString (SIZE(0..255))
                MAX-ACCESS  read-only
                STATUS      current
                DESCRIPTION "The switch timer."
        ::= { sntp 5 }

sntpServer1 OBJECT-TYPE
                SYNTAX      DisplayString (SIZE(0..255))
                MAX-ACCESS  read-write
                STATUS      current
                DESCRIPTION "SNTP Server 1."
        ::= { sntp 9 }

sntpServer2 OBJECT-TYPE
                SYNTAX      DisplayString (SIZE(0..255))
                MAX-ACCESS  read-write
                STATUS      current
                DESCRIPTION "SNTP Server 2."
        ::= { sntp 10 }

sntpServerQueryPeriod OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Time Server Query Period."
        ::= { sntp 11 }

--
-- backupAndRestore
--

--backupServerIP OBJECT-TYPE
--                SYNTAX      IpAddress
--                MAX-ACCESS  read-write
--                STATUS      current
--                DESCRIPTION "The IP address of a TFTP server from which a
--                             firmware image can be uploaded."
--        DEFVAL { '00000000'H }
--        ::= { backupAndRestore  1 }

backupServerIP OBJECT-TYPE
                SYNTAX      DisplayString (SIZE(1..128))
                MAX-ACCESS  read-write
                STATUS      current
                DESCRIPTION "The IP address of a TFTP server from which a
                             firmware image can be uploaded."
        DEFVAL { '00000000'H }
        ::= { backupAndRestore  1 }

backupAgentBoardFwFileName OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..80))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION ""
        DEFVAL      { "data.bin" }
        ::= { backupAndRestore  2 }

backupStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger the TFTP
                     upload action.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { backupAndRestore  3 }

--restoreServerIP OBJECT-TYPE
--                SYNTAX      IpAddress
--                MAX-ACCESS  read-write
--                STATUS      current
--                DESCRIPTION "The IP address of a TFTP server from which a
--                             firmware image can be downloaded."
--        DEFVAL { '00000000'H }
--        ::= { backupAndRestore  4 }

restoreServerIP OBJECT-TYPE
                SYNTAX      DisplayString (SIZE(1..128))
                MAX-ACCESS  read-write
                STATUS      current
                DESCRIPTION "The IP address of a TFTP server from which a
                             firmware image can be downloaded."
        DEFVAL { '00000000'H }
        ::= { backupAndRestore  4 }

restoreAgentBoardFwFileName OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..80))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION ""
        DEFVAL      { "data.bin" }
        ::= { backupAndRestore  5 }

restoreStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger the TFTP
                     download action.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { backupAndRestore  6 }

--
-- factoryDefault
--

factoryDefaultAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger loading
                     factory default configuration.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { factoryDefault 1 }

--
-- systemReboot
--

systemRebootAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger the system
                     restart.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { systemReboot 1 }

--
-- switchCurrentPortNameList
--

switchCurrentPortNameListTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SwitchCurrentPortNameListEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and status information about
                     the logical ports in this system."
        ::= { basicSetting 10 }

switchCurrentPortNameListEntry OBJECT-TYPE
        SYNTAX      SwitchCurrentPortNameListEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about logical port name in this system. "
        INDEX       { swCurrentPortNameListIndex }
        ::= { switchCurrentPortNameListTable 1 }

SwitchCurrentPortNameListEntry ::= SEQUENCE
{
    swCurrentPortNameListIndex      Integer32,
    swCurrentPortNameListPortName   DisplayString,
    swCurrentPortNameListPortNumber DisplayString
}

swCurrentPortNameListIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of logical port."
        ::= { switchCurrentPortNameListEntry 1 }

swCurrentPortNameListPortName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The name of logical port."
        ::= { switchCurrentPortNameListEntry 2 }

swCurrentPortNameListPortNumber OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..30))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port number(s) that port name belong to.
                     If the logical port stood for a trunk group, this
                     object shows the members of trunk group."
        ::= { switchCurrentPortNameListEntry 3 }

--
-- agingTime
--
agingTime          OBJECT IDENTIFIER ::= { basicSetting 11 }

agingTimeSetting OBJECT-TYPE
       SYNTAX  Integer32(0..600)
       ACCESS  read-write
       STATUS  current
       DESCRIPTION   "The MAC address table flush time."
       ::= { agingTime 1 }

agingTimeStatus OBJECT-TYPE
       SYNTAX      INTEGER
                   {
                       enabled(1),
                       disabled(2)
                   }
       MAX-ACCESS  read-write
       STATUS      current
       DESCRIPTION "(1)Enable Flush MAC address table when the aging time is expired.
                    (2)Disable Flush MAC address table when the aging time is expired."
       ::= { agingTime 3 }

--
-- time
--

time  OBJECT IDENTIFIER ::= { basicSetting 12 }

timeCurrentTime OBJECT-TYPE
       SYNTAX  DateAndTime
       ACCESS  read-write
       STATUS  current
       DESCRIPTION   "The system current time."
       ::= { time 1 }

ptp  OBJECT IDENTIFIER ::= { time 2 }

ptpState OBJECT-TYPE
       SYNTAX      INTEGER
                   {
                       enabled(1),
                       disabled(2)
                   }
       MAX-ACCESS  read-write
       STATUS      current
       DESCRIPTION "Enable/Disable Percision Time protocol"
       ::= { ptp 1 }

ptpVersion OBJECT-TYPE
        SYNTAX      Integer32(1..2)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Percision Time protocol Version."
        ::= { ptp 2 }

ptpSyncInterval OBJECT-TYPE
        SYNTAX      Integer32(1..1024)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Percision Time protocol Sync Interval."
        ::= { ptp 3 }

ptpPeerToPeerState OBJECT-TYPE
        SYNTAX     INTEGER
                   {
                       enabled(1),
                       disabled(2)
                   }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Percision Time protocol peer to peer  state."
        ::= { ptp 4 }        
        
ptpClockStratum OBJECT-TYPE
        SYNTAX      Integer32(0..4)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Percision Time protocol Clock Stratum."
        ::= { ptp 5 }
        
ptpPriority1 OBJECT-TYPE
        SYNTAX      Integer32(0..255)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Percision Time protocol priority 1."
        ::= { ptp 6 }
        
ptpPriority2 OBJECT-TYPE
        SYNTAX      Integer32(0..255)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Percision Time protocol priority 2."
        ::= { ptp 7 }

ptpOffsetToMasterSeconds OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The seconds of the offset to master."
        ::= { ptp 8 } 
        
ptpOffsetToMasterNanoseconds OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The nanoseconds of the offset to master."
        ::= { ptp 9 }         

ptpV1GrandmasterUuid OBJECT-TYPE
        SYNTAX      OCTET STRING (SIZE (6))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The nanoseconds of the offset to master."
        ::= { ptp 10 }
        
ptpV1ParentUuid OBJECT-TYPE
        SYNTAX      OCTET STRING (SIZE (6))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The nanoseconds of the offset to master."
        ::= { ptp 11 }
                
ptpV1ClockIdentifier OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..4))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The nanoseconds of the offset to master."
        ::= { ptp 12 }
            
ptpV2GrandmasterIdentity OBJECT-TYPE
        SYNTAX      OCTET STRING (SIZE (8))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The nanoseconds of the offset to master."
        ::= { ptp 13 }
        
ptpV2ParentIdentity OBJECT-TYPE
        SYNTAX      OCTET STRING (SIZE (8))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The nanoseconds of the offset to master."
        ::= { ptp 14 }
                                           
ptpPortTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF PtpPortEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and status information about
                     the PTP ports."
        ::= { ptp 15 }
        
ptpPortEntry OBJECT-TYPE
        SYNTAX      PtpPortEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about the PTP ports."
        INDEX       { ptpPortIndex }
        ::= { ptpPortTable 1 }

PtpPortEntry ::= SEQUENCE
{
    ptpPortIndex     Integer32,
    ptpPortEnabled   INTEGER,
    ptpPortStatus    INTEGER
}

ptpPortIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Index of the PTP port table."
        ::= { ptpPortEntry 1 }

ptpPortEnabled OBJECT-TYPE
       SYNTAX      INTEGER
                   {
                       enabled(1),
                       disabled(2)
                   }
       MAX-ACCESS  read-write
       STATUS      current
       DESCRIPTION "Enable or disable PTP for the port."
       ::= { ptpPortEntry 2 }

ptpPortStatus OBJECT-TYPE
       SYNTAX      INTEGER
                   {
                       initializing(1), 
                       faulty(2),
                       disabled(3),
                       listening(4),
                       pre-master(5),
                       master(6),
                       passive(7),
                       uncalibrated(8),
                       slave(9)
                   }
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION "The status of the PTP port."
       ::= { ptpPortEntry 3 }
               
--
-- DIP switch
--

dipSwitch                  OBJECT IDENTIFIER ::= { basicSetting 13 }


dipSwitchControlStatus OBJECT-TYPE
       SYNTAX      INTEGER
                   {
                       enabled(1),
                       disabled(2)
                   }
       MAX-ACCESS  read-write
       STATUS      current
       DESCRIPTION "Enable/Disable DIP switch control function"
       ::= { dipSwitch 1 }

dipSwitchRingCompatibleMode OBJECT-TYPE
       SYNTAX      INTEGER
                   {
                       iA-Ring(1),
                       compatible-Ring(2)
                   }
       MAX-ACCESS  read-write
       STATUS      current
       DESCRIPTION "Configure the ring compatible mode."
       ::= { dipSwitch 2 }

--
-- dipSwitchTable
--

dipSwitchTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF DipSwitchEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and status information about
                     the DIP switch."
        ::= { dipSwitch 3 }

dipSwitchEntry OBJECT-TYPE
        SYNTAX      DipSwitchEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about the DIP switch."
        INDEX       { dipSwitchIndex }
        ::= { dipSwitchTable 1 }

DipSwitchEntry ::= SEQUENCE
{
    dipSwitchIndex    Integer32,
    dipSwitchStatus   INTEGER
}

dipSwitchIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Index of the DIP switch."
        ::= { dipSwitchEntry 1 }

dipSwitchStatus OBJECT-TYPE
       SYNTAX      INTEGER
                   {
                       enabled(1),
                       disabled(2)
                   }
       MAX-ACCESS  read-only
       STATUS      current
       DESCRIPTION "The status of the DIP switch entry."
       ::= { dipSwitchEntry 2 }

--
-- portConfiguration
--

portControl      OBJECT IDENTIFIER ::= { portConfiguration 1 }
rateLimiting     OBJECT IDENTIFIER ::= { portConfiguration 2 }
portTrunk     OBJECT IDENTIFIER ::= { portConfiguration 3 }

portCtrlTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF PortCtrlEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of status information and configuration about
                     each switch ports(including expansion slot)."
        ::= { portControl 1 }

portCtrlEntry OBJECT-TYPE
        SYNTAX      PortCtrlEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about configuration in one switch port of the switch."
        INDEX       { portCtrlIndex }
        ::= { portCtrlTable 1 }

PortCtrlEntry ::= SEQUENCE
{
    portCtrlIndex                Integer32,
    portCtrlPortName             DisplayString,
    portCtrlPortStatus           INTEGER,
    portCtrlNegotiation          INTEGER,
    portCtrlSpeed                INTEGER,
    portCtrlDuplex               INTEGER,
    portCtrlFlowControl          INTEGER
}

portCtrlIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of port management table."
        ::= { portCtrlEntry 1 }

portCtrlPortName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The name of port."
        ::= { portCtrlEntry 2 }

portCtrlPortStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "To disable (or enable) control of this port."
        ::= { portCtrlEntry 3 }

portCtrlNegotiation OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        auto(1),
                        force(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The mode of network negotiation,
                     (1)Automatic
                     (2)Force"
        ::= { portCtrlEntry 4 }

portCtrlSpeed OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        speed-10M(1),
                        speed-100M(2),
                        speed-1000M(3),
                        speed-10000M(4)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Set the speed of the port. If portCtrlNegotiation is auto,
                    this object can't be modified."
        ::= { portCtrlEntry 5 }

portCtrlDuplex OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        fullduplex(1),
                        halfduplex(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Set full-duplex or half-duplex mode of the port. If portCtrlNegotiation is auto,
                    this object can't be modified."
        ::= { portCtrlEntry 6 }

portCtrlFlowControl OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS       current
        DESCRIPTION "Set flow control."
        ::= { portCtrlEntry 7 }

--
-- PortStatusEntry
--
portStatusTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF PortStatusEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of current status about
                     each switch ports(including expansion slot)."
        ::= { portControl 2 }

portStatusEntry OBJECT-TYPE
        SYNTAX      PortStatusEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about configuration in one switch port of the switch."
        INDEX       { portStatusIndex }
        ::= { portStatusTable 1 }

PortStatusEntry ::= SEQUENCE
{
    portStatusIndex                Integer32,
    portStatusPortName             DisplayString,
    portStatusPortMode           DisplayString
}

portStatusIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of port management table."
        ::= { portStatusEntry 1 }

portStatusPortName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The name of port."
        ::= { portStatusEntry 2 }

portStatusPortMode OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Port media"
        ::= { portStatusEntry 3 }

--
-- MiniGBICPortStatusEntry
--
miniGBICPortStatusTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF MiniGBICPortStatusEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of current Mini-GBIC (Giga Bitrate Interface Converter) status about
                     each switch ports(including expansion slot)."
        ::= { portControl 3 }
miniGBICPortStatusEntry OBJECT-TYPE
        SYNTAX      MiniGBICPortStatusEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                      about Mini-GBIC (Giga Bitrate Interface Converter) status in one switch port of the switch."
        INDEX       { miniGBICPortStatusIndex }
        ::= { miniGBICPortStatusTable 1 }
MiniGBICPortStatusEntry ::= SEQUENCE
{
    miniGBICPortStatusIndex                Integer32,
    miniGBICPortStatusPortName             DisplayString,
    miniGBICPortStatusComCodes             DisplayString,
    miniGBICPortStatusVenderName           DisplayString,
    miniGBICPortStatusVenderPN             DisplayString,
    miniGBICPortStatusLW                   DisplayString,
    miniGBICPortStatusVenderSN             DisplayString,
    miniGBICPortStatusConType              DisplayString,
    miniGBICPortStatusTemp                 DisplayString,
    miniGBICPortStatusVoltage              DisplayString,
    miniGBICPortStatusTxPower              DisplayString,
    miniGBICPortStatusRxPower              DisplayString
}
miniGBICPortStatusIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of port management table."
        ::= { miniGBICPortStatusEntry 1 }
miniGBICPortStatusPortName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The name of port."
        ::= { miniGBICPortStatusEntry 2 }
miniGBICPortStatusComCodes OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..15))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Gigabit Ethernet Compliance Codes."
        ::= { miniGBICPortStatusEntry 3 }
miniGBICPortStatusVenderName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..15))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Vender Name."
        ::= { miniGBICPortStatusEntry 4 }
miniGBICPortStatusVenderPN OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..15))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Vendor Part Number."
        ::= { miniGBICPortStatusEntry 5 }
miniGBICPortStatusLW OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Laser wavelength."
        ::= { miniGBICPortStatusEntry 6 }
miniGBICPortStatusVenderSN OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..15))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Vendor Serial Number."
        ::= { miniGBICPortStatusEntry 7 }
miniGBICPortStatusConType OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Connector Type."
        ::= { miniGBICPortStatusEntry 8 }
miniGBICPortStatusTemp OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Temperature (C)."
        ::= { miniGBICPortStatusEntry 9 }
miniGBICPortStatusVoltage OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Voltage (V)."
        ::= { miniGBICPortStatusEntry 10 }
miniGBICPortStatusTxPower OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..15))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Tx Power (mW/dBm)."
       ::= { miniGBICPortStatusEntry 11 }
miniGBICPortStatusRxPower OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..15))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Rx Power (mW/dBm)."
        ::= { miniGBICPortStatusEntry 12 }
        
--
-- rateLimiting
--

rateLimitingTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF RateLimitingEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     port rate limiting in this system.
                     You can set up every port??s bandwidth rate and packet
                     limitation type."
        ::= { rateLimiting 1 }

rateLimitingEntry OBJECT-TYPE
        SYNTAX      RateLimitingEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about port rate limiting."
        INDEX       { rateLimitingPortNum }
        ::= { rateLimitingTable 1 }

RateLimitingEntry ::= SEQUENCE
{
    rateLimitingPortNum Integer32,
    rateLimitingIngressRate Integer32,
    rateLimitingEgressRate  Integer32,
    rateLimitingIngressBrust Integer32,
    rateLimitingEgressBrust  Integer32
}

rateLimitingPortNum OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Port number."
        ::= { rateLimitingEntry 1 }

rateLimitingIngressRate OBJECT-TYPE
        SYNTAX      Integer32(0..1048576)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Ingress rate, the rate range is from 0 kbps to 1048576 kbps
                     , and zero means no limit."
        ::= { rateLimitingEntry 3 }

rateLimitingEgressRate OBJECT-TYPE
        SYNTAX      Integer32(0..1048576)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Egress rate, the rate range is from 0 kbps to 1048576 kbps
                     , and zero means no limit."
        ::= { rateLimitingEntry 4 }

-- rateLimitingIngressBrust OBJECT-TYPE
--         SYNTAX      Integer32(0..1040)
--         MAX-ACCESS  read-write
--         STATUS      current
--         DESCRIPTION "Max burst size in kilobits."
--         ::= { rateLimitingEntry 5 }

-- rateLimitingEgressBrust OBJECT-TYPE
--         SYNTAX      Integer32(0..1040)
--         MAX-ACCESS  read-write
--         STATUS      current
--         DESCRIPTION "Max burst size in kilobits."
--         ::= { rateLimitingEntry 6 }

--
-- stormControl
--

stormControlTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF StormControlEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     port storm control in this system.
                     You can set up every port??s DLF limiting, Multicast limiting and Broadcast limiting."
        ::= { rateLimiting 2 }

stormControlEntry OBJECT-TYPE
        SYNTAX      StormControlEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about port storm control."
        INDEX       { stormControlIndex }
        ::= { stormControlTable 1 }

StormControlEntry ::= SEQUENCE
{
    stormControlIndex Integer32,
    stormControlDLF Integer32,
    stormControlMulticast  Integer32,
    stormControlBroadcast Integer32
} 

stormControlIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of port management table."
        ::= { stormControlEntry 1 }

stormControlDLF OBJECT-TYPE
        SYNTAX      Integer32(0..10000000) 
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The limiting value of DLF(Destination Lookup Fail) traffic storm filter
                     , the limiting value range is from 0 Kb to 10000000 Kb."
        ::= { stormControlEntry 2 }

stormControlMulticast OBJECT-TYPE
        SYNTAX      Integer32(0..10000000) 
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The limiting value of multicast traffic storm filter
                    , the limiting value range is from 0 Kb to 10000000 Kb."
        ::= { stormControlEntry 3 }

stormControlBroadcast OBJECT-TYPE
         SYNTAX      Integer32(0..10000000)
         MAX-ACCESS  read-write
         STATUS      current
         DESCRIPTION "The limiting value of broadcast traffic storm filter
                    , the limiting value range is from 0 Kb to 10000000 Kb."
         ::= { stormControlEntry 4 }       
--
-- portTrunk
--

aggregatorSetting OBJECT IDENTIFIER ::= { portTrunk 1 }
lacp              OBJECT IDENTIFIER ::= { portTrunk 4 }

portTrunkSysPri OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The system priority of trunking."
        ::= { aggregatorSetting 1}

portTrunkAggregatorTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF PortTrunkAggregatorEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     of each trunk group in this system.
                     Link aggregation lets you group up to switch_port_num
                     consecutive ports into a single dedicated connection.
                     This feature can expand bandwidth to a device on the
                     network, such as another switch or a server."
        ::= { aggregatorSetting 2 }

portTrunkAggregatorEntry OBJECT-TYPE
        SYNTAX      PortTrunkAggregatorEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about aggregator setting of the trunk."
        INDEX       { portTrunkAggregatorIndex }
        ::= { portTrunkAggregatorTable 1 }

PortTrunkAggregatorEntry ::= SEQUENCE
{
    portTrunkAggregatorIndex          Integer32,
    portTrunkAggregatorGroupName      DisplayString,
    portTrunkAggregatorMemberPorts    PortList,
    portTrunkAggregatorLACPStatus     INTEGER,
    portTrunkAggregatorLACPActivePorts PortList,
    portTrunkAggregatorHash           INTEGER,
    portTrunkAggregatorStatus         INTEGER
}

portTrunkAggregatorIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Index of trunk aggregator table."
        ::= { portTrunkAggregatorEntry 1 }

portTrunkAggregatorGroupName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..80))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The name of trunk group."
        ::= { portTrunkAggregatorEntry 2 }

portTrunkAggregatorMemberPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The members of trunk group."
        ::= { portTrunkAggregatorEntry 3 }

portTrunkAggregatorLACPStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The LACP function of trunk.
                     (1) LACP is enabled for the group.
                     (2) LACP is disabled for the group. "
        ::= { portTrunkAggregatorEntry 4 }

portTrunkAggregatorLACPActivePorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The active ports of the LACP group."
        ::= { portTrunkAggregatorEntry 5 }
        
portTrunkAggregatorHash OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        srcMac(1),
                        dstMac(2),
                        srcdstMac(3),
                        srcIp(4),
                        dstIp(5),
                        srcdstIp(6)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The Hash Type of trunk.
                     (1) Source MAC address.
                     (2) Destination MAC address. 
                     (3) Source+dest MAC address. 
                     (4) Source IP address. 
                     (5) Destination IP address. 
                     (6) Source+dest IP address. "
        ::= { portTrunkAggregatorEntry 6 }

portTrunkAggregatorStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        invalid(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of this entry. If this object is not equal to valid(1), all associated
                     the aggregator entry shall be deleted by the agent."
        ::= { portTrunkAggregatorEntry 7 }

lacpStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "(1) LACP is enabled for the system.
                     (2) LACP is disabled for the system."
        ::= { lacp 1 }

lacpPortInfoTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF LacpPortInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     of each trunk group in this system.
                     Link aggregation lets you group up to switch_port_num
                     consecutive ports into a single dedicated connection.
                     This feature can expand bandwidth to a device on the
                     network, such as another switch or a server."
        ::= { lacp 2 }

lacpPortInfoEntry OBJECT-TYPE
        SYNTAX      LacpPortInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about aggregator setting of the trunk."
        INDEX       { lacpPortInfoIndex }
        ::= { lacpPortInfoTable 1 }

LacpPortInfoEntry ::= SEQUENCE
{
    lacpPortInfoIndex        Integer32,
    lacpPortInfoLACPStatus   INTEGER,
    lacpPortInfoLACPGroupID  Integer32,
    lacpPortInfoLACPPartner  INTEGER
}

lacpPortInfoIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "This object indicates the port number."
        ::= { lacpPortInfoEntry 1 }

lacpPortInfoLACPStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        passive(2),
                        disabled(3)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "This object indicates the LACP status of the
                     port."
        ::= { lacpPortInfoEntry 2 }

lacpPortInfoLACPGroupID OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "This object indicates the LACP group ID of the
                     port."
        ::= { lacpPortInfoEntry 3 }

lacpPortInfoLACPPartner OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        true(1),
                        false(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "This object indicates the partner status of the
                     port."
        ::= { lacpPortInfoEntry 4 }

--
-- ringRedundancy
--

superRing         OBJECT IDENTIFIER ::= { ringRedundancy 1 }
rstp              OBJECT IDENTIFIER ::= { ringRedundancy 2 }
bridgeInformation OBJECT IDENTIFIER ::= { ringRedundancy 3 }
--
-- superRing
--

superRingStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Super Ring function status.
                     (1) Super Ring function is enabled.
                     (2) Super Ring function is disabled."
        ::= { superRing 1 }

superRingRingMasterStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Ring Master function status.
                     (1) Ring Master function is enabled.
                     (2) Ring Master function is disabled.
                     This item can't be modified if superRingStatus
                     object is set to disabled(2)
                     "
        ::= { superRing 2 }

superRingRingPort1 OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Port Id(1~8) will be working port.
                     The system will automatically decide which port
                     is working port and which port is backup port if
                     the superRingStatus object is set to enabled(1)."
        ::= { superRing 3 }


superRingRingPort2 OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Port Id(1~8) will be working port.
                     The system will automatically decide which port
                     is working port and which port is backup port if
                     the superRingStatus object is set to enabled(1)."
        ::= { superRing 4 }

--
-- rstp
--

rstpStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enalbed(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Enable/Disable rapid spanning tree protocol for system."
        ::= { rstp 1 }

rstpPriority OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "A value used to identify the root bridge. The bridge with
                     the lowest value has the highest priority and is selected
                     as the root. Enter a number 0 through 61440 in steps of
                     4096. If you change the value, you must restart RSTP.
                     This item can't be modified, if rstpStatus was disabled."
        ::= { rstp 2 }

rstpMaxAge OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The number of seconds a bridge waits without receiving
                     Spanning-Tree Protocol configuration messages before
                     attempting a reconfiguration. Enter a number 6 through
                     40.
                     Note: 2*(Forward Delay Time-1) should be greater than or
                     equal to the Max Age. The Max Age should be greater
                     than or equal to 2*(Hello Time + 1).
                     This item can't be modified, if rstpStatus was disabled."
        ::= { rstp 3 }

rstpHelloTime  OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The number of seconds between the transmission of
                     Spanning-Tree Protocol configuration messages.
                     Enter a number 1 through 10
                     Note: 2*(Forward Delay Time-1) should be greater than or
                     equal to the Max Age. The Max Age should be greater
                     than or equal to 2*(Hello Time + 1).
                     This item can't be modified, if rstpStatus was disabled."
        ::= { rstp 4 }

rstpForwardDelayTime  OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The number of seconds a port waits before changing
                     from its Spanning-Tree Protocol learning and listening
                     states to the forwarding state. Enter a number 4 through
                     30.
                     Note:2*(Forward Delay Time-1) should be greater than or
                     equal to the Max Age. The Max Age should be greater
                     than or equal to 2*(Hello Time + 1).
                     This item can't be modified, if rstpStatus was disabled."
        ::= { rstp 5 }

rstpPerPortCfgTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF RstpPerPortCfgEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     rapid spanning tree (Per port)."
        ::= { rstp 6 }

rstpPerPortCfgEntry OBJECT-TYPE
        SYNTAX      RstpPerPortCfgEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing configuration
                     about RSTP (Per port)."
        INDEX       { rstpPerPortCfgPortNum }
        ::= { rstpPerPortCfgTable 1 }

RstpPerPortCfgEntry ::= SEQUENCE
{
    rstpPerPortCfgPortNum         Integer32,
    rstpPerPortCfgPathCost        Integer32,
    rstpPerPortCfgPriority        Integer32,
    rstpPerPortCfgAdminP2P        INTEGER,
    rstpPerPortCfgAdminEdge       INTEGER
}

rstpPerPortCfgPortNum OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Port number."
        ::= { rstpPerPortCfgEntry 1 }

rstpPerPortCfgPathCost OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The cost of the path to the other bridge from
                     this transmitting bridge at the specified port.
                     Enter a number 1 through *********."
        ::= { rstpPerPortCfgEntry 2 }

rstpPerPortCfgPriority OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Decide which port should be blocked by priority
                     in LAN. Enter a number 0 through 240 in steps of
                     16."
        ::= { rstpPerPortCfgEntry 3 }

rstpPerPortCfgAdminP2P OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        true(1),
                        false(2),
                        auto(3)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Some of the rapid state transactions that are
                     possible within RSTP are dependent upon whether
                     the Port concerned can only be connected to
                     exactly one other Bridge(ie., it is served by a
                     point-to-point LAN segment), or can be connected
                     to two or more Bridges(i.e., it is served by a
                     shared medium LAN segment).
                     The adminPointToPointMAC allow the p2p status
                     of the link to be manipulated administratively."
        ::= { rstpPerPortCfgEntry 4 }

rstpPerPortCfgAdminEdge OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        true(1),
                        false(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Present in implementations that support the
                     identification of edge ports. All ports
                     directly connected to end stations cannot
                     create bridging loops in the network and can
                     thus directly transition to forwarding,
                     skipping the listening and learning stages."
        ::= { rstpPerPortCfgEntry 5 }

--
-- bridgeInformation
--

rstpRootBridgeInformationTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF RstpRootBridgeInformationEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about root bridge
                     of rapid spanning tree in this system."
        ::= { bridgeInformation 1 }

rstpRootBridgeInformationEntry OBJECT-TYPE
        SYNTAX      RstpRootBridgeInformationEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about root bridge information of the RSTP."
        INDEX       { rstpRootBridgeInformationIndex }
        ::= { rstpRootBridgeInformationTable 1 }

RstpRootBridgeInformationEntry ::= SEQUENCE
{
    rstpRootBridgeInformationIndex              Integer32,
    rstpRootBridgeInformationBridgeID           DisplayString,
    rstpRootBridgeInformationRootPriority       Integer32,
    rstpRootBridgeInformationRootPort           DisplayString,
    rstpRootBridgeInformationRootPathCost       Integer32,
    rstpRootBridgeInformationMaxAge             Integer32,
    rstpRootBridgeInformationHelloTime          Integer32,
    rstpRootBridgeInformationForwardDelay       Integer32,
    rstpRootBridgeInformationTopologyChanges    Integer32,
    rstpRootBridgeInformationLastTopologyChange Integer32
}

rstpRootBridgeInformationIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of root bridge information table."
        ::= { rstpRootBridgeInformationEntry 1 }

rstpRootBridgeInformationBridgeID OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..255))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Bridge ID."
        ::= { rstpRootBridgeInformationEntry 2 }

rstpRootBridgeInformationRootPriority OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Root Priority."
        ::= { rstpRootBridgeInformationEntry 3 }

rstpRootBridgeInformationRootPort  OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..80))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Root Port."
        ::= { rstpRootBridgeInformationEntry 4 }

rstpRootBridgeInformationRootPathCost OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Root Path Cost."
        ::= { rstpRootBridgeInformationEntry 5 }

rstpRootBridgeInformationMaxAge  OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Max Age."
        ::= { rstpRootBridgeInformationEntry 6 }

rstpRootBridgeInformationHelloTime OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Hello Time."
        ::= { rstpRootBridgeInformationEntry 7 }

rstpRootBridgeInformationForwardDelay  OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Forward Delay Time."
        ::= { rstpRootBridgeInformationEntry 8 }

rstpRootBridgeInformationTopologyChanges  OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The topology changes counter."
        ::= { rstpRootBridgeInformationEntry 9 }

rstpRootBridgeInformationLastTopologyChange  OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "This object indicates the time from the last topology changed."
        ::= { rstpRootBridgeInformationEntry 10 }

rstpPerPortInfoTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF RstpPerPortInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     rapid spanning tree (Per port)."
        ::= { bridgeInformation 2 }

rstpPerPortInfoEntry OBJECT-TYPE
        SYNTAX      RstpPerPortInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about RSTP (Per port)."
        INDEX       { rstpPerPortInfoPortNum }
        ::= { rstpPerPortInfoTable 1 }

RstpPerPortInfoEntry ::= SEQUENCE
{
    rstpPerPortInfoPortNum         Integer32,
    rstpPerPortInfoPathCost        Integer32,
    rstpPerPortInfoPriority        Integer32,
    rstpPerPortInfoAdminP2P        INTEGER,
    rstpPerPortInfoAdminEdge       INTEGER,
    rstpPerPortInfoState           INTEGER,
    rstpPerPortInfoRole            INTEGER,
    rstpPerPortInfoOperPathCost    Integer32,
    rstpPerPortInfoOperP2P         INTEGER,
    rstpPerPortInfoOperEdge        INTEGER
}

rstpPerPortInfoPortNum OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Port number."
        ::= { rstpPerPortInfoEntry 1 }

rstpPerPortInfoPathCost OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The cost of the path to the other bridge from
                     this transmitting bridge at the specified port.
                     Enter a number 1 through *********."
        ::= { rstpPerPortInfoEntry 2 }

rstpPerPortInfoPriority OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Decide which port should be blocked by priority
                     in LAN. Enter a number 0 through 240 in steps of
                     16."
        ::= { rstpPerPortInfoEntry 3 }

rstpPerPortInfoAdminP2P OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        auto(1),
                        true(2),
                        false(3)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Some of the rapid state transactions that are
                     possible within RSTP are dependent upon whether
                     the Port concerned can only be connected to
                     exactly one other Bridge(ie., it is served by a
                     point-to-point LAN segment), or can be connected
                     to two or more Bridges(i.e., it is served by a
                     shared medium LAN segment).
                     The adminPointToPointMAC allow the p2p status
                     of the link to be manipulated administratively."
        ::= { rstpPerPortInfoEntry 4 }

rstpPerPortInfoAdminEdge OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        true(1),
                        false(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Present in implementations that support the
                     identification of edge ports. All ports
                     directly connected to end stations cannot
                     create bridging loops in the network and can
                     thus directly transition to forwarding,
                     skipping the listening and learning stages."
        ::= { rstpPerPortInfoEntry 5 }

rstpPerPortInfoState OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        nonStp(1),
                        learning(2),
                        forwarding(3),
                        disabled(4),
                        discarding(5),
                        unknown(6)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port state."
        ::= { rstpPerPortInfoEntry 7 }

rstpPerPortInfoRole OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        disabled(1),
                        root(2),
                        designated(3),
                        alternated(4),
                        backup(5),
                        nonStp(6),
                        unknown(7)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port role."
        ::= { rstpPerPortInfoEntry 8 }

rstpPerPortInfoOperPathCost OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The operation path cost."
        ::= { rstpPerPortInfoEntry 9 }

rstpPerPortInfoOperP2P OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        true(1),
                        false(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The operation point to point state."
        ::= { rstpPerPortInfoEntry 10 }

rstpPerPortInfoOperEdge OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        true(1),
                        false(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The operation edge port state."
        ::= { rstpPerPortInfoEntry 11 }

erps OBJECT IDENTIFIER ::= { ringRedundancy 4 }

erpsStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The ERPS status."
        ::= { erps 1 }

erpsLogStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The ERPS log status."
        ::= { erps 2 }

erpsRingTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF ErpsRingEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     the ERPS ring (Per R-APS VLAN)."
        ::= { erps 3 }

erpsRingEntry OBJECT-TYPE
        SYNTAX      ErpsRingEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about ERPS ring (Per R-APS VLAN)."
        INDEX       { erpsRapsVLANVid }
        ::= { erpsRingTable 1 }

ErpsRingEntry ::= SEQUENCE
{
    erpsRapsVLANVid             Integer32,
    erpsRapsVLANWestPort        DisplayString,
    erpsRapsVLANWestPortStatus  INTEGER,
    erpsRapsVLANEastPort        DisplayString,
    erpsRapsVLANEastPortStatus  INTEGER,
    erpsRapsVLANVirtualChannel  INTEGER,
    erpsRapsVLANRplOwner        INTEGER,
    erpsRapsVLANRplPort         INTEGER,
    erpsRapsVLANWtrTimer        Integer32,
    erpsRapsVLANHoldoffTimer    Integer32,
    erpsRapsVLANGuardTimer      Integer32,
    erpsRapsVLANMel             Integer32,
    erpsRapsVLANPropagateTC     INTEGER,
    erpsRapsVLANNodeStatus      INTEGER,
    erpsRapsVLANEnable          INTEGER,
    erpsRapsVLANCtrlStatus      INTEGER
}

erpsRapsVLANVid OBJECT-TYPE
        SYNTAX      Integer32(1..4094)
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The R-APS VLAN ID of the ring."
        ::= { erpsRingEntry 1 }

erpsRapsVLANWestPort OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..15))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The west port of the ring"
        ::= { erpsRingEntry 2 }

erpsRapsVLANEastPort OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..15))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The east port of the ring"
        ::= { erpsRingEntry 3 }

erpsRapsVLANWestPortStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        forwarding(1),
                        blocking(2),
                        signal-fail-blocking(3)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The west port state of the ring"
        ::= { erpsRingEntry 4 }

erpsRapsVLANEastPortStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        forwarding(1),
                        blocking(2),
                        signal-fail-blocking(3)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The east port state of the ring"
        ::= { erpsRingEntry 5 }

erpsRapsVLANVirtualChannel OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        none(0),
                        west-port(1),
                        east-port(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The virtual channel port of the ring"
        ::= { erpsRingEntry 6 }

erpsRapsVLANRplOwner OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of the RPL owner."
        ::= { erpsRingEntry 7 }

erpsRapsVLANRplPort OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        none(0),
                        west-port(1),
                        east-port(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The RPL port."
        ::= { erpsRingEntry 8 }

erpsRapsVLANWtrTimer OBJECT-TYPE
        SYNTAX      Integer32(0..12)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The WTR timer (minutes)."
        ::= { erpsRingEntry 9 }

erpsRapsVLANHoldoffTimer OBJECT-TYPE
        SYNTAX      Integer32(0..10000)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The holdoff timer (milliseconds)."
        ::= { erpsRingEntry 10 }

erpsRapsVLANGuardTimer OBJECT-TYPE
        SYNTAX      Integer32(10..2000)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The guard timer (milliseconds)."
        ::= { erpsRingEntry 11 }

erpsRapsVLANMel OBJECT-TYPE
        SYNTAX      Integer32(0..7)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The MEL."
        ::= { erpsRingEntry 12 }

erpsRapsVLANPropagateTC OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The propagation topology changed status."
        ::= { erpsRingEntry 13 }

erpsRapsVLANNodeStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        none(0),
                        idle(1),
                        protection(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The node status."
        ::= { erpsRingEntry 14 }

erpsRapsVLANEnable OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The R-APS VLAN status."
        ::= { erpsRingEntry 15 }

erpsRapsVLANCtrlStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        invalid(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "Create or destroy the ring."
        ::= { erpsRingEntry 16 }

erpsUerpsStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The UERPS status."
        ::= { erps 4 }

erpsHeartbeatInterval OBJECT-TYPE
        SYNTAX      Integer32(50..10000)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The Heartbeat Interval (milliseconds)."
        ::= { erps 5 }
--
-- ieee8021qVlan
--

vlanOperationMode OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        ieee8021q(1),
                        portbased(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The operation mode of VLAN.
                        (1)IEEE802.1Q VLAN,
                        (2)Portbased VLAN"
        ::= { ieee8021qVlan 1 }

vlanGVRP OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Enable GVRP Protocol. The item has no meaning,
                     if the operation mode of VLAN isn't IEEE802.1Q."
        ::= { ieee8021qVlan 2 }

vlanIEEE8021QGroupTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF VlanIEEE8021QGroupEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration of
                     static ieee802.1q group in this system.
                     Tag-based VLANs are based on IEEE 802.1Q
                     specifications. Traffic is forwarded to VLAN member
                     ports based on identifying VLAN tags in data packets.
                     You can also configure the switch to interoperate
                     with existing tag-based VLAN networks and legacy
                     non-tag networks."
        ::= { ieee8021qVlan 4 }

vlanIEEE8021QGroupEntry OBJECT-TYPE
        SYNTAX      VlanIEEE8021QGroupEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about group name of the IEEE802.1Q VLAN."
        INDEX       { vlanIEEE8021QGroupVid }
        ::= { vlanIEEE8021QGroupTable 1 }

VlanIEEE8021QGroupEntry ::= SEQUENCE
{
    vlanIEEE8021QGroupVid     Integer32,
    vlanIEEE8021QGroupName    DisplayString,
    vlanIEEE8021QGroupStatus  INTEGER,
    vlanIEEE8021QMemberPorts    PortList,
    vlanIEEE8021QTaggedPorts    PortList
}

vlanIEEE8021QGroupVid OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "VLAN ID."
        ::= { vlanIEEE8021QGroupEntry 1 }

vlanIEEE8021QGroupName  OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..16))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The group name of VLAN."
        ::= { vlanIEEE8021QGroupEntry 2 }

vlanIEEE8021QGroupStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        invalid(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The status of this entry. If this object is not equal to valid(1), all associated
 	    VLAN entries shall be deleted by the agent."
        ::= { vlanIEEE8021QGroupEntry 3 }

vlanIEEE8021QMemberPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The member port list of the VLAN"
        ::= { vlanIEEE8021QGroupEntry 4 }

vlanIEEE8021QTaggedPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The tagged port list of the VLAN"
        ::= { vlanIEEE8021QGroupEntry 5 }

vlanPortBasedTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF VlanPortBasedEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     of each port based VLAN group in this
                     system. Traffic is forwarded to the member ports of
                     the same VLAN group. By adding ports to the VLAN you
                     have created one port-based VLAN group completely"
        ::= { ieee8021qVlan 5 }

vlanPortBasedEntry OBJECT-TYPE
        SYNTAX      VlanPortBasedEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about port based VLAN."
        INDEX       { vlanPortBasedIndex }
        ::= { vlanPortBasedTable 1 }

VlanPortBasedEntry ::= SEQUENCE
{
    vlanPortBasedIndex         Integer32,
    vlanPortBasedMembers       PortList
}

vlanPortBasedIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Index of port-based VLAN table."
        ::= { vlanPortBasedEntry 1 }

vlanPortBasedMembers OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The member mask of VLAN.
                     Note: If there were a trunk group, all of trunk
                     members should be in the same VLAN group.
                     If some members of trunk group doesn't be included
                     in VLAN group, the SNMP agent reports error."
        ::= { vlanPortBasedEntry 4 }

vlanPortPvidTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF VlanPortPvidEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     of the port VLAN ID."
        ::= { ieee8021qVlan 7 }

vlanPortPvidEntry OBJECT-TYPE
        SYNTAX      VlanPortPvidEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about the port VLAN ID."
        INDEX       { vlanPortID }
        ::= { vlanPortPvidTable 1 }

VlanPortPvidEntry ::= SEQUENCE
{
    vlanPortID   Integer32,
    vlanPortPvid Integer32
}

vlanPortID OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port number"
        ::= { vlanPortPvidEntry 1 }

vlanPortPvid OBJECT-TYPE
        SYNTAX      Integer32(1..4094)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The VLAN ID of the port VLAN ID"
        ::= { vlanPortPvidEntry 2 }

vlanGVRPPortTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF VlanGVRPPortEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     of GVRP(per port)."
        ::= { ieee8021qVlan 8 }

vlanGVRPPortEntry OBJECT-TYPE
        SYNTAX      VlanGVRPPortEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about GVRP(per port)."
        INDEX       { vlanGVRPPortIndex }
        ::= { vlanGVRPPortTable 1 }

VlanGVRPPortEntry ::= SEQUENCE
{
    vlanGVRPPortIndex  Integer32,
    vlanGVRPPortStatus INTEGER
}

vlanGVRPPortIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port number"
        ::= { vlanGVRPPortEntry 1 }


vlanGVRPPortStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The GVRP status of the port"
        ::= { vlanGVRPPortEntry 2 }

vlanManageVLANID OBJECT-TYPE
        SYNTAX      Integer32(1..4094)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The management VLAN ID"
        ::= { ieee8021qVlan 9 }


gvrpStatistics OBJECT IDENTIFIER ::= { ieee8021qVlan 10 }

gvrpStatisticsClearAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger to clear
                     the GVRP counter.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { gvrpStatistics 1 }

gvrpStatisticsRxJoinEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX join empty packet."
        ::= {gvrpStatistics 2 }
        
gvrpStatisticsTxJoinEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX join empty packet."
        ::= {gvrpStatistics 3 }        
        
gvrpStatisticsRxJoinIn OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX join in packet."
        ::= {gvrpStatistics 4 }
        
gvrpStatisticsTxJoinIn OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX join in packet."
        ::= {gvrpStatistics 5 }
        
gvrpStatisticsRxEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX empty packet."
        ::= {gvrpStatistics 6 }
        
gvrpStatisticsTxEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX empty packet."
        ::= {gvrpStatistics 7 }
        
gvrpStatisticsRxLeaveIn OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX leave in packet."
        ::= {gvrpStatistics 8 }
        
gvrpStatisticsTxLeaveIn OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX leave in packet."
        ::= {gvrpStatistics 9 }
        
gvrpStatisticsRxLeaveEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX leave empty packet."
        ::= {gvrpStatistics 10 }
        
gvrpStatisticsTxLeaveEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX leave empty packet."
        ::= {gvrpStatistics 11 }
        
gvrpStatisticsRxLeaveAll OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX leave all packet."
        ::= {gvrpStatistics 12 }
        
gvrpStatisticsTxLeaveAll OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX leave all packet."
        ::= {gvrpStatistics 13 }                       
        
vlanIEEE8021QGroupInfoTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF VlanIEEE8021QGroupInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration of
                     static and dynamic ieee802.1q group in this system.
                     Tag-based VLANs are based on IEEE 802.1Q
                     specifications. Traffic is forwarded to VLAN member
                     ports based on identifying VLAN tags in data packets.
                     You can also configure the switch to interoperate
                     with existing tag-based VLAN networks and legacy
                     non-tag networks."
        ::= { ieee8021qVlan 11 }

vlanIEEE8021QGroupInfoEntry OBJECT-TYPE
        SYNTAX      VlanIEEE8021QGroupInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about group name of the IEEE802.1Q VLAN."
        INDEX       { vlanIEEE8021QGroupInfoVid }
        ::= { vlanIEEE8021QGroupInfoTable 1 }

VlanIEEE8021QGroupInfoEntry ::= SEQUENCE
{
    vlanIEEE8021QGroupInfoVid                 Integer32,
    vlanIEEE8021QGroupInfoStaticMemberPorts   PortList,
    vlanIEEE8021QGroupInfoStaticTaggedPorts   PortList,
    vlanIEEE8021QGroupInfoDynamicMemberPorts  PortList,
    vlanIEEE8021QGroupInfoDynamicTaggedPorts  PortList,    
    vlanIEEE8021QGroupInfoTaggedPorts         PortList,    
    vlanIEEE8021QGroupInfoMemberPorts         PortList        
}

vlanIEEE8021QGroupInfoVid OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "VLAN ID."
        ::= { vlanIEEE8021QGroupInfoEntry 1 }

vlanIEEE8021QGroupInfoStaticMemberPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The static member port list of the VLAN."
        ::= { vlanIEEE8021QGroupInfoEntry 2 }

vlanIEEE8021QGroupInfoStaticTaggedPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The static tagged port list of the VLAN."
        ::= { vlanIEEE8021QGroupInfoEntry 3 }
        
vlanIEEE8021QGroupInfoDynamicMemberPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The dynamic member port list of the VLAN."
        ::= { vlanIEEE8021QGroupInfoEntry 4 }

vlanIEEE8021QGroupInfoDynamicTaggedPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The dynamic tagged port list of the VLAN."
        ::= { vlanIEEE8021QGroupInfoEntry 5 }   

vlanIEEE8021QGroupInfoMemberPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The static and dynamic tagged port list of the VLAN."
        ::= { vlanIEEE8021QGroupInfoEntry 6 }   

vlanIEEE8021QGroupInfoTaggedPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The static and dynamic member port list of the VLAN."
        ::= { vlanIEEE8021QGroupInfoEntry 7 }

vlanQinQTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF VlanQinQEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     of the QinQ."
        ::= { ieee8021qVlan 12 }

vlanQinQEntry OBJECT-TYPE
        SYNTAX      VlanQinQEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about the QinQ."
        INDEX       { vlanQinQPortID }
        ::= { vlanQinQTable 1 }

VlanQinQEntry ::= SEQUENCE
{
    vlanQinQPortID     Integer32,
    vlanQinQPortStatus INTEGER,
    vlanQinQTPID       DisplayString
}

vlanQinQPortID OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port number"
        ::= { vlanQinQEntry 1 }
        
vlanQinQPortStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "To disable (or enable) QinQ of this port."
        ::= { vlanQinQEntry 2 }

vlanQinQTPID OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The TPID of the QinQ port.(The default TPID is 8100)"
        ::= { vlanQinQEntry 3 }

--
-- trafficPrioritization
--

qosPolicy OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        a-strict-priority-scheme(2),
                        weighted-round-robin-scheme(3),
                        deficit-round-robin-scheme(4)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "(2)Use the strict priority scheme.
                     (3)Use the weighted round-robin scheme.
                     (4)Use the deficit round-robin scheme.
                     Using the  weight fair queue scheme: the switch will follow the
                     rates to process priority queue from Hi to lowest queue.
                     Use the strict priority scheme: Always higher queue will be process first,
                     except higher queue is empty."
        ::= { trafficPrioritization 1 }

qosCOSTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF QosCOSEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     PortBased QOS."
        ::= { trafficPrioritization 4 }


qosCOSEntry OBJECT-TYPE
        SYNTAX      QosCOSEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about COS QOS."
        INDEX       { qosCOSPriority }
        ::= { qosCOSTable 1 }

QosCOSEntry ::= SEQUENCE
{
    qosCOSPriority       Integer32,
    qosCOSPriorityQueue  INTEGER
}

qosCOSPriority OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "COS priority."
        ::= { qosCOSEntry 1 }

qosCOSPriorityQueue OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        queue0(0),
                        queue1(1),
                        queue2(2),
                        queue3(3),
                        queue4(4),
                        queue5(5),
                        queue6(6),
                        queue7(7)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The port priority will follow the COS priority that you have assigned
                     to the priority queue."
        ::= { qosCOSEntry 3 }

qosTOSTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF QosTOSEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     PortBased QOS."
        ::= { trafficPrioritization 6 }

qosTOSEntry OBJECT-TYPE
        SYNTAX      QosTOSEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about TOS QOS."
        INDEX       { qosTOSPriority }
        ::= { qosTOSTable 1 }

QosTOSEntry ::= SEQUENCE
{
    qosTOSPriority       Integer32,
    qosTOSPriorityQueue  INTEGER
}

qosTOSPriority OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "TOS priority."
        ::= { qosTOSEntry 1 }

qosTOSPriorityQueue OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        queue0(0),
                        queue1(1),
                        queue2(2),
                        queue3(3),
                        queue4(4),
                        queue5(5),
                        queue6(6),
                        queue7(7)
                    }
     MAX-ACCESS  read-write
     STATUS      current
     DESCRIPTION "The port priority will follow the TOS priority that you have assigned
                  to the priority queue."
     ::= { qosTOSEntry 3 }

--
-- multicastFiltering
--

igmpStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to enable or disable IGMP snooping on the device.
                     When enabled, the device will examine IGMP packets and set
                     up filters for IGMP ports.

                     The Internet Group Management Protocol (IGMP) is an internal
                     protocol of the Internet Protocol (IP) suite. IP manages
                     multicast traffic by using switches, routers, and hosts that
                     support IGMP. Enabling IGMP allows the ports to detect IGMP
                     queries and report packets and manage IP multicast traffic
                     through the switch. IGMP have three fundamental types of
                     message as follows:

                     Message     Description
                     --------------------------------------------------------------
                     Query       A message sent from the querier (IGMP router or switch)
                                 asking for a response from each host belonging to the
                                 multicast group.

                     Report      A message sent by a host to the querier to indicate
                                 that the host wants to be or is a member of a given
                                 group indicated in the report message.

                     Leave Group A message sent by a host to the querier to indicate
                                 that the host has quit to be a member of a specific
                                 multicast group.

                     You can enable IGMP protocol and IGMP Query function. You will see
                     the IGMP snooping information in this section -- difference
                     multicast group VID and member port, and IP multicast addresses
                     range from ********* through ***************."
        ::= { multicastFiltering  1 }

staticMulticastIPTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF StaticMulticastIPEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     the static multicast IP entries."
        ::= { multicastFiltering  4 }

staticMulticastIPEntry OBJECT-TYPE
        SYNTAX      StaticMulticastIPEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about the static multicast IP."
        INDEX       { staticMulticastIndex }
        ::= { staticMulticastIPTable 1 }

StaticMulticastIPEntry ::= SEQUENCE
{
    staticMulticastIndex   Integer32,
    staticMulticastIPAddr  IpAddress,
    staticMulticastIPVID     Integer32,
    staticMulticastIPMembers PortList,
    staticMulticastIPStatus  INTEGER
}

staticMulticastIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The Index of the static multicast entry."
        ::= { staticMulticastIPEntry 1 }

staticMulticastIPAddr OBJECT-TYPE
        SYNTAX      IpAddress
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The IP address of the static multicast entry."
        ::= { staticMulticastIPEntry 2 }

staticMulticastIPVID OBJECT-TYPE
        SYNTAX      Integer32(1..4094)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The VLAN id of the static multicast entry."
        ::= { staticMulticastIPEntry 3 }

staticMulticastIPMembers OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The member port list of the static multicast entry."
        ::= { staticMulticastIPEntry 4 }

staticMulticastIPStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        invalid(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of the static multicast entry."
        ::= { staticMulticastIPEntry 5 }

igmpFastLeave OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The fast leave status of IGMP"
        ::= { multicastFiltering 5 }

igmpProxy OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The status of IGMP proxy"
        ::= { multicastFiltering 6 }

igmpDynamicTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF IgmpDynamicEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     IGMP dynamic entries."
        ::= { multicastFiltering  7 }

igmpDynamicEntry OBJECT-TYPE
        SYNTAX      IgmpDynamicEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about IGMP."
        INDEX       { igmpDynamicEntryIndex }
        ::= { igmpDynamicTable 1 }

IgmpDynamicEntry ::= SEQUENCE
{
    igmpDynamicEntryIndex    Integer32,
    igmpDynamicEntryIPAddr   IpAddress,
    igmpDynamicEntryVID      Integer32,
    igmpDynamicEntryJoinPort  Integer32,
    igmpDynamicEntryLiftTime     Integer32
}

igmpDynamicEntryIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Index of IGMP table."
        ::= { igmpDynamicEntry 1 }

igmpDynamicEntryIPAddr  OBJECT-TYPE
                SYNTAX      IpAddress
                MAX-ACCESS  read-only
                STATUS      current
                DESCRIPTION "The IP address of an IGMP entry."
        ::= { igmpDynamicEntry 2 }

igmpDynamicEntryVID OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The VLAN ID of an IGMP entry."
        ::= { igmpDynamicEntry 3 }

igmpDynamicEntryJoinPort OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The join port mask of IGMP entry."
        ::= { igmpDynamicEntry 4 }

igmpDynamicEntryLiftTime OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The lift time of the IGMP entry."
        ::= { igmpDynamicEntry 5 }
        
igmpStatistics OBJECT IDENTIFIER ::= { multicastFiltering 8 }

igmpStatisticsClearAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION
        "Setting this object to active(1) trigger to clear IGMP 
         statistics counter. Setting this object to notActive(2) has no effect.
         The system always returns the value notActive(2) when this object is read."
        ::= { igmpStatistics 1 }

igmpStatisticsRxTotal OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the RX IGMP packets."
        ::= { igmpStatistics 2 }
        
igmpStatisticsRxValid OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the RX valid IGMP packets."
        ::= { igmpStatistics 3 }

igmpStatisticsRxInvalid OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the RX IGMP invalid packets."
        ::= { igmpStatistics 4 }

igmpStatisticsRxGeneralQueries OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the RX IGMP general query packets."
        ::= { igmpStatistics 5 }

igmpStatisticsTxGeneralQueries OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the TX IGMP general query packets."
        ::= { igmpStatistics 6 }
        
igmpStatisticsRxGroupSpecificQueries OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the RX IGMP group specific query packets."
        ::= { igmpStatistics 7 }

igmpStatisticsTxGroupSpecificQueries OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the TX IGMP group specific query packets."
        ::= { igmpStatistics 8 }      
          
igmpStatisticsRxLeaves OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the RX IGMP leaves packets."
        ::= { igmpStatistics 9 }                                                

igmpStatisticsTxLeaves OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the TX IGMP leaves packets."
        ::= { igmpStatistics 10 } 

igmpStatisticsRxReports OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the RX IGMP report packets."
        ::= { igmpStatistics 11 }         

igmpStatisticsTxReports OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the TX IGMP report packets."
        ::= { igmpStatistics 12 }   
        
igmpStatisticsRxOthers OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
        "The counter of the RX IGMP other packets."
        ::= { igmpStatistics 13 }             

igmpInformation OBJECT IDENTIFIER ::= { multicastFiltering 9 }

igmpInformationRouterIp OBJECT-TYPE
        SYNTAX IpAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                    "The router IP for IGMP"
        ::= { igmpInformation 1 }  
        
igmpInformationRouterPort OBJECT-TYPE
        SYNTAX Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION
                     "The router port for IGMP"
        ::= { igmpInformation 2 } 

snmpCommunityStringTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SnmpCommunityStringEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and information about
                     community string of SNMP agent."
        ::= { snmp 5 }         
        
snmpCommunityStringEntry OBJECT-TYPE
        SYNTAX      SnmpCommunityStringEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about community string of SNMP agent."
        INDEX       { snmpCommunityStringIndex }
        ::= { snmpCommunityStringTable 1 }

SnmpCommunityStringEntry ::= SEQUENCE
{
    snmpCommunityStringIndex      Integer32,
    snmpCommunityStringName       DisplayString,
    snmpCommunityStringAttribute  INTEGER,
    snmpCommunityStringStatus     INTEGER
}

snmpCommunityStringIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of community string."
        ::= { snmpCommunityStringEntry 1 }

snmpCommunityStringName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..15))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The community string of SNMP agent."
        ::= { snmpCommunityStringEntry 2 }

snmpCommunityStringAttribute OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        ro(1),
                        rw(2),
                        r-sysinfo-only(3)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The attribute of community string."
        ::= { snmpCommunityStringEntry 3 }

snmpCommunityStringStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        creatrequest(2),
                        undercreation(3),
                        invalid(4)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of this entry. If this object is not equal to valid(1), all associated
 	                 SNMP entries shall be deleted by the agent."
        ::= { snmpCommunityStringEntry 4 }

snmpTrapServerTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SnmpTrapServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and information about
                     trap server of SNMP agent."
        ::= { snmp 6 }

snmpTrapServerEntry OBJECT-TYPE
        SYNTAX      SnmpTrapServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about trap server of SNMP agent."
        INDEX       { snmpTrapServerIndex }
        ::= { snmpTrapServerTable 1 }

SnmpTrapServerEntry ::= SEQUENCE
{
    snmpTrapServerIndex      Integer32,
    snmpTrapServerIPAddr     IpAddress,
    snmpTrapServerTrapComm   DisplayString,
    snmpTrapServerStatus     INTEGER,
    snmpTrapServerPort       Integer32,   
    snmpTrapServerIP         DisplayString
}

snmpTrapServerIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of trap server."
        ::= { snmpTrapServerEntry 1 }

-- snmpTrapServerIPAddr OBJECT-TYPE
--         SYNTAX      IpAddress
--         MAX-ACCESS  read-write
--         STATUS      current
--         DESCRIPTION "Trap Server IP Address."
--         ::= { snmpTrapServerEntry 2 }

snmpTrapServerTrapComm OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..15))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The community string of trap server."
        ::= { snmpTrapServerEntry 3 }

snmpTrapServerStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        creatrequest(2),
                        undercreation(3),
                        invalid(4)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of this entry. If this object is not equal to valid(1), all associated
 	                 SNMP entries shall be deleted by the agent."
        ::= { snmpTrapServerEntry 5 }

snmpTrapServerPort OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The port number of the trap server."
        ::= { snmpTrapServerEntry 6 }

snmpTrapServerIP OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..128))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Trap Server IP Address(IPv4 or IPv6)."
        ::= { snmpTrapServerEntry 7 }		
		
snmpUserTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SnmpUserEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and information about
                     the SNMP user configuration."
        ::= { snmp 7 }

snmpUserEntry OBJECT-TYPE
        SYNTAX      SnmpUserEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about the SNMP user configuration."
        INDEX       { snmpUserIndex }
        ::= { snmpUserTable 1 }

SnmpUserEntry ::= SEQUENCE
{
    snmpUserIndex      Integer32,
    snmpUserName       DisplayString,
    snmpUserAuthType   INTEGER,
    snmpUserAuthPwd    DisplayString,
    snmpUserEncrypType INTEGER,
    snmpUserEncrypKey  DisplayString,
    snmpUserStatus     INTEGER
}

snmpUserIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The index of the SNMP user entry"
        ::= { snmpUserEntry 1 }

snmpUserName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The SNMP user name"
        ::= { snmpUserEntry 2 }

snmpUserAuthType OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        none(0),
                        md5(1),
                        sha(2),
                        sha-256(3)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The type of SNMP user authentication password."
        ::= { snmpUserEntry 3 }

snmpUserAuthPwd OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0|8..32))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The SNMP user authentication password."
        ::= { snmpUserEntry 4 }

snmpUserEncrypType OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        none(0),
                        des(1),
                        aes(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The type of SNMP user privacy password."
        ::= { snmpUserEntry 5 }

snmpUserEncrypKey OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0|8..32))
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The SNMP user privacy password."
        ::= { snmpUserEntry 6 }

snmpUserStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        invalid(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The SNMP user status"
        ::= { snmpUserEntry 7 }

snmpStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The SNMP status"
        ::= { snmp 8 }

snmpTrapMode OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        trap(0),
                        inform(1),
                        trapv3(2),
                        informv3(3)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The SNMP trap mode"
        ::= { snmp 10 }

snmpTrapv3ServerTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SnmpTrapv3ServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and information about
                     trapv3 server of SNMP agent."
        ::= { snmp 9 }

snmpTrapv3ServerEntry OBJECT-TYPE
        SYNTAX      SnmpTrapv3ServerEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about trapv3 server of SNMP agent."
        INDEX       { snmpTrapv3ServerIndex }
        ::= { snmpTrapv3ServerTable 1 }


SnmpTrapv3ServerEntry ::= SEQUENCE
{
    snmpTrapv3ServerIndex           Integer32,
    snmpTrapv3ServerUsmName         DisplayString,
    snmpTrapv3ServerUsmAuthMode     INTEGER,
    snmpTrapv3ServerUsmAuthKey      DisplayString,
    snmpTrapv3ServerUsmPrivMode     INTEGER,
    snmpTrapv3ServerUsmPrivKey      DisplayString,
    snmpTrapv3ServerStatus          INTEGER,
    snmpTrapv3ServerPort            Integer32,
    snmpTrapv3ServerIP              DisplayString
}

snmpTrapv3ServerIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of trapv3 server."
        ::= { snmpTrapv3ServerEntry 1 }

snmpTrapv3ServerUsmName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..15))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The security name string of trapv3 server."
        ::= { snmpTrapv3ServerEntry 2 }

snmpTrapv3ServerUsmAuthMode OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        none(0),
                        md5(1),
                        sha(2),
                        sha-256(3)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The authentication type of trapv3 server."
        ::= { snmpTrapv3ServerEntry 3 }

snmpTrapv3ServerUsmAuthKey OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..15))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The authentication key string of trapv3 server."
        ::= { snmpTrapv3ServerEntry 4 }

snmpTrapv3ServerUsmPrivMode OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        none(0),
                        des(1),
                        aes(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The encryption type of trapv3 server."
        ::= { snmpTrapv3ServerEntry 5 }

snmpTrapv3ServerUsmPrivKey OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..15))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The encryption key string of trapv3 server."
        ::= { snmpTrapv3ServerEntry 6 }

snmpTrapv3ServerStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        creatrequest(2),
                        undercreation(3),
                        invalid(4)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of this entry. If this object is not equal to valid(1), all associated
 	                 SNMP entries shall be deleted by the agent."
        ::= { snmpTrapv3ServerEntry 7 }

snmpTrapv3ServerPort OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The port number of the trapv3 server."
        ::= { snmpTrapv3ServerEntry 8 }

snmpTrapv3ServerIP OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..128))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Trapv3 Server IP Address(IPv4 or IPv6)."
        ::= { snmpTrapv3ServerEntry 9 }

snmpTrapEventTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SnmpTrapEventEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and information about
                     the SNMP Trap Event configuration."
        ::= { snmp 11 }

snmpTrapEventEntry OBJECT-TYPE
        SYNTAX      SnmpTrapEventEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about the SNMP Trap Event configuration."
        INDEX       { snmpTrapEventIndex }
        ::= { snmpTrapEventTable 1 }

SnmpTrapEventEntry ::= SEQUENCE
{
    snmpTrapEventIndex      Integer32,
    snmpTrapEventName       DisplayString,
    snmpTrapEventStatus     INTEGER
}

snmpTrapEventIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The index of the SNMP Trap Event entry"
        ::= { snmpTrapEventEntry 1 }

snmpTrapEventName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The SNMP Trap Event name"
        ::= { snmpTrapEventEntry 2 }


snmpTrapEventStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        invalid(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The SNMP Trap Event status"
        ::= { snmpTrapEventEntry 3 }


--
-- security
--
macFiltering       OBJECT IDENTIFIER ::= { security 3 }
ieee8021x          OBJECT IDENTIFIER ::= { security 4 }
staticSecurity     OBJECT IDENTIFIER ::= { security 5 }


macFilteringTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF MACFilteringEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     MAC filter configuration.
                     MAC address filtering allows the switch to drop
                     unwanted traffic. Traffic is filtered based on
                     the destination addresses. For example, if your
                     network is congested because of high utilization
                     from one MAC address, you can filter all traffic
                     transmitted to that MAC address, restoring network
                      flow while you troubleshoot the problem."
        ::= { macFiltering 1}

macFilteringEntry OBJECT-TYPE
        SYNTAX      MACFilteringEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about MAC filter configuration."
        INDEX       { macFilteringIndex }
        ::= { macFilteringTable 1 }

MACFilteringEntry ::= SEQUENCE
{
    macFilteringIndex    Integer32,
    macFilteringAddr     MacAddress,
    macFilteringStatus   INTEGER
}

macFilteringIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of MAC."
        ::= { macFilteringEntry 1 }

macFilteringAddr OBJECT-TYPE
        SYNTAX      MacAddress
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "MAC address of the entry."
        ::= { macFilteringEntry 2 }

macFilteringStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        creatrequest(2),
                        undercreation(3),
                        invalid(4)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of this entry. If this object is not equal to valid(1), all associated
 	    MAC entries shall be deleted by the agent."
        ::= { macFilteringEntry 3 }

--
-- ieee8021x
--

radiusServerSetting     OBJECT IDENTIFIER ::= { ieee8021x 1 }
portAuthConfiguration   OBJECT IDENTIFIER ::= { ieee8021x 2 }

radius8021xProtocolStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "IEEE802.1x protocol function status.
                     (1) IEEE802.1x protocol function is enabled.
                     (2) IEEE802.1x protocol function is disabled.
                     802.1x makes use of the physical access characteristics
                     of IEEE802 LAN infrastructures in order to provide a
                     means of authenticating and authorizing devices attached
                     to a LAN port that has point-to-point connection
                     characteristics, and of preventing access to that port
                     in cases in which the authentication and authorization
                     process fails."
        ::= { radiusServerSetting 1 }

radiusServerIP OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..136))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Radius Server IP Address: the IP address of the authentication server."
        ::= { radiusServerSetting 2 }

radiusServerPort OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Server Port: The UDP port number used by the authentication server to authenticate."
        ::= { radiusServerSetting 3 }

radiusAccountingPort OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Accounting Port: The UDP port number used by the authentication server to retrieve
                     accounting information. "
        ::= { radiusServerSetting 4 }

radiusSharedKey OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..29))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Shared Key: A key shared between this switch and authentication server."
        ::= { radiusServerSetting 5 }

radiusNASIdentifier OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..29))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "NAS Identifier: A string used to identify this switch."
        ::= { radiusServerSetting 6 }

radiusMiscQuietPeriod OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Quiet Period: used to define periods of time during
                     which it will not attempt to acquire a supplicant
                     (Default time is 60 seconds)."
        ::= { radiusServerSetting 7 }

radiusMiscTxPeriod OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "TX Period: used to determine when an EAPOL PDU is to
                     be transmitted(Default value is 30 seconds)."
        ::= { radiusServerSetting 8 }

radiusMiscSupplicantTimeout OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Supplicant Timeout: used to determine timeout conditions
                     in the exchanges between the supplicant and
                     authentication server(Default value is 30 seconds)."
        ::= { radiusServerSetting 9 }

radiusMiscServerTimeout OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Server Timeout: used to determine timeout conditions
                     in the exchanges between the authenticator and
                     authentication server(Default value is 30 seconds)."
        ::= { radiusServerSetting 10}

radiusMiscReAuthMax OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "ReAuthMax: used to determine the number of
                     reauthentication attempts that are permitted
                     before the specific port becomes unauthorized
                     (Default value is 2 times)."
        ::= { radiusServerSetting 11}

radiusMiscReauthPeriod OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Reauth Period: used to determine a nonzero number
                     of seconds between periodic reauthentication of
                     the supplications(Default value is 3600 seconds)."
        ::= { radiusServerSetting 12}

radiusPerPortCfgTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF RadiusPerPortCfgEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information and configuration about
                     Radius per port configuration."
        ::= { portAuthConfiguration 1}

radiusPerPortCfgEntry OBJECT-TYPE
        SYNTAX      RadiusPerPortCfgEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about Radius per port configuration."
        INDEX       { radiusPerPortCfgIndex }
        ::= { radiusPerPortCfgTable 1 }

RadiusPerPortCfgEntry ::= SEQUENCE
{
    radiusPerPortCfgIndex    Integer32,
    radiusPerPortCfgPortName DisplayString,
    radiusPerPortCfgState    INTEGER,
    radiusPerPortState       INTEGER
}

radiusPerPortCfgIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of port."
        ::= { radiusPerPortCfgEntry 1 }

radiusPerPortCfgPortName OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..16))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The name of port."
        ::= { radiusPerPortCfgEntry 2 }

radiusPerPortCfgState OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        reject(1),
                        accept(2),
                        authorize(3),
                        disabled(4)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "You can select the specific port and configure the
                     authorization state. Each port can select four kinds
                     of authorization state :
                     Reject: force the specific port to be unauthorized.
                     Accept: force the specific port to be authorized.
                     Authorize: the state of the specific port was determined by
                     the outcome of the authentication.
                     Disable: the specific port didn't support 802.1x function."
        ::= { radiusPerPortCfgEntry 3 }

radiusPerPortState OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        initialize(1),
                        disconnected(2),
                        connecting(3),
                        authenticating(4),
                        authenticated(5),
                        aborting(6),
                        held(7),
                        force-authentication(8),
                        force-unauthentication(9)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The running status of the  port of the 802.1x function."
        ::= { radiusPerPortCfgEntry 4 }

--
-- static security
--

staticSecurityMacTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF StaticSecurityMacEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     static port security configuration.
                     This function allow you to restrict traffic flow in a port.
                     you can allow only several MACs to pass one port."
        ::= { staticSecurity 1}

staticSecurityMacEntry OBJECT-TYPE
        SYNTAX      StaticSecurityMacEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing MACs and the
                     associated port."
        INDEX       { staticSecurityMacIndex }
        ::= { staticSecurityMacTable 1 }

StaticSecurityMacEntry ::= SEQUENCE
{
    staticSecurityMacIndex    Integer32,
    staticSecurityMacVid      Integer32,
    staticSecurityMacAddr     MacAddress,    
    staticSecurityMacPort     Integer32,
    staticSecurityMacStatus   Integer32
}

staticSecurityMacIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of MAC."
        ::= { staticSecurityMacEntry 1 }

staticSecurityMacVid OBJECT-TYPE
        SYNTAX      Integer32(1..4094)
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The filtering VLAN ID"
        ::= { staticSecurityMacEntry 2 }

staticSecurityMacAddr OBJECT-TYPE
        SYNTAX      MacAddress
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "MAC address of the entry."
        ::= { staticSecurityMacEntry 3 }

   
staticSecurityMacPort OBJECT-TYPE
        SYNTAX      Integer32                    
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The associated port that allow the mac to pass."
        ::= { staticSecurityMacEntry 4 }

staticSecurityMacStatus OBJECT-TYPE
        SYNTAX      Integer32
        	    {
                        valid(1),
                        invalid(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The status of this entry"
        ::= { staticSecurityMacEntry 5 }

staticSecurityStateTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF StaticSecurityStateEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     static port security configuration.
                     This table contain the state(enable/disable) of each port."
        ::= { staticSecurity 2}

staticSecurityStateEntry OBJECT-TYPE
        SYNTAX      StaticSecurityStateEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing the state(enable/disable) 
                     of the associated port"
        INDEX       { staticSecurityPortNum }
        ::= { staticSecurityStateTable 1 }

StaticSecurityStateEntry ::= SEQUENCE
{
    staticSecurityPortNum    PortList,    
    staticSecurityPortState      INTEGER

}

staticSecurityPortNum OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Port number."
        ::= { staticSecurityStateEntry 1 }

staticSecurityPortState OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The static port security state"
        ::= { staticSecurityStateEntry 2 }
        

--
-- warning
--

eventAndEmailWarning     OBJECT IDENTIFIER ::= { warning 1 }

eventSelection           OBJECT IDENTIFIER ::= { eventAndEmailWarning 1 }
sysLogConfiguration      OBJECT IDENTIFIER ::= { eventAndEmailWarning 2 }
smtpConfiguration        OBJECT IDENTIFIER ::= { eventAndEmailWarning 3 }
                          
portEventsTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF PortEventsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     port events."
        ::= { eventSelection 2 }

portEventsEntry OBJECT-TYPE
        SYNTAX      PortEventsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing type of port events."
        INDEX       { eventPortNumber }
        ::= { portEventsTable 1 }

PortEventsEntry ::= SEQUENCE
{
    eventPortNumber          Integer32,
    eventPortEventEmail       INTEGER,
    eventPortEventRelay      INTEGER    
}

eventPortNumber OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Port number for port event table."
        ::= {portEventsEntry 1 }

eventPortEventEmail OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        linkup(1),
                        linkdown(2),
                        linkupandlinkdown(3),
                        disabled(4)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to indicate the which event should be
                     an Email alert. The value can't be modified, if
                     eventEmailAlertStatus is disabled."
        ::= { portEventsEntry 3 }

eventPortEventRelay OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        linkup(1),
                        linkdown(2),
                        linkupandlinkdown(3),
                        disabled(4)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to indicate the which event should be
                     an Email alert. The value can't be modified, if
                     eventEmailAlertStatus is disabled."
        ::= { portEventsEntry 4 }

powerEventsTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF PowerEventsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     power events."
        ::= { eventSelection 3 }

powerEventsEntry OBJECT-TYPE
        SYNTAX      PowerEventsEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing power events configuration."
        INDEX       { eventPowerNumber }
        ::= { powerEventsTable 1 }

PowerEventsEntry ::= SEQUENCE
{
    eventPowerNumber          Integer32,
    eventPowerEventSMTP       INTEGER,
    eventPowerEventRelay      INTEGER    
}

eventPowerNumber OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The power number for the power event table."
        ::= {powerEventsEntry 1 }

eventPowerEventSMTP OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        power-on(1),
                        power-off(2),
                        disabled(3)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to indicate the which event should be
                     an Email alert."
        ::= { powerEventsEntry 3 }

eventPowerEventRelay OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        power-on(1),
                        power-off(2),
                        disabled(3)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to indicate the which event should be
                     an relay alert."
        ::= { powerEventsEntry 4 }
        
syslogEvents  OBJECT IDENTIFIER ::= { eventSelection 4 }

syslogEventsSMTP OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        log-emerg(0),
                        log-alert(1),
                        log-crit(2),
                        log-err(3),
                        log-warning(4),
                        log-notice(5),
                        log-info(6),
                        log-debug(7),
                        disabled(8)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to indicate the which event should be
                     an Email alert."
        ::= { syslogEvents 1 }

syslogEventsRelay OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        log-emerg(0),
                        log-alert(1),
                        log-crit(2),
                        log-err(3),
                        log-warning(4),
                        log-notice(5),
                        log-info(6),
                        log-debug(7),
                        disabled(8)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to indicate the which event should be
                     an relay alert."
        ::= { syslogEvents 2 }

syslogStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to specify the log should be sent to syslog server,
                     or the log should be kept in switch. When disabled, the
                     switch won't send out or record any log."
        ::= { sysLogConfiguration 1 }

--eventServerAddr OBJECT-TYPE
--        SYNTAX      IpAddress
--        MAX-ACCESS  read-write
--        STATUS      current
--        DESCRIPTION "The IP address of syslog server. It can't be modified
--                     when syslogStatus is disabled."
--        ::= { sysLogConfiguration 2 }
        
eventServerPort OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The syslog server port."
        ::= { sysLogConfiguration 3 }

eventServerLevel OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        log-emerg(0),
                        log-alert(1),
                        log-crit(2),
                        log-err(3),
                        log-warning(4),
                        log-notice(5),
                        log-info(6),
                        log-debug(7)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The syslog log level"
        ::= { sysLogConfiguration 4 }

eventLogToFlash OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Log event to the flash."
        ::= { sysLogConfiguration 5 }

eventServerIP OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(1..136))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The IP address (IPv4 and IPv6) of syslog server. It can't be modified
                     when syslogStatus is disabled."
        ::= { sysLogConfiguration 6 }

eventEmailAlertAddr OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..31))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The address of SMTP server."
        ::= { smtpConfiguration 2 }

eventEmailAlertAuthentication OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Parameter to enable the authentication feature SMTP server.
                     It can't be modified when eventEmailAlertStatus is disabled."
        ::= { smtpConfiguration 3 }

eventEmailAlertAccount OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..31))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The Email account for SMTP server. It can't be modified when
                     eventEmailAlertAuthentication is disabled."
        ::= { smtpConfiguration 4 }

eventEmailAlertPassword OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..31))
        MAX-ACCESS  write-only
        STATUS      current
        DESCRIPTION "The password of Email account. It can't be modified when
                     eventEmailAlertAuthentication is disabled."
        ::= { smtpConfiguration 5 }

emailAlertRcptTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF EmailAlertRcptEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information
                     of recipient Email address list."
        ::= { smtpConfiguration 6 }

emailAlertRcptEntry OBJECT-TYPE
        SYNTAX      EmailAlertRcptEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing recipient Email addresses."
        INDEX       { eventEmailAlertRcptIndex }
        ::= { emailAlertRcptTable 1 }

EmailAlertRcptEntry ::= SEQUENCE
{
    eventEmailAlertRcptIndex     Integer32,
    eventEmailAlertRcptEmailAddr DisplayString
}

eventEmailAlertRcptIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Index of recipient Email address."
        ::= { emailAlertRcptEntry 1 }

eventEmailAlertRcptEmailAddr OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..31))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The recipient Email address."
        ::= { emailAlertRcptEntry 2 }

eventEmailSenderAddr OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..31))
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The sender address of SMTP server."
        ::= { smtpConfiguration 7 }

eventEmailAlertSubject OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The Email subject for SMTP server."
        ::= { smtpConfiguration 8 }
        
eventEmailSendTestAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Send test Email."
        ::= { smtpConfiguration 9 }        
    
macAddressTable          OBJECT IDENTIFIER ::= { monitorandDiag 1 }
portStatistic            OBJECT IDENTIFIER ::= { monitorandDiag 2 }
portmirroring            OBJECT IDENTIFIER ::= { monitorandDiag 3 }
eventLog                 OBJECT IDENTIFIER ::= { monitorandDiag 4 }
eventWarning             OBJECT IDENTIFIER ::= { monitorandDiag 5 }

--
--  macAddressTable
--

macAddrTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF MACAddrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     MAC filter configuration."
        ::= { macAddressTable 1}

macAddrEntry OBJECT-TYPE
        SYNTAX      MACAddrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about all MAC configuration."
        INDEX       { macAddressIndex }
        ::= { macAddrTable 1 }

MACAddrEntry ::= SEQUENCE
{
    macAddressIndex    Integer32,
    macAddressPortName DisplayString,
    macAddressAddr     MacAddress,
    macAddressType     INTEGER
}

macAddressIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Index of MAC."
        ::= { macAddrEntry 1 }

macAddressPortName OBJECT-TYPE
        SYNTAX      DisplayString(SIZE(0..7))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port name, if you want to insert a new MAC entry,
                    the port name must be correct (Please refer to the
                    switchCurrentPortNameListTable) when creating a new entry."
        ::= { macAddrEntry 2 }

macAddressAddr OBJECT-TYPE
        SYNTAX      MacAddress
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "MAC address of the entry."
        ::= { macAddrEntry 3 }

macAddressType OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        dynamic(1),
                        static(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The type of this entry."
        ::= { macAddrEntry 4 }

--
--  portStatistic
--

switchPortStatTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF SwitchPortStatEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and statistics information about
                     each switch ports(including expansion slot)."
        ::= { portStatistic 1 }

switchPortStatEntry OBJECT-TYPE
        SYNTAX      SwitchPortStatEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing information
                     about statistics in one switch port of the switch."
        INDEX       { swPortStatIndex }
        ::= { switchPortStatTable 1 }

SwitchPortStatEntry ::= SEQUENCE
{
    swPortStatIndex            Integer32,
    swPortStatLink             INTEGER,
    swPortStatState            INTEGER,
    swPortStatTXGoodPkt        Integer32,
    swPortStatTXBadPkt         Integer32,
    swPortStatRXGoodPkt        Integer32,
    swPortStatRXBadPkt         Integer32,
    swPortStatTxRate           Integer32,
    swPortStatRxRate           Integer32,
    swPortStatClearAction      INTEGER
}

swPortStatIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Index of port statistic table."
        ::= { switchPortStatEntry 1 }

swPortStatLink OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        up(1),
                        down(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Indicates the link state."
        ::= { switchPortStatEntry 3 }

swPortStatState OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Indicates the port state"
        ::= { switchPortStatEntry 4 }

swPortStatTXGoodPkt OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The count of good packets of TX."
        ::= { switchPortStatEntry 5 }

swPortStatTXBadPkt OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The count of bad packets of TX."
        ::= { switchPortStatEntry 6 }

swPortStatRXGoodPkt OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The count of good packets of RX."
        ::= { switchPortStatEntry 7 }

swPortStatRXBadPkt OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The count of bad packets of RX."
        ::= { switchPortStatEntry 8 }

swPortStatTxRate OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The TX rate of the port"
        ::= { switchPortStatEntry 11 }

swPortStatRxRate OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The RX rate of the port"
        ::= { switchPortStatEntry 12 }

swPortStatClearAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger to clear
                     the port statistics counter.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { switchPortStatEntry 13 }

--
--  portmirroring
--

swPortMirrorDirect OBJECT-TYPE
		SYNTAX		INTEGER
					{
						both(1),
						rx(2),
						tx(3),
						disabled(4)
					}
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The mirror direct"
        ::= { portmirroring 4 }

swPortMirroredPorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The mirrored port list"
        ::= { portmirroring 5 }

swPortMirrorPort OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The mirror port list"
        ::= { portmirroring 6 }

--
-- eventLog
--

eventLogTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF EventLogEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     logs."
        ::= { eventLog 1 }

eventLogEntry OBJECT-TYPE
        SYNTAX      EventLogEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing list of logs."
        INDEX       { eventLogIndex }
        ::= { eventLogTable 1 }

EventLogEntry ::= SEQUENCE
{
    eventLogIndex          Integer32,
    eventLogDescription    DisplayString
}

eventLogIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Index of log."
        ::= {eventLogEntry 1 }

eventLogDescription OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..100))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The description of log."
        ::= { eventLogEntry 2 }

eventLogClearAllAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger to clear all
                     events.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { eventLog 2 }


--
-- eventWarningLog
--                            
eventWarningLog          OBJECT IDENTIFIER ::= { eventWarning 1 }

eventWarningLogTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF EventWarningLogEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     event warning logs."
        ::= { eventWarningLog 1 }

eventWarningLogEntry OBJECT-TYPE
        SYNTAX      EventWarningLogEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing event warning logs."
        INDEX       { eventWarningLogIndex }
        ::= { eventWarningLogTable 1 }

EventWarningLogEntry ::= SEQUENCE
{
    eventWarningLogIndex          Integer32,
    eventWarningLogDescription    DisplayString
}

eventWarningLogIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "Index of log."
        ::= {eventWarningLogEntry 1 }

eventWarningLogDescription OBJECT-TYPE
        SYNTAX      DisplayString (SIZE(0..100))
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The description of log."
        ::= { eventWarningLogEntry 2 }

eventWarningLogClearAllAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger to clear all
                     events.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { eventWarningLog 2 }

eventWarningLogClearRelayAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger to clear relay
                     events.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { eventWarningLog 3 }

--
-- lldpMgt
--

lldpStatus OBJECT-TYPE
		SYNTAX		INTEGER
					{
						enable(1),
						disable(2)
					}
		MAX-ACCESS	read-write
		STATUS		current
		DESCRIPTION	"LLDP Configuration.
					 (1) Enable.
					 (2) Disable."
		::= { lldpMgt 1 }

lldpInterval OBJECT-TYPE
		SYNTAX		Integer32(5..65535)
        MAX-ACCESS	read-write
        STATUS		current
        DESCRIPTION	"LLDP Interval."
        ::= { lldpMgt 2 }

lldpTxTtl OBJECT-TYPE
		SYNTAX		Integer32(5..65535)
        MAX-ACCESS	read-write
        STATUS		current
        DESCRIPTION	"Transmitter Time To Live."
        ::= { lldpMgt 3 }

lldpNeighborInfoTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF LldpNeighborInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive and LLDP neighbor information."
        ::= { lldpMgt 4 }

lldpNeighborInfoEntry OBJECT-TYPE
        SYNTAX      LldpNeighborInfoEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing LLDP neighbor
                     information in one switch port of the switch."
        INDEX       { lldpNeighborInfoLocalPortId }
        ::= { lldpNeighborInfoTable 1 }

LldpNeighborInfoEntry ::= SEQUENCE
{
    lldpNeighborInfoLocalPortId     Integer32,
    lldpNeighborInfoChassisId       DisplayString,
    lldpNeighborInfoPortId          DisplayString,
    lldpNeighborInfoPortDesc        DisplayString,
    lldpNeighborInfoSystemName      DisplayString,
    lldpNeighborInfoSystemDesc      DisplayString,
    lldpNeighborInfoManagementAddr  DisplayString
}

lldpNeighborInfoLocalPortId OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port index of the neighbor information table."
        ::= { lldpNeighborInfoEntry 1 }

lldpNeighborInfoChassisId OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The chassis ID of the neighbor information."
        ::= { lldpNeighborInfoEntry 2 }

lldpNeighborInfoPortId OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port ID of the neighbor information."
        ::= { lldpNeighborInfoEntry 3 }

lldpNeighborInfoPortDesc OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port description of the neighbor information."
        ::= { lldpNeighborInfoEntry 4 }

lldpNeighborInfoSystemName OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The system name of the neighbor information."
        ::= { lldpNeighborInfoEntry 5 }

lldpNeighborInfoSystemDesc OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The system description of the neighbor information."
        ::= { lldpNeighborInfoEntry 6 }

lldpNeighborInfoManagementAddr OBJECT-TYPE
        SYNTAX      DisplayString
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The management address of the neighbor information."
        ::= { lldpNeighborInfoEntry 7 }

--
-- save
--

saveCfgMgtAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger saving
                     current configuration.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { save 1 }

fdbClearDynamicMacAddr OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Clear dynamic MAC address"
        ::= { fdb 1 }

staticMacAddrTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF StaticMacAddrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     static MAC address."
        ::= { fdb 2 }

staticMacAddrEntry OBJECT-TYPE
        SYNTAX      StaticMacAddrEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing list of the static MAC address."
        INDEX       { staticMacAddrIndex }
        ::= { staticMacAddrTable 1 }

StaticMacAddrEntry ::= SEQUENCE
{
    staticMacAddrIndex        Integer32,
    staticMacAddr            MacAddress,
    staticMacAddrVid         Integer32,
    staticMacAddrSourcePorts PortList,
    staticMacAddrStatus      INTEGER
}

staticMacAddrIndex OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The index of the static MAC address"
        ::= {staticMacAddrEntry 1 }

staticMacAddr OBJECT-TYPE
        SYNTAX      MacAddress
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The static MAC address"
        ::= {staticMacAddrEntry 2 }

staticMacAddrVid OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The static MAC address VLAN ID"
        ::= {staticMacAddrEntry 3 }

staticMacAddrSourcePorts OBJECT-TYPE
        SYNTAX      PortList
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The static MAC address source port."
        ::= {staticMacAddrEntry 4 }

staticMacAddrStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        valid(1),
                        invalid(2)
                    }
        MAX-ACCESS  read-create
        STATUS      current
        DESCRIPTION "The status of static MAC address entry"
        ::= {staticMacAddrEntry 5 }

garpJoinTimer OBJECT-TYPE
        SYNTAX      Integer32(10..65535)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The GARP join timer."
        ::= { garp 1 }

garpLeaveTimer OBJECT-TYPE
        SYNTAX      Integer32(10..65535)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The GARP leave timer."
        ::= { garp 2 }

garpLeaveAllTimer OBJECT-TYPE
        SYNTAX      Integer32(10..65535)
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The GARP leave all timer."
        ::= { garp 3 }

gmrp  OBJECT IDENTIFIER ::= { garp 4 }

gmrpStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The GMRP status."
        ::= { gmrp 1 }

gmrpPortTable OBJECT-TYPE
        SYNTAX      SEQUENCE OF GmrpPortEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "Table of descriptive information about
                     GMRP(per port)."
        ::= { gmrp 2 }

gmrpPortEntry OBJECT-TYPE
        SYNTAX      GmrpPortEntry
        MAX-ACCESS  not-accessible
        STATUS      current
        DESCRIPTION "An entry in the table, containing GMRP configuration of the port."
        INDEX       { gmrpPortID }
        ::= { gmrpPortTable 1 }

GmrpPortEntry ::= SEQUENCE
{
    gmrpPortID            Integer32,
    gmrpPortStatus        INTEGER
}

gmrpPortID OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The port number."
        ::= {gmrpPortEntry 1 }

gmrpPortStatus OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        enabled(1),
                        disabled(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "The GMRP port status."
        ::= {gmrpPortEntry 2 }

gmrpStatistics OBJECT IDENTIFIER ::= { gmrp 3 }

gmrpStatisticsClearAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger to clear
                     the GMRP counter.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { gmrpStatistics 1 }

gmrpStatisticsRxJoinEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX join empty packet."
        ::= {gmrpStatistics 2 }
        
gmrpStatisticsTxJoinEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX join empty packet."
        ::= {gmrpStatistics 3 }        
        
gmrpStatisticsRxJoinIn OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX join in packet."
        ::= {gmrpStatistics 4 }
        
gmrpStatisticsTxJoinIn OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX join in packet."
        ::= {gmrpStatistics 5 }
        
gmrpStatisticsRxEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX empty packet."
        ::= {gmrpStatistics 6 }
        
gmrpStatisticsTxEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX empty packet."
        ::= {gmrpStatistics 7 }
        
gmrpStatisticsRxLeaveIn OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX leave in packet."
        ::= {gmrpStatistics 8 }
        
gmrpStatisticsTxLeaveIn OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX leave in packet."
        ::= {gmrpStatistics 9 }
        
gmrpStatisticsRxLeaveEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX leave empty packet."
        ::= {gmrpStatistics 10 }
        
gmrpStatisticsTxLeaveEmpty OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX leave empty packet."
        ::= {gmrpStatistics 11 }
        
gmrpStatisticsRxLeaveAll OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the RX leave all packet."
        ::= {gmrpStatistics 12 }
        
gmrpStatisticsTxLeaveAll OBJECT-TYPE
        SYNTAX      Integer32
        MAX-ACCESS  read-only
        STATUS      current
        DESCRIPTION "The counter of the TX leave all packet."
        ::= {gmrpStatistics 13 }                
                                                               
ping  OBJECT IDENTIFIER ::= { diagnosis 1 }

pingDestinationAdrress OBJECT-TYPE
       SYNTAX  DisplayString
       ACCESS  read-write
       STATUS  current
       DESCRIPTION   "The IP address or name of the destination host for pingAction."
       ::= { ping 1 }

pingAction OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger to ping pingDestinationAdrress.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { ping 2 } 

pingResultAddress OBJECT-TYPE
       SYNTAX  DisplayString
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "The IP address or name of the destination host of the ping result."
       ::= { ping 3 }      
       
pingResultSentPackets OBJECT-TYPE 
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "The sent packets of the ping result."
       ::= { ping 4 }  
       
pingResultReceivedPackets OBJECT-TYPE 
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "The received packets of the ping result."
       ::= { ping 5 }                               

pingResultLostPackets OBJECT-TYPE 
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "The lost packets of the ping result."
       ::= { ping 6 } 

--
-- Locate
--
locate  OBJECT IDENTIFIER ::= { diagnosis 2 }

locateBeep OBJECT-TYPE
        SYNTAX      INTEGER
                    {
                        active(1),
                        notActive(2)
                    }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "This object allows you to locate the device physically through a sound.
                     Setting this object to active(1) trigger to intermittent beeps.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2)
                     when this object is read."
        ::= { locate 1 } 
--
-- temperatureLog
--

temperatureLogSystemHighestT OBJECT-TYPE
       SYNTAX  Integer32
       ACCESS  read-only
       STATUS  current
       DESCRIPTION   "The system's highest temperature. (unit:0.01 degree Celsius)"
       ::= { temperatureLog 1 }

temperatureLogSystemLowestT OBJECT-TYPE
        SYNTAX  Integer32
        ACCESS  read-only
        STATUS  current
        DESCRIPTION "The system's lowest temperature. (unit:0.01 degree Celsius)"
        ::= { temperatureLog 2 }

temperatureLogSystemAverageT OBJECT-TYPE
       SYNTAX   Integer32
       ACCESS   read-only
       STATUS   current
       DESCRIPTION "The system's average temperature. (unit:0.01 degree Celsius)"
       ::= { temperatureLog 3 }

temperatureLogSystemRecordTime OBJECT-TYPE
        SYNTAX  Integer32
        ACCESS  read-only
        STATUS  current
        DESCRIPTION "The system's temperature log recorded time. (unit:Minute)"
        ::= { temperatureLog 4 }

temperatureLogUserHighestT OBJECT-TYPE
       SYNTAX   Integer32
       ACCESS   read-only
       STATUS   current
       DESCRIPTION   "The user's highest temperature. (unit:0.01 degree Celsius)"
       ::= { temperatureLog 5 }

temperatureLogUserLowestT OBJECT-TYPE
        SYNTAX  Integer32
        ACCESS  read-only
        STATUS  current
        DESCRIPTION "The user's lowest temperature. (unit:0.01 degree Celsius)"
        ::= { temperatureLog 6 }

temperatureLogUserAverageT OBJECT-TYPE
       SYNTAX   Integer32
       ACCESS   read-only
       STATUS   current
       DESCRIPTION "The user's average temperature. (unit:0.01 degree Celsius)"
       ::= { temperatureLog 7 }

temperatureLogUserRecordTime OBJECT-TYPE
        SYNTAX  Integer32
        ACCESS  read-only
        STATUS  current
        DESCRIPTION "The user's temperature log recorded time. (unit:Minute)"
        ::= { temperatureLog 8 }

temperatureLogResetUserLog OBJECT-TYPE
        SYNTAX  INTEGER
                {
                    active(1),
                    notActive(2)
                }
        MAX-ACCESS  read-write
        STATUS      current
        DESCRIPTION "Setting this object to active(1) trigger to reset user's temperature log.
                     Setting this object to notActive(2) has no effect.
                     The system always returns the value notActive(2) when this object is read."
        ::= { temperatureLog 9 }
        
END

