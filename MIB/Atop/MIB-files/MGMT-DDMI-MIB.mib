-- *****************************************************************
-- DDMI-MIB:  
-- ****************************************************************

MGMT-DDMI-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTSfpTransceiver FROM MGMT-TC
    ;

mgmtDdmiMib MODULE-IDENTITY
    LAST-UPDATED "202102240000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of DDMI"
    REVISION    "202102240000Z"
    DESCRIPTION
        "Renamed a few parameters and added alarm state"
    REVISION    "201711270000Z"
    DESCRIPTION
        "Add IfIndex to DdmiStatusInterfaceTableInfoGroup. Editorial changes"
    REVISION    "201410100000Z"
    DESCRIPTION
        "Editorial changes"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 121 }


MGMTddmiMonitorState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the monitor state for a particular monitor
         type for SFPs."
    SYNTAX      INTEGER { none(0), lowWarn(1), highWarn(2),
                          lowAlarm(3), highAlarm(4) }

mgmtDdmiMibObjects OBJECT IDENTIFIER
    ::= { mgmtDdmiMib 1 }

mgmtDdmiConfig OBJECT IDENTIFIER
    ::= { mgmtDdmiMibObjects 2 }

mgmtDdmiConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtDdmiConfig 1 }

mgmtDdmiConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Set to true to enable DDMI on all SFP ports."
    ::= { mgmtDdmiConfigGlobals 1 }

mgmtDdmiStatus OBJECT IDENTIFIER
    ::= { mgmtDdmiMibObjects 3 }

mgmtDdmiStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTDdmiStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a DDMI status table of port interface."
    ::= { mgmtDdmiStatus 2 }

mgmtDdmiStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTDdmiStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of DDMI status."
    INDEX       { mgmtDdmiStatusInterfaceIfIndex }
    ::= { mgmtDdmiStatusInterfaceTable 1 }

MGMTDdmiStatusInterfaceEntry ::= SEQUENCE {
    mgmtDdmiStatusInterfaceIfIndex                          MGMTInterfaceIndex,
    mgmtDdmiStatusInterfaceA0Supported                      TruthValue,
    mgmtDdmiStatusInterfaceA0SfpDetected                    TruthValue,
    mgmtDdmiStatusInterfaceA0Vendor                         MGMTDisplayString,
    mgmtDdmiStatusInterfaceA0PartNumber                     MGMTDisplayString,
    mgmtDdmiStatusInterfaceA0SerialNumber                   MGMTDisplayString,
    mgmtDdmiStatusInterfaceA0Revision                       MGMTDisplayString,
    mgmtDdmiStatusInterfaceA0DateCode                       MGMTDisplayString,
    mgmtDdmiStatusInterfaceA0SfpType                        MGMTSfpTransceiver,
    mgmtDdmiStatusInterfaceA2Supported                      TruthValue,
    mgmtDdmiStatusInterfaceA2TemperatureState               MGMTddmiMonitorState,
    mgmtDdmiStatusInterfaceA2TemperatureCurrent             MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TemperatureHighAlarmThreshold  MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TemperatureLowAlarmThreshold   MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TemperatureHighWarnThreshold   MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TemperatureLowWarnThreshold    MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2VoltageState                   MGMTddmiMonitorState,
    mgmtDdmiStatusInterfaceA2VoltageCurrent                 MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2VoltageHighAlarmThreshold      MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2VoltageLowAlarmThreshold       MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2VoltageHighWarnThreshold       MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2VoltageLowWarnThreshold        MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxBiasState                    MGMTddmiMonitorState,
    mgmtDdmiStatusInterfaceA2TxBiasCurrent                  MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxBiasHighAlarmThreshold       MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxBiasLowAlarmThreshold        MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxBiasHighWarnThreshold        MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxBiasLowWarnThreshold         MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxPowerState                   MGMTddmiMonitorState,
    mgmtDdmiStatusInterfaceA2TxPowerCurrent                 MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxPowerHighAlarmThreshold      MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxPowerLowAlarmThreshold       MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxPowerHighWarnThreshold       MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2TxPowerLowWarnThreshold        MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2RxPowerState                   MGMTddmiMonitorState,
    mgmtDdmiStatusInterfaceA2RxPowerCurrent                 MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2RxPowerHighAlarmThreshold      MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2RxPowerLowAlarmThreshold       MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2RxPowerHighWarnThreshold       MGMTDisplayString,
    mgmtDdmiStatusInterfaceA2RxPowerLowWarnThreshold        MGMTDisplayString
}

mgmtDdmiStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtDdmiStatusInterfaceEntry 1 }

mgmtDdmiStatusInterfaceA0Supported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Support transceiver status information or not. true is to supported and
         false is not supported."
    ::= { mgmtDdmiStatusInterfaceEntry 2 }

mgmtDdmiStatusInterfaceA0SfpDetected OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "SFP module is detected or not. true is to detected and false is not
         detected."
    ::= { mgmtDdmiStatusInterfaceEntry 3 }

mgmtDdmiStatusInterfaceA0Vendor OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Vendor name."
    ::= { mgmtDdmiStatusInterfaceEntry 4 }

mgmtDdmiStatusInterfaceA0PartNumber OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Part number."
    ::= { mgmtDdmiStatusInterfaceEntry 5 }

mgmtDdmiStatusInterfaceA0SerialNumber OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Serial number."
    ::= { mgmtDdmiStatusInterfaceEntry 6 }

mgmtDdmiStatusInterfaceA0Revision OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Revision."
    ::= { mgmtDdmiStatusInterfaceEntry 7 }

mgmtDdmiStatusInterfaceA0DateCode OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Date Code."
    ::= { mgmtDdmiStatusInterfaceEntry 8 }

mgmtDdmiStatusInterfaceA0SfpType OBJECT-TYPE
    SYNTAX      MGMTSfpTransceiver
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "SFP type."
    ::= { mgmtDdmiStatusInterfaceEntry 9 }

mgmtDdmiStatusInterfaceA2Supported OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "True if DDMI status information is supported by the SFP."
    ::= { mgmtDdmiStatusInterfaceEntry 1002 }

mgmtDdmiStatusInterfaceA2TemperatureState OBJECT-TYPE
    SYNTAX      MGMTddmiMonitorState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current Temperature monitor state"
    ::= { mgmtDdmiStatusInterfaceEntry 1003 }

mgmtDdmiStatusInterfaceA2TemperatureCurrent OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current Temperature in degrees Celsius."
    ::= { mgmtDdmiStatusInterfaceEntry 1004 }

mgmtDdmiStatusInterfaceA2TemperatureHighAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Temperature high alarm threshold in degrees Celsius."
    ::= { mgmtDdmiStatusInterfaceEntry 1005 }

mgmtDdmiStatusInterfaceA2TemperatureLowAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Temperature low alarm threshold in degrees Celsius."
    ::= { mgmtDdmiStatusInterfaceEntry 1006 }

mgmtDdmiStatusInterfaceA2TemperatureHighWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Temperature high warning threshold in degrees Celsius."
    ::= { mgmtDdmiStatusInterfaceEntry 1007 }

mgmtDdmiStatusInterfaceA2TemperatureLowWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Temperature low warning threshold in degrees Celsius."
    ::= { mgmtDdmiStatusInterfaceEntry 1008 }

mgmtDdmiStatusInterfaceA2VoltageState OBJECT-TYPE
    SYNTAX      MGMTddmiMonitorState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current Voltage monitor state"
    ::= { mgmtDdmiStatusInterfaceEntry 1009 }

mgmtDdmiStatusInterfaceA2VoltageCurrent OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current Voltage in Volts."
    ::= { mgmtDdmiStatusInterfaceEntry 1010 }

mgmtDdmiStatusInterfaceA2VoltageHighAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Voltage high alarm threshold in Volts."
    ::= { mgmtDdmiStatusInterfaceEntry 1011 }

mgmtDdmiStatusInterfaceA2VoltageLowAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Voltage low alarm threshold in Volts."
    ::= { mgmtDdmiStatusInterfaceEntry 1012 }

mgmtDdmiStatusInterfaceA2VoltageHighWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Voltage high warning threshold in Volts."
    ::= { mgmtDdmiStatusInterfaceEntry 1013 }

mgmtDdmiStatusInterfaceA2VoltageLowWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Voltage low warning threshold in Volts."
    ::= { mgmtDdmiStatusInterfaceEntry 1014 }

mgmtDdmiStatusInterfaceA2TxBiasState OBJECT-TYPE
    SYNTAX      MGMTddmiMonitorState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current TxBias monitor state"
    ::= { mgmtDdmiStatusInterfaceEntry 1015 }

mgmtDdmiStatusInterfaceA2TxBiasCurrent OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current TxBias in mA."
    ::= { mgmtDdmiStatusInterfaceEntry 1016 }

mgmtDdmiStatusInterfaceA2TxBiasHighAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TxBias high alarm threshold in mA."
    ::= { mgmtDdmiStatusInterfaceEntry 1017 }

mgmtDdmiStatusInterfaceA2TxBiasLowAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TxBias low alarm threshold in mA."
    ::= { mgmtDdmiStatusInterfaceEntry 1018 }

mgmtDdmiStatusInterfaceA2TxBiasHighWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TxBias high warning threshold in mA."
    ::= { mgmtDdmiStatusInterfaceEntry 1019 }

mgmtDdmiStatusInterfaceA2TxBiasLowWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TxBias low warning threshold in mA."
    ::= { mgmtDdmiStatusInterfaceEntry 1020 }

mgmtDdmiStatusInterfaceA2TxPowerState OBJECT-TYPE
    SYNTAX      MGMTddmiMonitorState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current TxPower monitor state"
    ::= { mgmtDdmiStatusInterfaceEntry 1021 }

mgmtDdmiStatusInterfaceA2TxPowerCurrent OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current TxPower in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1022 }

mgmtDdmiStatusInterfaceA2TxPowerHighAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TxPower high alarm threshold in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1023 }

mgmtDdmiStatusInterfaceA2TxPowerLowAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TxPower low alarm threshold in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1024 }

mgmtDdmiStatusInterfaceA2TxPowerHighWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TxPower high warning threshold in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1025 }

mgmtDdmiStatusInterfaceA2TxPowerLowWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "TxPower low warning threshold in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1026 }

mgmtDdmiStatusInterfaceA2RxPowerState OBJECT-TYPE
    SYNTAX      MGMTddmiMonitorState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current RxPower monitor state"
    ::= { mgmtDdmiStatusInterfaceEntry 1027 }

mgmtDdmiStatusInterfaceA2RxPowerCurrent OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Current RxPower in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1028 }

mgmtDdmiStatusInterfaceA2RxPowerHighAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "RxPower high alarm threshold in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1029 }

mgmtDdmiStatusInterfaceA2RxPowerLowAlarmThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "RxPower low alarm threshold in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1030 }

mgmtDdmiStatusInterfaceA2RxPowerHighWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "RxPower high warning threshold in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1031 }

mgmtDdmiStatusInterfaceA2RxPowerLowWarnThreshold OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "RxPower low warning threshold in mW."
    ::= { mgmtDdmiStatusInterfaceEntry 1032 }

mgmtDdmiMibConformance OBJECT IDENTIFIER
    ::= { mgmtDdmiMib 2 }

mgmtDdmiMibCompliances OBJECT IDENTIFIER
    ::= { mgmtDdmiMibConformance 1 }

mgmtDdmiMibGroups OBJECT IDENTIFIER
    ::= { mgmtDdmiMibConformance 2 }

mgmtDdmiConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDdmiConfigGlobalsMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDdmiMibGroups 1 }

mgmtDdmiStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDdmiStatusInterfaceIfIndex,
                  mgmtDdmiStatusInterfaceA0Supported,
                  mgmtDdmiStatusInterfaceA0SfpDetected,
                  mgmtDdmiStatusInterfaceA0Vendor,
                  mgmtDdmiStatusInterfaceA0PartNumber,
                  mgmtDdmiStatusInterfaceA0SerialNumber,
                  mgmtDdmiStatusInterfaceA0Revision,
                  mgmtDdmiStatusInterfaceA0DateCode,
                  mgmtDdmiStatusInterfaceA0SfpType,
                  mgmtDdmiStatusInterfaceA2Supported,
                  mgmtDdmiStatusInterfaceA2TemperatureState,
                  mgmtDdmiStatusInterfaceA2TemperatureCurrent,
                  mgmtDdmiStatusInterfaceA2TemperatureHighAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2TemperatureLowAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2TemperatureHighWarnThreshold,
                  mgmtDdmiStatusInterfaceA2TemperatureLowWarnThreshold,
                  mgmtDdmiStatusInterfaceA2VoltageState,
                  mgmtDdmiStatusInterfaceA2VoltageCurrent,
                  mgmtDdmiStatusInterfaceA2VoltageHighAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2VoltageLowAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2VoltageHighWarnThreshold,
                  mgmtDdmiStatusInterfaceA2VoltageLowWarnThreshold,
                  mgmtDdmiStatusInterfaceA2TxBiasState,
                  mgmtDdmiStatusInterfaceA2TxBiasCurrent,
                  mgmtDdmiStatusInterfaceA2TxBiasHighAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2TxBiasLowAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2TxBiasHighWarnThreshold,
                  mgmtDdmiStatusInterfaceA2TxBiasLowWarnThreshold,
                  mgmtDdmiStatusInterfaceA2TxPowerState,
                  mgmtDdmiStatusInterfaceA2TxPowerCurrent,
                  mgmtDdmiStatusInterfaceA2TxPowerHighAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2TxPowerLowAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2TxPowerHighWarnThreshold,
                  mgmtDdmiStatusInterfaceA2TxPowerLowWarnThreshold,
                  mgmtDdmiStatusInterfaceA2RxPowerState,
                  mgmtDdmiStatusInterfaceA2RxPowerCurrent,
                  mgmtDdmiStatusInterfaceA2RxPowerHighAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2RxPowerLowAlarmThreshold,
                  mgmtDdmiStatusInterfaceA2RxPowerHighWarnThreshold,
                  mgmtDdmiStatusInterfaceA2RxPowerLowWarnThreshold }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDdmiMibGroups 2 }

mgmtDdmiMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtDdmiConfigGlobalsInfoGroup,
                       mgmtDdmiStatusInterfaceTableInfoGroup }

    ::= { mgmtDdmiMibCompliances 1 }

END
