-- *****************************************************************
-- DAYLIGHT-SAVING-MIB:  
-- ****************************************************************

MGMT-DAYLIGHT-SAVING-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    MGMTDisplayString FROM MGMT-TC
    MGMTInteger16 FROM MGMT-TC
    MGMTUnsigned16 FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtDaylightSavingMib MODULE-IDENTITY
    LAST-UPDATED "201701060000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of daylight saving. Used to configure system
         Summer time(Daylight Saving) and Time Zone."
    REVISION    "201701060000Z"
    DESCRIPTION
        "Update time zone offset valid range"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 97 }


MGMTDaylightSavingMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the available summer time(Daylight Saving)
         mode."
    SYNTAX      INTEGER { disable(0), recurring(1), nonRecurring(2) }

mgmtDaylightSavingMibObjects OBJECT IDENTIFIER
    ::= { mgmtDaylightSavingMib 1 }

mgmtDaylightSavingConfig OBJECT IDENTIFIER
    ::= { mgmtDaylightSavingMibObjects 2 }

mgmtDaylightSavingConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtDaylightSavingConfig 1 }

mgmtDaylightSavingConfigGlobalsTimeZoneAcronym OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a acronym to identify the time zone."
    ::= { mgmtDaylightSavingConfigGlobals 1 }

mgmtDaylightSavingConfigGlobalsTimeZoneOffset OBJECT-TYPE
    SYNTAX      MGMTInteger16 (-1439..1439)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "To set the system time zone with respect to UTC in minutes."
    ::= { mgmtDaylightSavingConfigGlobals 2 }

mgmtDaylightSavingConfigGlobalsSummerTimeMode OBJECT-TYPE
    SYNTAX      MGMTDaylightSavingMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time mode.
         
         Disabled: The daylight feature is disabled, and no input validation is
         performed on remaining configurations parameters.
         
         Recurring mode: Summer time configuration will repeat every year. To
         enable this mode requires that the parameters Month, Week and Day are
         configured with valid values (non zero). The parameters Year and Date
         must be set to 0, signaling that they are not used.
         
         Non recurring mode: Summer time configuration is done once. To enable
         this feature requires that the following values are configured with
         valid values.
         
         (non zero): Year, Month and Date. The parameters Week and Day must be
         set to 0 signaling that they are not used."
    ::= { mgmtDaylightSavingConfigGlobals 3 }

mgmtDaylightSavingConfigGlobalsSummerTimeWeekStart OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..5)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time starting week. This object needs to be set when
         summer time mode is recurring. Object value 0 means unused object."
    ::= { mgmtDaylightSavingConfigGlobals 4 }

mgmtDaylightSavingConfigGlobalsSummerTimeDayStart OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..7)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time starting day. where monday = 1, sunday = 7.
         
         This object needs to be set when summer time mode is recurring. Object
         value 0 means unused object."
    ::= { mgmtDaylightSavingConfigGlobals 5 }

mgmtDaylightSavingConfigGlobalsSummerTimeMonthStart OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..12)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time starting month. This object needs to be set when
         summer time mode is not disabled."
    ::= { mgmtDaylightSavingConfigGlobals 6 }

mgmtDaylightSavingConfigGlobalsSummerTimeDateStart OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..31)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time starting date. This object needs to be set when
         summer time mode is non recurring."
    ::= { mgmtDaylightSavingConfigGlobals 7 }

mgmtDaylightSavingConfigGlobalsSummerTimeYearStart OBJECT-TYPE
    SYNTAX      MGMTUnsigned16 (0..2097)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time starting year. This object needs to be set when
         summer time mode is non recurring. Object value 0 means unused object."
    ::= { mgmtDaylightSavingConfigGlobals 8 }

mgmtDaylightSavingConfigGlobalsSummerTimeHourStart OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..23)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time starting hour. This object needs to be set when
         summer time mode is not disabled:"
    ::= { mgmtDaylightSavingConfigGlobals 9 }

mgmtDaylightSavingConfigGlobalsSummerTimeMinuteStart OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..59)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time starting minute. This object needs to be set when
         summer time mode is not disabled."
    ::= { mgmtDaylightSavingConfigGlobals 10 }

mgmtDaylightSavingConfigGlobalsSummerTimeWeekEnd OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..5)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time ending week. This object needs to be set when
         summer time mode is recurring. Object value 0 means unused object."
    ::= { mgmtDaylightSavingConfigGlobals 11 }

mgmtDaylightSavingConfigGlobalsSummerTimeDayEnd OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..7)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time ending day. This object needs to be set when
         summer time mode is recurring. Object value 0 means unused object."
    ::= { mgmtDaylightSavingConfigGlobals 12 }

mgmtDaylightSavingConfigGlobalsSummerTimeMonthEnd OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (1..12)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time ending month. This object needs to be set when
         summer time mode is not disabled."
    ::= { mgmtDaylightSavingConfigGlobals 13 }

mgmtDaylightSavingConfigGlobalsSummerTimeDateEnd OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..31)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time ending date. This object needs to be set when
         summer time mode is non recurring. Object value 0 means unused object."
    ::= { mgmtDaylightSavingConfigGlobals 14 }

mgmtDaylightSavingConfigGlobalsSummerTimeYearEnd OBJECT-TYPE
    SYNTAX      MGMTUnsigned16 (0..2097)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time ending year. This object needs to be set when
         summer time mode is non recurring. Object value 0 means unused object."
    ::= { mgmtDaylightSavingConfigGlobals 15 }

mgmtDaylightSavingConfigGlobalsSummerTimeHourEnd OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..23)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time ending hour. This object needs to be set when
         summer time mode is not disabled."
    ::= { mgmtDaylightSavingConfigGlobals 16 }

mgmtDaylightSavingConfigGlobalsSummerTimeMinuteEnd OBJECT-TYPE
    SYNTAX      MGMTUnsigned8 (0..59)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This is a summer time ending minute. This object needs to be set when
         summer time mode is not disabled."
    ::= { mgmtDaylightSavingConfigGlobals 17 }

mgmtDaylightSavingConfigGlobalsSummerTimeOffset OBJECT-TYPE
    SYNTAX      MGMTInteger16 (1..1439)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The value of this object indicates the number of minutes to add or to
         subtract during summertime. This object needs to be set when summer
         time mode is not disabled."
    ::= { mgmtDaylightSavingConfigGlobals 18 }

mgmtDaylightSavingMibConformance OBJECT IDENTIFIER
    ::= { mgmtDaylightSavingMib 2 }

mgmtDaylightSavingMibCompliances OBJECT IDENTIFIER
    ::= { mgmtDaylightSavingMibConformance 1 }

mgmtDaylightSavingMibGroups OBJECT IDENTIFIER
    ::= { mgmtDaylightSavingMibConformance 2 }

mgmtDaylightSavingConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtDaylightSavingConfigGlobalsTimeZoneAcronym,
                  mgmtDaylightSavingConfigGlobalsTimeZoneOffset,
                  mgmtDaylightSavingConfigGlobalsSummerTimeMode,
                  mgmtDaylightSavingConfigGlobalsSummerTimeWeekStart,
                  mgmtDaylightSavingConfigGlobalsSummerTimeDayStart,
                  mgmtDaylightSavingConfigGlobalsSummerTimeMonthStart,
                  mgmtDaylightSavingConfigGlobalsSummerTimeDateStart,
                  mgmtDaylightSavingConfigGlobalsSummerTimeYearStart,
                  mgmtDaylightSavingConfigGlobalsSummerTimeHourStart,
                  mgmtDaylightSavingConfigGlobalsSummerTimeMinuteStart,
                  mgmtDaylightSavingConfigGlobalsSummerTimeWeekEnd,
                  mgmtDaylightSavingConfigGlobalsSummerTimeDayEnd,
                  mgmtDaylightSavingConfigGlobalsSummerTimeMonthEnd,
                  mgmtDaylightSavingConfigGlobalsSummerTimeDateEnd,
                  mgmtDaylightSavingConfigGlobalsSummerTimeYearEnd,
                  mgmtDaylightSavingConfigGlobalsSummerTimeHourEnd,
                  mgmtDaylightSavingConfigGlobalsSummerTimeMinuteEnd,
                  mgmtDaylightSavingConfigGlobalsSummerTimeOffset }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtDaylightSavingMibGroups 1 }

mgmtDaylightSavingMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtDaylightSavingConfigGlobalsInfoGroup }

    ::= { mgmtDaylightSavingMibCompliances 1 }

END
