-- *****************************************************************
-- MRP-MIB:  
-- ****************************************************************

MGMT-MRP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    ;

mgmtMrpMib MODULE-IDENTITY
    LAST-UPDATED "201510190000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private MRP MIB."
    REVISION    "201510190000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 86 }


mgmtMrpMibObjects OBJECT IDENTIFIER
    ::= { mgmtMrpMib 1 }

mgmtMrpCapabilities OBJECT IDENTIFIER
    ::= { mgmtMrpMibObjects 1 }

mgmtMrpCapabilitiesJoinTimeoutMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum value of MRP Join timeout in centiseconds."
    ::= { mgmtMrpCapabilities 1 }

mgmtMrpCapabilitiesJoinTimeoutMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum value of MRP Join timeout in centiseconds."
    ::= { mgmtMrpCapabilities 2 }

mgmtMrpCapabilitiesLeaveTimeoutMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum value of MRP Leave timeout in centiseconds."
    ::= { mgmtMrpCapabilities 3 }

mgmtMrpCapabilitiesLeaveTimeoutMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum value of MRP Leave timeout in centiseconds."
    ::= { mgmtMrpCapabilities 4 }

mgmtMrpCapabilitiesLeaveAllTimeoutMin OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Minimum value of MRP LeaveAll timeout in centiseconds."
    ::= { mgmtMrpCapabilities 5 }

mgmtMrpCapabilitiesLeaveAllTimeoutMax OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Maximum value of MRP LeaveAll timeout in centiseconds."
    ::= { mgmtMrpCapabilities 6 }

mgmtMrpConfig OBJECT IDENTIFIER
    ::= { mgmtMrpMibObjects 2 }

mgmtMrpConfigInterface OBJECT IDENTIFIER
    ::= { mgmtMrpConfig 1 }

mgmtMrpConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTMrpConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is the MRP interface configuration table. The number of interfaces
         is the total number of ports available on the switch/stack. MRP timer
         values and the state of the PeriodicTransmission STM can be configured
         for each interface."
    ::= { mgmtMrpConfigInterface 1 }

mgmtMrpConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTMrpConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtMrpConfigInterfaceIfIndex }
    ::= { mgmtMrpConfigInterfaceTable 1 }

MGMTMrpConfigInterfaceEntry ::= SEQUENCE {
    mgmtMrpConfigInterfaceIfIndex               MGMTInterfaceIndex,
    mgmtMrpConfigInterfaceJoinTimeout           Integer32,
    mgmtMrpConfigInterfaceLeaveTimeout          Integer32,
    mgmtMrpConfigInterfaceLeaveAllTimeout       Integer32,
    mgmtMrpConfigInterfacePeriodicTransmission  TruthValue
}

mgmtMrpConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Interface index number."
    ::= { mgmtMrpConfigInterfaceEntry 1 }

mgmtMrpConfigInterfaceJoinTimeout OBJECT-TYPE
    SYNTAX      Integer32 (1..20)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Join-timeout protocol parameter. Range [1, 20]cs."
    ::= { mgmtMrpConfigInterfaceEntry 2 }

mgmtMrpConfigInterfaceLeaveTimeout OBJECT-TYPE
    SYNTAX      Integer32 (60..300)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Leave-timeout protocol parameter. Range [60, 300]cs."
    ::= { mgmtMrpConfigInterfaceEntry 3 }

mgmtMrpConfigInterfaceLeaveAllTimeout OBJECT-TYPE
    SYNTAX      Integer32 (1000..5000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "LeaveAll-timeout protocol parameter. Range [1000, 5000] cs."
    ::= { mgmtMrpConfigInterfaceEntry 4 }

mgmtMrpConfigInterfacePeriodicTransmission OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "PeriodicTransmission state of MRP. TRUE - enable PeriodicTransmission,
         FALSE - disable PeriodicTransmission."
    ::= { mgmtMrpConfigInterfaceEntry 5 }

mgmtMrpMibConformance OBJECT IDENTIFIER
    ::= { mgmtMrpMib 2 }

mgmtMrpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtMrpMibConformance 1 }

mgmtMrpMibGroups OBJECT IDENTIFIER
    ::= { mgmtMrpMibConformance 2 }

mgmtMrpCapabilitiesInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMrpCapabilitiesJoinTimeoutMin,
                  mgmtMrpCapabilitiesJoinTimeoutMax,
                  mgmtMrpCapabilitiesLeaveTimeoutMin,
                  mgmtMrpCapabilitiesLeaveTimeoutMax,
                  mgmtMrpCapabilitiesLeaveAllTimeoutMin,
                  mgmtMrpCapabilitiesLeaveAllTimeoutMax }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMrpMibGroups 1 }

mgmtMrpConfigInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtMrpConfigInterfaceIfIndex,
                  mgmtMrpConfigInterfaceJoinTimeout,
                  mgmtMrpConfigInterfaceLeaveTimeout,
                  mgmtMrpConfigInterfaceLeaveAllTimeout,
                  mgmtMrpConfigInterfacePeriodicTransmission }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtMrpMibGroups 2 }

mgmtMrpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtMrpCapabilitiesInfoGroup,
                       mgmtMrpConfigInterfaceTableInfoGroup }

    ::= { mgmtMrpMibCompliances 1 }

END
