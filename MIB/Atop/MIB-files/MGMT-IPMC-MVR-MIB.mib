-- *****************************************************************
-- IPMC-MVR-MIB:  
-- ****************************************************************

MGMT-IPMC-MVR-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    InetAddressIPv6 FROM INET-ADDRESS-MIB
    IpAddress FROM SNMPv2-SMI
    Unsigned32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    MGMTPortList FROM MGMT-TC
    MGMTRowEditorState FROM MGMT-TC
    MGMTUnsigned8 FROM MGMT-TC
    ;

mgmtIpmcMvrMib MODULE-IDENTITY
    LAST-UPDATED "201708240000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private version of the IPMC MVR MIB"
    REVISION    "201708240000Z"
    DESCRIPTION
        "Added Querier election cofigured option"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 68 }


MGMTIpmcMvrGroupSrcListGroupFilterMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the group filter mode for an IPMC group
         address."
    SYNTAX      INTEGER { exclude(0), include(1) }

MGMTIpmcMvrGroupSrcListSourceType ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the source filtering type from IPMC."
    SYNTAX      INTEGER { deny(0), permit(1) }

MGMTIpmcMvrInterfaceQuerierStatus ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the Querier status for MVR VLAN interface."
    SYNTAX      INTEGER { disabled(-1), initial(0), idle(1),
                          active(2) }

MGMTIpmcMvrVlanInterfaceMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the MVR VLAN interface's operational mode."
    SYNTAX      INTEGER { dynamic(0), compatible(1) }

MGMTIpmcMvrVlanInterfacePortRole ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the MVR port's operational role."
    SYNTAX      INTEGER { inactive(0), source(1), receiver(2) }

MGMTIpmcMvrVlanInterfaceTagging ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration indicates the VLAN tagging for IPMC control frames in
         MVR."
    SYNTAX      INTEGER { untagged(0), tagging(1) }

mgmtIpmcMvrMibObjects OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrMib 1 }

mgmtIpmcMvrConfig OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrMibObjects 2 }

mgmtIpmcMvrConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrConfig 1 }

mgmtIpmcMvrConfigGlobalsAdminState OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the IPMC MVR global functionality."
    ::= { mgmtIpmcMvrConfigGlobals 1 }

mgmtIpmcMvrConfigPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcMvrConfigPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing extra MVR helper features per port basis."
    ::= { mgmtIpmcMvrConfig 2 }

mgmtIpmcMvrConfigPortEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrConfigPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters."
    INDEX       { mgmtIpmcMvrConfigPortPortIndex }
    ::= { mgmtIpmcMvrConfigPortTable 1 }

MGMTIpmcMvrConfigPortEntry ::= SEQUENCE {
    mgmtIpmcMvrConfigPortPortIndex         MGMTInterfaceIndex,
    mgmtIpmcMvrConfigPortDoImmediateLeave  TruthValue
}

mgmtIpmcMvrConfigPortPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcMvrConfigPortEntry 1 }

mgmtIpmcMvrConfigPortDoImmediateLeave OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the MVR immediate leave functionality."
    ::= { mgmtIpmcMvrConfigPortEntry 2 }

mgmtIpmcMvrConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcMvrConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing MVR VLAN interface entries."
    ::= { mgmtIpmcMvrConfig 3 }

mgmtIpmcMvrConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters"
    INDEX       { mgmtIpmcMvrConfigInterfaceIfIndex }
    ::= { mgmtIpmcMvrConfigInterfaceTable 1 }

MGMTIpmcMvrConfigInterfaceEntry ::= SEQUENCE {
    mgmtIpmcMvrConfigInterfaceIfIndex               MGMTInterfaceIndex,
    mgmtIpmcMvrConfigInterfaceName                  MGMTDisplayString,
    mgmtIpmcMvrConfigInterfaceIgmpQuerierAddress    IpAddress,
    mgmtIpmcMvrConfigInterfaceMode                  MGMTIpmcMvrVlanInterfaceMode,
    mgmtIpmcMvrConfigInterfaceTagging               MGMTIpmcMvrVlanInterfaceTagging,
    mgmtIpmcMvrConfigInterfacePriority              MGMTUnsigned8,
    mgmtIpmcMvrConfigInterfaceLastListenerQueryInt  Unsigned32,
    mgmtIpmcMvrConfigInterfaceChannelProfile        MGMTDisplayString,
    mgmtIpmcMvrConfigInterfaceQuerierElection       TruthValue,
    mgmtIpmcMvrConfigInterfaceAction                MGMTRowEditorState
}

mgmtIpmcMvrConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrConfigInterfaceEntry 1 }

mgmtIpmcMvrConfigInterfaceName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MVR Name is an optional attribute to indicate the name of the specific
         MVR VLAN that user could easily associate the MVR VLAN purpose with its
         name."
    ::= { mgmtIpmcMvrConfigInterfaceEntry 2 }

mgmtIpmcMvrConfigInterfaceIgmpQuerierAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The static IPv4 source address of the specific MVR interface for seding
         IGMP Query message with respect to IGMP Querier election."
    ::= { mgmtIpmcMvrConfigInterfaceEntry 3 }

mgmtIpmcMvrConfigInterfaceMode OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrVlanInterfaceMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "In Dynamic mode, MVR allows dynamic MVR membership reports on source
         ports. In Compatible mode, MVR membership reports are forbidden on
         source ports."
    ::= { mgmtIpmcMvrConfigInterfaceEntry 4 }

mgmtIpmcMvrConfigInterfaceTagging OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrVlanInterfaceTagging
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify whether the traversed IGMP/MLD control frames will be sent as
         Untagged or Tagged with MVR VID."
    ::= { mgmtIpmcMvrConfigInterfaceEntry 5 }

mgmtIpmcMvrConfigInterfacePriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify how the traversed IGMP/MLD control frames will be sent in
         prioritized manner in VLAN tag."
    ::= { mgmtIpmcMvrConfigInterfaceEntry 6 }

mgmtIpmcMvrConfigInterfaceLastListenerQueryInt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Last Listener/Member Query Interval is used to control
         IGMP protocol stack for fast aging mechanism. It defines the maximum
         time to wait for IGMP/MLD report memberships on a port before removing
         the port from multicast group membership. The value is in units of
         tenths of a seconds. The range is from 0 to 31744."
    ::= { mgmtIpmcMvrConfigInterfaceEntry 7 }

mgmtIpmcMvrConfigInterfaceChannelProfile OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The profile used for the channel filtering condition in the specific
         MVR VLAN. Profile selected for designated interface channel is not
         allowed to have overlapped permit group address by comparing with other
         MVR VLAN interface's channel."
    ::= { mgmtIpmcMvrConfigInterfaceEntry 8 }

mgmtIpmcMvrConfigInterfaceQuerierElection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the capability to run IGMP Querier election per-VLAN
         basis."
    ::= { mgmtIpmcMvrConfigInterfaceEntry 9 }

mgmtIpmcMvrConfigInterfaceAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcMvrConfigInterfaceEntry 100 }

mgmtIpmcMvrConfigInterfaceTableRowEditor OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrConfig 4 }

mgmtIpmcMvrConfigInterfaceTableRowEditorIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 1 }

mgmtIpmcMvrConfigInterfaceTableRowEditorName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "MVR Name is an optional attribute to indicate the name of the specific
         MVR VLAN that user could easily associate the MVR VLAN purpose with its
         name."
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 2 }

mgmtIpmcMvrConfigInterfaceTableRowEditorIgmpQuerierAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The static IPv4 source address of the specific MVR interface for seding
         IGMP Query message with respect to IGMP Querier election."
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 3 }

mgmtIpmcMvrConfigInterfaceTableRowEditorMode OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrVlanInterfaceMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "In Dynamic mode, MVR allows dynamic MVR membership reports on source
         ports. In Compatible mode, MVR membership reports are forbidden on
         source ports."
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 4 }

mgmtIpmcMvrConfigInterfaceTableRowEditorTagging OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrVlanInterfaceTagging
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify whether the traversed IGMP/MLD control frames will be sent as
         Untagged or Tagged with MVR VID."
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 5 }

mgmtIpmcMvrConfigInterfaceTableRowEditorPriority OBJECT-TYPE
    SYNTAX      MGMTUnsigned8
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Specify how the traversed IGMP/MLD control frames will be sent in
         prioritized manner in VLAN tag."
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 6 }

mgmtIpmcMvrConfigInterfaceTableRowEditorLastListenerQueryInt OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "This setting Last Listener/Member Query Interval is used to control
         IGMP protocol stack for fast aging mechanism. It defines the maximum
         time to wait for IGMP/MLD report memberships on a port before removing
         the port from multicast group membership. The value is in units of
         tenths of a seconds. The range is from 0 to 31744."
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 7 }

mgmtIpmcMvrConfigInterfaceTableRowEditorChannelProfile OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..16))
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "The profile used for the channel filtering condition in the specific
         MVR VLAN. Profile selected for designated interface channel is not
         allowed to have overlapped permit group address by comparing with other
         MVR VLAN interface's channel."
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 8 }

mgmtIpmcMvrConfigInterfaceTableRowEditorQuerierElection OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Enable/Disable the capability to run IGMP Querier election per-VLAN
         basis."
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 9 }

mgmtIpmcMvrConfigInterfaceTableRowEditorAction OBJECT-TYPE
    SYNTAX      MGMTRowEditorState
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Action"
    ::= { mgmtIpmcMvrConfigInterfaceTableRowEditor 100 }

mgmtIpmcMvrConfigVlanPortTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcMvrConfigVlanPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for managing MVR port roles of a specific MVR VLAN
         interface."
    ::= { mgmtIpmcMvrConfig 5 }

mgmtIpmcMvrConfigVlanPortEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrConfigVlanPortEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each port has a set of parameters."
    INDEX       { mgmtIpmcMvrConfigVlanPortIfIndex,
                  mgmtIpmcMvrConfigVlanPortPortIndex }
    ::= { mgmtIpmcMvrConfigVlanPortTable 1 }

MGMTIpmcMvrConfigVlanPortEntry ::= SEQUENCE {
    mgmtIpmcMvrConfigVlanPortIfIndex    MGMTInterfaceIndex,
    mgmtIpmcMvrConfigVlanPortPortIndex  MGMTInterfaceIndex,
    mgmtIpmcMvrConfigVlanPortRole       MGMTIpmcMvrVlanInterfacePortRole
}

mgmtIpmcMvrConfigVlanPortIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrConfigVlanPortEntry 1 }

mgmtIpmcMvrConfigVlanPortPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcMvrConfigVlanPortEntry 2 }

mgmtIpmcMvrConfigVlanPortRole OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrVlanInterfacePortRole
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Configure a MVR port of the designated MVR VLAN as one of the following
         roles: Inactive, Source or Receiver. An inactive port does not
         participate MVR operations. Configure uplink ports that receive and
         send multicast data as a source port, and multicast subscribers cannot
         be directly connected to source ports. Configure a port as a receiver
         port if it is a subscriber port and should only receive multicast data,
         and a receiver port does not receive data unless it becomes a member of
         the multicast group by issuing IGMP/MLD control messages. Be Caution:
         MVR source ports are not recommended to be overlapped with management
         VLAN ports."
    ::= { mgmtIpmcMvrConfigVlanPortEntry 3 }

mgmtIpmcMvrStatus OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrMibObjects 3 }

mgmtIpmcMvrStatusGroupAddressCount OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrStatus 1 }

mgmtIpmcMvrStatusGroupAddressCountFromIgmp OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the total number of registered multicast group
         address from IGMP learning in MVR."
    ::= { mgmtIpmcMvrStatusGroupAddressCount 1 }

mgmtIpmcMvrStatusGroupAddressCountFromMld OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the total number of registered multicast group
         address from MLD learning in MVR."
    ::= { mgmtIpmcMvrStatusGroupAddressCount 2 }

mgmtIpmcMvrStatusIgmpVlanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcMvrStatusIgmpVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the per VLAN interface status in MVR
         from IGMP protocol."
    ::= { mgmtIpmcMvrStatus 2 }

mgmtIpmcMvrStatusIgmpVlanEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrStatusIgmpVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcMvrStatusIgmpVlanIfIndex }
    ::= { mgmtIpmcMvrStatusIgmpVlanTable 1 }

MGMTIpmcMvrStatusIgmpVlanEntry ::= SEQUENCE {
    mgmtIpmcMvrStatusIgmpVlanIfIndex                 MGMTInterfaceIndex,
    mgmtIpmcMvrStatusIgmpVlanQuerierStatus           MGMTIpmcMvrInterfaceQuerierStatus,
    mgmtIpmcMvrStatusIgmpVlanActiveQuerierAddress    IpAddress,
    mgmtIpmcMvrStatusIgmpVlanQuerierUptime           Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanQueryInterval           Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanStartupQueryCount       Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanQuerierExpiryTime       Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanCounterTxQuery          Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanCounterTxSpecificQuery  Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanCounterRxQuery          Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanCounterRxV1Join         Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanCounterRxV2Join         Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanCounterRxV2Leave        Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanCounterRxV3Join         Unsigned32,
    mgmtIpmcMvrStatusIgmpVlanCounterRxErrors         Unsigned32
}

mgmtIpmcMvrStatusIgmpVlanIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 1 }

mgmtIpmcMvrStatusIgmpVlanQuerierStatus OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrInterfaceQuerierStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The IGMP Querier status of the specific VLAN interface."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 2 }

mgmtIpmcMvrStatusIgmpVlanActiveQuerierAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The active IGMP Querier address on the specific VLAN interface."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 3 }

mgmtIpmcMvrStatusIgmpVlanQuerierUptime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the in operation timer for the specific interface act as a
         IGMP Querier."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 4 }

mgmtIpmcMvrStatusIgmpVlanQueryInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control IGMP protocol stack as stated in
         RFC-3376 8.2."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 5 }

mgmtIpmcMvrStatusIgmpVlanStartupQueryCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control IGMP protocol stack as stated in
         RFC-3376 8.7."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 6 }

mgmtIpmcMvrStatusIgmpVlanQuerierExpiryTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control IGMP protocol stack as stated in
         RFC-3376 8.5."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 7 }

mgmtIpmcMvrStatusIgmpVlanCounterTxQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         transmitting IGMP Query control frames."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 8 }

mgmtIpmcMvrStatusIgmpVlanCounterTxSpecificQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         transmitting IGMP Specific Query control frames."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 9 }

mgmtIpmcMvrStatusIgmpVlanCounterRxQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMP Query control frames."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 10 }

mgmtIpmcMvrStatusIgmpVlanCounterRxV1Join OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMPv1 Join control frames."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 11 }

mgmtIpmcMvrStatusIgmpVlanCounterRxV2Join OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMPv2 Join control frames."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 12 }

mgmtIpmcMvrStatusIgmpVlanCounterRxV2Leave OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMPv2 Leave control frames."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 13 }

mgmtIpmcMvrStatusIgmpVlanCounterRxV3Join OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving IGMPv3 Join control frames."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 14 }

mgmtIpmcMvrStatusIgmpVlanCounterRxErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific IGMP interface for
         receiving invalid IGMP control frames."
    ::= { mgmtIpmcMvrStatusIgmpVlanEntry 15 }

mgmtIpmcMvrStatusIgmpGroupAddressTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcMvrStatusIgmpGroupAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the registered IPv4 multicast group
         address status in IGMP from MVR."
    ::= { mgmtIpmcMvrStatus 3 }

mgmtIpmcMvrStatusIgmpGroupAddressEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrStatusIgmpGroupAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcMvrStatusIgmpGroupAddressIfIndex,
                  mgmtIpmcMvrStatusIgmpGroupAddressGroupAddress }
    ::= { mgmtIpmcMvrStatusIgmpGroupAddressTable 1 }

MGMTIpmcMvrStatusIgmpGroupAddressEntry ::= SEQUENCE {
    mgmtIpmcMvrStatusIgmpGroupAddressIfIndex         MGMTInterfaceIndex,
    mgmtIpmcMvrStatusIgmpGroupAddressGroupAddress    IpAddress,
    mgmtIpmcMvrStatusIgmpGroupAddressMemberPorts     MGMTPortList,
    mgmtIpmcMvrStatusIgmpGroupAddressHardwareSwitch  TruthValue
}

mgmtIpmcMvrStatusIgmpGroupAddressIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrStatusIgmpGroupAddressEntry 1 }

mgmtIpmcMvrStatusIgmpGroupAddressGroupAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv4 multicast address."
    ::= { mgmtIpmcMvrStatusIgmpGroupAddressEntry 2 }

mgmtIpmcMvrStatusIgmpGroupAddressMemberPorts OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the memberships of the registered multicast group
         address from MVR."
    ::= { mgmtIpmcMvrStatusIgmpGroupAddressEntry 3 }

mgmtIpmcMvrStatusIgmpGroupAddressHardwareSwitch OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote whether the multicast traffic destined to the
         registered group address could be forwarding by switch hardware or not."
    ::= { mgmtIpmcMvrStatusIgmpGroupAddressEntry 4 }

mgmtIpmcMvrStatusIgmpGroupSrcListTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcMvrStatusIgmpGroupSrcListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the address SFM (a.k.a Source List
         Multicast) status in source list of the registered IPv4 multicast group
         in IGMP from MVR."
    ::= { mgmtIpmcMvrStatus 4 }

mgmtIpmcMvrStatusIgmpGroupSrcListEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrStatusIgmpGroupSrcListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcMvrStatusIgmpGroupSrcListIfIndex,
                  mgmtIpmcMvrStatusIgmpGroupSrcListGroupAddress,
                  mgmtIpmcMvrStatusIgmpGroupSrcListPortIndex,
                  mgmtIpmcMvrStatusIgmpGroupSrcListHostAddress }
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListTable 1 }

MGMTIpmcMvrStatusIgmpGroupSrcListEntry ::= SEQUENCE {
    mgmtIpmcMvrStatusIgmpGroupSrcListIfIndex          MGMTInterfaceIndex,
    mgmtIpmcMvrStatusIgmpGroupSrcListGroupAddress     IpAddress,
    mgmtIpmcMvrStatusIgmpGroupSrcListPortIndex        MGMTInterfaceIndex,
    mgmtIpmcMvrStatusIgmpGroupSrcListHostAddress      IpAddress,
    mgmtIpmcMvrStatusIgmpGroupSrcListGroupFilterMode  MGMTIpmcMvrGroupSrcListGroupFilterMode,
    mgmtIpmcMvrStatusIgmpGroupSrcListFilterTimer      Unsigned32,
    mgmtIpmcMvrStatusIgmpGroupSrcListSourceType       MGMTIpmcMvrGroupSrcListSourceType,
    mgmtIpmcMvrStatusIgmpGroupSrcListSourceTimer      Unsigned32,
    mgmtIpmcMvrStatusIgmpGroupSrcListHardwareFilter   TruthValue
}

mgmtIpmcMvrStatusIgmpGroupSrcListIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListEntry 1 }

mgmtIpmcMvrStatusIgmpGroupSrcListGroupAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv4 multicast address."
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListEntry 2 }

mgmtIpmcMvrStatusIgmpGroupSrcListPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListEntry 3 }

mgmtIpmcMvrStatusIgmpGroupSrcListHostAddress OBJECT-TYPE
    SYNTAX      IpAddress
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv4 source address."
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListEntry 4 }

mgmtIpmcMvrStatusIgmpGroupSrcListGroupFilterMode OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrGroupSrcListGroupFilterMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the source filtering mode of the specific
         registered multicast group address from MVR."
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListEntry 5 }

mgmtIpmcMvrStatusIgmpGroupSrcListFilterTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to count down the timer for the specific multicast group's
         filtering mode transition."
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListEntry 6 }

mgmtIpmcMvrStatusIgmpGroupSrcListSourceType OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrGroupSrcListSourceType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the filtering type of the specific source address
         in multicasting to the registered group address."
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListEntry 7 }

mgmtIpmcMvrStatusIgmpGroupSrcListSourceTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to count down the timer for purging the specific source
         address from the registered multicast group's source list."
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListEntry 8 }

mgmtIpmcMvrStatusIgmpGroupSrcListHardwareFilter OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote whether the traffic destined to the multicast
         group address from the specific source address could be forwarding by
         switch hardware or not."
    ::= { mgmtIpmcMvrStatusIgmpGroupSrcListEntry 9 }

mgmtIpmcMvrStatusMldVlanTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcMvrStatusMldVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the per VLAN interface status in MVR
         from MLD protocol."
    ::= { mgmtIpmcMvrStatus 5 }

mgmtIpmcMvrStatusMldVlanEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrStatusMldVlanEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcMvrStatusMldVlanIfIndex }
    ::= { mgmtIpmcMvrStatusMldVlanTable 1 }

MGMTIpmcMvrStatusMldVlanEntry ::= SEQUENCE {
    mgmtIpmcMvrStatusMldVlanIfIndex                 MGMTInterfaceIndex,
    mgmtIpmcMvrStatusMldVlanQuerierStatus           MGMTIpmcMvrInterfaceQuerierStatus,
    mgmtIpmcMvrStatusMldVlanActiveQuerierAddress    InetAddressIPv6,
    mgmtIpmcMvrStatusMldVlanQuerierUptime           Unsigned32,
    mgmtIpmcMvrStatusMldVlanQueryInterval           Unsigned32,
    mgmtIpmcMvrStatusMldVlanStartupQueryCount       Unsigned32,
    mgmtIpmcMvrStatusMldVlanQuerierExpiryTime       Unsigned32,
    mgmtIpmcMvrStatusMldVlanCounterTxQuery          Unsigned32,
    mgmtIpmcMvrStatusMldVlanCounterTxSpecificQuery  Unsigned32,
    mgmtIpmcMvrStatusMldVlanCounterRxQuery          Unsigned32,
    mgmtIpmcMvrStatusMldVlanCounterRxV1Report       Unsigned32,
    mgmtIpmcMvrStatusMldVlanCounterRxV1Done         Unsigned32,
    mgmtIpmcMvrStatusMldVlanCounterRxV2Report       Unsigned32,
    mgmtIpmcMvrStatusMldVlanCounterRxErrors         Unsigned32
}

mgmtIpmcMvrStatusMldVlanIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 1 }

mgmtIpmcMvrStatusMldVlanQuerierStatus OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrInterfaceQuerierStatus
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The MLD Querier status of the specific VLAN interface."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 2 }

mgmtIpmcMvrStatusMldVlanActiveQuerierAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "The active MLD Querier address on the specific VLAN interface."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 3 }

mgmtIpmcMvrStatusMldVlanQuerierUptime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the in operation timer for the specific interface act as a
         MLD Querier."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 4 }

mgmtIpmcMvrStatusMldVlanQueryInterval OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control MLD protocol stack as stated in
         RFC-3810 9.2."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 5 }

mgmtIpmcMvrStatusMldVlanStartupQueryCount OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control MLD protocol stack as stated in
         RFC-3810 9.7."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 6 }

mgmtIpmcMvrStatusMldVlanQuerierExpiryTime OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "This setting is used to control MLD protocol stack as stated in
         RFC-3810 9.5."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 7 }

mgmtIpmcMvrStatusMldVlanCounterTxQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         transmitting MLD Query control frames."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 8 }

mgmtIpmcMvrStatusMldVlanCounterTxSpecificQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         transmitting MLD Specific Query control frames."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 9 }

mgmtIpmcMvrStatusMldVlanCounterRxQuery OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving MLD Query control frames."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 10 }

mgmtIpmcMvrStatusMldVlanCounterRxV1Report OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving MLDv1 Report control frames."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 11 }

mgmtIpmcMvrStatusMldVlanCounterRxV1Done OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving MLDv1 Done control frames."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 12 }

mgmtIpmcMvrStatusMldVlanCounterRxV2Report OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving MLDv2 Report control frames."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 13 }

mgmtIpmcMvrStatusMldVlanCounterRxErrors OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It presents the current packet count on the specific MLD interface for
         receiving invalid MLD control frames."
    ::= { mgmtIpmcMvrStatusMldVlanEntry 14 }

mgmtIpmcMvrStatusMldGroupAddressTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcMvrStatusMldGroupAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the registered IPv6 multicast group
         address status in MLD from MVR."
    ::= { mgmtIpmcMvrStatus 6 }

mgmtIpmcMvrStatusMldGroupAddressEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrStatusMldGroupAddressEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcMvrStatusMldGroupAddressIfIndex,
                  mgmtIpmcMvrStatusMldGroupAddressGroupAddress }
    ::= { mgmtIpmcMvrStatusMldGroupAddressTable 1 }

MGMTIpmcMvrStatusMldGroupAddressEntry ::= SEQUENCE {
    mgmtIpmcMvrStatusMldGroupAddressIfIndex         MGMTInterfaceIndex,
    mgmtIpmcMvrStatusMldGroupAddressGroupAddress    InetAddressIPv6,
    mgmtIpmcMvrStatusMldGroupAddressMemberPorts     MGMTPortList,
    mgmtIpmcMvrStatusMldGroupAddressHardwareSwitch  TruthValue
}

mgmtIpmcMvrStatusMldGroupAddressIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrStatusMldGroupAddressEntry 1 }

mgmtIpmcMvrStatusMldGroupAddressGroupAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv6 multicast address."
    ::= { mgmtIpmcMvrStatusMldGroupAddressEntry 2 }

mgmtIpmcMvrStatusMldGroupAddressMemberPorts OBJECT-TYPE
    SYNTAX      MGMTPortList
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the memberships of the registered multicast group
         address from MVR."
    ::= { mgmtIpmcMvrStatusMldGroupAddressEntry 3 }

mgmtIpmcMvrStatusMldGroupAddressHardwareSwitch OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote whether the multicast traffic destined to the
         registered group address could be forwarding by switch hardware or not."
    ::= { mgmtIpmcMvrStatusMldGroupAddressEntry 4 }

mgmtIpmcMvrStatusMldGroupSrcListTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTIpmcMvrStatusMldGroupSrcListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table for displaying the address SFM (a.k.a Source List
         Multicast) status in source list of the registered IPv6 multicast group
         in MLD from MVR."
    ::= { mgmtIpmcMvrStatus 7 }

mgmtIpmcMvrStatusMldGroupSrcListEntry OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrStatusMldGroupSrcListEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each entry has a set of parameters."
    INDEX       { mgmtIpmcMvrStatusMldGroupSrcListIfIndex,
                  mgmtIpmcMvrStatusMldGroupSrcListGroupAddress,
                  mgmtIpmcMvrStatusMldGroupSrcListPortIndex,
                  mgmtIpmcMvrStatusMldGroupSrcListHostAddress }
    ::= { mgmtIpmcMvrStatusMldGroupSrcListTable 1 }

MGMTIpmcMvrStatusMldGroupSrcListEntry ::= SEQUENCE {
    mgmtIpmcMvrStatusMldGroupSrcListIfIndex          MGMTInterfaceIndex,
    mgmtIpmcMvrStatusMldGroupSrcListGroupAddress     InetAddressIPv6,
    mgmtIpmcMvrStatusMldGroupSrcListPortIndex        MGMTInterfaceIndex,
    mgmtIpmcMvrStatusMldGroupSrcListHostAddress      InetAddressIPv6,
    mgmtIpmcMvrStatusMldGroupSrcListGroupFilterMode  MGMTIpmcMvrGroupSrcListGroupFilterMode,
    mgmtIpmcMvrStatusMldGroupSrcListFilterTimer      Unsigned32,
    mgmtIpmcMvrStatusMldGroupSrcListSourceType       MGMTIpmcMvrGroupSrcListSourceType,
    mgmtIpmcMvrStatusMldGroupSrcListSourceTimer      Unsigned32,
    mgmtIpmcMvrStatusMldGroupSrcListHardwareFilter   TruthValue
}

mgmtIpmcMvrStatusMldGroupSrcListIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrStatusMldGroupSrcListEntry 1 }

mgmtIpmcMvrStatusMldGroupSrcListGroupAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv6 multicast address."
    ::= { mgmtIpmcMvrStatusMldGroupSrcListEntry 2 }

mgmtIpmcMvrStatusMldGroupSrcListPortIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number of the physical port."
    ::= { mgmtIpmcMvrStatusMldGroupSrcListEntry 3 }

mgmtIpmcMvrStatusMldGroupSrcListHostAddress OBJECT-TYPE
    SYNTAX      InetAddressIPv6
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Assigned IPv6 source address."
    ::= { mgmtIpmcMvrStatusMldGroupSrcListEntry 4 }

mgmtIpmcMvrStatusMldGroupSrcListGroupFilterMode OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrGroupSrcListGroupFilterMode
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the source filtering mode of the specific
         registered multicast group address from MVR."
    ::= { mgmtIpmcMvrStatusMldGroupSrcListEntry 5 }

mgmtIpmcMvrStatusMldGroupSrcListFilterTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to count down the timer for the specific multicast group's
         filtering mode transition."
    ::= { mgmtIpmcMvrStatusMldGroupSrcListEntry 6 }

mgmtIpmcMvrStatusMldGroupSrcListSourceType OBJECT-TYPE
    SYNTAX      MGMTIpmcMvrGroupSrcListSourceType
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote the filtering type of the specific source address
         in multicasting to the registered group address."
    ::= { mgmtIpmcMvrStatusMldGroupSrcListEntry 7 }

mgmtIpmcMvrStatusMldGroupSrcListSourceTimer OBJECT-TYPE
    SYNTAX      Unsigned32
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to count down the timer for purging the specific source
         address from the registered multicast group's source list."
    ::= { mgmtIpmcMvrStatusMldGroupSrcListEntry 8 }

mgmtIpmcMvrStatusMldGroupSrcListHardwareFilter OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "It is used to denote whether the traffic destined to the multicast
         group address from the specific source address could be forwarding by
         switch hardware or not."
    ::= { mgmtIpmcMvrStatusMldGroupSrcListEntry 9 }

mgmtIpmcMvrControl OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrMibObjects 4 }

mgmtIpmcMvrControlStatisticsClear OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrControl 1 }

mgmtIpmcMvrControlStatisticsClearIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Logical interface number of the VLAN interface."
    ::= { mgmtIpmcMvrControlStatisticsClear 1 }

mgmtIpmcMvrMibConformance OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrMib 2 }

mgmtIpmcMvrMibCompliances OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrMibConformance 1 }

mgmtIpmcMvrMibGroups OBJECT IDENTIFIER
    ::= { mgmtIpmcMvrMibConformance 2 }

mgmtIpmcMvrConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrConfigGlobalsAdminState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 1 }

mgmtIpmcMvrConfigPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrConfigPortPortIndex,
                  mgmtIpmcMvrConfigPortDoImmediateLeave }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 2 }

mgmtIpmcMvrConfigInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrConfigInterfaceIfIndex,
                  mgmtIpmcMvrConfigInterfaceName,
                  mgmtIpmcMvrConfigInterfaceIgmpQuerierAddress,
                  mgmtIpmcMvrConfigInterfaceMode,
                  mgmtIpmcMvrConfigInterfaceTagging,
                  mgmtIpmcMvrConfigInterfacePriority,
                  mgmtIpmcMvrConfigInterfaceLastListenerQueryInt,
                  mgmtIpmcMvrConfigInterfaceChannelProfile,
                  mgmtIpmcMvrConfigInterfaceQuerierElection,
                  mgmtIpmcMvrConfigInterfaceAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 3 }

mgmtIpmcMvrConfigInterfaceTableRowEditorInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrConfigInterfaceTableRowEditorIfIndex,
                  mgmtIpmcMvrConfigInterfaceTableRowEditorName,
                  mgmtIpmcMvrConfigInterfaceTableRowEditorIgmpQuerierAddress,
                  mgmtIpmcMvrConfigInterfaceTableRowEditorMode,
                  mgmtIpmcMvrConfigInterfaceTableRowEditorTagging,
                  mgmtIpmcMvrConfigInterfaceTableRowEditorPriority,
                  mgmtIpmcMvrConfigInterfaceTableRowEditorLastListenerQueryInt,
                  mgmtIpmcMvrConfigInterfaceTableRowEditorChannelProfile,
                  mgmtIpmcMvrConfigInterfaceTableRowEditorQuerierElection,
                  mgmtIpmcMvrConfigInterfaceTableRowEditorAction }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 4 }

mgmtIpmcMvrConfigVlanPortTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrConfigVlanPortIfIndex,
                  mgmtIpmcMvrConfigVlanPortPortIndex,
                  mgmtIpmcMvrConfigVlanPortRole }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 5 }

mgmtIpmcMvrStatusGroupAddressCountInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrStatusGroupAddressCountFromIgmp,
                  mgmtIpmcMvrStatusGroupAddressCountFromMld }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 6 }

mgmtIpmcMvrStatusIgmpVlanTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrStatusIgmpVlanIfIndex,
                  mgmtIpmcMvrStatusIgmpVlanQuerierStatus,
                  mgmtIpmcMvrStatusIgmpVlanActiveQuerierAddress,
                  mgmtIpmcMvrStatusIgmpVlanQuerierUptime,
                  mgmtIpmcMvrStatusIgmpVlanQueryInterval,
                  mgmtIpmcMvrStatusIgmpVlanStartupQueryCount,
                  mgmtIpmcMvrStatusIgmpVlanQuerierExpiryTime,
                  mgmtIpmcMvrStatusIgmpVlanCounterTxQuery,
                  mgmtIpmcMvrStatusIgmpVlanCounterTxSpecificQuery,
                  mgmtIpmcMvrStatusIgmpVlanCounterRxQuery,
                  mgmtIpmcMvrStatusIgmpVlanCounterRxV1Join,
                  mgmtIpmcMvrStatusIgmpVlanCounterRxV2Join,
                  mgmtIpmcMvrStatusIgmpVlanCounterRxV2Leave,
                  mgmtIpmcMvrStatusIgmpVlanCounterRxV3Join,
                  mgmtIpmcMvrStatusIgmpVlanCounterRxErrors }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 7 }

mgmtIpmcMvrStatusIgmpGroupAddressTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrStatusIgmpGroupAddressIfIndex,
                  mgmtIpmcMvrStatusIgmpGroupAddressGroupAddress,
                  mgmtIpmcMvrStatusIgmpGroupAddressMemberPorts,
                  mgmtIpmcMvrStatusIgmpGroupAddressHardwareSwitch }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 8 }

mgmtIpmcMvrStatusIgmpGroupSrcListTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrStatusIgmpGroupSrcListIfIndex,
                  mgmtIpmcMvrStatusIgmpGroupSrcListGroupAddress,
                  mgmtIpmcMvrStatusIgmpGroupSrcListPortIndex,
                  mgmtIpmcMvrStatusIgmpGroupSrcListHostAddress,
                  mgmtIpmcMvrStatusIgmpGroupSrcListGroupFilterMode,
                  mgmtIpmcMvrStatusIgmpGroupSrcListFilterTimer,
                  mgmtIpmcMvrStatusIgmpGroupSrcListSourceType,
                  mgmtIpmcMvrStatusIgmpGroupSrcListSourceTimer,
                  mgmtIpmcMvrStatusIgmpGroupSrcListHardwareFilter }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 9 }

mgmtIpmcMvrStatusMldVlanTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrStatusMldVlanIfIndex,
                  mgmtIpmcMvrStatusMldVlanQuerierStatus,
                  mgmtIpmcMvrStatusMldVlanActiveQuerierAddress,
                  mgmtIpmcMvrStatusMldVlanQuerierUptime,
                  mgmtIpmcMvrStatusMldVlanQueryInterval,
                  mgmtIpmcMvrStatusMldVlanStartupQueryCount,
                  mgmtIpmcMvrStatusMldVlanQuerierExpiryTime,
                  mgmtIpmcMvrStatusMldVlanCounterTxQuery,
                  mgmtIpmcMvrStatusMldVlanCounterTxSpecificQuery,
                  mgmtIpmcMvrStatusMldVlanCounterRxQuery,
                  mgmtIpmcMvrStatusMldVlanCounterRxV1Report,
                  mgmtIpmcMvrStatusMldVlanCounterRxV1Done,
                  mgmtIpmcMvrStatusMldVlanCounterRxV2Report,
                  mgmtIpmcMvrStatusMldVlanCounterRxErrors }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 10 }

mgmtIpmcMvrStatusMldGroupAddressTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrStatusMldGroupAddressIfIndex,
                  mgmtIpmcMvrStatusMldGroupAddressGroupAddress,
                  mgmtIpmcMvrStatusMldGroupAddressMemberPorts,
                  mgmtIpmcMvrStatusMldGroupAddressHardwareSwitch }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 11 }

mgmtIpmcMvrStatusMldGroupSrcListTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrStatusMldGroupSrcListIfIndex,
                  mgmtIpmcMvrStatusMldGroupSrcListGroupAddress,
                  mgmtIpmcMvrStatusMldGroupSrcListPortIndex,
                  mgmtIpmcMvrStatusMldGroupSrcListHostAddress,
                  mgmtIpmcMvrStatusMldGroupSrcListGroupFilterMode,
                  mgmtIpmcMvrStatusMldGroupSrcListFilterTimer,
                  mgmtIpmcMvrStatusMldGroupSrcListSourceType,
                  mgmtIpmcMvrStatusMldGroupSrcListSourceTimer,
                  mgmtIpmcMvrStatusMldGroupSrcListHardwareFilter }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 12 }

mgmtIpmcMvrControlStatisticsClearInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtIpmcMvrControlStatisticsClearIfIndex }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtIpmcMvrMibGroups 13 }

mgmtIpmcMvrMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtIpmcMvrConfigGlobalsInfoGroup,
                       mgmtIpmcMvrConfigPortTableInfoGroup,
                       mgmtIpmcMvrConfigInterfaceTableInfoGroup,
                       mgmtIpmcMvrConfigInterfaceTableRowEditorInfoGroup,
                       mgmtIpmcMvrConfigVlanPortTableInfoGroup,
                       mgmtIpmcMvrStatusGroupAddressCountInfoGroup,
                       mgmtIpmcMvrStatusIgmpVlanTableInfoGroup,
                       mgmtIpmcMvrStatusIgmpGroupAddressTableInfoGroup,
                       mgmtIpmcMvrStatusIgmpGroupSrcListTableInfoGroup,
                       mgmtIpmcMvrStatusMldVlanTableInfoGroup,
                       mgmtIpmcMvrStatusMldGroupAddressTableInfoGroup,
                       mgmtIpmcMvrStatusMldGroupSrcListTableInfoGroup,
                       mgmtIpmcMvrControlStatisticsClearInfoGroup }

    ::= { mgmtIpmcMvrMibCompliances 1 }

END
