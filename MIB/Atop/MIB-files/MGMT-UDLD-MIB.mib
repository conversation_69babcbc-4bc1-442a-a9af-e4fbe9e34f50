-- *****************************************************************
-- UDLD-MIB:  
-- ****************************************************************

MGMT-UDLD-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Unsigned32 FROM SNMPv2-SMI
    MGMTDisplayString FROM MGMT-TC
    MGMTInterfaceIndex FROM MGMT-TC
    ;

mgmtUdldMib MODULE-IDENTITY
    LAST-UPDATED "201407010000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "This is a private MIB for udld"
    REVISION    "201407010000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 123 }


MGMTUdldDetectionState ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the link detection state."
    SYNTAX      INTEGER { inDeterminant(0), uniDirectional(1),
                          biDirectional(2), neighborMismatch(3),
                          loopback(4), multipleNeighbor(5) }

MGMTUdldMode ::= TEXTUAL-CONVENTION
    STATUS      current
    DESCRIPTION
        "This enumeration defines the available udld mode."
    SYNTAX      INTEGER { disable(0), normal(1), aggressive(2) }

mgmtUdldMibObjects OBJECT IDENTIFIER
    ::= { mgmtUdldMib 1 }

mgmtUdldConfig OBJECT IDENTIFIER
    ::= { mgmtUdldMibObjects 2 }

mgmtUdldConfigInterface OBJECT IDENTIFIER
    ::= { mgmtUdldConfig 1 }

mgmtUdldConfigInterfaceParamTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTUdldConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of udld interface conf parameters"
    ::= { mgmtUdldConfigInterface 1 }

mgmtUdldConfigInterfaceParamEntry OBJECT-TYPE
    SYNTAX      MGMTUdldConfigInterfaceParamEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each physical interface has a set of configurable parameters"
    INDEX       { mgmtUdldConfigInterfaceParamIfIndex }
    ::= { mgmtUdldConfigInterfaceParamTable 1 }

MGMTUdldConfigInterfaceParamEntry ::= SEQUENCE {
    mgmtUdldConfigInterfaceParamIfIndex           MGMTInterfaceIndex,
    mgmtUdldConfigInterfaceParamUdldMode          MGMTUdldMode,
    mgmtUdldConfigInterfaceParamProbeMsgInterval  Unsigned32
}

mgmtUdldConfigInterfaceParamIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtUdldConfigInterfaceParamEntry 1 }

mgmtUdldConfigInterfaceParamUdldMode OBJECT-TYPE
    SYNTAX      MGMTUdldMode
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Port udld mode disable/normal/aggresive."
    ::= { mgmtUdldConfigInterfaceParamEntry 2 }

mgmtUdldConfigInterfaceParamProbeMsgInterval OBJECT-TYPE
    SYNTAX      Unsigned32 (7..90)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Port probe message interval(seconds). Valid range: 7 to 90 seconds."
    ::= { mgmtUdldConfigInterfaceParamEntry 3 }

mgmtUdldStatus OBJECT IDENTIFIER
    ::= { mgmtUdldMibObjects 3 }

mgmtUdldStatusInterface OBJECT IDENTIFIER
    ::= { mgmtUdldStatus 1 }

mgmtUdldStatusInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTUdldStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of udld interface local device information"
    ::= { mgmtUdldStatusInterface 1 }

mgmtUdldStatusInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTUdldStatusInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each udld enabled interface has a local device information"
    INDEX       { mgmtUdldStatusInterfaceIfIndex }
    ::= { mgmtUdldStatusInterfaceTable 1 }

MGMTUdldStatusInterfaceEntry ::= SEQUENCE {
    mgmtUdldStatusInterfaceIfIndex     MGMTInterfaceIndex,
    mgmtUdldStatusInterfaceDeviceID    MGMTDisplayString,
    mgmtUdldStatusInterfaceDeviceName  MGMTDisplayString,
    mgmtUdldStatusInterfaceLinkState   MGMTUdldDetectionState
}

mgmtUdldStatusInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtUdldStatusInterfaceEntry 1 }

mgmtUdldStatusInterfaceDeviceID OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Local device id."
    ::= { mgmtUdldStatusInterfaceEntry 2 }

mgmtUdldStatusInterfaceDeviceName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Local device name."
    ::= { mgmtUdldStatusInterfaceEntry 3 }

mgmtUdldStatusInterfaceLinkState OBJECT-TYPE
    SYNTAX      MGMTUdldDetectionState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Local device link detected state."
    ::= { mgmtUdldStatusInterfaceEntry 4 }

mgmtUdldStatusInterfaceNeighborTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTUdldStatusInterfaceNeighborEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of udld interface neighbor cache information"
    ::= { mgmtUdldStatusInterface 2 }

mgmtUdldStatusInterfaceNeighborEntry OBJECT-TYPE
    SYNTAX      MGMTUdldStatusInterfaceNeighborEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each udld enabled interface has a neighbor cache information"
    INDEX       { mgmtUdldStatusInterfaceNeighborIfIndex }
    ::= { mgmtUdldStatusInterfaceNeighborTable 1 }

MGMTUdldStatusInterfaceNeighborEntry ::= SEQUENCE {
    mgmtUdldStatusInterfaceNeighborIfIndex             MGMTInterfaceIndex,
    mgmtUdldStatusInterfaceNeighborNeighborDeviceID    MGMTDisplayString,
    mgmtUdldStatusInterfaceNeighborNeighborPortID      MGMTDisplayString,
    mgmtUdldStatusInterfaceNeighborNeighborDeviceName  MGMTDisplayString,
    mgmtUdldStatusInterfaceNeighborLinkDetectionState  MGMTUdldDetectionState
}

mgmtUdldStatusInterfaceNeighborIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "Logical interface number."
    ::= { mgmtUdldStatusInterfaceNeighborEntry 1 }

mgmtUdldStatusInterfaceNeighborNeighborDeviceID OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Neighbor device id."
    ::= { mgmtUdldStatusInterfaceNeighborEntry 2 }

mgmtUdldStatusInterfaceNeighborNeighborPortID OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Neighbor port id."
    ::= { mgmtUdldStatusInterfaceNeighborEntry 3 }

mgmtUdldStatusInterfaceNeighborNeighborDeviceName OBJECT-TYPE
    SYNTAX      MGMTDisplayString (SIZE(0..254))
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Neighbor device name."
    ::= { mgmtUdldStatusInterfaceNeighborEntry 4 }

mgmtUdldStatusInterfaceNeighborLinkDetectionState OBJECT-TYPE
    SYNTAX      MGMTUdldDetectionState
    MAX-ACCESS  read-only
    STATUS      current
    DESCRIPTION
        "Neighbor device link detected state."
    ::= { mgmtUdldStatusInterfaceNeighborEntry 5 }

mgmtUdldMibConformance OBJECT IDENTIFIER
    ::= { mgmtUdldMib 2 }

mgmtUdldMibCompliances OBJECT IDENTIFIER
    ::= { mgmtUdldMibConformance 1 }

mgmtUdldMibGroups OBJECT IDENTIFIER
    ::= { mgmtUdldMibConformance 2 }

mgmtUdldConfigInterfaceParamTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtUdldConfigInterfaceParamIfIndex,
                  mgmtUdldConfigInterfaceParamUdldMode,
                  mgmtUdldConfigInterfaceParamProbeMsgInterval }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtUdldMibGroups 1 }

mgmtUdldStatusInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtUdldStatusInterfaceIfIndex,
                  mgmtUdldStatusInterfaceDeviceID,
                  mgmtUdldStatusInterfaceDeviceName,
                  mgmtUdldStatusInterfaceLinkState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtUdldMibGroups 2 }

mgmtUdldStatusInterfaceNeighborTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtUdldStatusInterfaceNeighborIfIndex,
                  mgmtUdldStatusInterfaceNeighborNeighborDeviceID,
                  mgmtUdldStatusInterfaceNeighborNeighborPortID,
                  mgmtUdldStatusInterfaceNeighborNeighborDeviceName,
                  mgmtUdldStatusInterfaceNeighborLinkDetectionState }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtUdldMibGroups 3 }

mgmtUdldMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtUdldConfigInterfaceParamTableInfoGroup,
                       mgmtUdldStatusInterfaceTableInfoGroup,
                       mgmtUdldStatusInterfaceNeighborTableInfoGroup }

    ::= { mgmtUdldMibCompliances 1 }

END
