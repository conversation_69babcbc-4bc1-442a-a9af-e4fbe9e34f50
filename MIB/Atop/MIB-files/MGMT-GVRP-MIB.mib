-- *****************************************************************
-- GVRP-MIB:  
-- ****************************************************************

MGMT-GVRP-MIB DEFINITIONS ::= BEGIN

IMPORTS
    NOTIFICATION-GROUP, MODULE-COMPLIANCE, OBJECT-GROUP FROM SNMPv2-CONF
    NOTIFICATION-TYPE, MODULE-IDENTITY, OBJECT-TYPE FROM SNMPv2-SMI
    TEXTUAL-CONVENTION FROM SNMPv2-TC
    mgmtSwitch FROM MGMT-SMI
    Integer32 FROM SNMPv2-SMI
    TruthValue FROM SNMPv2-TC
    MGMTInterfaceIndex FROM MGMT-TC
    ;

mgmtGvrpMib MODULE-IDENTITY
    LAST-UPDATED "201703130000Z"
    ORGANIZATION
        ""
    CONTACT-INFO
        ""
    DESCRIPTION
        "Private GVRP MIB."
    REVISION    "201703130000Z"
    DESCRIPTION
        "Updated the description of GVRP Max VLANs parameter."
    REVISION    "201510220000Z"
    DESCRIPTION
        "Fixed a typo and updated the description of GVRP Max VLANs value."
    REVISION    "201411110000Z"
    DESCRIPTION
        "Initial version"
    ::= { mgmtSwitch 89 }


mgmtGvrpMibObjects OBJECT IDENTIFIER
    ::= { mgmtGvrpMib 1 }

mgmtGvrpConfig OBJECT IDENTIFIER
    ::= { mgmtGvrpMibObjects 2 }

mgmtGvrpConfigGlobals OBJECT IDENTIFIER
    ::= { mgmtGvrpConfig 1 }

mgmtGvrpConfigGlobalsMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Global enabling of GVRP protocol. TRUE - enable GVRP, FALSE - disable
         GVRP."
    ::= { mgmtGvrpConfigGlobals 1 }

mgmtGvrpConfigGlobalsJoinTime OBJECT-TYPE
    SYNTAX      Integer32 (1..20)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Join-time protocol parameter. Range [1,20] centi seconds."
    ::= { mgmtGvrpConfigGlobals 2 }

mgmtGvrpConfigGlobalsLeaveTime OBJECT-TYPE
    SYNTAX      Integer32 (60..300)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Leave-time protocol parameter. Range [60,300] centi seconds."
    ::= { mgmtGvrpConfigGlobals 3 }

mgmtGvrpConfigGlobalsLeaveAllTime OBJECT-TYPE
    SYNTAX      Integer32 (1000..5000)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Leave-all-time protocol parameter. Range [1000,5000] centi seconds."
    ::= { mgmtGvrpConfigGlobals 4 }

mgmtGvrpConfigGlobalsMaxVlans OBJECT-TYPE
    SYNTAX      Integer32 (1..4094)
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Maximum number of VLANs simultaneously supported by GVRP. Range is
         [1,4094]. GVRP must be disabled in order to change this parameter."
    ::= { mgmtGvrpConfigGlobals 5 }

mgmtGvrpConfigInterface OBJECT IDENTIFIER
    ::= { mgmtGvrpConfig 2 }

mgmtGvrpConfigInterfaceTable OBJECT-TYPE
    SYNTAX      SEQUENCE OF MGMTGvrpConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "This is a table of interface configuration"
    ::= { mgmtGvrpConfigInterface 1 }

mgmtGvrpConfigInterfaceEntry OBJECT-TYPE
    SYNTAX      MGMTGvrpConfigInterfaceEntry
    MAX-ACCESS  not-accessible
    STATUS      current
    DESCRIPTION
        "Each interface has a set of parameters"
    INDEX       { mgmtGvrpConfigInterfaceIfIndex }
    ::= { mgmtGvrpConfigInterfaceTable 1 }

MGMTGvrpConfigInterfaceEntry ::= SEQUENCE {
    mgmtGvrpConfigInterfaceIfIndex  MGMTInterfaceIndex,
    mgmtGvrpConfigInterfaceMode     TruthValue
}

mgmtGvrpConfigInterfaceIfIndex OBJECT-TYPE
    SYNTAX      MGMTInterfaceIndex
    MAX-ACCESS  accessible-for-notify
    STATUS      current
    DESCRIPTION
        "The index of logical interface."
    ::= { mgmtGvrpConfigInterfaceEntry 1 }

mgmtGvrpConfigInterfaceMode OBJECT-TYPE
    SYNTAX      TruthValue
    MAX-ACCESS  read-write
    STATUS      current
    DESCRIPTION
        "Per-port mode of GVRP. TRUE - enable GVRP on the port, FALSE - disable
         GVRP on the port."
    ::= { mgmtGvrpConfigInterfaceEntry 2 }

mgmtGvrpMibConformance OBJECT IDENTIFIER
    ::= { mgmtGvrpMib 2 }

mgmtGvrpMibCompliances OBJECT IDENTIFIER
    ::= { mgmtGvrpMibConformance 1 }

mgmtGvrpMibGroups OBJECT IDENTIFIER
    ::= { mgmtGvrpMibConformance 2 }

mgmtGvrpConfigGlobalsInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtGvrpConfigGlobalsMode,
                  mgmtGvrpConfigGlobalsJoinTime,
                  mgmtGvrpConfigGlobalsLeaveTime,
                  mgmtGvrpConfigGlobalsLeaveAllTime,
                  mgmtGvrpConfigGlobalsMaxVlans }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtGvrpMibGroups 1 }

mgmtGvrpConfigInterfaceTableInfoGroup OBJECT-GROUP
    OBJECTS     { mgmtGvrpConfigInterfaceIfIndex,
                  mgmtGvrpConfigInterfaceMode }
    STATUS      current
    DESCRIPTION
        "A collection of objects."
    ::= { mgmtGvrpMibGroups 2 }

mgmtGvrpMibCompliance MODULE-COMPLIANCE
    STATUS      current
    DESCRIPTION
        "The compliance statement for the implementation."

    MODULE      -- this module

    MANDATORY-GROUPS { mgmtGvrpConfigGlobalsInfoGroup,
                       mgmtGvrpConfigInterfaceTableInfoGroup }

    ::= { mgmtGvrpMibCompliances 1 }

END
