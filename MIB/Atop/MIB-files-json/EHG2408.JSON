{"RFC1155-SMI": {"internet": {"ObjectName": "internet", "ModuleName": "RFC1155-SMI", "OBJECT_IDENTIFIER": "iso org(3) dod(6) 1", "OID": "*******.", "NameSpace": "iso.org.dod.internet"}, "directory": {"ObjectName": "directory", "ModuleName": "RFC1155-SMI", "OBJECT_IDENTIFIER": "internet 1", "OID": "*******.1", "NameSpace": "iso.org.dod.internet.directory"}, "mgmt": {"ObjectName": "mgmt", "ModuleName": "RFC1155-SMI", "OBJECT_IDENTIFIER": "internet 2", "OID": "*******.2", "NameSpace": "iso.org.dod.internet.mgmt"}, "experimental": {"ObjectName": "experimental", "ModuleName": "RFC1155-SMI", "OBJECT_IDENTIFIER": "internet 3", "OID": "*******.3", "NameSpace": "iso.org.dod.internet.experimental"}, "private": {"ObjectName": "private", "ModuleName": "RFC1155-SMI", "OBJECT_IDENTIFIER": "internet 4", "OID": "*******.4", "NameSpace": "iso.org.dod.internet.private"}, "enterprises": {"ObjectName": "enterprises", "ModuleName": "RFC1155-SMI", "OBJECT_IDENTIFIER": "private 1", "OID": "*******.4.1", "NameSpace": "iso.org.dod.internet.private.enterprises"}, "OBJECT-TYPE": {"TYPE NOTATION": {"SYNTAX": "(TYPE ObjectSyntax)", "ACCESS": "Access", "STATUS": "Status"}, "VALUE NOTATION": {"value": "(VALUE ObjectName)"}, "Access": {"ObjectName": "Access", "ModuleName": "RFC1155-SMI", "MACRO": "\"read-only\""}, "Status": {"ObjectName": "Status", "ModuleName": "RFC1155-SMI", "MACRO": "\"mandatory\""}}, "ObjectName": {"ObjectName": "ObjectName", "ModuleName": "RFC1155-SMI", "MACRO": "OBJECT_IDENTIFIER"}, "ObjectSyntax": {"ObjectName": "ObjectSyntax", "ModuleName": "RFC1155-SMI", "MACRO": "CHOICE"}, "SimpleSyntax": {"ObjectName": "SimpleSyntax", "ModuleName": "RFC1155-SMI", "MACRO": "CHOICE"}, "ApplicationSyntax": {"ObjectName": "ApplicationSyntax", "ModuleName": "RFC1155-SMI", "MACRO": "CHOICE"}, "NetworkAddress": {"ObjectName": "NetworkAddress", "ModuleName": "RFC1155-SMI", "MACRO": "CHOICE"}, "IpAddress": {"ObjectName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModuleName": "RFC1155-SMI", "MACRO": "[APPLICATION 0]"}, "Counter": {"ObjectName": "Counter", "ModuleName": "RFC1155-SMI", "MACRO": "[APPLICATION 1]"}, "Gauge": {"ObjectName": "Gauge", "ModuleName": "RFC1155-SMI", "MACRO": "[APPLICATION 2]"}, "TimeTicks": {"ObjectName": "TimeTicks", "ModuleName": "RFC1155-SMI", "MACRO": "[APPLICATION 3]"}, "Opaque": {"ObjectName": "Opaque", "ModuleName": "RFC1155-SMI", "MACRO": "[APPLICATION 4]"}}, "RFC1158-MIB": {"IMPORTS": {"RFC1155-SMI": ["mgmt", "OBJECT-TYPE", "NetworkAddress", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Counter", "Gauge", "TimeTicks"]}, "DisplayString": {"ObjectName": "DisplayString", "ModuleName": "RFC1158-MIB", "MACRO": "OCTET STRING"}, "mib-2": {"ObjectName": "mib-2", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mgmt 1", "OID": "*******.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2"}, "system": {"ObjectName": "system", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 1", "OID": "*******.2.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system"}, "interfaces": {"ObjectName": "interfaces", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 2", "OID": "*******.2.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces"}, "at": {"ObjectName": "at", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 3", "OID": "*******.2.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at"}, "ip": {"ObjectName": "ip", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 4", "OID": "*******.2.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip"}, "icmp": {"ObjectName": "icmp", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 5", "OID": "*******.2.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp"}, "tcp": {"ObjectName": "tcp", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 6", "OID": "*******.2.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp"}, "udp": {"ObjectName": "udp", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 7", "OID": "*******.2.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp"}, "egp": {"ObjectName": "egp", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 8", "OID": "*******.2.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp"}, "transmission": {"ObjectName": "transmission", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 10", "OID": "*******.2.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.transmission"}, "snmp": {"ObjectName": "snmp", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "mib-2 11", "OID": "*******.2.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp"}, "sysDescr": {"ObjectName": "sysDescr", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysDescr"}, "sysObjectID": {"ObjectName": "sysObjectID", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysObjectID"}, "sysUpTime": {"ObjectName": "sysUpTime", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 3", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysUpTime"}, "sysContact": {"ObjectName": "sysContact", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 4", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysContact"}, "sysName": {"ObjectName": "sysName", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 5", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysName"}, "sysLocation": {"ObjectName": "sysLocation", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 6", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysLocation"}, "sysServices": {"ObjectName": "sysServices", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 7", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysServices"}, "ifNumber": {"ObjectName": "ifNumber", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "interfaces 1", "OID": "*******.2.1.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifNumber"}, "ifTable": {"ObjectName": "ifTable", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IfEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "interfaces 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable"}, "ifEntry": {"ObjectName": "ifEntry", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IfEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifTable 1", "OID": "*******.*******.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry"}, "IfEntry": {"ObjectName": "IfEntry", "ModuleName": "RFC1158-MIB", "MACRO": "SEQUENCE"}, "ifIndex": {"ObjectName": "ifIndex", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 1", "OID": "*******.*******.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifIndex"}, "ifDescr": {"ObjectName": "ifDescr", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 2", "OID": "*******.*******.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifDescr"}, "ifType": {"ObjectName": "ifType", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "regular1822", "3": "hdh1822", "4": "ddn-x25", "5": "rfc877-x25", "6": "ethernet-csmacd", "7": "iso88023-csmacd", "8": "iso88024-tokenBus", "9": "iso88025-tokenRing", "10": "iso88026-man", "11": "star<PERSON>an", "12": "proteon-10Mbit", "13": "proteon-80Mbit", "14": "hyperchannel", "15": "fddi", "16": "lapb", "17": "sdlc", "18": "t1-carrier", "19": "cept", "20": "basicISDN", "21": "primaryISDN", "22": "propPointToPointSerial", "23": "terminalServer-asyncPort", "24": "softwareLoopback", "25": "eon", "26": "ethernet-3Mbit", "27": "nsip", "28": "slip"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 3", "OID": "*******.*******.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifType"}, "ifMtu": {"ObjectName": "ifMtu", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 4", "OID": "*******.*******.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifMtu"}, "ifSpeed": {"ObjectName": "ifSpeed", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 5", "OID": "*******.*******.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifSpeed"}, "ifPhysAddress": {"ObjectName": "if<PERSON><PERSON>s<PERSON><PERSON><PERSON>", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 6", "OID": "*******.*******.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifPhysAddress"}, "ifAdminStatus": {"ObjectName": "ifAdminStatus", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "up", "2": "down", "3": "testing"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 7", "OID": "*******.*******.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifAdminStatus"}, "ifOperStatus": {"ObjectName": "ifOperStatus", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "up", "2": "down", "3": "testing"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 8", "OID": "*******.*******.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOperStatus"}, "ifLastChange": {"ObjectName": "ifLastChange", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 9", "OID": "*******.*******.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifLastChange"}, "ifInOctets": {"ObjectName": "ifInOctets", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 10", "OID": "*******.*******.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInOctets"}, "ifInUcastPkts": {"ObjectName": "ifInUcastPkts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 11", "OID": "*******.*******.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInUcastPkts"}, "ifInNUcastPkts": {"ObjectName": "ifInNUcastPkts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 12", "OID": "*******.*******.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInNUcastPkts"}, "ifInDiscards": {"ObjectName": "ifInDiscards", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 13", "OID": "*******.*******.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInDiscards"}, "ifInErrors": {"ObjectName": "ifInErrors", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 14", "OID": "*******.*******.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInErrors"}, "ifInUnknownProtos": {"ObjectName": "ifInUnknownProtos", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 15", "OID": "*******.*******.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInUnknownProtos"}, "ifOutOctets": {"ObjectName": "ifOutOctets", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 16", "OID": "*******.*******.1.16", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutOctets"}, "ifOutUcastPkts": {"ObjectName": "ifOutUcastPkts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 17", "OID": "*******.*******.1.17", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutUcastPkts"}, "ifOutNUcastPkts": {"ObjectName": "ifOutNUcastPkts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 18", "OID": "*******.*******.1.18", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutNUcastPkts"}, "ifOutDiscards": {"ObjectName": "ifOutDiscards", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 19", "OID": "*******.*******.1.19", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutDiscards"}, "ifOutErrors": {"ObjectName": "ifOutErrors", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 20", "OID": "*******.*******.1.20", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutErrors"}, "ifOutQLen": {"ObjectName": "ifOutQLen", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 21", "OID": "*******.*******.1.21", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutQLen"}, "ifSpecific": {"ObjectName": "ifSpecific", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 22", "OID": "*******.*******.1.22", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifSpecific"}, "nullSpecific": {"ObjectName": "nullSpecific", "ModuleName": "RFC1158-MIB", "OBJECT_IDENTIFIER": "0 0", "OID": "0.0", "NameSpace": "null"}, "atTable": {"ObjectName": "atTable", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF AtEntry", "ACCESS": "read-write", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "at 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable"}, "atEntry": {"ObjectName": "atEntry", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "AtEntry", "ACCESS": "read-write", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "atTable 1", "OID": "*******.*******.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable.atEntry"}, "AtEntry": {"ObjectName": "AtEntry", "ModuleName": "RFC1158-MIB", "MACRO": "SEQUENCE"}, "atIfIndex": {"ObjectName": "atIfIndex", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "atEntry 1", "OID": "*******.*******.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable.atEntry.atIfIndex"}, "atPhysAddress": {"ObjectName": "atPhys<PERSON><PERSON><PERSON>", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "ACCESS": "read-write", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "atEntry 2", "OID": "*******.*******.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable.atEntry.atPhysAddress"}, "atNetAddress": {"ObjectName": "atNetAddress", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "NetworkAddress", "ACCESS": "read-write", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "atEntry 3", "OID": "*******.*******.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable.atEntry.atNetAddress"}, "ipForwarding": {"ObjectName": "ipForwarding", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "gateway", "2": "host"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForwarding"}, "ipDefaultTTL": {"ObjectName": "ipDefaultTTL", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipDefaultTTL"}, "ipInReceives": {"ObjectName": "ipInReceives", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 3", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInReceives"}, "ipInHdrErrors": {"ObjectName": "ipInHdrErrors", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 4", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInHdrErrors"}, "ipInAddrErrors": {"ObjectName": "ipInAddrErrors", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 5", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInAddrErrors"}, "ipForwDatagrams": {"ObjectName": "ipForwDatagrams", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 6", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForwDatagrams"}, "ipInUnknownProtos": {"ObjectName": "ipInUnknownProtos", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 7", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInUnknownProtos"}, "ipInDiscards": {"ObjectName": "ipInDiscards", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 8", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInDiscards"}, "ipInDelivers": {"ObjectName": "ipInDelivers", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 9", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInDelivers"}, "ipOutRequests": {"ObjectName": "ipOutRequests", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 10", "OID": "*******.*******0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipOutRequests"}, "ipOutDiscards": {"ObjectName": "ipOutDiscards", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 11", "OID": "*******.*******1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipOutDiscards"}, "ipOutNoRoutes": {"ObjectName": "ipOutNoRoutes", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 12", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipOutNoRoutes"}, "ipReasmTimeout": {"ObjectName": "ipReasmTimeout", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 13", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipReasmTimeout"}, "ipReasmReqds": {"ObjectName": "ipReasmReqds", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 14", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipReasmReqds"}, "ipReasmOKs": {"ObjectName": "ipReasmOKs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 15", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipReasmOKs"}, "ipReasmFails": {"ObjectName": "ipReasmFails", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 16", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipReasmFails"}, "ipFragOKs": {"ObjectName": "ipFragOKs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 17", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipFragOKs"}, "ipFragFails": {"ObjectName": "ipFragFails", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 18", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipFragFails"}, "ipFragCreates": {"ObjectName": "ipFragCreates", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 19", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipFragCreates"}, "ipAddrTable": {"ObjectName": "ipAddrTable", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IpAddrEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 20", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable"}, "ipAddrEntry": {"ObjectName": "ipAddrEntry", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IpAddrEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry"}, "IpAddrEntry": {"ObjectName": "IpAddrEntry", "ModuleName": "RFC1158-MIB", "MACRO": "SEQUENCE"}, "ipAdEntAddr": {"ObjectName": "ipAdEntAddr", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntAddr"}, "ipAdEntIfIndex": {"ObjectName": "ipAdEntIfIndex", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntIfIndex"}, "ipAdEntNetMask": {"ObjectName": "ipAdEntNetMask", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntNetMask"}, "ipAdEntBcastAddr": {"ObjectName": "ipAdEntBcastAddr", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntBcastAddr"}, "ipAdEntReasmMaxSize": {"ObjectName": "ipAdEntReasmMaxSize", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntReasmMaxSize"}, "ipRoutingTable": {"ObjectName": "ipRoutingTable", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IpRouteEntry", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 21", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable"}, "ipRouteEntry": {"ObjectName": "ipRouteEntry", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IpRouteEntry", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRoutingTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry"}, "IpRouteEntry": {"ObjectName": "IpRouteEntry", "ModuleName": "RFC1158-MIB", "MACRO": "SEQUENCE"}, "ipRouteDest": {"ObjectName": "ipRouteDest", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteDest"}, "ipRouteIfIndex": {"ObjectName": "ipRouteIfIndex", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteIfIndex"}, "ipRouteMetric1": {"ObjectName": "ipRouteMetric1", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMetric1"}, "ipRouteMetric2": {"ObjectName": "ipRouteMetric2", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMetric2"}, "ipRouteMetric3": {"ObjectName": "ipRouteMetric3", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMetric3"}, "ipRouteMetric4": {"ObjectName": "ipRouteMetric4", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 6", "OID": "*******.********.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMetric4"}, "ipRouteNextHop": {"ObjectName": "ipRouteNextHop", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 7", "OID": "*******.********.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteNextHop"}, "ipRouteType": {"ObjectName": "ipRouteType", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "invalid", "3": "direct", "4": "remote"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 8", "OID": "*******.********.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteType"}, "ipRouteProto": {"ObjectName": "ipRouteProto", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "local", "3": "netmgmt", "4": "icmp", "5": "egp", "6": "ggp", "7": "hello", "8": "rip", "9": "is-is", "10": "es-is", "11": "ciscoIgrp", "12": "bbnSpfIgp", "13": "ospf", "14": "bgp"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 9", "OID": "*******.********.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteProto"}, "ipRouteAge": {"ObjectName": "ipRouteAge", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 10", "OID": "*******.********.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteAge"}, "ipRouteMask": {"ObjectName": "ipRouteMask", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 11", "OID": "*******.********.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMask"}, "ipNetToMediaTable": {"ObjectName": "ipNetToMediaTable", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IpNetToMediaEntry", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 22", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable"}, "ipNetToMediaEntry": {"ObjectName": "ipNetToMediaEntry", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IpNetToMediaEntry", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry"}, "IpNetToMediaEntry": {"ObjectName": "IpNetToMediaEntry", "ModuleName": "RFC1158-MIB", "MACRO": "SEQUENCE"}, "ipNetToMediaIfIndex": {"ObjectName": "ipNetToMediaIfIndex", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry.ipNetToMediaIfIndex"}, "ipNetToMediaPhysAddress": {"ObjectName": "ipNetToMediaPhysAddress", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry.ipNetToMediaPhysAddress"}, "ipNetToMediaNetAddress": {"ObjectName": "ipNetToMediaNetAddress", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry.ipNetToMediaNetAddress"}, "ipNetToMediaType": {"ObjectName": "ipNetToMediaType", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "invalid", "3": "dynamic", "4": "static"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry.ipNetToMediaType"}, "icmpInMsgs": {"ObjectName": "icmpInMsgs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInMsgs"}, "icmpInErrors": {"ObjectName": "icmpInErrors", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInErrors"}, "icmpInDestUnreachs": {"ObjectName": "icmpInDestUnreachs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 3", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInDestUnreachs"}, "icmpInTimeExcds": {"ObjectName": "icmpInTimeExcds", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 4", "OID": "*******.2.1.5.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInTimeExcds"}, "icmpInParmProbs": {"ObjectName": "icmpInParmProbs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 5", "OID": "*******.2.1.5.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInParmProbs"}, "icmpInSrcQuenchs": {"ObjectName": "icmpInSrcQuenchs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 6", "OID": "*******.2.1.5.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInSrcQuenchs"}, "icmpInRedirects": {"ObjectName": "icmpInRedirects", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 7", "OID": "*******.2.1.5.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInRedirects"}, "icmpInEchos": {"ObjectName": "icmpInEchos", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 8", "OID": "*******.2.1.5.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInEchos"}, "icmpInEchoReps": {"ObjectName": "icmpInEchoReps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 9", "OID": "*******.2.1.5.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInEchoReps"}, "icmpInTimestamps": {"ObjectName": "icmpInTimestamps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 10", "OID": "*******.*******0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInTimestamps"}, "icmpInTimestampReps": {"ObjectName": "icmpInTimestampReps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 11", "OID": "*******.*******1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInTimestampReps"}, "icmpInAddrMasks": {"ObjectName": "icmpInAddrMasks", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 12", "OID": "*******.*******2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInAddrMasks"}, "icmpInAddrMaskReps": {"ObjectName": "icmpInAddrMaskReps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 13", "OID": "*******.*******3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInAddrMaskReps"}, "icmpOutMsgs": {"ObjectName": "icmpOutMsgs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 14", "OID": "*******.*******4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutMsgs"}, "icmpOutErrors": {"ObjectName": "icmpOutErrors", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 15", "OID": "*******.*******5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutErrors"}, "icmpOutDestUnreachs": {"ObjectName": "icmpOutDestUnreachs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 16", "OID": "*******.*******6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutDestUnreachs"}, "icmpOutTimeExcds": {"ObjectName": "icmpOutTimeExcds", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 17", "OID": "*******.*******7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutTimeExcds"}, "icmpOutParmProbs": {"ObjectName": "icmpOutParmProbs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 18", "OID": "*******.*******8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutParmProbs"}, "icmpOutSrcQuenchs": {"ObjectName": "icmpOutSrcQuenchs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 19", "OID": "*******.*******9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutSrcQuenchs"}, "icmpOutRedirects": {"ObjectName": "icmpOutRedirects", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 20", "OID": "*******.*******0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutRedirects"}, "icmpOutEchos": {"ObjectName": "icmpOutEchos", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 21", "OID": "*******.*******1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutEchos"}, "icmpOutEchoReps": {"ObjectName": "icmpOutEchoReps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 22", "OID": "*******.*******2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutEchoReps"}, "icmpOutTimestamps": {"ObjectName": "icmpOutTimestamps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 23", "OID": "*******.*******3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutTimestamps"}, "icmpOutTimestampReps": {"ObjectName": "icmpOutTimestampReps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 24", "OID": "*******.*******4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutTimestampReps"}, "icmpOutAddrMasks": {"ObjectName": "icmpOutAddrMasks", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 25", "OID": "*******.*******5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutAddrMasks"}, "icmpOutAddrMaskReps": {"ObjectName": "icmpOutAddrMaskReps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 26", "OID": "*******.*******6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutAddrMaskReps"}, "tcpRtoAlgorithm": {"ObjectName": "tcpRtoAlgorithm", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "constant", "3": "rsre", "4": "vanj"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 1", "OID": "*******.2.1.6.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpRtoAlgorithm"}, "tcpRtoMin": {"ObjectName": "tcpRtoMin", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 2", "OID": "*******.2.1.6.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpRtoMin"}, "tcpRtoMax": {"ObjectName": "tcpRtoMax", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 3", "OID": "*******.2.1.6.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpRtoMax"}, "tcpMaxConn": {"ObjectName": "tcpMaxConn", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 4", "OID": "*******.2.1.6.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpMaxConn"}, "tcpActiveOpens": {"ObjectName": "tcpActiveOpens", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 5", "OID": "*******.2.1.6.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpActiveOpens"}, "tcpPassiveOpens": {"ObjectName": "tcpPassiveOpens", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 6", "OID": "*******.2.1.6.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpPassiveOpens"}, "tcpAttemptFails": {"ObjectName": "tcpAttemptFails", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 7", "OID": "*******.2.1.6.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpAttemptFails"}, "tcpEstabResets": {"ObjectName": "tcpEstabResets", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 8", "OID": "*******.2.1.6.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpEstabResets"}, "tcpCurrEstab": {"ObjectName": "tcpCurrEstab", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 9", "OID": "*******.2.1.6.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpCurrEstab"}, "tcpInSegs": {"ObjectName": "tcpInSegs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 10", "OID": "*******.2.1.6.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpInSegs"}, "tcpOutSegs": {"ObjectName": "tcpOutSegs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 11", "OID": "*******.2.1.6.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpOutSegs"}, "tcpRetransSegs": {"ObjectName": "tcpRetransSegs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 12", "OID": "*******.2.1.6.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpRetransSegs"}, "tcpConnTable": {"ObjectName": "tcpConnTable", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF TcpConnEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 13", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable"}, "tcpConnEntry": {"ObjectName": "tcpConnEntry", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TcpConnEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry"}, "TcpConnEntry": {"ObjectName": "TcpConnEntry", "ModuleName": "RFC1158-MIB", "MACRO": "SEQUENCE"}, "tcpConnState": {"ObjectName": "tcpConnState", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "closed", "2": "listen", "3": "synSent", "4": "synReceived", "5": "established", "6": "finWait1", "7": "finWait2", "8": "closeWait", "9": "lastAck", "10": "closing", "11": "timeWait"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnState"}, "tcpConnLocalAddress": {"ObjectName": "tcpConnLocalAddress", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnLocalAddress"}, "tcpConnLocalPort": {"ObjectName": "tcpConnLocalPort", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnLocalPort"}, "tcpConnRemAddress": {"ObjectName": "tcpConnRemAddress", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnRemAddress"}, "tcpConnRemPort": {"ObjectName": "tcpConnRemPort", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnRemPort"}, "tcpInErrs": {"ObjectName": "tcpInErrs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 14", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpInErrs"}, "tcpOutRsts": {"ObjectName": "tcpOutRsts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 15", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpOutRsts"}, "udpInDatagrams": {"ObjectName": "udpInDatagrams", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 1", "OID": "*******.2.1.7.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpInDatagrams"}, "udpNoPorts": {"ObjectName": "udpNoPorts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 2", "OID": "*******.2.1.7.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpNoPorts"}, "udpInErrors": {"ObjectName": "udpInErrors", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 3", "OID": "*******.2.1.7.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpInErrors"}, "udpOutDatagrams": {"ObjectName": "udpOutDatagrams", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 4", "OID": "*******.2.1.7.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpOutDatagrams"}, "udpTable": {"ObjectName": "udpTable", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF UdpEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 5", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpTable"}, "udpEntry": {"ObjectName": "udpEntry", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "UdpEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udpTable 1", "OID": "*******.*******.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpTable.udpEntry"}, "UdpEntry": {"ObjectName": "UdpEntry", "ModuleName": "RFC1158-MIB", "MACRO": "SEQUENCE"}, "udpLocalAddress": {"ObjectName": "udpLocalAddress", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udpEntry 1", "OID": "*******.*******.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpTable.udpEntry.udpLocalAddress"}, "udpLocalPort": {"ObjectName": "udpLocalPort", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udpEntry 2", "OID": "*******.*******.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpTable.udpEntry.udpLocalPort"}, "egpInMsgs": {"ObjectName": "egpInMsgs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpInMsgs"}, "egpInErrors": {"ObjectName": "egpInErrors", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpInErrors"}, "egpOutMsgs": {"ObjectName": "egpOutMsgs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 3", "OID": "*******.2.1.8.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpOutMsgs"}, "egpOutErrors": {"ObjectName": "egpOutErrors", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 4", "OID": "*******.2.1.8.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpOutErrors"}, "egpNeighTable": {"ObjectName": "egpNeighTable", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF EgpNeighEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 5", "OID": "*******.2.1.8.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable"}, "egpNeighEntry": {"ObjectName": "egpNeighEntry", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "EgpNeighEntry", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighTable 1", "OID": "*******.2.1.8.5.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry"}, "EgpNeighEntry": {"ObjectName": "EgpNeighEntry", "ModuleName": "RFC1158-MIB", "MACRO": "SEQUENCE"}, "egpNeighState": {"ObjectName": "egpNeighState", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "idle", "2": "acquisition", "3": "down", "4": "up", "5": "cease"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 1", "OID": "*******.2.1.8.5.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighState"}, "egpNeighAddr": {"ObjectName": "egpNeighAddr", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 2", "OID": "*******.2.1.8.5.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighAddr"}, "egpNeighAs": {"ObjectName": "egpNeighAs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 3", "OID": "*******.2.1.8.5.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighAs"}, "egpNeighInMsgs": {"ObjectName": "egpNeighInMsgs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 4", "OID": "*******.2.1.8.5.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighInMsgs"}, "egpNeighInErrs": {"ObjectName": "egpNeighInErrs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 5", "OID": "*******.2.1.8.5.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighInErrs"}, "egpNeighOutMsgs": {"ObjectName": "egpNeighOutMsgs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 6", "OID": "*******.2.1.8.5.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighOutMsgs"}, "egpNeighOutErrs": {"ObjectName": "egpNeighOutErrs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 7", "OID": "*******.2.1.8.5.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighOutErrs"}, "egpNeighInErrMsgs": {"ObjectName": "egpNeighInErrMsgs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 8", "OID": "*******.2.1.8.5.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighInErrMsgs"}, "egpNeighOutErrMsgs": {"ObjectName": "egpNeighOutErrMsgs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 9", "OID": "*******.2.1.8.5.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighOutErrMsgs"}, "egpNeighStateUps": {"ObjectName": "egpNeighStateUps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 10", "OID": "*******.2.1.8.5.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighStateUps"}, "egpNeighStateDowns": {"ObjectName": "egpNeighStateDowns", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 11", "OID": "*******.2.1.8.5.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighStateDowns"}, "egpNeighIntervalHello": {"ObjectName": "egpNeighIntervalHello", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 12", "OID": "*******.2.1.8.5.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighIntervalHello"}, "egpNeighIntervalPoll": {"ObjectName": "egpNeighIntervalPoll", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 13", "OID": "*******.2.1.8.5.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighIntervalPoll"}, "egpNeighMode": {"ObjectName": "egpNeighMode", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "active", "2": "passive"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 14", "OID": "*******.2.1.8.5.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighMode"}, "egpNeighEventTrigger": {"ObjectName": "egpNeighEventTrigger", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "start", "2": "stop"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 15", "OID": "*******.2.1.8.5.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighEventTrigger"}, "egpAs": {"ObjectName": "egpAs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 6", "OID": "*******.2.1.8.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpAs"}, "snmpInPkts": {"ObjectName": "snmpInPkts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 1", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInPkts"}, "snmpOutPkts": {"ObjectName": "snmpOutPkts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 2", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutPkts"}, "snmpInBadVersions": {"ObjectName": "snmpInBadVersions", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 3", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadVersions"}, "snmpInBadCommunityNames": {"ObjectName": "snmpInBadCommunityNames", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 4", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadCommunityNames"}, "snmpInBadCommunityUses": {"ObjectName": "snmpInBadCommunityUses", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 5", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadCommunityUses"}, "snmpInASNParseErrs": {"ObjectName": "snmpInASNParseErrs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 6", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInASNParseErrs"}, "snmpInBadTypes": {"ObjectName": "snmpInBadTypes", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 7", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadTypes"}, "snmpInTooBigs": {"ObjectName": "snmpInTooBigs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 8", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTooBigs"}, "snmpInNoSuchNames": {"ObjectName": "snmpInNoSuchNames", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 9", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInNoSuchNames"}, "snmpInBadValues": {"ObjectName": "snmpInBadValues", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 10", "OID": "*******.********0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadValues"}, "snmpInReadOnlys": {"ObjectName": "snmpInReadOnlys", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 11", "OID": "*******.********1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInReadOnlys"}, "snmpInGenErrs": {"ObjectName": "snmpInGenErrs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 12", "OID": "*******.********2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGenErrs"}, "snmpInTotalReqVars": {"ObjectName": "snmpInTotalReqVars", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 13", "OID": "*******.********3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTotalReqVars"}, "snmpInTotalSetVars": {"ObjectName": "snmpInTotalSetVars", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 14", "OID": "*******.********4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTotalSetVars"}, "snmpInGetRequests": {"ObjectName": "snmpInGetRequests", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 15", "OID": "*******.********5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGetRequests"}, "snmpInGetNexts": {"ObjectName": "snmpInGetNexts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 16", "OID": "*******.********6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGetNexts"}, "snmpInSetRequests": {"ObjectName": "snmpInSetRequests", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 17", "OID": "*******.********7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInSetRequests"}, "snmpInGetResponses": {"ObjectName": "snmpInGetResponses", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 18", "OID": "*******.********8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGetResponses"}, "snmpInTraps": {"ObjectName": "snmpInTraps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 19", "OID": "*******.********9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTraps"}, "snmpOutTooBigs": {"ObjectName": "snmpOutTooBigs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 20", "OID": "*******.********0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutTooBigs"}, "snmpOutNoSuchNames": {"ObjectName": "snmpOutNoSuchNames", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 21", "OID": "*******.********1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutNoSuchNames"}, "snmpOutBadValues": {"ObjectName": "snmpOutBadValues", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 22", "OID": "*******.********2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutBadValues"}, "snmpOutReadOnlys": {"ObjectName": "snmpOutReadOnlys", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 23", "OID": "*******.********3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutReadOnlys"}, "snmpOutGenErrs": {"ObjectName": "snmpOutGenErrs", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 24", "OID": "*******.********4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGenErrs"}, "snmpOutGetRequests": {"ObjectName": "snmpOutGetRequests", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 25", "OID": "*******.********5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGetRequests"}, "snmpOutGetNexts": {"ObjectName": "snmpOutGetNexts", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 26", "OID": "*******.********6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGetNexts"}, "snmpOutSetRequests": {"ObjectName": "snmpOutSetRequests", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 27", "OID": "*******.********7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutSetRequests"}, "snmpOutGetResponses": {"ObjectName": "snmpOutGetResponses", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 28", "OID": "*******.********8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGetResponses"}, "snmpOutTraps": {"ObjectName": "snmpOutTraps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 29", "OID": "*******.********9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutTraps"}, "snmpEnableAuthTraps": {"ObjectName": "snmpEnableAuthTraps", "ModuleName": "RFC1158-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "enabled", "2": "disabled"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 30", "OID": "*******.********0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpEnableAuthTraps"}}, "SNMPv2-SMI": {"org": {"ObjectName": "org", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "iso 3", "OID": "1.3.", "NameSpace": "iso.org."}, "dod": {"ObjectName": "dod", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "org 6", "OID": "1.3.6", "NameSpace": "iso.org.org."}, "internet": {"ObjectName": "internet", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "dod 1", "OID": "*******", "NameSpace": "iso.org.org.dod."}, "directory": {"ObjectName": "directory", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "internet 1", "OID": "*******.1", "NameSpace": "iso.org.dod.internet.directory"}, "mgmt": {"ObjectName": "mgmt", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "internet 2", "OID": "*******.2", "NameSpace": "iso.org.dod.internet.mgmt"}, "mib-2": {"ObjectName": "mib-2", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "mgmt 1", "OID": "*******.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2"}, "transmission": {"ObjectName": "transmission", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "mib-2 10", "OID": "*******.2.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.transmission"}, "experimental": {"ObjectName": "experimental", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "internet 3", "OID": "*******.3", "NameSpace": "iso.org.dod.internet.experimental"}, "private": {"ObjectName": "private", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "internet 4", "OID": "*******.4", "NameSpace": "iso.org.dod.internet.private"}, "enterprises": {"ObjectName": "enterprises", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "private 1", "OID": "*******.4.1", "NameSpace": "iso.org.dod.internet.private.enterprises"}, "security": {"ObjectName": "security", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "internet 5", "OID": "*******.5", "NameSpace": "iso.org.dod.internet.security"}, "snmpV2": {"ObjectName": "snmpV2", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "internet 6", "OID": "*******.6", "NameSpace": "iso.org.dod.internet.snmpV2"}, "snmpDomains": {"ObjectName": "snmpDomains", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "snmpV2 1", "OID": "*******.6.1", "NameSpace": "iso.org.dod.internet.snmpV2.snmpDomains"}, "snmpProxys": {"ObjectName": "snmpProxys", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "snmpV2 2", "OID": "*******.6.2", "NameSpace": "iso.org.dod.internet.snmpV2.snmpProxys"}, "snmpModules": {"ObjectName": "snmpModules", "ModuleName": "SNMPv2-SMI", "OBJECT_IDENTIFIER": "snmpV2 3", "OID": "*******.6.3", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules"}, "MODULE-IDENTITY": {"TYPE NOTATION": {"LAST-UPDATED": "value(Update UTCTime)", "ORGANIZATION": "Text", "CONTACT-INFO": "Text", "DESCRIPTION": "Text", "RevisionPart": null}, "VALUE NOTATION": {"value(VALUE OBJECT IDENTIFIER)": null}, "RevisionPart": {"ObjectName": "RevisionPart", "ModuleName": "SNMPv2-SMI", "MACRO": "Revisions"}, "Revisions": {"ObjectName": "Revisions", "ModuleName": "SNMPv2-SMI", "MACRO": "Revision"}, "Revision": {"ObjectName": "Revision", "ModuleName": "SNMPv2-SMI", "MACRO": "\"REVISION\""}, "Text": {"ObjectName": "Text", "ModuleName": "SNMPv2-SMI", "MACRO": "\"\""}}, "OBJECT-IDENTITY": {"TYPE NOTATION": {"STATUS": "Status", "DESCRIPTION": "Text", "ReferPart": null}, "VALUE NOTATION": {"value(VALUE OBJECT IDENTIFIER)": null}, "Status": {"ObjectName": "Status", "ModuleName": "SNMPv2-SMI", "MACRO": "\"current\""}, "ReferPart": {"ObjectName": "ReferPart", "ModuleName": "SNMPv2-SMI", "MACRO": "\"REFERENCE\""}, "Text": {"ObjectName": "Text", "ModuleName": "SNMPv2-SMI", "MACRO": "\"\""}}, "ObjectName": {"ObjectName": "ObjectName", "ModuleName": "SNMPv2-SMI", "MACRO": "OBJECT_IDENTIFIER"}, "NotificationName": {"ObjectName": "NotificationName", "ModuleName": "SNMPv2-SMI", "MACRO": "OBJECT_IDENTIFIER"}, "ObjectSyntax": {"ObjectName": "ObjectSyntax", "ModuleName": "SNMPv2-SMI", "MACRO": "CHOICE"}, "SimpleSyntax": {"ObjectName": "SimpleSyntax", "ModuleName": "SNMPv2-SMI", "MACRO": "CHOICE"}, "Integer32": {"ObjectName": "Integer32", "ModuleName": "SNMPv2-SMI", "MACRO": "[UNIVERSAL 2]"}, "ApplicationSyntax": {"ObjectName": "ApplicationSyntax", "ModuleName": "SNMPv2-SMI", "MACRO": "CHOICE"}, "IpAddress": {"ObjectName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModuleName": "SNMPv2-SMI", "MACRO": "[APPLICATION 0]"}, "Counter32": {"ObjectName": "Counter32", "ModuleName": "SNMPv2-SMI", "MACRO": "[APPLICATION 1]"}, "Gauge32": {"ObjectName": "Gauge32", "ModuleName": "SNMPv2-SMI", "MACRO": "[APPLICATION 2]"}, "Unsigned32": {"ObjectName": "Unsigned32", "ModuleName": "SNMPv2-SMI", "MACRO": "[APPLICATION 2]"}, "TimeTicks": {"ObjectName": "TimeTicks", "ModuleName": "SNMPv2-SMI", "MACRO": "[APPLICATION 3]"}, "Opaque": {"ObjectName": "Opaque", "ModuleName": "SNMPv2-SMI", "MACRO": "[APPLICATION 4]"}, "Counter64": {"ObjectName": "Counter64", "ModuleName": "SNMPv2-SMI", "MACRO": "[APPLICATION 6]"}, "OBJECT-TYPE": {"TYPE NOTATION": {"SYNTAX": "Syntax", "UnitsPart": null, "MAX-ACCESS": "Access", "STATUS": "Status", "DESCRIPTION": "Text", "ReferPart": null, "IndexPart": null, "DefValPart": null}, "VALUE NOTATION": {"value(VALUE ObjectName)": null}, "Syntax": {"ObjectName": "Syntax", "ModuleName": "SNMPv2-SMI", "MACRO": "type(ObjectSyntax)"}, "Kibbles": {"ObjectName": "Kibbles", "ModuleName": "SNMPv2-SMI", "MACRO": "<PERSON><PERSON>"}, "Kibble": {"ObjectName": "<PERSON><PERSON>", "ModuleName": "SNMPv2-SMI", "MACRO": "identifier"}, "UnitsPart": {"ObjectName": "UnitsPart", "ModuleName": "SNMPv2-SMI", "MACRO": "\"UNITS\""}, "Access": {"ObjectName": "Access", "ModuleName": "SNMPv2-SMI", "MACRO": "\"not-accessible\""}, "Status": {"ObjectName": "Status", "ModuleName": "SNMPv2-SMI", "MACRO": "\"current\""}, "ReferPart": {"ObjectName": "ReferPart", "ModuleName": "SNMPv2-SMI", "MACRO": "\"REFERENCE\""}, "IndexPart": {"ObjectName": "IndexPart", "ModuleName": "SNMPv2-SMI", "MACRO": "\"INDEX\""}, "IndexTypes": {"ObjectName": "IndexTypes", "ModuleName": "SNMPv2-SMI", "MACRO": "IndexType"}, "IndexType": {"ObjectName": "IndexType", "ModuleName": "SNMPv2-SMI", "MACRO": "\"IMPLIED\""}, "Index": {"ObjectName": "Index", "ModuleName": "SNMPv2-SMI", "MACRO": "value(Indexobject ObjectName)"}, "Entry": {"ObjectName": "Entry", "ModuleName": "SNMPv2-SMI", "MACRO": "value(Entryobject ObjectName)"}, "DefValPart": {"ObjectName": "DefValPart", "ModuleName": "SNMPv2-SMI", "MACRO": "\"DEFVAL\""}, "Text": {"ObjectName": "Text", "ModuleName": "SNMPv2-SMI", "MACRO": "\"\""}}, "NOTIFICATION-TYPE": {"TYPE NOTATION": {"ObjectsPart": null, "STATUS": "Status", "DESCRIPTION": "Text", "ReferPart": null}, "VALUE NOTATION": {"value(VALUE NotificationName)": null}, "ObjectsPart": {"ObjectName": "ObjectsPart", "ModuleName": "SNMPv2-SMI", "MACRO": "\"OBJECTS\""}, "Objects": {"ObjectName": "Objects", "ModuleName": "SNMPv2-SMI", "MACRO": "Object"}, "Object": {"ObjectName": "Object", "ModuleName": "SNMPv2-SMI", "MACRO": "value(Name ObjectName)"}, "Status": {"ObjectName": "Status", "ModuleName": "SNMPv2-SMI", "MACRO": "\"current\""}, "ReferPart": {"ObjectName": "ReferPart", "ModuleName": "SNMPv2-SMI", "MACRO": "\"REFERENCE\""}, "Text": {"ObjectName": "Text", "ModuleName": "SNMPv2-SMI", "MACRO": "\"\""}}, "zeroDotZero": {"ObjectName": "zeroDotZero", "ModuleName": "SNMPv2-SMI", "MACRO": "OBJECT-IDENTITY", "STATUS": "current", "DESCRIPTION": "A value used for null identifiers.", "OBJECT_IDENTIFIER": "0 0", "OID": "0.0", "NameSpace": "null"}}, "SNMPv2-CONF": {"OBJECT-GROUP": {"TYPE NOTATION": {"ObjectsPart": null, "STATUS": "Status", "DESCRIPTION": "Text", "ReferPart": null}, "VALUE NOTATION": {"value(VALUE OBJECT IDENTIFIER)": null}, "ObjectsPart": {"ObjectName": "ObjectsPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"OBJECTS\""}, "Objects": {"ObjectName": "Objects", "ModuleName": "SNMPv2-CONF", "MACRO": "Object"}, "Object": {"ObjectName": "Object", "ModuleName": "SNMPv2-CONF", "MACRO": "value(Name ObjectName)"}, "Status": {"ObjectName": "Status", "ModuleName": "SNMPv2-CONF", "MACRO": "\"current\""}, "ReferPart": {"ObjectName": "ReferPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"REFERENCE\""}, "Text": {"ObjectName": "Text", "ModuleName": "SNMPv2-CONF", "MACRO": "\"\""}}, "NOTIFICATION-GROUP": {"TYPE NOTATION": {"NotificationsPart": null, "STATUS": "Status", "DESCRIPTION": "Text", "ReferPart": null}, "VALUE NOTATION": {"value(VALUE OBJECT IDENTIFIER)": null}, "NotificationsPart": {"ObjectName": "NotificationsPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"NOTIFICATIONS\""}, "Notifications": {"ObjectName": "Notifications", "ModuleName": "SNMPv2-CONF", "MACRO": "Notification"}, "Notification": {"ObjectName": "Notification", "ModuleName": "SNMPv2-CONF", "MACRO": "value(Name NotificationName)"}, "Status": {"ObjectName": "Status", "ModuleName": "SNMPv2-CONF", "MACRO": "\"current\""}, "ReferPart": {"ObjectName": "ReferPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"REFERENCE\""}, "Text": {"ObjectName": "Text", "ModuleName": "SNMPv2-CONF", "MACRO": "\"\""}}, "MODULE-COMPLIANCE": {"TYPE NOTATION": {"STATUS": "Status", "DESCRIPTION": "Text", "ReferPart": null, "ModulePart": null}, "VALUE NOTATION": {"value(VALUE OBJECT IDENTIFIER)": null}, "Status": {"ObjectName": "Status", "ModuleName": "SNMPv2-CONF", "MACRO": "\"current\""}, "ReferPart": {"ObjectName": "ReferPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"REFERENCE\""}, "ModulePart": {"ObjectName": "Mo<PERSON>lePart", "ModuleName": "SNMPv2-CONF", "MACRO": "<PERSON><PERSON><PERSON>"}, "Modules": {"ObjectName": "<PERSON><PERSON><PERSON>", "ModuleName": "SNMPv2-CONF", "MACRO": "<PERSON><PERSON><PERSON>"}, "Module": {"ObjectName": "<PERSON><PERSON><PERSON>", "ModuleName": "SNMPv2-CONF", "MACRO": "\"MODULE\""}, "ModuleName": {"ObjectName": "ModuleName", "ModuleName": "SNMPv2-CONF", "MACRO": "modulereference"}, "ModuleIdentifier": {"ObjectName": "ModuleIdentifier", "ModuleName": "SNMPv2-CONF", "MACRO": "value(ModuleID OBJECT IDENTIFIER)"}, "MandatoryPart": {"ObjectName": "MandatoryPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"MANDATORY-GROUPS\""}, "Groups": {"ObjectName": "Groups", "ModuleName": "SNMPv2-CONF", "MACRO": "Group"}, "Group": {"ObjectName": "Group", "ModuleName": "SNMPv2-CONF", "MACRO": "value(Group OBJECT IDENTIFIER)"}, "CompliancePart": {"ObjectName": "CompliancePart", "ModuleName": "SNMPv2-CONF", "MACRO": "Compliances"}, "Compliances": {"ObjectName": "Compliances", "ModuleName": "SNMPv2-CONF", "MACRO": "Compliance"}, "Compliance": {"ObjectName": "Compliance", "ModuleName": "SNMPv2-CONF", "MACRO": "ComplianceGroup"}, "ComplianceGroup": {"ObjectName": "ComplianceGroup", "ModuleName": "SNMPv2-CONF", "MACRO": "\"GROUP\""}, "Object": {"ObjectName": "Object", "ModuleName": "SNMPv2-CONF", "MACRO": "\"OBJECT\""}, "SyntaxPart": {"ObjectName": "SyntaxPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"SYNTAX\""}, "WriteSyntaxPart": {"ObjectName": "WriteSyntaxPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"WRITE-SYNTAX\""}, "AccessPart": {"ObjectName": "AccessPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"MIN-ACCESS\""}, "Access": {"ObjectName": "Access", "ModuleName": "SNMPv2-CONF", "MACRO": "\"not-accessible\""}, "Text": {"ObjectName": "Text", "ModuleName": "SNMPv2-CONF", "MACRO": "\"\""}}, "AGENT-CAPABILITIES": {"TYPE NOTATION": {"PRODUCT-RELEASE": "Text", "STATUS": "Status", "DESCRIPTION": "Text", "ReferPart": null, "ModulePart": null}, "VALUE NOTATION": {"value(VALUE OBJECT IDENTIFIER)": null}, "Status": {"ObjectName": "Status", "ModuleName": "SNMPv2-CONF", "MACRO": "\"current\""}, "ReferPart": {"ObjectName": "ReferPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"REFERENCE\""}, "ModulePart": {"ObjectName": "Mo<PERSON>lePart", "ModuleName": "SNMPv2-CONF", "MACRO": "<PERSON><PERSON><PERSON>"}, "Modules": {"ObjectName": "<PERSON><PERSON><PERSON>", "ModuleName": "SNMPv2-CONF", "MACRO": "<PERSON><PERSON><PERSON>"}, "Module": {"ObjectName": "<PERSON><PERSON><PERSON>", "ModuleName": "SNMPv2-CONF", "MACRO": "\"SUPPORTS\""}, "ModuleName": {"ObjectName": "ModuleName", "ModuleName": "SNMPv2-CONF", "MACRO": "identifier"}, "ModuleIdentifier": {"ObjectName": "ModuleIdentifier", "ModuleName": "SNMPv2-CONF", "MACRO": "value(ModuleID OBJECT IDENTIFIER)"}, "Groups": {"ObjectName": "Groups", "ModuleName": "SNMPv2-CONF", "MACRO": "Group"}, "Group": {"ObjectName": "Group", "ModuleName": "SNMPv2-CONF", "MACRO": "value(Name OBJECT IDENTIFIER)"}, "VariationPart": {"ObjectName": "VariationPart", "ModuleName": "SNMPv2-CONF", "MACRO": "Variations"}, "Variations": {"ObjectName": "Variations", "ModuleName": "SNMPv2-CONF", "MACRO": "Variation"}, "Variation": {"ObjectName": "Variation", "ModuleName": "SNMPv2-CONF", "MACRO": "ObjectVariation"}, "NotificationVariation": {"ObjectName": "NotificationVariation", "ModuleName": "SNMPv2-CONF", "MACRO": "\"VARIATION\""}, "ObjectVariation": {"ObjectName": "ObjectVariation", "ModuleName": "SNMPv2-CONF", "MACRO": "\"VARIATION\""}, "SyntaxPart": {"ObjectName": "SyntaxPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"SYNTAX\""}, "WriteSyntaxPart": {"ObjectName": "WriteSyntaxPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"WRITE-SYNTAX\""}, "AccessPart": {"ObjectName": "AccessPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"ACCESS\""}, "Access": {"ObjectName": "Access", "ModuleName": "SNMPv2-CONF", "MACRO": "\"not-implemented\""}, "CreationPart": {"ObjectName": "CreationPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"CREATION-REQUIRES\""}, "Cells": {"ObjectName": "Cells", "ModuleName": "SNMPv2-CONF", "MACRO": "Cell"}, "Cell": {"ObjectName": "Cell", "ModuleName": "SNMPv2-CONF", "MACRO": "value(Cell ObjectName)"}, "DefValPart": {"ObjectName": "DefValPart", "ModuleName": "SNMPv2-CONF", "MACRO": "\"DEFVAL\""}, "Text": {"ObjectName": "Text", "ModuleName": "SNMPv2-CONF", "MACRO": "\"\""}}}, "SNMPv2-TC": {"IMPORTS": {"SNMPv2-SMI": ["ObjectSyntax", "TimeTicks"]}, "TEXTUAL-CONVENTION": {"TYPE NOTATION": {"DisplayPart": null, "STATUS": "Status", "DESCRIPTION": "Text", "ReferPart": null, "SYNTAX": "Syntax"}, "VALUE NOTATION": {"value(VALUE Syntax)": null}, "DisplayPart": {"ObjectName": "DisplayPart", "ModuleName": "SNMPv2-TC", "MACRO": "\"DISPLAY-HINT\""}, "Status": {"ObjectName": "Status", "ModuleName": "SNMPv2-TC", "MACRO": "\"current\""}, "ReferPart": {"ObjectName": "ReferPart", "ModuleName": "SNMPv2-TC", "MACRO": "\"REFERENCE\""}, "Text": {"ObjectName": "Text", "ModuleName": "SNMPv2-TC", "MACRO": "\"\""}, "Syntax": {"ObjectName": "Syntax", "ModuleName": "SNMPv2-TC", "MACRO": "type(ObjectSyntax)"}, "Kibbles": {"ObjectName": "Kibbles", "ModuleName": "SNMPv2-TC", "MACRO": "<PERSON><PERSON>"}, "Kibble": {"ObjectName": "<PERSON><PERSON>", "ModuleName": "SNMPv2-TC", "MACRO": "identifier"}}, "DisplayString": {"ObjectName": "DisplayString", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "PhysAddress": {"ObjectName": "PhysAdd<PERSON>", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MacAddress": {"ObjectName": "<PERSON><PERSON><PERSON><PERSON>", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "TruthValue": {"ObjectName": "TruthValue", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"1": " true", "2 ": "false"}}}, "TestAndIncr": {"ObjectName": "TestAndIncr", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION"}, "AutonomousType": {"ObjectName": "AutonomousType", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OBJECT_IDENTIFIER"}, "InstancePointer": {"ObjectName": "In<PERSON><PERSON><PERSON><PERSON>", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OBJECT_IDENTIFIER"}, "VariablePointer": {"ObjectName": "VariablePointer", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OBJECT_IDENTIFIER"}, "RowPointer": {"ObjectName": "<PERSON><PERSON><PERSON>er", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OBJECT_IDENTIFIER"}, "RowStatus": {"ObjectName": "RowStatus", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"1": "active", "2": "notInService", "3": "notReady", "4": "createAndGo", "5": "createAndWait", "6": "destroy"}}}, "TimeStamp": {"ObjectName": "TimeStamp", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "TimeTicks"}, "TimeInterval": {"ObjectName": "TimeInterval", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION"}, "DateAndTime": {"ObjectName": "DateAndTime", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "StorageType": {"ObjectName": "StorageType", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"1": "other", "2": "volatile", "3": "nonVolatile", "4": "permanent", "5": "readOnly"}}}, "TDomain": {"ObjectName": "TD<PERSON>in", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OBJECT_IDENTIFIER"}, "TAddress": {"ObjectName": "<PERSON><PERSON><PERSON><PERSON>", "ModuleName": "SNMPv2-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}}, "SNMPv2-MIB": {"IMPORTS": {"SNMPv2-SMI": ["MODULE-IDENTITY", "OBJECT-TYPE", "NOTIFICATION-TYPE", "TimeTicks", "Counter32", "snmpModules", "mib-2"], "SNMPv2-TC": ["DisplayString", "TestAndIncr", "TimeStamp"], "SNMPv2-CONF": ["MODULE-COMPLIANCE", "OBJECT-GROUP", "NOTIFICATION-GROUP"]}, "snmpMIB": {"ObjectName": "snmpMIB", "ModuleName": "SNMPv2-MIB", "MACRO": "MODULE-IDENTITY", "LAST-UPDATED": "200210160000Z", "ORGANIZATION": "IETF SNMPv3 Working Group", "CONTACT-INFO": "WG-EMail:   <EMAIL>\r\n             Subscribe:  <EMAIL>\r\n\r\n             Co-Chair:   <PERSON> Associates Laboratories\r\n             postal:     15204 Omega Drive, Suite 300\r\n                         Rockville, MD 20850-4601\r\n                         USA\r\n             EMail:      <EMAIL>\r\n             phone:      ****** 947-7107\r\n\r\n             Co-Chair:   <PERSON>sys Networks\r\n             postal:     35 Industrial Way\r\n                         P. O. Box 5005\r\n                         Rochester, NH 03866-5005\r\n                         USA\r\n             EMail:      <EMAIL>\r\n             phone:      ****** 337-2614\r\n\r\n             Editor:     <PERSON> Software, Inc.\r\n             postal:     2141 North First Street\r\n                         San Jose, CA 95131\r\n                         USA\r\n             EMail:      <EMAIL>\r\n             phone:      ****** 546-1006", "DESCRIPTION": "The initial revision of this MIB module was published\r\n            as RFC 1450.", "OBJECT_IDENTIFIER": "snmpModules 1", "OID": "*******.6.3.1", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB"}, "snmpMIBObjects": {"ObjectName": "snmpMIBObjects", "ModuleName": "SNMPv2-MIB", "OBJECT_IDENTIFIER": "snmpMIB 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects"}, "system": {"ObjectName": "system", "ModuleName": "SNMPv2-MIB", "OBJECT_IDENTIFIER": "mib-2 1", "OID": "*******.2.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system"}, "sysDescr": {"ObjectName": "sysDescr", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "A textual description of the entity.  This value should\r\n            include the full name and version identification of\r\n            the system's hardware type, software operating-system,\r\n            and networking software.", "OBJECT_IDENTIFIER": "system 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysDescr"}, "sysObjectID": {"ObjectName": "sysObjectID", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The vendor's authoritative identification of the\r\n            network management subsystem contained in the entity.\r\n            This value is allocated within the SMI enterprises\r\n            subtree (*******.4.1) and provides an easy and\r\n            unambiguous means for determining `what kind of box' is\r\n            being managed.  For example, if vendor `Flintstones,\r\n            Inc.' was assigned the subtree *******.4.1.424242,\r\n            it could assign the identifier *******.4.1.424242.1.1\r\n            to its `Fred Router'.", "OBJECT_IDENTIFIER": "system 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysObjectID"}, "sysUpTime": {"ObjectName": "sysUpTime", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The time (in hundredths of a second) since the\r\n            network management portion of the system was last\r\n            re-initialized.", "OBJECT_IDENTIFIER": "system 3", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysUpTime"}, "sysContact": {"ObjectName": "sysContact", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "MAX-ACCESS": "read-write", "STATUS": "current", "DESCRIPTION": "The textual identification of the contact person for\r\n            this managed node, together with information on how\r\n            to contact this person.  If no contact information is\r\n            known, the value is the zero-length string.", "OBJECT_IDENTIFIER": "system 4", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysContact"}, "sysName": {"ObjectName": "sysName", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "MAX-ACCESS": "read-write", "STATUS": "current", "DESCRIPTION": "An administratively-assigned name for this managed\r\n            node.  By convention, this is the node's fully-qualified\r\n            domain name.  If the name is unknown, the value is\r\n            the zero-length string.", "OBJECT_IDENTIFIER": "system 5", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysName"}, "sysLocation": {"ObjectName": "sysLocation", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "MAX-ACCESS": "read-write", "STATUS": "current", "DESCRIPTION": "The physical location of this node (e.g., 'telephone\r\n            closet, 3rd floor').  If the location is unknown, the\r\n            value is the zero-length string.", "OBJECT_IDENTIFIER": "system 6", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysLocation"}, "sysServices": {"ObjectName": "sysServices", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "A value which indicates the set of services that this\r\n            entity may potentially offer.  The value is a sum.\r\n\r\n            This sum initially takes the value zero. Then, for\r\n            each layer, L, in the range 1 through 7, that this node\r\n            performs transactions for, 2 raised to (L - 1) is added\r\n            to the sum.  For example, a node which performs only\r\n            routing functions would have a value of 4 (2^(3-1)).\r\n            In contrast, a node which is a host offering application\r\n            services would have a value of 72 (2^(4-1) + 2^(7-1)).\r\n            Note that in the context of the Internet suite of\r\n            protocols, values should be calculated accordingly:\r\n\r\n                 layer      functionality\r\n                   1        physical (e.g., repeaters)\r\n                   2        datalink/subnetwork (e.g., bridges)\r\n                   3        internet (e.g., supports the IP)\r\n                   4        end-to-end  (e.g., supports the TCP)\r\n                   7        applications (e.g., supports the SMTP)\r\n\r\n            For systems including OSI protocols, layers 5 and 6\r\n            may also be counted.", "OBJECT_IDENTIFIER": "system 7", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysServices"}, "sysORLastChange": {"ObjectName": "sysORLastChange", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeStamp", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The value of sysUpTime at the time of the most recent\r\n            change in state or value of any instance of sysORID.", "OBJECT_IDENTIFIER": "system 8", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysORLastChange"}, "sysORTable": {"ObjectName": "sysORTable", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF SysOREntry", "MAX-ACCESS": "not-accessible", "STATUS": "current", "DESCRIPTION": "The (conceptual) table listing the capabilities of\r\n            the local SNMP application acting as a command\r\n            responder with respect to various MIB modules.\r\n            SNMP entities having dynamically-configurable support\r\n            of MIB modules will have a dynamically-varying number\r\n            of conceptual rows.", "OBJECT_IDENTIFIER": "system 9", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysORTable"}, "sysOREntry": {"ObjectName": "sysOREntry", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SysOREntry", "MAX-ACCESS": "not-accessible", "STATUS": "current", "DESCRIPTION": "An entry (conceptual row) in the sysORTable.", "OBJECT_IDENTIFIER": "sysORTable 1", "OID": "*******.*******.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysORTable.sysOREntry"}, "SysOREntry": {"ObjectName": "SysOREntry", "ModuleName": "SNMPv2-MIB", "MACRO": "SEQUENCE"}, "sysORIndex": {"ObjectName": "sysORIndex", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "MAX-ACCESS": "not-accessible", "STATUS": "current", "DESCRIPTION": "The auxiliary variable used for identifying instances\r\n            of the columnar objects in the sysORTable.", "OBJECT_IDENTIFIER": "sysOREntry 1", "OID": "*******.*******.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysORTable.sysOREntry.sysORIndex"}, "sysORID": {"ObjectName": "sysORID", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "An authoritative identification of a capabilities\r\n            statement with respect to various MIB modules supported\r\n            by the local SNMP application acting as a command\r\n            responder.", "OBJECT_IDENTIFIER": "sysOREntry 2", "OID": "*******.*******.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysORTable.sysOREntry.sysORID"}, "sysORDescr": {"ObjectName": "sysORDescr", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "A textual description of the capabilities identified\r\n            by the corresponding instance of sysORID.", "OBJECT_IDENTIFIER": "sysOREntry 3", "OID": "*******.*******.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysORTable.sysOREntry.sysORDescr"}, "sysORUpTime": {"ObjectName": "sysORUpTime", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeStamp", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The value of sysUpTime at the time this conceptual\r\n            row was last instantiated.", "OBJECT_IDENTIFIER": "sysOREntry 4", "OID": "*******.*******.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysORTable.sysOREntry.sysORUpTime"}, "snmp": {"ObjectName": "snmp", "ModuleName": "SNMPv2-MIB", "OBJECT_IDENTIFIER": "mib-2 11", "OID": "*******.2.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp"}, "snmpInPkts": {"ObjectName": "snmpInPkts", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The total number of messages delivered to the SNMP\r\n            entity from the transport service.", "OBJECT_IDENTIFIER": "snmp 1", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInPkts"}, "snmpInBadVersions": {"ObjectName": "snmpInBadVersions", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The total number of SNMP messages which were delivered\r\n            to the SNMP entity and were for an unsupported SNMP\r\n            version.", "OBJECT_IDENTIFIER": "snmp 3", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadVersions"}, "snmpInBadCommunityNames": {"ObjectName": "snmpInBadCommunityNames", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The total number of community-based SNMP messages (for\r\n           example,  SNMPv1) delivered to the SNMP entity which\r\n           used an SNMP community name not known to said entity.\r\n           Also, implementations which authenticate community-based\r\n           SNMP messages using check(s) in addition to matching\r\n           the community name (for example, by also checking\r\n           whether the message originated from a transport address\r\n           allowed to use a specified community name) MAY include\r\n           in this value the number of messages which failed the\r\n           additional check(s).  It is strongly RECOMMENDED that\r\n\r\n           the documentation for any security model which is used\r\n           to authenticate community-based SNMP messages specify\r\n           the precise conditions that contribute to this value.", "OBJECT_IDENTIFIER": "snmp 4", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadCommunityNames"}, "snmpInBadCommunityUses": {"ObjectName": "snmpInBadCommunityUses", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The total number of community-based SNMP messages (for\r\n           example, SNMPv1) delivered to the SNMP entity which\r\n           represented an SNMP operation that was not allowed for\r\n           the SNMP community named in the message.  The precise\r\n           conditions under which this counter is incremented\r\n           (if at all) depend on how the SNMP entity implements\r\n           its access control mechanism and how its applications\r\n           interact with that access control mechanism.  It is\r\n           strongly RECOMMENDED that the documentation for any\r\n           access control mechanism which is used to control access\r\n           to and visibility of MIB instrumentation specify the\r\n           precise conditions that contribute to this value.", "OBJECT_IDENTIFIER": "snmp 5", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadCommunityUses"}, "snmpInASNParseErrs": {"ObjectName": "snmpInASNParseErrs", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The total number of ASN.1 or BER errors encountered by\r\n            the SNMP entity when decoding received SNMP messages.", "OBJECT_IDENTIFIER": "snmp 6", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInASNParseErrs"}, "snmpEnableAuthenTraps": {"ObjectName": "snmpEnableAuthenTraps", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "enabled", "2": "disabled"}}, "MAX-ACCESS": "read-write", "STATUS": "current", "DESCRIPTION": "Indicates whether the SNMP entity is permitted to\r\n            generate authenticationFailure traps.  The value of this\r\n            object overrides any configuration information; as such,\r\n            it provides a means whereby all authenticationFailure\r\n            traps may be disabled.\r\n\r\n            Note that it is strongly recommended that this object\r\n            be stored in non-volatile memory so that it remains\r\n            constant across re-initializations of the network\r\n            management system.", "OBJECT_IDENTIFIER": "snmp 30", "OID": "*******.********0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpEnableAuthenTraps"}, "snmpSilentDrops": {"ObjectName": "snmpSilentDrops", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The total number of Confirmed Class PDUs (such as\r\n           GetRequest-PDUs, GetNextRequest-PDUs,\r\n           GetBulkRequest-PDUs, SetRequest-PDUs, and\r\n           InformRequest-PDUs) delivered to the SNMP entity which\r\n           were silently dropped because the size of a reply\r\n           containing an alternate Response Class PDU (such as a\r\n           Response-PDU) with an empty variable-bindings field\r\n           was greater than either a local constraint or the\r\n           maximum message size associated with the originator of\r\n           the request.", "OBJECT_IDENTIFIER": "snmp 31", "OID": "*******.********1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpSilentDrops"}, "snmpProxyDrops": {"ObjectName": "snmpProxyDrops", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "current", "DESCRIPTION": "The total number of Confirmed Class PDUs\r\n            (such as GetRequest-PDUs, GetNextRequest-PDUs,\r\n            GetBulkRequest-PDUs, SetRequest-PDUs, and\r\n            InformRequest-PDUs) delivered to the SNMP entity which\r\n            were silently dropped because the transmission of\r\n            the (possibly translated) message to a proxy target\r\n            failed in a manner (other than a time-out) such that\r\n            no Response Class PDU (such as a Response-PDU) could\r\n            be returned.", "OBJECT_IDENTIFIER": "snmp 32", "OID": "*******.********2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpProxyDrops"}, "snmpTrap": {"ObjectName": "snmpTrap", "ModuleName": "SNMPv2-MIB", "OBJECT_IDENTIFIER": "snmpMIBObjects 4", "OID": "*******.*******.4", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTrap"}, "snmpTrapOID": {"ObjectName": "snmpTrapOID", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "MAX-ACCESS": "accessible-for-notify", "STATUS": "current", "DESCRIPTION": "The authoritative identification of the notification\r\n            currently being sent.  This variable occurs as\r\n            the second varbind in every SNMPv2-Trap-PDU and\r\n            InformRequest-PDU.", "OBJECT_IDENTIFIER": "snmpTrap 1", "OID": "*******.*******.4.1", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTrap.snmpTrapOID"}, "snmpTrapEnterprise": {"ObjectName": "snmpTrapEnterprise", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "MAX-ACCESS": "accessible-for-notify", "STATUS": "current", "DESCRIPTION": "The authoritative identification of the enterprise\r\n            associated with the trap currently being sent.  When an\r\n            SNMP proxy agent is mapping an RFC1157 Trap-PDU\r\n            into a SNMPv2-Trap-PDU, this variable occurs as the\r\n            last varbind.", "OBJECT_IDENTIFIER": "snmpTrap 3", "OID": "*******.*******.4.3", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTrap.snmpTrapEnterprise"}, "snmpTraps": {"ObjectName": "snmpTraps", "ModuleName": "SNMPv2-MIB", "OBJECT_IDENTIFIER": "snmpMIBObjects 5", "OID": "*******.*******.5", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTraps"}, "coldStart": {"ObjectName": "coldStart", "ModuleName": "SNMPv2-MIB", "MACRO": "NOTIFICATION-TYPE", "STATUS": "current", "DESCRIPTION": "A coldStart trap signifies that the SNMP entity,\r\n            supporting a notification originator application, is\r\n            reinitializing itself and that its configuration may\r\n            have been altered.", "OBJECT_IDENTIFIER": "snmpTraps 1", "OID": "*******.*******.5.1", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTraps.coldStart"}, "warmStart": {"ObjectName": "warmStart", "ModuleName": "SNMPv2-MIB", "MACRO": "NOTIFICATION-TYPE", "STATUS": "current", "DESCRIPTION": "A warmStart trap signifies that the SNMP entity,\r\n            supporting a notification originator application,\r\n            is reinitializing itself such that its configuration\r\n            is unaltered.", "OBJECT_IDENTIFIER": "snmpTraps 2", "OID": "*******.*******.5.2", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTraps.warmStart"}, "authenticationFailure": {"ObjectName": "authenticationFailure", "ModuleName": "SNMPv2-MIB", "MACRO": "NOTIFICATION-TYPE", "STATUS": "current", "DESCRIPTION": "An authenticationFailure trap signifies that the SNMP\r\n             entity has received a protocol message that is not\r\n             properly authenticated.  While all implementations\r\n             of SNMP entities MAY be capable of generating this\r\n             trap, the snmpEnableAuthenTraps object indicates\r\n             whether this trap will be generated.", "OBJECT_IDENTIFIER": "snmpTraps 5", "OID": "*******.*******.5.5", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTraps.authenticationFailure"}, "snmpSet": {"ObjectName": "snmpSet", "ModuleName": "SNMPv2-MIB", "OBJECT_IDENTIFIER": "snmpMIBObjects 6", "OID": "*******.*******.6", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpSet"}, "snmpSetSerialNo": {"ObjectName": "snmpSetSerialNo", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TestAndIncr", "MAX-ACCESS": "read-write", "STATUS": "current", "DESCRIPTION": "An advisory lock used to allow several cooperating\r\n            command generator applications to coordinate their\r\n            use of the SNMP set operation.\r\n\r\n            This object is used for coarse-grain coordination.\r\n            To achieve fine-grain coordination, one or more similar\r\n            objects might be defined within each MIB group, as\r\n            appropriate.", "OBJECT_IDENTIFIER": "snmpSet 1", "OID": "*******.*******.6.1", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpSet.snmpSetSerialNo"}, "snmpMIBConformance": {"ObjectName": "snmpMIBConformance", "ModuleName": "SNMPv2-MIB", "OBJECT_IDENTIFIER": "snmpMIB 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance"}, "snmpMIBCompliances": {"ObjectName": "snmpMIBCompliances", "ModuleName": "SNMPv2-MIB", "OBJECT_IDENTIFIER": "snmpMIBConformance 1", "OID": "*******.*******.1", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBCompliances"}, "snmpMIBGroups": {"ObjectName": "snmpMIBGroups", "ModuleName": "SNMPv2-MIB", "OBJECT_IDENTIFIER": "snmpMIBConformance 2", "OID": "*******.*******.2", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBGroups"}, "snmpBasicCompliance": {"ObjectName": "snmpBasicCompliance", "ModuleName": "SNMPv2-MIB", "MACRO": "MODULE-COMPLIANCE", "STATUS": "deprecated", "DESCRIPTION": "This group is mandatory for SNMPv2 entities which\r\n            support community-based authentication.", "OBJECT_IDENTIFIER": "snmpMIBCompliances 2", "OID": "*******.*******.1.2", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBCompliances.snmpBasicCompliance"}, "snmpBasicComplianceRev2": {"ObjectName": "snmpBasicComplianceRev2", "ModuleName": "SNMPv2-MIB", "MACRO": "MODULE-COMPLIANCE", "STATUS": "current", "DESCRIPTION": "This group is mandatory for an SNMP entity which\r\n            supports command responder applications, and is\r\n            able to reinitialize itself such that its\r\n            configuration is unaltered.", "OBJECT_IDENTIFIER": "snmpMIBCompliances 3", "OID": "*******.*******.1.3", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBCompliances.snmpBasicComplianceRev2"}, "snmpGroup": {"ObjectName": "snmpGroup", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing basic instrumentation\r\n            and control of an SNMP entity.", "OBJECT_IDENTIFIER": "snmpMIBGroups 8", "OID": "*******.*******.2.8", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBGroups.snmpGroup"}, "snmpCommunityGroup": {"ObjectName": "snmpCommunityGroup", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing basic instrumentation\r\n            of a SNMP entity which supports community-based\r\n            authentication.", "OBJECT_IDENTIFIER": "snmpMIBGroups 9", "OID": "*******.*******.2.9", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBGroups.snmpCommunityGroup"}, "snmpSetGroup": {"ObjectName": "snmpSetGroup", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects which allow several cooperating\r\n            command generator applications to coordinate their\r\n            use of the set operation.", "OBJECT_IDENTIFIER": "snmpMIBGroups 5", "OID": "*******.*******.2.5", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBGroups.snmpSetGroup"}, "systemGroup": {"ObjectName": "systemGroup", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "The system group defines objects which are common to all\r\n            managed systems.", "OBJECT_IDENTIFIER": "snmpMIBGroups 6", "OID": "*******.*******.2.6", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBGroups.systemGroup"}, "snmpBasicNotificationsGroup": {"ObjectName": "snmpBasicNotificationsGroup", "ModuleName": "SNMPv2-MIB", "MACRO": "NOTIFICATION-GROUP", "STATUS": "current", "DESCRIPTION": "The basic notifications implemented by an SNMP entity\r\n        supporting command responder applications.", "OBJECT_IDENTIFIER": "snmpMIBGroups 7", "OID": "*******.*******.2.7", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBGroups.snmpBasicNotificationsGroup"}, "snmpWarmStartNotificationGroup": {"ObjectName": "snmpWarmStartNotificationGroup", "ModuleName": "SNMPv2-MIB", "MACRO": "NOTIFICATION-GROUP", "STATUS": "current", "DESCRIPTION": "An additional notification for an SNMP entity supporting\r\n     command responder applications, if it is able to reinitialize\r\n     itself such that its configuration is unaltered.", "OBJECT_IDENTIFIER": "snmpMIBGroups 11", "OID": "*******.*******.2.11", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBGroups.snmpWarmStartNotificationGroup"}, "snmpNotificationGroup": {"ObjectName": "snmpNotificationGroup", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "These objects are required for entities\r\n            which support notification originator applications.", "OBJECT_IDENTIFIER": "snmpMIBGroups 12", "OID": "*******.*******.2.12", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBGroups.snmpNotificationGroup"}, "snmpOutPkts": {"ObjectName": "snmpOutPkts", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Messages which were\r\n            passed from the SNMP protocol entity to the\r\n            transport service.", "OBJECT_IDENTIFIER": "snmp 2", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutPkts"}, "snmpInTooBigs": {"ObjectName": "snmpInTooBigs", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP PDUs which were\r\n            delivered to the SNMP protocol entity and for\r\n            which the value of the error-status field was\r\n            `tooBig'.", "OBJECT_IDENTIFIER": "snmp 8", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTooBigs"}, "snmpInNoSuchNames": {"ObjectName": "snmpInNoSuchNames", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP PDUs which were\r\n            delivered to the SNMP protocol entity and for\r\n            which the value of the error-status field was\r\n            `noSuchName'.", "OBJECT_IDENTIFIER": "snmp 9", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInNoSuchNames"}, "snmpInBadValues": {"ObjectName": "snmpInBadValues", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP PDUs which were\r\n            delivered to the SNMP protocol entity and for\r\n            which the value of the error-status field was\r\n            `badValue'.", "OBJECT_IDENTIFIER": "snmp 10", "OID": "*******.********0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadValues"}, "snmpInReadOnlys": {"ObjectName": "snmpInReadOnlys", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number valid SNMP PDUs which were delivered\r\n            to the SNMP protocol entity and for which the value\r\n            of the error-status field was `readOnly'.  It should\r\n            be noted that it is a protocol error to generate an\r\n            SNMP PDU which contains the value `readOnly' in the\r\n            error-status field, as such this object is provided\r\n            as a means of detecting incorrect implementations of\r\n            the SNMP.", "OBJECT_IDENTIFIER": "snmp 11", "OID": "*******.********1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInReadOnlys"}, "snmpInGenErrs": {"ObjectName": "snmpInGenErrs", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP PDUs which were delivered\r\n            to the SNMP protocol entity and for which the value\r\n            of the error-status field was `genErr'.", "OBJECT_IDENTIFIER": "snmp 12", "OID": "*******.********2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGenErrs"}, "snmpInTotalReqVars": {"ObjectName": "snmpInTotalReqVars", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of MIB objects which have been\r\n            retrieved successfully by the SNMP protocol entity\r\n            as the result of receiving valid SNMP Get-Request\r\n            and Get-Next PDUs.", "OBJECT_IDENTIFIER": "snmp 13", "OID": "*******.********3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTotalReqVars"}, "snmpInTotalSetVars": {"ObjectName": "snmpInTotalSetVars", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of MIB objects which have been\r\n            altered successfully by the SNMP protocol entity as\r\n            the result of receiving valid SNMP Set-Request PDUs.", "OBJECT_IDENTIFIER": "snmp 14", "OID": "*******.********4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTotalSetVars"}, "snmpInGetRequests": {"ObjectName": "snmpInGetRequests", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Get-Request PDUs which\r\n            have been accepted and processed by the SNMP\r\n            protocol entity.", "OBJECT_IDENTIFIER": "snmp 15", "OID": "*******.********5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGetRequests"}, "snmpInGetNexts": {"ObjectName": "snmpInGetNexts", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Get-Next PDUs which have been\r\n            accepted and processed by the SNMP protocol entity.", "OBJECT_IDENTIFIER": "snmp 16", "OID": "*******.********6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGetNexts"}, "snmpInSetRequests": {"ObjectName": "snmpInSetRequests", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Set-Request PDUs which\r\n            have been accepted and processed by the SNMP protocol\r\n            entity.", "OBJECT_IDENTIFIER": "snmp 17", "OID": "*******.********7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInSetRequests"}, "snmpInGetResponses": {"ObjectName": "snmpInGetResponses", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Get-Response PDUs which\r\n            have been accepted and processed by the SNMP protocol\r\n            entity.", "OBJECT_IDENTIFIER": "snmp 18", "OID": "*******.********8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGetResponses"}, "snmpInTraps": {"ObjectName": "snmpInTraps", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Trap PDUs which have been\r\n            accepted and processed by the SNMP protocol entity.", "OBJECT_IDENTIFIER": "snmp 19", "OID": "*******.********9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTraps"}, "snmpOutTooBigs": {"ObjectName": "snmpOutTooBigs", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP PDUs which were generated\r\n            by the SNMP protocol entity and for which the value\r\n            of the error-status field was `tooBig.'", "OBJECT_IDENTIFIER": "snmp 20", "OID": "*******.********0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutTooBigs"}, "snmpOutNoSuchNames": {"ObjectName": "snmpOutNoSuchNames", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP PDUs which were generated\r\n            by the SNMP protocol entity and for which the value\r\n            of the error-status was `noSuchName'.", "OBJECT_IDENTIFIER": "snmp 21", "OID": "*******.********1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutNoSuchNames"}, "snmpOutBadValues": {"ObjectName": "snmpOutBadValues", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP PDUs which were generated\r\n            by the SNMP protocol entity and for which the value\r\n            of the error-status field was `badValue'.", "OBJECT_IDENTIFIER": "snmp 22", "OID": "*******.********2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutBadValues"}, "snmpOutGenErrs": {"ObjectName": "snmpOutGenErrs", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP PDUs which were generated\r\n            by the SNMP protocol entity and for which the value\r\n            of the error-status field was `genErr'.", "OBJECT_IDENTIFIER": "snmp 24", "OID": "*******.********4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGenErrs"}, "snmpOutGetRequests": {"ObjectName": "snmpOutGetRequests", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Get-Request PDUs which\r\n            have been generated by the SNMP protocol entity.", "OBJECT_IDENTIFIER": "snmp 25", "OID": "*******.********5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGetRequests"}, "snmpOutGetNexts": {"ObjectName": "snmpOutGetNexts", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Get-Next PDUs which have\r\n            been generated by the SNMP protocol entity.", "OBJECT_IDENTIFIER": "snmp 26", "OID": "*******.********6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGetNexts"}, "snmpOutSetRequests": {"ObjectName": "snmpOutSetRequests", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Set-Request PDUs which\r\n            have been generated by the SNMP protocol entity.", "OBJECT_IDENTIFIER": "snmp 27", "OID": "*******.********7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutSetRequests"}, "snmpOutGetResponses": {"ObjectName": "snmpOutGetResponses", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Get-Response PDUs which\r\n            have been generated by the SNMP protocol entity.", "OBJECT_IDENTIFIER": "snmp 28", "OID": "*******.********8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGetResponses"}, "snmpOutTraps": {"ObjectName": "snmpOutTraps", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "MAX-ACCESS": "read-only", "STATUS": "obsolete", "DESCRIPTION": "The total number of SNMP Trap PDUs which have\r\n            been generated by the SNMP protocol entity.", "OBJECT_IDENTIFIER": "snmp 29", "OID": "*******.********9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutTraps"}, "snmpObsoleteGroup": {"ObjectName": "snmpObsoleteGroup", "ModuleName": "SNMPv2-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "obsolete", "DESCRIPTION": "A collection of objects from RFC 1213 made obsolete\r\n            by this MIB module.", "OBJECT_IDENTIFIER": "snmpMIBGroups 10", "OID": "*******.*******.2.10", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBConformance.snmpMIBGroups.snmpObsoleteGroup"}}, "RFC-1212": {"IMPORTS": {"RFC1155-SMI": ["ObjectName"], "RFC1158-MIB": ["DisplayString"]}, "OBJECT-TYPE": {"TYPE NOTATION": {"SYNTAX": "type(ObjectSyntax)", "ACCESS": "Access", "STATUS": "Status", "DescrPart": null, "ReferPart": null, "IndexPart": null, "DefValPart": null}, "VALUE NOTATION": {"value": "(VALUE ObjectName)"}, "Access": {"ObjectName": "Access", "ModuleName": "RFC-1212", "MACRO": "\"read-only\""}, "Status": {"ObjectName": "Status", "ModuleName": "RFC-1212", "MACRO": "\"mandatory\""}, "DescrPart": {"ObjectName": "DescrPart", "ModuleName": "RFC-1212", "MACRO": "\"DESCRIPTION\""}, "ReferPart": {"ObjectName": "ReferPart", "ModuleName": "RFC-1212", "MACRO": "\"REFERENCE\""}, "IndexPart": {"ObjectName": "IndexPart", "ModuleName": "RFC-1212", "MACRO": "\"INDEX\""}, "IndexTypes": {"ObjectName": "IndexTypes", "ModuleName": "RFC-1212", "MACRO": "IndexType"}, "IndexType": {"ObjectName": "IndexType", "ModuleName": "RFC-1212", "MACRO": "value"}, "DefValPart": {"ObjectName": "DefValPart", "ModuleName": "RFC-1212", "MACRO": "\"DEFVAL\""}}, "IndexSyntax": {"ObjectName": "IndexSyntax", "ModuleName": "RFC-1212", "MACRO": "CHOICE"}}, "RFC1213-MIB": {"IMPORTS": {"RFC1155-SMI": ["mgmt", "NetworkAddress", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Counter", "Gauge", "TimeTicks"], "RFC-1212": ["OBJECT-TYPE"]}, "mib-2": {"ObjectName": "mib-2", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mgmt 1", "OID": "*******.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2"}, "DisplayString": {"ObjectName": "DisplayString", "ModuleName": "RFC1213-MIB", "MACRO": "OCTET STRING"}, "PhysAddress": {"ObjectName": "PhysAdd<PERSON>", "ModuleName": "RFC1213-MIB", "MACRO": "OCTET STRING"}, "system": {"ObjectName": "system", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 1", "OID": "*******.2.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system"}, "interfaces": {"ObjectName": "interfaces", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 2", "OID": "*******.2.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces"}, "at": {"ObjectName": "at", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 3", "OID": "*******.2.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at"}, "ip": {"ObjectName": "ip", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 4", "OID": "*******.2.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip"}, "icmp": {"ObjectName": "icmp", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 5", "OID": "*******.2.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp"}, "tcp": {"ObjectName": "tcp", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 6", "OID": "*******.2.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp"}, "udp": {"ObjectName": "udp", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 7", "OID": "*******.2.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp"}, "egp": {"ObjectName": "egp", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 8", "OID": "*******.2.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp"}, "transmission": {"ObjectName": "transmission", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 10", "OID": "*******.2.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.transmission"}, "snmp": {"ObjectName": "snmp", "ModuleName": "RFC1213-MIB", "OBJECT_IDENTIFIER": "mib-2 11", "OID": "*******.2.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp"}, "sysDescr": {"ObjectName": "sysDescr", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysDescr"}, "sysObjectID": {"ObjectName": "sysObjectID", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysObjectID"}, "sysUpTime": {"ObjectName": "sysUpTime", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 3", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysUpTime"}, "sysContact": {"ObjectName": "sysContact", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 4", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysContact"}, "sysName": {"ObjectName": "sysName", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 5", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysName"}, "sysLocation": {"ObjectName": "sysLocation", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 6", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysLocation"}, "sysServices": {"ObjectName": "sysServices", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "system 7", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.system.sysServices"}, "ifNumber": {"ObjectName": "ifNumber", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "interfaces 1", "OID": "*******.2.1.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifNumber"}, "ifTable": {"ObjectName": "ifTable", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IfEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "interfaces 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable"}, "ifEntry": {"ObjectName": "ifEntry", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IfEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifTable 1", "OID": "*******.*******.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry"}, "IfEntry": {"ObjectName": "IfEntry", "ModuleName": "RFC1213-MIB", "MACRO": "SEQUENCE"}, "ifIndex": {"ObjectName": "ifIndex", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 1", "OID": "*******.*******.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifIndex"}, "ifDescr": {"ObjectName": "ifDescr", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 2", "OID": "*******.*******.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifDescr"}, "ifType": {"ObjectName": "ifType", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "regular1822", "3": "hdh1822", "4": "ddn-x25", "5": "rfc877-x25", "6": "ethernet-csmacd", "7": "iso88023-csmacd", "8": "iso88024-tokenBus", "9": "iso88025-tokenRing", "10": "iso88026-man", "11": "star<PERSON>an", "12": "proteon-10Mbit", "13": "proteon-80Mbit", "14": "hyperchannel", "15": "fddi", "16": "lapb", "17": "sdlc", "18": "ds1", "19": "e1", "20": "basicISDN", "21": "primaryISDN", "22": "propPointToPointSerial", "23": "ppp", "24": "softwareLoopback", "25": "eon", "26": "ethernet-3Mbit", "27": "nsip", "28": "slip", "29": "ultra", "30": "ds3", "31": "sip", "32": "frame-relay"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 3", "OID": "*******.*******.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifType"}, "ifMtu": {"ObjectName": "ifMtu", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 4", "OID": "*******.*******.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifMtu"}, "ifSpeed": {"ObjectName": "ifSpeed", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 5", "OID": "*******.*******.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifSpeed"}, "ifPhysAddress": {"ObjectName": "if<PERSON><PERSON>s<PERSON><PERSON><PERSON>", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "PhysAdd<PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 6", "OID": "*******.*******.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifPhysAddress"}, "ifAdminStatus": {"ObjectName": "ifAdminStatus", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "up", "2": "down", "3": "testing"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 7", "OID": "*******.*******.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifAdminStatus"}, "ifOperStatus": {"ObjectName": "ifOperStatus", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "up", "2": "down", "3": "testing"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 8", "OID": "*******.*******.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOperStatus"}, "ifLastChange": {"ObjectName": "ifLastChange", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 9", "OID": "*******.*******.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifLastChange"}, "ifInOctets": {"ObjectName": "ifInOctets", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 10", "OID": "*******.*******.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInOctets"}, "ifInUcastPkts": {"ObjectName": "ifInUcastPkts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 11", "OID": "*******.*******.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInUcastPkts"}, "ifInNUcastPkts": {"ObjectName": "ifInNUcastPkts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 12", "OID": "*******.*******.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInNUcastPkts"}, "ifInDiscards": {"ObjectName": "ifInDiscards", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 13", "OID": "*******.*******.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInDiscards"}, "ifInErrors": {"ObjectName": "ifInErrors", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 14", "OID": "*******.*******.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInErrors"}, "ifInUnknownProtos": {"ObjectName": "ifInUnknownProtos", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 15", "OID": "*******.*******.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInUnknownProtos"}, "ifOutOctets": {"ObjectName": "ifOutOctets", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 16", "OID": "*******.*******.1.16", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutOctets"}, "ifOutUcastPkts": {"ObjectName": "ifOutUcastPkts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 17", "OID": "*******.*******.1.17", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutUcastPkts"}, "ifOutNUcastPkts": {"ObjectName": "ifOutNUcastPkts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 18", "OID": "*******.*******.1.18", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutNUcastPkts"}, "ifOutDiscards": {"ObjectName": "ifOutDiscards", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 19", "OID": "*******.*******.1.19", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutDiscards"}, "ifOutErrors": {"ObjectName": "ifOutErrors", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 20", "OID": "*******.*******.1.20", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutErrors"}, "ifOutQLen": {"ObjectName": "ifOutQLen", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 21", "OID": "*******.*******.1.21", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutQLen"}, "ifSpecific": {"ObjectName": "ifSpecific", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ifEntry 22", "OID": "*******.*******.1.22", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifSpecific"}, "atTable": {"ObjectName": "atTable", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF AtEntry", "ACCESS": "not-accessible", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "at 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable"}, "atEntry": {"ObjectName": "atEntry", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "AtEntry", "ACCESS": "not-accessible", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "atTable 1", "OID": "*******.*******.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable.atEntry"}, "AtEntry": {"ObjectName": "AtEntry", "ModuleName": "RFC1213-MIB", "MACRO": "SEQUENCE"}, "atIfIndex": {"ObjectName": "atIfIndex", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "atEntry 1", "OID": "*******.*******.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable.atEntry.atIfIndex"}, "atPhysAddress": {"ObjectName": "atPhys<PERSON><PERSON><PERSON>", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "PhysAdd<PERSON>", "ACCESS": "read-write", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "atEntry 2", "OID": "*******.*******.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable.atEntry.atPhysAddress"}, "atNetAddress": {"ObjectName": "atNetAddress", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "NetworkAddress", "ACCESS": "read-write", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "atEntry 3", "OID": "*******.*******.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.at.atTable.atEntry.atNetAddress"}, "ipForwarding": {"ObjectName": "ipForwarding", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "forwarding", "2": "not-forwarding"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForwarding"}, "ipDefaultTTL": {"ObjectName": "ipDefaultTTL", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipDefaultTTL"}, "ipInReceives": {"ObjectName": "ipInReceives", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 3", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInReceives"}, "ipInHdrErrors": {"ObjectName": "ipInHdrErrors", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 4", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInHdrErrors"}, "ipInAddrErrors": {"ObjectName": "ipInAddrErrors", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 5", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInAddrErrors"}, "ipForwDatagrams": {"ObjectName": "ipForwDatagrams", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 6", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForwDatagrams"}, "ipInUnknownProtos": {"ObjectName": "ipInUnknownProtos", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 7", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInUnknownProtos"}, "ipInDiscards": {"ObjectName": "ipInDiscards", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 8", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInDiscards"}, "ipInDelivers": {"ObjectName": "ipInDelivers", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 9", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipInDelivers"}, "ipOutRequests": {"ObjectName": "ipOutRequests", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 10", "OID": "*******.*******0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipOutRequests"}, "ipOutDiscards": {"ObjectName": "ipOutDiscards", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 11", "OID": "*******.*******1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipOutDiscards"}, "ipOutNoRoutes": {"ObjectName": "ipOutNoRoutes", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 12", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipOutNoRoutes"}, "ipReasmTimeout": {"ObjectName": "ipReasmTimeout", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 13", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipReasmTimeout"}, "ipReasmReqds": {"ObjectName": "ipReasmReqds", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 14", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipReasmReqds"}, "ipReasmOKs": {"ObjectName": "ipReasmOKs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 15", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipReasmOKs"}, "ipReasmFails": {"ObjectName": "ipReasmFails", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 16", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipReasmFails"}, "ipFragOKs": {"ObjectName": "ipFragOKs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 17", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipFragOKs"}, "ipFragFails": {"ObjectName": "ipFragFails", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 18", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipFragFails"}, "ipFragCreates": {"ObjectName": "ipFragCreates", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 19", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipFragCreates"}, "ipAddrTable": {"ObjectName": "ipAddrTable", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IpAddrEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 20", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable"}, "ipAddrEntry": {"ObjectName": "ipAddrEntry", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IpAddrEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry"}, "IpAddrEntry": {"ObjectName": "IpAddrEntry", "ModuleName": "RFC1213-MIB", "MACRO": "SEQUENCE"}, "ipAdEntAddr": {"ObjectName": "ipAdEntAddr", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntAddr"}, "ipAdEntIfIndex": {"ObjectName": "ipAdEntIfIndex", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntIfIndex"}, "ipAdEntNetMask": {"ObjectName": "ipAdEntNetMask", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntNetMask"}, "ipAdEntBcastAddr": {"ObjectName": "ipAdEntBcastAddr", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntBcastAddr"}, "ipAdEntReasmMaxSize": {"ObjectName": "ipAdEntReasmMaxSize", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipAddrEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipAddrTable.ipAddrEntry.ipAdEntReasmMaxSize"}, "ipRouteTable": {"ObjectName": "ipRouteTable", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IpRouteEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 21", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRouteTable"}, "ipRouteEntry": {"ObjectName": "ipRouteEntry", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IpRouteEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRouteTable.ipRouteEntry"}, "IpRouteEntry": {"ObjectName": "IpRouteEntry", "ModuleName": "RFC1213-MIB", "MACRO": "SEQUENCE"}, "ipRouteDest": {"ObjectName": "ipRouteDest", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteDest"}, "ipRouteIfIndex": {"ObjectName": "ipRouteIfIndex", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteIfIndex"}, "ipRouteMetric1": {"ObjectName": "ipRouteMetric1", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMetric1"}, "ipRouteMetric2": {"ObjectName": "ipRouteMetric2", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMetric2"}, "ipRouteMetric3": {"ObjectName": "ipRouteMetric3", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMetric3"}, "ipRouteMetric4": {"ObjectName": "ipRouteMetric4", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 6", "OID": "*******.********.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMetric4"}, "ipRouteNextHop": {"ObjectName": "ipRouteNextHop", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 7", "OID": "*******.********.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteNextHop"}, "ipRouteType": {"ObjectName": "ipRouteType", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "invalid", "3": "direct", "4": "indirect"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 8", "OID": "*******.********.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteType"}, "ipRouteProto": {"ObjectName": "ipRouteProto", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "local", "3": "netmgmt", "4": "icmp", "5": "egp", "6": "ggp", "7": "hello", "8": "rip", "9": "is-is", "10": "es-is", "11": "ciscoIgrp", "12": "bbnSpfIgp", "13": "ospf", "14": "bgp"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 9", "OID": "*******.********.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteProto"}, "ipRouteAge": {"ObjectName": "ipRouteAge", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 10", "OID": "*******.********.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteAge"}, "ipRouteMask": {"ObjectName": "ipRouteMask", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 11", "OID": "*******.********.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMask"}, "ipRouteMetric5": {"ObjectName": "ipRouteMetric5", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 12", "OID": "*******.********.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteMetric5"}, "ipRouteInfo": {"ObjectName": "ipRouteInfo", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipRouteEntry 13", "OID": "*******.********.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingTable.ipRouteEntry.ipRouteInfo"}, "ipNetToMediaTable": {"ObjectName": "ipNetToMediaTable", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IpNetToMediaEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 22", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable"}, "ipNetToMediaEntry": {"ObjectName": "ipNetToMediaEntry", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IpNetToMediaEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry"}, "IpNetToMediaEntry": {"ObjectName": "IpNetToMediaEntry", "ModuleName": "RFC1213-MIB", "MACRO": "SEQUENCE"}, "ipNetToMediaIfIndex": {"ObjectName": "ipNetToMediaIfIndex", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry.ipNetToMediaIfIndex"}, "ipNetToMediaPhysAddress": {"ObjectName": "ipNetToMediaPhysAddress", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "PhysAdd<PERSON>", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry.ipNetToMediaPhysAddress"}, "ipNetToMediaNetAddress": {"ObjectName": "ipNetToMediaNetAddress", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry.ipNetToMediaNetAddress"}, "ipNetToMediaType": {"ObjectName": "ipNetToMediaType", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "invalid", "3": "dynamic", "4": "static"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ipNetToMediaEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipNetToMediaTable.ipNetToMediaEntry.ipNetToMediaType"}, "ipRoutingDiscards": {"ObjectName": "ipRoutingDiscards", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "ip 23", "OID": "*******.*******3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipRoutingDiscards"}, "icmpInMsgs": {"ObjectName": "icmpInMsgs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInMsgs"}, "icmpInErrors": {"ObjectName": "icmpInErrors", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInErrors"}, "icmpInDestUnreachs": {"ObjectName": "icmpInDestUnreachs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 3", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInDestUnreachs"}, "icmpInTimeExcds": {"ObjectName": "icmpInTimeExcds", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 4", "OID": "*******.2.1.5.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInTimeExcds"}, "icmpInParmProbs": {"ObjectName": "icmpInParmProbs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 5", "OID": "*******.2.1.5.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInParmProbs"}, "icmpInSrcQuenchs": {"ObjectName": "icmpInSrcQuenchs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 6", "OID": "*******.2.1.5.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInSrcQuenchs"}, "icmpInRedirects": {"ObjectName": "icmpInRedirects", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 7", "OID": "*******.2.1.5.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInRedirects"}, "icmpInEchos": {"ObjectName": "icmpInEchos", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 8", "OID": "*******.2.1.5.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInEchos"}, "icmpInEchoReps": {"ObjectName": "icmpInEchoReps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 9", "OID": "*******.2.1.5.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInEchoReps"}, "icmpInTimestamps": {"ObjectName": "icmpInTimestamps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 10", "OID": "*******.*******0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInTimestamps"}, "icmpInTimestampReps": {"ObjectName": "icmpInTimestampReps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 11", "OID": "*******.*******1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInTimestampReps"}, "icmpInAddrMasks": {"ObjectName": "icmpInAddrMasks", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 12", "OID": "*******.*******2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInAddrMasks"}, "icmpInAddrMaskReps": {"ObjectName": "icmpInAddrMaskReps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 13", "OID": "*******.*******3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpInAddrMaskReps"}, "icmpOutMsgs": {"ObjectName": "icmpOutMsgs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 14", "OID": "*******.*******4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutMsgs"}, "icmpOutErrors": {"ObjectName": "icmpOutErrors", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 15", "OID": "*******.*******5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutErrors"}, "icmpOutDestUnreachs": {"ObjectName": "icmpOutDestUnreachs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 16", "OID": "*******.*******6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutDestUnreachs"}, "icmpOutTimeExcds": {"ObjectName": "icmpOutTimeExcds", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 17", "OID": "*******.*******7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutTimeExcds"}, "icmpOutParmProbs": {"ObjectName": "icmpOutParmProbs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 18", "OID": "*******.*******8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutParmProbs"}, "icmpOutSrcQuenchs": {"ObjectName": "icmpOutSrcQuenchs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 19", "OID": "*******.*******9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutSrcQuenchs"}, "icmpOutRedirects": {"ObjectName": "icmpOutRedirects", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 20", "OID": "*******.*******0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutRedirects"}, "icmpOutEchos": {"ObjectName": "icmpOutEchos", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 21", "OID": "*******.*******1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutEchos"}, "icmpOutEchoReps": {"ObjectName": "icmpOutEchoReps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 22", "OID": "*******.*******2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutEchoReps"}, "icmpOutTimestamps": {"ObjectName": "icmpOutTimestamps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 23", "OID": "*******.*******3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutTimestamps"}, "icmpOutTimestampReps": {"ObjectName": "icmpOutTimestampReps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 24", "OID": "*******.*******4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutTimestampReps"}, "icmpOutAddrMasks": {"ObjectName": "icmpOutAddrMasks", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 25", "OID": "*******.*******5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutAddrMasks"}, "icmpOutAddrMaskReps": {"ObjectName": "icmpOutAddrMaskReps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "icmp 26", "OID": "*******.*******6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.icmp.icmpOutAddrMaskReps"}, "tcpRtoAlgorithm": {"ObjectName": "tcpRtoAlgorithm", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "constant", "3": "rsre", "4": "vanj"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 1", "OID": "*******.2.1.6.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpRtoAlgorithm"}, "tcpRtoMin": {"ObjectName": "tcpRtoMin", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 2", "OID": "*******.2.1.6.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpRtoMin"}, "tcpRtoMax": {"ObjectName": "tcpRtoMax", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 3", "OID": "*******.2.1.6.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpRtoMax"}, "tcpMaxConn": {"ObjectName": "tcpMaxConn", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 4", "OID": "*******.2.1.6.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpMaxConn"}, "tcpActiveOpens": {"ObjectName": "tcpActiveOpens", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 5", "OID": "*******.2.1.6.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpActiveOpens"}, "tcpPassiveOpens": {"ObjectName": "tcpPassiveOpens", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 6", "OID": "*******.2.1.6.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpPassiveOpens"}, "tcpAttemptFails": {"ObjectName": "tcpAttemptFails", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 7", "OID": "*******.2.1.6.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpAttemptFails"}, "tcpEstabResets": {"ObjectName": "tcpEstabResets", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 8", "OID": "*******.2.1.6.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpEstabResets"}, "tcpCurrEstab": {"ObjectName": "tcpCurrEstab", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 9", "OID": "*******.2.1.6.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpCurrEstab"}, "tcpInSegs": {"ObjectName": "tcpInSegs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 10", "OID": "*******.2.1.6.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpInSegs"}, "tcpOutSegs": {"ObjectName": "tcpOutSegs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 11", "OID": "*******.2.1.6.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpOutSegs"}, "tcpRetransSegs": {"ObjectName": "tcpRetransSegs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 12", "OID": "*******.2.1.6.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpRetransSegs"}, "tcpConnTable": {"ObjectName": "tcpConnTable", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF TcpConnEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 13", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable"}, "tcpConnEntry": {"ObjectName": "tcpConnEntry", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TcpConnEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry"}, "TcpConnEntry": {"ObjectName": "TcpConnEntry", "ModuleName": "RFC1213-MIB", "MACRO": "SEQUENCE"}, "tcpConnState": {"ObjectName": "tcpConnState", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "closed", "2": "listen", "3": "synSent", "4": "synReceived", "5": "established", "6": "finWait1", "7": "finWait2", "8": "closeWait", "9": "lastAck", "10": "closing", "11": "timeWait", "12": "deleteTCB"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnState"}, "tcpConnLocalAddress": {"ObjectName": "tcpConnLocalAddress", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnLocalAddress"}, "tcpConnLocalPort": {"ObjectName": "tcpConnLocalPort", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnLocalPort"}, "tcpConnRemAddress": {"ObjectName": "tcpConnRemAddress", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnRemAddress"}, "tcpConnRemPort": {"ObjectName": "tcpConnRemPort", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcpConnEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpConnTable.tcpConnEntry.tcpConnRemPort"}, "tcpInErrs": {"ObjectName": "tcpInErrs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 14", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpInErrs"}, "tcpOutRsts": {"ObjectName": "tcpOutRsts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "tcp 15", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.tcp.tcpOutRsts"}, "udpInDatagrams": {"ObjectName": "udpInDatagrams", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 1", "OID": "*******.2.1.7.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpInDatagrams"}, "udpNoPorts": {"ObjectName": "udpNoPorts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 2", "OID": "*******.2.1.7.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpNoPorts"}, "udpInErrors": {"ObjectName": "udpInErrors", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 3", "OID": "*******.2.1.7.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpInErrors"}, "udpOutDatagrams": {"ObjectName": "udpOutDatagrams", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 4", "OID": "*******.2.1.7.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpOutDatagrams"}, "udpTable": {"ObjectName": "udpTable", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF UdpEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udp 5", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpTable"}, "udpEntry": {"ObjectName": "udpEntry", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "UdpEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udpTable 1", "OID": "*******.*******.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpTable.udpEntry"}, "UdpEntry": {"ObjectName": "UdpEntry", "ModuleName": "RFC1213-MIB", "MACRO": "SEQUENCE"}, "udpLocalAddress": {"ObjectName": "udpLocalAddress", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udpEntry 1", "OID": "*******.*******.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpTable.udpEntry.udpLocalAddress"}, "udpLocalPort": {"ObjectName": "udpLocalPort", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "udpEntry 2", "OID": "*******.*******.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.udp.udpTable.udpEntry.udpLocalPort"}, "egpInMsgs": {"ObjectName": "egpInMsgs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 1", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpInMsgs"}, "egpInErrors": {"ObjectName": "egpInErrors", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpInErrors"}, "egpOutMsgs": {"ObjectName": "egpOutMsgs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 3", "OID": "*******.2.1.8.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpOutMsgs"}, "egpOutErrors": {"ObjectName": "egpOutErrors", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 4", "OID": "*******.2.1.8.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpOutErrors"}, "egpNeighTable": {"ObjectName": "egpNeighTable", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF EgpNeighEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 5", "OID": "*******.2.1.8.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable"}, "egpNeighEntry": {"ObjectName": "egpNeighEntry", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "EgpNeighEntry", "ACCESS": "not-accessible", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighTable 1", "OID": "*******.2.1.8.5.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry"}, "EgpNeighEntry": {"ObjectName": "EgpNeighEntry", "ModuleName": "RFC1213-MIB", "MACRO": "SEQUENCE"}, "egpNeighState": {"ObjectName": "egpNeighState", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "idle", "2": "acquisition", "3": "down", "4": "up", "5": "cease"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 1", "OID": "*******.2.1.8.5.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighState"}, "egpNeighAddr": {"ObjectName": "egpNeighAddr", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 2", "OID": "*******.2.1.8.5.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighAddr"}, "egpNeighAs": {"ObjectName": "egpNeighAs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 3", "OID": "*******.2.1.8.5.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighAs"}, "egpNeighInMsgs": {"ObjectName": "egpNeighInMsgs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 4", "OID": "*******.2.1.8.5.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighInMsgs"}, "egpNeighInErrs": {"ObjectName": "egpNeighInErrs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 5", "OID": "*******.2.1.8.5.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighInErrs"}, "egpNeighOutMsgs": {"ObjectName": "egpNeighOutMsgs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 6", "OID": "*******.2.1.8.5.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighOutMsgs"}, "egpNeighOutErrs": {"ObjectName": "egpNeighOutErrs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 7", "OID": "*******.2.1.8.5.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighOutErrs"}, "egpNeighInErrMsgs": {"ObjectName": "egpNeighInErrMsgs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 8", "OID": "*******.2.1.8.5.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighInErrMsgs"}, "egpNeighOutErrMsgs": {"ObjectName": "egpNeighOutErrMsgs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 9", "OID": "*******.2.1.8.5.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighOutErrMsgs"}, "egpNeighStateUps": {"ObjectName": "egpNeighStateUps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 10", "OID": "*******.2.1.8.5.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighStateUps"}, "egpNeighStateDowns": {"ObjectName": "egpNeighStateDowns", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 11", "OID": "*******.2.1.8.5.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighStateDowns"}, "egpNeighIntervalHello": {"ObjectName": "egpNeighIntervalHello", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 12", "OID": "*******.2.1.8.5.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighIntervalHello"}, "egpNeighIntervalPoll": {"ObjectName": "egpNeighIntervalPoll", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 13", "OID": "*******.2.1.8.5.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighIntervalPoll"}, "egpNeighMode": {"ObjectName": "egpNeighMode", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "active", "2": "passive"}}, "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 14", "OID": "*******.2.1.8.5.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighMode"}, "egpNeighEventTrigger": {"ObjectName": "egpNeighEventTrigger", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "start", "2": "stop"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egpNeighEntry 15", "OID": "*******.2.1.8.5.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpNeighTable.egpNeighEntry.egpNeighEventTrigger"}, "egpAs": {"ObjectName": "egpAs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "egp 6", "OID": "*******.2.1.8.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.egp.egpAs"}, "snmpInPkts": {"ObjectName": "snmpInPkts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 1", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInPkts"}, "snmpOutPkts": {"ObjectName": "snmpOutPkts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 2", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutPkts"}, "snmpInBadVersions": {"ObjectName": "snmpInBadVersions", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 3", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadVersions"}, "snmpInBadCommunityNames": {"ObjectName": "snmpInBadCommunityNames", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 4", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadCommunityNames"}, "snmpInBadCommunityUses": {"ObjectName": "snmpInBadCommunityUses", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 5", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadCommunityUses"}, "snmpInASNParseErrs": {"ObjectName": "snmpInASNParseErrs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 6", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInASNParseErrs"}, "snmpInTooBigs": {"ObjectName": "snmpInTooBigs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 8", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTooBigs"}, "snmpInNoSuchNames": {"ObjectName": "snmpInNoSuchNames", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 9", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInNoSuchNames"}, "snmpInBadValues": {"ObjectName": "snmpInBadValues", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 10", "OID": "*******.********0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInBadValues"}, "snmpInReadOnlys": {"ObjectName": "snmpInReadOnlys", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 11", "OID": "*******.********1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInReadOnlys"}, "snmpInGenErrs": {"ObjectName": "snmpInGenErrs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 12", "OID": "*******.********2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGenErrs"}, "snmpInTotalReqVars": {"ObjectName": "snmpInTotalReqVars", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 13", "OID": "*******.********3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTotalReqVars"}, "snmpInTotalSetVars": {"ObjectName": "snmpInTotalSetVars", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 14", "OID": "*******.********4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTotalSetVars"}, "snmpInGetRequests": {"ObjectName": "snmpInGetRequests", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 15", "OID": "*******.********5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGetRequests"}, "snmpInGetNexts": {"ObjectName": "snmpInGetNexts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 16", "OID": "*******.********6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGetNexts"}, "snmpInSetRequests": {"ObjectName": "snmpInSetRequests", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 17", "OID": "*******.********7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInSetRequests"}, "snmpInGetResponses": {"ObjectName": "snmpInGetResponses", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 18", "OID": "*******.********8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInGetResponses"}, "snmpInTraps": {"ObjectName": "snmpInTraps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 19", "OID": "*******.********9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpInTraps"}, "snmpOutTooBigs": {"ObjectName": "snmpOutTooBigs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 20", "OID": "*******.********0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutTooBigs"}, "snmpOutNoSuchNames": {"ObjectName": "snmpOutNoSuchNames", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 21", "OID": "*******.********1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutNoSuchNames"}, "snmpOutBadValues": {"ObjectName": "snmpOutBadValues", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 22", "OID": "*******.********2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutBadValues"}, "snmpOutGenErrs": {"ObjectName": "snmpOutGenErrs", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 24", "OID": "*******.********4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGenErrs"}, "snmpOutGetRequests": {"ObjectName": "snmpOutGetRequests", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 25", "OID": "*******.********5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGetRequests"}, "snmpOutGetNexts": {"ObjectName": "snmpOutGetNexts", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 26", "OID": "*******.********6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGetNexts"}, "snmpOutSetRequests": {"ObjectName": "snmpOutSetRequests", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 27", "OID": "*******.********7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutSetRequests"}, "snmpOutGetResponses": {"ObjectName": "snmpOutGetResponses", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 28", "OID": "*******.********8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutGetResponses"}, "snmpOutTraps": {"ObjectName": "snmpOutTraps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter", "ACCESS": "read-only", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 29", "OID": "*******.********9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpOutTraps"}, "snmpEnableAuthenTraps": {"ObjectName": "snmpEnableAuthenTraps", "ModuleName": "RFC1213-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "enabled", "2": "disabled"}}, "ACCESS": "read-write", "STATUS": "mandatory", "OBJECT_IDENTIFIER": "snmp 30", "OID": "*******.********0", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpEnableAuthenTraps"}}, "IANAifType-MIB": {"IMPORTS": {"SNMPv2-SMI": ["MODULE-IDENTITY", "mib-2"], "SNMPv2-TC": ["TEXTUAL-CONVENTION"]}, "ianaifType": {"ObjectName": "ianaifType", "ModuleName": "IANAifType-MIB", "MACRO": "MODULE-IDENTITY", "LAST-UPDATED": "201205170000Z", "ORGANIZATION": "IANA", "CONTACT-INFO": "        Internet Assigned Numbers Authority\r\n\r\n                     Postal: ICANN\r\n                             4676 Admiralty Way, Suite 330\r\n                             Marina del Rey, CA 90292\r\n\r\n                     Tel:    ****** 823 9358\r\n                     E-Mail: iana&amp;iana.org", "DESCRIPTION": "Initial version of this MIB as published in\r\n                     RFC 1573.", "OBJECT_IDENTIFIER": "mib-2 30", "OID": "*******.2.1.30", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ianaifType"}, "IANAifType": {"ObjectName": "IANAifType", "ModuleName": "IANAifType-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"1": "other", "2": "regular1822", "3": "hdh1822", "4": "ddnX25", "5": "rfc877x25", "6": "ethernetCsmacd", "7": "iso88023Csmacd", "8": "iso88024TokenBus", "9": "iso88025TokenRing", "10": "iso88026Man", "11": "star<PERSON>an", "12": "proteon10Mbit", "13": "proteon80Mbit", "14": "hyperchannel", "15": "fddi", "16": "lapb", "17": "sdlc", "18": "ds1", "19": "e1", "20": "basicISDN", "21": "primaryISDN", "22": "propPointToPointSerial", "23": "ppp", "24": "softwareLoopback", "25": "eon", "26": "ethernet3Mbit", "27": "nsip", "28": "slip", "29": "ultra", "30": "ds3", "31": "sip", "32": "frameRelay", "33": "rs232", "34": "para", "35": "arcnet", "36": "arcnetPlus", "37": "atm", "38": "miox25", "39": "sonet", "40": "x25ple", "41": "iso88022llc", "42": "localTalk", "43": "smdsDxi", "44": "frameRelayService", "45": "v35", "46": "hssi", "47": "hippi", "48": "modem", "49": "aal5", "50": "sonetPath", "51": "sonetVT", "52": "smdsIcip", "53": "propVirtual", "54": "propMultiplexor", "55": "ieee80212", "56": "fibreChannel", "57": "hippiInterface", "58": "frameRelayInterconnect", "59": "aflane8023", "60": "aflane8025", "61": "cctEmul", "62": "fastEther", "63": "isdn", "64": "v11", "65": "v36", "66": "g703at64k", "67": "g703at2mb", "68": "qllc", "69": "fastEtherFX", "70": "channel", "71": "ieee80211", "72": "ibm370parChan", "73": "escon", "74": "dlsw", "75": "isdns", "76": "isdnu", "77": "lapd", "78": "ipSwitch", "79": "rsrb", "80": "atmLogical", "81": "ds0", "82": "ds0Bundle", "83": "bsc", "84": "async", "85": "cnr", "86": "iso88025Dtr", "87": "eplrs", "88": "arap", "89": "propCnls", "90": "hostPad", "91": "termPad", "92": "frameRelayMPI", "93": "x213", "94": "adsl", "95": "radsl", "96": "sdsl", "97": "vdsl", "98": "iso88025CRFPInt", "99": "my<PERSON>t", "100": "voiceEM", "101": "voiceFXO", "102": "voiceFXS", "103": "voiceEncap", "104": "voiceOverIp", "105": "atmDxi", "106": "atmFuni", "107": "atmIma", "108": "pppMultilinkBundle", "109": "ipOverCdlc", "110": "ipOverClaw", "111": "stackToStack", "112": "virtualIpAddress", "113": "mpc", "114": "ipOverAtm", "115": "iso88025Fiber", "116": "tdlc", "117": "gigabitEthernet", "118": "hdlc", "119": "lapf", "120": "v37", "121": "x25mlp", "122": "x25huntGroup", "123": "transpHdlc", "124": "interleave", "125": "fast", "126": "ip", "127": "docsCableMaclayer", "128": "docsCableDownstream", "129": "docsCableUpstream", "130": "a12MppSwitch", "131": "tunnel", "132": "coffee", "133": "ces", "134": "atmSubInterface", "135": "l2vlan", "136": "l3ipvlan", "137": "l3ipxvlan", "138": "digitalPowerline", "139": "mediaMailOverIp", "140": "dtm", "141": "dcn", "142": "ipForward", "143": "msdsl", "144": "ieee1394", "145": "if-gsn", "146": "dvbRccMacLayer", "147": "dvbRccDownstream", "148": "dvbRccUpstream", "149": "atmVirtual", "150": "mplsTunnel", "151": "srp", "152": "voiceOverAtm", "153": "voiceOverFrameRelay", "154": "idsl", "155": "compositeLink", "156": "ss7SigLink", "157": "propWirelessP2P", "158": "frForward", "159": "rfc1483", "160": "usb", "161": "ieee8023adLag", "162": "bgpp<PERSON><PERSON><PERSON><PERSON>", "163": "frf16MfrBundle", "164": "h323Gatekeeper", "165": "h323Proxy", "166": "mpls", "167": "mfSigLink", "168": "hdsl2", "169": "shdsl", "170": "ds1FDL", "171": "pos", "172": "dvbAsiIn", "173": "dvbAsiOut", "174": "plc", "175": "nfas", "176": "tr008", "177": "gr303RDT", "178": "gr303IDT", "179": "isup", "180": "propDocsWirelessMaclayer", "181": "propDocsWirelessDownstream", "182": "propDocsWirelessUpstream", "183": "hiperlan2", "184": "propBWAp2Mp", "185": "sonetOverheadChannel", "186": "digitalWrapperOverheadChannel", "187": "aal2", "188": "radioMAC", "189": "atmRadio", "190": "imt", "191": "mvl", "192": "reachDSL", "193": "frDlciEndPt", "194": "atmVciEndPt", "195": "opticalChannel", "196": "opticalTransport", "197": "propAtm", "198": "voiceOverCable", "199": "infiniband", "200": "teLink", "201": "q2931", "202": "virtualTg", "203": "sipTg", "204": "sipSig", "205": "docsCableUpstreamChannel", "206": "econet", "207": "pon155", "208": "pon622", "209": "bridge", "210": "linegroup", "211": "voiceEMFGD", "212": "voiceFGDEANA", "213": "voiceDID", "214": "mpegTransport", "215": "sixToFour", "216": "gtp", "217": "pdnEtherLoop1", "218": "pdnEtherLoop2", "219": "opticalChannelGroup", "220": "homepna", "221": "gfp", "222": "ciscoISLvlan", "223": "actelisMetaLOOP", "224": "fcipLink", "225": "rpr", "226": "qam", "227": "lmp", "228": "cblVectaStar", "229": "docsCableMCmtsDownstream", "230": "adsl2", "231": "macSecControlledIF", "232": "macSecUncontrolledIF", "233": "aviciOpticalEther", "234": "atmbond", "235": "voiceFGDOS", "236": "mocaVersion1", "237": "ieee80216WMAN", "238": "adsl2plus", "239": "dvbRcsMacLayer", "240": "dvbTdm", "241": "dvbRcsTdma", "242": "x86Laps", "243": "wwanPP", "244": "wwanPP2", "245": "voiceEBS", "246": "ifPwType", "247": "ilan", "248": "pip", "249": "aluELP", "250": "gpon", "251": "vdsl2", "252": "capwapDot11Profile", "253": "capwapDot11Bss", "254": "capwapWtpVirtualRadio", "255": "bits", "256": "docsCableUpstreamRfPort", "257": "cableDownstreamRfPort", "258": "vmwareVirtualNic", "259": "ieee802154", "260": "otnOdu", "261": "otnOtu", "262": "ifVfiType", "263": "g9981", "264": "g9982", "265": "g9983", "266": "aluEpon", "267": "alu<PERSON><PERSON><PERSON><PERSON><PERSON>", "268": "aluEponPhysicalUni", "269": "aluEponLogicalLink", "270": "alu<PERSON><PERSON>n<PERSON><PERSON>", "271": "aluGponPhysicalUni", "272": "vmwareNicTeam"}}}, "IANAtunnelType": {"ObjectName": "IANAtunnelType", "ModuleName": "IANAifType-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"1": "other", "2": "direct", "3": "gre", "4": "minimal", "5": "l2tp", "6": "pptp", "7": "l2f", "8": "udp", "9": "atmp", "10": "msdp", "11": "sixToFour", "12": "sixOverFour", "13": "isatap", "14": "teredo", "15": "ipHttps"}}}}, "IF-MIB": {"IMPORTS": {"SNMPv2-SMI": ["MODULE-IDENTITY", "OBJECT-TYPE", "Counter32", "Gauge32", "Counter64", "Integer32", "TimeTicks", "mib-2", "NOTIFICATION-TYPE"], "SNMPv2-TC": ["TEXTUAL-CONVENTION", "DisplayString", "PhysAdd<PERSON>", "TruthValue", "RowStatus", "TimeStamp", "AutonomousType", "TestAndIncr"], "SNMPv2-CONF": ["MODULE-COMPLIANCE", "OBJECT-GROUP", "NOTIFICATION-GROUP"], "SNMPv2-MIB": ["snmpTraps"], "IANAifType-MIB": ["IANAifType"]}, "ifMIB": {"ObjectName": "ifMIB", "ModuleName": "IF-MIB", "MACRO": "MODULE-IDENTITY", "LAST-UPDATED": "200006140000Z", "ORGANIZATION": "IETF Interfaces MIB Working Group", "CONTACT-INFO": "   <PERSON>isco Systems, Inc.\r\n                170 West Tasman Drive\r\n                San Jose, CA  95134-1706\r\n                US\r\n\r\n                408-526-5260\r\n                <EMAIL>", "DESCRIPTION": "Initial revision, published as part of RFC 1573.", "OBJECT_IDENTIFIER": "mib-2 31", "OID": "*******.2.1.31", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB"}, "ifMIBObjects": {"ObjectName": "ifMIBObjects", "ModuleName": "IF-MIB", "OBJECT_IDENTIFIER": "ifMIB 1", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects"}, "interfaces": {"ObjectName": "interfaces", "ModuleName": "IF-MIB", "OBJECT_IDENTIFIER": "mib-2 2", "OID": "*******.2.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces"}, "OwnerString": {"ObjectName": "OwnerString", "ModuleName": "IF-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "InterfaceIndex": {"ObjectName": "InterfaceIndex", "ModuleName": "IF-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Integer32"}, "InterfaceIndexOrZero": {"ObjectName": "InterfaceIndexOrZero", "ModuleName": "IF-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Integer32"}, "ifNumber": {"ObjectName": "ifNumber", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "interfaces 1", "OID": "*******.2.1.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifNumber"}, "ifTableLastChange": {"ObjectName": "ifTableLastChange", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "STATUS": "current", "OBJECT_IDENTIFIER": "ifMIBObjects 5", "OID": "*******.********.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifTableLastChange"}, "ifTable": {"ObjectName": "ifTable", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IfEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "interfaces 2", "OID": "*******.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable"}, "ifEntry": {"ObjectName": "ifEntry", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IfEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ifTable 1", "OID": "*******.*******.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry"}, "IfEntry": {"ObjectName": "IfEntry", "ModuleName": "IF-MIB", "MACRO": "SEQUENCE"}, "ifIndex": {"ObjectName": "ifIndex", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InterfaceIndex", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 1", "OID": "*******.*******.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifIndex"}, "ifDescr": {"ObjectName": "ifDescr", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 2", "OID": "*******.*******.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifDescr"}, "ifType": {"ObjectName": "ifType", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IANAifType", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 3", "OID": "*******.*******.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifType"}, "ifMtu": {"ObjectName": "ifMtu", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 4", "OID": "*******.*******.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifMtu"}, "ifSpeed": {"ObjectName": "ifSpeed", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 5", "OID": "*******.*******.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifSpeed"}, "ifPhysAddress": {"ObjectName": "if<PERSON><PERSON>s<PERSON><PERSON><PERSON>", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "PhysAdd<PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 6", "OID": "*******.*******.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifPhysAddress"}, "ifAdminStatus": {"ObjectName": "ifAdminStatus", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "up", "2": "down", "3": "testing"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 7", "OID": "*******.*******.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifAdminStatus"}, "ifOperStatus": {"ObjectName": "ifOperStatus", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "up", "2": "down", "3": "testing", "4": "unknown", "5": "dormant", "6": "notPresent", "7": "lowerLayerDown"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 8", "OID": "*******.*******.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOperStatus"}, "ifLastChange": {"ObjectName": "ifLastChange", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 9", "OID": "*******.*******.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifLastChange"}, "ifInOctets": {"ObjectName": "ifInOctets", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 10", "OID": "*******.*******.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInOctets"}, "ifInUcastPkts": {"ObjectName": "ifInUcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 11", "OID": "*******.*******.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInUcastPkts"}, "ifInNUcastPkts": {"ObjectName": "ifInNUcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifEntry 12", "OID": "*******.*******.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInNUcastPkts"}, "ifInDiscards": {"ObjectName": "ifInDiscards", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 13", "OID": "*******.*******.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInDiscards"}, "ifInErrors": {"ObjectName": "ifInErrors", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 14", "OID": "*******.*******.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInErrors"}, "ifInUnknownProtos": {"ObjectName": "ifInUnknownProtos", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 15", "OID": "*******.*******.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifInUnknownProtos"}, "ifOutOctets": {"ObjectName": "ifOutOctets", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 16", "OID": "*******.*******.1.16", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutOctets"}, "ifOutUcastPkts": {"ObjectName": "ifOutUcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 17", "OID": "*******.*******.1.17", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutUcastPkts"}, "ifOutNUcastPkts": {"ObjectName": "ifOutNUcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifEntry 18", "OID": "*******.*******.1.18", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutNUcastPkts"}, "ifOutDiscards": {"ObjectName": "ifOutDiscards", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 19", "OID": "*******.*******.1.19", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutDiscards"}, "ifOutErrors": {"ObjectName": "ifOutErrors", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifEntry 20", "OID": "*******.*******.1.20", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutErrors"}, "ifOutQLen": {"ObjectName": "ifOutQLen", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge32", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifEntry 21", "OID": "*******.*******.1.21", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifOutQLen"}, "ifSpecific": {"ObjectName": "ifSpecific", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifEntry 22", "OID": "*******.*******.1.22", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.interfaces.ifTable.ifEntry.ifSpecific"}, "ifXTable": {"ObjectName": "ifXTable", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IfXEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ifMIBObjects 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable"}, "ifXEntry": {"ObjectName": "ifXEntry", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IfXEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXTable 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry"}, "IfXEntry": {"ObjectName": "IfXEntry", "ModuleName": "IF-MIB", "MACRO": "SEQUENCE"}, "ifName": {"ObjectName": "ifName", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 1", "OID": "*******.********.1.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifName"}, "ifInMulticastPkts": {"ObjectName": "ifInMulticastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 2", "OID": "*******.********.1.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifInMulticastPkts"}, "ifInBroadcastPkts": {"ObjectName": "ifInBroadcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 3", "OID": "*******.********.1.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifInBroadcastPkts"}, "ifOutMulticastPkts": {"ObjectName": "ifOutMulticastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 4", "OID": "*******.********.1.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifOutMulticastPkts"}, "ifOutBroadcastPkts": {"ObjectName": "ifOutBroadcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 5", "OID": "*******.********.1.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifOutBroadcastPkts"}, "ifHCInOctets": {"ObjectName": "ifHCInOctets", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter64", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 6", "OID": "*******.********.1.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifHCInOctets"}, "ifHCInUcastPkts": {"ObjectName": "ifHCInUcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter64", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 7", "OID": "*******.********.1.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifHCInUcastPkts"}, "ifHCInMulticastPkts": {"ObjectName": "ifHCInMulticastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter64", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 8", "OID": "*******.********.1.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifHCInMulticastPkts"}, "ifHCInBroadcastPkts": {"ObjectName": "ifHCInBroadcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter64", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 9", "OID": "*******.********.1.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifHCInBroadcastPkts"}, "ifHCOutOctets": {"ObjectName": "ifHCOutOctets", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter64", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 10", "OID": "*******.********.1.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifHCOutOctets"}, "ifHCOutUcastPkts": {"ObjectName": "ifHCOutUcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter64", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 11", "OID": "*******.********.1.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifHCOutUcastPkts"}, "ifHCOutMulticastPkts": {"ObjectName": "ifHCOutMulticastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter64", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 12", "OID": "*******.********.1.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifHCOutMulticastPkts"}, "ifHCOutBroadcastPkts": {"ObjectName": "ifHCOutBroadcastPkts", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter64", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 13", "OID": "*******.********.1.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifHCOutBroadcastPkts"}, "ifLinkUpDownTrapEnable": {"ObjectName": "ifLinkUpDownTrapEnable", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "enabled", "2": "disabled"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 14", "OID": "*******.********.1.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifLinkUpDownTrapEnable"}, "ifHighSpeed": {"ObjectName": "ifHighSpeed", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge32", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 15", "OID": "*******.********.1.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifHighSpeed"}, "ifPromiscuousMode": {"ObjectName": "ifPromiscuousMode", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TruthValue", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 16", "OID": "*******.********.1.1.16", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifPromiscuousMode"}, "ifConnectorPresent": {"ObjectName": "ifConnectorPresent", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TruthValue", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 17", "OID": "*******.********.1.1.17", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifConnectorPresent"}, "ifAlias": {"ObjectName": "if<PERSON><PERSON><PERSON>", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 18", "OID": "*******.********.1.1.18", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifAlias"}, "ifCounterDiscontinuityTime": {"ObjectName": "ifCounterDiscontinuityTime", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeStamp", "STATUS": "current", "OBJECT_IDENTIFIER": "ifXEntry 19", "OID": "*******.********.1.1.19", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifXTable.ifXEntry.ifCounterDiscontinuityTime"}, "ifStackTable": {"ObjectName": "ifStackTable", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IfStackEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ifMIBObjects 2", "OID": "*******.********.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifStackTable"}, "ifStackEntry": {"ObjectName": "ifStackEntry", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IfStackEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ifStackTable 1", "OID": "*******.********.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifStackTable.ifStackEntry"}, "IfStackEntry": {"ObjectName": "IfStackEntry", "ModuleName": "IF-MIB", "MACRO": "SEQUENCE"}, "ifStackHigherLayer": {"ObjectName": "ifStack<PERSON>igher<PERSON><PERSON>er", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InterfaceIndexOrZero", "STATUS": "current", "OBJECT_IDENTIFIER": "ifStackEntry 1", "OID": "*******.********.2.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifStackTable.ifStackEntry.ifStackHigherLayer"}, "ifStackLowerLayer": {"ObjectName": "ifStackLowerLayer", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InterfaceIndexOrZero", "STATUS": "current", "OBJECT_IDENTIFIER": "ifStackEntry 2", "OID": "*******.********.2.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifStackTable.ifStackEntry.ifStackLowerLayer"}, "ifStackStatus": {"ObjectName": "ifStackStatus", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "RowStatus", "STATUS": "current", "OBJECT_IDENTIFIER": "ifStackEntry 3", "OID": "*******.********.2.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifStackTable.ifStackEntry.ifStackStatus"}, "ifStackLastChange": {"ObjectName": "ifStackLastChange", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "STATUS": "current", "OBJECT_IDENTIFIER": "ifMIBObjects 6", "OID": "*******.********.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifStackLastChange"}, "ifRcvAddressTable": {"ObjectName": "ifRcvAddressTable", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IfRcvAddressEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ifMIBObjects 4", "OID": "*******.********.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifRcvAddressTable"}, "ifRcvAddressEntry": {"ObjectName": "ifRcvAddressEntry", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IfRcvAddressEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ifRcvAddressTable 1", "OID": "*******.********.4.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifRcvAddressTable.ifRcvAddressEntry"}, "IfRcvAddressEntry": {"ObjectName": "IfRcvAddressEntry", "ModuleName": "IF-MIB", "MACRO": "SEQUENCE"}, "ifRcvAddressAddress": {"ObjectName": "ifRcvAddressAddress", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "PhysAdd<PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "ifRcvAddressEntry 1", "OID": "*******.********.4.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifRcvAddressTable.ifRcvAddressEntry.ifRcvAddressAddress"}, "ifRcvAddressStatus": {"ObjectName": "ifRcvAddressStatus", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "RowStatus", "STATUS": "current", "OBJECT_IDENTIFIER": "ifRcvAddressEntry 2", "OID": "*******.********.4.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifRcvAddressTable.ifRcvAddressEntry.ifRcvAddressStatus"}, "ifRcvAddressType": {"ObjectName": "ifRcvAddressType", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "volatile", "3": "nonVolatile"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "ifRcvAddressEntry 3", "OID": "*******.********.4.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifRcvAddressTable.ifRcvAddressEntry.ifRcvAddressType"}, "linkDown": {"ObjectName": "linkDown", "ModuleName": "IF-MIB", "MACRO": "NOTIFICATION-TYPE", "STATUS": "current", "DESCRIPTION": "A linkDown trap signifies that the SNMP entity, acting in\r\n            an agent role, has detected that the ifOperStatus object for\r\n            one of its communication links is about to enter the down\r\n            state from some other state (but not from the notPresent\r\n            state).  This other state is indicated by the included value\r\n            of ifOperStatus.", "OBJECT_IDENTIFIER": "snmpTraps 3", "OID": "*******.*******.5.3", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTraps.linkDown"}, "linkUp": {"ObjectName": "linkUp", "ModuleName": "IF-MIB", "MACRO": "NOTIFICATION-TYPE", "STATUS": "current", "DESCRIPTION": "A linkUp trap signifies that the SNMP entity, acting in an\r\n            agent role, has detected that the ifOperStatus object for\r\n            one of its communication links left the down state and\r\n            transitioned into some other state (but not into the\r\n            notPresent state).  This other state is indicated by the\r\n            included value of ifOperStatus.", "OBJECT_IDENTIFIER": "snmpTraps 4", "OID": "*******.*******.5.4", "NameSpace": "iso.org.dod.internet.snmpV2.snmpModules.snmpMIB.snmpMIBObjects.snmpTraps.linkUp"}, "ifConformance": {"ObjectName": "ifConformance", "ModuleName": "IF-MIB", "OBJECT_IDENTIFIER": "ifMIB 2", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance"}, "ifGroups": {"ObjectName": "ifGroups", "ModuleName": "IF-MIB", "OBJECT_IDENTIFIER": "ifConformance 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups"}, "ifCompliances": {"ObjectName": "ifCompliances", "ModuleName": "IF-MIB", "OBJECT_IDENTIFIER": "ifConformance 2", "OID": "*******.********.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifCompliances"}, "ifCompliance3": {"ObjectName": "ifCompliance3", "ModuleName": "IF-MIB", "MACRO": "MODULE-COMPLIANCE", "STATUS": "current", "DESCRIPTION": "Write access is not required.", "OBJECT_IDENTIFIER": "ifCompliances 3", "OID": "*******.********.2.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifCompliances.ifCompliance3"}, "ifGeneralInformationGroup": {"ObjectName": "ifGeneralInformationGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing information applicable to\r\n            all network interfaces.", "OBJECT_IDENTIFIER": "ifGroups 10", "OID": "*******.********.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifGeneralInformationGroup"}, "ifFixedLengthGroup": {"ObjectName": "ifFixedLengthGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing information specific to\r\n            non-high speed (non-high speed interfaces transmit and\r\n            receive at speeds less than or equal to 20,000,000\r\n            bits/second) character-oriented or fixed-length-transmission\r\n            network interfaces.", "OBJECT_IDENTIFIER": "ifGroups 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifFixedLengthGroup"}, "ifHCFixedLengthGroup": {"ObjectName": "ifHCFixedLengthGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing information specific to\r\n            high speed (greater than 20,000,000 bits/second) character-\r\n            oriented or fixed-length-transmission network interfaces.", "OBJECT_IDENTIFIER": "ifGroups 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifHCFixedLengthGroup"}, "ifPacketGroup": {"ObjectName": "ifPacketGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing information specific to\r\n            non-high speed (non-high speed interfaces transmit and\r\n            receive at speeds less than or equal to 20,000,000\r\n            bits/second) packet-oriented network interfaces.", "OBJECT_IDENTIFIER": "ifGroups 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifPacketGroup"}, "ifHCPacketGroup": {"ObjectName": "ifHCPacketGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing information specific to\r\n            high speed (greater than 20,000,000 bits/second but less\r\n            than or equal to 650,000,000 bits/second) packet-oriented\r\n            network interfaces.", "OBJECT_IDENTIFIER": "ifGroups 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifHCPacketGroup"}, "ifVHCPacketGroup": {"ObjectName": "ifVHCPacketGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing information specific to\r\n            higher speed (greater than 650,000,000 bits/second) packet-\r\n            oriented network interfaces.", "OBJECT_IDENTIFIER": "ifGroups 6", "OID": "*******.********.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifVHCPacketGroup"}, "ifRcvAddressGroup": {"ObjectName": "ifRcvAddressGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing information on the\r\n            multiple addresses which an interface receives.", "OBJECT_IDENTIFIER": "ifGroups 7", "OID": "*******.********.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifRcvAddressGroup"}, "ifStackGroup2": {"ObjectName": "ifStackGroup2", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing information on the\r\n            layering of MIB-II interfaces.", "OBJECT_IDENTIFIER": "ifGroups 11", "OID": "*******.********.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifStackGroup2"}, "ifCounterDiscontinuityGroup": {"ObjectName": "ifCounterDiscontinuityGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "A collection of objects providing information specific to\r\n            interface counter discontinuities.", "OBJECT_IDENTIFIER": "ifGroups 13", "OID": "*******.********.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifCounterDiscontinuityGroup"}, "linkUpDownNotificationsGroup": {"ObjectName": "linkUpDownNotificationsGroup", "ModuleName": "IF-MIB", "MACRO": "NOTIFICATION-GROUP", "STATUS": "current", "DESCRIPTION": "The notifications which indicate specific changes in the\r\n            value of ifOperStatus.", "OBJECT_IDENTIFIER": "ifGroups 14", "OID": "*******.********.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.linkUpDownNotificationsGroup"}, "ifTestTable": {"ObjectName": "ifTestTable", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IfTestEntry", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifMIBObjects 3", "OID": "*******.********.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifTestTable"}, "ifTestEntry": {"ObjectName": "ifTestEntry", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IfTestEntry", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifTestTable 1", "OID": "*******.********.3.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifTestTable.ifTestEntry"}, "IfTestEntry": {"ObjectName": "IfTestEntry", "ModuleName": "IF-MIB", "MACRO": "SEQUENCE"}, "ifTestId": {"ObjectName": "ifTestId", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TestAndIncr", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifTestEntry 1", "OID": "*******.********.3.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifTestTable.ifTestEntry.ifTestId"}, "ifTestStatus": {"ObjectName": "ifTestStatus", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "notInUse", "2": "inUse"}}, "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifTestEntry 2", "OID": "*******.********.3.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifTestTable.ifTestEntry.ifTestStatus"}, "ifTestType": {"ObjectName": "ifTestType", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "AutonomousType", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifTestEntry 3", "OID": "*******.********.3.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifTestTable.ifTestEntry.ifTestType"}, "ifTestResult": {"ObjectName": "ifTestResult", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "none", "2": "success", "3": "inProgress", "4": "notSupported", "5": "unAbleToRun", "6": "aborted", "7": "failed"}}, "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifTestEntry 4", "OID": "*******.********.3.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifTestTable.ifTestEntry.ifTestResult"}, "ifTestCode": {"ObjectName": "ifTestCode", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifTestEntry 5", "OID": "*******.********.3.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifTestTable.ifTestEntry.ifTestCode"}, "ifTestOwner": {"ObjectName": "ifTestOwner", "ModuleName": "IF-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OwnerString", "STATUS": "deprecated", "OBJECT_IDENTIFIER": "ifTestEntry 6", "OID": "*******.********.3.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifMIBObjects.ifTestTable.ifTestEntry.ifTestOwner"}, "ifGeneralGroup": {"ObjectName": "ifGeneralGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "deprecated", "DESCRIPTION": "A collection of objects deprecated in favour of\r\n            ifGeneralInformationGroup.", "OBJECT_IDENTIFIER": "ifGroups 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifGeneralGroup"}, "ifTestGroup": {"ObjectName": "ifTestGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "deprecated", "DESCRIPTION": "A collection of objects providing the ability to invoke\r\n            tests on an interface.", "OBJECT_IDENTIFIER": "ifGroups 8", "OID": "*******.********.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifTestGroup"}, "ifStackGroup": {"ObjectName": "ifStackGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "deprecated", "DESCRIPTION": "The previous collection of objects providing information on\r\n            the layering of MIB-II interfaces.", "OBJECT_IDENTIFIER": "ifGroups 9", "OID": "*******.********.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifStackGroup"}, "ifOldObjectsGroup": {"ObjectName": "ifOldObjectsGroup", "ModuleName": "IF-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "deprecated", "DESCRIPTION": "The collection of objects deprecated from the original MIB-\r\n            II interfaces group.", "OBJECT_IDENTIFIER": "ifGroups 12", "OID": "*******.********.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifGroups.ifOldObjectsGroup"}, "ifCompliance": {"ObjectName": "ifCompliance", "ModuleName": "IF-MIB", "MACRO": "MODULE-COMPLIANCE", "STATUS": "deprecated", "DESCRIPTION": "Write access is not required, nor is support for the value\r\n            testing(3).", "OBJECT_IDENTIFIER": "ifCompliances 1", "OID": "*******.********.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifCompliances.ifCompliance"}, "ifCompliance2": {"ObjectName": "ifCompliance2", "ModuleName": "IF-MIB", "MACRO": "MODULE-COMPLIANCE", "STATUS": "deprecated", "DESCRIPTION": "Write access is not required.", "OBJECT_IDENTIFIER": "ifCompliances 2", "OID": "*******.********.2.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ifMIB.ifConformance.ifCompliances.ifCompliance2"}}, "IP-FORWARD-MIB": {"IMPORTS": {"SNMPv2-SMI": ["MODULE-IDENTITY", "OBJECT-TYPE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integer32", "Gauge32"], "SNMPv2-TC": ["RowStatus"], "RFC1213-MIB": ["ip"], "SNMPv2-CONF": ["MODULE-COMPLIANCE", "OBJECT-GROUP"]}, "ipForward": {"ObjectName": "ipForward", "ModuleName": "IP-FORWARD-MIB", "MACRO": "MODULE-IDENTITY", "LAST-UPDATED": "9609190000Z", "ORGANIZATION": "IETF OSPF Working Group", "CONTACT-INFO": "        <PERSON>\r\n      Postal: Cisco Systems\r\n              519 Lado Drive\r\n              Santa Barbara, California 93111\r\n\r\n      Phone:  ****** 681 0115\r\n      Email:  <EMAIL>\r\n      ", "DESCRIPTION": "Revisions made by the OSPF WG.", "OBJECT_IDENTIFIER": "ip 24", "OID": "*******.*******4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward"}, "ipCidrRouteNumber": {"ObjectName": "ipCidrRouteNumber", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipForward 3", "OID": "*******.*******4.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteNumber"}, "ipCidrRouteTable": {"ObjectName": "ipCidrRouteTable", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IpCidrRouteEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ipForward 4", "OID": "*******.*******4.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable"}, "ipCidrRouteEntry": {"ObjectName": "ipCidrRouteEntry", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IpCidrRouteEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteTable 1", "OID": "*******.*******4.4.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry"}, "IpCidrRouteEntry": {"ObjectName": "IpCidrRouteEntry", "ModuleName": "IP-FORWARD-MIB", "MACRO": "SEQUENCE"}, "ipCidrRouteDest": {"ObjectName": "ipCidrRouteDest", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 1", "OID": "*******.*******4.4.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteDest"}, "ipCidrRouteMask": {"ObjectName": "ipCidrRouteMask", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 2", "OID": "*******.*******4.4.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteMask"}, "ipCidrRouteTos": {"ObjectName": "ipCidrRouteTos", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 3", "OID": "*******.*******4.4.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteTos"}, "ipCidrRouteNextHop": {"ObjectName": "ipCidrRouteNextHop", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 4", "OID": "*******.*******4.4.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteNextHop"}, "ipCidrRouteIfIndex": {"ObjectName": "ipCidrRouteIfIndex", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 5", "OID": "*******.*******4.4.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteIfIndex"}, "ipCidrRouteType": {"ObjectName": "ipCidrRouteType", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "reject", "3": "local", "4": "remote"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 6", "OID": "*******.*******4.4.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteType"}, "ipCidrRouteProto": {"ObjectName": "ipCidrRouteProto", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "local", "3": "netmgmt", "4": "icmp", "5": "egp", "6": "ggp", "7": "hello", "8": "rip", "9": "isIs", "10": "esIs", "11": "ciscoIgrp", "12": "bbnSpfIgp", "13": "ospf", "14": "bgp", "15": "idpr", "16": "ciscoEigrp"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 7", "OID": "*******.*******4.4.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteProto"}, "ipCidrRouteAge": {"ObjectName": "ipCidrRouteAge", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 8", "OID": "*******.*******4.4.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteAge"}, "ipCidrRouteInfo": {"ObjectName": "ipCidrRouteInfo", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 9", "OID": "*******.*******4.4.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteInfo"}, "ipCidrRouteNextHopAS": {"ObjectName": "ipCidrRouteNextHopAS", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 10", "OID": "*******.*******4.4.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteNextHopAS"}, "ipCidrRouteMetric1": {"ObjectName": "ipCidrRouteMetric1", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 11", "OID": "*******.*******4.4.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteMetric1"}, "ipCidrRouteMetric2": {"ObjectName": "ipCidrRouteMetric2", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 12", "OID": "*******.*******4.4.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteMetric2"}, "ipCidrRouteMetric3": {"ObjectName": "ipCidrRouteMetric3", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 13", "OID": "*******.*******4.4.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteMetric3"}, "ipCidrRouteMetric4": {"ObjectName": "ipCidrRouteMetric4", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 14", "OID": "*******.*******4.4.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteMetric4"}, "ipCidrRouteMetric5": {"ObjectName": "ipCidrRouteMetric5", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 15", "OID": "*******.*******4.4.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteMetric5"}, "ipCidrRouteStatus": {"ObjectName": "ipCidrRouteStatus", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "RowStatus", "STATUS": "current", "OBJECT_IDENTIFIER": "ipCidrRouteEntry 16", "OID": "*******.*******4.4.1.16", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipCidrRouteTable.ipCidrRouteEntry.ipCidrRouteStatus"}, "ipForwardConformance": {"ObjectName": "ipForwardConformance", "ModuleName": "IP-FORWARD-MIB", "OBJECT_IDENTIFIER": "ipForward 5", "OID": "*******.*******4.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardConformance"}, "ipForwardGroups": {"ObjectName": "ipForwardGroups", "ModuleName": "IP-FORWARD-MIB", "OBJECT_IDENTIFIER": "ipForwardConformance 1", "OID": "*******.*******4.5.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardConformance.ipForwardGroups"}, "ipForwardCompliances": {"ObjectName": "ipForwardCompliances", "ModuleName": "IP-FORWARD-MIB", "OBJECT_IDENTIFIER": "ipForwardConformance 2", "OID": "*******.*******4.5.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardConformance.ipForwardCompliances"}, "ipForwardCompliance": {"ObjectName": "ipForwardCompliance", "ModuleName": "IP-FORWARD-MIB", "MACRO": "MODULE-COMPLIANCE", "STATUS": "current", "DESCRIPTION": "The compliance statement for SNMPv2 entities\r\n       which implement the ipForward MIB.", "OBJECT_IDENTIFIER": "ipForwardCompliances 1", "OID": "*******.*******4.5.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardConformance.ipForwardCompliances.ipForwardCompliance"}, "ipForwardCidrRouteGroup": {"ObjectName": "ipForwardCidrRouteGroup", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "The CIDR Route Table.", "OBJECT_IDENTIFIER": "ipForwardGroups 3", "OID": "*******.*******4.5.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardConformance.ipForwardGroups.ipForwardCidrRouteGroup"}, "ipForwardNumber": {"ObjectName": "ipForwardNumber", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForward 1", "OID": "*******.*******4.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardNumber"}, "ipForwardTable": {"ObjectName": "ipForwardTable", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IpForwardEntry", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForward 2", "OID": "*******.*******4.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable"}, "ipForwardEntry": {"ObjectName": "ipForwardEntry", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IpForwardEntry", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardTable 1", "OID": "*******.*******4.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry"}, "IpForwardEntry": {"ObjectName": "IpForwardEntry", "ModuleName": "IP-FORWARD-MIB", "MACRO": "SEQUENCE"}, "ipForwardDest": {"ObjectName": "ipForwardDest", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 1", "OID": "*******.*******4.2.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardDest"}, "ipForwardMask": {"ObjectName": "ipForwardMask", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 2", "OID": "*******.*******4.2.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardMask"}, "ipForwardPolicy": {"ObjectName": "ipForwardPolicy", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 3", "OID": "*******.*******4.2.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardPolicy"}, "ipForwardNextHop": {"ObjectName": "ipForwardNextHop", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 4", "OID": "*******.*******4.2.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardNextHop"}, "ipForwardIfIndex": {"ObjectName": "ipForwardIfIndex", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 5", "OID": "*******.*******4.2.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardIfIndex"}, "ipForwardType": {"ObjectName": "ipForwardType", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "invalid", "3": "local", "4": "remote"}}, "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 6", "OID": "*******.*******4.2.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardType"}, "ipForwardProto": {"ObjectName": "ipForwardProto", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "local", "3": "netmgmt", "4": "icmp", "5": "egp", "6": "ggp", "7": "hello", "8": "rip", "9": "is-is", "10": "es-is", "11": "ciscoIgrp", "12": "bbnSpfIgp", "13": "ospf", "14": "bgp", "15": "idpr"}}, "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 7", "OID": "*******.*******4.2.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardProto"}, "ipForwardAge": {"ObjectName": "ipForwardAge", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 8", "OID": "*******.*******4.2.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardAge"}, "ipForwardInfo": {"ObjectName": "ipForwardInfo", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OBJECT_IDENTIFIER", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 9", "OID": "*******.*******4.2.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardInfo"}, "ipForwardNextHopAS": {"ObjectName": "ipForwardNextHopAS", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 10", "OID": "*******.*******4.2.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardNextHopAS"}, "ipForwardMetric1": {"ObjectName": "ipForwardMetric1", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 11", "OID": "*******.*******4.2.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardMetric1"}, "ipForwardMetric2": {"ObjectName": "ipForwardMetric2", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 12", "OID": "*******.*******4.2.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardMetric2"}, "ipForwardMetric3": {"ObjectName": "ipForwardMetric3", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 13", "OID": "*******.*******4.2.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardMetric3"}, "ipForwardMetric4": {"ObjectName": "ipForwardMetric4", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 14", "OID": "*******.*******4.2.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardMetric4"}, "ipForwardMetric5": {"ObjectName": "ipForwardMetric5", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "ipForwardEntry 15", "OID": "*******.*******4.2.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardTable.ipForwardEntry.ipForwardMetric5"}, "ipForwardOldCompliance": {"ObjectName": "ipForwardOldCompliance", "ModuleName": "IP-FORWARD-MIB", "MACRO": "MODULE-COMPLIANCE", "STATUS": "obsolete", "DESCRIPTION": "The compliance statement for SNMP entities\r\n       which implement the ipForward MIB.", "OBJECT_IDENTIFIER": "ipForwardCompliances 2", "OID": "*******.*******4.5.2.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardConformance.ipForwardCompliances.ipForwardOldCompliance"}, "ipForwardMultiPathGroup": {"ObjectName": "ipForwardMultiPathGroup", "ModuleName": "IP-FORWARD-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "obsolete", "DESCRIPTION": "IP Multipath Route Table.", "OBJECT_IDENTIFIER": "ipForwardGroups 2", "OID": "*******.*******4.5.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.ip.ipForward.ipForwardConformance.ipForwardGroups.ipForwardMultiPathGroup"}}, "BGP4-MIB": {"IMPORTS": {"SNMPv2-SMI": ["MODULE-IDENTITY", "OBJECT-TYPE", "NOTIFICATION-TYPE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integer32", "Counter32", "Gauge32"], "RFC1213-MIB": ["mib-2"]}, "bgp": {"ObjectName": "bgp", "ModuleName": "BGP4-MIB", "MACRO": "MODULE-IDENTITY", "LAST-UPDATED": "9405050000Z", "ORGANIZATION": "IETF BGP Working Group", "CONTACT-INFO": "   <PERSON>  (Editor)\r\n                            Postal: IBM Corp.\r\n                                    P.O.Box 218\r\n                                    Yorktown Heights, NY 10598\r\n                                    US\r\n\r\n                               Tel: ****** 945 3156\r\n                               Fax: ****** 945 2141\r\n                            E-mail: <EMAIL>", "DESCRIPTION": "The MIB module for BGP-4.", "OBJECT_IDENTIFIER": "mib-2 15", "OID": "*******.2.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp"}, "bgpVersion": {"ObjectName": "bgpVersion", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp 1", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpVersion"}, "bgpLocalAs": {"ObjectName": "bgpLocalAs", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp 2", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpLocalAs"}, "bgpPeerTable": {"ObjectName": "bgpPeerTable", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF BgpPeerEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp 3", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable"}, "bgpPeerEntry": {"ObjectName": "bgpPeerEntry", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "BgpPeerEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry"}, "BgpPeerEntry": {"ObjectName": "BgpPeerEntry", "ModuleName": "BGP4-MIB", "MACRO": "SEQUENCE"}, "bgpPeerIdentifier": {"ObjectName": "bgpPeerIdentifier", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerIdentifier"}, "bgpPeerState": {"ObjectName": "bgpPeerState", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "idle", "2": "connect", "3": "active", "4": "opensent", "5": "openconfirm", "6": "established"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerState"}, "bgpPeerAdminStatus": {"ObjectName": "bgpPeerAdminStatus", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "stop", "2": "start"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerAdminStatus"}, "bgpPeerNegotiatedVersion": {"ObjectName": "bgpPeerNegotiatedVersion", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerNegotiatedVersion"}, "bgpPeerLocalAddr": {"ObjectName": "bgpPeerLocalAddr", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerLocalAddr"}, "bgpPeerLocalPort": {"ObjectName": "bgpPeerLocalPort", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 6", "OID": "*******.********.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerLocalPort"}, "bgpPeerRemoteAddr": {"ObjectName": "bgpPeerRemoteAddr", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 7", "OID": "*******.********.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerRemoteAddr"}, "bgpPeerRemotePort": {"ObjectName": "bgpPeerRemotePort", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 8", "OID": "*******.********.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerRemotePort"}, "bgpPeerRemoteAs": {"ObjectName": "bgpPeerRemoteAs", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 9", "OID": "*******.********.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerRemoteAs"}, "bgpPeerInUpdates": {"ObjectName": "bgpPeerInUpdates", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 10", "OID": "*******.********.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerInUpdates"}, "bgpPeerOutUpdates": {"ObjectName": "bgpPeerOutUpdates", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 11", "OID": "*******.********.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerOutUpdates"}, "bgpPeerInTotalMessages": {"ObjectName": "bgpPeerInTotalMessages", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 12", "OID": "*******.********.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerInTotalMessages"}, "bgpPeerOutTotalMessages": {"ObjectName": "bgpPeerOutTotalMessages", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 13", "OID": "*******.********.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerOutTotalMessages"}, "bgpPeerLastError": {"ObjectName": "bgpPeerLastError", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 14", "OID": "*******.********.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerLastError"}, "bgpPeerFsmEstablishedTransitions": {"ObjectName": "bgpPeerFsmEstablishedTransitions", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 15", "OID": "*******.********.1.15", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerFsmEstablishedTransitions"}, "bgpPeerFsmEstablishedTime": {"ObjectName": "bgpPeerFsmEstablishedTime", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge32", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 16", "OID": "*******.********.1.16", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerFsmEstablishedTime"}, "bgpPeerConnectRetryInterval": {"ObjectName": "bgpPeerConnectRetryInterval", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 17", "OID": "*******.********.1.17", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerConnectRetryInterval"}, "bgpPeerHoldTime": {"ObjectName": "bgpPeerHoldTime", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 18", "OID": "*******.********.1.18", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerHoldTime"}, "bgpPeerKeepAlive": {"ObjectName": "bgpPeerKeepAlive", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 19", "OID": "*******.********.1.19", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerKeepAlive"}, "bgpPeerHoldTimeConfigured": {"ObjectName": "bgpPeerHoldTimeConfigured", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 20", "OID": "*******.********.1.20", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerHoldTimeConfigured"}, "bgpPeerKeepAliveConfigured": {"ObjectName": "bgpPeerKeepAliveConfigured", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 21", "OID": "*******.********.1.21", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerKeepAliveConfigured"}, "bgpPeerMinASOriginationInterval": {"ObjectName": "bgpPeerMinASOriginationInterval", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 22", "OID": "*******.********.1.22", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerMinASOriginationInterval"}, "bgpPeerMinRouteAdvertisementInterval": {"ObjectName": "bgpPeerMinRouteAdvertisementInterval", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 23", "OID": "*******.********.1.23", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerMinRouteAdvertisementInterval"}, "bgpPeerInUpdateElapsedTime": {"ObjectName": "bgpPeerInUpdateElapsedTime", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge32", "STATUS": "current", "OBJECT_IDENTIFIER": "bgpPeerEntry 24", "OID": "*******.********.1.24", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpPeerTable.bgpPeerEntry.bgpPeerInUpdateElapsedTime"}, "bgpIdentifier": {"ObjectName": "bgpIdentifier", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp 4", "OID": "*******.2.1.15.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpIdentifier"}, "bgpRcvdPathAttrTable": {"ObjectName": "bgpRcvdPathAttrTable", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF BgpPathAttrEntry", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "bgp 5", "OID": "*******.2.1.15.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpRcvdPathAttrTable"}, "bgpPathAttrEntry": {"ObjectName": "bgpPathAttrEntry", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "BgpPathAttrEntry", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "bgpRcvdPathAttrTable 1", "OID": "*******.2.1.15.5.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpRcvdPathAttrTable.bgpPathAttrEntry"}, "BgpPathAttrEntry": {"ObjectName": "BgpPathAttrEntry", "ModuleName": "BGP4-MIB", "MACRO": "SEQUENCE"}, "bgpPathAttrPeer": {"ObjectName": "bgpPathAttrPeer", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "bgpPathAttrEntry 1", "OID": "*******.2.1.15.5.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpRcvdPathAttrTable.bgpPathAttrEntry.bgpPathAttrPeer"}, "bgpPathAttrDestNetwork": {"ObjectName": "bgpPathAttrDestNetwork", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "bgpPathAttrEntry 2", "OID": "*******.2.1.15.5.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpRcvdPathAttrTable.bgpPathAttrEntry.bgpPathAttrDestNetwork"}, "bgpPathAttrOrigin": {"ObjectName": "bgpPathAttr<PERSON><PERSON><PERSON>", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "igp", "2": "egp", "3": "incomplete"}}, "STATUS": "obsolete", "OBJECT_IDENTIFIER": "bgpPathAttrEntry 3", "OID": "*******.2.1.15.5.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpRcvdPathAttrTable.bgpPathAttrEntry.bgpPathAttrOrigin"}, "bgpPathAttrASPath": {"ObjectName": "bgpPathAttrASPath", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "bgpPathAttrEntry 4", "OID": "*******.2.1.15.5.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpRcvdPathAttrTable.bgpPathAttrEntry.bgpPathAttrASPath"}, "bgpPathAttrNextHop": {"ObjectName": "bgpPathAttrNextHop", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "bgpPathAttrEntry 5", "OID": "*******.2.1.15.5.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpRcvdPathAttrTable.bgpPathAttrEntry.bgpPathAttrNextHop"}, "bgpPathAttrInterASMetric": {"ObjectName": "bgpPathAttrInterASMetric", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "obsolete", "OBJECT_IDENTIFIER": "bgpPathAttrEntry 6", "OID": "*******.2.1.15.5.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpRcvdPathAttrTable.bgpPathAttrEntry.bgpPathAttrInterASMetric"}, "bgp4PathAttrTable": {"ObjectName": "bgp4PathAttrTable", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF Bgp4PathAttrEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp 6", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable"}, "bgp4PathAttrEntry": {"ObjectName": "bgp4PathAttrEntry", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Bgp4PathAttrEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry"}, "Bgp4PathAttrEntry": {"ObjectName": "Bgp4PathAttrEntry", "ModuleName": "BGP4-MIB", "MACRO": "SEQUENCE"}, "bgp4PathAttrPeer": {"ObjectName": "bgp4PathAttrPeer", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrPeer"}, "bgp4PathAttrIpAddrPrefixLen": {"ObjectName": "bgp4PathAttrIpAddrPrefixLen", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrIpAddrPrefixLen"}, "bgp4PathAttrIpAddrPrefix": {"ObjectName": "bgp4PathAttrIpAddrPrefix", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrIpAddrPrefix"}, "bgp4PathAttrOrigin": {"ObjectName": "bgp4PathAttr<PERSON><PERSON>in", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "igp", "2": "egp", "3": "incomplete"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrOrigin"}, "bgp4PathAttrASPathSegment": {"ObjectName": "bgp4PathAttrASPathSegment", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrASPathSegment"}, "bgp4PathAttrNextHop": {"ObjectName": "bgp4PathAttrNextHop", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 6", "OID": "*******.********.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrNextHop"}, "bgp4PathAttrMultiExitDisc": {"ObjectName": "bgp4PathAttrMultiExitDisc", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 7", "OID": "*******.********.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrMultiExitDisc"}, "bgp4PathAttrLocalPref": {"ObjectName": "bgp4PathAttrLocalPref", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 8", "OID": "*******.********.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrLocalPref"}, "bgp4PathAttrAtomicAggregate": {"ObjectName": "bgp4PathAttrAtomicAggregate", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "lessSpecificRrouteNotSelected", "2": "lessSpecificRouteSelected"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 9", "OID": "*******.********.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrAtomicAggregate"}, "bgp4PathAttrAggregatorAS": {"ObjectName": "bgp4PathAttrAggregatorAS", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 10", "OID": "*******.********.1.10", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrAggregatorAS"}, "bgp4PathAttrAggregatorAddr": {"ObjectName": "bgp4PathAttrAggregatorAddr", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 11", "OID": "*******.********.1.11", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrAggregatorAddr"}, "bgp4PathAttrCalcLocalPref": {"ObjectName": "bgp4PathAttrCalcLocalPref", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 12", "OID": "*******.********.1.12", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrCalcLocalPref"}, "bgp4PathAttrBest": {"ObjectName": "bgp4PathAttrBest", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "false", "2": "true"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 13", "OID": "*******.********.1.13", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrBest"}, "bgp4PathAttrUnknown": {"ObjectName": "bgp4PathAttrUnknown", "ModuleName": "BGP4-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "STATUS": "current", "OBJECT_IDENTIFIER": "bgp4PathAttrEntry 14", "OID": "*******.********.1.14", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgp4PathAttrTable.bgp4PathAttrEntry.bgp4PathAttrUnknown"}, "bgpTraps": {"ObjectName": "bgpTraps", "ModuleName": "BGP4-MIB", "OBJECT_IDENTIFIER": "bgp 7", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpTraps"}, "bgpEstablished": {"ObjectName": "bgpEstablished", "ModuleName": "BGP4-MIB", "MACRO": "NOTIFICATION-TYPE", "STATUS": "current", "DESCRIPTION": "The BGP Established event is generated when\r\n                            the BGP FSM enters the ESTABLISHED state.", "OBJECT_IDENTIFIER": "bgpTraps 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpTraps.bgpEstablished"}, "bgpBackwardTransition": {"ObjectName": "bgpBackwardTransition", "ModuleName": "BGP4-MIB", "MACRO": "NOTIFICATION-TYPE", "STATUS": "current", "DESCRIPTION": "The BGPBackwardTransition Event is generated\r\n                            when the BGP FSM moves from a higher numbered\r\n                            state to a lower numbered state.", "OBJECT_IDENTIFIER": "bgpTraps 2", "OID": "*******.********.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.bgp.bgpTraps.bgpBackwardTransition"}}, "INET-ADDRESS-MIB": {"IMPORTS": {"SNMPv2-SMI": ["MODULE-IDENTITY", "mib-2", "Unsigned32"], "SNMPv2-TC": ["TEXTUAL-CONVENTION"]}, "inetAddressMIB": {"ObjectName": "inetAddressMIB", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "MODULE-IDENTITY", "LAST-UPDATED": "200502040000Z", "ORGANIZATION": "IETF Operations and Management Area", "CONTACT-INFO": "<PERSON><PERSON><PERSON> (Editor)\r\n         International University Bremen\r\n         P.O. Box 750 561\r\n         28725 Bremen, Germany\r\n\r\n         Phone: +49 ************\r\n         EMail: j.s<PERSON><PERSON>@iu-bremen.de\r\n\r\n         Send comments to <<EMAIL>>.", "DESCRIPTION": "Initial version, published as RFC 2851.", "OBJECT_IDENTIFIER": "mib-2 76", "OID": "*******.2.1.76", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.inetAddressMIB"}, "InetAddressType": {"ObjectName": "InetAddressType", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": "unknown", "1": "ipv4", "2": "ipv6", "3": "ipv4z", "4": "ipv6z", "16": "dns"}}}, "InetAddress": {"ObjectName": "InetAdd<PERSON>", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "InetAddressIPv4": {"ObjectName": "InetAddressIPv4", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "InetAddressIPv6": {"ObjectName": "InetAddressIPv6", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "InetAddressIPv4z": {"ObjectName": "InetAddressIPv4z", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "InetAddressIPv6z": {"ObjectName": "InetAddressIPv6z", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "InetAddressDNS": {"ObjectName": "InetAddressDNS", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "InetAddressPrefixLength": {"ObjectName": "InetAddressPrefixLength", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Unsigned32"}, "InetPortNumber": {"ObjectName": "InetPortNumber", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION"}, "InetAutonomousSystemNumber": {"ObjectName": "InetAutonomousSystemNumber", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION"}, "InetScopeType": {"ObjectName": "InetScopeType", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"1": "interfaceLocal", "2": "linkLocal", "3": "subnetLocal", "4": "adminLocal", "5": "siteLocal", "8": "organizationLocal", "14": "global"}}}, "InetZoneIndex": {"ObjectName": "InetZoneIndex", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION"}, "InetVersion": {"ObjectName": "InetVersion", "ModuleName": "INET-ADDRESS-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": "unknown", "1": "ipv4", "2": "ipv6"}}}}, "HOST-RESOURCES-MIB": {"IMPORTS": {"SNMPv2-SMI": ["MODULE-IDENTITY", "OBJECT-TYPE", "mib-2", "Integer32", "Counter32", "Gauge32", "TimeTicks"], "SNMPv2-TC": ["TEXTUAL-CONVENTION", "DisplayString", "TruthValue", "DateAndTime", "AutonomousType"], "SNMPv2-CONF": ["MODULE-COMPLIANCE", "OBJECT-GROUP"], "IF-MIB": ["InterfaceIndexOrZero"]}, "hostResourcesMibModule": {"ObjectName": "hostResourcesMibModule", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "MODULE-IDENTITY", "LAST-UPDATED": "200003060000Z", "ORGANIZATION": "IETF Host Resources MIB Working Group", "CONTACT-INFO": "<PERSON>\r\n       Postal: Lucent Technologies, Inc.\r\n               1213 Innsbruck Dr.\r\n               <PERSON>, CA 94089\r\n               USA\r\n       Phone:  ************\r\n       Fax:    ************\r\n       Email:  <EMAIL>\r\n\r\n       In addition, the Host Resources MIB mailing list is\r\n       dedicated to discussion of this MIB. To join the\r\n       mailing list, send a request message to\r\n       <EMAIL>. The mailing list\r\n       <NAME_EMAIL>.", "DESCRIPTION": "The original version of this MIB, published as\r\n       RFC1514.", "OBJECT_IDENTIFIER": "hrMIBAdminInfo 1"}, "host": {"ObjectName": "host", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "mib-2 25", "OID": "*******.2.1.25", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host"}, "hrSystem": {"ObjectName": "hrSystem", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "host 1", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSystem"}, "hrStorage": {"ObjectName": "hrStorage", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "host 2", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage"}, "hrDevice": {"ObjectName": "hrDevice", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "host 3", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice"}, "hrSWRun": {"ObjectName": "hrSWRun", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "host 4", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun"}, "hrSWRunPerf": {"ObjectName": "hrSWRunPerf", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "host 5", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRunPerf"}, "hrSWInstalled": {"ObjectName": "hrSWInstalled", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "host 6", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled"}, "hrMIBAdminInfo": {"ObjectName": "hrMIBAdminInfo", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "host 7", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo"}, "KBytes": {"ObjectName": "KBytes", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Integer32"}, "ProductID": {"ObjectName": "ProductID", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OBJECT_IDENTIFIER"}, "InternationalDisplayString": {"ObjectName": "InternationalDisplayString", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "hrSystemUptime": {"ObjectName": "hrSystemUptime", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSystem 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSystem.hrSystemUptime"}, "hrSystemDate": {"ObjectName": "hrSystemDate", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DateAndTime", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSystem 2", "OID": "*******.********.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSystem.hrSystemDate"}, "hrSystemInitialLoadDevice": {"ObjectName": "hrSystemInitialLoadDevice", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSystem 3", "OID": "*******.********.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSystem.hrSystemInitialLoadDevice"}, "hrSystemInitialLoadParameters": {"ObjectName": "hrSystemInitialLoadParameters", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InternationalDisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSystem 4", "OID": "*******.********.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSystem.hrSystemInitialLoadParameters"}, "hrSystemNumUsers": {"ObjectName": "hrSystemNumUsers", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSystem 5", "OID": "*******.********.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSystem.hrSystemNumUsers"}, "hrSystemProcesses": {"ObjectName": "hrSystemProcesses", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Gauge32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSystem 6", "OID": "*******.********.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSystem.hrSystemProcesses"}, "hrSystemMaxProcesses": {"ObjectName": "hrSystemMaxProcesses", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSystem 7", "OID": "*******.********.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSystem.hrSystemMaxProcesses"}, "hrStorageTypes": {"ObjectName": "hrStorageTypes", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "hrStorage 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTypes"}, "hrMemorySize": {"ObjectName": "hrMemorySize", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "KBytes", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorage 2", "OID": "*******.********.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrMemorySize"}, "hrStorageTable": {"ObjectName": "hrStorageTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrStorageEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorage 3", "OID": "*******.********.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTable"}, "hrStorageEntry": {"ObjectName": "hrStorageEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrStorageEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorageTable 1", "OID": "*******.********.3.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTable.hrStorageEntry"}, "HrStorageEntry": {"ObjectName": "HrStorageEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrStorageIndex": {"ObjectName": "hrStorageIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorageEntry 1", "OID": "*******.********.3.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTable.hrStorageEntry.hrStorageIndex"}, "hrStorageType": {"ObjectName": "hrStorageType", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "AutonomousType", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorageEntry 2", "OID": "*******.********.3.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTable.hrStorageEntry.hrStorageType"}, "hrStorageDescr": {"ObjectName": "hrStorageDescr", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorageEntry 3", "OID": "*******.********.3.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTable.hrStorageEntry.hrStorageDescr"}, "hrStorageAllocationUnits": {"ObjectName": "hrStorageAllocationUnits", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorageEntry 4", "OID": "*******.********.3.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTable.hrStorageEntry.hrStorageAllocationUnits"}, "hrStorageSize": {"ObjectName": "hrStorageSize", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorageEntry 5", "OID": "*******.********.3.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTable.hrStorageEntry.hrStorageSize"}, "hrStorageUsed": {"ObjectName": "hrStorageUsed", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorageEntry 6", "OID": "*******.********.3.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTable.hrStorageEntry.hrStorageUsed"}, "hrStorageAllocationFailures": {"ObjectName": "hrStorageAllocationFailures", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrStorageEntry 7", "OID": "*******.********.3.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrStorage.hrStorageTable.hrStorageEntry.hrStorageAllocationFailures"}, "hrDeviceTypes": {"ObjectName": "hrDeviceTypes", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "hrDevice 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDeviceTypes"}, "hrDeviceTable": {"ObjectName": "hrDeviceTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrDeviceEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDevice 2", "OID": "*******.********.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDeviceTable"}, "hrDeviceEntry": {"ObjectName": "hrDeviceEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrDeviceEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDeviceTable 1", "OID": "*******.********.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDeviceTable.hrDeviceEntry"}, "HrDeviceEntry": {"ObjectName": "HrDeviceEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrDeviceIndex": {"ObjectName": "hrDeviceIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDeviceEntry 1", "OID": "*******.********.2.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDeviceTable.hrDeviceEntry.hrDeviceIndex"}, "hrDeviceType": {"ObjectName": "hrDeviceType", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "AutonomousType", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDeviceEntry 2", "OID": "*******.********.2.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDeviceTable.hrDeviceEntry.hrDeviceType"}, "hrDeviceDescr": {"ObjectName": "hrDeviceDescr", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDeviceEntry 3", "OID": "*******.********.2.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDeviceTable.hrDeviceEntry.hrDeviceDescr"}, "hrDeviceID": {"ObjectName": "hrDeviceID", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "ProductID", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDeviceEntry 4", "OID": "*******.********.2.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDeviceTable.hrDeviceEntry.hrDeviceID"}, "hrDeviceStatus": {"ObjectName": "hrDeviceStatus", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "unknown", "2": "running", "3": "warning", "4": "testing", "5": "down"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "hrDeviceEntry 5", "OID": "*******.********.2.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDeviceTable.hrDeviceEntry.hrDeviceStatus"}, "hrDeviceErrors": {"ObjectName": "hrDeviceErrors", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Counter32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDeviceEntry 6", "OID": "*******.********.2.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDeviceTable.hrDeviceEntry.hrDeviceErrors"}, "hrProcessorTable": {"ObjectName": "hrProcessorTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrProcessorEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDevice 3", "OID": "*******.********.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrProcessorTable"}, "hrProcessorEntry": {"ObjectName": "hrProcessorEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrProcessorEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrProcessorTable 1", "OID": "*******.********.3.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrProcessorTable.hrProcessorEntry"}, "HrProcessorEntry": {"ObjectName": "HrProcessorEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrProcessorFrwID": {"ObjectName": "hrProcessorFrwID", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "ProductID", "STATUS": "current", "OBJECT_IDENTIFIER": "hrProcessorEntry 1", "OID": "*******.********.3.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrProcessorTable.hrProcessorEntry.hrProcessorFrwID"}, "hrProcessorLoad": {"ObjectName": "hrProcessorLoad", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrProcessorEntry 2", "OID": "*******.********.3.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrProcessorTable.hrProcessorEntry.hrProcessorLoad"}, "hrNetworkTable": {"ObjectName": "hrNetworkTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrNetworkEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDevice 4", "OID": "*******.********.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrNetworkTable"}, "hrNetworkEntry": {"ObjectName": "hrNetworkEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrNetworkEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrNetworkTable 1", "OID": "*******.********.4.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrNetworkTable.hrNetworkEntry"}, "HrNetworkEntry": {"ObjectName": "HrNetworkEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrNetworkIfIndex": {"ObjectName": "hrNetworkIfIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InterfaceIndexOrZero", "STATUS": "current", "OBJECT_IDENTIFIER": "hrNetworkEntry 1", "OID": "*******.********.4.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrNetworkTable.hrNetworkEntry.hrNetworkIfIndex"}, "hrPrinterTable": {"ObjectName": "hrPrinterTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrPrinterEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDevice 5", "OID": "*******.********.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPrinterTable"}, "hrPrinterEntry": {"ObjectName": "hrPrinterEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrPrinterEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrPrinterTable 1", "OID": "*******.********.5.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPrinterTable.hrPrinterEntry"}, "HrPrinterEntry": {"ObjectName": "HrPrinterEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrPrinterStatus": {"ObjectName": "hrPrinterStatus", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "unknown", "3": "idle", "4": "printing", "5": "warmup"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "hrPrinterEntry 1", "OID": "*******.********.5.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPrinterTable.hrPrinterEntry.hrPrinterStatus"}, "hrPrinterDetectedErrorState": {"ObjectName": "hrPrinterDetectedErrorState", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "STATUS": "current", "OBJECT_IDENTIFIER": "hrPrinterEntry 2", "OID": "*******.********.5.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPrinterTable.hrPrinterEntry.hrPrinterDetectedErrorState"}, "hrDiskStorageTable": {"ObjectName": "hrDiskStorageTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrDiskStorageEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDevice 6", "OID": "*******.********.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDiskStorageTable"}, "hrDiskStorageEntry": {"ObjectName": "hrDiskStorageEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrDiskStorageEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDiskStorageTable 1", "OID": "*******.********.6.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDiskStorageTable.hrDiskStorageEntry"}, "HrDiskStorageEntry": {"ObjectName": "HrDiskStorageEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrDiskStorageAccess": {"ObjectName": "hrDiskStorageAccess", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "readWrite", "2": "readOnly"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "hrDiskStorageEntry 1", "OID": "*******.********.6.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDiskStorageTable.hrDiskStorageEntry.hrDiskStorageAccess"}, "hrDiskStorageMedia": {"ObjectName": "hrDiskStorageMedia", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "other", "2": "unknown", "3": "hardDisk", "4": "floppyDisk", "5": "opticalDiskROM", "6": "opticalDiskWORM", "7": "opticalDiskRW", "8": "ramDisk"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "hrDiskStorageEntry 2", "OID": "*******.********.6.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDiskStorageTable.hrDiskStorageEntry.hrDiskStorageMedia"}, "hrDiskStorageRemoveble": {"ObjectName": "hrDiskStorageRemoveble", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TruthValue", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDiskStorageEntry 3", "OID": "*******.********.6.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDiskStorageTable.hrDiskStorageEntry.hrDiskStorageRemoveble"}, "hrDiskStorageCapacity": {"ObjectName": "hrDiskStorageCapacity", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "KBytes", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDiskStorageEntry 4", "OID": "*******.********.6.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrDiskStorageTable.hrDiskStorageEntry.hrDiskStorageCapacity"}, "hrPartitionTable": {"ObjectName": "hrPartitionTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrPartitionEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDevice 7", "OID": "*******.********.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPartitionTable"}, "hrPartitionEntry": {"ObjectName": "hrPartitionEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrPartitionEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrPartitionTable 1", "OID": "*******.********.7.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPartitionTable.hrPartitionEntry"}, "HrPartitionEntry": {"ObjectName": "HrPartitionEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrPartitionIndex": {"ObjectName": "hrPartitionIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrPartitionEntry 1", "OID": "*******.********.7.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPartitionTable.hrPartitionEntry.hrPartitionIndex"}, "hrPartitionLabel": {"ObjectName": "hrPartitionLabel", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InternationalDisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrPartitionEntry 2", "OID": "*******.********.7.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPartitionTable.hrPartitionEntry.hrPartitionLabel"}, "hrPartitionID": {"ObjectName": "hrPartitionID", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "OCTET STRING", "STATUS": "current", "OBJECT_IDENTIFIER": "hrPartitionEntry 3", "OID": "*******.********.7.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPartitionTable.hrPartitionEntry.hrPartitionID"}, "hrPartitionSize": {"ObjectName": "hrPartitionSize", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "KBytes", "STATUS": "current", "OBJECT_IDENTIFIER": "hrPartitionEntry 4", "OID": "*******.********.7.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPartitionTable.hrPartitionEntry.hrPartitionSize"}, "hrPartitionFSIndex": {"ObjectName": "hrPartitionFSIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrPartitionEntry 5", "OID": "*******.********.7.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrPartitionTable.hrPartitionEntry.hrPartitionFSIndex"}, "hrFSTypes": {"ObjectName": "hrFSTypes", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "hrDevice 9", "OID": "*******.********.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTypes"}, "hrFSTable": {"ObjectName": "hrFSTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrFSEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrDevice 8", "OID": "*******.********.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable"}, "hrFSEntry": {"ObjectName": "hrFSEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrFSEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSTable 1", "OID": "*******.********.8.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry"}, "HrFSEntry": {"ObjectName": "HrFSEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrFSIndex": {"ObjectName": "hrFSIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSEntry 1", "OID": "*******.********.8.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry.hrFSIndex"}, "hrFSMountPoint": {"ObjectName": "hrFSMountPoint", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InternationalDisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSEntry 2", "OID": "*******.********.8.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry.hrFSMountPoint"}, "hrFSRemoteMountPoint": {"ObjectName": "hrFSRemoteMountPoint", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InternationalDisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSEntry 3", "OID": "*******.********.8.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry.hrFSRemoteMountPoint"}, "hrFSType": {"ObjectName": "hrFSType", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "AutonomousType", "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSEntry 4", "OID": "*******.********.8.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry.hrFSType"}, "hrFSAccess": {"ObjectName": "hrFSAccess", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "readWrite", "2": "readOnly"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSEntry 5", "OID": "*******.********.8.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry.hrFSAccess"}, "hrFSBootable": {"ObjectName": "hrFSBootable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TruthValue", "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSEntry 6", "OID": "*******.********.8.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry.hrFSBootable"}, "hrFSStorageIndex": {"ObjectName": "hrFSStorageIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSEntry 7", "OID": "*******.********.8.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry.hrFSStorageIndex"}, "hrFSLastFullBackupDate": {"ObjectName": "hrFSLastFullBackupDate", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DateAndTime", "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSEntry 8", "OID": "*******.********.8.1.8", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry.hrFSLastFullBackupDate"}, "hrFSLastPartialBackupDate": {"ObjectName": "hrFSLastPartialBackupDate", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DateAndTime", "STATUS": "current", "OBJECT_IDENTIFIER": "hrFSEntry 9", "OID": "*******.********.8.1.9", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrDevice.hrFSTable.hrFSEntry.hrFSLastPartialBackupDate"}, "hrSWOSIndex": {"ObjectName": "hrSWOSIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRun 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWOSIndex"}, "hrSWRunTable": {"ObjectName": "hrSWRunTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrSWRunEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRun 2", "OID": "*******.********.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWRunTable"}, "hrSWRunEntry": {"ObjectName": "hrSWRunEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrSWRunEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunTable 1", "OID": "*******.********.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWRunTable.hrSWRunEntry"}, "HrSWRunEntry": {"ObjectName": "HrSWRunEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrSWRunIndex": {"ObjectName": "hrSWRunIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunEntry 1", "OID": "*******.********.2.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWRunTable.hrSWRunEntry.hrSWRunIndex"}, "hrSWRunName": {"ObjectName": "hrSWRunName", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InternationalDisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunEntry 2", "OID": "*******.********.2.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWRunTable.hrSWRunEntry.hrSWRunName"}, "hrSWRunID": {"ObjectName": "hrSWRunID", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "ProductID", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunEntry 3", "OID": "*******.********.2.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWRunTable.hrSWRunEntry.hrSWRunID"}, "hrSWRunPath": {"ObjectName": "hr<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InternationalDisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunEntry 4", "OID": "*******.********.2.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWRunTable.hrSWRunEntry.hrSWRunPath"}, "hrSWRunParameters": {"ObjectName": "hrSWRunParameters", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InternationalDisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunEntry 5", "OID": "*******.********.2.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWRunTable.hrSWRunEntry.hrSWRunParameters"}, "hrSWRunType": {"ObjectName": "hrSWRunType", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "unknown", "2": "operatingSystem", "3": "deviceDriver", "4": "application"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunEntry 6", "OID": "*******.********.2.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWRunTable.hrSWRunEntry.hrSWRunType"}, "hrSWRunStatus": {"ObjectName": "hrSWRunStatus", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "running", "2": "runnable", "3": "notRunnable", "4": "invalid"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunEntry 7", "OID": "*******.********.2.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRun.hrSWRunTable.hrSWRunEntry.hrSWRunStatus"}, "hrSWRunPerfTable": {"ObjectName": "hrSWRunPerfTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrSWRunPerfEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunPerf 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRunPerf.hrSWRunPerfTable"}, "hrSWRunPerfEntry": {"ObjectName": "hrSWRunPerfEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrSWRunPerfEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunPerfTable 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRunPerf.hrSWRunPerfTable.hrSWRunPerfEntry"}, "HrSWRunPerfEntry": {"ObjectName": "HrSWRunPerfEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrSWRunPerfCPU": {"ObjectName": "hrSWRunPerfCPU", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunPerfEntry 1", "OID": "*******.********.1.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRunPerf.hrSWRunPerfTable.hrSWRunPerfEntry.hrSWRunPerfCPU"}, "hrSWRunPerfMem": {"ObjectName": "hrSWRunPerfMem", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "KBytes", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWRunPerfEntry 2", "OID": "*******.********.1.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWRunPerf.hrSWRunPerfTable.hrSWRunPerfEntry.hrSWRunPerfMem"}, "hrSWInstalledLastChange": {"ObjectName": "hrSWInstalledLastChange", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWInstalled 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled.hrSWInstalledLastChange"}, "hrSWInstalledLastUpdateTime": {"ObjectName": "hrSWInstalledLastUpdateTime", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "TimeTicks", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWInstalled 2", "OID": "*******.********.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled.hrSWInstalledLastUpdateTime"}, "hrSWInstalledTable": {"ObjectName": "hrSWInstalledTable", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF HrSWInstalledEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWInstalled 3", "OID": "*******.********.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled.hrSWInstalledTable"}, "hrSWInstalledEntry": {"ObjectName": "hrSWInstalledEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "HrSWInstalledEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWInstalledTable 1", "OID": "*******.********.3.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled.hrSWInstalledTable.hrSWInstalledEntry"}, "HrSWInstalledEntry": {"ObjectName": "HrSWInstalledEntry", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "SEQUENCE"}, "hrSWInstalledIndex": {"ObjectName": "hrSWInstalledIndex", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWInstalledEntry 1", "OID": "*******.2.1.25.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled.hrSWInstalledTable.hrSWInstalledEntry.hrSWInstalledIndex"}, "hrSWInstalledName": {"ObjectName": "hrSWInstalledName", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "InternationalDisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWInstalledEntry 2", "OID": "*******.2.1.25.*******", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled.hrSWInstalledTable.hrSWInstalledEntry.hrSWInstalledName"}, "hrSWInstalledID": {"ObjectName": "hrSWInstalledID", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "ProductID", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWInstalledEntry 3", "OID": "*******.********.3.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled.hrSWInstalledTable.hrSWInstalledEntry.hrSWInstalledID"}, "hrSWInstalledType": {"ObjectName": "hrSWInstalledType", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "unknown", "2": "operatingSystem", "3": "deviceDriver", "4": "application"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWInstalledEntry 4", "OID": "*******.********.3.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled.hrSWInstalledTable.hrSWInstalledEntry.hrSWInstalledType"}, "hrSWInstalledDate": {"ObjectName": "hrSWInstalledDate", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DateAndTime", "STATUS": "current", "OBJECT_IDENTIFIER": "hrSWInstalledEntry 5", "OID": "*******.********.3.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrSWInstalled.hrSWInstalledTable.hrSWInstalledEntry.hrSWInstalledDate"}, "hrMIBCompliances": {"ObjectName": "hrMIBCompliances", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "hrMIBAdminInfo 2", "OID": "*******.********.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo.hrMIBCompliances"}, "hrMIBGroups": {"ObjectName": "hrMIBGroups", "ModuleName": "HOST-RESOURCES-MIB", "OBJECT_IDENTIFIER": "hrMIBAdminInfo 3", "OID": "*******.********.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo.hrMIBGroups"}, "hrMIBCompliance": {"ObjectName": "hrMIBCompliance", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "MODULE-COMPLIANCE", "STATUS": "current", "DESCRIPTION": "The Installed Software Group.\r\n              Implementation of this group is at the discretion\r\n              of the implementor.", "OBJECT_IDENTIFIER": "hrMIBCompliances 1", "OID": "*******.********.2.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo.hrMIBCompliances.hrMIBCompliance"}, "hrSystemGroup": {"ObjectName": "hrSystemGroup", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "The Host Resources System Group.", "OBJECT_IDENTIFIER": "hrMIBGroups 1", "OID": "*******.********.3.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo.hrMIBGroups.hrSystemGroup"}, "hrStorageGroup": {"ObjectName": "hrStorageGroup", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "The Host Resources Storage Group.", "OBJECT_IDENTIFIER": "hrMIBGroups 2", "OID": "*******.********.3.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo.hrMIBGroups.hrStorageGroup"}, "hrDeviceGroup": {"ObjectName": "hrDeviceGroup", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "The Host Resources Device Group.", "OBJECT_IDENTIFIER": "hrMIBGroups 3", "OID": "*******.********.3.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo.hrMIBGroups.hrDeviceGroup"}, "hrSWRunGroup": {"ObjectName": "hrSWRunGroup", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "The Host Resources Running Software Group.", "OBJECT_IDENTIFIER": "hrMIBGroups 4", "OID": "*******.********.3.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo.hrMIBGroups.hrSWRunGroup"}, "hrSWRunPerfGroup": {"ObjectName": "hrSWRunPerfGroup", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "The Host Resources Running Software\r\n            Performance Group.", "OBJECT_IDENTIFIER": "hrMIBGroups 5", "OID": "*******.********.3.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo.hrMIBGroups.hrSWRunPerfGroup"}, "hrSWInstalledGroup": {"ObjectName": "hrSWInstalledGroup", "ModuleName": "HOST-RESOURCES-MIB", "MACRO": "OBJECT-GROUP", "STATUS": "current", "DESCRIPTION": "The Host Resources Installed Software Group.", "OBJECT_IDENTIFIER": "hrMIBGroups 6", "OID": "*******.********.3.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.host.hrMIBAdminInfo.hrMIBGroups.hrSWInstalledGroup"}}, "MGMT-SMI": {"IMPORTS": {"SNMPv2-SMI": ["MODULE-IDENTITY", "OBJECT-IDENTITY", "enterprises"]}, "atop": {"ObjectName": "atop", "ModuleName": "MGMT-SMI", "MACRO": "MODULE-IDENTITY", "LAST-UPDATED": "201309240000Z", "ORGANIZATION": "", "CONTACT-INFO": "", "DESCRIPTION": "Initial version of this MIB module.", "OBJECT_IDENTIFIER": "enterprises 3755", "OID": "*******.4.1.3755", "NameSpace": "iso.org.dod.internet.private.enterprises.atop"}, "products": {"ObjectName": "products", "ModuleName": "MGMT-SMI", "OBJECT_IDENTIFIER": "atop 0", "OID": "*******.4.1.3755.0", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.products"}, "managedSwitch": {"ObjectName": "managedSwitch", "ModuleName": "MGMT-SMI", "OBJECT_IDENTIFIER": "products 0", "OID": "*******.4.1.3755.0.0", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.products.managedSwitch"}, "mgmtSwitch": {"ObjectName": "mgmtSwitch", "ModuleName": "MGMT-SMI", "MACRO": "OBJECT-IDENTITY", "STATUS": "current", "DESCRIPTION": "switchMgmt is the root OBJECT IDENTIFIER from\n         which object id values are assigned.", "OBJECT_IDENTIFIER": "managedSwitch 70", "OID": "*******.4.1.3755.0.0.70", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.products.managedSwitch.mgmtSwitch"}}, "MGMT-TC": {"IMPORTS": {"SNMPv2-SMI": ["Gauge32"], "SNMPv2-TC": ["TEXTUAL-CONVENTION"]}, "MGMTInteger8": {"ObjectName": "MGMTInteger8", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION"}, "MGMTInteger16": {"ObjectName": "MGMTInteger16", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION"}, "MGMTInteger64": {"ObjectName": "MGMTInteger64", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MGMTUnsigned8": {"ObjectName": "MGMTUnsigned8", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Gauge32"}, "MGMTUnsigned16": {"ObjectName": "MGMTUnsigned16", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Gauge32"}, "MGMTUnsigned64": {"ObjectName": "MGMTUnsigned64", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MGMTInteger32e-9": {"ObjectName": "MGMTInteger32e-9", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION"}, "MGMTTimeStamp": {"ObjectName": "MGMTTimeStamp", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MGMTEtherType": {"ObjectName": "MGMTEtherType", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Gauge32"}, "MGMTPsecUserBitmaskType": {"ObjectName": "MGMTPsecUserBitmaskType", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Gauge32"}, "MGMTInterfaceIndex": {"ObjectName": "MGMTInterfaceIndex", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Integer32"}, "MGMTRowEditorState": {"ObjectName": "MGMTRowEditorState", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Gauge32"}, "MGMTPercent": {"ObjectName": "MGMTPercent", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Gauge32(0..100)"}, "MGMTPortList": {"ObjectName": "MGMTPortList", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MGMTVlan": {"ObjectName": "MGMTVlan", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Gauge32"}, "MGMTVlanOrZero": {"ObjectName": "MGMTVlanOrZero", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "Gauge32"}, "MGMTVlanListQuarter": {"ObjectName": "MGMTVlanListQuarter", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MGMTDisplayString": {"ObjectName": "MGMTDisplayString", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MGMTInetAddress": {"ObjectName": "MGMTInetAddress", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MGMTIpAddress": {"ObjectName": "MGMTIpAddress", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MGMTVclProtoEncap": {"ObjectName": "MGMTVclProtoEncap", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MGMTBitType": {"ObjectName": "MGMTBitType", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " any", "1": "zero", "2 ": "one"}}}, "MGMTDestMacType": {"ObjectName": "MGMTDestMacType", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " any", "1": "unicast", "2": "multicast", "3 ": "broadcast"}}}, "MGMTVcapKeyType": {"ObjectName": "MGMTVcapKeyType", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " normal", "1": "doubleTag", "2": "ipAddr", "3 ": "macIpAddr"}}}, "MGMTVlanTagPriority": {"ObjectName": "MGMTVlanTagPriority", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " any", "1": "value0", "2": "value1", "3": "value2", "4": "value3", "5": "value4", "6": "value5", "7": "value6", "8": "value7", "9": "range0to1", "10": "range2to3", "11": "range4to5", "12": "range6to7", "13": "range0to3", "14 ": "range4to7"}}}, "MGMTVlanTagType": {"ObjectName": "MGMTVlanTagType", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " any", "1": "untagged", "2": "tagged", "3": "cTagged", "4 ": "sTagged"}}}, "MGMTASRType": {"ObjectName": "MGMTASRType", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " any", "1": "specific", "2 ": "range"}}}, "MGMTAdvDestMacType": {"ObjectName": "MGMTAdvDestMacType", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " any", "1": "unicast", "2": "multicast", "3": "broadcast", "4 ": "specific"}}}, "MGMTASType": {"ObjectName": "MGMTASType", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " any", "1 ": "specific"}}}, "MGMTSfpTransceiver": {"ObjectName": "MGMTSfpTransceiver", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": "none", "1": "notSupported", "2": "sfp100FX", "3": "sfp100BaseLx", "4": "sfp100BaseZx", "5": "sfp100BaseSx", "6": "sfp100BaseBx10", "7": "sfp100BaseT", "8": "sfp1000BaseBx10", "9": "sfp1000BaseT", "10": "sfp1000BaseCx", "11": "sfp1000BaseSx", "12": "sfp1000BaseLx", "13": "sfp1000BaseZx", "14": "sfp1000BaseLr", "15": "sfp1000BaseX", "16": "sfp2G5", "17": "sfp5G", "18": "sfp10G", "19": "sfp10GSr", "20": "sfp10GLr", "21": "sfp10GLrm", "22": "sfp10GEr", "23": "sfp10GDac"}}}, "MGMTMepDmTimeUnit": {"ObjectName": "MGMTMepDmTimeUnit", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " microSeconds", "1 ": "nanoSeconds"}}}, "MGMTMepInstanceDirection": {"ObjectName": "MGMTMepInstanceDirection", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " down", "1 ": "up"}}}, "MGMTMepTxRate": {"ObjectName": "MGMTMepTxRate", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": " invalid", "1": "frames300PerSecond", "2": "frames100PerSecond", "3": "frames10PerSecond", "4": "frames1PerSecond", "5": "frames6PerMinute", "6": "frames1PerMinute", "7 ": "frames6PerHour"}}}, "MGMTPortStatusSpeed": {"ObjectName": "MGMTPortStatusSpeed", "ModuleName": "MGMT-TC", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": {"INTEGER": {"0": "undefined", "1": "speed10M", "2": "speed100M", "3": "speed1G", "4": "speed2G5", "5": "speed5G", "6": "speed10G", "7": "speed12G"}}}}, "Atop-EHG2408-L2MGMT-MIB": {"IMPORTS": {"SNMPv2-TC": ["DateAndTime"], "RFC1155-SMI": ["enterprises"]}, "atop": {"ObjectName": "atop", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "enterprises 13576", "OID": "*******.4.1.13576", "NameSpace": "iso.org.dod.internet.private.enterprises.atop"}, "atop-ieswitch": {"ObjectName": "atop-ieswitch", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "atop 7", "OID": "*******.4.1.3755.7", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch"}, "insatopEHG2408": {"ObjectName": "insatopEHG2408", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "atop-ieswitch 5001", "OID": "*******.4.1.3755.7.5001", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408"}, "atopEHG2408": {"ObjectName": "atopEHG2408", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "MODULE-IDENTITY", "LAST-UPDATED": "1503050000Z", "ORGANIZATION": "", "CONTACT-INFO": " :         \t\t\n            \n            \n            \t", "DESCRIPTION": "Initial version of this MIB.", "OBJECT_IDENTIFIER": "insatopEHG2408 1", "OID": "*******.4.1.3755.7.5001.1", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408"}, "systemInfo": {"ObjectName": "systemInfo", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "atopEHG2408 1", "OID": "*******.4.1.3755.7.5001.1.1", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo"}, "basicSetting": {"ObjectName": "basicSetting", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "atopEHG2408 2", "OID": "*******.4.1.3755.7.5001.1.2", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting"}, "snmp": {"ObjectName": "snmp", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "atopEHG2408 8", "OID": "*******.4.1.3755.7.5001.1.8", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.snmp"}, "DisplayString": {"ObjectName": "DisplayString", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "TEXTUAL-CONVENTION", "SYNTAX": "OCTET STRING"}, "MacAddress": {"ObjectName": "<PERSON><PERSON><PERSON><PERSON>", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OCTET STRING"}, "PortList": {"ObjectName": "PortList", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OCTET STRING"}, "systemDescr": {"ObjectName": "systemDescr", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "ACCESS": "read-only", "STATUS": "current", "OBJECT_IDENTIFIER": "systemInfo 4", "OID": "*******.4.1.3755.7.5001.1.1.4", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.systemDescr"}, "systemFwVer": {"ObjectName": "systemFwVer", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "systemInfo 5", "OID": "*******.4.1.3755.7.5001.1.1.5", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.systemFwVer"}, "systemMacAddress": {"ObjectName": "systemMacAddress", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "systemInfo 6", "OID": "*******.4.1.3755.7.5001.1.1.6", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.systemMacAddress"}, "systemKernelVer": {"ObjectName": "systemKernelVer", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "systemInfo 7", "OID": "*******.4.1.3755.7.5001.1.1.7", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.systemKernelVer"}, "systemModelName": {"ObjectName": "systemModelName", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "systemInfo 10", "OID": "*******.4.1.3755.7.5001.1.1.10", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.systemModelName"}, "consoleInfo": {"ObjectName": "consoleInfo", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "systemInfo 8", "OID": "*******.4.1.3755.7.5001.1.1.8", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.consoleInfo"}, "consoleInfoBaudRate": {"ObjectName": "consoleInfoBaudRate", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "ACCESS": "read-only", "STATUS": "current", "OBJECT_IDENTIFIER": "consoleInfo 1", "OID": "*******.4.1.3755.7.5001.1.1.8.1", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.consoleInfo.consoleInfoBaudRate"}, "consoleInfoDataBits": {"ObjectName": "consoleInfoDataBits", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "ACCESS": "read-only", "STATUS": "current", "OBJECT_IDENTIFIER": "consoleInfo 2", "OID": "*******.4.1.3755.7.5001.1.1.8.2", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.consoleInfo.consoleInfoDataBits"}, "consoleInfoParity": {"ObjectName": "consoleInfoParity", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "none"}}, "ACCESS": "read-only", "STATUS": "current", "OBJECT_IDENTIFIER": "consoleInfo 3", "OID": "*******.4.1.3755.7.5001.1.1.8.3", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.consoleInfo.consoleInfoParity"}, "consoleInfoStopBit": {"ObjectName": "consoleInfoStopBit", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "ACCESS": "read-only", "STATUS": "current", "OBJECT_IDENTIFIER": "consoleInfo 4", "OID": "*******.4.1.3755.7.5001.1.1.8.4", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.consoleInfo.consoleInfoStopBit"}, "consoleInfoFlowControl": {"ObjectName": "consoleInfoFlowControl", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "none"}}, "ACCESS": "read-only", "STATUS": "current", "OBJECT_IDENTIFIER": "consoleInfo 5", "OID": "*******.4.1.3755.7.5001.*******", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.systemInfo.consoleInfo.consoleInfoFlowControl"}, "adminPassword": {"ObjectName": "adminPassword", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "basicSetting 2", "OID": "*******.4.1.3755.7.5001.1.2.2", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.adminPassword"}, "ipConfiguration": {"ObjectName": "ipConfiguration", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "basicSetting 3", "OID": "*******.4.1.3755.7.5001.1.2.3", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.ipConfiguration"}, "factoryDefault": {"ObjectName": "factoryDefault", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "basicSetting 8", "OID": "*******.4.1.3755.7.5001.1.2.8", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.factoryDefault"}, "systemReboot": {"ObjectName": "systemReboot", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "OBJECT_IDENTIFIER": "basicSetting 9", "OID": "*******.4.1.3755.7.5001.1.2.9", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.systemReboot"}, "adminPasswordUserName": {"ObjectName": "adminPasswordUserName", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "adminPassword 1", "OID": "*******.4.1.3755.7.5001.*******", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.adminPassword.adminPasswordUserName"}, "adminPasswordPassword": {"ObjectName": "adminPasswordPassword", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "adminPassword 2", "OID": "*******.4.1.3755.7.5001.*******", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.adminPassword.adminPasswordPassword"}, "ipConfigurationTable": {"ObjectName": "ipConfigurationTable", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF IPconfigurationEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ipConfiguration 1", "OID": "*******.4.1.3755.7.5001.*******", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.ipConfiguration.ipConfigurationTable"}, "ipConfigurationEntry": {"ObjectName": "ipConfigurationEntry", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "IPconfigurationEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "ipConfigurationTable 1", "OID": "*******.4.1.3755.7.5001.*******.1", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.ipConfiguration.ipConfigurationTable.ipConfigurationEntry"}, "IPconfigurationEntry": {"ObjectName": "IPconfigurationEntry", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "SEQUENCE"}, "ipConfigurationIndex": {"ObjectName": "ipConfigurationIndex", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "ipConfigurationEntry 1", "OID": "*******.4.1.3755.7.5001.*******.1.1", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.ipConfiguration.ipConfigurationTable.ipConfigurationEntry.ipConfigurationIndex"}, "ipConfigurationDHCPStatus": {"ObjectName": "ipConfigurationDHCPStatus", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "enabled", "2": "disabled"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "ipConfigurationEntry 2", "OID": "*******.4.1.3755.7.5001.*******.1.2", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.ipConfiguration.ipConfigurationTable.ipConfigurationEntry.ipConfigurationDHCPStatus"}, "ipConfigurationAddress": {"ObjectName": "ipConfigurationAddress", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "ipConfigurationEntry 3", "OID": "*******.4.1.3755.7.5001.*******.1.3", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.ipConfiguration.ipConfigurationTable.ipConfigurationEntry.ipConfigurationAddress"}, "ipConfigurationSubMask": {"ObjectName": "ipConfigurationSubMask", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "ipConfigurationEntry 4", "OID": "*******.4.1.3755.7.5001.*******.1.4", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.ipConfiguration.ipConfigurationTable.ipConfigurationEntry.ipConfigurationSubMask"}, "ipConfigurationGateway": {"ObjectName": "ipConfigurationGateway", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "ipConfigurationEntry 5", "OID": "*******.4.1.3755.7.5001.*******.1.5", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.ipConfiguration.ipConfigurationTable.ipConfigurationEntry.ipConfigurationGateway"}, "ipConfigurationDNS1": {"ObjectName": "ipConfigurationDNS1", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "STATUS": "current", "OBJECT_IDENTIFIER": "ipConfigurationEntry 6", "OID": "*******.4.1.3755.7.5001.*******.1.6", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.ipConfiguration.ipConfigurationTable.ipConfigurationEntry.ipConfigurationDNS1"}, "factoryDefaultAction": {"ObjectName": "factoryDefaultAction", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "active", "2": "notActive"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "factoryDefault 1", "OID": "*******.4.1.3755.7.5001.*******", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.factoryDefault.factoryDefaultAction"}, "systemRebootAction": {"ObjectName": "systemRebootAction", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "active", "2": "notActive"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "systemReboot 1", "OID": "*******.4.1.3755.7.5001.*******", "NameSpace": "iso.org.dod.internet.private.enterprises.atop.atop-ieswitch.insatopEHG2408.atopEHG2408.basicSetting.systemReboot.systemRebootAction"}, "snmpCommunityStringTable": {"ObjectName": "snmpCommunityStringTable", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF SnmpCommunityStringEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "snmp 5", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpCommunityStringTable"}, "snmpCommunityStringEntry": {"ObjectName": "snmpCommunityStringEntry", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SnmpCommunityStringEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpCommunityStringTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpCommunityStringTable.snmpCommunityStringEntry"}, "SnmpCommunityStringEntry": {"ObjectName": "SnmpCommunityStringEntry", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "SEQUENCE"}, "snmpCommunityStringIndex": {"ObjectName": "snmpCommunityStringIndex", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpCommunityStringEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpCommunityStringTable.snmpCommunityStringEntry.snmpCommunityStringIndex"}, "snmpCommunityStringName": {"ObjectName": "snmpCommunityStringName", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpCommunityStringEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpCommunityStringTable.snmpCommunityStringEntry.snmpCommunityStringName"}, "snmpCommunityStringAttribute": {"ObjectName": "snmpCommunityStringAttribute", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "ro", "2": "rw", "3": "r-sysinfo-only"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "snmpCommunityStringEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpCommunityStringTable.snmpCommunityStringEntry.snmpCommunityStringAttribute"}, "snmpCommunityStringStatus": {"ObjectName": "snmpCommunityStringStatus", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "valid", "2": "creatrequest", "3": "undercreation", "4": "invalid"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "snmpCommunityStringEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpCommunityStringTable.snmpCommunityStringEntry.snmpCommunityStringStatus"}, "snmpTrapServerTable": {"ObjectName": "snmpTrapServerTable", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF SnmpTrapServerEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "snmp 6", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpTrapServerTable"}, "snmpTrapServerEntry": {"ObjectName": "snmpTrapServerEntry", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SnmpTrapServerEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpTrapServerTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpTrapServerTable.snmpTrapServerEntry"}, "SnmpTrapServerEntry": {"ObjectName": "SnmpTrapServerEntry", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "SEQUENCE"}, "snmpTrapServerIndex": {"ObjectName": "snmpTrapServerIndex", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpTrapServerEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpTrapServerTable.snmpTrapServerEntry.snmpTrapServerIndex"}, "snmpTrapServerTrapComm": {"ObjectName": "snmpTrapServerTrapComm", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpTrapServerEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpTrapServerTable.snmpTrapServerEntry.snmpTrapServerTrapComm"}, "snmpTrapServerStatus": {"ObjectName": "snmpTrapServerStatus", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "valid", "2": "creatrequest", "3": "undercreation", "4": "invalid"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "snmpTrapServerEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpTrapServerTable.snmpTrapServerEntry.snmpTrapServerStatus"}, "snmpTrapServerPort": {"ObjectName": "snmpTrapServerPort", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpTrapServerEntry 6", "OID": "*******.********.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpTrapServerTable.snmpTrapServerEntry.snmpTrapServerPort"}, "snmpTrapServerIP": {"ObjectName": "snmpTrapServerIP", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpTrapServerEntry 7", "OID": "*******.********.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpTrapServerTable.snmpTrapServerEntry.snmpTrapServerIP"}, "snmpUserTable": {"ObjectName": "snmpUserTable", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SEQUENCE OF SnmpUserEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "snmp 7", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpUserTable"}, "snmpUserEntry": {"ObjectName": "snmpUserEntry", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "SnmpUserEntry", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpUserTable 1", "OID": "*******.********.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpUserTable.snmpUserEntry"}, "SnmpUserEntry": {"ObjectName": "SnmpUserEntry", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "SEQUENCE"}, "snmpUserIndex": {"ObjectName": "snmpUserIndex", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "Integer32", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpUserEntry 1", "OID": "*******.********.1.1", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpUserTable.snmpUserEntry.snmpUserIndex"}, "snmpUserName": {"ObjectName": "snmpUserName", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpUserEntry 2", "OID": "*******.********.1.2", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpUserTable.snmpUserEntry.snmpUserName"}, "snmpUserAuthType": {"ObjectName": "snmpUserAuthType", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER{", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpUserEntry 3", "OID": "*******.********.1.3", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpUserTable.snmpUserEntry.snmpUserAuthType"}, "snmpUserAuthPwd": {"ObjectName": "snmpUserAuthPwd", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString(SIZE(0|8..32))", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpUserEntry 4", "OID": "*******.********.1.4", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpUserTable.snmpUserEntry.snmpUserAuthPwd"}, "snmpUserEncrypType": {"ObjectName": "snmpUserEncrypType", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "INTEGER{", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpUserEntry 5", "OID": "*******.********.1.5", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpUserTable.snmpUserEntry.snmpUserEncrypType"}, "snmpUserEncrypKey": {"ObjectName": "snmpUserEncrypKey", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": "DisplayString(SIZE(0|8..32))", "STATUS": "current", "OBJECT_IDENTIFIER": "snmpUserEntry 6", "OID": "*******.********.1.6", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpUserTable.snmpUserEntry.snmpUserEncrypKey"}, "snmpUserStatus": {"ObjectName": "snmpUserStatus", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "valid", "2": "invalid"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "snmpUserEntry 7", "OID": "*******.********.1.7", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpUserTable.snmpUserEntry.snmpUserStatus"}, "snmpStatus": {"ObjectName": "snmpStatus", "ModuleName": "Atop-EHG2408-L2MGMT-MIB", "MACRO": "OBJECT-TYPE", "SYNTAX": {"INTEGER": {"1": "enabled", "2": "disabled"}}, "STATUS": "current", "OBJECT_IDENTIFIER": "snmp 8", "OID": "*******.********", "NameSpace": "iso.org.dod.internet.mgmt.mib-2.snmp.snmpStatus"}}}