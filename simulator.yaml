#exmaple array
#type: <PERSON>H7506,<PERSON>H7508,<PERSON><PERSON><PERSON>12,<PERSON><PERSON><PERSON>20,EHG750x

# environments:
  # simulator_group:                     #create one of device,useing fixed ip,fixed mac
      # type: "EH7520"
      # startMacAddress: "00-60-e9-18-01-02"  #if not exit ,create random mac automatically
      # startPreFixIp: "************/24"
  # simulator_group1:                    #create fiv of devices,useing fixed ip,random mac
      # number: 5
      # type: "EH7508"
      # macAddress: "00-60-e9-18-99-99"  #if not exit ,create random mac automatically
      # startPreFixIp: "************/24"
    
# environments:
  # simulator_group: #create once device using macAddress:"00-60-e9-18-01-99"
      # type: "EH7520"
      # startPreFixIp: "************/24"
      # startMacAddress: "00-60-e9-18-01-99"
  # simulator_group1: #create five of devices use random macAddress
      # number: 5
      # type: "EH7508"
      # startPreFixIp: "************/24"
  # simulator_group2: #create three of devices with macAddress increased from "00-60-e9-18-05-01"
      # number: 3
      # type: "EH7508"
      # startPreFixIp: "************/24"
      # startMacAddress: "00-60-e9-18-05-01"
environments:
  simulator_group1: 
      number: 10
      type: "EH7520"
      startPreFixIp: "***********/24"
      startMacAddress: "00-60-E9-18-01-01"
  simulator_group2: 
      number: 10
      type: "EH7508"
      startPreFixIp: "***********/24"
      startMacAddress: "00-60-E9-18-0A-01"