package mnms

import (
	"fmt"
	"os"
	"testing"
)

var name = "daemon_test"
var tes_func = func() {
	fmt.Fprintln(os.<PERSON>derr, name+"test function")
}

func TestDaemonUninstall(t *testing.T) {
	s, err := NewDaemon(name, []string{name})
	if err != nil {
		t.<PERSON>(err)
	}
	s.<PERSON>Run<PERSON>vent(tes_func)
	err = s.Run<PERSON>ode("uninstall")
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
	}
}

func TestDaemonStop(t *testing.T) {
	s, err := NewDaemon(name, []string{name})
	if err != nil {
		t.<PERSON>al(err)
	}
	s.<PERSON>un<PERSON>(tes_func)
	err = s.Run<PERSON>ode("stop")
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
	}
}
func TestDaemonStatus(t *testing.T) {
	s, err := NewDaemon(name, []string{name})
	if err != nil {
		t.Fatal(err)
	}
	s.RegisterRunEvent(tes_func)
	err = s.<PERSON>Mode("status")
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
	}
}
