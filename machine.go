package mnms

import (
	"fmt"
	"os"
	"runtime"

	"github.com/denisbrodbeck/machineid"
	"github.com/shirou/gopsutil/cpu"
	"github.com/shirou/gopsutil/mem"
	"github.com/shirou/gopsutil/net"
)

// MachineInfo holds the collected machine information
type MachineInfo struct {
	Hostname          string              `json:"hostname"`
	OS                string              `json:"os"`
	Architecture      string              `json:"architecture"`
	CPUInfo           []cpu.InfoStat      `json:"cpu_info"`
	TotalMemory       uint64              `json:"total_memory"`
	FreeMemory        uint64              `json:"free_memory"`
	NetworkInterfaces []net.InterfaceStat `json:"network_interfaces"`
	MachineID         string              `json:"machine_id"`
	Client            string              `json:"client"`
}

var machineInfo *MachineInfo

// GetMachineInfo collects and returns machine information
func GetMachineInfo() (*MachineInfo, error) {

	// Only Memory Information will change, update every time
	vmem, err := mem.VirtualMemory()
	if err != nil {
		return nil, fmt.Errorf("error getting memory info: %v", err)
	}

	// Other MachineInfo never changes, so cache it
	if machineInfo != nil {
		machineInfo.FreeMemory = vmem.Free
		return machineInfo, nil
	}
	// Client
	client := "unknown"
	if QC.Name != "" {
		client = QC.Name
	}

	// Hostname
	hostname, err := os.Hostname()
	if err != nil {
		return nil, fmt.Errorf("error getting hostname: %v", err)
	}

	// OS and Architecture
	os := runtime.GOOS
	architecture := runtime.GOARCH

	// CPU Information
	cpuInfo, err := cpu.Info()
	if err != nil {
		return nil, fmt.Errorf("error getting CPU info: %v", err)
	}

	// Network Interfaces
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("error getting network interfaces: %v", err)
	}

	// Machine ID
	machineID, err := machineid.ID()
	if err != nil {
		return nil, fmt.Errorf("error getting machine ID: %v", err)
	}

	// Populate MachineInfo struct
	machineInfo := &MachineInfo{
		Client:       client,
		Hostname:     hostname,
		OS:           os,
		Architecture: architecture,
		CPUInfo:      cpuInfo,
		TotalMemory:  vmem.Total,
		FreeMemory:   vmem.Free,

		NetworkInterfaces: interfaces,
		MachineID:         machineID,
	}

	return machineInfo, nil
}