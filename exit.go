package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"runtime"
	"time"

	"github.com/qeof/q"
)

func DoExit(code int) {
	q.Q("exiting mnms", code)
	if QC.DumpStackTrace && code != 0 {
		buf := make([]byte, 1<<16)
		stackSize := runtime.Stack(buf, true)
		q.Q(string(buf[0:stackSize]))
	}
	os.Exit(code)
}
func ClientExit() {
	if !QC.IsRoot {
		ci := ClientInfo{
			Name:   QC.Name,
			Kind:   QC.Kind,
			Start:  QC.SvcStartTime,
			Now:    int(time.Now().Unix()),
			Status: "inactive",
		}
		jsonBytes, err := json.Marshal(ci)
		if err != nil {
			q.Q(err)
		}
		resp, err := PostWithToken(QC.RootURL+"/api/v1/register",
			QC.AdminToken, bytes.NewBuffer(jsonBytes))
		if err != nil {
			q.Q(err)
		}
		if resp != nil {
			if resp.StatusCode == 500 {
				// print message while exit client
				bodyText, _ := io.ReadAll(resp.Body)
				q.Q(string(bodyText))
			}
			resp.Body.Close()
		}
	}
}

func SyslogExit() {
	err := SendSyslog(LOG_NOTICE, "main", "exiting main() "+QC.Name)
	if err != nil {
		q.Q(err)
	}
	if err := recover(); err != nil {
		q.Q("recover: exit with exception", err)
		fmt.Fprintf(os.Stderr, "recover: exit with exception: %v\n", err)
		QC.DumpStackTrace = true
		DoExit(1)
	}
}
