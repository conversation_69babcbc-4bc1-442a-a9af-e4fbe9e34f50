package mnms

import (
	"bytes"
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/qeof/q"
)

const (
	configmac     = "00-60-E9-18-0A-01"
	configoldip   = "************"
	confignewip   = "**************"
	confignetmask = "*************"
	configgateway = "**************"
)

const (
	hostmac   = "00-60-E9-18-0A-02"
	hostvalue = "atoptest"
)

const loginuser = "admin"
const loginpwd = "default"

func init() {
	QC.IsRoot = true
}

func TestGwdBeep(t *testing.T) {
	t.Skip()
	err := createSimulator()
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	defer ShutdownSimulator()

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()

		HTTPMain()
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		GwdMain()
	}()
	time.Sleep(time.Second * 1)

	d, err := GetDevData()
	if err != nil {
		t.Fatal(err)
	}
	//gwd beep [mac address]
	cmd := fmt.Sprintf("gwd beep %v", d.Mac)
	cmdJson := `[{"command":"` + cmd + `"}]`

	url := fmt.Sprintf("http://localhost:%d/api/v1/commands", QC.Port)

	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	token, err := GetToken("admin")
	if err != nil {
		t.Fatal(err)
	}

	resp, err := PostWithToken(url, token, bytes.NewBuffer([]byte(cmdJson)))
	if resp.StatusCode != 200 || err != nil {
		t.Fatalf("post err: %v", err)
	}
	if resp != nil {
		q.Q(resp.Header)
		//save close
		defer resp.Body.Close()
	}

	q.Q(QC.CmdData)
	q.Q(QC.Clients)
	_ = CheckCmds()
}

func TestGwdReset(t *testing.T) {
	t.Skip()
	err := createSimulator()
	if err != nil {
		t.Fatal(err)
	}
	defer ShutdownSimulator()

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()

		HTTPMain()
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		GwdMain()
	}()
	time.Sleep(time.Second * 1)

	d, err := GetDevData()
	if err != nil {
		t.Fatal(err)
	}
	// gwd reset [mac address]
	cmd := fmt.Sprintf("gwd reset %v", d.Mac)
	cmdJson := `[{"command":"` + cmd + `"}]`

	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	token, err := GetToken("admin")
	if err != nil {
		t.Fatal(err)
	}

	url := fmt.Sprintf("http://localhost:%d/api/v1/commands", QC.Port)

	resp, err := PostWithToken(url, token, bytes.NewBuffer([]byte(cmdJson)))
	if resp.StatusCode != 200 || err != nil {
		t.Fatalf("post err: %v", err)
	}
	if resp != nil {
		q.Q(resp.Header)
		// save close
		defer resp.Body.Close()
	}

	q.Q(QC.CmdData)
	q.Q(QC.Clients)
	_ = CheckCmds()
}

func TestGwdConfigIP(t *testing.T) {
	t.Skip()
	err := createSimulator()
	if err != nil {
		t.Fatal(err)
	}
	defer ShutdownSimulator()

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()

		HTTPMain()
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		GwdMain()
	}()
	time.Sleep(time.Second * 1)
	d, err := GetDevData()
	if err != nil {
		t.Fatal(err)
	}
	//gwd config network set [mac] [ip] [new ip] [mask] [gateway] [hostname]
	cmd := fmt.Sprintf("gwd config network set %v %v %v %v %v %v", d.Mac, configoldip, confignewip, confignetmask, configgateway, hostvalue)
	cmdJson := `[{"command":"` + cmd + `"}]`

	_ = cleanMNMSConfig()
	_ = InitDefaultMNMSConfigIfNotExist()
	defer func() {
		_ = cleanMNMSConfig()
	}()
	token, err := GetToken("admin")
	if err != nil {
		t.Fatal(err)
	}

	url := fmt.Sprintf("http://localhost:%d/api/v1/commands", QC.Port)
	resp, err := PostWithToken(url, token, bytes.NewBuffer([]byte(cmdJson)))
	if err != nil {
		t.Fatal(err)
	}
	if resp == nil {
		t.Fatal("no resp")
	}
	if resp.StatusCode != 200 {
		t.Fatalf("post status code %v", resp.StatusCode)
	}
	if resp != nil {
		q.Q(resp.Header)

	}

	// save close, already check resp is not nil
	defer resp.Body.Close()
	q.Q(QC.CmdData)
	q.Q(QC.Clients)
	_ = CheckCmds()
}

func GetDevData() (DevInfo, error) {
	if len(QC.DevData) == 0 {
		return DevInfo{}, errors.New("no device exist")
	} else {
		for _, v := range QC.DevData {
			return v, nil
		}
	}
	return DevInfo{}, errors.New("no device exist")
}
