package main

import (
	"encoding/pem"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime/debug"
	"sync"
	"time"

	"mnms"
	_ "net/http/pprof"

	"github.com/qeof/q"
)

var Version string

func printVersion() {
	if Version == "" {
		Version = "Development version"
	}
	fmt.Fprintf(os.Stderr, "License: \n")
	fmt.Fprintf(os.Stderr, "  Program: %s\n", mnms.QC.License.Program)
	fmt.Fprintf(os.Stderr, "  Version: %s\n", mnms.QC.License.Version)
	fmt.Fprintf(os.Stderr, "  Generated: %s\n", mnms.QC.License.Generated)
	fmt.Fprintf(os.Stderr, "  NumClients: %d\n", mnms.QC.License.NumClients)
	fmt.Fprintf(os.Stderr, "  NumOfDevice: %d\n", mnms.QC.License.NumDevice)
	fmt.Fprintf(os.Stderr, "  MachineID: %s\n", mnms.QC.License.MachineID)
	fmt.Fprintf(os.Stderr, "  EnabledFeatures: %s\n", mnms.QC.License.EnabledFeatures)
	info, _ := debug.ReadBuildInfo()
	fmt.Fprintln(os.Stderr, "Build information:")
	fmt.Fprintln(os.Stderr, info.GoVersion, info.Main, info.Settings)
}

func main() {
	var wg sync.WaitGroup
	q.O = "stderr"
	q.P = ""

	stop := func() {
		mnms.ClientExit()
		mnms.SyslogExit()
	}
	flagversion := flag.Bool("version", false, "print version")
	flag.IntVar(&mnms.QC.Port, "p", mnms.QC.Port, "port")
	flag.StringVar(&mnms.QC.Name, "n", "", "name")
	flag.StringVar(&q.O, "O", "stderr", "debug log output")
	dp := flag.String("P", "", "debug log pattern string")
	debuglog := flag.Bool("debuglog", false, "enable debug log, this will override -P to .*")
	flag.BoolVar(&mnms.QC.DumpStackTrace, "ds", false, "dump stack trace when exiting with non zero code")
	flag.IntVar(&mnms.QC.CmdInterval, "ic", mnms.QC.CmdInterval, "command processing interval")
	flag.IntVar(&mnms.QC.RegisterInterval, "ir", mnms.QC.RegisterInterval, "Network service registration interval")
	flag.IntVar(&mnms.QC.GwdInterval, "ig", mnms.QC.GwdInterval, "device scan interval")
	nosyslog := flag.Bool("nosyslog", false, "no syslog service")
	nohttp := flag.Bool("nohttp", false, "no http service")
	fake := flag.Bool("fake", false, "fake mode for testing")
	_ = flag.Bool("M", false, "monitor mode")
	flag.StringVar(&mnms.QC.SyslogServerAddr, "ss",
		mnms.QC.SyslogServerAddr, "syslog server address")
	flag.StringVar(&mnms.QC.SyslogLocalPath, "so", mnms.QC.SyslogLocalPath, "local path of syslog")
	flag.UintVar(&mnms.QC.SyslogFileSize, "sf", mnms.QC.SyslogFileSize, "file size(megabytes) of syslog")
	flag.BoolVar(&mnms.QC.SyslogCompress, "sc", mnms.QC.SyslogCompress, "enable compress file of backup syslog")
	prikeyfile := flag.String("privkey", "", "private key file")
	license := flag.String("license", "./nmskey", "license file")
	pp := flag.Bool("pprof", false, "enable pprof analysis")
	nowg := flag.Bool("nowg", false, "no nms wireguard service")
	wgname := flag.String("wgname", "nms-wg0", "wireguard vpn interface name")
	configFile := flag.String("config", "bbrootconfig.enc", "configuration file name")
	flag.IntVar(&mnms.QC.SshServerPort, "sp", mnms.QC.SshServerPort, "ssh server port")
	nosshsrv := flag.Bool("nosshsrv", false, "no ssh server")
	flag.StringVar(&mnms.QC.SshServerKeyPath, "sk", mnms.QC.SshServerKeyPath, "ssh key path")

	var daemon string
	flag.StringVar(&daemon, mnms.DaemonFlag, "", mnms.Usage)
	flag.Parse()
	if *flagversion {
		mnms.LoadLicenseToQC(*license)
		printVersion()
		mnms.DoExit(0)
	}

	mnms.QC.IsRoot = true

	// version
	if Version != "" {
		mnms.QC.Version = Version
	}

	// enable Daemon
	s, err := mnms.NewDaemon(mnms.QC.Name, os.Args)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
	service := func() {
		// register daemon into root
		mnms.RegisterDaemon(s)
		mnms.LoadLicenseToQC(*license)
		if *pp {
			go func() {
				q.Q(http.ListenAndServe("localhost:6060", nil))
			}()
		}

		if *debuglog {
			*dp = ".*"
		}

		if *dp == "." {
			fmt.Fprintln(os.Stderr, "error: invalid debug pattern")
			mnms.DoExit(1)
		}
		_, err := regexp.Compile(*dp)
		if err != nil {
			fmt.Fprintf(os.Stderr, "error: invalid regular expression, %v\n", err)
			mnms.DoExit(1)
		}
		q.P = *dp
		q.Q(q.O, q.P)
		ex, err := os.Executable()
		if err != nil {
			panic(err)
		}
		exPath := filepath.Dir(ex)
		q.Q(exPath)
		// enforce -M must be the first argument if present
		for i := 1; i < len(os.Args); i++ {
			if os.Args[i] == "-M" && i != 1 {
				fmt.Fprintln(os.Stderr, "error: -M must be the first argument")
				mnms.DoExit(1)
			}
		}
		if len(os.Args) > 2 && os.Args[1] == "-M" {
			q.P = ".*"
			q.Q("monitor run mode")
			t0 := time.Now().Unix()
			ix := 0
			for {
				ix++
				runarg := fmt.Sprintf("monitor: run #%d %v", ix, os.Args)
				q.Q("monitor: run", ix, os.Args)
				err = mnms.SendSyslog(mnms.LOG_NOTICE, "monitor", runarg)
				if err != nil {
					q.Q("error: syslog", err)
				}
				ec := exec.Command(ex, os.Args[2:]...)
				ec.Dir = exPath
				output, err := ec.CombinedOutput()
				t1 := time.Now().Unix()
				diff := t1 - t0
				q.Q("monitor:", string(output))
				if diff < 3 { // XXX
					q.Q("monitor: spinning, exit")
					mnms.DoExit(1)
				}
				t0 = t1
				if err != nil {
					q.Q("monitor:", err)
					errmsg := fmt.Sprintf("monitor: #%d %v",
						ix, err.Error())
					err = mnms.SendSyslog(mnms.LOG_ERR, "monitor", errmsg)
					if err != nil {
						q.Q("error: syslog", err)
					}
					continue
				}
			}
		}

		mnms.QC.AdminToken, err = mnms.GetToken("admin")
		if err != nil {
			q.Q(err)
			fmt.Fprintln(os.Stderr, "error: can't get admin token")
			mnms.DoExit(1)
		}

		if !*nosyslog {
			wg.Add(1)
			go func() {
				defer wg.Done()
				mnms.StartSyslogServer()
			}()
		} else {
			q.Q("skip running syslog server")
		}

		// validate wgname is valid
		if !mnms.WgTunnelNameIsValid(*wgname) && !*nowg {
			fmt.Fprintln(os.Stderr, "error: invalid wireguard vpn interface name")
			mnms.DoExit(1)
		}
		mnms.WgInit(*wgname, (!*nowg))

		if mnms.QC.Name == "" {
			fmt.Fprintln(os.Stderr, "error: -n name is required")
			mnms.DoExit(1)
		}
		q.Q(mnms.QC.Name)
		q.Q(mnms.QC.Domain, len(mnms.QC.Domain))

		if !*fake {
			mnms.CheckQCLicense()
			// init publickey
			// setup private key if provided
			if *prikeyfile != "" {
				keyBytes, err := os.ReadFile(*prikeyfile)
				if err == nil {
					// check keyBytes is a private key pem
					block, _ := pem.Decode(keyBytes)
					if block != nil {
						q.Q("use private key file", *prikeyfile)
						mnms.SetPrivateKey(string(keyBytes))
					}
				} else {
					q.Q(err)
					q.Q("use default private key")
				}
			} else {
				// did not provide private key file, use default private key
				fmt.Fprintf(os.Stderr, "warning: using default private key\n")
			}
			mnms.QC.OwnPublicKeys, err = mnms.GenerateOwnPublickey()
			if err != nil {
				q.Q(err)
				mnms.DoExit(1)
			}
			// init mnms config
			err := mnms.InitDefaultMNMSConfigIfNotExist()
			if err != nil {
				q.Q(err)
				mnms.DoExit(1)
			}

			// NOTE: Custom topology trees are already loaded in qc.go init()
			// Create default custom topology tree if none exist
			if len(mnms.QC.CustomTopologyMgr.ListTrees()) == 0 {
				err = mnms.QC.CustomTopologyMgr.CreateTree("default", "Default custom topology tree")
				if err != nil {
					q.Q("Warning: failed to create default custom topology tree:", err)
				}
			}
			wg.Add(1)
			go func() {
				defer wg.Done()
				mnms.CheckNetworkServicesAlive(60)
			}()
		}

		if !*nohttp {
			wg.Add(1)
			go func() {
				defer wg.Done()
				mnms.HTTPMain()
			}()
		}

		if !*nosshsrv {
			wg.Add(1)
			go func() {
				defer wg.Done()
				err = mnms.RunSSHServer()
				if err != nil {
					fmt.Fprintln(os.Stderr, err)
				}
			}()
		}

		// automatically check service version
		wg.Add(1)
		go func() {
			defer wg.Done()
			err = mnms.RunAutomaticallyCheckServiceVersion()
			if err != nil {
				q.Q(err)
			}
		}()

		wg.Add(1)
		go func() {
			defer wg.Done()
			mnms.RunVerifyCommands()
		}()

		time.Sleep(1 * time.Second)

		wg.Wait()
		q.Q("exit normally")
		mnms.DoExit(0)
	}

	s.RegisterRunEvent(service)
	s.RegisterStopEvent(stop)
	mnms.QC.ConfigFile = *configFile
	err = s.RunMode(daemon)
	if err != nil {
		fmt.Fprintln(os.Stderr, err)
		mnms.DoExit(1)
	}
}
