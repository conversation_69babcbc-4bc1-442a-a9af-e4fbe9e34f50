package mnms

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/influxdata/go-syslog/v3"
	"github.com/qeof/q"
)

// SyslogEntry represents a parsed syslog entry
type SyslogEntry struct {
	Timestamp time.Time `json:"timestamp"`
	Hostname  string    `json:"hostname"`
	Facility  int       `json:"facility"`
	Severity  int       `json:"severity"`
	Priority  int       `json:"priority"`
	Tag       string    `json:"tag"`
	Message   string    `json:"message"`
	RawLine   string    `json:"raw_line"`
}

type AlertConfig struct {
	MinSeverity        int      `json:"min_severity"`
	MaxSeverity        int      `json:"max_severity"`
	RateLimitSeconds   int      `json:"rate_limit_seconds"`
	MaxAlertsPerMinute int      `json:"max_alerts_per_minute"`
	Keywords           []string `json:"keywords"`
	ExcludeKeywords    []string `json:"exclude_keywords"`
}

// WhatsAppConfig defines WhatsApp configuration via Twilio
type WhatsAppConfig struct {
	Enabled     bool        `json:"enabled"`
	AccountSID  string      `json:"account_sid"`
	AuthToken   string      `json:"auth_token"`
	FromNumber  string      `json:"from_number"`
	ToNumbers   []string    `json:"to_numbers"`
	AlertConfig AlertConfig `json:"alert_config"`
}

// TelegramConfig defines Telegram configuration
type TelegramConfig struct {
	Enabled     bool        `json:"enabled"`
	BotToken    string      `json:"bot_token"`
	ChatIDs     []string    `json:"chat_ids"`
	AlertConfig AlertConfig `json:"alert_config"`
}

// MQTTConfig defines MQTT configuration
type MQTTConfig struct {
	Enabled     bool        `json:"enabled"`
	BrokerHost  string      `json:"broker_host"`
	BrokerPort  int         `json:"broker_port"`
	Username    string      `json:"username"`
	Password    string      `json:"password"`
	Topic       string      `json:"topic"`
	QoS         byte        `json:"qos"`
	Retain      bool        `json:"retain"`
	AlertConfig AlertConfig `json:"alert_config"`
}

// ForwardConfig represents the main configuration structure
type ForwardConfig struct {
	ListenSyslogAddr string         `json:"listen_syslog_addr,omitempty"` // addr to listen for syslog messages, copied from QC.SyslogServerAddr
	WhatsApp         WhatsAppConfig `json:"whatsapp"`
	Telegram         TelegramConfig `json:"telegram"`
	MQTT             MQTTConfig     `json:"mqtt"`
}

// RateLimiter handles rate limiting for alerts
type RateLimiter struct {
	mu            sync.RWMutex
	messageCount  map[string]int
	platformCount map[string]int // Per-platform global count
	lastReset     time.Time
	lastMessage   map[string]time.Time
}

type ProcessResult struct {
	WhatsAppResult string `json:"whatsapp_result"`
	TelegramResult string `json:"telegram_result"`
	MQTTResult     string `json:"mqtt_result"`
	RawLine        string `json:"raw_line"`
}

// NewRateLimiter creates a new rate limiter
func NewRateLimiter() *RateLimiter {
	return &RateLimiter{
		messageCount:  make(map[string]int),
		platformCount: make(map[string]int),
		lastReset:     time.Now(),
		lastMessage:   make(map[string]time.Time),
	}
}

// ShouldAllow checks if a message should be allowed based on rate limits
func (rl *RateLimiter) ShouldAllow(message string, rateLimitSeconds int, maxAlertsPerMinute int, platform string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	now := time.Now()

	// Reset counters every minute
	if now.Sub(rl.lastReset) >= time.Minute {
		rl.messageCount = make(map[string]int)
		rl.platformCount = make(map[string]int)
		rl.lastReset = now
	}

	// Check per-message rate limit
	messageKey := fmt.Sprintf("%s:%s", platform, message)
	if lastTime, exists := rl.lastMessage[messageKey]; exists {
		if now.Sub(lastTime) < time.Duration(rateLimitSeconds)*time.Second {
			return false
		}
	}

	// Check per-minute rate limit (per-platform)
	if rl.platformCount[platform] >= maxAlertsPerMinute {
		return false
	}

	// Allow the message
	rl.messageCount[messageKey]++
	rl.platformCount[platform]++
	rl.lastMessage[messageKey] = now
	return true
}

// AlertForwarder handles forwarding alerts to multiple platforms
type AlertForwarder struct {
	config      *ForwardConfig
	rateLimiter *RateLimiter
	mqttClient  mqtt.Client
	mqttInitErr error // Error from MQTT client initialization
	httpClient  *http.Client
	wg          sync.WaitGroup
	ctx         context.Context
	cancel      context.CancelFunc
}

func init() {
	QC.ForwardSvcData = make(map[string]ForwardConfig)
}

func ForwardInit(configFile string) {
	config, err := LoadForwardConfig(configFile)
	if err != nil {
		config = DefaultForwardConfig()
		if !os.IsNotExist(err) || !errors.Is(err, os.ErrNotExist) {
			q.Q("Using default forward config due to error:", err)
		} else {
			q.Q("Using default forward config")
		}
	}

	forwarder, err := NewAlertForwarder(config)
	if err != nil {
		q.Q(err)
		fmt.Fprintln(os.Stderr, "error: failed to create alert forwarder:", err)
		return
	}

	QC.ForwardMutex.Lock()
	QC.ForwardSvcData[QC.Name] = *config
	QC.AlertForwarder = forwarder
	QC.ForwardMutex.Unlock()
}

// NewAlertForwarder creates a new alert forwarder
func NewAlertForwarder(config *ForwardConfig) (*AlertForwarder, error) {
	q.Q("Initializing Alert Forwarder with config:", config)
	ctx, cancel := context.WithCancel(context.Background())
	af := &AlertForwarder{
		config:      config,
		rateLimiter: NewRateLimiter(),
		httpClient:  &http.Client{Timeout: 30 * time.Second},
		ctx:         ctx,
		cancel:      cancel,
		mqttClient:  nil,
		mqttInitErr: nil,
	}

	// Initialize MQTT client if enabled
	if config.MQTT.Enabled {
		if err := af.initMQTTClient(); err != nil {
			q.Q("Failed to initialize MQTT client:", err)
			af.mqttClient = nil
			af.mqttInitErr = err // Store the initialization error
		}
	}
	return af, nil
}

func DefaultForwardConfig() *ForwardConfig {
	return &ForwardConfig{
		WhatsApp: WhatsAppConfig{
			Enabled: false,
			AlertConfig: AlertConfig{
				MinSeverity:        0,
				MaxSeverity:        7,
				RateLimitSeconds:   300,
				MaxAlertsPerMinute: 5,
			},
		},
		Telegram: TelegramConfig{
			Enabled: false,
			AlertConfig: AlertConfig{
				MinSeverity:        0,
				MaxSeverity:        7,
				RateLimitSeconds:   180,
				MaxAlertsPerMinute: 10,
			},
		},
		MQTT: MQTTConfig{
			Enabled:    false,
			BrokerHost: "localhost",
			BrokerPort: 1883,
			Topic:      "mnms/alerts",
			QoS:        1,
			Retain:     false,
			AlertConfig: AlertConfig{
				MinSeverity:        0,
				MaxSeverity:        7,
				RateLimitSeconds:   60,
				MaxAlertsPerMinute: 20,
			},
		},
	}
}

// Syslog format regexes for non-standard formats not handled by go-syslog library
var (
	// Pseudo-RFC5424: <priority>ISO-timestamp hostname appname: message (missing version number)
	pseudoRFC5424Regex = regexp.MustCompile(`^<(\d+)>(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z?)\s+(\S+)\s+([^:]+):\s*(.*)$`)

	// Cisco format: <priority>seq: *timestamp: message
	ciscoRegex = regexp.MustCompile(`^<(\d+)>(\d+):\s*\*(\w+\s+\d+\s+\d+:\d+:\d+\.\d+\s+\w+):\s*(.*)$`)

	// Generic fallback: <priority>everything_else
	genericRegex = regexp.MustCompile(`^<(\d+)>(.*)$`)
) // Stop stops the alert forwarder
func (af *AlertForwarder) Stop() {
	// SendSyslog(LOG_INFO, "Forward", "Stopping alert forwarder...")
	q.Q("Stopping alert forwarder...")
	af.cancel()

	if af.mqttClient != nil {
		// Disconnect MQTT client if connected
		if af.mqttClient.IsConnected() {
			af.mqttClient.Disconnect(250)
		}
	}

	af.wg.Wait()
	// SendSyslog(LOG_INFO, "Forward", "Alert forwarder stopped")
	q.Q("Alert forwarder stopped")
}

// parseSyslogLine parses a syslog line using the existing parsingDataofSyslog function
// with fallback for non-standard formats
func (af *AlertForwarder) parseSyslogLine(line string, clientAddr string) *SyslogEntry {
	line = strings.TrimSpace(line)
	if line == "" {
		return nil
	}

	// First try using the existing parsingDataofSyslog function (handles RFC3164 and proper RFC5424)
	base, timestamp, err := parsingDataofSyslog(line)
	if err == nil && base.Valid() {
		return af.convertBaseToSyslogEntry(base, timestamp, line)
	}

	// Try pseudo-RFC5424 format (ISO timestamp without version number)
	if entry := af.parsePseudoRFC5424Format(line); entry != nil {
		return entry
	}

	// If that fails, try Cisco format parsing
	if entry := af.parseCiscoFormat(line, clientAddr); entry != nil {
		return entry
	}

	// If all parsing fails, try generic priority extraction
	if entry := af.parseGenericFormat(line); entry != nil {
		return entry
	}

	// If all parsing methods fail, log and return nil
	q.Q("failed to parse syslog line with RFC3164, RFC5424, pseudo-RFC5424, Cisco, and generic formats:", line)
	SendSyslog(LOG_WARNING, "bbfwdsvc", fmt.Sprintf("Failed to parse syslog line: %s", line))
	return nil
}

// convertBaseToSyslogEntry converts syslog.Base to SyslogEntry
func (af *AlertForwarder) convertBaseToSyslogEntry(base syslog.Base, timestamp time.Time, rawLine string) *SyslogEntry {
	entry := &SyslogEntry{
		RawLine:   rawLine,
		Timestamp: timestamp,
	}

	if base.Priority != nil {
		entry.Priority = int(*base.Priority)
		entry.Facility = int(*base.Priority) / 8
		entry.Severity = int(*base.Priority) % 8
	}

	if base.Hostname != nil {
		entry.Hostname = *base.Hostname
	}

	if base.Appname != nil {
		entry.Tag = *base.Appname
	} else {
		// For RFC3164, extract tag from the message if possible
		entry.Tag = af.extractTagFromMessage(rawLine)
	}

	if base.Message != nil {
		entry.Message = *base.Message
	}

	return entry
}

// extractTagFromMessage tries to extract tag from raw syslog line for RFC3164 format
func (af *AlertForwarder) extractTagFromMessage(rawLine string) string {
	// Simple regex to extract tag from RFC3164 format: <priority>timestamp hostname tag: message
	rfc3164TagRegex := regexp.MustCompile(`^<\d+>\w+\s+\d+\s+\d+:\d+:\d+\s+\S+\s+([^:]+):`)
	matches := rfc3164TagRegex.FindStringSubmatch(rawLine)
	if len(matches) >= 2 {
		return strings.TrimSpace(matches[1])
	}
	return "unknown"
}

// parsePseudoRFC5424Format handles pseudo-RFC5424 format: <priority>ISO-timestamp hostname appname: message
// This format looks like RFC5424 but is missing the version number
func (af *AlertForwarder) parsePseudoRFC5424Format(line string) *SyslogEntry {
	matches := pseudoRFC5424Regex.FindStringSubmatch(line)
	if len(matches) != 6 {
		return nil
	}

	priority, err := strconv.Atoi(matches[1])
	if err != nil {
		return nil
	}

	// Parse ISO 8601 timestamp
	timestamp, err := time.Parse("2006-01-02T15:04:05.000Z", matches[2])
	if err != nil {
		// Try without milliseconds
		timestamp, err = time.Parse("2006-01-02T15:04:05Z", matches[2])
		if err != nil {
			// Try without Z suffix
			timestamp, err = time.Parse("2006-01-02T15:04:05", matches[2])
			if err != nil {
				timestamp = time.Now()
			}
		}
	}

	return &SyslogEntry{
		Facility:  priority / 8,
		Severity:  priority % 8,
		Priority:  priority,
		Timestamp: timestamp,
		Hostname:  matches[3],
		Tag:       matches[4],
		Message:   matches[5],
		RawLine:   line,
	}
}

// parseCiscoFormat handles Cisco-specific syslog format
// Format: <priority>seq: *timestamp: message
func (af *AlertForwarder) parseCiscoFormat(line string, clientAddr string) *SyslogEntry {
	matches := ciscoRegex.FindStringSubmatch(line)
	if len(matches) != 5 {
		return nil
	}

	priority, err := strconv.Atoi(matches[1])
	if err != nil {
		return nil
	}

	// Parse Cisco timestamp format: "Mar  1 08:13:42.296 UTC"
	timestampStr := strings.TrimSpace(matches[3])
	timestamp := af.parseCiscoTimestamp(timestampStr)

	message := matches[4]

	// Extract tag from Cisco message (e.g., "%LINK-3-UPDOWN" from the message)
	tag := "cisco"
	if strings.HasPrefix(message, "%") {
		if colonIndex := strings.Index(message, ":"); colonIndex > 0 {
			tag = message[1:colonIndex] // Extract tag without % and before :
		}
	}

	// Use actual source IP as hostname, fallback to "cisco-device" if not available
	hostname := "cisco-device"
	if clientAddr != "" {
		hostname = clientAddr
	}

	return &SyslogEntry{
		Facility:  priority / 8,
		Severity:  priority % 8,
		Priority:  priority,
		Timestamp: timestamp,
		Hostname:  hostname,
		Tag:       tag,
		Message:   message,
		RawLine:   line,
	}
}

// parseCiscoTimestamp parses Cisco timestamp format
func (af *AlertForwarder) parseCiscoTimestamp(timestampStr string) time.Time {
	// Cisco format: "Mar  1 08:13:42.296 UTC"
	// Try different Cisco timestamp formats
	formats := []string{
		"Jan  2 15:04:05.000 MST",
		"Jan  2 15:04:05.000 UTC",
		"Jan  2 15:04:05 MST",
		"Jan  2 15:04:05 UTC",
		"Jan 2 15:04:05.000 MST",
		"Jan 2 15:04:05.000 UTC",
		"Jan 2 15:04:05 MST",
		"Jan 2 15:04:05 UTC",
	}

	currentYear := time.Now().Year()

	for _, format := range formats {
		if t, err := time.Parse(format, timestampStr); err == nil {
			// Add current year since Cisco logs don't include year
			return time.Date(currentYear, t.Month(), t.Day(), t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), t.Location())
		}
	}

	// If parsing fails, return current time
	return time.Now()
}

// parseGenericFormat handles any format that at least has <priority> at the beginning
func (af *AlertForwarder) parseGenericFormat(line string) *SyslogEntry {
	matches := genericRegex.FindStringSubmatch(line)
	if len(matches) != 3 {
		return nil
	}

	priority, err := strconv.Atoi(matches[1])
	if err != nil {
		return nil
	}

	return &SyslogEntry{
		Facility:  priority / 8,
		Severity:  priority % 8,
		Priority:  priority,
		Timestamp: time.Now(),
		Hostname:  "unknown",
		Tag:       "generic",
		Message:   matches[2],
		RawLine:   line,
	}
}

// processLogEntry processes a parsed log entry
func (af *AlertForwarder) processLogEntry(entry *SyslogEntry) ProcessResult {
	// SendSyslog(LOG_DEBUG, "Forward", fmt.Sprintf("Processing log entry: %s", entry.RawLine))
	q.Q("Processing log entry:", entry.RawLine)
	result := ProcessResult{
		RawLine: entry.RawLine,
	}

	// WhatsApp
	if af.config.WhatsApp.Enabled {
		shouldForward, reason := af.shouldForwardAlert(entry, &af.config.WhatsApp.AlertConfig, "whatsapp")
		if shouldForward {
			if err := af.forwardToWhatsApp(entry); err != nil {
				result.WhatsAppResult = fmt.Sprintf("WhatsApp alert FAILED: %v", err)
			} else {
				result.WhatsAppResult = "WhatsApp alert sent successfully."
			}
		} else {
			result.WhatsAppResult = fmt.Sprintf("WhatsApp alert not sent: %s", reason)
		}
	} else {
		result.WhatsAppResult = "WhatsApp alert disabled."
	}

	// Telegram
	if af.config.Telegram.Enabled {
		shouldForward, reason := af.shouldForwardAlert(entry, &af.config.Telegram.AlertConfig, "telegram")
		if shouldForward {
			if err := af.forwardToTelegram(entry); err != nil {
				result.TelegramResult = fmt.Sprintf("Telegram alert FAILED: %v", err)
			} else {
				result.TelegramResult = "Telegram alert sent successfully."
			}
		} else {
			result.TelegramResult = fmt.Sprintf("Telegram alert not sent: %s", reason)
		}
	} else {
		result.TelegramResult = "Telegram alert disabled."
	}

	// MQTT with initialization check
	if af.config.MQTT.Enabled {
		if af.mqttInitErr != nil {
			result.MQTTResult = fmt.Sprintf("MQTT alert FAILED: Initialization error - %v", af.mqttInitErr)
		} else {
			shouldForward, reason := af.shouldForwardAlert(entry, &af.config.MQTT.AlertConfig, "mqtt")
			if shouldForward {
				if err := af.forwardToMQTT(entry); err != nil {
					result.MQTTResult = fmt.Sprintf("MQTT alert FAILED: %v", err)
				} else {
					result.MQTTResult = "MQTT alert sent successfully."
				}
			} else {
				result.MQTTResult = fmt.Sprintf("MQTT alert not sent: %s", reason)
			}
		}
	} else {
		result.MQTTResult = "MQTT alert disabled."
	}

	return result
}

// shouldForwardAlert checks if an alert should be forwarded based on configuration
func (af *AlertForwarder) shouldForwardAlert(entry *SyslogEntry, config *AlertConfig, platform string) (bool, string) {
	// Check severity range
	if entry.Severity < config.MinSeverity || entry.Severity > config.MaxSeverity {
		return false, "Alert severity out of range."
	}

	// Check keywords
	if len(config.Keywords) > 0 {
		found := false
		message := strings.ToLower(entry.Message)
		for _, keyword := range config.Keywords {
			if strings.Contains(message, strings.ToLower(keyword)) {
				found = true
				break
			}
		}
		if !found {
			return false, "Alert message does not contain required keywords."
		}
	}

	// Check exclude keywords
	if len(config.ExcludeKeywords) > 0 {
		message := strings.ToLower(entry.Message)
		for _, keyword := range config.ExcludeKeywords {
			if strings.Contains(message, strings.ToLower(keyword)) {
				return false, "Alert message contains excluded keywords."
			}
		}
	}

	// Check rate limiting with platform-specific rate limiting
	messageKey := fmt.Sprintf("%s:%s", entry.Tag, entry.Message)
	if !af.rateLimiter.ShouldAllow(messageKey, config.RateLimitSeconds, config.MaxAlertsPerMinute, platform) {
		// SendSyslog(LOG_DEBUG, "Forward", fmt.Sprintf("Rate limited for platform %s: %s", platform, messageKey))
		q.Q("Rate limited for platform", platform, messageKey)
		return false, "Rate limit exceeded for platform " + platform
	}

	return true, "Alert should be forwarded."
}

// initMQTTClient initializes the MQTT client
func (af *AlertForwarder) initMQTTClient() error {
	opts := mqtt.NewClientOptions()
	broker := fmt.Sprintf("tcp://%s:%d", af.config.MQTT.BrokerHost, af.config.MQTT.BrokerPort)
	opts.AddBroker(broker)
	opts.SetClientID("mnms-syslog-forwarder")
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(10 * time.Second)
	opts.SetConnectTimeout(10 * time.Second)
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(10 * time.Second)

	if af.config.MQTT.Username != "" {
		opts.SetUsername(af.config.MQTT.Username)
		opts.SetPassword(af.config.MQTT.Password)
	}

	opts.SetTLSConfig(&tls.Config{InsecureSkipVerify: true})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		// SendSyslog(LOG_ERR, "bbfwdsvc", fmt.Sprintf("MQTT connection lost: %v", err))
		q.Q("MQTT connection lost:", err)
	})

	opts.SetOnConnectHandler(func(client mqtt.Client) {
		// SendSyslog(LOG_INFO, "bbfwdsvc", "MQTT client connected")
		q.Q("MQTT client connected")
	})

	af.mqttClient = mqtt.NewClient(opts)
	if token := af.mqttClient.Connect(); token.Wait() && token.Error() != nil {
		return fmt.Errorf("mqtt connection failed: %w", token.Error())
	}

	return nil
}

// forwardToWhatsApp forwards alert to WhatsApp via Twilio
func (af *AlertForwarder) forwardToWhatsApp(entry *SyslogEntry) error {
	message := af.formatWhatsAppMessage(entry)

	// Validate configuration first
	if af.config.WhatsApp.AccountSID == "" {
		return fmt.Errorf("account SID not configured")
	}
	if af.config.WhatsApp.AuthToken == "" {
		return fmt.Errorf("auth token not configured")
	}
	if af.config.WhatsApp.FromNumber == "" {
		return fmt.Errorf("from number not configured")
	}
	if len(af.config.WhatsApp.ToNumbers) == 0 {
		return fmt.Errorf("no to numbers configured")
	}

	var lastError error
	successCount := 0

	for _, toNumber := range af.config.WhatsApp.ToNumbers {
		if err := af.sendWhatsAppMessage(toNumber, message); err != nil {
			// SendSyslog(LOG_ERR, "Forward", fmt.Sprintf("Failed to send WhatsApp message to %s: %v", toNumber, err))
			lastError = err
		} else {
			// SendSyslog(LOG_INFO, "Forward", fmt.Sprintf("WhatsApp alert sent to %s", toNumber))
			successCount++
		}
	}

	if successCount == 0 {
		return fmt.Errorf("failed to send to all %d recipients, last error: %v", len(af.config.WhatsApp.ToNumbers), lastError)
	} else if successCount < len(af.config.WhatsApp.ToNumbers) {
		return fmt.Errorf("sent to %d/%d recipients, some failed", successCount, len(af.config.WhatsApp.ToNumbers))
	}

	return nil
}

// formatWhatsAppMessage formats a message for WhatsApp
func (af *AlertForwarder) formatWhatsAppMessage(entry *SyslogEntry) string {
	emoji := severityEmojis[entry.Severity]
	severityName := severityNames[entry.Severity]

	return fmt.Sprintf("%s MNMS Alert %s\nTime: %s\nHost: %s\nSeverity: %s (%d)\nTag: %s\nMessage: %s",
		emoji, emoji,
		entry.Timestamp.Format("2006-01-02 15:04:05"),
		entry.Hostname,
		severityName, entry.Severity,
		entry.Tag,
		entry.Message)
}

// sendWhatsAppMessage sends a WhatsApp message via Twilio
func (af *AlertForwarder) sendWhatsAppMessage(to, message string) error {
	twilioURL := fmt.Sprintf("https://api.twilio.com/2010-04-01/Accounts/%s/Messages.json", af.config.WhatsApp.AccountSID)

	data := url.Values{}
	data.Set("From", "whatsapp:"+af.config.WhatsApp.FromNumber)
	data.Set("To", "whatsapp:"+to)
	data.Set("Body", message)

	req, err := http.NewRequest("POST", twilioURL, strings.NewReader(data.Encode()))
	if err != nil {
		return err
	}

	req.SetBasicAuth(af.config.WhatsApp.AccountSID, af.config.WhatsApp.AuthToken)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := af.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 201 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("twilio API error: %s - %s", resp.Status, string(body))
	}

	return nil
}

// forwardToTelegram forwards alert to Telegram
func (af *AlertForwarder) forwardToTelegram(entry *SyslogEntry) error {
	message := af.formatTelegramMessage(entry)

	// Validate configuration first
	if af.config.Telegram.BotToken == "" {
		return fmt.Errorf("bot token not configured")
	}
	if len(af.config.Telegram.ChatIDs) == 0 {
		return fmt.Errorf("no chat IDs configured")
	}

	var lastError error
	successCount := 0

	for _, chatID := range af.config.Telegram.ChatIDs {
		if err := af.sendTelegramMessage(chatID, message); err != nil {
			// SendSyslog(LOG_ERR, "Forward", fmt.Sprintf("Failed to send Telegram message to %s: %v", chatID, err))
			lastError = err
		} else {
			// SendSyslog(LOG_INFO, "Forward", fmt.Sprintf("Telegram alert sent to %s", chatID))
			successCount++
		}
	}

	if successCount == 0 {
		return fmt.Errorf("failed to send to all %d chat IDs, last error: %v", len(af.config.Telegram.ChatIDs), lastError)
	} else if successCount < len(af.config.Telegram.ChatIDs) {
		return fmt.Errorf("sent to %d/%d chat IDs, some failed", successCount, len(af.config.Telegram.ChatIDs))
	}

	return nil
}

// formatTelegramMessage formats a message for Telegram
func (af *AlertForwarder) formatTelegramMessage(entry *SyslogEntry) string {
	emoji := severityEmojis[entry.Severity]
	severityName := severityNames[entry.Severity]

	return fmt.Sprintf("%s *MNMS Alert* %s\n*Time:* %s\n*Host:* `%s`\n*Severity:* %s %s\n*Tag:* `%s`\n*Message:* %s",
		emoji, emoji,
		entry.Timestamp.Format("2006-01-02 15:04:05"),
		entry.Hostname,
		emoji, severityName,
		entry.Tag,
		entry.Message)
}

// sendTelegramMessage sends a Telegram message
func (af *AlertForwarder) sendTelegramMessage(chatID, message string) error {
	telegramURL := fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", af.config.Telegram.BotToken)

	payload := map[string]interface{}{
		"chat_id":    chatID,
		"text":       message,
		"parse_mode": "Markdown",
	}

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	resp, err := af.httpClient.Post(telegramURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("telegram API error: %s - %s", resp.Status, string(body))
	}

	return nil
}

// forwardToMQTT forwards alert to MQTT
func (af *AlertForwarder) forwardToMQTT(entry *SyslogEntry) error {
	// Check initialization error first
	if af.mqttInitErr != nil {
		return fmt.Errorf("mqtt client initialization failed: %v", af.mqttInitErr)
	}

	// Validate configuration
	if af.config.MQTT.BrokerHost == "" {
		return fmt.Errorf("broker host not configured")
	}
	if af.config.MQTT.Topic == "" {
		return fmt.Errorf("topic not configured")
	}

	if af.mqttClient == nil {
		return fmt.Errorf("mqtt client not initialized")
	}

	if !af.mqttClient.IsConnected() {
		return fmt.Errorf("mqtt client not connected to broker %s:%d", af.config.MQTT.BrokerHost, af.config.MQTT.BrokerPort)
	}

	jsonData, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %v", err)
	}

	token := af.mqttClient.Publish(af.config.MQTT.Topic, af.config.MQTT.QoS, af.config.MQTT.Retain, jsonData)
	if !token.WaitTimeout(5 * time.Second) {
		return fmt.Errorf("publish timeout to topic %s", af.config.MQTT.Topic)
	}

	if token.Error() != nil {
		return fmt.Errorf("publish failed to topic %s: %v", af.config.MQTT.Topic, token.Error())
	}

	// SendSyslog(LOG_INFO, "Forward", fmt.Sprintf("MQTT alert published to topic: %s", af.config.MQTT.Topic))
	return nil
}

func ForwardCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd, err := ExpandCommandKVValue(cmdinfo.Command)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}
	// cmd := cmdinfo.Command
	if QC.Kind != "forward" {
		cmdinfo.Status = "error: this command can only be run on forwarder service"
		return cmdinfo
	}

	// configure ForwardConfig by url
	if strings.HasPrefix(cmd, "forward import") {
		return ForwardImportCmd(cmdinfo)
	}

	// by flags, support each config separately
	if strings.HasPrefix(cmd, "forward config") {
		return ForwardConfigCmd(cmdinfo)
	}

	// by raw json
	if strings.HasPrefix(cmd, "forward raw-json config") {
		return ForwardRawJsonConfigCmd(cmdinfo)
	}

	// upload to root immediately
	if strings.HasPrefix(cmd, "forward upload") {
		return ForwardUploadConfigCmd(cmdinfo)
	}

	// send a message directly to platforms with stored config
	// user can easily send a message to platforms without listening to syslog
	if strings.HasPrefix(cmd, "forward send") {
		return ForwardSendCmd(cmdinfo)
	}

	// send a custom message to platforms with custom config
	// user can send a custom message to platforms while editing config
	if strings.HasPrefix(cmd, "forward custom send") {
		return ForwardCustomSendCmd(cmdinfo)
	}

	cmdinfo.Status = "error: unknown forward command"
	return cmdinfo
}

/*
ForwardImportCmd handles the "forward import" command
Usage: forward import [url]

	[url]: fetches a forward config from a URL or file and applies it. Make sure forward service can access the URL.

Example:

	forward import https://example.com/forward_config.json
	forward import file:///path/to/local/forward_config.json
	forward import file://C:/path/to/local/forward_config.json
*/
func ForwardImportCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 3 || ws[1] != "import" {
		cmdinfo.Status = "error: usage: forward import [url]"
		return cmdinfo
	}

	url := ws[2]
	config, err := GetForwardConfig(url)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to read forward config from %s: %v", url, err)
		return cmdinfo
	}
	QC.ForwardMutex.Lock()
	defer QC.ForwardMutex.Unlock()

	if err := ApplyForwardConfig(config); err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to apply new forward config: %v", err)
		return cmdinfo
	}

	QC.ForwardSvcData[QC.Name] = *config

	cmdinfo.Status = "ok"
	return cmdinfo
}

func GetForwardConfig(fileUrl string) (*ForwardConfig, error) {
	if fileUrl == "" {
		return nil, fmt.Errorf("no URL provided for forward config")
	}

	if strings.HasPrefix(fileUrl, "http://") || strings.HasPrefix(fileUrl, "https://") {
		resp, err := GetWithToken(fileUrl, QC.AdminToken)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch forward config: %w", err)
		}
		defer resp.Body.Close()
		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("failed to fetch forward config, status code: %d", resp.StatusCode)
		}
		var config ForwardConfig
		if err := json.NewDecoder(resp.Body).Decode(&config); err != nil {
			return nil, fmt.Errorf("failed to decode forward config: %w", err)
		}
		return &config, nil
	}

	// Handle file:// URLs
	if strings.HasPrefix(fileUrl, "file://") {
		parsed, err := url.Parse(fileUrl)
		if err != nil {
			return nil, fmt.Errorf("failed to parse file URL: %w", err)
		}
		localPath := parsed.Path
		// On Windows, remove leading slash if present (e.g. /C:/path)
		if runtime.GOOS == "windows" && strings.HasPrefix(localPath, "/") && len(localPath) > 2 && localPath[2] == ':' {
			localPath = localPath[1:]
		}
		data, err := os.ReadFile(localPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read forward config file: %w", err)
		}
		var config ForwardConfig
		if err := json.Unmarshal(data, &config); err != nil {
			return nil, fmt.Errorf("failed to parse forward config file: %w", err)
		}
		return &config, nil
	}

	// If it's a local file path, read the file directly
	data, err := os.ReadFile(fileUrl)
	if err != nil {
		return nil, fmt.Errorf("failed to read forward config file: %w", err)
	}
	var config ForwardConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse forward config file: %w", err)
	}
	return &config, nil
}

func LoadForwardConfig(filename string) (*ForwardConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config ForwardConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	return &config, nil
}

func ApplyForwardConfig(config *ForwardConfig) error {
	if QC.AlertForwarder == nil {
		return fmt.Errorf("alert forwarder is not initialized")
	}

	// Create new forwarder first - don't stop old one until we know new one works
	newForwarder, err := NewAlertForwarder(config)
	if err != nil {
		q.Q("Failed to create new alert forwarder:", err)
		// Keep the old forwarder running since new one failed
		return fmt.Errorf("failed to create new alert forwarder: %w", err)
	}

	// Only stop old forwarder after new one is successfully created
	oldForwarder := QC.AlertForwarder
	QC.AlertForwarder = newForwarder

	// Stop the old forwarder after successful replacement
	oldForwarder.Stop()

	return nil
}

/*
ForwardConfigCmd handles the "forward config" command with flags.
Usage: forward config [flag1=value1 flag2=value2 ...]
Flags:
- whatsapp=true|false
- whatsapp_account_sid=your_account_sid
- whatsapp_auth_token=your_auth_token
- whatsapp_from_number=your_whatsapp_from_number
- whatsapp_to_numbers=comma,separated,phone,numbers
- whatsapp_min_severity=0-7 (default: 0)
- whatsapp_max_severity=0-7 (default: 7)
- whatsapp_rate_limit_seconds=seconds (default: 300)
- whatsapp_max_alerts_per_minute=number (default: 5)
- whatsapp_keywords=comma,separated,keywords (whitelist)
- whatsapp_exclude_keywords=comma,separated,keywords (blacklist)
- telegram=true|false
- telegram_bot_token=your_bot_token
- telegram_chat_ids=comma,separated,chat_ids
- telegram_min_severity=0-7 (default: 0)
- telegram_max_severity=0-7 (default: 7)
- telegram_rate_limit_seconds=seconds (default: 180)
- telegram_max_alerts_per_minute=number (default: 10)
- telegram_keywords=comma,separated,keywords (whitelist)
- telegram_exclude_keywords=comma,separated,keywords (blacklist)
- mqtt=true|false
- mqtt_broker_host=your_mqtt_broker_host (default: localhost)
- mqtt_broker_port=your_mqtt_broker_port (default: 1883)
- mqtt_username=your_mqtt_username
- mqtt_password=your_mqtt_password
- mqtt_topic=your_mqtt_topic (default: mnms/alerts)
- mqtt_qos=0|1|2 (default: 1)
- mqtt_retain=true|false (default: false)
- mqtt_min_severity=0-7 (default: 0)
- mqtt_max_severity=0-7 (default: 7)
- mqtt_rate_limit_seconds=seconds (default: 60)
- mqtt_max_alerts_per_minute=number (default: 20)
- mqtt_keywords=comma,separated,keywords (whitelist)
- mqtt_exclude_keywords=comma,separated,keywords (blacklist)
Example:

	forward config -whatsapp=true -whatsapp_to_numbers=+**********,+1987654321 -whatsapp_keywords=error,critical -telegram=true -telegram_chat_ids=12345,67890 -mqtt=true -mqtt_broker_host=broker.example.com -mqtt_topic=alerts

This will enable WhatsApp, Telegram, and MQTT forwarding, set WhatsApp to send to two numbers and only forward messages containing "error" or "critical", set Telegram to two chat IDs, and configure MQTT with a custom broker and topic.
*/
func ForwardConfigCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 3 || ws[1] != "config" {
		cmdinfo.Status = "error: usage: forward config [-flag1=value1 -flag2=value2 ...]"
		return cmdinfo
	}

	QC.ForwardMutex.Lock()
	defer QC.ForwardMutex.Unlock()

	config, ok := QC.ForwardSvcData[QC.Name]
	if !ok {
		q.Q("No existing forwarder config found, using default")
		config = *DefaultForwardConfig()
	}

	f := NewFlag("forward config", flag.ContinueOnError)

	// WhatsAppConfig
	f.BoolVar(&config.WhatsApp.Enabled, "whatsapp", config.WhatsApp.Enabled, "Enable WhatsApp forwarding")
	f.StringVar(&config.WhatsApp.AccountSID, "whatsapp_account_sid", config.WhatsApp.AccountSID, "Twilio Account SID for WhatsApp")
	f.StringVar(&config.WhatsApp.AuthToken, "whatsapp_auth_token", config.WhatsApp.AuthToken, "Twilio Auth Token for WhatsApp")
	f.StringVar(&config.WhatsApp.FromNumber, "whatsapp_from_number", config.WhatsApp.FromNumber, "WhatsApp From Number")
	// ToNumbers as comma-separated string
	var whatsappToNumbers string
	f.StringVar(&whatsappToNumbers, "whatsapp_to_numbers", strings.Join(config.WhatsApp.ToNumbers, ","), "Comma-separated WhatsApp To Numbers")

	// WhatsApp AlertConfig
	f.IntVar(&config.WhatsApp.AlertConfig.MinSeverity, "whatsapp_min_severity", config.WhatsApp.AlertConfig.MinSeverity, "WhatsApp min severity")
	f.IntVar(&config.WhatsApp.AlertConfig.MaxSeverity, "whatsapp_max_severity", config.WhatsApp.AlertConfig.MaxSeverity, "WhatsApp max severity")
	f.IntVar(&config.WhatsApp.AlertConfig.RateLimitSeconds, "whatsapp_rate_limit_seconds", config.WhatsApp.AlertConfig.RateLimitSeconds, "WhatsApp rate limit seconds")
	f.IntVar(&config.WhatsApp.AlertConfig.MaxAlertsPerMinute, "whatsapp_max_alerts_per_minute", config.WhatsApp.AlertConfig.MaxAlertsPerMinute, "WhatsApp max alerts per minute")
	var whatsappKeywords string
	f.StringVar(&whatsappKeywords, "whatsapp_keywords", strings.Join(config.WhatsApp.AlertConfig.Keywords, ","), "Comma-separated WhatsApp keywords (whitelist)")
	var whatsappExcludeKeywords string
	f.StringVar(&whatsappExcludeKeywords, "whatsapp_exclude_keywords", strings.Join(config.WhatsApp.AlertConfig.ExcludeKeywords, ","), "Comma-separated WhatsApp exclude keywords (blacklist)")

	// TelegramConfig
	f.BoolVar(&config.Telegram.Enabled, "telegram", config.Telegram.Enabled, "Enable Telegram forwarding")
	f.StringVar(&config.Telegram.BotToken, "telegram_bot_token", config.Telegram.BotToken, "Telegram Bot Token")
	var telegramChatIDs string
	f.StringVar(&telegramChatIDs, "telegram_chat_ids", strings.Join(config.Telegram.ChatIDs, ","), "Comma-separated Telegram Chat IDs")

	// Telegram AlertConfig
	f.IntVar(&config.Telegram.AlertConfig.MinSeverity, "telegram_min_severity", config.Telegram.AlertConfig.MinSeverity, "Telegram min severity")
	f.IntVar(&config.Telegram.AlertConfig.MaxSeverity, "telegram_max_severity", config.Telegram.AlertConfig.MaxSeverity, "Telegram max severity")
	f.IntVar(&config.Telegram.AlertConfig.RateLimitSeconds, "telegram_rate_limit_seconds", config.Telegram.AlertConfig.RateLimitSeconds, "Telegram rate limit seconds")
	f.IntVar(&config.Telegram.AlertConfig.MaxAlertsPerMinute, "telegram_max_alerts_per_minute", config.Telegram.AlertConfig.MaxAlertsPerMinute, "Telegram max alerts per minute")
	var telegramKeywords string
	f.StringVar(&telegramKeywords, "telegram_keywords", strings.Join(config.Telegram.AlertConfig.Keywords, ","), "Comma-separated Telegram keywords (whitelist)")
	var telegramExcludeKeywords string
	f.StringVar(&telegramExcludeKeywords, "telegram_exclude_keywords", strings.Join(config.Telegram.AlertConfig.ExcludeKeywords, ","), "Comma-separated Telegram exclude keywords (blacklist)")

	// MQTTConfig
	f.BoolVar(&config.MQTT.Enabled, "mqtt", config.MQTT.Enabled, "Enable MQTT forwarding")
	f.StringVar(&config.MQTT.BrokerHost, "mqtt_broker_host", config.MQTT.BrokerHost, "MQTT Broker Host")
	f.IntVar(&config.MQTT.BrokerPort, "mqtt_broker_port", config.MQTT.BrokerPort, "MQTT Broker Port")
	f.StringVar(&config.MQTT.Username, "mqtt_username", config.MQTT.Username, "MQTT Username")
	f.StringVar(&config.MQTT.Password, "mqtt_password", config.MQTT.Password, "MQTT Password")
	f.StringVar(&config.MQTT.Topic, "mqtt_topic", config.MQTT.Topic, "MQTT Topic")
	var mqttQoS int = int(config.MQTT.QoS)
	f.IntVar(&mqttQoS, "mqtt_qos", mqttQoS, "MQTT QoS")
	f.BoolVar(&config.MQTT.Retain, "mqtt_retain", config.MQTT.Retain, "MQTT Retain")

	// MQTT AlertConfig
	f.IntVar(&config.MQTT.AlertConfig.MinSeverity, "mqtt_min_severity", config.MQTT.AlertConfig.MinSeverity, "MQTT min severity")
	f.IntVar(&config.MQTT.AlertConfig.MaxSeverity, "mqtt_max_severity", config.MQTT.AlertConfig.MaxSeverity, "MQTT max severity")
	f.IntVar(&config.MQTT.AlertConfig.RateLimitSeconds, "mqtt_rate_limit_seconds", config.MQTT.AlertConfig.RateLimitSeconds, "MQTT rate limit seconds")
	f.IntVar(&config.MQTT.AlertConfig.MaxAlertsPerMinute, "mqtt_max_alerts_per_minute", config.MQTT.AlertConfig.MaxAlertsPerMinute, "MQTT max alerts per minute")
	var mqttKeywords string
	f.StringVar(&mqttKeywords, "mqtt_keywords", strings.Join(config.MQTT.AlertConfig.Keywords, ","), "Comma-separated MQTT keywords (whitelist)")
	var mqttExcludeKeywords string
	f.StringVar(&mqttExcludeKeywords, "mqtt_exclude_keywords", strings.Join(config.MQTT.AlertConfig.ExcludeKeywords, ","), "Comma-separated MQTT exclude keywords (blacklist)")
	// Parse flags
	err := f.Parse(ws[2:])
	if err != nil {
		cmdinfo.Status = "error: " + err.Error()
		return cmdinfo
	}

	// Check for unparsed arguments (likely missing dashes)
	if len(f.Args()) > 0 {
		unparsedArgs := strings.Join(f.Args(), " ")
		cmdinfo.Status = fmt.Sprintf("error: unparsed arguments detected: '%s'. Did you forget to use dashes (-) for flags? Example: -whatsapp=true -telegram_bot_token=xyz", unparsedArgs)
		return cmdinfo
	}

	config.MQTT.QoS = byte(mqttQoS)

	q.Q(config)

	// Validate severity ranges for all platforms
	var severityErrors []string

	// Check WhatsApp severity configuration
	if config.WhatsApp.AlertConfig.MinSeverity < 0 || config.WhatsApp.AlertConfig.MinSeverity > 7 ||
		config.WhatsApp.AlertConfig.MaxSeverity < 0 || config.WhatsApp.AlertConfig.MaxSeverity > 7 ||
		config.WhatsApp.AlertConfig.MinSeverity > config.WhatsApp.AlertConfig.MaxSeverity {
		severityErrors = append(severityErrors, fmt.Sprintf("WhatsApp severity invalid (Min: %d, Max: %d)",
			config.WhatsApp.AlertConfig.MinSeverity, config.WhatsApp.AlertConfig.MaxSeverity))
	}

	// Check Telegram severity configuration
	if config.Telegram.AlertConfig.MinSeverity < 0 || config.Telegram.AlertConfig.MinSeverity > 7 ||
		config.Telegram.AlertConfig.MaxSeverity < 0 || config.Telegram.AlertConfig.MaxSeverity > 7 ||
		config.Telegram.AlertConfig.MinSeverity > config.Telegram.AlertConfig.MaxSeverity {
		severityErrors = append(severityErrors, fmt.Sprintf("Telegram severity invalid (Min: %d, Max: %d)",
			config.Telegram.AlertConfig.MinSeverity, config.Telegram.AlertConfig.MaxSeverity))
	}

	// Check MQTT severity configuration
	if config.MQTT.AlertConfig.MinSeverity < 0 || config.MQTT.AlertConfig.MinSeverity > 7 ||
		config.MQTT.AlertConfig.MaxSeverity < 0 || config.MQTT.AlertConfig.MaxSeverity > 7 ||
		config.MQTT.AlertConfig.MinSeverity > config.MQTT.AlertConfig.MaxSeverity {
		severityErrors = append(severityErrors, fmt.Sprintf("MQTT severity invalid (Min: %d, Max: %d)",
			config.MQTT.AlertConfig.MinSeverity, config.MQTT.AlertConfig.MaxSeverity))
	}

	// If any severity errors found, return comprehensive error message
	if len(severityErrors) > 0 {
		cmdinfo.Status = fmt.Sprintf("error: invalid severity configuration detected. All severity values must be 0-7 (RFC3164 syslog levels) and MinSeverity ≤ MaxSeverity. Valid levels: Emergency(0), Alert(1), Critical(2), Error(3), Warning(4), Notice(5), Info(6), Debug(7). Issues found: %s",
			strings.Join(severityErrors, "; "))
		return cmdinfo
	}

	// Validate rate limit and max alerts per minute
	var rateLimitErrors []string

	// Check WhatsApp rate limit configuration
	if config.WhatsApp.AlertConfig.RateLimitSeconds < 0 {
		rateLimitErrors = append(rateLimitErrors, fmt.Sprintf("WhatsApp rate limit seconds invalid (%d)",
			config.WhatsApp.AlertConfig.RateLimitSeconds))
	}
	if config.WhatsApp.AlertConfig.MaxAlertsPerMinute < 0 {
		rateLimitErrors = append(rateLimitErrors, fmt.Sprintf("WhatsApp max alerts per minute invalid (%d)",
			config.WhatsApp.AlertConfig.MaxAlertsPerMinute))
	}

	// Check Telegram rate limit configuration
	if config.Telegram.AlertConfig.RateLimitSeconds < 0 {
		rateLimitErrors = append(rateLimitErrors, fmt.Sprintf("Telegram rate limit seconds invalid (%d)",
			config.Telegram.AlertConfig.RateLimitSeconds))
	}
	if config.Telegram.AlertConfig.MaxAlertsPerMinute < 0 {
		rateLimitErrors = append(rateLimitErrors, fmt.Sprintf("Telegram max alerts per minute invalid (%d)",
			config.Telegram.AlertConfig.MaxAlertsPerMinute))
	}

	// Check MQTT rate limit configuration
	if config.MQTT.AlertConfig.RateLimitSeconds < 0 {
		rateLimitErrors = append(rateLimitErrors, fmt.Sprintf("MQTT rate limit seconds invalid (%d)",
			config.MQTT.AlertConfig.RateLimitSeconds))
	}
	if config.MQTT.AlertConfig.MaxAlertsPerMinute < 0 {
		rateLimitErrors = append(rateLimitErrors, fmt.Sprintf("MQTT max alerts per minute invalid (%d)",
			config.MQTT.AlertConfig.MaxAlertsPerMinute))
	}

	// If any rate limit errors found, return comprehensive error message
	if len(rateLimitErrors) > 0 {
		cmdinfo.Status = fmt.Sprintf("error: invalid rate limit configuration detected. Rate limit seconds and max alerts per minute must be >= 0 (non-negative values). Rate limit seconds = 0 means no per-message rate limiting, max alerts per minute = 0 means no alerts will be sent. Issues found: %s",
			strings.Join(rateLimitErrors, "; "))
		return cmdinfo
	}

	// Parse comma-separated lists
	if whatsappToNumbers != "" {
		config.WhatsApp.ToNumbers = strings.Split(whatsappToNumbers, ",")
	}
	if whatsappKeywords != "" {
		config.WhatsApp.AlertConfig.Keywords = strings.Split(whatsappKeywords, ",")
	}
	if whatsappExcludeKeywords != "" {
		config.WhatsApp.AlertConfig.ExcludeKeywords = strings.Split(whatsappExcludeKeywords, ",")
	}
	if telegramChatIDs != "" {
		config.Telegram.ChatIDs = strings.Split(telegramChatIDs, ",")
	}
	if telegramKeywords != "" {
		config.Telegram.AlertConfig.Keywords = strings.Split(telegramKeywords, ",")
	}
	if telegramExcludeKeywords != "" {
		config.Telegram.AlertConfig.ExcludeKeywords = strings.Split(telegramExcludeKeywords, ",")
	}
	if mqttKeywords != "" {
		config.MQTT.AlertConfig.Keywords = strings.Split(mqttKeywords, ",")
	}
	if mqttExcludeKeywords != "" {
		config.MQTT.AlertConfig.ExcludeKeywords = strings.Split(mqttExcludeKeywords, ",")
	}

	// Apply the config first - only save to QC.ForwardSvcData if successful
	if err := ApplyForwardConfig(&config); err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to apply new forward config: %v", err)
		return cmdinfo
	}

	// Save updated config only after successful application
	QC.ForwardSvcData[QC.Name] = config

	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
ForwardRawJsonConfigCmd handles the "forward raw-json config" command.
Usage: forward raw-json config [json_string]

	[json_string]: should be a valid JSON string representing the ForwardConfig structure, use single quotes for the entire string if using bbctl.

Example with bbctl:

	bbctl -cc service-name forward raw-json config '{"whatsapp": {"enabled": true, "account_sid": "your_account_sid", "auth_token": "your_auth_token", "from_number": "your_whatsapp_from_number", "to_numbers": ["+**********"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 300, "max_alerts_per_minute": 5, "keywords": ["error", "critical"], "exclude_keywords": []}}, "telegram": {"enabled": true, "bot_token": "your_bot_token", "chat_ids": ["12345"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 180, "max_alerts_per_minute": 10, "keywords": [], "exclude_keywords": []}}, "mqtt": {"enabled": true, "broker_host": "localhost", "broker_port": 1883, "username": "", "password": "", "topic": "mnms/alerts", "qos": 1, "retain": false, "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 60, "max_alerts_per_minute": 20, "keywords": [], "exclude_keywords":[]}}}'

Example with Script UI (fill service name in the client field):

	forward raw-json config {"whatsapp": {"enabled": true, "account_sid": "your_account_sid", "auth_token": "your_auth_token", "from_number": "your_whatsapp_from_number", "to_numbers": ["+**********"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 300, "max_alerts_per_minute": 5, "keywords": ["error", "critical"], "exclude_keywords": []}}, "telegram": {"enabled": true, "bot_token": "your_bot_token", "chat_ids": ["12345"], "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 180, "max_alerts_per_minute": 10, "keywords": [], "exclude_keywords": []}}, "mqtt": {"enabled": true, "broker_host": "localhost", "broker_port": 1883, "username": "", "password": "", "topic": "mnms/alerts", "qos": 1, "retain": false, "alert_config": {"min_severity": 0, "max_severity": 7, "rate_limit_seconds": 60, "max_alerts_per_minute": 20, "keywords": [], "exclude_keywords":[]}}}
*/
func ForwardRawJsonConfigCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	q.Q(ws)
	if len(ws) < 4 || ws[1] != "raw-json" || ws[2] != "config" {
		cmdinfo.Status = "error: usage: forward raw-json config [json_string]"
		return cmdinfo
	}

	rawConfig := strings.Join(ws[3:], " ")
	var config ForwardConfig
	if err := json.Unmarshal([]byte(rawConfig), &config); err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to parse raw config: %v", err)
		return cmdinfo
	}

	QC.ForwardMutex.Lock()
	defer QC.ForwardMutex.Unlock()

	if err := ApplyForwardConfig(&config); err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to apply new forward config: %v", err)
		return cmdinfo
	}

	QC.ForwardSvcData[QC.Name] = config

	cmdinfo.Status = "ok"
	return cmdinfo
}

/*
ForwardSendCmd sends a message directly with stored config without listening to syslog
Usage: forward send [facility] [severity] [tag] [message...]

	[facility]    : syslog facility (0-23)
	[severity]    : syslog severity (0-7)
	[tag]         : syslog tag
	[message...]  : syslog message, can be multiple words

Example with bbctl:

	bbctl -cc service-name forward send 1 5 mytag This is a test message

Example with Script UI (fill service name in the client field):

	forward send 1 5 mytag This is a test message
*/
func ForwardSendCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 6 || ws[1] != "send" {
		cmdinfo.Status = "error: usage: forward send [facility] [severity] [tag] [message...]"
		return cmdinfo
	}

	// Parse facility
	facility, err := strconv.Atoi(ws[2])
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: invalid facility '%s': %v", ws[2], err)
		return cmdinfo
	}

	// Validate facility range (0-23)
	if facility < 0 || facility > 23 {
		cmdinfo.Status = "error: facility must be between 0-23"
		return cmdinfo
	}

	// Parse severity
	severity, err := strconv.Atoi(ws[3])
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: invalid severity '%s': %v", ws[3], err)
		return cmdinfo
	}

	// Validate severity range (0-7)
	if severity < 0 || severity > 7 {
		cmdinfo.Status = "error: severity must be between 0-7"
		return cmdinfo
	}

	// Get tag
	tag := ws[4]

	// Get message (everything after tag)
	message := strings.Join(ws[5:], " ")
	if message == "" {
		cmdinfo.Status = "error: message cannot be empty"
		return cmdinfo
	}

	QC.ForwardMutex.Lock()
	_, ok := QC.ForwardSvcData[QC.Name]
	QC.ForwardMutex.Unlock()

	if !ok {
		cmdinfo.Status = "error: no forward configuration found"
		return cmdinfo
	}

	if QC.AlertForwarder == nil {
		cmdinfo.Status = "error: alert forwarder not initialized"
		return cmdinfo
	}

	// Create a proper syslog entry with user-provided parameters
	priority := facility*8 + severity
	testEntry := &SyslogEntry{
		Timestamp: time.Now(),
		Hostname:  "test-host",
		Facility:  facility,
		Severity:  severity,
		Priority:  priority,
		Tag:       tag,
		Message:   message,
		RawLine:   fmt.Sprintf("<%d>%s test-host %s: %s", priority, time.Now().Format("Jan 2 15:04:05"), tag, message),
	}

	// Use the standard processLogEntry method
	result := QC.AlertForwarder.processLogEntry(testEntry)
	// to json string
	jsonBytes, err := json.Marshal(result)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to marshal result: %v", err)
		return cmdinfo
	}

	cmdinfo.Status = "ok"
	cmdinfo.Result = string(jsonBytes)
	return cmdinfo
}

/*
ForwardCustomSendCmd sends a custom message with custom config
Usage: forward custom send [custom-raw-json-config] [facility] [severity] [tag] [message...]

	[custom-raw-json-config]: should be a valid JSON string representing the ForwardConfig structure, use single quotes for the entire string if using bbctl.
	note that use escape characters for quotes if needed.
	[facility]    : syslog facility (0-23) RFC3164
	[severity]    : syslog severity (0-7)
	[tag]         : syslog tag
	[message...]  : syslog message, can be multiple words

Example with bbctl:

	bbctl -cc service-name forward custom send '{"whatsapp": {...}, "telegram": {...}, "mqtt": {...}}' 1 5 mytag This is a test message

Example with Script UI (fill service name in the client field):

	forward custom send {"whatsapp": {...}, "telegram": {...}, "mqtt": {...}} 1 5 mytag This is a test message
*/
func ForwardCustomSendCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	ws := strings.Fields(cmd)
	if len(ws) < 5 || ws[1] != "custom" || ws[2] != "send" {
		cmdinfo.Status = "error: usage: forward custom send [custom-raw-json-config] [facility] [severity] [tag] [message...]"
		return cmdinfo
	}

	// Find where the JSON config ends and message begins
	// Look for the closing brace of the JSON
	cmdParts := strings.SplitN(cmd, " ", 4)
	if len(cmdParts) < 4 {
		cmdinfo.Status = "error: usage: forward custom send [custom-raw-json-config] [facility] [severity] [tag] [message...]"
		return cmdinfo
	}

	remaining := cmdParts[3] // Everything after "forward custom send "

	// Debug: log what we're trying to parse
	q.Q("ForwardCustomSend", fmt.Sprintf("Remaining to parse: %q", remaining))

	// Find the end of JSON by counting braces
	braceCount := 0
	jsonEnd := -1
	inString := false
	escaped := false
	firstBraceFound := false

	for i, char := range remaining {
		if escaped {
			escaped = false
			continue
		}

		if char == '\\' {
			escaped = true
			continue
		}

		if char == '"' {
			inString = !inString
			continue
		}

		if !inString {
			if char == '{' {
				if !firstBraceFound {
					firstBraceFound = true
				}
				braceCount++
			} else if char == '}' {
				braceCount--
				if braceCount == 0 && firstBraceFound {
					jsonEnd = i
					break
				}
			}
		}
	}

	q.Q("ForwardCustomSend", fmt.Sprintf("JSON parsing: firstBraceFound=%v, jsonEnd=%d, braceCount=%d", firstBraceFound, jsonEnd, braceCount))

	if jsonEnd == -1 || !firstBraceFound {
		cmdinfo.Status = fmt.Sprintf("error: invalid JSON config format - could not find complete JSON object (firstBraceFound=%v, jsonEnd=%d)", firstBraceFound, jsonEnd)
		return cmdinfo
	}

	rawConfig := remaining[:jsonEnd+1]
	remainingParams := strings.TrimSpace(remaining[jsonEnd+1:])

	// Debug: log extracted parts
	q.Q("ForwardCustomSend", fmt.Sprintf("Extracted JSON: %q", rawConfig))
	q.Q("ForwardCustomSend", fmt.Sprintf("Remaining params: %q", remainingParams))

	if remainingParams == "" {
		cmdinfo.Status = "error: missing syslog parameters: [facility] [severity] [tag] [message...]"
		return cmdinfo
	}

	// Parse syslog parameters
	paramFields := strings.Fields(remainingParams)
	if len(paramFields) < 4 {
		cmdinfo.Status = "error: usage: forward custom send [custom-raw-json-config] [facility] [severity] [tag] [message...]"
		return cmdinfo
	}

	// Parse facility
	facility, err := strconv.Atoi(paramFields[0])
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: invalid facility '%s': %v", paramFields[0], err)
		return cmdinfo
	}

	// Validate facility range (0-23)
	if facility < 0 || facility > 23 {
		cmdinfo.Status = "error: facility must be between 0-23"
		return cmdinfo
	}

	// Parse severity
	severity, err := strconv.Atoi(paramFields[1])
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: invalid severity '%s': %v", paramFields[1], err)
		return cmdinfo
	}

	// Validate severity range (0-7)
	if severity < 0 || severity > 7 {
		cmdinfo.Status = "error: severity must be between 0-7"
		return cmdinfo
	}

	// Get tag
	tag := paramFields[2]

	// Get message (everything after tag)
	message := strings.Join(paramFields[3:], " ")
	if message == "" {
		cmdinfo.Status = "error: message cannot be empty"
		return cmdinfo
	}

	// Parse the custom config
	var config ForwardConfig
	if err := json.Unmarshal([]byte(rawConfig), &config); err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to parse custom config: %v", err)
		return cmdinfo
	}

	// Create a temporary alert forwarder with custom config
	tempForwarder, err := NewAlertForwarder(&config)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to create temporary forwarder: %v", err)
		return cmdinfo
	}
	defer tempForwarder.Stop()

	// Create a proper syslog entry with user-provided parameters
	priority := facility*8 + severity
	testEntry := &SyslogEntry{
		Timestamp: time.Now(),
		Hostname:  "test-host",
		Facility:  facility,
		Severity:  severity,
		Priority:  priority,
		Tag:       tag,
		Message:   message,
		RawLine:   fmt.Sprintf("<%d>%s test-host %s: %s", priority, time.Now().Format("Jan 2 15:04:05"), tag, message),
	}

	// Use the standard processLogEntry method with custom config
	result := tempForwarder.processLogEntry(testEntry)
	// to json string
	jsonBytes, err := json.Marshal(result)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to marshal result: %v", err)
		return cmdinfo
	}

	cmdinfo.Status = "ok"
	cmdinfo.Result = string(jsonBytes)
	return cmdinfo
}

// uploadForwardConfigToRoot uploads a forward configuration to the root service
func uploadForwardConfigToRoot(config ForwardConfig) error {
	// Copy listen port from syslog server address
	config.ListenSyslogAddr = QC.SyslogServerAddr

	// Prepare config map for upload
	configMap := make(map[string]ForwardConfig)
	configMap[QC.Name] = config

	jsonBytes, err := json.Marshal(configMap)
	if err != nil {
		return fmt.Errorf("failed to marshal forward config: %w", err)
	}

	// Post to root service
	resp, err := PostWithToken(QC.RootURL+"/api/v1/forwards", QC.AdminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		return fmt.Errorf("failed to post forward config to root: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to upload config, status: %s, body: %s", resp.Status, string(body))
	}

	return nil
}

// ForwardUploadConfigCmd uploads the current forward config to the root service immediately
// Usage: forward upload
func ForwardUploadConfigCmd(cmdinfo *CmdInfo) *CmdInfo {
	QC.ForwardMutex.Lock()
	config, ok := QC.ForwardSvcData[QC.Name]
	QC.ForwardMutex.Unlock()

	if !ok {
		cmdinfo.Status = "error: no forward configuration found"
		return cmdinfo
	}

	if err := uploadForwardConfigToRoot(config); err != nil {
		cmdinfo.Status = fmt.Sprintf("error: failed to upload config to root: %v", err)
		return cmdinfo
	}

	cmdinfo.Status = "ok"
	return cmdinfo
}

// PostForwardConfigToRoot posts the forward configuration to the root service for UI updates
func PostForwardConfigToRoot(interval int) {
	for {
		QC.ForwardMutex.Lock()
		c, ok := QC.ForwardSvcData[QC.Name]
		QC.ForwardMutex.Unlock()
		if !ok {
			// SendSyslog(LOG_WARNING, "bbfwdsvc", "No forward configuration found for this service")
			q.Q("No forward configuration found for this service")
			time.Sleep(time.Duration(interval) * time.Second)
			continue
		}

		// Use the extracted upload function
		if err := uploadForwardConfigToRoot(c); err != nil {
			// SendSyslog(LOG_ERR, "bbfwdsvc", fmt.Sprintf("Failed to upload forward config to root: %v", err))
		}

		time.Sleep(time.Duration(interval) * time.Second)
	}
}
