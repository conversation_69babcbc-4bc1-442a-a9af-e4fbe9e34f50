package mnms

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"
	"sync"
	"testing"

	"github.com/bitfield/script"
	"github.com/qeof/q"
)

func init() {
	QC.IsRoot = true
}

func TestDevices(t *testing.T) {
	killNmsctlProcesses()

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		HTTPMain()
	}()

	myName := "test123"
	adminToken, err := GetToken("admin")
	if err != nil {
		t.Fatal(err)
	}
	q.Q("wait for root to become ready...")
	if err := waitForRoot(); err != nil {
		t.Fatal(err)
	}
	url := fmt.Sprintf("http://localhost:%d/api/v1/register", QC.Port)
	ci := ClientInfo{Name: myName}
	jsonBytes, err := json.Marshal(ci)
	if err != nil {
		t.Fatalf("json marshal %v", err)
	}
	resp, err := PostWithToken(url, adminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	if resp != nil {
		if resp.StatusCode != 200 {
			t.Fatalf("post StatusCode %d", resp.StatusCode)
		}
		q.Q(resp.Header)
		//save close
		resp.Body.Close()
	}

	err = ReadTestData()
	if err != nil {
		t.Fatal(err)
	}
	url = fmt.Sprintf("http://localhost:%d/api/v1/devices", QC.Port)
	resp, err = GetWithToken(url, adminToken)

	if err != nil {
		t.Fatalf("get %v", err)
	}
	if resp.StatusCode != 200 {
		t.Fatalf("response status code expect 200 but  %v", resp.StatusCode)
	}
	if resp != nil {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			t.Fatalf("reading resp, %v", err)
		}
		q.Q(string(body))
		l, err := script.Echo(string(body)).JQ("[.[]]|length").String()
		if err != nil {
			t.Fatalf("%v", err)
		}
		l = strings.TrimSpace(l)
		q.Q(l)
		lval, err := strconv.Atoi(l)
		if err != nil {
			t.Fatal(err)
		}

		if lval < 45 {
			t.Fatalf("wrong len, %v", l)
		}
		l, err = script.Echo(string(body)).JQ(`.[]|select(.mac=="02-42-C0-A8-64-86" ) | .ipaddress`).String()
		if err != nil {
			t.Fatalf("%v", err)
		}
		l = strings.TrimSpace(l)
		q.Q(l)
		if l != `"***************"` {
			t.Fatalf("wrong ipaddr, %v", l)
		}
		//save close, in if resp != nil block
		resp.Body.Close()
	}

	q.Q("end test devices")
}

func ReadTestData() error {
	dat, err := os.ReadFile("testdata.json")
	if err != nil {
		q.Q("can't read file", err)
		return err
	}
	var devinfo map[string]DevInfo
	err = json.Unmarshal(dat, &devinfo)
	if err != nil {
		q.Q("can't unmarshal", err)
		return err
	}

	for _, v := range devinfo {
		if !InsertDev(v) {
			q.Q("can't insert dev", err)
			return err
		}
	}
	return nil
}
func TestFindDevice(t *testing.T) {
	defer func() {
		killNmsctlProcesses()
	}()

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		HTTPMain()
	}()
	// Initialize QC.DevData with some test data
	QC.DevData = make(map[string]DevInfo)
	QC.DevData["device1"] = DevInfo{Mac: "00:11:22:33:44:55", Lock: false}
	QC.DevData["device2"] = DevInfo{Mac: "AA:BB:CC:DD:EE:FF", Lock: true}

	// Test FindDev function
	dev, err := FindDev("device1")
	if err != nil {
		t.Errorf("FindDev returned an error: %v", err)
	}
	q.Q(dev)

	if dev == nil || dev.Mac != "00:11:22:33:44:55" {
		t.Error("FindDev returned incorrect device")
	}

	_, err = FindDev("device3")
	if err == nil {
		t.Fatal("FindDev did not return an error for non-existent device")
	}
	q.Q("end test devices")

	// Test LockDev function
	LockDev("device1")
	devPtr, ok := QC.DevData["device1"]
	if !ok {
		t.Errorf("LockDev did not find the device")
	} else {
		dev := devPtr
		if !dev.Lock {
			t.Error("LockDev did not lock the device")
		}
	}
	LockDev("device3") // Locking a non-existent device should not cause any issues

	// Test UnlockDev function
	unLockDev("device1")
	devPtr, ok = QC.DevData["device1"]
	if !ok {
		t.Errorf("UnlockDev did not find the device")
	} else {
		dev := devPtr
		if dev.Lock {
			t.Error("UnlockDev did not unlock the device")
		}
	}
	unLockDev("device3") // Unlocking a non-existent device should not cause any issues

	// Test DevIsLocked function
	locked, err := DevIsLocked("device2")
	if err != nil {
		t.Errorf("DevIsLocked returned an error: %v", err)
	}
	q.Q(locked)

	if !locked {
		t.Error("DevIsLocked returned incorrect lock status")
	}

	_, err = DevIsLocked("device3")
	if err == nil {
		t.Fatal("DevIsLocked did not return an error for non-existent device")
	}

}
func TestSaveDevices(t *testing.T) {
	// Backup original working directory and defer its restoration
	oldDir, _ := os.Getwd()
	defer func() { os.Chdir(oldDir) }()

	// Create a temporary directory and move into it
	tmpDir, _ := os.MkdirTemp("", "test")
	os.Chdir(tmpDir)

	QC.DevData = map[string]DevInfo{
		"device1": {Mac: "00:11:22:33:44:55"},
	}

	filename, err := SaveDevices()

	if err != nil {
		t.Fatalf("Expected no error but got: %v", err)
	}

	content, _ := os.ReadFile(filename)

	devinfo := make(map[string]DevInfo)
	err = json.Unmarshal(content, &devinfo)
	if err != nil {
		t.Fatalf("error: %v", err)
	}

	contentDev, ok := devinfo["device1"]
	if !ok {
		t.Errorf("did not find the device from ReadFile")
	}

	QC.DevMutex.Lock()
	defer QC.DevMutex.Unlock()
	expectedDev, ok := QC.DevData["device1"]
	if !ok {
		t.Errorf("did not find the device from QC.DevData")
	}

	if contentDev.Mac != expectedDev.Mac {
		t.Fatalf("File content %v does not match expected %v", devinfo, QC.DevData)
	}
}

func TestPublishDevices_NoRootURL(t *testing.T) {
	// Backup current RootURL and defer its restoration
	oldRootURL := QC.RootURL
	defer func() { QC.RootURL = oldRootURL }()

	// Set RootURL to empty
	QC.RootURL = ""

	devdata := make(map[string]DevInfo)
	devdata["00:11:22:33:44:55"] = DevInfo{}

	err := PublishDevices(&devdata)

	if err == nil || err.Error() != "skip publishing devices, no root" {
		t.Error("Expected error message 'skip publishing devices, no root', but got", err)
	}
}

func TestNegativeFindDevWithIP(t *testing.T) {
	// Mock QC.DevData
	QC.DevData = make(map[string]DevInfo)

	// Try to find a device which does not exist
	_, err := FindDevWithIP("***********")
	if err == nil {
		t.Error("Expected an error when device does not exist")
	}
}

func TestNegativeFindDev(t *testing.T) {
	// Mock QC.DevData
	QC.DevData = make(map[string]DevInfo)

	// Try to find a device which does not exist
	_, err := FindDev("EHG7XXX")
	if err == nil {
		t.Error("Expected an error when device does not exist")
	}
}

func TestNegativeLockDev(t *testing.T) {
	// Mock QC.DevData
	QC.DevData = make(map[string]DevInfo)

	// Try to lock a device which does not exist
	LockDev("EHG7510")
	_, ok := QC.DevData["EHG7510"]
	if ok {
		t.Error("Expected no device when device does not exist")
	}
}

func TestNegativeUnLockDev(t *testing.T) {
	// Mock QC.DevData
	QC.DevData = make(map[string]DevInfo)

	// Try to unlock a device which does not exist
	unLockDev("EHG7512")
	_, ok := QC.DevData["EHG7512"]
	if ok {
		t.Error("Expected no device when device does not exist")
	}
}

func TestNegativeDevIsLocked(t *testing.T) {
	// Mock QC.DevData
	QC.DevData = make(map[string]DevInfo)

	// Try to check if a device which does not exist is locked
	_, err := DevIsLocked("EHG7608")
	if err == nil {
		t.Error("Expected an error when device does not exist")
	}
}

/* To testing license limitatation
GET TOKEN, replace admin with your username and testpw with your password
$ TOKEN=$(curl -s -X POST -H 'Accept: application/json' -H 'Content-Type: application/json' --data '{"user":"admin","password":"testpw"}' http://localhost:27182/api/v1/login | jq -r '.token')

ADD Device to client
$ curl -H 'Accept: application/json' -H "Authorization: Bearer ${TOKEN}" http://localhost:27182/api/v1/devices
*/

/*
	TestCheckLicenseClientsLimit

This test is not a kind of automation test. When you want to test license limitation, first you need to get a license file
that has limitation on number of device. Put the license file in the root directory of nimbl. The test requires nimbl root and
at least one client is running (modify correct URL in the test code).

Change code to generate bunch of fake devices, and check if nimbl root can detect the license limitation in syslog or UI.
countDev := 10
*/
func TestCheckLicenseDevicesLimit(t *testing.T) {
	// make sure nimble root and at least one client is running, replace url with your nimble root url
	rootURL := `http://localhost:27182`
	clientURL := `http://localhost:27185`
	// testing endpoint is /api if nimbl is running it should return a response "mnms says hello"
	resp, err := http.Get(rootURL + "/api")
	if err != nil {
		// quiet quit, if nimbl is not running (for CI/CD)
		t.Log("nimbl root is not running, terminate test")
		return
	}
	if resp.StatusCode != 200 {
		t.Log("nimbl root is not running, terminate test")
		return
	}
	// Testing client is running
	resp, err = http.Get(clientURL + "/api")
	if err != nil {
		// quiet quit, if nimbl is not running (for CI/CD)
		t.Log("nimbl client is not running, terminate test")
		return
	}
	if resp.StatusCode != 200 {
		t.Log("nimbl client is not running, terminate test")
		return
	}

	// Fake DevInfo
	devInfo := &DevInfo{
		Mac:            "00-AA-BB-33-44-55",
		ModelName:      "EHG7608",
		Timestamp:      "1629782400",
		Scanproto:      "agent",
		IPAddress:      "***********",
		Netmask:        "*************",
		Gateway:        "************",
		Hostname:       "test_dev1",
		Kernel:         "4.14.180",
		Ap:             "1.0.0",
		ScannedBy:      "client1",
		ArpMissed:      0,
		Lock:           false,
		ReadCommunity:  "public",
		WriteCommunity: "private",
		IsDHCP:         false,
		TopologyProto:  "snmp",
	}

	// Get token
	adminToken, err := GetToken("admin")
	if err != nil {
		t.Fatal(err)
	}

	countDev := 10 // number of devices to add
	// create devInfo map
	devinfoMap := make(map[string]DevInfo)
	// Add devices to client POST /api/v1/devices
	for i := 0; i < countDev; i++ {
		// modify MAC address, IP address, hostname
		devInfo.Mac = fmt.Sprintf("00-AA-BB-33-44-%02d", i)
		devInfo.IPAddress = fmt.Sprintf("11.12.13.%d", i)
		devInfo.Hostname = fmt.Sprintf("test_dev%d", i)
		devinfoMap[devInfo.Mac] = *devInfo

	}

	jsonBytes, err := json.Marshal(devinfoMap)
	if err != nil {
		t.Fatalf("json marshal %v", err)
	}
	res, err := PostWithToken(clientURL+"/api/v1/devices", adminToken, bytes.NewBuffer(jsonBytes))
	if err != nil {
		t.Fatalf("post %v", err)
	}
	//dump response body
	if res != nil {
		//read response body
		body, err := io.ReadAll(res.Body)
		if err != nil {
			t.Fatalf("reading resp, %v", err)
		}
		t.Log(string(body))
		//save close
		res.Body.Close()
	}
}
